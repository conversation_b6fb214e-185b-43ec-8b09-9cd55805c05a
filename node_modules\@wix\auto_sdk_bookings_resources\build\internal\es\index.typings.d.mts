import { af as CursorSearch, S as SearchResourcesResponse } from './bookings-resources-v2-resource-resources.universal-WjeiBRqP.mjs';
export { b5 as ActionEvent, ai as Aggregation, ax as AggregationData, aj as AggregationKindOneOf, aQ as AggregationResults, aR as AggregationResultsResultOneOf, aE as AggregationResultsScalarResult, A as AggregationType, bm as AggregationTypeWithLiterals, Q as ApplicationError, ba as BaseEventMetadata, T as BulkActionMetadata, B as BulkCreateResourcesOptions, K as BulkCreateResourcesRequest, a as BulkCreateResourcesResponse, b as BulkCreateResourcesValidationErrors, ad as BulkDeleteResourcesRequest, g as BulkDeleteResourcesResponse, O as BulkResourceResult, d as BulkUpdateResourcesOptions, aa as BulkUpdateResourcesRequest, e as BulkUpdateResourcesResponse, f as BulkUpdateResourcesValidationErrors, D as BusinessLocation, bp as CommonSearchWithEntityContext, j as CountResourcesOptions, aW as CountResourcesRequest, k as CountResourcesResponse, H as CreateResourceRequest, J as CreateResourceResponse, C as CreateResourceValidationErrors, a0 as CursorPaging, a2 as CursorPagingMetadata, aT as CursorQuery, aU as CursorQueryPagingMethodOneOf, ag as CursorSearchPagingMethodOneOf, a3 as Cursors, aq as DateHistogramAggregation, aM as DateHistogramResult, aO as DateHistogramResults, ab as DeleteResourceRequest, ac as DeleteResourceResponse, a$ as DomainEvent, b0 as DomainEventBodyOneOf, b6 as Empty, b1 as EntityCreatedEvent, b4 as EntityDeletedEvent, b3 as EntityUpdatedEvent, bb as EventMetadata, F as EventsSchedule, G as ExtendedFields, aX as FixResourceSchedulesRequest, aY as FixResourceSchedulesResponse, Z as GetDeletedResourceRequest, _ as GetDeletedResourceResponse, X as GetResourceRequest, Y as GetResourceResponse, au as GroupByAggregation, av as GroupByAggregationKindOneOf, aN as GroupByValueResults, b8 as IdentificationData, b9 as IdentificationDataIdOneOf, al as IncludeMissingValuesOptions, I as Interval, bl as IntervalWithLiterals, P as ItemMetadata, $ as ListDeletedResourcesRequest, a1 as ListDeletedResourcesResponse, L as LocationOptions, o as ManagementType, be as ManagementTypeWithLiterals, M as MaskedResource, b7 as MessageEnvelope, s as MissingValues, bi as MissingValuesWithLiterals, u as Mode, bn as ModeWithLiterals, at as NestedAggregation, ar as NestedAggregationItem, as as NestedAggregationItemKindOneOf, aA as NestedAggregationResults, aB as NestedAggregationResultsResultOneOf, N as NestedAggregationType, bk as NestedAggregationTypeWithLiterals, aJ as NestedResultValue, aK as NestedResultValueResultOneOf, aP as NestedResults, aF as NestedValueAggregationResult, aS as QueryResourcesRequest, aV as QueryResourcesResponse, ao as RangeAggregation, az as RangeAggregationResult, ak as RangeBucket, aH as RangeResult, aD as RangeResults, aZ as ReindexTenantRequest, a_ as ReindexTenantResponse, a4 as RemoveResourceFromTrashBinRequest, a5 as RemoveResourceFromTrashBinResponse, R as Resource, v as ResourceCompositionDetailsOneOf, l as ResourceCreatedEnvelope, m as ResourceDeletedEnvelope, bc as ResourceSearchSpec, n as ResourceUpdatedEnvelope, i as ResourcesQueryBuilder, bd as ResourcesQueryResult, b2 as RestoreInfo, a6 as RestoreResourceFromTrashBinRequest, a7 as RestoreResourceFromTrashBinResponse, aL as Results, ap as ScalarAggregation, aI as ScalarResult, t as ScalarType, bj as ScalarTypeWithLiterals, y as Schedule, aw as SearchDetails, ae as SearchResourcesRequest, x as SingleResource, r as SortDirection, bh as SortDirectionWithLiterals, p as SortOrder, bf as SortOrderWithLiterals, q as SortType, bg as SortTypeWithLiterals, ah as Sorting, z as SpecificLocation, U as UpdateResource, a8 as UpdateResourceRequest, a9 as UpdateResourceResponse, c as UpdateResourceValidationErrors, V as V2WorkingHoursSchedules, am as ValueAggregation, an as ValueAggregationOptionsOneOf, ay as ValueAggregationResult, aG as ValueResult, aC as ValueResults, W as WebhookIdentityType, bo as WebhookIdentityTypeWithLiterals, w as WorkingHoursSchedule, E as WorkingHoursSchedules, bu as bulkCreateResources, bz as bulkDeleteResources, bx as bulkUpdateResources, bB as countResources, bt as createResource, by as deleteResource, bv as getResource, bq as onResourceCreated, br as onResourceDeleted, bs as onResourceUpdated, bA as queryResources, bw as updateResource } from './bookings-resources-v2-resource-resources.universal-WjeiBRqP.mjs';
import '@wix/sdk-types';

/** @hidden */
type ResourceSearch = {};
/**
 * Retrieves a list of resources matching the provided search criteria.
 *
 *
 * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.
 * @param search - Search criteria including filter, sort, aggregations, and paging options.
 *
 * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.
 * @public
 * @requiredField search
 * @permissionId BOOKINGS.RESOURCE_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.ResourcesService.SearchResources
 */
declare function searchResources(search: CursorSearch): Promise<SearchResourcesResponse>;

export { CursorSearch, type ResourceSearch, SearchResourcesResponse, searchResources };
