// src/bookings-availability-v1-slot-availability-availability-calendar.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-availability-v1-slot-availability-availability-calendar.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v2/availability/schedule",
        destPath: "/v2/availability/schedule"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      },
      {
        srcPath: "/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      },
      {
        srcPath: "/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      },
      {
        srcPath: "/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "editor-flow.wixapps.net": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/bookings/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/bookings/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar",
        destPath: ""
      },
      {
        srcPath: "/_api/availability-calendar/v2/availability/schedule",
        destPath: "/v2/availability/schedule"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "bookings.wixapps.net": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "*.wixforms.com": [
      {
        srcPath: "/_api/availability-calendar",
        destPath: ""
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/availability-calendar",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_availability-calendar";
function queryAvailability(payload) {
  function __queryAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v1.slot_availability",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.AvailabilityCalendar.QueryAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({
        protoPath: "/v1/availability/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryAvailability;
}
function getScheduleAvailability(payload) {
  function __getScheduleAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v1.slot_availability",
      method: "GET",
      methodFqn: "com.wixpress.bookings.availability.AvailabilityCalendar.GetScheduleAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({
        protoPath: "/v2/availability/schedule/{scheduleId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __getScheduleAvailability;
}
function calculateMultiSlotAvailability(payload) {
  function __calculateMultiSlotAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v1.slot_availability",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.AvailabilityCalendar.CalculateMultiSlotAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({
        protoPath: "/v1/availability/multislot",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __calculateMultiSlotAvailability;
}

// src/bookings-availability-v1-slot-availability-availability-calendar.universal.ts
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNDEFINED"] = "UNDEFINED";
  LocationType2["OWNER_BUSINESS"] = "OWNER_BUSINESS";
  LocationType2["OWNER_CUSTOM"] = "OWNER_CUSTOM";
  LocationType2["CUSTOM"] = "CUSTOM";
  return LocationType2;
})(LocationType || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var LocationLocationType = /* @__PURE__ */ ((LocationLocationType2) => {
  LocationLocationType2["UNKNOWN_LOCATION_TYPE"] = "UNKNOWN_LOCATION_TYPE";
  LocationLocationType2["BUSINESS"] = "BUSINESS";
  LocationLocationType2["CUSTOM"] = "CUSTOM";
  LocationLocationType2["CUSTOMER"] = "CUSTOMER";
  return LocationLocationType2;
})(LocationLocationType || {});
async function queryAvailability2(query, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    query,
    timezone: options?.timezone,
    slotsPerDay: options?.slotsPerDay
  });
  const reqOpts = queryAvailability(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          query: "$[0]",
          timezone: "$[1].timezone",
          slotsPerDay: "$[1].slotsPerDay"
        },
        singleArgumentUnchanged: false
      },
      ["query", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getScheduleAvailability2(scheduleId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    scheduleId
  });
  const reqOpts = getScheduleAvailability(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { scheduleId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["scheduleId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function calculateMultiSlotAvailability2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    from: options?.from,
    to: options?.to,
    timeZone: options?.timeZone,
    bookable: options?.bookable,
    bookingPolicyViolations: options?.bookingPolicyViolations,
    location: options?.location,
    slots: options?.slots,
    slotsPerDay: options?.slotsPerDay,
    cursorPaging: options?.cursorPaging
  });
  const reqOpts = calculateMultiSlotAvailability(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          from: "$[0].from",
          to: "$[0].to",
          timeZone: "$[0].timeZone",
          bookable: "$[0].bookable",
          bookingPolicyViolations: "$[0].bookingPolicyViolations",
          location: "$[0].location",
          slots: "$[0].slots",
          slotsPerDay: "$[0].slotsPerDay",
          cursorPaging: "$[0].cursorPaging"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-availability-v1-slot-availability-availability-calendar.public.ts
function queryAvailability3(httpClient) {
  return (query, options) => queryAvailability2(
    query,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function getScheduleAvailability3(httpClient) {
  return (scheduleId) => getScheduleAvailability2(
    scheduleId,
    // @ts-ignore
    { httpClient }
  );
}
function calculateMultiSlotAvailability3(httpClient) {
  return (options) => calculateMultiSlotAvailability2(
    options,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-availability-v1-slot-availability-availability-calendar.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
var queryAvailability4 = /* @__PURE__ */ createRESTModule(queryAvailability3);
var getScheduleAvailability4 = /* @__PURE__ */ createRESTModule(getScheduleAvailability3);
var calculateMultiSlotAvailability4 = /* @__PURE__ */ createRESTModule(calculateMultiSlotAvailability3);
export {
  LocationLocationType,
  LocationType,
  SortOrder,
  calculateMultiSlotAvailability4 as calculateMultiSlotAvailability,
  getScheduleAvailability4 as getScheduleAvailability,
  queryAvailability4 as queryAvailability
};
//# sourceMappingURL=index.mjs.map