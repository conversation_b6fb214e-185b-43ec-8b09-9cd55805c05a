import { CreateResourceTypeRequest as CreateResourceTypeRequest$1, CreateResourceTypeResponse as CreateResourceTypeResponse$1, GetResourceTypeRequest as GetResourceTypeRequest$1, GetResourceTypeResponse as GetResourceTypeResponse$1, UpdateResourceTypeRequest as UpdateResourceTypeRequest$1, UpdateResourceTypeResponse as UpdateResourceTypeResponse$1, DeleteResourceTypeRequest as DeleteResourceTypeRequest$1, DeleteResourceTypeResponse as DeleteResourceTypeResponse$1, QueryResourceTypesRequest as QueryResourceTypesRequest$1, QueryResourceTypesResponse as QueryResourceTypesResponse$1, CountResourceTypesRequest as CountResourceTypesRequest$1, CountResourceTypesResponse as CountResourceTypesResponse$1 } from './index.typings.mjs';
import '@wix/sdk-types';

/**
 * A resource type is a classification of resources. For example room, equipment,
 * or vehicle. Customers can only book *services*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
 * if at least 1 *resource*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
 * for every resource type connected to the service is available during the requested time.
 */
interface ResourceType {
    /**
     * Resource type ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Revision number, which increments by 1 each time the resource type is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the resource type.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was created.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was last updated.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Name of the resource type. For example, `meeting room`. The name must be
     * unique per site.
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
    /** Extensions enabling users to save custom data related to the resource type. */
    extendedFields?: ExtendedFields;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateResourceTypeRequest {
    /** Resource type to create. */
    resourceType: ResourceType;
}
interface CreateResourceTypeResponse {
    /** Created resource type. */
    resourceType?: ResourceType;
}
interface GetResourceTypeRequest {
    /**
     * ID of the resource type to retrieve.
     * @format GUID
     */
    resourceTypeId: string;
}
interface GetResourceTypeResponse {
    /** Retrieved resource type. */
    resourceType?: ResourceType;
}
interface UpdateResourceTypeRequest {
    /** Resource type to update. */
    resourceType: ResourceType;
}
interface UpdateResourceTypeResponse {
    /** Updated resource type. */
    resourceType?: ResourceType;
}
interface DeleteResourceTypeRequest {
    /**
     * ID of the resource type to delete.
     * @format GUID
     */
    resourceTypeId: string;
}
interface DeleteResourceTypeResponse {
}
interface QueryResourceTypesRequest {
    /**
     * Information about filtering and sorting.
     * Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters and sorting options.
     */
    query?: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 3
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 200
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryResourceTypesResponse {
    /** Retrieved resource types. */
    resourceTypes?: ResourceType[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor pointing to next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface CountResourceTypesRequest {
    /**
     * Filter to base the count on.
     * Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
}
interface CountResourceTypesResponse {
    /** Number of resource types matching the filter. */
    count?: number;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createResourceType(): __PublicMethodMetaInfo<'POST', {}, CreateResourceTypeRequest$1, CreateResourceTypeRequest, CreateResourceTypeResponse$1, CreateResourceTypeResponse>;
declare function getResourceType(): __PublicMethodMetaInfo<'GET', {
    resourceTypeId: string;
}, GetResourceTypeRequest$1, GetResourceTypeRequest, GetResourceTypeResponse$1, GetResourceTypeResponse>;
declare function updateResourceType(): __PublicMethodMetaInfo<'PATCH', {
    resourceTypeId: string;
}, UpdateResourceTypeRequest$1, UpdateResourceTypeRequest, UpdateResourceTypeResponse$1, UpdateResourceTypeResponse>;
declare function deleteResourceType(): __PublicMethodMetaInfo<'DELETE', {
    resourceTypeId: string;
}, DeleteResourceTypeRequest$1, DeleteResourceTypeRequest, DeleteResourceTypeResponse$1, DeleteResourceTypeResponse>;
declare function queryResourceTypes(): __PublicMethodMetaInfo<'POST', {}, QueryResourceTypesRequest$1, QueryResourceTypesRequest, QueryResourceTypesResponse$1, QueryResourceTypesResponse>;
declare function countResourceTypes(): __PublicMethodMetaInfo<'POST', {}, CountResourceTypesRequest$1, CountResourceTypesRequest, CountResourceTypesResponse$1, CountResourceTypesResponse>;

export { type __PublicMethodMetaInfo, countResourceTypes, createResourceType, deleteResourceType, getResourceType, queryResourceTypes, updateResourceType };
