{"version": 3, "sources": ["../../../src/bookings-resources-v2-resource-resources.public.ts", "../../../src/bookings-resources-v2-resource-resources.universal.ts", "../../../src/bookings-resources-v2-resource-resources.http.ts", "../../../src/bookings-resources-v2-resource-resources.context.ts"], "sourcesContent": ["import { renameKeysFromRESTResponseToSDKResponse } from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { EventDefinition, HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  BulkCreateResourcesOptions,\n  BulkCreateResourcesResponse,\n  BulkCreateResourcesValidationErrors,\n  BulkDeleteResourcesResponse,\n  BulkUpdateResourcesOptions,\n  BulkUpdateResourcesResponse,\n  BulkUpdateResourcesValidationErrors,\n  CountResourcesOptions,\n  CountResourcesResponse,\n  CreateResourceValidationErrors,\n  MaskedResource,\n  Resource,\n  ResourceCreatedEnvelope,\n  ResourceDeletedEnvelope,\n  ResourceSearch,\n  ResourceUpdatedEnvelope,\n  ResourcesQueryBuilder,\n  SearchResourcesResponse,\n  UpdateResource,\n  UpdateResourceValidationErrors,\n  bulkCreateResources as universalBulkCreateResources,\n  bulkDeleteResources as universalBulkDeleteResources,\n  bulkUpdateResources as universalBulkUpdateResources,\n  countResources as universalCountResources,\n  createResource as universalCreateResource,\n  deleteResource as universalDeleteResource,\n  getResource as universalGetResource,\n  queryResources as universalQueryResources,\n  searchResources as universalSearchResources,\n  updateResource as universalUpdateResource,\n} from './bookings-resources-v2-resource-resources.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function createResource(\n  httpClient: HttpClient\n): CreateResourceSignature {\n  return (resource: NonNullablePaths<Resource, `name`, 2>) =>\n    universalCreateResource(\n      resource,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CreateResourceSignature {\n  /**\n   * Creates a new resource.\n   *\n   *\n   * ## Connected schedules\n   *\n   * A new event *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration))\n   * is automatically created for the resource.\n   *\n   * ## Locations\n   *\n   * If you don't specify `locationOptions`, Wix Bookings automatically sets\n   * `locationOptions.availableInAllLocations` to `true`.\n   *\n   * If you specify 1 business location ID in `locationOptions.specificLocationOptions.businessLocations`,\n   * you must specify `locationOptions.specificLocationOptions.availableInBusinessLocations`\n   * as `true`. Currently, you can specify only a single business location.\n   * @param - Resource to create.\n   * @returns Created resource.\n   */\n  (resource: NonNullablePaths<Resource, `name`, 2>): Promise<\n    NonNullablePaths<\n      Resource,\n      | `workingHoursSchedules.values`\n      | `locationOptions.specificLocationOptions.businessLocations`,\n      4\n    > & {\n      __validationErrorsType?: CreateResourceValidationErrors;\n    }\n  >;\n}\n\nexport function bulkCreateResources(\n  httpClient: HttpClient\n): BulkCreateResourcesSignature {\n  return (\n    resources: NonNullablePaths<Resource, `name`, 2>[],\n    options?: BulkCreateResourcesOptions\n  ) =>\n    universalBulkCreateResources(\n      resources,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface BulkCreateResourcesSignature {\n  /**\n   * Creates up to 50 resources.\n   *\n   *\n   * Refer to *Create Resource*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/create-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/create-resource))\n   * for more details.\n   * @param - Resources to create.\n   */\n  (\n    resources: NonNullablePaths<Resource, `name`, 2>[],\n    options?: BulkCreateResourcesOptions\n  ): Promise<\n    NonNullablePaths<\n      BulkCreateResourcesResponse,\n      | `results`\n      | `results.${number}.itemMetadata.originalIndex`\n      | `results.${number}.itemMetadata.success`\n      | `results.${number}.itemMetadata.error.code`\n      | `results.${number}.itemMetadata.error.description`\n      | `bulkActionMetadata.totalSuccesses`\n      | `bulkActionMetadata.totalFailures`\n      | `bulkActionMetadata.undetailedFailures`,\n      6\n    > & {\n      __validationErrorsType?: BulkCreateResourcesValidationErrors;\n    }\n  >;\n}\n\nexport function getResource(httpClient: HttpClient): GetResourceSignature {\n  return (resourceId: string) =>\n    universalGetResource(\n      resourceId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetResourceSignature {\n  /**\n   * Retrieves a resource.\n   * @param - ID of the resource to retrieve.\n   * @returns Retrieved resource.\n   */\n  (resourceId: string): Promise<\n    NonNullablePaths<\n      Resource,\n      | `workingHoursSchedules.values`\n      | `locationOptions.specificLocationOptions.businessLocations`,\n      4\n    >\n  >;\n}\n\nexport function updateResource(\n  httpClient: HttpClient\n): UpdateResourceSignature {\n  return (\n    _id: string,\n    resource: NonNullablePaths<UpdateResource, `revision`, 2>\n  ) =>\n    universalUpdateResource(\n      _id,\n      resource,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateResourceSignature {\n  /**\n   * Updates a resource.\n   *\n   *\n   * Each time the resource is updated, `revision` increments by 1. You must include the current revision of the resource when updating it.\n   * This ensures you're working with the latest service information and prevents unintended overwrites.\n   * @param - Resource ID.\n   * @returns Updated resource.\n   */\n  (\n    _id: string,\n    resource: NonNullablePaths<UpdateResource, `revision`, 2>\n  ): Promise<\n    NonNullablePaths<\n      Resource,\n      | `workingHoursSchedules.values`\n      | `locationOptions.specificLocationOptions.businessLocations`,\n      4\n    > & {\n      __validationErrorsType?: UpdateResourceValidationErrors;\n    }\n  >;\n}\n\nexport function bulkUpdateResources(\n  httpClient: HttpClient\n): BulkUpdateResourcesSignature {\n  return (\n    resources: NonNullablePaths<\n      MaskedResource,\n      `resource._id` | `resource.revision`,\n      3\n    >[],\n    options?: BulkUpdateResourcesOptions\n  ) =>\n    universalBulkUpdateResources(\n      resources,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface BulkUpdateResourcesSignature {\n  /**\n   * Updates multiple resources.\n   *\n   *\n   * Refer to *Update Resource*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/update-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/update-resource))\n   * for more details.\n   * @param - Resources to update.\n   */\n  (\n    resources: NonNullablePaths<\n      MaskedResource,\n      `resource._id` | `resource.revision`,\n      3\n    >[],\n    options?: BulkUpdateResourcesOptions\n  ): Promise<\n    NonNullablePaths<\n      BulkUpdateResourcesResponse,\n      | `results`\n      | `results.${number}.itemMetadata.originalIndex`\n      | `results.${number}.itemMetadata.success`\n      | `results.${number}.itemMetadata.error.code`\n      | `results.${number}.itemMetadata.error.description`\n      | `bulkActionMetadata.totalSuccesses`\n      | `bulkActionMetadata.totalFailures`\n      | `bulkActionMetadata.undetailedFailures`,\n      6\n    > & {\n      __validationErrorsType?: BulkUpdateResourcesValidationErrors;\n    }\n  >;\n}\n\nexport function deleteResource(\n  httpClient: HttpClient\n): DeleteResourceSignature {\n  return (resourceId: string) =>\n    universalDeleteResource(\n      resourceId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DeleteResourceSignature {\n  /**\n   * Deletes a resource.\n   *\n   *\n   * Deleting a resource cancels its event schedule and all its working hour\n   * schedules that aren't shared with another resource. Learn more about\n   * _how Bookings uses the Calendar APIs_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).\n   * @param - ID of the resource to delete.\n   */\n  (resourceId: string): Promise<void>;\n}\n\nexport function bulkDeleteResources(\n  httpClient: HttpClient\n): BulkDeleteResourcesSignature {\n  return (ids: string[]) =>\n    universalBulkDeleteResources(\n      ids,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface BulkDeleteResourcesSignature {\n  /**\n   * Deletes multiple resources.\n   *\n   *\n   * Refer to *Delete Resource*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/delete-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/delete-resource))\n   * for more details.\n   * @param - IDs of the resources to delete.\n   */\n  (ids: string[]): Promise<\n    NonNullablePaths<\n      BulkDeleteResourcesResponse,\n      | `results`\n      | `results.${number}.itemMetadata.originalIndex`\n      | `results.${number}.itemMetadata.success`\n      | `results.${number}.itemMetadata.error.code`\n      | `results.${number}.itemMetadata.error.description`\n      | `bulkActionMetadata.totalSuccesses`\n      | `bulkActionMetadata.totalFailures`\n      | `bulkActionMetadata.undetailedFailures`,\n      6\n    >\n  >;\n}\n\nexport function searchResources(\n  httpClient: HttpClient\n): SearchResourcesSignature {\n  return (search: ResourceSearch) =>\n    universalSearchResources(\n      search,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface SearchResourcesSignature {\n  /**\n   * Retrieves a list of resources matching the provided search criteria.\n   *\n   *\n   * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.\n   * @param - Search criteria including filter, sort, aggregations, and paging options.\n   *\n   * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.\n   */\n  (search: ResourceSearch): Promise<\n    NonNullablePaths<\n      SearchResourcesResponse,\n      | `resources`\n      | `aggregationData.results`\n      | `aggregationData.results.${number}.scalar.type`\n      | `aggregationData.results.${number}.scalar.value`\n      | `aggregationData.results.${number}.name`\n      | `aggregationData.results.${number}.type`\n      | `aggregationData.results.${number}.fieldPath`,\n      6\n    >\n  >;\n}\n\nexport function queryResources(\n  httpClient: HttpClient\n): QueryResourcesSignature {\n  return () =>\n    universalQueryResources(\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface QueryResourcesSignature {\n  /**\n   * Creates a query to retrieve a list of resources.\n   *\n   * The `queryResources()` function builds a query to retrieve a list of resources and returns a `ResourcesQueryBuilder` object.\n   *\n   * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-builder/find) function.\n   *\n   * You can refine the query by chaining `ResourcesQueryBuilder` functions onto the query. `ResourcesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResources()` returns.\n   *\n   * `queryResources()` runs with the following `ResourcesQueryBuilder` default that you can override:\n   *\n   * + `limit` is `50`.\n   * + Sorted by `id` in ascending order.\n   *\n   * The functions that are chained to `queryResources()` are applied in the order they are called. For example, if you apply `ascending(\"typeId\")` and then `ascending(\"name\")`, the results are sorted first by the `\"typeId\"`, and then, if there are multiple results with the same `\"typeId\"`, the items are sorted by `\"name\"`.\n   *\n   * The following `ResourcesQueryBuilder` functions are supported for the `queryResources()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-result/items) property in `ResourcesQueryResult`.\n   */\n  (): ResourcesQueryBuilder;\n}\n\nexport function countResources(\n  httpClient: HttpClient\n): CountResourcesSignature {\n  return (options?: CountResourcesOptions) =>\n    universalCountResources(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CountResourcesSignature {\n  /**\n   * Counts resources according to given criteria.\n   *\n   *\n   * Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters and sorting options.\n   * @param - Filter to base the count on. See *the supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for a complete list of filtering options.\n   */\n  (options?: CountResourcesOptions): Promise<\n    NonNullablePaths<CountResourcesResponse, `count`, 2>\n  >;\n}\n\nexport const onResourceCreated = EventDefinition(\n  'wix.bookings.resources.v2.resource_created',\n  true,\n  (event: ResourceCreatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<ResourceCreatedEnvelope>();\nexport const onResourceDeleted = EventDefinition(\n  'wix.bookings.resources.v2.resource_deleted',\n  true,\n  (event: ResourceDeletedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'undefined.createdDate' },\n            { path: 'undefined.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<ResourceDeletedEnvelope>();\nexport const onResourceUpdated = EventDefinition(\n  'wix.bookings.resources.v2.resource_updated',\n  true,\n  (event: ResourceUpdatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<ResourceUpdatedEnvelope>();\n\nexport {\n  ActionEvent,\n  Aggregation,\n  AggregationData,\n  AggregationKindOneOf,\n  AggregationResults,\n  AggregationResultsResultOneOf,\n  AggregationResultsScalarResult,\n  AggregationType,\n  ApplicationError,\n  BaseEventMetadata,\n  BulkActionMetadata,\n  BulkCreateResourcesOptions,\n  BulkCreateResourcesRequest,\n  BulkCreateResourcesResponse,\n  BulkDeleteResourcesRequest,\n  BulkDeleteResourcesResponse,\n  BulkResourceResult,\n  BulkUpdateResourcesOptions,\n  BulkUpdateResourcesRequest,\n  BulkUpdateResourcesResponse,\n  BusinessLocation,\n  CountResourcesOptions,\n  CountResourcesRequest,\n  CountResourcesResponse,\n  CreateResourceRequest,\n  CreateResourceResponse,\n  CursorPaging,\n  CursorPagingMetadata,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  CursorSearch,\n  CursorSearchPagingMethodOneOf,\n  Cursors,\n  DateHistogramAggregation,\n  DateHistogramResult,\n  DateHistogramResults,\n  DeleteResourceRequest,\n  DeleteResourceResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  Empty,\n  EntityCreatedEvent,\n  EntityDeletedEvent,\n  EntityUpdatedEvent,\n  EventMetadata,\n  EventsSchedule,\n  ExtendedFields,\n  FixResourceSchedulesRequest,\n  FixResourceSchedulesResponse,\n  GetDeletedResourceRequest,\n  GetDeletedResourceResponse,\n  GetResourceRequest,\n  GetResourceResponse,\n  GroupByAggregation,\n  GroupByAggregationKindOneOf,\n  GroupByValueResults,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  IncludeMissingValuesOptions,\n  Interval,\n  ItemMetadata,\n  ListDeletedResourcesRequest,\n  ListDeletedResourcesResponse,\n  LocationOptions,\n  ManagementType,\n  MaskedResource,\n  MessageEnvelope,\n  MissingValues,\n  Mode,\n  NestedAggregation,\n  NestedAggregationItem,\n  NestedAggregationItemKindOneOf,\n  NestedAggregationResults,\n  NestedAggregationResultsResultOneOf,\n  NestedAggregationType,\n  NestedResultValue,\n  NestedResultValueResultOneOf,\n  NestedResults,\n  NestedValueAggregationResult,\n  QueryResourcesRequest,\n  QueryResourcesResponse,\n  RangeAggregation,\n  RangeAggregationResult,\n  RangeBucket,\n  RangeResult,\n  RangeResults,\n  ReindexTenantRequest,\n  ReindexTenantResponse,\n  RemoveResourceFromTrashBinRequest,\n  RemoveResourceFromTrashBinResponse,\n  Resource,\n  ResourceCompositionDetailsOneOf,\n  ResourceCreatedEnvelope,\n  ResourceDeletedEnvelope,\n  ResourceSearchSpec,\n  ResourceUpdatedEnvelope,\n  ResourcesQueryBuilder,\n  ResourcesQueryResult,\n  RestoreInfo,\n  RestoreResourceFromTrashBinRequest,\n  RestoreResourceFromTrashBinResponse,\n  Results,\n  ScalarAggregation,\n  ScalarResult,\n  ScalarType,\n  Schedule,\n  SearchDetails,\n  SearchResourcesRequest,\n  SearchResourcesResponse,\n  SingleResource,\n  SortDirection,\n  SortOrder,\n  SortType,\n  Sorting,\n  SpecificLocation,\n  UpdateResource,\n  UpdateResourceRequest,\n  UpdateResourceResponse,\n  V2WorkingHoursSchedules,\n  ValueAggregation,\n  ValueAggregationOptionsOneOf,\n  ValueAggregationResult,\n  ValueResult,\n  ValueResults,\n  WebhookIdentityType,\n  WorkingHoursSchedule,\n  WorkingHoursSchedules,\n} from './bookings-resources-v2-resource-resources.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport {\n  HttpClient,\n  HttpResponse,\n  Search as SearchSdkType,\n  NonNullablePaths,\n  SearchSpec,\n} from '@wix/sdk-types';\nimport * as ambassadorWixBookingsResourcesV2Resource from './bookings-resources-v2-resource-resources.http.js';\n// @ts-ignore\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/**\n * A resource represents an entity, such as a room or a staff member, that can be\n * scheduled for use in a *booking*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).\n * The availability of a resource is tracked to ensure that it\n * can be allocated at a requested time slot and to prevent double bookings.\n */\nexport interface Resource extends ResourceCompositionDetailsOneOf {\n  /**\n   * Resource ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the resource is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating the resource.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was created.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was last updated.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the resource.\n   * @maxLength 40\n   * @minLength 1\n   */\n  name?: string | null;\n  /**\n   * ID of the *resource type*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).\n   *\n   * Once a type has been set it can't be modified. You can create a resource\n   * without specifying a type. However, customers can't book such resources.\n   * @format GUID\n   * @immutable\n   */\n  typeId?: string | null;\n  /**\n   * Information about the *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * specifying the working hours and locations of the resource.\n   *\n   * Resources without a working hour schedule are available 24/7 at the locations\n   * specified in the field `locationOptions`.\n   * When both `workingHoursSchedules` and `locationOptions` are set,\n   * `workingHoursSchedules` takes precedence.\n   */\n  workingHoursSchedules?: WorkingHoursSchedules;\n  /** Information about the location where the resource is available. */\n  locationOptions?: LocationOptions;\n  /**\n   * Schedule containing the *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * for which the resource has been booked.\n   * @readonly\n   */\n  eventsSchedule?: EventsSchedule;\n  /** Extensions enabling users to save custom data related to the resource. */\n  extendedFields?: ExtendedFields;\n}\n\n/** @oneof */\nexport interface ResourceCompositionDetailsOneOf {}\n\nexport interface WorkingHoursSchedule {\n  /**\n   * ID of the working hour *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   * @format GUID\n   */\n  scheduleId?: string | null;\n  /**\n   * Whether the schedule is shared by multiple resources or unique for this resource.\n   *\n   * Default: `false`\n   * @readonly\n   */\n  shared?: boolean | null;\n}\n\nexport interface SingleResource {\n  /**\n   * Schedule details that specify the working hours and locations of this resource.\n   *\n   * When this field is missing or empty, the resource is assumed to be available\n   * around the clock (24/7) at the locations specified in `locationOptions`. When\n   * both `workingHoursSchedules` and `locationOptions` are set,\n   * `workingHoursSchedules` takes precedence.\n   */\n  workingHoursSchedules?: V2WorkingHoursSchedules;\n  /**\n   * Information about the location where the resource is available.\n   *\n   * If you don't specify `locationOptions`, Wix Bookings automatically sets\n   * `{\"availableInAllLocations\": true}`.\n   */\n  locationOptions?: LocationOptions;\n  /**\n   * Schedule containing the *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * for which the resource has been booked.\n   * @readonly\n   */\n  eventsSchedule?: Schedule;\n}\n\nexport interface V2WorkingHoursSchedules {\n  /**\n   * Schedules specifying the working hours of the resource. Currently, only a single schedule is supported.\n   * @maxSize 1\n   */\n  values?: Schedule[];\n}\n\n/** Deprecated and subject to removal */\nexport interface Schedule {\n  /**\n   * Schedule ID. See [Schedules API](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/schedule-object) for more information.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n  /**\n   * Whether this schedule is used by multiple resources or unique for this resource.\n   *\n   * `true` if it is a shared schedule (for example from a business or location), `false` this is a custom schedule specific to the resource.\n   * Default: `false`.\n   */\n  shared?: boolean | null;\n}\n\nexport interface LocationOptions {\n  /**\n   * Whether the resource is available in all *business locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).\n   *\n   * - `true`: The resource is available in all business locations.\n   * - `false`: The resource is available only in specific locations.\n   *\n   * Default: `false`\n   */\n  availableInAllLocations?: boolean | null;\n  /** Details of resource availability in specific locations. */\n  specificLocationOptions?: SpecificLocation;\n}\n\nexport interface SpecificLocation {\n  /**\n   * Whether the resource is available in *business locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).\n   *\n   * - `true`: The resource is available in business locations.\n   * - `false`: The resource isn't available in business locations.\n   *\n   * Default: `false`\n   */\n  availableInBusinessLocations?: boolean | null;\n  /**\n   * Information about the business locations where the resource is available.\n   * Not returned, if the resource is available in either all business locations\n   * or in no business location.\n   * You can specify up to 100 business locations.\n   * @maxSize 100\n   */\n  businessLocations?: BusinessLocation[];\n}\n\nexport interface BusinessLocation {\n  /**\n   * ID of the business *location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   * @format GUID\n   */\n  locationId?: string | null;\n}\n\nexport interface WorkingHoursSchedules {\n  /**\n   * Schedules specifying the working hours of the resource. Currently, only a single schedule is supported.\n   * @maxSize 1\n   */\n  values?: WorkingHoursSchedule[];\n}\n\nexport interface EventsSchedule {\n  /**\n   * ID of the event *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   * @format GUID\n   */\n  scheduleId?: string | null;\n}\n\nexport enum ManagementType {\n  /** The resource is managed by a Wix user. */\n  WIX_USER = 'WIX_USER',\n}\n\n/** @enumType */\nexport type ManagementTypeWithLiterals = ManagementType | 'WIX_USER';\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\nexport interface CreateResourceRequest {\n  /** Resource to create. */\n  resource: Resource;\n}\n\nexport interface CreateResourceResponse {\n  /** Created resource. */\n  resource?: Resource;\n}\n\nexport interface BulkCreateResourcesRequest {\n  /**\n   * Resources to create.\n   * @maxSize 50\n   */\n  resources: Resource[];\n  /**\n   * Whether to include the created resources in the response.\n   *\n   * Default: `false`\n   */\n  returnEntity?: boolean;\n}\n\nexport interface BulkCreateResourcesResponse {\n  /** The result of each resource creation. */\n  results?: BulkResourceResult[];\n  /** Create statistics. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkResourceResult {\n  /** Item metadata. */\n  itemMetadata?: ItemMetadata;\n  /** The resulting resource after the bulk operation. */\n  item?: Resource;\n}\n\nexport interface ItemMetadata {\n  /**\n   * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Index of the item within the request array. Allows for correlation between request and response items. */\n  originalIndex?: number;\n  /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */\n  success?: boolean;\n  /** Details about the error in case of failure. */\n  error?: ApplicationError;\n}\n\nexport interface ApplicationError {\n  /** Error code. */\n  code?: string;\n  /** Description of the error. */\n  description?: string;\n  /** Data related to the error. */\n  data?: Record<string, any> | null;\n}\n\nexport interface BulkActionMetadata {\n  /** Number of items that were successfully processed. */\n  totalSuccesses?: number;\n  /** Number of items that couldn't be processed. */\n  totalFailures?: number;\n  /** Number of failures without details because detailed failure threshold was exceeded. */\n  undetailedFailures?: number;\n}\n\nexport interface GetResourceRequest {\n  /**\n   * ID of the resource to retrieve.\n   * @format GUID\n   */\n  resourceId: string;\n}\n\nexport interface GetResourceResponse {\n  /** Retrieved resource. */\n  resource?: Resource;\n}\n\nexport interface GetDeletedResourceRequest {\n  /**\n   * ID of the deleted resource to retrieve from the trash bin.\n   * @format GUID\n   */\n  resourceId?: string;\n}\n\nexport interface GetDeletedResourceResponse {\n  /** Retrieved resource. */\n  resource?: Resource;\n}\n\nexport interface ListDeletedResourcesRequest {\n  /**\n   * IDs of the resources to retrieve.\n   * @format GUID\n   * @maxSize 100\n   */\n  resourceIds?: string[];\n  /** Paging details, allowing you specify a limit and a cursor. */\n  paging?: CursorPaging;\n}\n\nexport interface CursorPaging {\n  /**\n   * Maximum number of items to return in the results.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface ListDeletedResourcesResponse {\n  /** Retrieved resources. */\n  resources?: Resource[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /**\n   * Number of items returned in the current response page.\n   *\n   * This count reflects the actual number of items in the current result set,\n   * which may be less than the requested limit if fewer items are available.\n   */\n  count?: number | null;\n  /**\n   * Navigation cursors for moving between result pages.\n   *\n   * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor\n   * to retrieve subsequent pages and `prev` cursor to go back to previous pages.\n   * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).\n   */\n  cursors?: Cursors;\n  /**\n   * Indicates whether additional results are available beyond the current page.\n   *\n   * - `true`: More results exist and can be retrieved using the `next` cursor.\n   * - `false`: This is the final page of results.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor token for retrieving the next page of results.\n   *\n   * Use this token in subsequent requests to continue pagination forward.\n   * Value is `null` when on the last page of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor token for retrieving the previous page of results.\n   *\n   * Use this token to navigate backwards through result pages.\n   * Value is `null` when on the first page of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface RemoveResourceFromTrashBinRequest {\n  /**\n   * ID of the resource to permanently delete from the trash bin.\n   * @format GUID\n   */\n  resourceId?: string;\n}\n\nexport interface RemoveResourceFromTrashBinResponse {}\n\nexport interface RestoreResourceFromTrashBinRequest {\n  /**\n   * ID of the resource to restore from the trash bin.\n   * @format GUID\n   */\n  resourceId?: string;\n}\n\nexport interface RestoreResourceFromTrashBinResponse {\n  /** Restored resource. */\n  resource?: Resource;\n}\n\nexport interface UpdateResourceRequest {\n  /** Resource to update. */\n  resource: Resource;\n}\n\nexport interface UpdateResourceResponse {\n  /** Updated resource. */\n  resource?: Resource;\n}\n\nexport interface BulkUpdateResourcesRequest {\n  /**\n   * Resources to update.\n   * @minSize 1\n   * @maxSize 100\n   */\n  resources: MaskedResource[];\n  /**\n   * Whether to include the updated resources in the response.\n   *\n   * Default: `false`\n   */\n  returnEntity?: boolean;\n}\n\nexport interface MaskedResource {\n  /** Resource to update. */\n  resource?: Resource;\n  /** Explicit list of fields to update. */\n  fieldMask?: string[];\n}\n\nexport interface BulkUpdateResourcesResponse {\n  /** The result of each resource update. */\n  results?: BulkResourceResult[];\n  /** Update statistics. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface DeleteResourceRequest {\n  /**\n   * ID of the resource to delete.\n   * @format GUID\n   */\n  resourceId: string;\n}\n\nexport interface DeleteResourceResponse {}\n\nexport interface BulkDeleteResourcesRequest {\n  /**\n   * IDs of the resources to delete.\n   * @format GUID\n   * @maxSize 50\n   */\n  ids: string[];\n}\n\nexport interface BulkDeleteResourcesResponse {\n  /** The result of each resource removal. */\n  results?: BulkResourceResult[];\n  /** Delete statistics. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface SearchResourcesRequest {\n  /**\n   * Search criteria including filter, sort, aggregations, and paging options.\n   *\n   * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.\n   */\n  search?: CursorSearch;\n}\n\nexport interface CursorSearch extends CursorSearchPagingMethodOneOf {\n  /**\n   * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,\n   * `filter`, `sort`, or `search` can't be specified.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object for narrowing search results. For example, to return only resources available at specific business locations: `\"filter\": {\"single_resource.location_options.specific_location_options.business_locations.location_id\": {\"$in\": [\"location-id-1\", \"location-id-2\"]}}`.\n   *\n   * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Array of sort objects specifying result order. For example, to sort by resource name in ascending order: `\"sort\": [{\"fieldName\": \"name\", \"order\": \"ASC\"}]`.\n   *\n   * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).\n   * @maxSize 10\n   */\n  sort?: Sorting[];\n  /**\n   * Aggregations for grouping data into categories (facets) and providing summaries for each category.\n   * For example, use aggregations to categorize search results by resource type, management type, or location availability.\n   * @maxSize 10\n   */\n  aggregations?: Aggregation[];\n  /** Free text to match in searchable fields. */\n  search?: SearchDetails;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.\n   *\n   * Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).\n   * @maxLength 50\n   */\n  timeZone?: string | null;\n}\n\n/** @oneof */\nexport interface CursorSearchPagingMethodOneOf {\n  /**\n   * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,\n   * `filter`, `sort`, or `search` can't be specified.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface Aggregation extends AggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. */\n  dateHistogram?: DateHistogramAggregation;\n  /** Nested aggregation configuration. */\n  nested?: NestedAggregation;\n  /**\n   * Aggregation name, returned in `aggregations.results.name`.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /** Type of aggregation. Client must specify matching aggregation field below. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Field to aggregate by. Use dot notation to specify a JSON path. For example `name` or `locationOptions.businessLocations.locationId`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n  /**\n   * Deprecated. Use `nested` instead.\n   * @deprecated Deprecated. Use `nested` instead.\n   * @replacedBy kind.nested\n   * @targetRemovalDate 2024-03-30\n   */\n  groupBy?: GroupByAggregation;\n}\n\n/** @oneof */\nexport interface AggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. */\n  dateHistogram?: DateHistogramAggregation;\n  /** Nested aggregation configuration. */\n  nested?: NestedAggregation;\n}\n\nexport interface RangeBucket {\n  /** Inclusive lower bound of the range. Required if `to` isn't specified. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. Required if `from` isn't specified. */\n  to?: number | null;\n}\n\nexport enum SortType {\n  /** Number of matches in the results. */\n  COUNT = 'COUNT',\n  /** Alphabetically by the field value. */\n  VALUE = 'VALUE',\n}\n\n/** @enumType */\nexport type SortTypeWithLiterals = SortType | 'COUNT' | 'VALUE';\n\nexport enum SortDirection {\n  /** Descending order. */\n  DESC = 'DESC',\n  /** Ascending order. */\n  ASC = 'ASC',\n}\n\n/** @enumType */\nexport type SortDirectionWithLiterals = SortDirection | 'DESC' | 'ASC';\n\nexport enum MissingValues {\n  /** Exclude missing values from the aggregation results. */\n  EXCLUDE = 'EXCLUDE',\n  /** Include missing values in the aggregation results. */\n  INCLUDE = 'INCLUDE',\n}\n\n/** @enumType */\nexport type MissingValuesWithLiterals = MissingValues | 'EXCLUDE' | 'INCLUDE';\n\nexport interface IncludeMissingValuesOptions {\n  /**\n   * Custom bucket name for missing values.\n   *\n   * Default values:\n   * - string: `N/A`\n   * - int: `0`\n   * - bool: `false`\n   * @maxLength 20\n   */\n  addToBucket?: string;\n}\n\nexport enum ScalarType {\n  UNKNOWN_SCALAR_TYPE = 'UNKNOWN_SCALAR_TYPE',\n  /** Total number of distinct values. */\n  COUNT_DISTINCT = 'COUNT_DISTINCT',\n  /** Minimum value. */\n  MIN = 'MIN',\n  /** Maximum value. */\n  MAX = 'MAX',\n}\n\n/** @enumType */\nexport type ScalarTypeWithLiterals =\n  | ScalarType\n  | 'UNKNOWN_SCALAR_TYPE'\n  | 'COUNT_DISTINCT'\n  | 'MIN'\n  | 'MAX';\n\nexport interface ValueAggregation extends ValueAggregationOptionsOneOf {\n  /** Options for including missing values in the aggregation results. */\n  includeOptions?: IncludeMissingValuesOptions;\n  /** Whether to sort by number of matches or value of the field. */\n  sortType?: SortTypeWithLiterals;\n  /** Whether to sort in ascending or descending order. */\n  sortDirection?: SortDirectionWithLiterals;\n  /**\n   * Number of aggregations to return.\n   *\n   * Min: `1`\n   * Max: `250`\n   * Default: `10`\n   */\n  limit?: number | null;\n  /**\n   * Whether missing values should be included or excluded from the aggregation results.\n   *\n   * Default: `EXCLUDE`\n   */\n  missingValues?: MissingValuesWithLiterals;\n}\n\n/** @oneof */\nexport interface ValueAggregationOptionsOneOf {\n  /** Options for including missing values in the aggregation results. */\n  includeOptions?: IncludeMissingValuesOptions;\n}\n\nexport enum NestedAggregationType {\n  UNKNOWN_AGGREGATION_TYPE = 'UNKNOWN_AGGREGATION_TYPE',\n  /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */\n  VALUE = 'VALUE',\n  /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */\n  RANGE = 'RANGE',\n  /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */\n  SCALAR = 'SCALAR',\n  /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */\n  DATE_HISTOGRAM = 'DATE_HISTOGRAM',\n}\n\n/** @enumType */\nexport type NestedAggregationTypeWithLiterals =\n  | NestedAggregationType\n  | 'UNKNOWN_AGGREGATION_TYPE'\n  | 'VALUE'\n  | 'RANGE'\n  | 'SCALAR'\n  | 'DATE_HISTOGRAM';\n\nexport interface RangeAggregation {\n  /**\n   * List of range buckets defining the ranges for aggregation. During aggregation, each entity is placed in the first bucket where its value falls within the specified range bounds.\n   * @maxSize 50\n   */\n  buckets?: RangeBucket[];\n}\n\nexport interface ScalarAggregation {\n  /** Operator for the scalar aggregation, for example `COUNT_DISTINCT`, `MIN`, `MAX`. */\n  type?: ScalarTypeWithLiterals;\n}\n\nexport interface DateHistogramAggregation {\n  /** Time interval for date histogram aggregation, for example `DAY`, `HOUR`, `MONTH`. */\n  interval?: IntervalWithLiterals;\n}\n\nexport enum Interval {\n  UNKNOWN_INTERVAL = 'UNKNOWN_INTERVAL',\n  /** Yearly interval */\n  YEAR = 'YEAR',\n  /** Monthly interval */\n  MONTH = 'MONTH',\n  /** Weekly interval */\n  WEEK = 'WEEK',\n  /** Daily interval */\n  DAY = 'DAY',\n  /** Hourly interval */\n  HOUR = 'HOUR',\n  /** Minute interval */\n  MINUTE = 'MINUTE',\n  /** Second interval */\n  SECOND = 'SECOND',\n}\n\n/** @enumType */\nexport type IntervalWithLiterals =\n  | Interval\n  | 'UNKNOWN_INTERVAL'\n  | 'YEAR'\n  | 'MONTH'\n  | 'WEEK'\n  | 'DAY'\n  | 'HOUR'\n  | 'MINUTE'\n  | 'SECOND';\n\nexport interface NestedAggregationItem extends NestedAggregationItemKindOneOf {\n  /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. Calculates counts within user-defined value ranges. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. Calculates counts within time intervals. */\n  dateHistogram?: DateHistogramAggregation;\n  /**\n   * User-defined name of aggregation. Must be unique and will appear in aggregation results.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /** Type of aggregation. Client must specify matching aggregation field below. */\n  type?: NestedAggregationTypeWithLiterals;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface NestedAggregationItemKindOneOf {\n  /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. Calculates counts within user-defined value ranges. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. Calculates counts within time intervals. */\n  dateHistogram?: DateHistogramAggregation;\n}\n\nexport enum AggregationType {\n  UNKNOWN_AGGREGATION_TYPE = 'UNKNOWN_AGGREGATION_TYPE',\n  /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */\n  VALUE = 'VALUE',\n  /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */\n  RANGE = 'RANGE',\n  /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */\n  SCALAR = 'SCALAR',\n  /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */\n  DATE_HISTOGRAM = 'DATE_HISTOGRAM',\n  /** Flattened list of aggregations, where each aggregation is nested within previous 1. */\n  NESTED = 'NESTED',\n}\n\n/** @enumType */\nexport type AggregationTypeWithLiterals =\n  | AggregationType\n  | 'UNKNOWN_AGGREGATION_TYPE'\n  | 'VALUE'\n  | 'RANGE'\n  | 'SCALAR'\n  | 'DATE_HISTOGRAM'\n  | 'NESTED';\n\n/** Nested aggregation for multi-level faceted search. Allows exploring large amounts of data through multiple levels of categorization, where each subsequent aggregation is nested within the previous aggregation to create hierarchical data summaries. */\nexport interface NestedAggregation {\n  /**\n   * Flattened list of aggregations where each next aggregation is nested within the previous 1.\n   * @minSize 2\n   * @maxSize 3\n   */\n  nestedAggregations?: NestedAggregationItem[];\n}\n\nexport interface GroupByAggregation extends GroupByAggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n  /**\n   * User-defined name of aggregation. Must be unique and will appear in aggregation results.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface GroupByAggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n}\n\nexport interface SearchDetails {\n  /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */\n  mode?: ModeWithLiterals;\n  /**\n   * Search term or expression.\n   * @maxLength 100\n   */\n  expression?: string | null;\n  /**\n   * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `locationOptions.availableInAllLocations`.\n   * @maxLength 200\n   * @maxSize 20\n   */\n  fields?: string[];\n  /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */\n  fuzzy?: boolean;\n}\n\nexport enum Mode {\n  /** At least 1 of the search terms must be present. */\n  OR = 'OR',\n  /** All search terms must be present. */\n  AND = 'AND',\n}\n\n/** @enumType */\nexport type ModeWithLiterals = Mode | 'OR' | 'AND';\n\nexport interface SearchResourcesResponse {\n  /**\n   * Retrieved resources that match the search criteria specified in the request.\n   *\n   * Each resource includes all available information such as name, type, management details,\n   * location options, working hour schedules, and availability configurations.\n   */\n  resources?: Resource[];\n  /**\n   * Cursor-based paging metadata for navigating through search results.\n   *\n   * Contains navigation information including the current page cursor, whether additional\n   * results are available, and result counts. Use the `next` cursor to fetch subsequent pages.\n   */\n  pagingMetadata?: CursorPagingMetadata;\n  /**\n   * Aggregation results derived from the aggregations specified in the search request.\n   *\n   * Provides analytical summaries such as resource counts by type, location distribution,\n   * management type statistics, or custom groupings. Only populated when aggregations\n   * are included in the search criteria.\n   */\n  aggregationData?: AggregationData;\n}\n\nexport interface AggregationData {\n  /**\n   * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.\n   * @maxSize 10000\n   */\n  results?: AggregationResults[];\n}\n\nexport interface ValueAggregationResult {\n  /**\n   * Value of the field.\n   * @maxLength 100\n   */\n  value?: string;\n  /** Count of entities with this value. */\n  count?: number;\n}\n\nexport interface RangeAggregationResult {\n  /** Inclusive lower bound of the range. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. */\n  to?: number | null;\n  /** Count of entities in this range. */\n  count?: number;\n}\n\nexport interface NestedAggregationResults\n  extends NestedAggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /**\n   * User-defined name of aggregation, matches the one specified in request.\n   * @maxLength 100\n   */\n  name?: string;\n  /** Type of aggregation that matches result. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface NestedAggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n}\n\nexport interface ValueResults {\n  /**\n   * Array of value aggregation results, each containing a field value and the count of entities with that value.\n   * @maxSize 250\n   */\n  results?: ValueAggregationResult[];\n}\n\nexport interface RangeResults {\n  /**\n   * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.\n   * @maxSize 50\n   */\n  results?: RangeAggregationResult[];\n}\n\nexport interface AggregationResultsScalarResult {\n  /** Type of scalar aggregation. */\n  type?: ScalarTypeWithLiterals;\n  /** Value of the scalar aggregation. */\n  value?: number;\n}\n\nexport interface NestedValueAggregationResult {\n  /**\n   * Value of the field.\n   * @maxLength 1000\n   */\n  value?: string;\n  /** Nested aggregations result data. */\n  nestedResults?: NestedAggregationResults;\n}\n\nexport interface ValueResult {\n  /**\n   * Value of the field.\n   * @maxLength 1000\n   */\n  value?: string;\n  /** Count of entities with this value. */\n  count?: number | null;\n}\n\nexport interface RangeResult {\n  /** Inclusive lower bound of the range. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. */\n  to?: number | null;\n  /** Count of entities in this range. */\n  count?: number | null;\n}\n\nexport interface ScalarResult {\n  /** Value of the scalar aggregation. */\n  value?: number;\n}\n\nexport interface NestedResultValue extends NestedResultValueResultOneOf {\n  /** Value aggregation result. */\n  value?: ValueResult;\n  /** Range aggregation result. */\n  range?: RangeResult;\n  /** Scalar aggregation result. */\n  scalar?: ScalarResult;\n  /** Date histogram aggregation result. */\n  dateHistogram?: ValueResult;\n}\n\n/** @oneof */\nexport interface NestedResultValueResultOneOf {\n  /** Value aggregation result. */\n  value?: ValueResult;\n  /** Range aggregation result. */\n  range?: RangeResult;\n  /** Scalar aggregation result. */\n  scalar?: ScalarResult;\n  /** Date histogram aggregation result. */\n  dateHistogram?: ValueResult;\n}\n\nexport interface Results {\n  /** Map of nested aggregation results, keyed by aggregation name. */\n  results?: Record<string, NestedResultValue>;\n}\n\nexport interface DateHistogramResult {\n  /**\n   * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * @maxLength 100\n   */\n  value?: string;\n  /** Count of documents in the bucket. */\n  count?: number;\n}\n\nexport interface GroupByValueResults {\n  /**\n   * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.\n   * @maxSize 1000\n   */\n  results?: NestedValueAggregationResult[];\n}\n\nexport interface DateHistogramResults {\n  /**\n   * Array of date histogram aggregation results, each containing a date bucket and its count.\n   * @maxSize 200\n   */\n  results?: DateHistogramResult[];\n}\n\n/**\n * Results of `NESTED` aggregation type in a flattened form\n * Aggregations in resulting array are keyed by requested aggregation `name`.\n */\nexport interface NestedResults {\n  /**\n   * Array of nested aggregation result groups, each containing multiple aggregation results.\n   * @maxSize 1000\n   */\n  results?: Results[];\n}\n\nexport interface AggregationResults extends AggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /** Group by value aggregation results. */\n  groupedByValue?: GroupByValueResults;\n  /** Date histogram aggregation results. */\n  dateHistogram?: DateHistogramResults;\n  /** Nested aggregation results. */\n  nested?: NestedResults;\n  /**\n   * User-defined name of aggregation as derived from search request.\n   * @maxLength 100\n   */\n  name?: string;\n  /** Type of aggregation that must match specified kind as derived from search request. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface AggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /** Group by value aggregation results. */\n  groupedByValue?: GroupByValueResults;\n  /** Date histogram aggregation results. */\n  dateHistogram?: DateHistogramResults;\n  /** Nested aggregation results. */\n  nested?: NestedResults;\n}\n\nexport interface QueryResourcesRequest {\n  /**\n   * Query for retrieving resources. Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  query?: CursorQuery;\n}\n\nexport interface CursorQuery extends CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   * @maxSize 5\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface QueryResourcesResponse {\n  /** Retrieved resources. */\n  resources?: Resource[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CountResourcesRequest {\n  /**\n   * Filter to base the count on. Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.\n   * @deprecated Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.\n   * @targetRemovalDate 2024-05-01\n   */\n  search?: SearchDetails;\n}\n\nexport interface CountResourcesResponse {\n  /** Total number of resources matching the filter. */\n  count?: number;\n}\n\nexport interface FixResourceSchedulesRequest {\n  /**\n   * ID of the resource to fix schedules for.\n   * @format GUID\n   */\n  resourceId?: string;\n  /** Working hour schedule that must be set for this resource. */\n  workingHoursSchedule?: Schedule;\n}\n\nexport interface FixResourceSchedulesResponse {\n  /** Updated resource. */\n  resource?: Resource;\n}\n\nexport interface ReindexTenantRequest {}\n\nexport interface ReindexTenantResponse {}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface Empty {}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type CreateResourceValidationErrors =\n  | {\n      ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';\n    }\n  | {\n      ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';\n    };\n/** @docsIgnore */\nexport type BulkCreateResourcesValidationErrors =\n  | {\n      ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';\n    }\n  | {\n      ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';\n    }\n  | {\n      ruleName?: 'RESOURCE_WORKING_HOURS_NOT_SUPPORTED_IN_BULK_REQUEST';\n    };\n/** @docsIgnore */\nexport type UpdateResourceValidationErrors =\n  | {\n      ruleName?: 'RESOURCE_NAME_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';\n    }\n  | {\n      ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';\n    }\n  | {\n      ruleName?: 'RESOURCE_WORKING_HOURS_SCHEDULE_ID_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_WORKING_HOURS_SHARED_IS_REQUIRED';\n    };\n/** @docsIgnore */\nexport type BulkUpdateResourcesValidationErrors =\n  | {\n      ruleName?: 'RESOURCE_NAME_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';\n    }\n  | {\n      ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';\n    }\n  | {\n      ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';\n    }\n  | {\n      ruleName?: 'RESOURCE_WORKING_HOURS_SCHEDULE_ID_IS_REQUIRED';\n    }\n  | {\n      ruleName?: 'RESOURCE_WORKING_HOURS_SHARED_IS_REQUIRED';\n    };\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface ResourceCreatedEnvelope {\n  entity: Resource;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a resource is created.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.RESOURCE_READ\n * @webhook\n * @eventType wix.bookings.resources.v2.resource_created\n * @serviceIdentifier wix.bookings.resources.v2.ResourcesService\n * @slug created\n */\nexport declare function onResourceCreated(\n  handler: (event: ResourceCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface ResourceDeletedEnvelope {\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a resource is deleted.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.RESOURCE_READ\n * @webhook\n * @eventType wix.bookings.resources.v2.resource_deleted\n * @serviceIdentifier wix.bookings.resources.v2.ResourcesService\n * @slug deleted\n */\nexport declare function onResourceDeleted(\n  handler: (event: ResourceDeletedEnvelope) => void | Promise<void>\n): void;\n\nexport interface ResourceUpdatedEnvelope {\n  entity: Resource;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when an resource is updated.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.RESOURCE_READ\n * @webhook\n * @eventType wix.bookings.resources.v2.resource_updated\n * @slug updated\n */\nexport declare function onResourceUpdated(\n  handler: (event: ResourceUpdatedEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Creates a new resource.\n *\n *\n * ## Connected schedules\n *\n * A new event *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration))\n * is automatically created for the resource.\n *\n * ## Locations\n *\n * If you don't specify `locationOptions`, Wix Bookings automatically sets\n * `locationOptions.availableInAllLocations` to `true`.\n *\n * If you specify 1 business location ID in `locationOptions.specificLocationOptions.businessLocations`,\n * you must specify `locationOptions.specificLocationOptions.availableInBusinessLocations`\n * as `true`. Currently, you can specify only a single business location.\n * @param resource - Resource to create.\n * @public\n * @requiredField resource\n * @requiredField resource.name\n * @permissionId BOOKINGS.RESOURCE_CREATE\n * @applicableIdentity APP\n * @returns Created resource.\n * @fqn wix.bookings.resources.v2.ResourcesService.CreateResource\n */\nexport async function createResource(\n  resource: NonNullablePaths<Resource, `name`, 2>\n): Promise<\n  NonNullablePaths<\n    Resource,\n    | `workingHoursSchedules.values`\n    | `locationOptions.specificLocationOptions.businessLocations`,\n    4\n  > & {\n    __validationErrorsType?: CreateResourceValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ resource: resource });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.createResource(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resource!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { resource: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['resource']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Creates up to 50 resources.\n *\n *\n * Refer to *Create Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/create-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/create-resource))\n * for more details.\n * @param resources - Resources to create.\n * @public\n * @requiredField resources\n * @requiredField resources.name\n * @permissionId BOOKINGS.RESOURCE_CREATE\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.ResourcesService.BulkCreateResources\n */\nexport async function bulkCreateResources(\n  resources: NonNullablePaths<Resource, `name`, 2>[],\n  options?: BulkCreateResourcesOptions\n): Promise<\n  NonNullablePaths<\n    BulkCreateResourcesResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  > & {\n    __validationErrorsType?: BulkCreateResourcesValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resources: resources,\n    returnEntity: options?.returnEntity,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.bulkCreateResources(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          resources: '$[0]',\n          returnEntity: '$[1].returnEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['resources', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkCreateResourcesOptions {\n  /**\n   * Whether to include the created resources in the response.\n   *\n   * Default: `false`\n   */\n  returnEntity?: boolean;\n}\n\n/**\n * Retrieves a resource.\n * @param resourceId - ID of the resource to retrieve.\n * @public\n * @requiredField resourceId\n * @permissionId BOOKINGS.RESOURCE_READ\n * @applicableIdentity APP\n * @returns Retrieved resource.\n * @fqn wix.bookings.resources.v2.ResourcesService.GetResource\n */\nexport async function getResource(\n  resourceId: string\n): Promise<\n  NonNullablePaths<\n    Resource,\n    | `workingHoursSchedules.values`\n    | `locationOptions.specificLocationOptions.businessLocations`,\n    4\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resourceId: resourceId,\n  });\n\n  const reqOpts = ambassadorWixBookingsResourcesV2Resource.getResource(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resource!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { resourceId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['resourceId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates a resource.\n *\n *\n * Each time the resource is updated, `revision` increments by 1. You must include the current revision of the resource when updating it.\n * This ensures you're working with the latest service information and prevents unintended overwrites.\n * @param _id - Resource ID.\n * @public\n * @requiredField _id\n * @requiredField resource\n * @requiredField resource.revision\n * @permissionId BOOKINGS.RESOURCE_UPDATE\n * @applicableIdentity APP\n * @returns Updated resource.\n * @fqn wix.bookings.resources.v2.ResourcesService.UpdateResource\n */\nexport async function updateResource(\n  _id: string,\n  resource: NonNullablePaths<UpdateResource, `revision`, 2>\n): Promise<\n  NonNullablePaths<\n    Resource,\n    | `workingHoursSchedules.values`\n    | `locationOptions.specificLocationOptions.businessLocations`,\n    4\n  > & {\n    __validationErrorsType?: UpdateResourceValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resource: { ...resource, id: _id },\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.updateResource(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resource!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { resource: '$[1]' },\n        explicitPathsToArguments: { 'resource.id': '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'resource']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateResource {\n  /**\n   * Resource ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the resource is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating the resource.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was created.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was last updated.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the resource.\n   * @maxLength 40\n   * @minLength 1\n   */\n  name?: string | null;\n  /**\n   * ID of the *resource type*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).\n   *\n   * Once a type has been set it can't be modified. You can create a resource\n   * without specifying a type. However, customers can't book such resources.\n   * @format GUID\n   * @immutable\n   */\n  typeId?: string | null;\n  /**\n   * Information about the *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * specifying the working hours and locations of the resource.\n   *\n   * Resources without a working hour schedule are available 24/7 at the locations\n   * specified in the field `locationOptions`.\n   * When both `workingHoursSchedules` and `locationOptions` are set,\n   * `workingHoursSchedules` takes precedence.\n   */\n  workingHoursSchedules?: WorkingHoursSchedules;\n  /** Information about the location where the resource is available. */\n  locationOptions?: LocationOptions;\n  /**\n   * Schedule containing the *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * for which the resource has been booked.\n   * @readonly\n   */\n  eventsSchedule?: EventsSchedule;\n  /** Extensions enabling users to save custom data related to the resource. */\n  extendedFields?: ExtendedFields;\n}\n\n/**\n * Updates multiple resources.\n *\n *\n * Refer to *Update Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/update-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/update-resource))\n * for more details.\n * @param resources - Resources to update.\n * @public\n * @documentationMaturity preview\n * @requiredField resources\n * @requiredField resources.resource._id\n * @requiredField resources.resource.revision\n * @permissionId BOOKINGS.RESOURCE_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.ResourcesService.BulkUpdateResources\n */\nexport async function bulkUpdateResources(\n  resources: NonNullablePaths<\n    MaskedResource,\n    `resource._id` | `resource.revision`,\n    3\n  >[],\n  options?: BulkUpdateResourcesOptions\n): Promise<\n  NonNullablePaths<\n    BulkUpdateResourcesResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  > & {\n    __validationErrorsType?: BulkUpdateResourcesValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resources: resources,\n    returnEntity: options?.returnEntity,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.bulkUpdateResources(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          resources: '$[0]',\n          returnEntity: '$[1].returnEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['resources', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkUpdateResourcesOptions {\n  /**\n   * Whether to include the updated resources in the response.\n   *\n   * Default: `false`\n   */\n  returnEntity?: boolean;\n}\n\n/**\n * Deletes a resource.\n *\n *\n * Deleting a resource cancels its event schedule and all its working hour\n * schedules that aren't shared with another resource. Learn more about\n * _how Bookings uses the Calendar APIs_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).\n * @param resourceId - ID of the resource to delete.\n * @public\n * @requiredField resourceId\n * @permissionId BOOKINGS.RESOURCE_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.ResourcesService.DeleteResource\n */\nexport async function deleteResource(resourceId: string): Promise<void> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resourceId: resourceId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.deleteResource(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { resourceId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['resourceId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Deletes multiple resources.\n *\n *\n * Refer to *Delete Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/delete-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/delete-resource))\n * for more details.\n * @param ids - IDs of the resources to delete.\n * @public\n * @requiredField ids\n * @permissionId BOOKINGS.RESOURCE_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.ResourcesService.BulkDeleteResources\n */\nexport async function bulkDeleteResources(\n  ids: string[]\n): Promise<\n  NonNullablePaths<\n    BulkDeleteResourcesResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ ids: ids });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.bulkDeleteResources(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { ids: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['ids']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a list of resources matching the provided search criteria.\n *\n *\n * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.\n * @param search - Search criteria including filter, sort, aggregations, and paging options.\n *\n * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.\n * @public\n * @requiredField search\n * @permissionId BOOKINGS.RESOURCE_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.ResourcesService.SearchResources\n */\nexport async function searchResources(\n  search: ResourceSearch\n): Promise<\n  NonNullablePaths<\n    SearchResourcesResponse,\n    | `resources`\n    | `aggregationData.results`\n    | `aggregationData.results.${number}.scalar.type`\n    | `aggregationData.results.${number}.scalar.value`\n    | `aggregationData.results.${number}.name`\n    | `aggregationData.results.${number}.type`\n    | `aggregationData.results.${number}.fieldPath`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ search: search });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.searchResources(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { search: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['search']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ResourceSearchSpec extends SearchSpec {\n  aggregatable: [\n    '_createdDate',\n    '_id',\n    '_updatedDate',\n    'appId',\n    'managementType',\n    'singleResource.locationOptions.availableInAllLocations',\n    'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations',\n    'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations',\n    'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations',\n    'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId',\n    'type'\n  ];\n  paging: 'cursor';\n  wql: [\n    {\n      operators: ['$hasAll', '$hasSome'];\n      fields: [\n        'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      ];\n      sort: 'BOTH';\n    },\n    {\n      operators: '*';\n      fields: [\n        '_createdDate',\n        '_id',\n        '_updatedDate',\n        'appId',\n        'managementType',\n        'name',\n        'singleResource.locationOptions.availableInAllLocations',\n        'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations',\n        'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations',\n        'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations',\n        'type'\n      ];\n      sort: 'BOTH';\n    }\n  ];\n}\n\nexport type CommonSearchWithEntityContext = SearchSdkType<\n  Resource,\n  ResourceSearchSpec\n>;\nexport type ResourceSearch = {\n  /** \n  Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,\n  `filter`, `sort`, or `search` can't be specified.  \n  */\n  cursorPaging?: {\n    /** \n  Maximum number of items to return in the results. \n  @max: 100 \n  */\n    limit?:\n      | NonNullable<CommonSearchWithEntityContext['cursorPaging']>['limit']\n      | null;\n    /** \n  Pointer to the next or previous page in the list of results.\n\n  Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.\n  Not relevant for the first request. \n  @maxLength: 16000 \n  */\n    cursor?:\n      | NonNullable<CommonSearchWithEntityContext['cursorPaging']>['cursor']\n      | null;\n  };\n  /** \n  Filter object for narrowing search results. For example, to return only resources available at specific business locations: `\"filter\": {\"single_resource.location_options.specific_location_options.business_locations.location_id\": {\"$in\": [\"location-id-1\", \"location-id-2\"]}}`.\n\n  Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).  \n  */\n  filter?: CommonSearchWithEntityContext['filter'] | null;\n  /** \n  Array of sort objects specifying result order. For example, to sort by resource name in ascending order: `\"sort\": [{\"fieldName\": \"name\", \"order\": \"ASC\"}]`.\n\n  Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)). \n  @maxSize: 10 \n  */\n  sort?: {\n    /** \n  Name of the field to sort by. \n  @maxLength: 512 \n  */\n    fieldName?: NonNullable<\n      CommonSearchWithEntityContext['sort']\n    >[number]['fieldName'];\n    /** \n  Sort order.  \n  */\n    order?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['order'];\n  }[];\n  /** \n  Aggregations for grouping data into categories (facets) and providing summaries for each category.\n  For example, use aggregations to categorize search results by resource type, management type, or location availability. \n  @maxSize: 10 \n  */\n  aggregations?: {\n    /** \n  Value aggregation configuration.  \n  */\n    value?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['value'];\n    /** \n  Range aggregation configuration.  \n  */\n    range?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['range'];\n    /** \n  Scalar aggregation configuration.  \n  */\n    scalar?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['scalar'];\n    /** \n  Date histogram aggregation configuration.  \n  */\n    dateHistogram?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['dateHistogram'];\n    /** \n  Nested aggregation configuration.  \n  */\n    nested?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['nested'];\n    /** \n  Aggregation name, returned in `aggregations.results.name`. \n  @maxLength: 100 \n  */\n    name?:\n      | NonNullable<\n          CommonSearchWithEntityContext['aggregations']\n        >[number]['name']\n      | null;\n    /** \n  Type of aggregation. Client must specify matching aggregation field below.  \n  */\n    type?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['type'];\n    /** \n  Field to aggregate by. Use dot notation to specify a JSON path. For example `name` or `locationOptions.businessLocations.locationId`. \n  @maxLength: 200 \n  */\n    fieldPath?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['fieldPath'];\n    /** \n  Deprecated. Use `nested` instead. \n  @deprecated: Deprecated. Use `nested` instead.,\n  @replacedBy: kind.nested,\n  @targetRemovalDate: 2024-03-30 \n  */\n    groupBy?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['groupBy'];\n  }[];\n  /** \n  Free text to match in searchable fields.  \n  */\n  search?: {\n    /** \n  Search mode. Defines the search logic for combining multiple terms in the `expression`.  \n  */\n    mode?: NonNullable<CommonSearchWithEntityContext['search']>['mode'];\n    /** \n  Search term or expression. \n  @maxLength: 100 \n  */\n    expression?:\n      | NonNullable<CommonSearchWithEntityContext['search']>['expression']\n      | null;\n    /** \n  Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `locationOptions.availableInAllLocations`. \n  @maxLength: 200,\n  @maxSize: 20 \n  */\n    fields?: NonNullable<CommonSearchWithEntityContext['search']>['fields'];\n    /** \n  Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions.  \n  */\n    fuzzy?: NonNullable<CommonSearchWithEntityContext['search']>['fuzzy'];\n  };\n  /** \n  Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.\n\n  Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties). \n  @maxLength: 50 \n  */\n  timeZone?: CommonSearchWithEntityContext['timeZone'] | null;\n};\n\n/**\n * Creates a query to retrieve a list of resources.\n *\n * The `queryResources()` function builds a query to retrieve a list of resources and returns a `ResourcesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-builder/find) function.\n *\n * You can refine the query by chaining `ResourcesQueryBuilder` functions onto the query. `ResourcesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResources()` returns.\n *\n * `queryResources()` runs with the following `ResourcesQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryResources()` are applied in the order they are called. For example, if you apply `ascending(\"typeId\")` and then `ascending(\"name\")`, the results are sorted first by the `\"typeId\"`, and then, if there are multiple results with the same `\"typeId\"`, the items are sorted by `\"name\"`.\n *\n * The following `ResourcesQueryBuilder` functions are supported for the `queryResources()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-result/items) property in `ResourcesQueryResult`.\n * @public\n * @permissionId BOOKINGS.RESOURCE_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.ResourcesService.QueryResources\n */\nexport function queryResources(): ResourcesQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    Resource,\n    'CURSOR',\n    QueryResourcesRequest,\n    QueryResourcesResponse\n  >({\n    func: async (payload: QueryResourcesRequest) => {\n      const reqOpts =\n        ambassadorWixBookingsResourcesV2Resource.queryResources(payload);\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (query: QueryResourcesRequest['query']) => {\n      const args = [query, {}] as [QueryResourcesRequest['query'], {}];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({ data }: HttpResponse<QueryResourcesResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [])\n      );\n\n      return {\n        items: transformedData?.resources,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'CURSOR',\n    transformationPaths: {},\n  });\n}\n\ninterface QueryCursorResult {\n  cursors: Cursors;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface ResourcesQueryResult extends QueryCursorResult {\n  items: Resource[];\n  query: ResourcesQueryBuilder;\n  next: () => Promise<ResourcesQueryResult>;\n  prev: () => Promise<ResourcesQueryResult>;\n}\n\nexport interface ResourcesQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  eq: (\n    propertyName:\n      | 'singleResource.locationOptions.availableInAllLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'locationOptions.availableInAllLocations'\n      | 'eventsSchedule.scheduleId',\n    value: any\n  ) => ResourcesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ne: (\n    propertyName:\n      | 'singleResource.locationOptions.availableInAllLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'locationOptions.availableInAllLocations'\n      | 'eventsSchedule.scheduleId',\n    value: any\n  ) => ResourcesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ge: (\n    propertyName:\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'eventsSchedule.scheduleId',\n    value: any\n  ) => ResourcesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  gt: (\n    propertyName:\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'eventsSchedule.scheduleId',\n    value: any\n  ) => ResourcesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  le: (\n    propertyName:\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'eventsSchedule.scheduleId',\n    value: any\n  ) => ResourcesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  lt: (\n    propertyName:\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'eventsSchedule.scheduleId',\n    value: any\n  ) => ResourcesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `string`.\n   * @param string - String to compare against. Case-insensitive.\n   */\n  startsWith: (\n    propertyName:\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | 'name'\n      | 'typeId'\n      | 'eventsSchedule.scheduleId',\n    value: string\n  ) => ResourcesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   */\n  hasSome: (\n    propertyName:\n      | 'singleResource.locationOptions.availableInAllLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'locationOptions.availableInAllLocations'\n      | 'eventsSchedule.scheduleId',\n    value: any[]\n  ) => ResourcesQueryBuilder;\n  in: (\n    propertyName:\n      | 'singleResource.locationOptions.availableInAllLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'locationOptions.availableInAllLocations'\n      | 'eventsSchedule.scheduleId',\n    value: any\n  ) => ResourcesQueryBuilder;\n  exists: (\n    propertyName:\n      | 'singleResource.locationOptions.availableInAllLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'name'\n      | 'typeId'\n      | 'locationOptions.availableInAllLocations'\n      | 'eventsSchedule.scheduleId',\n    value: boolean\n  ) => ResourcesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  ascending: (\n    ...propertyNames: Array<\n      | 'singleResource.locationOptions.availableInAllLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'type'\n      | 'typeId'\n      | 'locationOptions.availableInAllLocations'\n      | 'eventsSchedule.scheduleId'\n      | 'appId'\n      | 'managementType'\n    >\n  ) => ResourcesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  descending: (\n    ...propertyNames: Array<\n      | 'singleResource.locationOptions.availableInAllLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations'\n      | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'\n      | '_id'\n      | '_createdDate'\n      | '_updatedDate'\n      | 'type'\n      | 'typeId'\n      | 'locationOptions.availableInAllLocations'\n      | 'eventsSchedule.scheduleId'\n      | 'appId'\n      | 'managementType'\n    >\n  ) => ResourcesQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */\n  limit: (limit: number) => ResourcesQueryBuilder;\n  /** @param cursor - A pointer to specific record */\n  skipTo: (cursor: string) => ResourcesQueryBuilder;\n  find: () => Promise<ResourcesQueryResult>;\n}\n\n/**\n * Counts resources according to given criteria.\n *\n *\n * Refer to the *supported filters article*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n * @public\n * @param options - Filter to base the count on. See *the supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for a complete list of filtering options.\n * @permissionId BOOKINGS.RESOURCE_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.ResourcesService.CountResources\n */\nexport async function countResources(\n  options?: CountResourcesOptions\n): Promise<NonNullablePaths<CountResourcesResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n    search: options?.search,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2Resource.countResources(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          filter: '$[0].filter',\n          search: '$[0].search',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountResourcesOptions {\n  /**\n   * Filter to base the count on. Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.\n   * @deprecated Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.\n   * @targetRemovalDate 2024-05-01\n   */\n  search?: SearchDetails;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFloatToRESTFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsResourcesV2ResourcesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/bookings/v2/resources',\n        destPath: '/v2/resources',\n      },\n      {\n        srcPath: '/_api/bookings/v2/resources',\n        destPath: '/v2/resources',\n      },\n      {\n        srcPath: '/v2/bulk/resources',\n        destPath: '/v2/bulk/resources',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/resources-2',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/resources',\n        destPath: '/v2/resources',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/resources',\n        destPath: '/v2/bulk/resources',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/resources-2',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_resources';\n\n/**\n * Creates a new resource.\n *\n *\n * ## Connected schedules\n *\n * A new event *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration))\n * is automatically created for the resource.\n *\n * ## Locations\n *\n * If you don't specify `locationOptions`, Wix Bookings automatically sets\n * `locationOptions.availableInAllLocations` to `true`.\n *\n * If you specify 1 business location ID in `locationOptions.specificLocationOptions.businessLocations`,\n * you must specify `locationOptions.specificLocationOptions.availableInBusinessLocations`\n * as `true`. Currently, you can specify only a single business location.\n */\nexport function createResource(payload: object): RequestOptionsFactory<any> {\n  function __createResource({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resource.createdDate' },\n          { path: 'resource.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.CreateResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resource.createdDate' },\n              { path: 'resource.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createResource;\n}\n\n/**\n * Creates up to 50 resources.\n *\n *\n * Refer to *Create Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/create-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/create-resource))\n * for more details.\n */\nexport function bulkCreateResources(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkCreateResources({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resources.createdDate' },\n          { path: 'resources.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.ResourcesService.BulkCreateResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/bulk/resources/create',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkCreateResources;\n}\n\n/** Retrieves a resource. */\nexport function getResource(payload: object): RequestOptionsFactory<any> {\n  function __getResource({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.GetResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/{resourceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resource.createdDate' },\n              { path: 'resource.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getResource;\n}\n\n/**\n * Updates a resource.\n *\n *\n * Each time the resource is updated, `revision` increments by 1. You must include the current revision of the resource when updating it.\n * This ensures you're working with the latest service information and prevents unintended overwrites.\n */\nexport function updateResource(payload: object): RequestOptionsFactory<any> {\n  function __updateResource({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resource.createdDate' },\n          { path: 'resource.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.UpdateResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/{resource.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resource.createdDate' },\n              { path: 'resource.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateResource;\n}\n\n/**\n * Updates multiple resources.\n *\n *\n * Refer to *Update Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/update-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/update-resource))\n * for more details.\n */\nexport function bulkUpdateResources(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateResources({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'resources.fieldMask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resources.resource.createdDate' },\n          { path: 'resources.resource.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.ResourcesService.BulkUpdateResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/bulk/resources/update',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateResources;\n}\n\n/**\n * Deletes a resource.\n *\n *\n * Deleting a resource cancels its event schedule and all its working hour\n * schedules that aren't shared with another resource. Learn more about\n * _how Bookings uses the Calendar APIs_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).\n */\nexport function deleteResource(payload: object): RequestOptionsFactory<any> {\n  function __deleteResource({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.DeleteResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/{resourceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteResource;\n}\n\n/**\n * Deletes multiple resources.\n *\n *\n * Refer to *Delete Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/delete-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/delete-resource))\n * for more details.\n */\nexport function bulkDeleteResources(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkDeleteResources({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.ResourcesService.BulkDeleteResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/bulk/resources/delete',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkDeleteResources;\n}\n\n/**\n * Retrieves a list of resources matching the provided search criteria.\n *\n *\n * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.\n */\nexport function searchResources(payload: object): RequestOptionsFactory<any> {\n  function __searchResources({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'search.aggregations.range.buckets.from' },\n          { path: 'search.aggregations.range.buckets.to' },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.from',\n          },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.to',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.SearchResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/search',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resources.createdDate' },\n              { path: 'resources.updatedDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'aggregationData.results.ranges.results.from' },\n              { path: 'aggregationData.results.ranges.results.to' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from',\n              },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.from',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.to',\n              },\n              { path: 'aggregationData.results.scalar.value' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.scalar.value',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.scalar.value',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __searchResources;\n}\n\n/**\n * Creates a query to retrieve a list of resources.\n *\n * The `queryResources()` function builds a query to retrieve a list of resources and returns a `ResourcesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-builder/find) function.\n *\n * You can refine the query by chaining `ResourcesQueryBuilder` functions onto the query. `ResourcesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResources()` returns.\n *\n * `queryResources()` runs with the following `ResourcesQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryResources()` are applied in the order they are called. For example, if you apply `ascending(\"typeId\")` and then `ascending(\"name\")`, the results are sorted first by the `\"typeId\"`, and then, if there are multiple results with the same `\"typeId\"`, the items are sorted by `\"name\"`.\n *\n * The following `ResourcesQueryBuilder` functions are supported for the `queryResources()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-result/items) property in `ResourcesQueryResult`.\n */\nexport function queryResources(payload: object): RequestOptionsFactory<any> {\n  function __queryResources({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.QueryResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resources.createdDate' },\n              { path: 'resources.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryResources;\n}\n\n/**\n * Counts resources according to given criteria.\n *\n *\n * Refer to the *supported filters article*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n */\nexport function countResources(payload: object): RequestOptionsFactory<any> {\n  function __countResources({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.CountResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countResources;\n}\n", "import {\n  createResource as publicCreateResource,\n  bulkCreateResources as publicBulkCreateResources,\n  getResource as publicGetResource,\n  updateResource as publicUpdateResource,\n  bulkUpdateResources as publicBulkUpdateResources,\n  deleteResource as publicDeleteResource,\n  bulkDeleteResources as publicBulkDeleteResources,\n  searchResources as publicSearchResources,\n  queryResources as publicQueryResources,\n  countResources as publicCountResources,\n} from './bookings-resources-v2-resource-resources.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { createEventModule } from '@wix/sdk-runtime/event-definition-modules';\nimport {\n  BuildRESTFunction,\n  MaybeContext,\n  BuildEventDefinition,\n} from '@wix/sdk-types';\nimport { onResourceCreated as publicOnResourceCreated } from './bookings-resources-v2-resource-resources.public.js';\nimport { onResourceDeleted as publicOnResourceDeleted } from './bookings-resources-v2-resource-resources.public.js';\nimport { onResourceUpdated as publicOnResourceUpdated } from './bookings-resources-v2-resource-resources.public.js';\n\nexport const createResource: MaybeContext<\n  BuildRESTFunction<typeof publicCreateResource> & typeof publicCreateResource\n> = /*#__PURE__*/ createRESTModule(publicCreateResource);\nexport const bulkCreateResources: MaybeContext<\n  BuildRESTFunction<typeof publicBulkCreateResources> &\n    typeof publicBulkCreateResources\n> = /*#__PURE__*/ createRESTModule(publicBulkCreateResources);\nexport const getResource: MaybeContext<\n  BuildRESTFunction<typeof publicGetResource> & typeof publicGetResource\n> = /*#__PURE__*/ createRESTModule(publicGetResource);\nexport const updateResource: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateResource> & typeof publicUpdateResource\n> = /*#__PURE__*/ createRESTModule(publicUpdateResource);\nexport const bulkUpdateResources: MaybeContext<\n  BuildRESTFunction<typeof publicBulkUpdateResources> &\n    typeof publicBulkUpdateResources\n> = /*#__PURE__*/ createRESTModule(publicBulkUpdateResources);\nexport const deleteResource: MaybeContext<\n  BuildRESTFunction<typeof publicDeleteResource> & typeof publicDeleteResource\n> = /*#__PURE__*/ createRESTModule(publicDeleteResource);\nexport const bulkDeleteResources: MaybeContext<\n  BuildRESTFunction<typeof publicBulkDeleteResources> &\n    typeof publicBulkDeleteResources\n> = /*#__PURE__*/ createRESTModule(publicBulkDeleteResources);\nexport const searchResources: MaybeContext<\n  BuildRESTFunction<typeof publicSearchResources> & typeof publicSearchResources\n> = /*#__PURE__*/ createRESTModule(publicSearchResources);\nexport const queryResources: MaybeContext<\n  BuildRESTFunction<typeof publicQueryResources> & typeof publicQueryResources\n> = /*#__PURE__*/ createRESTModule(publicQueryResources);\nexport const countResources: MaybeContext<\n  BuildRESTFunction<typeof publicCountResources> & typeof publicCountResources\n> = /*#__PURE__*/ createRESTModule(publicCountResources);\n/**\n * Triggered when a resource is created.\n */\nexport const onResourceCreated: BuildEventDefinition<\n  typeof publicOnResourceCreated\n> = createEventModule(publicOnResourceCreated);\n/**\n * Triggered when a resource is deleted.\n */\nexport const onResourceDeleted: BuildEventDefinition<\n  typeof publicOnResourceDeleted\n> = createEventModule(publicOnResourceDeleted);\n/**\n * Triggered when an resource is updated.\n */\nexport const onResourceUpdated: BuildEventDefinition<\n  typeof publicOnResourceUpdated\n> = createEventModule(publicOnResourceUpdated);\n\nexport {\n  ManagementType,\n  SortOrder,\n  SortType,\n  SortDirection,\n  MissingValues,\n  ScalarType,\n  NestedAggregationType,\n  Interval,\n  AggregationType,\n  Mode,\n  WebhookIdentityType,\n} from './bookings-resources-v2-resource-resources.universal.js';\nexport {\n  Resource,\n  ResourceCompositionDetailsOneOf,\n  WorkingHoursSchedule,\n  SingleResource,\n  V2WorkingHoursSchedules,\n  Schedule,\n  LocationOptions,\n  SpecificLocation,\n  BusinessLocation,\n  WorkingHoursSchedules,\n  EventsSchedule,\n  ExtendedFields,\n  CreateResourceRequest,\n  CreateResourceResponse,\n  BulkCreateResourcesRequest,\n  BulkCreateResourcesResponse,\n  BulkResourceResult,\n  ItemMetadata,\n  ApplicationError,\n  BulkActionMetadata,\n  GetResourceRequest,\n  GetResourceResponse,\n  GetDeletedResourceRequest,\n  GetDeletedResourceResponse,\n  ListDeletedResourcesRequest,\n  CursorPaging,\n  ListDeletedResourcesResponse,\n  CursorPagingMetadata,\n  Cursors,\n  RemoveResourceFromTrashBinRequest,\n  RemoveResourceFromTrashBinResponse,\n  RestoreResourceFromTrashBinRequest,\n  RestoreResourceFromTrashBinResponse,\n  UpdateResourceRequest,\n  UpdateResourceResponse,\n  BulkUpdateResourcesRequest,\n  MaskedResource,\n  BulkUpdateResourcesResponse,\n  DeleteResourceRequest,\n  DeleteResourceResponse,\n  BulkDeleteResourcesRequest,\n  BulkDeleteResourcesResponse,\n  SearchResourcesRequest,\n  CursorSearch,\n  CursorSearchPagingMethodOneOf,\n  Sorting,\n  Aggregation,\n  AggregationKindOneOf,\n  RangeBucket,\n  IncludeMissingValuesOptions,\n  ValueAggregation,\n  ValueAggregationOptionsOneOf,\n  RangeAggregation,\n  ScalarAggregation,\n  DateHistogramAggregation,\n  NestedAggregationItem,\n  NestedAggregationItemKindOneOf,\n  NestedAggregation,\n  GroupByAggregation,\n  GroupByAggregationKindOneOf,\n  SearchDetails,\n  SearchResourcesResponse,\n  AggregationData,\n  ValueAggregationResult,\n  RangeAggregationResult,\n  NestedAggregationResults,\n  NestedAggregationResultsResultOneOf,\n  ValueResults,\n  RangeResults,\n  AggregationResultsScalarResult,\n  NestedValueAggregationResult,\n  ValueResult,\n  RangeResult,\n  ScalarResult,\n  NestedResultValue,\n  NestedResultValueResultOneOf,\n  Results,\n  DateHistogramResult,\n  GroupByValueResults,\n  DateHistogramResults,\n  NestedResults,\n  AggregationResults,\n  AggregationResultsResultOneOf,\n  QueryResourcesRequest,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  QueryResourcesResponse,\n  CountResourcesRequest,\n  CountResourcesResponse,\n  FixResourceSchedulesRequest,\n  FixResourceSchedulesResponse,\n  ReindexTenantRequest,\n  ReindexTenantResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  RestoreInfo,\n  EntityUpdatedEvent,\n  EntityDeletedEvent,\n  ActionEvent,\n  Empty,\n  MessageEnvelope,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  BaseEventMetadata,\n  EventMetadata,\n  ResourceCreatedEnvelope,\n  ResourceDeletedEnvelope,\n  ResourceUpdatedEnvelope,\n  BulkCreateResourcesOptions,\n  UpdateResource,\n  BulkUpdateResourcesOptions,\n  ResourceSearchSpec,\n  ResourcesQueryResult,\n  ResourcesQueryBuilder,\n  CountResourcesOptions,\n} from './bookings-resources-v2-resource-resources.universal.js';\nexport {\n  ManagementTypeWithLiterals,\n  SortOrderWithLiterals,\n  SortTypeWithLiterals,\n  SortDirectionWithLiterals,\n  MissingValuesWithLiterals,\n  ScalarTypeWithLiterals,\n  NestedAggregationTypeWithLiterals,\n  IntervalWithLiterals,\n  AggregationTypeWithLiterals,\n  ModeWithLiterals,\n  WebhookIdentityTypeWithLiterals,\n  CreateResourceValidationErrors,\n  BulkCreateResourcesValidationErrors,\n  UpdateResourceValidationErrors,\n  BulkUpdateResourcesValidationErrors,\n  CommonSearchWithEntityContext,\n  ResourceSearch,\n} from './bookings-resources-v2-resource-resources.universal.js';\n"], "mappings": ";AAAA,SAAS,2CAAAA,gDAA+C;AACxD,SAAS,wCAAAC,6CAA4C;AACrD,SAAS,kBAAAC,uBAAsB;AAC/B,SAAS,uBAAqD;;;ACH9D,SAAS,kBAAkB,yBAAyB;AACpD,SAAS,oBAAoB;AAC7B;AAAA,EACE;AAAA,EACA;AAAA,OACK;;;ACLP,SAAS,yBAAyB;AAClC,SAAS,oCAAoC;AAC7C,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,iDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAqBd,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,wBAAwB;AAAA,UAChC,EAAE,MAAM,wBAAwB;AAAA,QAClC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,YAAY,SAA6C;AACvE,WAAS,cAAc,EAAE,KAAK,GAAQ;AACpC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,sBAAsB,CAAC;AAAA,MACzC;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,iCAAiC;AAAA,UACzC,EAAE,MAAM,iCAAiC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yCAAyC;AAAA,UACjD,EAAE,MAAM,uCAAuC;AAAA,UAC/C;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wBAAwB;AAAA,YAChC,EAAE,MAAM,wBAAwB;AAAA,UAClC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wBAAwB;AAAA,YAChC,EAAE,MAAM,wBAAwB;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADrgBA,SAAS,kBAAAC,uBAAsB;AA4MxB,IAAK,iBAAL,kBAAKC,oBAAL;AAEL,EAAAA,gBAAA,cAAW;AAFD,SAAAA;AAAA,GAAA;AA0VL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AA6DL,IAAK,WAAL,kBAAKC,cAAL;AAEL,EAAAA,UAAA,WAAQ;AAER,EAAAA,UAAA,WAAQ;AAJE,SAAAA;AAAA,GAAA;AAUL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,UAAO;AAEP,EAAAA,eAAA,SAAM;AAJI,SAAAA;AAAA,GAAA;AAUL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,aAAU;AAEV,EAAAA,eAAA,aAAU;AAJA,SAAAA;AAAA,GAAA;AAuBL,IAAK,aAAL,kBAAKC,gBAAL;AACL,EAAAA,YAAA,yBAAsB;AAEtB,EAAAA,YAAA,oBAAiB;AAEjB,EAAAA,YAAA,SAAM;AAEN,EAAAA,YAAA,SAAM;AAPI,SAAAA;AAAA,GAAA;AA+CL,IAAK,wBAAL,kBAAKC,2BAAL;AACL,EAAAA,uBAAA,8BAA2B;AAE3B,EAAAA,uBAAA,WAAQ;AAER,EAAAA,uBAAA,WAAQ;AAER,EAAAA,uBAAA,YAAS;AAET,EAAAA,uBAAA,oBAAiB;AATP,SAAAA;AAAA,GAAA;AAuCL,IAAK,WAAL,kBAAKC,cAAL;AACL,EAAAA,UAAA,sBAAmB;AAEnB,EAAAA,UAAA,UAAO;AAEP,EAAAA,UAAA,WAAQ;AAER,EAAAA,UAAA,UAAO;AAEP,EAAAA,UAAA,SAAM;AAEN,EAAAA,UAAA,UAAO;AAEP,EAAAA,UAAA,YAAS;AAET,EAAAA,UAAA,YAAS;AAfC,SAAAA;AAAA,GAAA;AAiEL,IAAK,kBAAL,kBAAKC,qBAAL;AACL,EAAAA,iBAAA,8BAA2B;AAE3B,EAAAA,iBAAA,WAAQ;AAER,EAAAA,iBAAA,WAAQ;AAER,EAAAA,iBAAA,YAAS;AAET,EAAAA,iBAAA,oBAAiB;AAEjB,EAAAA,iBAAA,YAAS;AAXC,SAAAA;AAAA,GAAA;AAyEL,IAAK,OAAL,kBAAKC,UAAL;AAEL,EAAAA,MAAA,QAAK;AAEL,EAAAA,MAAA,SAAM;AAJI,SAAAA;AAAA,GAAA;AA8dL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAsPZ,eAAsBC,gBACpB,UAUA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC,EAAE,SAAmB,CAAC;AAE5E,QAAM,UACqC,eAAe,OAAO;AAEjE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,UAAU,OAAO;AAAA,QAC7C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU;AAAA,IACb;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAiBA,eAAsBC,qBACpB,WACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,cAAc,SAAS;AAAA,EACzB,CAAC;AAED,QAAM,UACqC,oBAAoB,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAqBA,eAAsBC,aACpB,YAQA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAAmD,YAAY,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,YAAY,OAAO;AAAA,QAC/C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY;AAAA,IACf;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkBA,eAAsBC,gBACpB,KACA,UAUA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,UAAU,EAAE,GAAG,UAAU,IAAI,IAAI;AAAA,EACnC,CAAC;AAED,QAAM,UACqC,eAAe,OAAO;AAEjE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,UAAU,OAAO;AAAA,QAC3C,0BAA0B,EAAE,eAAe,OAAO;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,UAAU;AAAA,IACpB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmFA,eAAsBC,qBACpB,WAKA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,cAAc,SAAS;AAAA,EACzB,CAAC;AAED,QAAM,UACqC,oBAAoB,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA0BA,eAAsBC,gBAAe,YAAmC;AAEtE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACqC,eAAe,OAAO;AAEjE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAAA,EACjC,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,YAAY,OAAO;AAAA,QAC/C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY;AAAA,IACf;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAgBA,eAAsBC,qBACpB,KAcA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC,EAAE,IAAS,CAAC;AAElE,QAAM,UACqC,oBAAoB,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,KAAK,OAAO;AAAA,QACxC,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,KAAK;AAAA,IACR;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAgBA,eAAsBC,iBACpB,QAaA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC,EAAE,OAAe,CAAC;AAExE,QAAM,UACqC,gBAAgB,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,OAAO;AAAA,QAC3C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,QAAQ;AAAA,IACX;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA+NO,SAASC,kBAAwC;AAEtD,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,SAAO,aAKL;AAAA,IACA,MAAM,OAAO,YAAmC;AAC9C,YAAM,UACqC,eAAe,OAAO;AAEjE,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,UAA0C;AAC7D,YAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AACvB,aAAO,sCAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC,EAAE,KAAK,MAA4C;AACvE,YAAM,kBAAkB;AAAA,QACtBpB,gBAAe,MAAM,CAAC,CAAC;AAAA,MACzB;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,mBAAmB,kBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;AA+NA,eAAsBqB,gBACpB,SAC+D;AAE/D,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,IACjB,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACqC,eAAe,OAAO;AAEjE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;ADjoFO,SAASC,gBACd,YACyB;AACzB,SAAO,CAAC,aACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAoCO,SAASC,qBACd,YAC8B;AAC9B,SAAO,CACL,WACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiCO,SAASC,aAAY,YAA8C;AACxE,SAAO,CAAC,eACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAkBO,SAASC,gBACd,YACyB;AACzB,SAAO,CACL,KACA,aAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA2BO,SAASC,qBACd,YAC8B;AAC9B,SAAO,CACL,WAKA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAqCO,SAASC,gBACd,YACyB;AACzB,SAAO,CAAC,eACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAgBO,SAASC,qBACd,YAC8B;AAC9B,SAAO,CAAC,QACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA4BO,SAASC,iBACd,YAC0B;AAC1B,SAAO,CAAC,WACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA2BO,SAASC,gBACd,YACyB;AACzB,SAAO,MACLA;AAAA;AAAA,IAEE,EAAE,WAAW;AAAA,EACf;AACJ;AAwBO,SAASC,gBACd,YACyB;AACzB,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiBO,IAAM,oBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,UACCC;AAAA,IACEC,gBAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAaC;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;AACpB,IAAM,oBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,UACCF;AAAA,IACEC,gBAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAaC;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,wBAAwB;AAAA,UAChC,EAAE,MAAM,wBAAwB;AAAA,UAChC,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;AACpB,IAAM,oBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,UACCF;AAAA,IACEC,gBAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAaC;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;;;AG3b3B,SAAS,wBAAwB;AACjC,SAAS,yBAAyB;AAU3B,IAAMC,kBAEK,iCAAiBA,eAAoB;AAChD,IAAMC,uBAGK,iCAAiBA,oBAAyB;AACrD,IAAMC,eAEK,iCAAiBA,YAAiB;AAC7C,IAAMC,kBAEK,iCAAiBA,eAAoB;AAChD,IAAMC,uBAGK,iCAAiBA,oBAAyB;AACrD,IAAMC,kBAEK,iCAAiBA,eAAoB;AAChD,IAAMC,uBAGK,iCAAiBA,oBAAyB;AACrD,IAAMC,mBAEK,iCAAiBA,gBAAqB;AACjD,IAAMC,kBAEK,iCAAiBA,eAAoB;AAChD,IAAMC,kBAEK,iCAAiBA,eAAoB;AAIhD,IAAMC,qBAET,kBAAkB,iBAAuB;AAItC,IAAMC,qBAET,kBAAkB,iBAAuB;AAItC,IAAMC,qBAET,kBAAkB,iBAAuB;", "names": ["renameKeysFromRESTResponseToSDKResponse", "transformRESTTimestampToSDKTimestamp", "transformPaths", "payload", "transformPaths", "ManagementType", "SortOrder", "SortType", "SortDirection", "<PERSON><PERSON><PERSON><PERSON>", "ScalarType", "NestedAggregationType", "Interval", "AggregationType", "Mode", "WebhookIdentityType", "createResource", "bulkCreateResources", "getResource", "updateResource", "bulkUpdateResources", "deleteResource", "bulkDeleteResources", "searchResources", "queryResources", "countResources", "createResource", "bulkCreateResources", "getResource", "updateResource", "bulkUpdateResources", "deleteResource", "bulkDeleteResources", "searchResources", "queryResources", "countResources", "renameKeysFromRESTResponseToSDKResponse", "transformPaths", "transformRESTTimestampToSDKTimestamp", "createResource", "bulkCreateResources", "getResource", "updateResource", "bulkUpdateResources", "deleteResource", "bulkDeleteResources", "searchResources", "queryResources", "countResources", "onResourceCreated", "onResourceDeleted", "onResourceUpdated"]}