import { QueryAvailabilityRequest as QueryAvailabilityRequest$1, QueryAvailabilityResponse as QueryAvailabilityResponse$1, GetScheduleAvailabilityRequest as GetScheduleAvailabilityRequest$1, GetScheduleAvailabilityResponse as GetScheduleAvailabilityResponse$1, CalculateMultiSlotAvailabilityRequest as CalculateMultiSlotAvailabilityRequest$1, CalculateMultiSlotAvailabilityResponse as CalculateMultiSlotAvailabilityResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

interface SlotAvailability {
    /**
     * The slot for the corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     */
    slot?: Slot;
    /**
     * Whether the slot is bookable. Bookability is determined by checking a
     * session's open slots and booking policies. Locks are not taken into
     * account.
     */
    bookable?: boolean;
    /**
     * Total number of spots for this slot.
     * For example, if a session has a total of 10 spots and 3 spots are booked,
     * `spotsTotal` is 10 and `openSpots` is 7.
     */
    totalSpots?: number | null;
    /** Number of open spots for this slot. */
    openSpots?: number | null;
    /** An object describing the slot's waitlist and its occupancy. */
    waitingList?: AvailabilityWaitingList;
    /** Booking policy violations for the slot. */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * Indicates whether the slot is locked because a waitlist exists.
     * When a slot frees up, the slot is offered to the next customer on the waitlist. Read-only.
     */
    locked?: boolean | null;
    /**
     * Deprecated. not in use since 2025-06-15.
     * @deprecated Deprecated. not in use since 2025-06-15.
     * @targetRemovalDate 2025-06-15
     */
    isFromV2?: boolean;
}
interface Slot {
    /**
     * ID for the slot's corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     *
     * Deprecated. Please use `eventId` instead.
     * @deprecated ID for the slot's corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     *
     * Deprecated. Please use `eventId` instead.
     * @replacedBy event_id
     * @targetRemovalDate 2025-09-30
     */
    sessionId?: string | null;
    /** Service ID. */
    serviceId?: string;
    /** Schedule ID. */
    scheduleId?: string;
    /**
     * The start time of this slot in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339)
     * format.
     *
     * If `timezone` is specified,
     * dates are based on the local date/time. This means that the timezone offset
     * in the `start_date` is ignored.
     */
    startDate?: string | null;
    /**
     * The end time of this slot in
     * [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339) format.
     *
     * If `timezone` is specified,
     * dates are based on the local date/time. This means that the timezone offset
     * in the `end_date` is ignored.
     */
    endDate?: string | null;
    /**
     * The timezone for which slot availability is to be calculated.
     *
     * Learn more about [handling Daylight Savings Time (DST) for local time zones](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_handling-daylight-savings-time-dst-for-local-time-zones)
     * when calculating availability.
     */
    timezone?: string | null;
    /**
     * The resource required for this slot. Currently, the only supported resource
     * is the relevant staff member for the slot.
     */
    resource?: SlotResource;
    /** Geographic location of the slot. */
    location?: Location;
    /**
     * ID for the slot's corresponding event, when the event is either a single event
     * or a specific event generated from a recurring event.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
}
interface SlotResource {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Resource name. Read only.
     * @maxLength 1200
     */
    name?: string | null;
}
interface Location {
    /**
     * Business location ID. Available only for locations that are business locations,
     * meaning the `location_type` is `"OWNER_BUSINESS"`.
     * @format GUID
     */
    id?: string | null;
    /** Location name. */
    name?: string | null;
    /** The full address of this location. */
    formattedAddress?: string | null;
    /**
     * The full translated address of this location.
     * @maxLength 512
     */
    formattedAddressTranslated?: string | null;
    /**
     * Location type.
     *
     * - `"OWNER_BUSINESS"`: The business address, as set in the site’s general settings.
     * - `"OWNER_CUSTOM"`: The address as set when creating the service.
     * - `"CUSTOM"`: The address as set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface AvailabilityWaitingList {
    /**
     * Total number of spots and open spots for this waitlist.
     * For example, a Yoga class with 10 waitlist spots and 3 registered
     * on the waitlist has 10 `total_spots` and 7 `open_spots`.
     */
    totalSpots?: number | null;
    /** Number of open spots for this waitlist. */
    openSpots?: number | null;
}
interface BookingPolicyViolations {
    /** Bookings policy violation. Too early to book this slot. */
    tooEarlyToBook?: boolean | null;
    /** Bookings policy violation. Too late to book this slot. */
    tooLateToBook?: boolean | null;
    /** Bookings policy violation. Online booking is disabled for this slot. */
    bookOnlineDisabled?: boolean | null;
}
interface QueryAvailabilityRequest {
    /**
     * Query options. Refer to the
     * _supported filters article_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))
     * for a complete list of supported filters.
     */
    query: QueryV2;
    /**
     * Time zone override for the `endDate` and `startDate` filters, in
     * [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database), such as
     * `America/New_York` or `UTC`.
     *
     * For example, if you specify `("startDate": "2025-11-25T17:00:00+01:00"}` (note
     * the +01:00 offset) in combination with `{"timezone": "Europe/Bucharest"}`
     * (+02:00 offset), *Query Availability* adjusts the effective start time to
     * reflect the specified time zone. In this case, the start time in UTC would
     * be `2025-11-25T15:00:00` (using the +02:00 offset).
     *
     * Learn more about *handling Daylight Savings Time*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst) | [REST](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst)).
     *
     * Default: No modification applied to the `endDate` and `startDate` filters.
     */
    timezone?: string | null;
    /**
     * Maximum number of appointment slots to return for each date. For example, if
     * you specify `{"slotsPerDay": 3}`, a maximum of 3 appointment slots/class events is
     * returned for each day that's within the query filter's date range.
     */
    slotsPerDay?: number | null;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /**
     * Filter object. For a list of
     * fields you can filter by, see Availability Calendar supported filters ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting)).
     * You must include `serviceId`, `startDate` and `endDate` in the filter. This avoids large results that can impact performance.
     */
    filter?: Record<string, any> | null;
    /**
     * Sort options.
     * Currently, only sorting by `startDate` is supported. For details
     * on sorting, see Availability Calendar supported sorting ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting#sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting#sorting)).
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryAvailabilityResponse {
    /** List of appointment slots or class events matching the filter. */
    availabilityEntries?: SlotAvailability[];
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface BookingPolicySettings {
    /**
     * The policy defining the maximum number of participants that can
     * be booked for a slot or a schedule.
     */
    maxParticipantsPerBooking?: number | null;
}
interface GetScheduleAvailabilityRequest {
    /**
     * The schedule ID for which availability is being checked.
     * @format GUID
     */
    scheduleId: string;
}
interface GetScheduleAvailabilityResponse {
    availability?: ScheduleAvailability;
    bookingPolicySettings?: BookingPolicySettings;
}
interface ScheduleAvailability {
    /**
     * The total number of spots defined for the schedule, including
     * both open and non-available spots.
     */
    totalSpots?: number | null;
    /** The number of open spots defined for the schedule. */
    openSpots?: number | null;
    /** Booking policy violations for the schedule. */
    bookingPolicyViolations?: BookingPolicyViolations;
}
interface CalculateMultiSlotAvailabilityRequest {
    /** @maxLength 30 */
    from?: string;
    /** @maxLength 30 */
    to?: string;
    /** @maxLength 30 */
    timeZone?: string;
    /** TODO good definition of what bookable means https://github.com/wix-private/scheduler/pull/18267/files?file-filters%5B%5D=.proto&show-viewed-files=true#r1199809006 */
    bookable?: boolean | null;
    /**
     * each nested field is checked on its own. i.e. if `too_early_to_book` is defined and `too_late_to_book` is not defined
     * we will return slots for which `too_early_to_book` is same as on the request, regardless of `too_late_to_book`.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * support filtering by location type, or by locationId. Other fields like `name` are ignored
     * must be set, and must have locationType. If locationType is `OWNER_BUSINESS`, must have location_id
     */
    location?: Location;
    /**
     * @minSize 1
     * @maxSize 6
     */
    slots?: RuleBasedConstraints[];
    /**
     * Maximum number of slots to load for each date. For example, if `slots_per_day` is set to `3`,
     * at most 3 available slots are returned for each day in the date range specified in the query's
     * `filter`.
     *
     * When a day has both bookable and non-bookable slots, bookable slots are returned first.
     * Non-bookable slots are returned according to the specified filters, after all
     * bookable slots are already included.
     */
    slotsPerDay?: number | null;
    cursorPaging?: CursorPaging;
}
interface RuleBasedConstraints {
    /** @format GUID */
    serviceId?: string;
    resourcesFilter?: ResourcesFilter;
    /** will be passed to availability-2, and to the availability-constraints SPI */
    customerChoices?: AvailabilityV2CustomerChoices;
}
interface ResourcesFilter {
    resourceIds?: string[];
}
/**
 * Selected customer choices.
 * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.
 */
interface AvailabilityV2CustomerChoices {
    /**
     * Selected customer duration in minutes.
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * Default: `15` minutes
     * @min 1
     * @max 44639
     */
    durationInMinutes?: number | null;
    /**
     * Selected add-on IDs.
     *
     * Max: Calculated as the product of the maximum number of add-on groups multiplied by the maximum number of add-ons per group. Currently 21 (3 groups × 7 add-ons per group), but may change in the future.
     * @format GUID
     * @maxSize 21
     */
    addOnIds?: string[] | null;
    /**
     * Selected duration choice ID.
     * @format GUID
     */
    durationChoiceId?: string | null;
}
interface CalculateMultiSlotAvailabilityResponse {
    slots?: SlotAvailability[];
    cursorPagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in current page. */
    count?: number | null;
    /** Cursor strings that point to the next page, previous page, or both. */
    cursors?: Cursors;
    /**
     * Whether there are more pages to retrieve following the current page.
     *
     * + `true`: Another page of results can be retrieved.
     * + `false`: This is the last page.
     */
    hasNext?: boolean | null;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function queryAvailability(): __PublicMethodMetaInfo<'POST', {}, QueryAvailabilityRequest$1, QueryAvailabilityRequest, QueryAvailabilityResponse$1, QueryAvailabilityResponse>;
declare function getScheduleAvailability(): __PublicMethodMetaInfo<'GET', {
    scheduleId: string;
}, GetScheduleAvailabilityRequest$1, GetScheduleAvailabilityRequest, GetScheduleAvailabilityResponse$1, GetScheduleAvailabilityResponse>;
declare function calculateMultiSlotAvailability(): __PublicMethodMetaInfo<'POST', {}, CalculateMultiSlotAvailabilityRequest$1, CalculateMultiSlotAvailabilityRequest, CalculateMultiSlotAvailabilityResponse$1, CalculateMultiSlotAvailabilityResponse>;

export { type __PublicMethodMetaInfo, calculateMultiSlotAvailability, getScheduleAvailability, queryAvailability };
