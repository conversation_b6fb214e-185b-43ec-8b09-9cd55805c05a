import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { ListEventTimeSlotsOptions, ListEventTimeSlotsResponse, GetEventTimeSlotOptions, GetEventTimeSlotResponse } from './index.typings.js';
export { AvailableResources, BookingPolicyViolations, CursorPaging, CursorPagingMetadata, Cursors, CustomerChoices, EventInfo, GetAvailabilityTimeSlotRequest, GetAvailabilityTimeSlotRequestCustomerChoices, GetAvailabilityTimeSlotResponse, GetEventTimeSlotRequest, GetMultiServiceAvailabilityTimeSlotRequest, GetMultiServiceAvailabilityTimeSlotResponse, ListAvailabilityTimeSlotsRequest, ListAvailabilityTimeSlotsResponse, ListEventTimeSlotsRequest, ListMultiServiceAvailabilityTimeSlotsRequest, ListMultiServiceAvailabilityTimeSlotsResponse, Location, LocationType, LocationTypeWithLiterals, NestedTimeSlot, NonBookableReasons, Resource, ResourceType, Service, TimeSlot, V2CustomerChoices, WaitingList } from './index.typings.js';

declare function listEventTimeSlots$1(httpClient: HttpClient): ListEventTimeSlotsSignature;
interface ListEventTimeSlotsSignature {
    /**
     * Retrieves a list of class session time slots that match the provided filters.
     *
     * ## Defaults
     *
     * List Event Time Slots uses the following defaults:
     *
     * - `providerId` set to the Bookings app ID.
     * - `timeZone` set to the business time zone.
     * - `cursorPaging.limit` set to `50`.
     * - Returns both bookable and non-bookable time slots.
     *
     * Time slots are always sorted by their `localStartDate` in ascending order.
     *
     * ## Service type limitations
     *
     * To retrieve appointment availability, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).
     *
     * To retrieve course availability, follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flows#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course)).
     */
    (options?: ListEventTimeSlotsOptions): Promise<NonNullablePaths<ListEventTimeSlotsResponse, `timeSlots` | `timeSlots.${number}.location.locationType`, 5>>;
}
declare function getEventTimeSlot$1(httpClient: HttpClient): GetEventTimeSlotSignature;
interface GetEventTimeSlotSignature {
    /**
     * Retrieves detailed information about a specific class session time slot.
     *
     *
     * Call this method after selecting a suitable slot with List Event Time Slots to obtain its full capacity and booking-policy status.
     *
     * ## Service type limitations
     *
     * To retrieve appointment availability, call Get Availability Time Slot ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability/availability-time-slots/get-availability-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot)).
     *
     * To retrieve course availability, follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flow#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).
     * @param - Event ID.
     */
    (eventId: string, options?: GetEventTimeSlotOptions): Promise<NonNullablePaths<GetEventTimeSlotResponse, `timeSlot.location.locationType` | `timeSlot.availableResources` | `timeSlot.nestedTimeSlots` | `timeSlot.nestedTimeSlots.${number}.serviceId` | `timeSlot.nestedTimeSlots.${number}.localStartDate` | `timeSlot.nestedTimeSlots.${number}.localEndDate`, 5>>;
}

declare const listEventTimeSlots: MaybeContext<BuildRESTFunction<typeof listEventTimeSlots$1> & typeof listEventTimeSlots$1>;
declare const getEventTimeSlot: MaybeContext<BuildRESTFunction<typeof getEventTimeSlot$1> & typeof getEventTimeSlot$1>;

export { GetEventTimeSlotOptions, GetEventTimeSlotResponse, ListEventTimeSlotsOptions, ListEventTimeSlotsResponse, getEventTimeSlot, listEventTimeSlots };
