{"version": 3, "sources": ["../../../index.ts", "../../../src/bookings-resources-v2-resource-type-resource-types.public.ts", "../../../src/bookings-resources-v2-resource-type-resource-types.universal.ts", "../../../src/bookings-resources-v2-resource-type-resource-types.http.ts", "../../../src/bookings-resources-v2-resource-type-resource-types.context.ts"], "sourcesContent": ["export * from './src/bookings-resources-v2-resource-type-resource-types.context.js';\n", "import { renameKeysFromRESTResponseToSDKResponse } from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { EventDefinition, HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  CountResourceTypesOptions,\n  CountResourceTypesResponse,\n  CreateResourceTypeApplicationErrors,\n  ResourceType,\n  ResourceTypeCreatedEnvelope,\n  ResourceTypeDeletedEnvelope,\n  ResourceTypeUpdatedEnvelope,\n  ResourceTypesQueryBuilder,\n  UpdateResourceType,\n  UpdateResourceTypeApplicationErrors,\n  countResourceTypes as universalCountResourceTypes,\n  createResourceType as universalCreateResourceType,\n  deleteResourceType as universalDeleteResourceType,\n  getResourceType as universalGetResourceType,\n  queryResourceTypes as universalQueryResourceTypes,\n  updateResourceType as universalUpdateResourceType,\n} from './bookings-resources-v2-resource-type-resource-types.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function createResourceType(\n  httpClient: HttpClient\n): CreateResourceTypeSignature {\n  return (resourceType: NonNullablePaths<ResourceType, `name`, 2>) =>\n    universalCreateResourceType(\n      resourceType,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CreateResourceTypeSignature {\n  /**\n   * Creates a new resource type.\n   * @param - Resource type to create.\n   * @returns Created resource type.\n   */\n  (resourceType: NonNullablePaths<ResourceType, `name`, 2>): Promise<\n    ResourceType & {\n      __applicationErrorsType?: CreateResourceTypeApplicationErrors;\n    }\n  >;\n}\n\nexport function getResourceType(\n  httpClient: HttpClient\n): GetResourceTypeSignature {\n  return (resourceTypeId: string) =>\n    universalGetResourceType(\n      resourceTypeId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetResourceTypeSignature {\n  /**\n   * Retrieves a resource type.\n   * @param - ID of the resource type to retrieve.\n   * @returns Retrieved resource type.\n   */\n  (resourceTypeId: string): Promise<ResourceType>;\n}\n\nexport function updateResourceType(\n  httpClient: HttpClient\n): UpdateResourceTypeSignature {\n  return (\n    _id: string,\n    resourceType: NonNullablePaths<UpdateResourceType, `revision`, 2>\n  ) =>\n    universalUpdateResourceType(\n      _id,\n      resourceType,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateResourceTypeSignature {\n  /**\n   * Updates a resource type.\n   *\n   *\n   * Each time the resource type is updated, `revision` increments by 1. You must\n   * include current revision of the resource type when updating it. This ensures\n   * you're working with the latest service information and prevents unintended overwrites.\n   * @param - Resource type ID.\n   * @returns Updated resource type.\n   */\n  (\n    _id: string,\n    resourceType: NonNullablePaths<UpdateResourceType, `revision`, 2>\n  ): Promise<\n    ResourceType & {\n      __applicationErrorsType?: UpdateResourceTypeApplicationErrors;\n    }\n  >;\n}\n\nexport function deleteResourceType(\n  httpClient: HttpClient\n): DeleteResourceTypeSignature {\n  return (resourceTypeId: string) =>\n    universalDeleteResourceType(\n      resourceTypeId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DeleteResourceTypeSignature {\n  /**\n   * Deletes a resource type.\n   *\n   *\n   * Deleting a resource type also automatically deletes all resources connected to it.\n   * @param - ID of the resource type to delete.\n   */\n  (resourceTypeId: string): Promise<void>;\n}\n\nexport function queryResourceTypes(\n  httpClient: HttpClient\n): QueryResourceTypesSignature {\n  return () =>\n    universalQueryResourceTypes(\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface QueryResourceTypesSignature {\n  /**\n   * Creates a query to retrieve a list of resource types.\n   *\n   * The `queryResourceTypes()` function builds a query to retrieve a list of resource types and returns a `ResourceTypesQueryBuilder` object.\n   *\n   * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-builder/find) function.\n   *\n   * You can refine the query by chaining `ResourceTypesQueryBuilder` functions onto the query. `ResourceTypesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResourceTypes()` returns.\n   *\n   * `queryResourceTypes()` runs with the following `ResourceTypesQueryBuilder` default that you can override:\n   *\n   * + `limit` is `50`.\n   * + Sorted by `id` in ascending order.\n   *\n   * The functions that are chained to `queryResourceTypes()` are applied in the order they are called.\n   *\n   * The following `ResourceTypesQueryBuilder` functions are supported for the `queryResourceTypes()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-result/items) property in `ResourceTypesQueryResult`.\n   */\n  (): ResourceTypesQueryBuilder;\n}\n\nexport function countResourceTypes(\n  httpClient: HttpClient\n): CountResourceTypesSignature {\n  return (options?: CountResourceTypesOptions) =>\n    universalCountResourceTypes(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CountResourceTypesSignature {\n  /**\n   * Counts resource types, given the provided filtering.\n   *\n   *\n   * Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters.\n   * @param - Filter to base the count on. See\n   * [queryResourceTypes()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/query-resource-types)\n   * for supported filters.\n   */\n  (options?: CountResourceTypesOptions): Promise<\n    NonNullablePaths<CountResourceTypesResponse, `count`, 2>\n  >;\n}\n\nexport const onResourceTypeCreated = EventDefinition(\n  'wix.bookings.resources.v2.resource_type_created',\n  true,\n  (event: ResourceTypeCreatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<ResourceTypeCreatedEnvelope>();\nexport const onResourceTypeDeleted = EventDefinition(\n  'wix.bookings.resources.v2.resource_type_deleted',\n  true,\n  (event: ResourceTypeDeletedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<ResourceTypeDeletedEnvelope>();\nexport const onResourceTypeUpdated = EventDefinition(\n  'wix.bookings.resources.v2.resource_type_updated',\n  true,\n  (event: ResourceTypeUpdatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<ResourceTypeUpdatedEnvelope>();\n\nexport {\n  ActionEvent,\n  BaseEventMetadata,\n  BusinessLocation,\n  CountResourceTypesOptions,\n  CountResourceTypesRequest,\n  CountResourceTypesResponse,\n  CreateResourceTypeErrors,\n  CreateResourceTypeRequest,\n  CreateResourceTypeResponse,\n  CursorPaging,\n  CursorPagingMetadata,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  Cursors,\n  DeleteResourceTypeRequest,\n  DeleteResourceTypeResponse,\n  DistinctLocationIds,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  EntityDeletedEvent,\n  EntityUpdatedEvent,\n  EventMetadata,\n  ExtendedFields,\n  GetResourceTypeRequest,\n  GetResourceTypeResponse,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  LocationOptions,\n  MessageEnvelope,\n  QueryResourceTypesRequest,\n  QueryResourceTypesResponse,\n  RequestedFields,\n  ResourceCounts,\n  ResourceType,\n  ResourceTypeCreatedEnvelope,\n  ResourceTypeDeletedEnvelope,\n  ResourceTypeUpdatedEnvelope,\n  ResourceTypesQueryBuilder,\n  ResourceTypesQueryResult,\n  RestoreInfo,\n  SortOrder,\n  Sorting,\n  SpecificLocation,\n  UpdateResourceType,\n  UpdateResourceTypeRequest,\n  UpdateResourceTypeResponse,\n  WebhookIdentityType,\n} from './bookings-resources-v2-resource-type-resource-types.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, HttpResponse, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsResourcesV2ResourceType from './bookings-resources-v2-resource-type-resource-types.http.js';\n// @ts-ignore\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/**\n * A resource type is a classification of resources. For example room, equipment,\n * or vehicle. Customers can only book *services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n * if at least 1 *resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))\n * for every resource type connected to the service is available during the requested time.\n */\nexport interface ResourceType {\n  /**\n   * Resource type ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the resource type is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating the resource type.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was created.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was last updated.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the resource type. For example, `meeting room`. The name must be\n   * unique per site.\n   * @maxLength 40\n   * @minLength 1\n   */\n  name?: string | null;\n  /** Extensions enabling users to save custom data related to the resource type. */\n  extendedFields?: ExtendedFields;\n}\n\nexport interface ResourceCounts {\n  /**\n   * Total number of resources connected to the type.\n   * @readonly\n   */\n  total?: number | null;\n  /**\n   * Whether at least 1 resource of the type is available in all business *locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).\n   * @readonly\n   */\n  hasResourcesInAllLocations?: boolean | null;\n  /**\n   * Whether at least 1 resource of the type is available in customer locations.\n   * @readonly\n   */\n  hasResourcesInCustomerLocations?: boolean | null;\n  /**\n   * Whether at least 1 resource of the type is available in custom business locations.\n   * @readonly\n   */\n  hasResourcesInCustomLocations?: boolean | null;\n  /**\n   * Number of distinct business locations with resources connected to the type.\n   * @readonly\n   */\n  distinctBusinessLocationsCount?: number | null;\n  /**\n   * IDs of all business locations with resources connected to the type. If there\n   * are more than 50 location IDs, only the first 50 IDs are returned.\n   * @readonly\n   */\n  distinctLocationIds?: DistinctLocationIds;\n}\n\nexport interface DistinctLocationIds {\n  /**\n   * IDs of the business locations with a resource connected to the type.\n   * @format GUID\n   * @maxSize 50\n   */\n  values?: string[];\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\nexport interface CreateResourceTypeRequest {\n  /** Resource type to create. */\n  resourceType: ResourceType;\n}\n\nexport interface LocationOptions {\n  /**\n   * Whether the resource is available in all *business locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).\n   *\n   * - `true`: The resource is available in all business locations.\n   * - `false`: The resource is available only in specific locations.\n   *\n   * Default: `false`\n   */\n  availableInAllLocations?: boolean | null;\n  /** Details of resource availability in specific locations. */\n  specificLocationOptions?: SpecificLocation;\n}\n\nexport interface SpecificLocation {\n  /**\n   * Whether the resource is available in *business locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).\n   *\n   * - `true`: The resource is available in business locations.\n   * - `false`: The resource isn't available in business locations.\n   *\n   * Default: `false`\n   */\n  availableInBusinessLocations?: boolean | null;\n  /**\n   * Information about the business locations where the resource is available.\n   * Not returned, if the resource is available in either all business locations\n   * or in no business location.\n   * You can specify up to 100 business locations.\n   * @maxSize 100\n   */\n  businessLocations?: BusinessLocation[];\n}\n\nexport interface BusinessLocation {\n  /**\n   * ID of the business *location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   * @format GUID\n   */\n  locationId?: string | null;\n}\n\nexport enum RequestedFields {\n  TOTAL_RESOURCE_COUNT = 'TOTAL_RESOURCE_COUNT',\n  SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS = 'SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS',\n  DISTINCT_RESOURCE_LOCATIONS = 'DISTINCT_RESOURCE_LOCATIONS',\n}\n\n/** @enumType */\nexport type RequestedFieldsWithLiterals =\n  | RequestedFields\n  | 'TOTAL_RESOURCE_COUNT'\n  | 'SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS'\n  | 'DISTINCT_RESOURCE_LOCATIONS';\n\nexport interface CreateResourceTypeResponse {\n  /** Created resource type. */\n  resourceType?: ResourceType;\n}\n\nexport enum CreateResourceTypeErrors {\n  UNKNOWN_CREATE_RESOURCE_TYPE_ERROR = 'UNKNOWN_CREATE_RESOURCE_TYPE_ERROR',\n  /** Failed to create requested `quantity` of resources for the resource type */\n  FAILED_TO_CREATE_RESOURCES = 'FAILED_TO_CREATE_RESOURCES',\n}\n\n/** @enumType */\nexport type CreateResourceTypeErrorsWithLiterals =\n  | CreateResourceTypeErrors\n  | 'UNKNOWN_CREATE_RESOURCE_TYPE_ERROR'\n  | 'FAILED_TO_CREATE_RESOURCES';\n\nexport interface GetResourceTypeRequest {\n  /**\n   * ID of the resource type to retrieve.\n   * @format GUID\n   */\n  resourceTypeId: string;\n}\n\nexport interface GetResourceTypeResponse {\n  /** Retrieved resource type. */\n  resourceType?: ResourceType;\n}\n\nexport interface UpdateResourceTypeRequest {\n  /** Resource type to update. */\n  resourceType: ResourceType;\n}\n\nexport interface UpdateResourceTypeResponse {\n  /** Updated resource type. */\n  resourceType?: ResourceType;\n}\n\nexport interface DeleteResourceTypeRequest {\n  /**\n   * ID of the resource type to delete.\n   * @format GUID\n   */\n  resourceTypeId: string;\n}\n\nexport interface DeleteResourceTypeResponse {}\n\nexport interface QueryResourceTypesRequest {\n  /**\n   * Information about filtering and sorting.\n   * Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters and sorting options.\n   */\n  query?: CursorQuery;\n}\n\nexport interface CursorQuery extends CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   * @maxSize 3\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 200\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface CursorPaging {\n  /**\n   * Number of items to load.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * You can get the relevant cursor token\n   * from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface QueryResourceTypesResponse {\n  /** Retrieved resource types. */\n  resourceTypes?: ResourceType[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  cursors?: Cursors;\n  /**\n   * Indicates if there are more results after the current page.\n   * If `true`, another page of results can be retrieved.\n   * If `false`, this is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor pointing to next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface CountResourceTypesRequest {\n  /**\n   * Filter to base the count on.\n   * Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountResourceTypesResponse {\n  /** Number of resource types matching the filter. */\n  count?: number;\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type CreateResourceTypeApplicationErrors =\n  | {\n      code?: 'RESOURCE_TYPE_ALREADY_EXISTS_FOR_NAME';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MAX_NUMBER_OF_RESOURCE_TYPES_REACHED';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type UpdateResourceTypeApplicationErrors = {\n  code?: 'RESOURCE_TYPE_ALREADY_EXISTS_FOR_NAME';\n  description?: string;\n  data?: Record<string, any>;\n};\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface ResourceTypeCreatedEnvelope {\n  entity: ResourceType;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a resource type is created.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.RESOURCE_TYPE_READ\n * @webhook\n * @eventType wix.bookings.resources.v2.resource_type_created\n * @slug created\n */\nexport declare function onResourceTypeCreated(\n  handler: (event: ResourceTypeCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface ResourceTypeDeletedEnvelope {\n  entity: ResourceType;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a resource type is deleted.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.RESOURCE_TYPE_READ\n * @webhook\n * @eventType wix.bookings.resources.v2.resource_type_deleted\n * @slug deleted\n */\nexport declare function onResourceTypeDeleted(\n  handler: (event: ResourceTypeDeletedEnvelope) => void | Promise<void>\n): void;\n\nexport interface ResourceTypeUpdatedEnvelope {\n  entity: ResourceType;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a resource type is updated.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.RESOURCE_TYPE_READ\n * @webhook\n * @eventType wix.bookings.resources.v2.resource_type_updated\n * @slug updated\n */\nexport declare function onResourceTypeUpdated(\n  handler: (event: ResourceTypeUpdatedEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Creates a new resource type.\n * @param resourceType - Resource type to create.\n * @public\n * @documentationMaturity preview\n * @requiredField resourceType\n * @requiredField resourceType.name\n * @permissionId BOOKINGS.RESOURCE_TYPE_CREATE\n * @applicableIdentity APP\n * @returns Created resource type.\n * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType\n */\nexport async function createResourceType(\n  resourceType: NonNullablePaths<ResourceType, `name`, 2>\n): Promise<\n  ResourceType & {\n    __applicationErrorsType?: CreateResourceTypeApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resourceType: resourceType,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2ResourceType.createResourceType(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resourceType!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { resourceType: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['resourceType']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a resource type.\n * @param resourceTypeId - ID of the resource type to retrieve.\n * @public\n * @documentationMaturity preview\n * @requiredField resourceTypeId\n * @permissionId BOOKINGS.RESOURCE_TYPE_READ\n * @permissionId BOOKINGS.RESOURCES_READ\n * @applicableIdentity APP\n * @returns Retrieved resource type.\n * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType\n */\nexport async function getResourceType(\n  resourceTypeId: string\n): Promise<ResourceType> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resourceTypeId: resourceTypeId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2ResourceType.getResourceType(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resourceType!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { resourceTypeId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['resourceTypeId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates a resource type.\n *\n *\n * Each time the resource type is updated, `revision` increments by 1. You must\n * include current revision of the resource type when updating it. This ensures\n * you're working with the latest service information and prevents unintended overwrites.\n * @param _id - Resource type ID.\n * @public\n * @documentationMaturity preview\n * @requiredField _id\n * @requiredField resourceType\n * @requiredField resourceType.revision\n * @permissionId BOOKINGS.RESOURCE_TYPE_UPDATE\n * @applicableIdentity APP\n * @returns Updated resource type.\n * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType\n */\nexport async function updateResourceType(\n  _id: string,\n  resourceType: NonNullablePaths<UpdateResourceType, `revision`, 2>\n): Promise<\n  ResourceType & {\n    __applicationErrorsType?: UpdateResourceTypeApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resourceType: { ...resourceType, id: _id },\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2ResourceType.updateResourceType(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resourceType!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { resourceType: '$[1]' },\n        explicitPathsToArguments: { 'resourceType.id': '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'resourceType']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateResourceType {\n  /**\n   * Resource type ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the resource type is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating the resource type.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was created.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was last updated.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the resource type. For example, `meeting room`. The name must be\n   * unique per site.\n   * @maxLength 40\n   * @minLength 1\n   */\n  name?: string | null;\n  /** Extensions enabling users to save custom data related to the resource type. */\n  extendedFields?: ExtendedFields;\n}\n\n/**\n * Deletes a resource type.\n *\n *\n * Deleting a resource type also automatically deletes all resources connected to it.\n * @param resourceTypeId - ID of the resource type to delete.\n * @public\n * @documentationMaturity preview\n * @requiredField resourceTypeId\n * @permissionId BOOKINGS.RESOURCE_TYPE_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType\n */\nexport async function deleteResourceType(\n  resourceTypeId: string\n): Promise<void> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    resourceTypeId: resourceTypeId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2ResourceType.deleteResourceType(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { resourceTypeId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['resourceTypeId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Creates a query to retrieve a list of resource types.\n *\n * The `queryResourceTypes()` function builds a query to retrieve a list of resource types and returns a `ResourceTypesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-builder/find) function.\n *\n * You can refine the query by chaining `ResourceTypesQueryBuilder` functions onto the query. `ResourceTypesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResourceTypes()` returns.\n *\n * `queryResourceTypes()` runs with the following `ResourceTypesQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryResourceTypes()` are applied in the order they are called.\n *\n * The following `ResourceTypesQueryBuilder` functions are supported for the `queryResourceTypes()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-result/items) property in `ResourceTypesQueryResult`.\n * @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.RESOURCE_TYPE_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes\n */\nexport function queryResourceTypes(): ResourceTypesQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    ResourceType,\n    'CURSOR',\n    QueryResourceTypesRequest,\n    QueryResourceTypesResponse\n  >({\n    func: async (payload: QueryResourceTypesRequest) => {\n      const reqOpts =\n        ambassadorWixBookingsResourcesV2ResourceType.queryResourceTypes(\n          payload\n        );\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (query: QueryResourceTypesRequest['query']) => {\n      const args = [query, {}] as [QueryResourceTypesRequest['query'], {}];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({\n      data,\n    }: HttpResponse<QueryResourceTypesResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [])\n      );\n\n      return {\n        items: transformedData?.resourceTypes,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'CURSOR',\n    transformationPaths: {},\n  });\n}\n\ninterface QueryCursorResult {\n  cursors: Cursors;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface ResourceTypesQueryResult extends QueryCursorResult {\n  items: ResourceType[];\n  query: ResourceTypesQueryBuilder;\n  next: () => Promise<ResourceTypesQueryResult>;\n  prev: () => Promise<ResourceTypesQueryResult>;\n}\n\nexport interface ResourceTypesQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  eq: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  ne: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  ge: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  gt: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  le: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  lt: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `string`.\n   * @param string - String to compare against. Case-insensitive.\n   * @documentationMaturity preview\n   */\n  startsWith: (\n    propertyName: 'name',\n    value: string\n  ) => ResourceTypesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   * @documentationMaturity preview\n   */\n  hasSome: (propertyName: 'name', value: any[]) => ResourceTypesQueryBuilder;\n  /** @documentationMaturity preview */\n  in: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;\n  /** @documentationMaturity preview */\n  exists: (propertyName: 'name', value: boolean) => ResourceTypesQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object.\n   * @documentationMaturity preview\n   */\n  limit: (limit: number) => ResourceTypesQueryBuilder;\n  /** @param cursor - A pointer to specific record\n   * @documentationMaturity preview\n   */\n  skipTo: (cursor: string) => ResourceTypesQueryBuilder;\n  /** @documentationMaturity preview */\n  find: () => Promise<ResourceTypesQueryResult>;\n}\n\n/**\n * Counts resource types, given the provided filtering.\n *\n *\n * Refer to the *supported filters article*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n * for a complete list of supported filters.\n * @public\n * @documentationMaturity preview\n * @param options - Filter to base the count on. See\n * [queryResourceTypes()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/query-resource-types)\n * for supported filters.\n * @permissionId BOOKINGS.RESOURCE_TYPE_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes\n */\nexport async function countResourceTypes(\n  options?: CountResourceTypesOptions\n): Promise<NonNullablePaths<CountResourceTypesResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsResourcesV2ResourceType.countResourceTypes(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountResourceTypesOptions {\n  /**\n   * Filter to base the count on.\n   * Refer to the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  filter?: Record<string, any> | null;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n      {\n        srcPath: '/_api/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/resource-types',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_resource-types';\n\n/** Creates a new resource type. */\nexport function createResourceType(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __createResourceType({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resourceType.createdDate' },\n          { path: 'resourceType.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceType.createdDate' },\n              { path: 'resourceType.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createResourceType;\n}\n\n/** Retrieves a resource type. */\nexport function getResourceType(payload: object): RequestOptionsFactory<any> {\n  function __getResourceType({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'GET' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/{resourceTypeId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceType.createdDate' },\n              { path: 'resourceType.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getResourceType;\n}\n\n/**\n * Updates a resource type.\n *\n *\n * Each time the resource type is updated, `revision` increments by 1. You must\n * include current revision of the resource type when updating it. This ensures\n * you're working with the latest service information and prevents unintended overwrites.\n */\nexport function updateResourceType(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __updateResourceType({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resourceType.createdDate' },\n          { path: 'resourceType.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'PATCH' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/{resourceType.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceType.createdDate' },\n              { path: 'resourceType.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateResourceType;\n}\n\n/**\n * Deletes a resource type.\n *\n *\n * Deleting a resource type also automatically deletes all resources connected to it.\n */\nexport function deleteResourceType(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __deleteResourceType({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'DELETE' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/{resourceTypeId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteResourceType;\n}\n\n/**\n * Creates a query to retrieve a list of resource types.\n *\n * The `queryResourceTypes()` function builds a query to retrieve a list of resource types and returns a `ResourceTypesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-builder/find) function.\n *\n * You can refine the query by chaining `ResourceTypesQueryBuilder` functions onto the query. `ResourceTypesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResourceTypes()` returns.\n *\n * `queryResourceTypes()` runs with the following `ResourceTypesQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryResourceTypes()` are applied in the order they are called.\n *\n * The following `ResourceTypesQueryBuilder` functions are supported for the `queryResourceTypes()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-result/items) property in `ResourceTypesQueryResult`.\n */\nexport function queryResourceTypes(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryResourceTypes({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceTypes.createdDate' },\n              { path: 'resourceTypes.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryResourceTypes;\n}\n\n/**\n * Counts resource types, given the provided filtering.\n *\n *\n * Refer to the *supported filters article*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n * for a complete list of supported filters.\n */\nexport function countResourceTypes(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __countResourceTypes({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countResourceTypes;\n}\n", "import {\n  createResourceType as publicCreateResourceType,\n  getResourceType as publicGetResourceType,\n  updateResourceType as publicUpdateResourceType,\n  deleteResourceType as publicDeleteResourceType,\n  queryResourceTypes as publicQueryResourceTypes,\n  countResourceTypes as publicCountResourceTypes,\n} from './bookings-resources-v2-resource-type-resource-types.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { createEventModule } from '@wix/sdk-runtime/event-definition-modules';\nimport {\n  BuildRESTFunction,\n  MaybeContext,\n  BuildEventDefinition,\n} from '@wix/sdk-types';\nimport { onResourceTypeCreated as publicOnResourceTypeCreated } from './bookings-resources-v2-resource-type-resource-types.public.js';\nimport { onResourceTypeDeleted as publicOnResourceTypeDeleted } from './bookings-resources-v2-resource-type-resource-types.public.js';\nimport { onResourceTypeUpdated as publicOnResourceTypeUpdated } from './bookings-resources-v2-resource-type-resource-types.public.js';\n\nexport const createResourceType: MaybeContext<\n  BuildRESTFunction<typeof publicCreateResourceType> &\n    typeof publicCreateResourceType\n> = /*#__PURE__*/ createRESTModule(publicCreateResourceType);\nexport const getResourceType: MaybeContext<\n  BuildRESTFunction<typeof publicGetResourceType> & typeof publicGetResourceType\n> = /*#__PURE__*/ createRESTModule(publicGetResourceType);\nexport const updateResourceType: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateResourceType> &\n    typeof publicUpdateResourceType\n> = /*#__PURE__*/ createRESTModule(publicUpdateResourceType);\nexport const deleteResourceType: MaybeContext<\n  BuildRESTFunction<typeof publicDeleteResourceType> &\n    typeof publicDeleteResourceType\n> = /*#__PURE__*/ createRESTModule(publicDeleteResourceType);\nexport const queryResourceTypes: MaybeContext<\n  BuildRESTFunction<typeof publicQueryResourceTypes> &\n    typeof publicQueryResourceTypes\n> = /*#__PURE__*/ createRESTModule(publicQueryResourceTypes);\nexport const countResourceTypes: MaybeContext<\n  BuildRESTFunction<typeof publicCountResourceTypes> &\n    typeof publicCountResourceTypes\n> = /*#__PURE__*/ createRESTModule(publicCountResourceTypes);\n/**\n * Triggered when a resource type is created.\n */\nexport const onResourceTypeCreated: BuildEventDefinition<\n  typeof publicOnResourceTypeCreated\n> = createEventModule(publicOnResourceTypeCreated);\n/**\n * Triggered when a resource type is deleted.\n */\nexport const onResourceTypeDeleted: BuildEventDefinition<\n  typeof publicOnResourceTypeDeleted\n> = createEventModule(publicOnResourceTypeDeleted);\n/**\n * Triggered when a resource type is updated.\n */\nexport const onResourceTypeUpdated: BuildEventDefinition<\n  typeof publicOnResourceTypeUpdated\n> = createEventModule(publicOnResourceTypeUpdated);\n\nexport {\n  RequestedFields,\n  CreateResourceTypeErrors,\n  SortOrder,\n  WebhookIdentityType,\n} from './bookings-resources-v2-resource-type-resource-types.universal.js';\nexport {\n  ResourceType,\n  ResourceCounts,\n  DistinctLocationIds,\n  ExtendedFields,\n  CreateResourceTypeRequest,\n  LocationOptions,\n  SpecificLocation,\n  BusinessLocation,\n  CreateResourceTypeResponse,\n  GetResourceTypeRequest,\n  GetResourceTypeResponse,\n  UpdateResourceTypeRequest,\n  UpdateResourceTypeResponse,\n  DeleteResourceTypeRequest,\n  DeleteResourceTypeResponse,\n  QueryResourceTypesRequest,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  Sorting,\n  CursorPaging,\n  QueryResourceTypesResponse,\n  CursorPagingMetadata,\n  Cursors,\n  CountResourceTypesRequest,\n  CountResourceTypesResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  RestoreInfo,\n  EntityUpdatedEvent,\n  EntityDeletedEvent,\n  ActionEvent,\n  MessageEnvelope,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  BaseEventMetadata,\n  EventMetadata,\n  ResourceTypeCreatedEnvelope,\n  ResourceTypeDeletedEnvelope,\n  ResourceTypeUpdatedEnvelope,\n  UpdateResourceType,\n  ResourceTypesQueryResult,\n  ResourceTypesQueryBuilder,\n  CountResourceTypesOptions,\n} from './bookings-resources-v2-resource-type-resource-types.universal.js';\nexport {\n  RequestedFieldsWithLiterals,\n  CreateResourceTypeErrorsWithLiterals,\n  SortOrderWithLiterals,\n  WebhookIdentityTypeWithLiterals,\n  CreateResourceTypeApplicationErrors,\n  UpdateResourceTypeApplicationErrors,\n} from './bookings-resources-v2-resource-type-resource-types.universal.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAAAA;AAAA,EAAA,0BAAAC;AAAA,EAAA,0BAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,6BAAAC;AAAA,EAAA,6BAAAC;AAAA,EAAA,6BAAAC;AAAA,EAAA,0BAAAC;AAAA,EAAA,0BAAAC;AAAA;AAAA;;;ACAA,IAAAC,iCAAwD;AACxD,IAAAC,oBAAqD;AACrD,IAAAC,0BAA+B;AAC/B,uBAA8D;;;ACH9D,6BAAoD;AACpD,2BAA6B;AAC7B,oCAGO;;;ACLP,0BAAkC;AAClC,uBAAqD;AACrD,IAAAC,oBAAqD;AACrD,wBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,kEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAGd,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,2BAA2B;AAAA,QACrC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,2BAA2B;AAAA,QACrC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AD3RA,IAAAC,0BAA+B;AAuJxB,IAAK,kBAAL,kBAAKC,qBAAL;AACL,EAAAA,iBAAA,0BAAuB;AACvB,EAAAA,iBAAA,4CAAyC;AACzC,EAAAA,iBAAA,iCAA8B;AAHpB,SAAAA;AAAA,GAAA;AAkBL,IAAK,2BAAL,kBAAKC,8BAAL;AACL,EAAAA,0BAAA,wCAAqC;AAErC,EAAAA,0BAAA,gCAA6B;AAHnB,SAAAA;AAAA,GAAA;AA2FL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AAgNL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AA2KZ,eAAsBC,oBACpB,cAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACyC,mBAAmB,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,cAAc,OAAO;AAAA,QACjD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc;AAAA,IACjB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAcA,eAAsBC,iBACpB,gBACuB;AAEvB,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACyC,gBAAgB,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,gBAAgB,OAAO;AAAA,QACnD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,gBAAgB;AAAA,IACnB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoBA,eAAsBE,oBACpB,KACA,cAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,cAAc,EAAE,GAAG,cAAc,IAAI,IAAI;AAAA,EAC3C,CAAC;AAED,QAAM,UACyC,mBAAmB,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,cAAc,OAAO;AAAA,QAC/C,0BAA0B,EAAE,mBAAmB,OAAO;AAAA,QACtD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,cAAc;AAAA,IACxB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkDA,eAAsBG,oBACpB,gBACe;AAEf,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACyC,mBAAmB,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAAA,EACjC,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAH;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,gBAAgB,OAAO;AAAA,QACnD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,gBAAgB;AAAA,IACnB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyBO,SAASI,sBAAgD;AAE9D,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,aAAO,mCAKL;AAAA,IACA,MAAM,OAAO,YAAuC;AAClD,YAAM,UACyC;AAAA,QAC3C;AAAA,MACF;AAEF,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,UAA8C;AACjE,YAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AACvB,iBAAO,qEAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC;AAAA,MACpB;AAAA,IACF,MAAgD;AAC9C,YAAM,sBAAkB;AAAA,YACtB,wCAAe,MAAM,CAAC,CAAC;AAAA,MACzB;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,uBAAmB,uBAAAJ,gBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;AA6FA,eAAsBK,oBACpB,SACmE;AAEnE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACyC,mBAAmB,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAL;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;ADhjCO,SAASM,oBACd,YAC6B;AAC7B,SAAO,CAAC,iBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAeO,SAASC,iBACd,YAC0B;AAC1B,SAAO,CAAC,mBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAWO,SAASC,oBACd,YAC6B;AAC7B,SAAO,CACL,KACA,iBAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAuBO,SAASC,oBACd,YAC6B;AAC7B,SAAO,CAAC,mBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAaO,SAASC,oBACd,YAC6B;AAC7B,SAAO,MACLA;AAAA;AAAA,IAEE,EAAE,WAAW;AAAA,EACf;AACJ;AAwBO,SAASC,oBACd,YAC6B;AAC7B,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAmBO,IAAM,4BAAwB;AAAA,EACnC;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA+B;AACxB,IAAM,4BAAwB;AAAA,EACnC;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA+B;AACxB,IAAM,4BAAwB;AAAA,EACnC;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA+B;;;AGrO/B,IAAAC,uBAAiC;AACjC,sCAAkC;AAU3B,IAAMC,sBAGK,2DAAiBA,mBAAwB;AACpD,IAAMC,mBAEK,2DAAiBA,gBAAqB;AACjD,IAAMC,sBAGK,2DAAiBA,mBAAwB;AACpD,IAAMC,sBAGK,2DAAiBA,mBAAwB;AACpD,IAAMC,sBAGK,2DAAiBA,mBAAwB;AACpD,IAAMC,sBAGK,2DAAiBA,mBAAwB;AAIpD,IAAMC,6BAET,mDAAkB,qBAA2B;AAI1C,IAAMC,6BAET,mDAAkB,qBAA2B;AAI1C,IAAMC,6BAET,mDAAkB,qBAA2B;", "names": ["countResourceTypes", "createResourceType", "deleteResourceType", "getResourceType", "onResourceTypeCreated", "onResourceTypeDeleted", "onResourceTypeUpdated", "queryResourceTypes", "updateResourceType", "import_rename_all_nested_keys", "import_timestamp", "import_transform_paths", "import_timestamp", "import_rest_modules", "payload", "import_transform_paths", "RequestedFields", "CreateResourceTypeErrors", "SortOrder", "WebhookIdentityType", "createResourceType", "sdkTransformError", "getResourceType", "updateResourceType", "deleteResourceType", "queryResourceTypes", "countResourceTypes", "createResourceType", "getResourceType", "updateResourceType", "deleteResourceType", "queryResourceTypes", "countResourceTypes", "import_rest_modules", "createResourceType", "getResourceType", "updateResourceType", "deleteResourceType", "queryResourceTypes", "countResourceTypes", "onResourceTypeCreated", "onResourceTypeDeleted", "onResourceTypeUpdated"]}