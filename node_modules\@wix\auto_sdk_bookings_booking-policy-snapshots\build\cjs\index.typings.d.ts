import { NonNullablePaths } from '@wix/sdk-types';

/**
 * The `bookingPolicySnapshot` object is the version of a service's booking policy
 * at the time a booking is created. This allows you for example to charge
 * customers the correct cancellation fee even after a service's cancellation
 * policy has been updated.
 */
interface BookingPolicySnapshot {
    /**
     * Booking policy snapshot ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * ID of the booking that's associated with this policy snapshot.
     * @format GUID
     */
    bookingId?: string | null;
    /**
     * Snapshot of the [booking policy](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/get-booking-policy).
     * at the time the corresponding booking was created.
     */
    policy?: BookingPolicy;
    /**
     * Date and time the booking policy snapshot was created in `YYYY-MM-DDThh:mm:ssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
}
/**
 * `BookingPolicy` is the main entity of `BookingPolicyService` and specifies a set of rules for booking a service
 * by visitors and members.
 *
 * Each `BookingPolicy` consists of a number of sub-policies. When the Bookings App is provisioned to a meta site then a
 * default `BookingPolicy` will be created with defaults for each of these sub-policies. This also applies when a request
 * is received to create a new `BookingPolicy` and one or more of these sub-policies are not provided.
 *
 * Sub-policies are defined in separate objects as specified below.
 * - The `CancellationPolicy` object defines the policy for canceling a booked session.
 * - The `ReschedulePolicy` object defines the policy for rescheduling booked session.
 *
 * By default each sub-policy is disabled. A more detailed specification of the default settings of each sub-policy
 * can be found in the description of the corresponding object.
 *
 * Partial updates are supported on the main entity level, however in order to update a sub-policy the client needs to provide the whole sub-policy object.
 */
interface BookingPolicy {
    /**
     * Booking policy ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking policy is updated. To prevent conflicting changes, the current `revision` must be passed when updating the booking policy.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the booking policy was created.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the booking policy was updated.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the booking policy.
     * @maxLength 400
     */
    name?: string | null;
    /** Custom description for the booking policy and whether the booking policy is displayed to the participant. */
    customPolicyDescription?: PolicyDescription;
    /**
     * Whether the booking policy is the default.
     * @readonly
     */
    default?: boolean | null;
    /** Rule for canceling a booking. */
    cancellationPolicy?: CancellationPolicy;
    /** Rule for rescheduling a booking. */
    reschedulePolicy?: ReschedulePolicy;
    /** Rules for cancellation fees. */
    cancellationFeePolicy?: CancellationFeePolicy;
    /** Rule for saving credit card details. */
    saveCreditCardPolicy?: SaveCreditCardPolicy;
}
/** A description of the booking policy to display to participants. */
interface PolicyDescription {
    /**
     * Whether the description is displayed to the participant. `true` means the
     * description is displayed.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Description of the booking policy.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    description?: string;
    /**
     * Description of the translated booking policy.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    descriptionTranslated?: string | null;
}
/** The rule for canceling a booked session. */
interface CancellationPolicy {
    /**
     * Whether customers can cancel the booking. `true` means customers can cancel
     * the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest cancellation time. `false` means
     * customers can cancel the booking until the last minute before the course or
     * session starts.
     *
     * Default: `false`
     */
    limitLatestCancellation?: boolean;
    /**
     * Minimum number of minutes before the start of the session customers can cancel.
     * For courses, this refers to the start of the first course session.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestCancellationInMinutes?: number;
}
/** The rule for rescheduling a booked session. */
interface ReschedulePolicy {
    /**
     * Whether customers can reschedule a booking for an appointment-based service.
     * `true` means customers can reschedule.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest supported rescheduling time. `false`
     * means customers can reschedule until the last minute before the session start.
     *
     * Default: `false`
     */
    limitLatestReschedule?: boolean;
    /**
     * Minimum number of minutes before the session start session customers can
     * reschedule their booking.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestRescheduleInMinutes?: number;
}
interface CancellationFeePolicy {
    /**
     * Whether customers must pay a cancellation fee when canceling a booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Time windows relative to the session start during which customers can cancel
     * their booking. Each window includes details about the fee for canceling
     * within it.
     * @maxSize 2
     */
    cancellationWindows?: CancellationWindow[];
    /**
     * Whether Wix automatically charges the cancellation fee from the customer once
     * they cancel their booking.
     *
     * Default: `true`
     */
    autoCollectFeeEnabled?: boolean | null;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gt:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface CancellationWindow extends CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
    /**
     * Start of the cancellation window in minutes before the session start. For
     * courses, this refers to the start of the first course session.
     * @min 1
     */
    startInMinutes?: number | null;
}
/** @oneof */
interface CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
}
interface SaveCreditCardPolicy {
    /**
     * Whether Wix stores credit card details of the customer. Storing the details
     * allows Wix to prefill the checkout and thus increases the likelihood that the
     * customer completes the booking process.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
interface ListPolicySnapshotsByBookingIdsRequest {
    /**
     * List of booking IDs to retrieve policy snapshots for.
     * @minSize 1
     * @maxSize 100
     * @format GUID
     */
    bookingIds: string[] | null;
}
interface ListPolicySnapshotsByBookingIdsResponse {
    /** Retrieved booking policy snapshots. */
    bookingPolicySnapshots?: BookingPolicySnapshot[];
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface Empty {
}
/** @docsIgnore */
type ListPolicySnapshotsByBookingIdsApplicationErrors = {
    code?: 'BOOKING_POLICY_SNAPSHOT_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/**
 * Retrieves a list of booking policy snapshots by booking IDs
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).
 * @param bookingIds - List of booking IDs to retrieve policy snapshots for.
 * @public
 * @requiredField bookingIds
 * @permissionId BOOKINGS.BOOKING_POLICY_SNAPSHOT_READ
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds
 */
declare function listPolicySnapshotsByBookingIds(bookingIds: string[]): Promise<NonNullablePaths<ListPolicySnapshotsByBookingIdsResponse, `bookingPolicySnapshots` | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.enabled` | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.description` | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.enabled` | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.limitLatestCancellation` | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.enabled` | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.limitLatestReschedule` | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicySnapshots.${number}.policy.cancellationFeePolicy.enabled` | `bookingPolicySnapshots.${number}.policy.saveCreditCardPolicy.enabled`, 6> & {
    __applicationErrorsType?: ListPolicySnapshotsByBookingIdsApplicationErrors;
}>;

export { type ActionEvent, type BookingPolicy, type BookingPolicySnapshot, type CancellationFeePolicy, type CancellationPolicy, type CancellationWindow, type CancellationWindowFeeOneOf, type DomainEvent, type DomainEventBodyOneOf, type Empty, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type ListPolicySnapshotsByBookingIdsApplicationErrors, type ListPolicySnapshotsByBookingIdsRequest, type ListPolicySnapshotsByBookingIdsResponse, type Money, type PolicyDescription, type ReschedulePolicy, type RestoreInfo, type SaveCreditCardPolicy, listPolicySnapshotsByBookingIds };
