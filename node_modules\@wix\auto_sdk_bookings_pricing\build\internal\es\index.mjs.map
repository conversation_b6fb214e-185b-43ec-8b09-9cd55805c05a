{"version": 3, "sources": ["../../../src/bookings-v2-price-info-pricing.universal.ts", "../../../src/bookings-v2-price-info-pricing.http.ts", "../../../src/bookings-v2-price-info-pricing.public.ts", "../../../src/bookings-v2-price-info-pricing.context.ts"], "sourcesContent": ["import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsV2PriceInfo from './bookings-v2-price-info-pricing.http.js';\n\nexport interface PriceInfo extends PriceInfoTotalPriceOneOf {\n  /**\n   * Calculated total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `FIXED`, `VARIED`, or `NO_FEE`.\n   */\n  calculatedPrice?: number;\n  /**\n   * Description of the total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `CUSTOM`.\n   * @deprecated Description of the total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `CUSTOM`.\n   * @replacedBy price_description_info.original\n   * @targetRemovalDate 2025-01-10\n   */\n  priceDescription?: string;\n  /**\n   * Description of the total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `CUSTOM`.\n   */\n  priceDescriptionInfo?: PriceDescriptionInfo;\n  /**\n   * List of line items, including the number of participants and the price per participant.\n   * @maxSize 20\n   */\n  bookingLineItems?: BookingLineItem[];\n  /**\n   * Total deposit the customer must pay when booking the service. Available if\n   * the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.options.deposit` is set to `true`.\n   */\n  deposit?: number | null;\n}\n\n/** @oneof */\nexport interface PriceInfoTotalPriceOneOf {\n  /**\n   * Calculated total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `FIXED`, `VARIED`, or `NO_FEE`.\n   */\n  calculatedPrice?: number;\n  /**\n   * Description of the total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `CUSTOM`.\n   * @deprecated Description of the total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `CUSTOM`.\n   * @replacedBy price_description_info.original\n   * @targetRemovalDate 2025-01-10\n   */\n  priceDescription?: string;\n  /**\n   * Description of the total price. Available if the _service's_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n   * `payment.rateType` is `CUSTOM`.\n   */\n  priceDescriptionInfo?: PriceDescriptionInfo;\n}\n\nexport interface BookingLineItem {\n  /**\n   * _Service ID_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).\n   *\n   * Required in *Calculate Price*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price))\n   * if the site uses Wix Bookings' default pricing logic.\n   *\n   * Optional in *Calculate Price*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price))\n   * if the site use pricing logic defined by a\n   * Wix Bookings Pricing Integration service plugin*\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction))\n   * integration.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * _Resource ID_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).\n   * Required in *Calculate Price*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price))\n   * and *Preview Price*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/preview-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/preview-price))\n   * for appointment-based services and classes.\n   * @format GUID\n   */\n  resourceId?: string | null;\n  /**\n   * Custom choices. Choices are specific values for an option the customer can choose to book.\n   * For example, the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.\n   * Each choice may have a different price. Refer to the *Service Options And Variants API*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction))\n   * for more details.\n   */\n  choices?: ServiceChoice[];\n  /**\n   * Number of participants for the line item.\n   * @min 1\n   */\n  numberOfParticipants?: number | null;\n  /**\n   * Price per participant for the line item.\n   * @readonly\n   */\n  pricePerParticipant?: number | null;\n}\n\nexport interface ServiceChoice extends ServiceChoiceChoiceOneOf {\n  /**\n   * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.\n   * Choices are specific values for an option the customer can choose to book. For example,\n   * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.\n   * Each choice may have a different price.\n   */\n  custom?: string;\n  duration?: Duration;\n  /**\n   * ID of the corresponding option for the choice. For example, the choice `child`\n   * could correspond to the option `ageGroup`. In this case, `optionId` is the ID\n   * for the `ageGroup` option.\n   * @format GUID\n   */\n  optionId?: string;\n}\n\n/** @oneof */\nexport interface ServiceChoiceChoiceOneOf {\n  /**\n   * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.\n   * Choices are specific values for an option the customer can choose to book. For example,\n   * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.\n   * Each choice may have a different price.\n   */\n  custom?: string;\n  duration?: Duration;\n}\n\nexport interface Duration {\n  /**\n   * Duration of the service in minutes.\n   * Min: 1 minute, Max: 30 days, 23 hours, and 59 minutes\n   * @min 1\n   * @max 44639\n   */\n  minutes?: number;\n  /**\n   * Name of the duration option.\n   * Defaults to the formatted duration e.g. \"1 hour, 30 minutes\".\n   * @maxLength 255\n   */\n  name?: string | null;\n}\n\nexport interface PricingServiceChoices {\n  /**\n   * Number of participants for this variant\n   * @min 1\n   */\n  numberOfParticipants?: number | null;\n  /**\n   * Service choices for these participants.\n   * @maxSize 5\n   */\n  choices?: ServiceChoice[];\n  /**\n   * Price per participant for the line item.\n   * @readonly\n   */\n  pricePerParticipant?: number | null;\n}\n\nexport interface PriceDescriptionInfo {\n  /**\n   * Price description in the site's main language. Refer to the *Site Properties API*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/introduction))\n   * for more details.\n   * @maxLength 50\n   */\n  original?: string;\n  /**\n   * Translated price description. Available if the booking was made in a\n   * secondary language.\n   * @maxLength 50\n   */\n  translated?: string | null;\n}\n\nexport interface PreviewPriceRequest {\n  /**\n   * List of line items to preview the price for.\n   * @minSize 1\n   * @maxSize 20\n   */\n  bookingLineItems: BookingLineItem[];\n}\n\nexport interface PreviewPriceResponse {\n  /** Information about each line item's price and the estimated total price based on the line items. */\n  priceInfo?: PriceInfo;\n}\n\nexport interface CalculatePriceRequest {\n  /**\n   * _Booking_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/introduction))\n   * to calculate the base price for.\n   */\n  booking: Booking;\n}\n\n/** An entity representing a scheduled appointment, class session, or course. */\nexport interface Booking extends BookingParticipantsInfoOneOf {\n  /**\n   * Total number of participants.\n   * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the booked service choices and participant count for each choice.\n   * When creating a booking, use this field only if the booking includes multiple service variants\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   *\n   * For example, use this for a spa package booking that includes different service levels:\n   * - 2 participants chose \"Standard Package\".\n   * - 1 participant chose \"VIP Package\".\n   */\n  participantsChoices?: ParticipantChoices;\n  /**\n   * Booking ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * An object describing the bookable entity - either a specific time slot or a recurring schedule.\n   *\n   * The structure depends on the type of service being booked:\n   *\n   * *For appointment services:** Use `slot` to book a specific time slot with a\n   * service provider. Appointments are typically one-time sessions at a specific date and time.\n   *\n   * *For class services:** Use `slot` to book a specific class session. Classes\n   * are individual sessions that can have multiple participants.\n   *\n   * *For course services:** Use `schedule` to book an entire course consisting of\n   * multiple sessions over time. Courses are recurring, multi-session offerings.\n   *\n   * Choose the appropriate field based on your service type and booking requirements.\n   */\n  bookedEntity?: BookedEntity;\n  /**\n   * Contact details of the site visitor or member\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/members/members/introduction) | [REST](https://dev.wix.com/docs/rest/crm/members-contacts/members/members/introduction))\n   * making the booking.\n   */\n  contactDetails?: ContactDetails;\n  /** Additional custom fields submitted with the booking form. */\n  additionalFields?: CustomFormField[];\n  /**\n   * Booking status. A booking is automatically confirmed if the service allows it\n   * and an eCommerce order is created. It is automatically declined if there is a\n   * double booking and the customer hasn't paid or is eligible for an automatic\n   * refund. Currently, only payments with pricing plans are automatically refundable.\n   */\n  status?: BookingStatusWithLiterals;\n  /**\n   * The payment status of the booking. This field automatically syncs with the\n   * `paymentStatus` of the corresponding eCommerce order\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup)| [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))\n   * when customers use Wix eCommerce checkout.\n   *\n   * ## Integration patterns\n   *\n   * *When using Wix eCommerce checkout:** Wix Bookings automatically syncs the payment status based on the eCommerce order's payment status.\n   * Do not manually update this field.\n   *\n   * *When using custom payment flows:** You can manually update the payment status with Confirm Booking or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) to reflect the customer's payment state.\n   *\n   * *For membership/pricing plan payments:** Wix Bookings automatically manages the payment status when customers pay with an active pricing plan ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/introduction)) subscription.\n   *\n   * All payment statuses are supported for every booking `status`.\n   */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /**\n   * Payment option selected by the customer. If the customer hasn't completed their checkout, they may still change the payment method. Must be one of the payment options offered by the service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)), unless `skipSelectedPaymentOptionValidation` is `true`.\n   *\n   * When undefined during an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)), Wix Bookings uses the service's default payment option\n   */\n  selectedPaymentOption?: SelectedPaymentOptionWithLiterals;\n  /**\n   * Date and time the booking was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /** External user ID that you can provide. */\n  externalUserId?: string | null;\n  /**\n   * Revision number to be used when updating, rescheduling, or cancelling the booking.\n   * Increments by 1 each time the booking is updated, rescheduled, or canceled. To prevent conflicting changes, the current revision must be specified when updating the booking.\n   */\n  revision?: string | null;\n  /**\n   * ID of the creator of the booking.\n   * If `appId` and another ID are present, the other ID takes precedence.\n   * @readonly\n   */\n  createdBy?: IdentificationData;\n  /**\n   * The start date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * For a slot, this is the start date of the slot. For a schedule, this is the start date of the first session.\n   * @readonly\n   */\n  startDate?: Date | null;\n  /**\n   * The end date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * For a slot, this is the end date of the slot. For a schedule, this is the end date of the last session.\n   * @readonly\n   */\n  endDate?: Date | null;\n  /**\n   * Date and time the booking was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Custom field data for this object.\n   * Extended fields must be configured in the app dashboard before they can be accessed with API calls.\n   */\n  extendedFields?: ExtendedFields;\n  /**\n   * Whether this booking overlaps with another confirmed booking. Returned\n   * only if set to `true`.\n   * @readonly\n   */\n  doubleBooked?: boolean | null;\n}\n\n/** @oneof */\nexport interface BookingParticipantsInfoOneOf {\n  /**\n   * Total number of participants.\n   * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the booked service choices and participant count for each choice.\n   * When creating a booking, use this field only if the booking includes multiple service variants\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   *\n   * For example, use this for a spa package booking that includes different service levels:\n   * - 2 participants chose \"Standard Package\".\n   * - 1 participant chose \"VIP Package\".\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\n/**\n * A multi-service booking is considered available if all single-service bookings are available as returned from List Multi Service Availability Time Slots.\n * Currently, `SEPARATE_BOOKINGS` and `PARALLEL_BOOKINGS` are not supported.\n * Multi-service booking is available if each of its bookings is available separately.\n * For `SEQUENTIAL_BOOKINGS`, see `List Multi Service Availability Time Slots` documentation.\n */\nexport enum MultiServiceBookingType {\n  /** You must schedule single-service bookings back-to-back with each booking starting when the previous booking ends */\n  SEQUENTIAL_BOOKINGS = 'SEQUENTIAL_BOOKINGS',\n  /** Not currently supported. */\n  SEPARATE_BOOKINGS = 'SEPARATE_BOOKINGS',\n  /** Not currently supported. */\n  PARALLEL_BOOKINGS = 'PARALLEL_BOOKINGS',\n}\n\n/** @enumType */\nexport type MultiServiceBookingTypeWithLiterals =\n  | MultiServiceBookingType\n  | 'SEQUENTIAL_BOOKINGS'\n  | 'SEPARATE_BOOKINGS'\n  | 'PARALLEL_BOOKINGS';\n\nexport interface BookedEntity extends BookedEntityItemOneOf {\n  /**\n   * Booked slot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).\n   *\n   * Specify `slot` when creating bookings for:\n   * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).\n   * Wix Bookings creates a new session when the booking is confirmed.\n   * - **Class services:** Group sessions at specific times (fitness classes, workshops).\n   * Wix Bookings links the booking to an existing scheduled session.\n   *\n   * For course services, specify `schedule` instead of `slot`.\n   */\n  slot?: BookedSlot;\n  /**\n   * Booked schedule\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   *\n   * Specify `schedule` when creating bookings for:\n   * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).\n   * Wix Bookings enrolls participants in all sessions defined by the course schedule.\n   */\n  schedule?: BookedSchedule;\n  /**\n   * Session title at the time of booking. If there is no pre-existing session,\n   * for example for appointment-based services, Wix Bookings sets `title` to the service name.\n   * @readonly\n   * @maxLength 6000\n   */\n  title?: string | null;\n  /**\n   * List of tags for the booking.\n   *\n   * - \"INDIVIDUAL\": For bookings of appointment-based services. Including when the appointment is for a group of participants.\n   * - \"GROUP\": For bookings of individual class sessions.\n   * - \"COURSE\": For course bookings.\n   */\n  tags?: string[] | null;\n}\n\n/** @oneof */\nexport interface BookedEntityItemOneOf {\n  /**\n   * Booked slot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).\n   *\n   * Specify `slot` when creating bookings for:\n   * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).\n   * Wix Bookings creates a new session when the booking is confirmed.\n   * - **Class services:** Group sessions at specific times (fitness classes, workshops).\n   * Wix Bookings links the booking to an existing scheduled session.\n   *\n   * For course services, specify `schedule` instead of `slot`.\n   */\n  slot?: BookedSlot;\n  /**\n   * Booked schedule\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   *\n   * Specify `schedule` when creating bookings for:\n   * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).\n   * Wix Bookings enrolls participants in all sessions defined by the course schedule.\n   */\n  schedule?: BookedSchedule;\n}\n\nexport interface BookedSlot {\n  /** Session ID. */\n  sessionId?: string | null;\n  /** Service ID. */\n  serviceId?: string;\n  /** Schedule ID. */\n  scheduleId?: string;\n  /**\n   * ID of the corresponding event\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).\n   * Available for both appointment and class bookings, not available for course bookings.\n   * For appointment-based services, Wix Bookings automatically populates `eventId` when the booking `status` changes to `CONFIRMED`.\n   * For class bookings, Wix Bookings automatically populates `eventId` upon booking creation.\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n  /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  startDate?: string | null;\n  /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  endDate?: string | null;\n  /** The time zone according to which the slot was shown to the user when booking, and should be shown in the future. */\n  timezone?: string | null;\n  /**\n   * Primary resource\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) for the booking.\n   * For example, the staff member ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) providing the service.\n   */\n  resource?: BookedResource;\n  /** Location where the session takes place. */\n  location?: Location;\n}\n\nexport interface BookedResource {\n  /**\n   * ID of the booking's primary resource.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Resource's name at the time of booking.\n   * @maxLength 40\n   */\n  name?: string | null;\n  /**\n   * Resource's email at the time of booking.\n   * @maxLength 500\n   */\n  email?: string | null;\n  /**\n   * ID of the schedule belonging to the booking's primary resource.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n}\n\nexport interface Location {\n  /**\n   * Business location ID. Available only for locations that are business locations,\n   * meaning the `location_type` is `\"OWNER_BUSINESS\"`.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Location name. */\n  name?: string | null;\n  /** The full address of this location. */\n  formattedAddress?: string | null;\n  /**\n   * The full translated address of this location.\n   * @maxLength 512\n   */\n  formattedAddressTranslated?: string | null;\n  /**\n   * Location type.\n   *\n   * - `\"OWNER_BUSINESS\"`: The business address, as set in the site’s general settings.\n   * - `\"OWNER_CUSTOM\"`: The address as set when creating the service.\n   * - `\"CUSTOM\"`: The address as set for the individual session.\n   */\n  locationType?: LocationTypeWithLiterals;\n}\n\nexport enum LocationType {\n  UNDEFINED = 'UNDEFINED',\n  OWNER_BUSINESS = 'OWNER_BUSINESS',\n  OWNER_CUSTOM = 'OWNER_CUSTOM',\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNDEFINED'\n  | 'OWNER_BUSINESS'\n  | 'OWNER_CUSTOM'\n  | 'CUSTOM';\n\nexport interface BookedSchedule {\n  /** Schedule ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)). */\n  scheduleId?: string;\n  /**\n   * Booked service ID.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * Location ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)) where the schedule's sessions take place.\n   * @readonly\n   */\n  location?: Location;\n  /**\n   * Time zone in which the slot or session was shown to the customer when they booked.\n   * Also used whenever the customer reviews the booking's timing in the future.\n   */\n  timezone?: string | null;\n  /**\n   * Start time of the first session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.\n   * @readonly\n   */\n  firstSessionStart?: string | null;\n  /**\n   * End time of the last session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.\n   * @readonly\n   */\n  lastSessionEnd?: string | null;\n}\n\nexport interface ContactDetails {\n  /**\n   * Contact ID.\n   * @format GUID\n   */\n  contactId?: string | null;\n  /**\n   * Contact's first name. When populated from a standard booking form, this\n   * property corresponds to the `name` field.\n   */\n  firstName?: string | null;\n  /** Contact's last name. */\n  lastName?: string | null;\n  /**\n   * Contact's email. If no [contact](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/contacts/contact-v4/contact-object)\n   * with this email exist, a new contact is created.\n   * Used to validate coupon usage limitations per contact. If not specified,\n   * the coupon usage limitation will not be enforced. (Coupon usage limitation\n   * validation is not supported yet).\n   * @format EMAIL\n   */\n  email?: string | null;\n  /** Contact's phone number. */\n  phone?: string | null;\n  /** Contact's full address. */\n  fullAddress?: Address;\n  /**\n   * Contact's time zone.\n   * @deprecated\n   */\n  timeZone?: string | null;\n  /**\n   * Contact's country in [ISO 3166-1 alpha-2 code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)\n   * format.\n   * @format COUNTRY\n   */\n  countryCode?: string | null;\n}\n\n/** Physical address */\nexport interface Address extends AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n  /**\n   * Country code.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  subdivision?: string | null;\n  /** City name. */\n  city?: string | null;\n  /** Zip/postal code. */\n  postalCode?: string | null;\n  /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */\n  addressLine2?: string | null;\n  /** A string containing the full address of this location. */\n  formattedAddress?: string | null;\n  /** Free text to help find the address. */\n  hint?: string | null;\n  /** Coordinates of the physical address. */\n  geocode?: AddressLocation;\n  /** Country full name. */\n  countryFullname?: string | null;\n  /** Multi-level subdivisions from top to bottom. */\n  subdivisions?: Subdivision[];\n}\n\n/** @oneof */\nexport interface AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n}\n\nexport interface StreetAddress {\n  /** Street number. */\n  number?: string;\n  /** Street name. */\n  name?: string;\n  /** Apartment number. */\n  apt?: string;\n}\n\nexport interface AddressLocation {\n  /** Address latitude. */\n  latitude?: number | null;\n  /** Address longitude. */\n  longitude?: number | null;\n}\n\nexport interface Subdivision {\n  /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  code?: string;\n  /** Subdivision full name. */\n  name?: string;\n}\n\nexport interface CustomFormField {\n  /**\n   * ID of the form field as defined in the form.\n   * @format GUID\n   */\n  _id?: string;\n  /** Value that was submitted for this field. */\n  value?: string | null;\n  /**\n   * Form field's label at the time of submission.\n   * @readonly\n   */\n  label?: string | null;\n  valueType?: ValueTypeWithLiterals;\n}\n\nexport enum ValueType {\n  /** Short text. This is the default value type. */\n  SHORT_TEXT = 'SHORT_TEXT',\n  /** Long text. */\n  LONG_TEXT = 'LONG_TEXT',\n  /** A text that represents the check box value. If selected the value is `true`, otherwise `false`. */\n  CHECK_BOX = 'CHECK_BOX',\n}\n\n/** @enumType */\nexport type ValueTypeWithLiterals =\n  | ValueType\n  | 'SHORT_TEXT'\n  | 'LONG_TEXT'\n  | 'CHECK_BOX';\n\n/** Booking status. */\nexport enum BookingStatus {\n  /** The booking was created, but the customer hasn't completed the related eCommerce order yet. */\n  CREATED = 'CREATED',\n  /**\n   * The merchant has confirmed the booking and it appears in the business calendar. Merchants can set up their services\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) to automatically confirm all `PENDING` bookings.\n   */\n  CONFIRMED = 'CONFIRMED',\n  /**\n   * The customer has canceled the booking. Depending on the relevant service's policy snapshot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))\n   * they may have to pay a cancellation fee ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction)).\n   */\n  CANCELED = 'CANCELED',\n  /** The merchant must manually confirm the booking before it appears in the business calendar. */\n  PENDING = 'PENDING',\n  /** The merchant has declined the booking before the customer was charged. */\n  DECLINED = 'DECLINED',\n  /**\n   * The booking is on a waitlist.\n   * Currently, you can't call Register to Waitlist ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/waitlist/register-to-waitlist)) for course or appointment bookings, even though this is supported in live sites.\n   * You can call Register to Waitlist only for class session bookings.\n   */\n  WAITING_LIST = 'WAITING_LIST',\n}\n\n/** @enumType */\nexport type BookingStatusWithLiterals =\n  | BookingStatus\n  | 'CREATED'\n  | 'CONFIRMED'\n  | 'CANCELED'\n  | 'PENDING'\n  | 'DECLINED'\n  | 'WAITING_LIST';\n\n/**\n * Payment status.\n * Automatically updated when using eCommerce checkout APIs.\n */\nexport enum PaymentStatus {\n  /** Undefined payment status. */\n  UNDEFINED = 'UNDEFINED',\n  /** The booking isn't paid. */\n  NOT_PAID = 'NOT_PAID',\n  /** The booking is fully paid. */\n  PAID = 'PAID',\n  /** The booking is partially paid. */\n  PARTIALLY_PAID = 'PARTIALLY_PAID',\n  /** The booking is refunded. */\n  REFUNDED = 'REFUNDED',\n  /** The booking is free of charge. */\n  EXEMPT = 'EXEMPT',\n}\n\n/** @enumType */\nexport type PaymentStatusWithLiterals =\n  | PaymentStatus\n  | 'UNDEFINED'\n  | 'NOT_PAID'\n  | 'PAID'\n  | 'PARTIALLY_PAID'\n  | 'REFUNDED'\n  | 'EXEMPT';\n\n/**\n * Selected payment option.\n *\n * One of the payment options offered by the service.\n * This field is set when the user selects an option during booking.\n * If left undefined, the payment option is resolved by the service configuration on checkout.\n */\nexport enum SelectedPaymentOption {\n  /** Undefined payment option. */\n  UNDEFINED = 'UNDEFINED',\n  /** Offline payment. */\n  OFFLINE = 'OFFLINE',\n  /** Online payment. */\n  ONLINE = 'ONLINE',\n  /** Payment using a Wix Pricing Plan. */\n  MEMBERSHIP = 'MEMBERSHIP',\n  /**\n   * Customers can pay only in person with a Wix Pricing Plan, while the Wix user\n   * must manually redeem the pricing plan in the dashboard.\n   */\n  MEMBERSHIP_OFFLINE = 'MEMBERSHIP_OFFLINE',\n}\n\n/** @enumType */\nexport type SelectedPaymentOptionWithLiterals =\n  | SelectedPaymentOption\n  | 'UNDEFINED'\n  | 'OFFLINE'\n  | 'ONLINE'\n  | 'MEMBERSHIP'\n  | 'MEMBERSHIP_OFFLINE';\n\nexport interface BookingSource {\n  /** Platform from which a booking was created. */\n  platform?: PlatformWithLiterals;\n  /** Actor that created this booking. */\n  actor?: ActorWithLiterals;\n  /**\n   * Wix site ID of the application that created the booking.\n   * @format GUID\n   * @readonly\n   */\n  appDefId?: string | null;\n  /**\n   * Name of the application that created the booking, as saved in Wix Developers Center at the time of booking.\n   * @readonly\n   */\n  appName?: string | null;\n}\n\nexport enum Platform {\n  UNDEFINED_PLATFORM = 'UNDEFINED_PLATFORM',\n  WEB = 'WEB',\n  MOBILE_APP = 'MOBILE_APP',\n}\n\n/** @enumType */\nexport type PlatformWithLiterals =\n  | Platform\n  | 'UNDEFINED_PLATFORM'\n  | 'WEB'\n  | 'MOBILE_APP';\n\nexport enum Actor {\n  UNDEFINED_ACTOR = 'UNDEFINED_ACTOR',\n  BUSINESS = 'BUSINESS',\n  CUSTOMER = 'CUSTOMER',\n}\n\n/** @enumType */\nexport type ActorWithLiterals =\n  | Actor\n  | 'UNDEFINED_ACTOR'\n  | 'BUSINESS'\n  | 'CUSTOMER';\n\nexport interface ParticipantNotification {\n  /**\n   * Whether to send a message about the changes to the customer.\n   *\n   * Default: `false`\n   */\n  notifyParticipants?: boolean;\n  /** Custom message to send to the participants about the changes to the booking. */\n  message?: string | null;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /**\n   * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.\n   * @format GUID\n   */\n  contactId?: string | null;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum IdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type IdentityTypeWithLiterals =\n  | IdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\n/**\n * Settings that control booking flow behavior and override default business rules.\n *\n * These settings allow administrators to bypass standard validation checks\n * and policies when creating, confirming, rescheduling, or canceling bookings.\n * Most settings require elevated permissions to use.\n *\n * Use flow control settings to handle special scenarios like:\n * - Emergency bookings outside normal business hours\n * - Admin-initiated bookings that bypass availability checks\n * - Custom payment flows that don't use standard eCommerce checkout\n * - Overriding cancellation or rescheduling policies in exceptional cases\n */\nexport interface FlowControlSettings {\n  /** Whether availability is checked when creating or confirming the booking. */\n  skipAvailabilityValidation?: boolean;\n  /**\n   * Whether the booking's `status` is automatically updated to `CONFIRMED` when\n   * the customer completes the eCommerce checkout\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),\n   * regardless of whether the relevant service requires manual business confirmation.\n   */\n  skipBusinessConfirmation?: boolean;\n  /**\n   * Whether the customer is allowed to pay with a payment method that isn't\n   * supported for the relevant service.\n   */\n  skipSelectedPaymentOptionValidation?: boolean;\n  /**\n   * Whether the customer receives an automatic refund if there's a double booking\n   * conflict. Only available if the customer has paid with a\n   * pricing plan.\n   */\n  withRefund?: boolean | null;\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\nexport interface ParticipantChoices {\n  /**\n   * Information about the booked service choices. Includes the number of participants.\n   * @minSize 1\n   * @maxSize 20\n   */\n  serviceChoices?: ServiceChoices[];\n}\n\nexport interface ServiceChoices {\n  /**\n   * Number of participants for this variant ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  numberOfParticipants?: number | null;\n  /**\n   * Service choices for these participants.\n   * @maxSize 5\n   */\n  choices?: ServiceChoice[];\n}\n\nexport interface MultiServiceBookingInfo {\n  /**\n   * Multi-service booking ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /** Type of the multi-service booking. */\n  type?: MultiServiceBookingTypeWithLiterals;\n}\n\nexport interface BookedAddOn {\n  /**\n   * The ID of the add-on.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * The ID of the add-on group.\n   * @format GUID\n   */\n  groupId?: string;\n  /**\n   * The add-on duration in minutes at the time of booking.\n   * @min 1\n   * @max 1440\n   * @readonly\n   */\n  durationInMinutes?: number | null;\n  /**\n   * The quantity of booked add-ons.\n   * @min 1\n   * @max 1000\n   */\n  quantity?: number | null;\n  /**\n   * Add-on `name` at the time of booking.\n   * @maxLength 100\n   * @readonly\n   */\n  name?: string | null;\n  /**\n   * Add-on name translated to the language the customer used during booking.\n   * @maxLength 100\n   * @readonly\n   */\n  nameTranslated?: string | null;\n}\n\nexport interface CalculatePriceResponse {\n  /** Information about each line item's base price and the total base price. */\n  priceInfo?: PriceInfo;\n}\n\n/** @docsIgnore */\nexport type PreviewPriceApplicationErrors =\n  | {\n      code?: 'INVALID_SERVICE_ID';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NUMBER_OF_PARTICIPANTS_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_SERVICE';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type CalculatePriceApplicationErrors =\n  | {\n      code?: 'INVALID_BOOKING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NUMBER_OF_PARTICIPANTS_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_SERVICE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ERROR_CONTACTING_PRICING_PROVIDER';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTIPLE_PRICING_PROVIDERS_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    };\n\n/**\n * Previews the base price for a set of line items belonging to the same\n * service, before a booking is created. During the booking flow, additional\n * taxes and fees may be added to the base price.\n *\n *\n * ## Response\n *\n * The response includes each line item's individual preview price and the\n * total of all line item preview prices. Note that the final price upon\n * booking creation may differ from the preview price.\n *\n * ## Errors\n *\n * _Preview Price_ fails if:\n *\n * - You specify line items that belong to different *services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).\n * - The site is using the *Bookings Pricing Integration SPI*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)).\n *\n * ## When to call Calculate Price instead\n *\n * To retrieve the price of an existing booking, call *Calculate Price*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price)).\n * @param bookingLineItems - List of line items to preview the price for.\n * @public\n * @documentationMaturity preview\n * @requiredField bookingLineItems\n * @permissionId BOOKINGS.PRICE_PREVIEW\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.pricing.BookingsPricingService.PreviewPrice\n */\nexport async function previewPrice(\n  bookingLineItems: BookingLineItem[]\n): Promise<\n  NonNullablePaths<\n    PreviewPriceResponse,\n    | `priceInfo.calculatedPrice`\n    | `priceInfo.priceDescription`\n    | `priceInfo.priceDescriptionInfo.original`\n    | `priceInfo.bookingLineItems`,\n    4\n  > & {\n    __applicationErrorsType?: PreviewPriceApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingLineItems: bookingLineItems,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2PriceInfo.previewPrice(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingLineItems: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingLineItems']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Calculates the base price of a booking.\n *\n *\n * The returned price serves as the foundation for charging the customer. During the\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/introduction)),\n * additional taxes and fees may be added to this base price.\n *\n * ## Price calculation method\n *\n * By default, Wix Bookings calculates a booking's price based on the relevant\n * `serviceOptionsAndVariants.variants.values.price`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n * You must then specify either `booking.bookedEntity.slot.serviceId` or\n * `booking.bookedEntity.schedule.scheduleId`.\n *\n * If the business uses the *Wix Bookings Pricing Integration service plugin*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)),\n * the returned `priceInfo` object reflects values received from the SPI implementor in\n * _Calculate Price_\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/calculate-price)).\n * In this case, it suffices to specify `booking.bookedEntity`.\n *\n * ## When to call Preview Price instead\n *\n * To estimate the price for multiple booking line items before the booking exists,\n * call *Preview Price*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/preview-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/preview-price)).\n * @param booking - _Booking_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/introduction))\n * to calculate the base price for.\n * @public\n * @documentationMaturity preview\n * @requiredField booking\n * @permissionId BOOKINGS.PRICE_CALCULATE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.pricing.BookingsPricingService.CalculatePrice\n */\nexport async function calculatePrice(booking: Booking): Promise<\n  NonNullablePaths<\n    CalculatePriceResponse,\n    | `priceInfo.calculatedPrice`\n    | `priceInfo.priceDescription`\n    | `priceInfo.priceDescriptionInfo.original`\n    | `priceInfo.bookingLineItems`,\n    4\n  > & {\n    __applicationErrorsType?: CalculatePriceApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ booking: booking });\n\n  const reqOpts = ambassadorWixBookingsV2PriceInfo.calculatePrice(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { booking: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['booking']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n", "import { transformSDKFloatToRESTFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsPricingBookingsPricingServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-pricing',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings-pricing/v2/price',\n        destPath: '/v2/pricing',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings-pricing',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings-pricing/v2/price',\n        destPath: '/v2/pricing',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/pricing/v2/pricing',\n        destPath: '/v2/pricing',\n      },\n      {\n        srcPath: '/bookings/v2/pricing',\n        destPath: '/v2/pricing',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_pricing';\n\n/**\n * Previews the base price for a set of line items belonging to the same\n * service, before a booking is created. During the booking flow, additional\n * taxes and fees may be added to the base price.\n *\n *\n * ## Response\n *\n * The response includes each line item's individual preview price and the\n * total of all line item preview prices. Note that the final price upon\n * booking creation may differ from the preview price.\n *\n * ## Errors\n *\n * _Preview Price_ fails if:\n *\n * - You specify line items that belong to different *services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).\n * - The site is using the *Bookings Pricing Integration SPI*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)).\n *\n * ## When to call Calculate Price instead\n *\n * To retrieve the price of an existing booking, call *Calculate Price*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price)).\n */\nexport function previewPrice(payload: object): RequestOptionsFactory<any> {\n  function __previewPrice({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'bookingLineItems.pricePerParticipant' },\n          { path: 'bookingLineItems.serviceChoices.pricePerParticipant' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.price_info',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.pricing.BookingsPricingService.PreviewPrice',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({\n        protoPath: '/v2/pricing/preview',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'priceInfo.calculatedPrice' },\n              { path: 'priceInfo.deposit' },\n              { path: 'priceInfo.bookingLineItems.pricePerParticipant' },\n              {\n                path: 'priceInfo.bookingLineItems.serviceChoices.pricePerParticipant',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __previewPrice;\n}\n\n/**\n * Calculates the base price of a booking.\n *\n *\n * The returned price serves as the foundation for charging the customer. During the\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/introduction)),\n * additional taxes and fees may be added to this base price.\n *\n * ## Price calculation method\n *\n * By default, Wix Bookings calculates a booking's price based on the relevant\n * `serviceOptionsAndVariants.variants.values.price`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n * You must then specify either `booking.bookedEntity.slot.serviceId` or\n * `booking.bookedEntity.schedule.scheduleId`.\n *\n * If the business uses the *Wix Bookings Pricing Integration service plugin*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)),\n * the returned `priceInfo` object reflects values received from the SPI implementor in\n * _Calculate Price_\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/calculate-price)).\n * In this case, it suffices to specify `booking.bookedEntity`.\n *\n * ## When to call Preview Price instead\n *\n * To estimate the price for multiple booking line items before the booking exists,\n * call *Preview Price*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/preview-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/preview-price)).\n */\nexport function calculatePrice(payload: object): RequestOptionsFactory<any> {\n  function __calculatePrice({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'booking.createdDate' },\n          { path: 'booking.startDate' },\n          { path: 'booking.endDate' },\n          { path: 'booking.updatedDate' },\n          { path: 'booking.canceledDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n          { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.price_info',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.pricing.BookingsPricingService.CalculatePrice',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({\n        protoPath: '/v2/pricing/calculate',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'priceInfo.calculatedPrice' },\n              { path: 'priceInfo.deposit' },\n              { path: 'priceInfo.bookingLineItems.pricePerParticipant' },\n              {\n                path: 'priceInfo.bookingLineItems.serviceChoices.pricePerParticipant',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __calculatePrice;\n}\n", "import { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  Booking,\n  BookingLineItem,\n  CalculatePriceApplicationErrors,\n  CalculatePriceResponse,\n  PreviewPriceApplicationErrors,\n  PreviewPriceResponse,\n  calculatePrice as universalCalculatePrice,\n  previewPrice as universalPreviewPrice,\n} from './bookings-v2-price-info-pricing.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function previewPrice(httpClient: HttpClient): PreviewPriceSignature {\n  return (bookingLineItems: BookingLineItem[]) =>\n    universalPreviewPrice(\n      bookingLineItems,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface PreviewPriceSignature {\n  /**\n   * Previews the base price for a set of line items belonging to the same\n   * service, before a booking is created. During the booking flow, additional\n   * taxes and fees may be added to the base price.\n   *\n   *\n   * ## Response\n   *\n   * The response includes each line item's individual preview price and the\n   * total of all line item preview prices. Note that the final price upon\n   * booking creation may differ from the preview price.\n   *\n   * ## Errors\n   *\n   * _Preview Price_ fails if:\n   *\n   * - You specify line items that belong to different *services*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).\n   * - The site is using the *Bookings Pricing Integration SPI*\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)).\n   *\n   * ## When to call Calculate Price instead\n   *\n   * To retrieve the price of an existing booking, call *Calculate Price*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price)).\n   * @param - List of line items to preview the price for.\n   */\n  (bookingLineItems: BookingLineItem[]): Promise<\n    NonNullablePaths<\n      PreviewPriceResponse,\n      | `priceInfo.calculatedPrice`\n      | `priceInfo.priceDescription`\n      | `priceInfo.priceDescriptionInfo.original`\n      | `priceInfo.bookingLineItems`,\n      4\n    > & {\n      __applicationErrorsType?: PreviewPriceApplicationErrors;\n    }\n  >;\n}\n\nexport function calculatePrice(\n  httpClient: HttpClient\n): CalculatePriceSignature {\n  return (booking: Booking) =>\n    universalCalculatePrice(\n      booking,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CalculatePriceSignature {\n  /**\n   * Calculates the base price of a booking.\n   *\n   *\n   * The returned price serves as the foundation for charging the customer. During the\n   * _eCommerce checkout_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/introduction)),\n   * additional taxes and fees may be added to this base price.\n   *\n   * ## Price calculation method\n   *\n   * By default, Wix Bookings calculates a booking's price based on the relevant\n   * `serviceOptionsAndVariants.variants.values.price`\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * You must then specify either `booking.bookedEntity.slot.serviceId` or\n   * `booking.bookedEntity.schedule.scheduleId`.\n   *\n   * If the business uses the *Wix Bookings Pricing Integration service plugin*\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)),\n   * the returned `priceInfo` object reflects values received from the SPI implementor in\n   * _Calculate Price_\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/calculate-price)).\n   * In this case, it suffices to specify `booking.bookedEntity`.\n   *\n   * ## When to call Preview Price instead\n   *\n   * To estimate the price for multiple booking line items before the booking exists,\n   * call *Preview Price*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/preview-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/preview-price)).\n   * @param - _Booking_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/introduction))\n   * to calculate the base price for.\n   */\n  (booking: Booking): Promise<\n    NonNullablePaths<\n      CalculatePriceResponse,\n      | `priceInfo.calculatedPrice`\n      | `priceInfo.priceDescription`\n      | `priceInfo.priceDescriptionInfo.original`\n      | `priceInfo.bookingLineItems`,\n      4\n    > & {\n      __applicationErrorsType?: CalculatePriceApplicationErrors;\n    }\n  >;\n}\n\nexport {\n  Actor,\n  Address,\n  AddressLocation,\n  AddressStreetOneOf,\n  BookedAddOn,\n  BookedEntity,\n  BookedEntityItemOneOf,\n  BookedResource,\n  BookedSchedule,\n  BookedSlot,\n  Booking,\n  BookingLineItem,\n  BookingParticipantsInfoOneOf,\n  BookingSource,\n  BookingStatus,\n  CalculatePriceRequest,\n  CalculatePriceResponse,\n  ContactDetails,\n  CustomFormField,\n  Duration,\n  ExtendedFields,\n  FlowControlSettings,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  IdentityType,\n  Location,\n  LocationType,\n  MultiServiceBookingInfo,\n  MultiServiceBookingType,\n  ParticipantChoices,\n  ParticipantNotification,\n  PaymentStatus,\n  Platform,\n  PreviewPriceRequest,\n  PreviewPriceResponse,\n  PriceDescriptionInfo,\n  PriceInfo,\n  PriceInfoTotalPriceOneOf,\n  PricingServiceChoices,\n  SelectedPaymentOption,\n  ServiceChoice,\n  ServiceChoiceChoiceOneOf,\n  ServiceChoices,\n  StreetAddress,\n  Subdivision,\n  ValueType,\n} from './bookings-v2-price-info-pricing.universal.js';\n", "import {\n  previewPrice as publicPreviewPrice,\n  calculatePrice as publicCalculatePrice,\n} from './bookings-v2-price-info-pricing.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { BuildRESTFunction, MaybeContext } from '@wix/sdk-types';\n\nexport const previewPrice: MaybeContext<\n  BuildRESTFunction<typeof publicPreviewPrice> & typeof publicPreviewPrice\n> = /*#__PURE__*/ createRESTModule(publicPreviewPrice);\nexport const calculatePrice: MaybeContext<\n  BuildRESTFunction<typeof publicCalculatePrice> & typeof publicCalculatePrice\n> = /*#__PURE__*/ createRESTModule(publicCalculatePrice);\n\nexport {\n  MultiServiceBookingType,\n  LocationType,\n  ValueType,\n  BookingStatus,\n  PaymentStatus,\n  SelectedPaymentOption,\n  Platform,\n  Actor,\n  IdentityType,\n} from './bookings-v2-price-info-pricing.universal.js';\nexport {\n  PriceInfo,\n  PriceInfoTotalPriceOneOf,\n  BookingLineItem,\n  ServiceChoice,\n  ServiceChoiceChoiceOneOf,\n  Duration,\n  PricingServiceChoices,\n  PriceDescriptionInfo,\n  PreviewPriceRequest,\n  PreviewPriceResponse,\n  CalculatePriceRequest,\n  Booking,\n  BookingParticipantsInfoOneOf,\n  BookedEntity,\n  BookedEntityItemOneOf,\n  BookedSlot,\n  BookedResource,\n  Location,\n  BookedSchedule,\n  ContactDetails,\n  Address,\n  AddressStreetOneOf,\n  StreetAddress,\n  AddressLocation,\n  Subdivision,\n  CustomFormField,\n  BookingSource,\n  ParticipantNotification,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  FlowControlSettings,\n  ExtendedFields,\n  ParticipantChoices,\n  ServiceChoices,\n  MultiServiceBookingInfo,\n  BookedAddOn,\n  CalculatePriceResponse,\n} from './bookings-v2-price-info-pricing.universal.js';\nexport {\n  MultiServiceBookingTypeWithLiterals,\n  LocationTypeWithLiterals,\n  ValueTypeWithLiterals,\n  BookingStatusWithLiterals,\n  PaymentStatusWithLiterals,\n  SelectedPaymentOptionWithLiterals,\n  PlatformWithLiterals,\n  ActorWithLiterals,\n  IdentityTypeWithLiterals,\n  PreviewPriceApplicationErrors,\n  CalculatePriceApplicationErrors,\n} from './bookings-v2-price-info-pricing.universal.js';\n"], "mappings": ";AAAA,SAAS,kBAAkB,yBAAyB;AACpD;AAAA,EACE;AAAA,EACA;AAAA,OACK;;;ACJP,SAAS,oCAAoC;AAC7C,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,2DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AA4Bd,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uCAAuC;AAAA,UAC/C,EAAE,MAAM,sDAAsD;AAAA,QAChE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,iDAAiD;AAAA,YACzD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgCO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,oBAAoB;AAAA,UAC5B,EAAE,MAAM,kBAAkB;AAAA,UAC1B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,QACjE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,iDAAiD;AAAA,YACzD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADgLO,IAAK,0BAAL,kBAAKC,6BAAL;AAEL,EAAAA,yBAAA,yBAAsB;AAEtB,EAAAA,yBAAA,uBAAoB;AAEpB,EAAAA,yBAAA,uBAAoB;AANV,SAAAA;AAAA,GAAA;AAoKL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,oBAAiB;AACjB,EAAAA,cAAA,kBAAe;AACf,EAAAA,cAAA,YAAS;AAJC,SAAAA;AAAA,GAAA;AAkKL,IAAK,YAAL,kBAAKC,eAAL;AAEL,EAAAA,WAAA,gBAAa;AAEb,EAAAA,WAAA,eAAY;AAEZ,EAAAA,WAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AAiBL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,aAAU;AAKV,EAAAA,eAAA,eAAY;AAMZ,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,aAAU;AAEV,EAAAA,eAAA,cAAW;AAMX,EAAAA,eAAA,kBAAe;AAvBL,SAAAA;AAAA,GAAA;AAwCL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,eAAY;AAEZ,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,UAAO;AAEP,EAAAA,eAAA,oBAAiB;AAEjB,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,YAAS;AAZC,SAAAA;AAAA,GAAA;AAgCL,IAAK,wBAAL,kBAAKC,2BAAL;AAEL,EAAAA,uBAAA,eAAY;AAEZ,EAAAA,uBAAA,aAAU;AAEV,EAAAA,uBAAA,YAAS;AAET,EAAAA,uBAAA,gBAAa;AAKb,EAAAA,uBAAA,wBAAqB;AAbX,SAAAA;AAAA,GAAA;AA2CL,IAAK,WAAL,kBAAKC,cAAL;AACL,EAAAA,UAAA,wBAAqB;AACrB,EAAAA,UAAA,SAAM;AACN,EAAAA,UAAA,gBAAa;AAHH,SAAAA;AAAA,GAAA;AAaL,IAAK,QAAL,kBAAKC,WAAL;AACL,EAAAA,OAAA,qBAAkB;AAClB,EAAAA,OAAA,cAAW;AACX,EAAAA,OAAA,cAAW;AAHD,SAAAA;AAAA,GAAA;AA4EL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,uBAAoB;AACpB,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,cAAW;AACX,EAAAA,cAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AA2NZ,eAAsBC,cACpB,kBAYA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAA2C,aAAa,OAAO;AAErE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,kBAAkB,OAAO;AAAA,QACrD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,kBAAkB;AAAA,IACrB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyCA,eAAsBC,gBAAe,SAWnC;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC,EAAE,QAAiB,CAAC;AAE1E,QAAM,UAA2C,eAAe,OAAO;AAEvE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,SAAS,OAAO;AAAA,QAC5C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;AE5uCO,SAASC,cAAa,YAA+C;AAC1E,SAAO,CAAC,qBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA4CO,SAASC,gBACd,YACyB;AACzB,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;;;ACtEA,SAAS,wBAAwB;AAG1B,IAAMC,gBAEK,iCAAiBA,aAAkB;AAC9C,IAAMC,kBAEK,iCAAiBA,eAAoB;", "names": ["payload", "MultiServiceBookingType", "LocationType", "ValueType", "BookingStatus", "PaymentStatus", "SelectedPaymentOption", "Platform", "Actor", "IdentityType", "previewPrice", "calculatePrice", "previewPrice", "calculatePrice", "previewPrice", "calculatePrice"]}