"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  AttendanceStatus: () => AttendanceStatus,
  SortOrder: () => SortOrder,
  WebhookIdentityType: () => WebhookIdentityType,
  bulkSetAttendance: () => bulkSetAttendance4,
  countAttendances: () => countAttendances4,
  getAttendance: () => getAttendance4,
  queryAttendance: () => queryAttendance4,
  setAttendance: () => setAttendance4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-v2-attendance-attendance.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_query_builder = require("@wix/sdk-runtime/query-builder");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-v2-attendance-attendance.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-attendance",
        destPath: ""
      },
      {
        srcPath: "/bookings-attendance",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings-attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      },
      {
        srcPath: "/bookings-attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings-attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/bookings/attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/bookings-attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/_api/bookings-attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/bookings/attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/bookings/attendance/v2/attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/bookings/v2/attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/bookings/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      },
      {
        srcPath: "/bookings-attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      },
      {
        srcPath: "/_api/bookings-attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      },
      {
        srcPath: "/bookings/attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings-attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/_api/bookings-attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      }
    ],
    _: [
      {
        srcPath: "/bookings-attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/_api/bookings-attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      },
      {
        srcPath: "/bookings/attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/bookings-attendance",
        destPath: "/v2/attendance"
      },
      {
        srcPath: "/api/bookings-attendance/v2/bulk/attendance",
        destPath: "/v2/bulk/attendance"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_attendance";
function getAttendance(payload) {
  function __getAttendance({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.attendance",
      method: "GET",
      methodFqn: "com.wixpress.bookings.attendance.v2.AttendanceService.GetAttendance",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({
        protoPath: "/v2/attendance/{attendanceId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "attendance.updatedDate" },
            { path: "attendance.statusUpdatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getAttendance;
}
function setAttendance(payload) {
  function __setAttendance({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "attendance.updatedDate" },
          { path: "attendance.statusUpdatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.attendance",
      method: "POST",
      methodFqn: "com.wixpress.bookings.attendance.v2.AttendanceService.SetAttendance",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({
        protoPath: "/v2/attendance/set",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "attendance.updatedDate" },
            { path: "attendance.statusUpdatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setAttendance;
}
function bulkSetAttendance(payload) {
  function __bulkSetAttendance({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "attendanceList.updatedDate" },
          { path: "attendanceList.statusUpdatedDate" },
          { path: "attendanceDetails.attendance.updatedDate" },
          { path: "attendanceDetails.attendance.statusUpdatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.attendance",
      method: "POST",
      methodFqn: "com.wixpress.bookings.attendance.v2.AttendanceService.BulkSetAttendance",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({
        protoPath: "/v2/bulk/attendance/set",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.updatedDate" },
            { path: "results.item.statusUpdatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkSetAttendance;
}
function queryAttendance(payload) {
  function __queryAttendance({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.attendance",
      method: "POST",
      methodFqn: "com.wixpress.bookings.attendance.v2.AttendanceService.QueryAttendance",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({
        protoPath: "/v2/attendance/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "attendances.updatedDate" },
            { path: "attendances.statusUpdatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryAttendance;
}
function countAttendances(payload) {
  function __countAttendances({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.attendance",
      method: "POST",
      methodFqn: "com.wixpress.bookings.attendance.v2.AttendanceService.CountAttendances",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({
        protoPath: "/v2/attendance/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countAttendances;
}

// src/bookings-v2-attendance-attendance.universal.ts
var import_transform_paths2 = require("@wix/sdk-runtime/transformations/transform-paths");
var AttendanceStatus = /* @__PURE__ */ ((AttendanceStatus2) => {
  AttendanceStatus2["NOT_SET"] = "NOT_SET";
  AttendanceStatus2["ATTENDED"] = "ATTENDED";
  AttendanceStatus2["NOT_ATTENDED"] = "NOT_ATTENDED";
  return AttendanceStatus2;
})(AttendanceStatus || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function getAttendance2(attendanceId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    attendanceId
  });
  const reqOpts = getAttendance(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.attendance;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { attendanceId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["attendanceId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function setAttendance2(attendance, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    attendance,
    participantNotification: options?.participantNotification
  });
  const reqOpts = setAttendance(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          attendance: "$[0]",
          participantNotification: "$[1].participantNotification"
        },
        singleArgumentUnchanged: false
      },
      ["attendance", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkSetAttendance2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    returnFullEntity: options?.returnFullEntity,
    attendanceDetails: options?.attendanceDetails
  });
  const reqOpts = bulkSetAttendance(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          returnFullEntity: "$[0].returnFullEntity",
          attendanceDetails: "$[0].attendanceDetails"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryAttendance2() {
  const { httpClient, sideEffects } = arguments[0];
  return (0, import_query_builder.queryBuilder)({
    func: async (payload) => {
      const reqOpts = queryAttendance(payload);
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({ data }) => {
      const transformedData = (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
        (0, import_transform_paths2.transformPaths)(data, [])
      );
      return {
        items: transformedData?.attendances,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = (0, import_transform_error.transformError)(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countAttendances2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = countAttendances(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-v2-attendance-attendance.public.ts
function getAttendance3(httpClient) {
  return (attendanceId) => getAttendance2(
    attendanceId,
    // @ts-ignore
    { httpClient }
  );
}
function setAttendance3(httpClient) {
  return (attendance, options) => setAttendance2(
    attendance,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkSetAttendance3(httpClient) {
  return (options) => bulkSetAttendance2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function queryAttendance3(httpClient) {
  return () => queryAttendance2(
    // @ts-ignore
    { httpClient }
  );
}
function countAttendances3(httpClient) {
  return (options) => countAttendances2(
    options,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-v2-attendance-attendance.context.ts
var import_rest_modules3 = require("@wix/sdk-runtime/rest-modules");
var getAttendance4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(getAttendance3);
var setAttendance4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(setAttendance3);
var bulkSetAttendance4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkSetAttendance3);
var queryAttendance4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryAttendance3);
var countAttendances4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(countAttendances3);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  AttendanceStatus,
  SortOrder,
  WebhookIdentityType,
  bulkSetAttendance,
  countAttendances,
  getAttendance,
  queryAttendance,
  setAttendance
});
//# sourceMappingURL=index.js.map