{"version": 3, "sources": ["../../../src/bookings-v1-booking-policy-booking-policies.http.ts", "../../../src/bookings-v1-booking-policy-booking-policies.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsV1BookingPoliciesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/booking-policies',\n        destPath: '/v1/booking-policies',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/booking-policies',\n        destPath: '/v1/booking-policies',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings/v1/booking-policies/strictest',\n        destPath: '/v1/booking-policies/strictest',\n      },\n      {\n        srcPath: '/_api/bookings/v1/booking-policies/query',\n        destPath: '/v1/booking-policies/query',\n      },\n      {\n        srcPath: '/bookings/v1/booking-policies',\n        destPath: '/v1/booking-policies',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_booking-policies';\n\n/** Creates a booking policy. */\nexport function createBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __createBookingPolicy({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'bookingPolicy.createdDate' },\n          { path: 'bookingPolicy.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createBookingPolicy;\n}\n\n/** Retrieves a booking policy. */\nexport function getBookingPolicy(payload: object): RequestOptionsFactory<any> {\n  function __getBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.GetBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicyId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getBookingPolicy;\n}\n\n/**\n * Retrieves the strictest version of each policy rule from a list of booking\n * policies.\n *\n *\n * Returns a hypothetical `bookingPolicy` object that combines the strictest\n * version of each rule. The `id` of the returned policy is `null` and no\n * corresponding `bookingPolicy` object is created. To create a new policy, you\n * can call *Create Booking Policy*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).\n */\nexport function getStrictestBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getStrictestBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/strictest',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getStrictestBookingPolicy;\n}\n\n/**\n * Updates a booking policy.\n *\n *\n * Each time the booking policy is updated, `revision` increments by 1.\n * The current `revision` must be specified when updating the booking policy.\n * This ensures you're working with the latest booking policy and prevents unintended overwrites.\n */\nexport function updateBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __updateBookingPolicy({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'bookingPolicy.createdDate' },\n          { path: 'bookingPolicy.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicy.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateBookingPolicy;\n}\n\n/**\n * Sets a booking policy as the default.\n *\n *\n * Also updates the site's current default policy by setting its `default`\n * attribute to `false`. If the provided policy is already the site's\n * default, the call succeeds without changing any `bookingPolicy` object.\n */\nexport function setDefaultBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __setDefaultBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicyId}:setDefault',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'currentDefaultBookingPolicy.createdDate' },\n              { path: 'currentDefaultBookingPolicy.updatedDate' },\n              { path: 'previousDefaultBookingPolicy.createdDate' },\n              { path: 'previousDefaultBookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setDefaultBookingPolicy;\n}\n\n/**\n * Deletes a booking policy.\n *\n *\n * You can't delete the default policy without first *setting a different policy as default*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/set-default-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/set-default-booking-policy)).\n */\nexport function deleteBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __deleteBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicyId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteBookingPolicy;\n}\n\n/**\n * Creates a query to retrieve a list of `bookingPolicy` objects.\n *\n * The `queryBookingPolicies()` function builds a query to retrieve a list of `bookingPolicy` objects and returns a `BookingPoliciesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-builder/find) function.\n *\n * You can refine the query by chaining `BookingPoliciesQueryBuilder` functions onto the query. `BookingPoliciesQueryBuilder` functions enable you to sort, filter, and control the results that `queryBookingPolicies()` returns.\n *\n * `queryBookingPolicies()` runs with the following `BookingPoliciesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryBookingPolicies()` are applied in the order they are called. For example, if you apply `ascending(\"waitlistPolicy.capacity\")` and then `ascending(\"name\")`, the results are sorted first by `waitlistPolicy.capacity`, and then, if there are multiple results with the same `waitlistPolicy.capacity`, the items are sorted by `name`.\n *\n * The following `BookingPoliciesQueryBuilder` functions are supported for the `queryBookingPolicies()` function. For a full description of the `bookingPolicy` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-result/items) property in `BookingPoliciesQueryResult`.\n */\nexport function queryBookingPolicies(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryBookingPolicies({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicies.createdDate' },\n              { path: 'bookingPolicies.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryBookingPolicies;\n}\n\n/**\n * Counts booking policies, given the provided filtering.\n *\n *\n * See *supported filters*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n * for a complete list of supported filters.\n */\nexport function countBookingPolicies(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __countBookingPolicies({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.CountBookingPolicies',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countBookingPolicies;\n}\n", "import * as ambassadorWixBookingsV1BookingPolicy from './bookings-v1-booking-policy-booking-policies.http.js';\nimport * as ambassadorWixBookingsV1BookingPolicyTypes from './bookings-v1-booking-policy-booking-policies.types.js';\nimport * as ambassadorWixBookingsV1BookingPolicyUniversalTypes from './bookings-v1-booking-policy-booking-policies.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function createBookingPolicy(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.CreateBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.CreateBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.CreateBookingPolicyResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.CreateBookingPolicyResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.createBookingPolicy(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/booking-policies',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getBookingPolicy(): __PublicMethodMetaInfo<\n  'GET',\n  { bookingPolicyId: string },\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.GetBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.GetBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.GetBookingPolicyResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.GetBookingPolicyResponse\n> {\n  const payload = { bookingPolicyId: ':bookingPolicyId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.getBookingPolicy(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v1/booking-policies/{bookingPolicyId}',\n    pathParams: { bookingPolicyId: 'bookingPolicyId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getStrictestBookingPolicy(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.GetStrictestBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.GetStrictestBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.GetStrictestBookingPolicyResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.GetStrictestBookingPolicyResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.getStrictestBookingPolicy(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/booking-policies/strictest',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateBookingPolicy(): __PublicMethodMetaInfo<\n  'PATCH',\n  { bookingPolicyId: string },\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.UpdateBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.UpdateBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.UpdateBookingPolicyResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.UpdateBookingPolicyResponse\n> {\n  const payload = { bookingPolicy: { id: ':bookingPolicyId' } } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.updateBookingPolicy(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PATCH',\n    path: '/v1/booking-policies/{bookingPolicy.id}',\n    pathParams: { bookingPolicyId: 'bookingPolicyId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function setDefaultBookingPolicy(): __PublicMethodMetaInfo<\n  'POST',\n  { bookingPolicyId: string },\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.SetDefaultBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.SetDefaultBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.SetDefaultBookingPolicyResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.SetDefaultBookingPolicyResponse\n> {\n  const payload = { bookingPolicyId: ':bookingPolicyId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.setDefaultBookingPolicy(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/booking-policies/{bookingPolicyId}:setDefault',\n    pathParams: { bookingPolicyId: 'bookingPolicyId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteBookingPolicy(): __PublicMethodMetaInfo<\n  'DELETE',\n  { bookingPolicyId: string },\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.DeleteBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.DeleteBookingPolicyRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.DeleteBookingPolicyResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.DeleteBookingPolicyResponse\n> {\n  const payload = { bookingPolicyId: ':bookingPolicyId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.deleteBookingPolicy(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'DELETE',\n    path: '/v1/booking-policies/{bookingPolicyId}',\n    pathParams: { bookingPolicyId: 'bookingPolicyId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryBookingPolicies(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.QueryBookingPoliciesRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.QueryBookingPoliciesRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.QueryBookingPoliciesResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.QueryBookingPoliciesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.queryBookingPolicies(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/booking-policies/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function countBookingPolicies(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.CountBookingPoliciesRequest,\n  ambassadorWixBookingsV1BookingPolicyTypes.CountBookingPoliciesRequest,\n  ambassadorWixBookingsV1BookingPolicyUniversalTypes.CountBookingPoliciesResponse,\n  ambassadorWixBookingsV1BookingPolicyTypes.CountBookingPoliciesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV1BookingPolicy.countBookingPolicies(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/booking-policies/count',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,8CACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAGd,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,4BAA4B;AAAA,QACtC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,4BAA4B;AAAA,QACtC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,2CAA2C;AAAA,YACnD,EAAE,MAAM,2CAA2C;AAAA,UACrD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,8BAA8B;AAAA,YACtC,EAAE,MAAM,8BAA8B;AAAA,UACxC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AChWO,SAASC,uBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACiC,oBAAoB,OAAO;AAElE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,oBAOd;AACA,QAAM,UAAU,EAAE,iBAAiB,mBAAmB;AAEtD,QAAM,oBACiC,iBAAiB,OAAO;AAE/D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,iBAAiB,kBAAkB;AAAA,IACjD,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,6BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACiC,0BAA0B,OAAO;AAExE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,uBAOd;AACA,QAAM,UAAU,EAAE,eAAe,EAAE,IAAI,mBAAmB,EAAE;AAE5D,QAAM,oBACiC,oBAAoB,OAAO;AAElE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,iBAAiB,kBAAkB;AAAA,IACjD,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,2BAOd;AACA,QAAM,UAAU,EAAE,iBAAiB,mBAAmB;AAEtD,QAAM,oBACiC,wBAAwB,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,iBAAiB,kBAAkB;AAAA,IACjD,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,uBAOd;AACA,QAAM,UAAU,EAAE,iBAAiB,mBAAmB;AAEtD,QAAM,oBACiC,oBAAoB,OAAO;AAElE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,iBAAiB,kBAAkB;AAAA,IACjD,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,wBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACiC,qBAAqB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,wBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACiC,qBAAqB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "createBookingPolicy", "getBookingPolicy", "getStrictestBookingPolicy", "updateBookingPolicy", "setDefaultBookingPolicy", "deleteBookingPolicy", "queryBookingPolicies", "countBookingPolicies"]}