// src/bookings-v1-booking-policy-booking-policies.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import { queryBuilder } from "@wix/sdk-runtime/query-builder";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-v1-booking-policy-booking-policies.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsV1BookingPoliciesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v1/booking-policies/strictest",
        destPath: "/v1/booking-policies/strictest"
      },
      {
        srcPath: "/_api/bookings/v1/booking-policies/query",
        destPath: "/v1/booking-policies/query"
      },
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policies";
function createBookingPolicy(payload) {
  function __createBookingPolicy({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBookingPolicy;
}
function getBookingPolicy(payload) {
  function __getBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "GET",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getBookingPolicy;
}
function getStrictestBookingPolicy(payload) {
  function __getStrictestBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/strictest",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getStrictestBookingPolicy;
}
function updateBookingPolicy(payload) {
  function __updateBookingPolicy({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "PATCH",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicy.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateBookingPolicy;
}
function setDefaultBookingPolicy(payload) {
  function __setDefaultBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}:setDefault",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "currentDefaultBookingPolicy.createdDate" },
            { path: "currentDefaultBookingPolicy.updatedDate" },
            { path: "previousDefaultBookingPolicy.createdDate" },
            { path: "previousDefaultBookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setDefaultBookingPolicy;
}
function deleteBookingPolicy(payload) {
  function __deleteBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "DELETE",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteBookingPolicy;
}
function queryBookingPolicies(payload) {
  function __queryBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicies.createdDate" },
            { path: "bookingPolicies.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryBookingPolicies;
}
function countBookingPolicies(payload) {
  function __countBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CountBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countBookingPolicies;
}

// src/bookings-v1-booking-policy-booking-policies.universal.ts
import { transformPaths as transformPaths2 } from "@wix/sdk-runtime/transformations/transform-paths";
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var PlacementType = /* @__PURE__ */ ((PlacementType2) => {
  PlacementType2["BEFORE"] = "BEFORE";
  PlacementType2["AFTER"] = "AFTER";
  PlacementType2["REPLACE"] = "REPLACE";
  return PlacementType2;
})(PlacementType || {});
var DayOfWeek = /* @__PURE__ */ ((DayOfWeek2) => {
  DayOfWeek2["MONDAY"] = "MONDAY";
  DayOfWeek2["TUESDAY"] = "TUESDAY";
  DayOfWeek2["WEDNESDAY"] = "WEDNESDAY";
  DayOfWeek2["THURSDAY"] = "THURSDAY";
  DayOfWeek2["FRIDAY"] = "FRIDAY";
  DayOfWeek2["SATURDAY"] = "SATURDAY";
  DayOfWeek2["SUNDAY"] = "SUNDAY";
  return DayOfWeek2;
})(DayOfWeek || {});
var ResolutionMethod = /* @__PURE__ */ ((ResolutionMethod2) => {
  ResolutionMethod2["QUERY_PARAM"] = "QUERY_PARAM";
  ResolutionMethod2["SUBDOMAIN"] = "SUBDOMAIN";
  ResolutionMethod2["SUBDIRECTORY"] = "SUBDIRECTORY";
  return ResolutionMethod2;
})(ResolutionMethod || {});
var State = /* @__PURE__ */ ((State2) => {
  State2["UNKNOWN"] = "UNKNOWN";
  State2["ENABLED"] = "ENABLED";
  State2["DISABLED"] = "DISABLED";
  State2["PENDING"] = "PENDING";
  State2["DEMO"] = "DEMO";
  return State2;
})(State || {});
var SiteCreatedContext = /* @__PURE__ */ ((SiteCreatedContext2) => {
  SiteCreatedContext2["OTHER"] = "OTHER";
  SiteCreatedContext2["FROM_TEMPLATE"] = "FROM_TEMPLATE";
  SiteCreatedContext2["DUPLICATE_BY_SITE_TRANSFER"] = "DUPLICATE_BY_SITE_TRANSFER";
  SiteCreatedContext2["DUPLICATE"] = "DUPLICATE";
  SiteCreatedContext2["OLD_SITE_TRANSFER"] = "OLD_SITE_TRANSFER";
  SiteCreatedContext2["FLASH"] = "FLASH";
  return SiteCreatedContext2;
})(SiteCreatedContext || {});
var Namespace = /* @__PURE__ */ ((Namespace2) => {
  Namespace2["UNKNOWN_NAMESPACE"] = "UNKNOWN_NAMESPACE";
  Namespace2["WIX"] = "WIX";
  Namespace2["SHOUT_OUT"] = "SHOUT_OUT";
  Namespace2["ALBUMS"] = "ALBUMS";
  Namespace2["WIX_STORES_TEST_DRIVE"] = "WIX_STORES_TEST_DRIVE";
  Namespace2["HOTELS"] = "HOTELS";
  Namespace2["CLUBS"] = "CLUBS";
  Namespace2["ONBOARDING_DRAFT"] = "ONBOARDING_DRAFT";
  Namespace2["DEV_SITE"] = "DEV_SITE";
  Namespace2["LOGOS"] = "LOGOS";
  Namespace2["VIDEO_MAKER"] = "VIDEO_MAKER";
  Namespace2["PARTNER_DASHBOARD"] = "PARTNER_DASHBOARD";
  Namespace2["DEV_CENTER_COMPANY"] = "DEV_CENTER_COMPANY";
  Namespace2["HTML_DRAFT"] = "HTML_DRAFT";
  Namespace2["SITELESS_BUSINESS"] = "SITELESS_BUSINESS";
  Namespace2["CREATOR_ECONOMY"] = "CREATOR_ECONOMY";
  Namespace2["DASHBOARD_FIRST"] = "DASHBOARD_FIRST";
  Namespace2["ANYWHERE"] = "ANYWHERE";
  Namespace2["HEADLESS"] = "HEADLESS";
  Namespace2["ACCOUNT_MASTER_CMS"] = "ACCOUNT_MASTER_CMS";
  Namespace2["RISE"] = "RISE";
  Namespace2["BRANDED_FIRST"] = "BRANDED_FIRST";
  Namespace2["NOWNIA"] = "NOWNIA";
  Namespace2["UGC_TEMPLATE"] = "UGC_TEMPLATE";
  Namespace2["CODUX"] = "CODUX";
  Namespace2["MEDIA_DESIGN_CREATOR"] = "MEDIA_DESIGN_CREATOR";
  Namespace2["SHARED_BLOG_ENTERPRISE"] = "SHARED_BLOG_ENTERPRISE";
  Namespace2["STANDALONE_FORMS"] = "STANDALONE_FORMS";
  Namespace2["STANDALONE_EVENTS"] = "STANDALONE_EVENTS";
  Namespace2["MIMIR"] = "MIMIR";
  return Namespace2;
})(Namespace || {});
var DeleteStatus = /* @__PURE__ */ ((DeleteStatus2) => {
  DeleteStatus2["UNKNOWN"] = "UNKNOWN";
  DeleteStatus2["TRASH"] = "TRASH";
  DeleteStatus2["DELETED"] = "DELETED";
  DeleteStatus2["PENDING_PURGE"] = "PENDING_PURGE";
  DeleteStatus2["PURGED_EXTERNALLY"] = "PURGED_EXTERNALLY";
  return DeleteStatus2;
})(DeleteStatus || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createBookingPolicy2(bookingPolicy) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingPolicy
  });
  const reqOpts = createBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.bookingPolicy;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicy: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicy"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getBookingPolicy2(bookingPolicyId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingPolicyId
  });
  const reqOpts = getBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.bookingPolicy;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getStrictestBookingPolicy2(bookingPolicyIds) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingPolicyIds
  });
  const reqOpts = getStrictestBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyIds: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyIds"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateBookingPolicy2(_id, bookingPolicy) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingPolicy: { ...bookingPolicy, id: _id }
  });
  const reqOpts = updateBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.bookingPolicy;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: { bookingPolicy: "$[1]" },
        explicitPathsToArguments: { "bookingPolicy.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "bookingPolicy"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function setDefaultBookingPolicy2(bookingPolicyId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingPolicyId
  });
  const reqOpts = setDefaultBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteBookingPolicy2(bookingPolicyId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingPolicyId
  });
  const reqOpts = deleteBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryBookingPolicies2() {
  const { httpClient, sideEffects } = arguments[0];
  return queryBuilder({
    func: async (payload) => {
      const reqOpts = queryBookingPolicies(payload);
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return renameKeysFromSDKRequestToRESTRequest({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({
      data
    }) => {
      const transformedData = renameKeysFromRESTResponseToSDKResponse(
        transformPaths2(data, [])
      );
      return {
        items: transformedData?.bookingPolicies,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = sdkTransformError(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countBookingPolicies2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    filter: options?.filter
  });
  const reqOpts = countBookingPolicies(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
export {
  DayOfWeek,
  DeleteStatus,
  Namespace,
  PlacementType,
  ResolutionMethod,
  SiteCreatedContext,
  SortOrder,
  State,
  WebhookIdentityType,
  countBookingPolicies2 as countBookingPolicies,
  createBookingPolicy2 as createBookingPolicy,
  deleteBookingPolicy2 as deleteBookingPolicy,
  getBookingPolicy2 as getBookingPolicy,
  getStrictestBookingPolicy2 as getStrictestBookingPolicy,
  queryBookingPolicies2 as queryBookingPolicies,
  setDefaultBookingPolicy2 as setDefaultBookingPolicy,
  updateBookingPolicy2 as updateBookingPolicy
};
//# sourceMappingURL=index.typings.mjs.map