import { NonNullablePaths } from '@wix/sdk-types';

/**
 * The `attendance` object represents the attendance information
 * for a booked session, such as:
 *
 * + Did anyone attend the session?
 * + How many people attended the session?
 *
 * The number of session `attendance` objects available depends on the booking type:
 * + Appointment bookings have 1 `attendance` object per appointment session.
 * + Class bookings have 1 `attendance` object for each session of the class. The number of sessions for a class is defined in Schedule and Sessions `schedule.capacity` property.
 * + Course bookings have an `attendance` object for each session of the course. For example, if there are 12 sessions in a course, there are 12 `attendance` objects. The number of sessions for a class is defined in Schedule and Sessions `schedule.capacity` property.
 */
interface Attendance {
    /**
     * ID of the `attendance` object.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Corresponding booking ID.
     * @format GUID
     */
    bookingId?: string | null;
    /** Corresponding session ID. */
    sessionId?: string | null;
    /** Status indicating if any participants attended the session. */
    status?: AttendanceStatusWithLiterals;
    /**
     * Total number of participants that attended the session. By default, the number
     * of attendees is set to `1`, but you can set a number to greater than `1` if multiple
     * participants attended.
     *
     * Do not set to `0` to indicate that no one attended the session. Instead, set the `status` field to `NOT_ATTENDED`.
     *
     * Default: 1
     */
    numberOfAttendees?: number;
}
declare enum AttendanceStatus {
    /** There is no available attendance information. */
    NOT_SET = "NOT_SET",
    /** At least a single participant attended the session. */
    ATTENDED = "ATTENDED",
    /** No participants attended the session. */
    NOT_ATTENDED = "NOT_ATTENDED"
}
/** @enumType */
type AttendanceStatusWithLiterals = AttendanceStatus | 'NOT_SET' | 'ATTENDED' | 'NOT_ATTENDED';
interface GetAttendanceRequest {
    /**
     * ID of the attendance object to retrieve.
     * @format GUID
     */
    attendanceId: string;
}
interface GetAttendanceResponse {
    /** Retrieved attendance. */
    attendance?: Attendance;
}
interface SetAttendanceRequest {
    /** Attendance to create or update. */
    attendance: Attendance;
    /** Information about whether to send a message to a customer after their attendance was set. */
    participantNotification?: ParticipantNotification;
}
interface ParticipantNotification {
    /**
     * Specify whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean | null;
    /**
     * Optional custom message to send to the participants about the changes to the booking.
     * @minLength 1
     * @maxLength 5000
     */
    message?: string | null;
}
interface SetAttendanceResponse {
    /** Created or updated attendance. */
    attendance?: Attendance;
}
interface AttendanceMarkedAsNotAttended {
    /** The attendance information for a booked session that you want to create or update. */
    attendance?: Attendance;
    /** Information about whether to send a message to a customer after their attendance was set. */
    participantNotification?: ParticipantNotification;
}
interface BulkSetAttendanceRequest {
    returnFullEntity?: boolean;
    /**
     * List of attendance details for booking sessions to create or update.
     * @maxSize 8
     */
    attendanceDetails?: AttendanceDetails[];
}
interface AttendanceDetails {
    /** Created or updated attendance information for a booking session. */
    attendance?: Attendance;
    /** Information about whether to send a message to the customer after their attendance was set. */
    participantNotification?: ParticipantNotification;
}
interface BulkSetAttendanceResponse {
    /**
     * List of created or updated `attendance` objects.
     * @minSize 1
     * @maxSize 8
     */
    results?: BulkAttendanceResult[];
    /** Information about the total number of successes and failures for the Bulk Set Attendance call. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkAttendanceResult {
    /** Created or updated `attendance` object. */
    item?: Attendance;
    /** Metadata for the created or updated `attendance` object. */
    itemMetadata?: ItemMetadata;
}
interface ItemMetadata {
    /** Item ID. Should always be available, unless it's impossible (for example, when failing to create an item). */
    _id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface QueryAttendanceRequest {
    /** Query options. */
    query: QueryV2;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object. See [API Query Language](https://dev.wix.com/api/rest/getting-started/api-query-language) for more information.
     *
     * For a detailed list of supported fields and operators, see [Supported Filters and Sorting](https://dev.wix.com/api/rest/wix-bookings/attendance/supported-filters).
     *
     * Max: 1 filter
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[ {"fieldName":"sortField1","order":"ASC"}, {"fieldName":"sortField2","order":"DESC"} ]`
     *
     * For details about sorting, see [Supported Filters and Sorting](https://dev.wix.com/api/rest/wix-bookings/attendance/supported-filters).
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /** Name of the field to sort by. */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
/**
 * Sort order. Use `ASC` for ascending order or `DESC` for descending order.
 *
 * Default: `ASC`.
 */
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface CursorPaging {
    /**
     * Number of `Attendance` objects to return.
     *
     * Default: `50`
     * Maximum: `1000`
     * @max 1000
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     *
     * Not relevant for the first request.
     */
    cursor?: string | null;
}
/** List of objects that contain attendance information. */
interface QueryAttendanceResponse {
    /** List of `attendance` objects that contain attendance information for a booked session. */
    attendances?: Attendance[];
    /** Metadata for the paged set of results. */
    pagingMetadata?: CursorPagingMetadata;
}
/** This is the preferred message for cursor-paging enabled services */
interface CursorPagingMetadata {
    /** Use these cursors to paginate between results. [Read more](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging). */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /** Cursor pointing to next page in the list of results. */
    next?: string | null;
    /** Cursor pointing to previous page in the list of results. */
    prev?: string | null;
}
interface CountAttendancesRequest {
    /** Filter criteria for counting attendance records. If not provided, counts all attendance records for the contact. */
    filter?: Record<string, any> | null;
}
interface CountAttendancesResponse {
    /** Total number of attendance records matching the filters. */
    count?: number;
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type GetAttendanceApplicationErrors = {
    code?: 'ATTENDANCE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type SetAttendanceApplicationErrors = {
    code?: 'SESSION_ID_NOT_PROVIDED';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type BulkSetAttendanceApplicationErrors = {
    code?: 'ATTENDANCES_MISSING_IN_REQUEST';
    description?: string;
    data?: Record<string, any>;
};
/**
 * Retrieves attendance information.
 * @param attendanceId - ID of the attendance object to retrieve.
 * @public
 * @requiredField attendanceId
 * @permissionId BOOKINGS.ATTENDANCE_READ
 * @applicableIdentity APP
 * @returns Retrieved attendance.
 * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.GetAttendance
 */
declare function getAttendance(attendanceId: string): Promise<NonNullablePaths<Attendance, `status` | `numberOfAttendees`, 2> & {
    __applicationErrorsType?: GetAttendanceApplicationErrors;
}>;
/**
 * Sets or updates attendance information for a booking session. This
 * information is stored in an `attendance` object.
 *
 * If an `attendance` object already exists for the session, it's updated.
 * Otherwise, a new object is created.
 *
 * By default, `numberOfAttendees` is set to `1`, but you can specify a higher
 * number if multiple participants attended. Do not set `numberOfAttendees` to
 * `0` to indicate no attendance, instead specify `{"status": "NOT_ATTENDED"}`.
 *
 * Validation guidelines:
 *
 * + The call succeeds for mismatches between `numberOfAttendees`
 * and `status`. For example, make sure that your code doesn't specify
 * `{"status": "NOT_ATTENDED"}` with `{"numberOfAttendees": 5}`.
 * + The API also allows `numberOfAttendees` to exceed the booking's
 * `numberOfParticipants`. Use higher values only when scenarios like
 * walk-ins justify the exception.
 * @param attendance - Attendance to create or update.
 * @public
 * @requiredField attendance
 * @requiredField attendance.bookingId
 * @requiredField attendance.status
 * @param options - Options to use when setting an attendance.
 * @permissionId BOOKINGS.ATTENDANCE_SET
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.SetAttendance
 */
declare function setAttendance(attendance: NonNullablePaths<Attendance, `bookingId` | `status`, 2>, options?: SetAttendanceOptions): Promise<NonNullablePaths<SetAttendanceResponse, `attendance.status` | `attendance.numberOfAttendees`, 3> & {
    __applicationErrorsType?: SetAttendanceApplicationErrors;
}>;
interface SetAttendanceOptions {
    /** Information about whether to send a message to a customer after their attendance was set. */
    participantNotification?: ParticipantNotification;
}
/**
 * Sets or updates attendance information for multiple booking sessions.
 *
 *
 * Refer to Set Attendance for detailed behavior of individual attendance
 * entries.
 *
 * The call fails entirely if any entry in `attendanceDetails` is missing a
 * required field.
 *
 * If attendance details are provided for a non-existent session, the call
 * succeeds for valid sessions while marking the unavailable session as a
 * failure in the response.
 * @public
 * @param options - Options to use when setting multiple attendances in bulk.
 * @permissionId BOOKINGS.ATTENDANCE_SET
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.BulkSetAttendance
 */
declare function bulkSetAttendance(options?: BulkSetAttendanceOptions): Promise<NonNullablePaths<BulkSetAttendanceResponse, `results` | `results.${number}.item.status` | `results.${number}.item.numberOfAttendees` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
    __applicationErrorsType?: BulkSetAttendanceApplicationErrors;
}>;
interface BulkSetAttendanceOptions {
    returnFullEntity?: boolean;
    /**
     * List of attendance details for booking sessions to create or update.
     * @maxSize 8
     */
    attendanceDetails?: AttendanceDetails[];
}
/**
 * Creates a query to retrieve a list of attendances.
 *
 * The `queryAttendances()` function builds a query to retrieve a list of attendances and returns a `AttendancesQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to call the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-builder/find) function.
 *
 * You can refine the query by chaining `AttendancesQueryBuilder` functions onto the query. `AttendancesQueryBuilder` functions enable you to sort, filter, and control the results that `queryAttendances()` returns.
 *
 * `queryAttendances()` uses the following `AttendancesQueryBuilder` default values that you can override:
 *
 * + `limit` is `50`.
 * + Sorted by `id` in ascending order.
 *
 * The functions that are chained to `queryAttendances()` are applied in the order they are called. For example, if you apply `ascending("status")` and then `ascending("numberOfAttendees")`, the results are sorted first by the `"status"`, and then, if there are multiple results with the same `"status"`, the items are sorted by `"numberOfAttendees"`.
 *
 * The following `AttendancesQueryBuilder` functions are supported for the `queryAttendances()` function. For a full description of the tip settings object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-result/items) property in `AttendancesQueryResult`.
 * @public
 * @permissionId BOOKINGS.ATTENDANCE_READ
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.QueryAttendance
 */
declare function queryAttendance(): AttendancesQueryBuilder;
interface QueryCursorResult {
    cursors: Cursors;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface AttendancesQueryResult extends QueryCursorResult {
    items: Attendance[];
    query: AttendancesQueryBuilder;
    next: () => Promise<AttendancesQueryResult>;
    prev: () => Promise<AttendancesQueryResult>;
}
interface AttendancesQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    eq: (propertyName: '_id' | 'bookingId' | 'sessionId' | 'status' | 'numberOfAttendees', value: any) => AttendancesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ne: (propertyName: '_id' | 'bookingId' | 'sessionId' | 'status' | 'numberOfAttendees', value: any) => AttendancesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ge: (propertyName: 'numberOfAttendees', value: any) => AttendancesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    gt: (propertyName: 'numberOfAttendees', value: any) => AttendancesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    le: (propertyName: 'numberOfAttendees', value: any) => AttendancesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    lt: (propertyName: 'numberOfAttendees', value: any) => AttendancesQueryBuilder;
    in: (propertyName: '_id' | 'bookingId' | 'sessionId' | 'status' | 'numberOfAttendees', value: any) => AttendancesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    ascending: (...propertyNames: Array<'_id' | 'bookingId' | 'sessionId' | 'status' | 'numberOfAttendees'>) => AttendancesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    descending: (...propertyNames: Array<'numberOfAttendees'>) => AttendancesQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */
    limit: (limit: number) => AttendancesQueryBuilder;
    /** @param cursor - A pointer to specific record */
    skipTo: (cursor: string) => AttendancesQueryBuilder;
    find: () => Promise<AttendancesQueryResult>;
}
/**
 * Counts attendance records by contact.
 *
 * Returns the total number of attendance records for the contact,
 * with optional filtering by attendance status and service IDs.
 *
 * Filtering options:
 * + **attendance_status**: Filter by attendance status (ATTENDED, NOT_ATTENDED)
 * + **service_id**: Filter by one or more service IDs from the bookings
 *
 * If no filters are provided, returns the total count of all attendance records
 * for the contact.
 * @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.ATTENDANCE_READ
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.CountAttendances
 */
declare function countAttendances(options?: CountAttendancesOptions): Promise<NonNullablePaths<CountAttendancesResponse, `count`, 2>>;
interface CountAttendancesOptions {
    /** Filter criteria for counting attendance records. If not provided, counts all attendance records for the contact. */
    filter?: Record<string, any> | null;
}

export { type ActionEvent, type ApplicationError, type Attendance, type AttendanceDetails, type AttendanceMarkedAsNotAttended, AttendanceStatus, type AttendanceStatusWithLiterals, type AttendancesQueryBuilder, type AttendancesQueryResult, type BulkActionMetadata, type BulkAttendanceResult, type BulkSetAttendanceApplicationErrors, type BulkSetAttendanceOptions, type BulkSetAttendanceRequest, type BulkSetAttendanceResponse, type CountAttendancesOptions, type CountAttendancesRequest, type CountAttendancesResponse, type CursorPaging, type CursorPagingMetadata, type Cursors, type DomainEvent, type DomainEventBodyOneOf, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type GetAttendanceApplicationErrors, type GetAttendanceRequest, type GetAttendanceResponse, type IdentificationData, type IdentificationDataIdOneOf, type ItemMetadata, type MessageEnvelope, type Paging, type ParticipantNotification, type QueryAttendanceRequest, type QueryAttendanceResponse, type QueryV2, type QueryV2PagingMethodOneOf, type RestoreInfo, type SetAttendanceApplicationErrors, type SetAttendanceOptions, type SetAttendanceRequest, type SetAttendanceResponse, SortOrder, type SortOrderWithLiterals, type Sorting, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, bulkSetAttendance, countAttendances, getAttendance, queryAttendance, setAttendance };
