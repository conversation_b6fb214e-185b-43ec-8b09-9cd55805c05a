import { ListBookingFeesByBookingIdsRequest as ListBookingFeesByBookingIdsRequest$1, ListBookingFeesByBookingIdsResponse as ListBookingFeesByBookingIdsResponse$1, ApplyBookingFeesToOrderRequest as ApplyBookingFeesToOrderRequest$1, ApplyBookingFeesToOrderResponse as ApplyBookingFeesToOrderResponse$1, CollectAppliedBookingFeesRequest as CollectAppliedBookingFeesRequest$1, CollectAppliedBookingFeesResponse as CollectAppliedBookingFeesResponse$1 } from './index.typings.mjs';
import '@wix/sdk-types';

/**
 * Fee for a specific booking that's calculated according to the associated booking policy snapshot ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction)). Currently, only cancellation fees, including no-show fees, are supported.
 *
 * Wix Bookings automatically applies the cancellation fee to the eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer cancels the booking themselves.
 * Cancellation fees aren't automatically applied to an eCommerce order if the business owner cancels the booking in their dashboard on behalf of the customer.
 * You can call Apply Booking Fee to Order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/apply-booking-fees-to-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/apply-booking-fees-to-order)) to manually apply booking fees to an eCommerce order.
 */
interface BookingFee {
    /**
     * Booking fee ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * ID of the booking associated with the fee.
     * @format GUID
     */
    bookingId?: string | null;
    /** Cancellation fee details. */
    cancellationFee?: CancellationFee;
    /**
     * Information about the *booking policy snapshot*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
     * according to which the fee was created.
     */
    policyDetails?: PolicyDetails;
}
interface CancellationFee {
    /** Price the customer must pay. */
    price?: Money;
    /** Status of the booking fee. */
    status?: BookingFeeStatusWithLiterals;
    /**
     * Information about what triggered the creation of the booking fee.
     * @readonly
     */
    trigger?: TriggerWithLiterals;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gte:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     * @readonly
     */
    formattedValue?: string | null;
}
declare enum BookingFeeStatus {
    /** There is no eCommerce order associated with the booking. */
    UNKNOWN_STATUS = "UNKNOWN_STATUS",
    /** The fee is informational only; the customer doesn't have to pay it. For example, it shows how much the customer would owe if they canceled the booking now. */
    PREVIEW = "PREVIEW",
    /** The booking fee hasn't been added to the eCommerce order yet. */
    NOT_YET_APPLIED_TO_ORDER = "NOT_YET_APPLIED_TO_ORDER",
    /** The booking fee has been added to the eCommerce order. The customer may not have paid it yet. */
    APPLIED_TO_ORDER = "APPLIED_TO_ORDER"
}
/** @enumType */
type BookingFeeStatusWithLiterals = BookingFeeStatus | 'UNKNOWN_STATUS' | 'PREVIEW' | 'NOT_YET_APPLIED_TO_ORDER' | 'APPLIED_TO_ORDER';
/** The domain event that triggered the booking fee calculation. */
declare enum Trigger {
    /** There is no information about what triggered the creation of the booking fee. */
    UNKNOWN_TRIGGER = "UNKNOWN_TRIGGER",
    /** The booking fee was created because the customer didn't show up to the booking or canceled after the expiration of the last cancellation window. */
    NOT_ATTENDED = "NOT_ATTENDED",
    /** The booking fee was created because the customer canceled the booking before the expiration of the last cancellation window. */
    BOOKING_CANCELED = "BOOKING_CANCELED"
}
/** @enumType */
type TriggerWithLiterals = Trigger | 'UNKNOWN_TRIGGER' | 'NOT_ATTENDED' | 'BOOKING_CANCELED';
interface PolicyDetails {
    /**
     * ID of the booking policy.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Description of the booking policy.
     * @maxLength 2500
     */
    description?: string | null;
    /**
     * Translated description of the booking policy according to the buyer language of the eCommerce order.
     * @maxLength 2500
     */
    descriptionTranslated?: string | null;
}
interface EcomOrderInfo {
    /**
     * Order ID.
     * @format GUID
     */
    orderId?: string | null;
    /** Additional fee price. */
    additionalFeePrice?: Price;
    /**
     * The eCommerce additional fee id that was created on the order.
     * @format GUID
     */
    additionalFeeId?: string | null;
}
interface Price {
    /**
     * Amount.
     * @decimalValue options { gte:0, lte:1000000000000000, maxScale:2 }
     */
    amount?: string;
    /**
     * Amount formatted with currency symbol.
     * @readonly
     * @maxLength 100
     */
    formattedAmount?: string;
}
interface BusinessNotification {
    /**
     * Whether to notify the business about changes made to the booking fees.
     * Default is false.
     */
    notifyBusiness?: boolean | null;
    /**
     * Optional custom message to send.
     * @minLength 1
     * @maxLength 5000
     */
    message?: string | null;
}
interface ListBookingFeesByBookingIdsRequest {
    /**
     * IDs of the bookings to retrieve booking fees for.
     * @format GUID
     * @maxSize 5
     */
    bookingIds?: string[];
    /**
     * IDs of the multi service bookings to retrieve booking fees for.
     * @format GUID
     * @maxSize 1
     */
    multiServiceBookingIds?: string[];
}
interface ListBookingFeesByBookingIdsResponse {
    /** List of retrieved booking fees. */
    bookingFees?: BookingFee[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Cursor strings that point to the next page, previous page, or both. */
    cursors?: Cursors;
    /**
     * Whether there are more pages to retrieve following the current page.
     *
     * + `true`: Another page of results can be retrieved.
     * + `false`: This is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface ApplyBookingFeesToOrderRequest {
    /**
     * IDs of the bookings for which to apply booking fees to an eCommerce order.
     * @format GUID
     * @minSize 1
     * @maxSize 5
     */
    bookingIds: string[] | null;
    /**
     * Custom price override for the additional fee that's added to the eCommerce
     * order. The override mustn't exceed the sum of all booking fees. You must have
     * the `OVERRIDE_BOOKING_FEE_PRICE` permission to use this property.
     */
    priceOverride?: Money;
    /**
     * Information about the message to the business and whether to send it if the
     * booking fee application to the eCommerce order fails.
     */
    businessNotification?: BusinessNotification;
}
interface ApplyBookingFeesToOrderResponse {
    /**
     * Booking fees that were applied as a single additional fee to the eCommerce
     * order.
     */
    bookingFees?: BookingFee[];
    /** Information about the eCommerce order to which the booking fees are applied. */
    ecomOrderInfo?: EcomOrderInfo;
}
interface CollectAppliedBookingFeesRequest {
    /**
     * ID of the eCommerce order that includes the booking fees as a single
     * `additionalFee`.
     * @format GUID
     */
    orderId: string | null;
    /**
     * ID of the additional fee that's related to all given booking fees.
     * @format GUID
     */
    additionalFeeId: string | null;
    /**
     * Information about whether to notify the business about failing to collect the
     * booking fees from the customer and the message to send.
     */
    businessNotification?: BusinessNotification;
}
interface CollectAppliedBookingFeesResponse {
    /** Collected amount. */
    collectedPrice?: Price;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function listBookingFeesByBookingIds(): __PublicMethodMetaInfo<'GET', {}, ListBookingFeesByBookingIdsRequest$1, ListBookingFeesByBookingIdsRequest, ListBookingFeesByBookingIdsResponse$1, ListBookingFeesByBookingIdsResponse>;
declare function applyBookingFeesToOrder(): __PublicMethodMetaInfo<'POST', {}, ApplyBookingFeesToOrderRequest$1, ApplyBookingFeesToOrderRequest, ApplyBookingFeesToOrderResponse$1, ApplyBookingFeesToOrderResponse>;
declare function collectAppliedBookingFees(): __PublicMethodMetaInfo<'POST', {}, CollectAppliedBookingFeesRequest$1, CollectAppliedBookingFeesRequest, CollectAppliedBookingFeesResponse$1, CollectAppliedBookingFeesResponse>;

export { type __PublicMethodMetaInfo, applyBookingFeesToOrder, collectAppliedBookingFees, listBookingFeesByBookingIds };
