"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  bulkCreateResources: () => bulkCreateResources2,
  bulkDeleteResources: () => bulkDeleteResources2,
  bulkUpdateResources: () => bulkUpdateResources2,
  countResources: () => countResources2,
  createResource: () => createResource2,
  deleteResource: () => deleteResource2,
  getResource: () => getResource2,
  queryResources: () => queryResources2,
  searchResources: () => searchResources2,
  updateResource: () => updateResource2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-resources-v2-resource-resources.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_float = require("@wix/sdk-runtime/transformations/float");
var import_float2 = require("@wix/sdk-runtime/transformations/float");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsResourcesV2ResourcesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/bookings/v2/resources",
        destPath: "/v2/resources"
      },
      {
        srcPath: "/_api/bookings/v2/resources",
        destPath: "/v2/resources"
      },
      {
        srcPath: "/v2/bulk/resources",
        destPath: "/v2/bulk/resources"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/resources-2",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/resources",
        destPath: "/v2/resources"
      },
      {
        srcPath: "/bookings/v2/bulk/resources",
        destPath: "/v2/bulk/resources"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/resources-2",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_resources";
function createResource(payload) {
  function __createResource({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resource.createdDate" },
          { path: "resource.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.CreateResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resource.createdDate" },
            { path: "resource.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createResource;
}
function bulkCreateResources(payload) {
  function __bulkCreateResources({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resources.createdDate" },
          { path: "resources.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.BulkCreateResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/bulk/resources/create",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkCreateResources;
}
function getResource(payload) {
  function __getResource({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "GET",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.GetResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/{resourceId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resource.createdDate" },
            { path: "resource.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getResource;
}
function updateResource(payload) {
  function __updateResource({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resource.createdDate" },
          { path: "resource.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "PATCH",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.UpdateResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/{resource.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resource.createdDate" },
            { path: "resource.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateResource;
}
function bulkUpdateResources(payload) {
  function __bulkUpdateResources({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "resources.fieldMask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resources.resource.createdDate" },
          { path: "resources.resource.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.BulkUpdateResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/bulk/resources/update",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkUpdateResources;
}
function deleteResource(payload) {
  function __deleteResource({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "DELETE",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.DeleteResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/{resourceId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteResource;
}
function bulkDeleteResources(payload) {
  function __bulkDeleteResources({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.BulkDeleteResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/bulk/resources/delete",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkDeleteResources;
}
function searchResources(payload) {
  function __searchResources({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "search.aggregations.range.buckets.from" },
          { path: "search.aggregations.range.buckets.to" },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.from"
          },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.to"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.SearchResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/search",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resources.createdDate" },
            { path: "resources.updatedDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "aggregationData.results.ranges.results.from" },
            { path: "aggregationData.results.ranges.results.to" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from"
            },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.from"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.to"
            },
            { path: "aggregationData.results.scalar.value" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.scalar.value"
            },
            {
              path: "aggregationData.results.nested.results.results.*.scalar.value"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __searchResources;
}
function queryResources(payload) {
  function __queryResources({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.QueryResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resources.createdDate" },
            { path: "resources.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryResources;
}
function countResources(payload) {
  function __countResources({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.CountResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countResources;
}

// src/bookings-resources-v2-resource-resources.meta.ts
function createResource2() {
  const payload = {};
  const getRequestOptions = createResource(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkCreateResources2() {
  const payload = {};
  const getRequestOptions = bulkCreateResources(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/resources/create",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getResource2() {
  const payload = { resourceId: ":resourceId" };
  const getRequestOptions = getResource(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/resources/{resourceId}",
    pathParams: { resourceId: "resourceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateResource2() {
  const payload = { resource: { id: ":resourceId" } };
  const getRequestOptions = updateResource(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v2/resources/{resource.id}",
    pathParams: { resourceId: "resourceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkUpdateResources2() {
  const payload = {};
  const getRequestOptions = bulkUpdateResources(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/resources/update",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteResource2() {
  const payload = { resourceId: ":resourceId" };
  const getRequestOptions = deleteResource(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v2/resources/{resourceId}",
    pathParams: { resourceId: "resourceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkDeleteResources2() {
  const payload = {};
  const getRequestOptions = bulkDeleteResources(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/resources/delete",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function searchResources2() {
  const payload = {};
  const getRequestOptions = searchResources(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/search",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryResources2() {
  const payload = {};
  const getRequestOptions = queryResources(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countResources2() {
  const payload = {};
  const getRequestOptions = countResources(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  bulkCreateResources,
  bulkDeleteResources,
  bulkUpdateResources,
  countResources,
  createResource,
  deleteResource,
  getResource,
  queryResources,
  searchResources,
  updateResource
});
//# sourceMappingURL=meta.js.map