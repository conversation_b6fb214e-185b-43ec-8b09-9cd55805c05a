import { H as CreateResourceRequest$1, J as CreateResourceResponse$1, K as BulkCreateResourcesRequest$1, a as BulkCreateResourcesResponse$1, X as GetResourceRequest$1, Y as GetResourceResponse$1, a8 as UpdateResourceRequest$1, a9 as UpdateResourceResponse$1, aa as BulkUpdateResourcesRequest$1, e as BulkUpdateResourcesResponse$1, ab as DeleteResourceRequest$1, ac as DeleteResourceResponse$1, ad as BulkDeleteResourcesRequest$1, g as BulkDeleteResourcesResponse$1, ae as SearchResourcesRequest$1, S as SearchResourcesResponse$1, aS as QueryResourcesRequest$1, aV as QueryResourcesResponse$1, aW as CountResourcesRequest$1, k as CountResourcesResponse$1 } from './bookings-resources-v2-resource-resources.universal-WjeiBRqP.js';
import '@wix/sdk-types';

/**
 * A resource represents an entity, such as a room or a staff member, that can be
 * scheduled for use in a *booking*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).
 * The availability of a resource is tracked to ensure that it
 * can be allocated at a requested time slot and to prevent double bookings.
 */
interface Resource extends ResourceCompositionDetailsOneOf {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Revision number, which increments by 1 each time the resource is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the resource.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was created.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was last updated.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Name of the resource.
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
    /**
     * ID of the *resource type*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     *
     * Once a type has been set it can't be modified. You can create a resource
     * without specifying a type. However, customers can't book such resources.
     * @format GUID
     * @immutable
     */
    typeId?: string | null;
    /**
     * Information about the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * specifying the working hours and locations of the resource.
     *
     * Resources without a working hour schedule are available 24/7 at the locations
     * specified in the field `locationOptions`.
     * When both `workingHoursSchedules` and `locationOptions` are set,
     * `workingHoursSchedules` takes precedence.
     */
    workingHoursSchedules?: WorkingHoursSchedules;
    /** Information about the location where the resource is available. */
    locationOptions?: LocationOptions;
    /**
     * Schedule containing the *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * for which the resource has been booked.
     * @readonly
     */
    eventsSchedule?: EventsSchedule;
    /** Extensions enabling users to save custom data related to the resource. */
    extendedFields?: ExtendedFields;
}
/** @oneof */
interface ResourceCompositionDetailsOneOf {
}
interface WorkingHoursSchedule {
    /**
     * ID of the working hour *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     * @format GUID
     */
    scheduleId?: string | null;
    /**
     * Whether the schedule is shared by multiple resources or unique for this resource.
     *
     * Default: `false`
     * @readonly
     */
    shared?: boolean | null;
}
interface LocationOptions {
    /**
     * Whether the resource is available in all *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in all business locations.
     * - `false`: The resource is available only in specific locations.
     *
     * Default: `false`
     */
    availableInAllLocations?: boolean | null;
    /** Details of resource availability in specific locations. */
    specificLocationOptions?: SpecificLocation;
}
interface SpecificLocation {
    /**
     * Whether the resource is available in *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in business locations.
     * - `false`: The resource isn't available in business locations.
     *
     * Default: `false`
     */
    availableInBusinessLocations?: boolean | null;
    /**
     * Information about the business locations where the resource is available.
     * Not returned, if the resource is available in either all business locations
     * or in no business location.
     * You can specify up to 100 business locations.
     * @maxSize 100
     */
    businessLocations?: BusinessLocation[];
}
interface BusinessLocation {
    /**
     * ID of the business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * @format GUID
     */
    locationId?: string | null;
}
interface WorkingHoursSchedules {
    /**
     * Schedules specifying the working hours of the resource. Currently, only a single schedule is supported.
     * @maxSize 1
     */
    values?: WorkingHoursSchedule[];
}
interface EventsSchedule {
    /**
     * ID of the event *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     * @format GUID
     */
    scheduleId?: string | null;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateResourceRequest {
    /** Resource to create. */
    resource: Resource;
}
interface CreateResourceResponse {
    /** Created resource. */
    resource?: Resource;
}
interface BulkCreateResourcesRequest {
    /**
     * Resources to create.
     * @maxSize 50
     */
    resources: Resource[];
    /**
     * Whether to include the created resources in the response.
     *
     * Default: `false`
     */
    returnEntity?: boolean;
}
interface BulkCreateResourcesResponse {
    /** The result of each resource creation. */
    results?: BulkResourceResult[];
    /** Create statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkResourceResult {
    /** Item metadata. */
    itemMetadata?: ItemMetadata;
    /** The resulting resource after the bulk operation. */
    item?: Resource;
}
interface ItemMetadata {
    /**
     * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).
     * @format GUID
     */
    id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface GetResourceRequest {
    /**
     * ID of the resource to retrieve.
     * @format GUID
     */
    resourceId: string;
}
interface GetResourceResponse {
    /** Retrieved resource. */
    resource?: Resource;
}
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface CursorPagingMetadata {
    /**
     * Number of items returned in the current response page.
     *
     * This count reflects the actual number of items in the current result set,
     * which may be less than the requested limit if fewer items are available.
     */
    count?: number | null;
    /**
     * Navigation cursors for moving between result pages.
     *
     * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor
     * to retrieve subsequent pages and `prev` cursor to go back to previous pages.
     * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).
     */
    cursors?: Cursors;
    /**
     * Indicates whether additional results are available beyond the current page.
     *
     * - `true`: More results exist and can be retrieved using the `next` cursor.
     * - `false`: This is the final page of results.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor token for retrieving the next page of results.
     *
     * Use this token in subsequent requests to continue pagination forward.
     * Value is `null` when on the last page of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor token for retrieving the previous page of results.
     *
     * Use this token to navigate backwards through result pages.
     * Value is `null` when on the first page of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface UpdateResourceRequest {
    /** Resource to update. */
    resource: Resource;
}
interface UpdateResourceResponse {
    /** Updated resource. */
    resource?: Resource;
}
interface BulkUpdateResourcesRequest {
    /**
     * Resources to update.
     * @minSize 1
     * @maxSize 100
     */
    resources: MaskedResource[];
    /**
     * Whether to include the updated resources in the response.
     *
     * Default: `false`
     */
    returnEntity?: boolean;
}
interface MaskedResource {
    /** Resource to update. */
    resource?: Resource;
    /** Explicit list of fields to update. */
    fieldMask?: string[];
}
interface BulkUpdateResourcesResponse {
    /** The result of each resource update. */
    results?: BulkResourceResult[];
    /** Update statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface DeleteResourceRequest {
    /**
     * ID of the resource to delete.
     * @format GUID
     */
    resourceId: string;
}
interface DeleteResourceResponse {
}
interface BulkDeleteResourcesRequest {
    /**
     * IDs of the resources to delete.
     * @format GUID
     * @maxSize 50
     */
    ids: string[];
}
interface BulkDeleteResourcesResponse {
    /** The result of each resource removal. */
    results?: BulkResourceResult[];
    /** Delete statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface SearchResourcesRequest {
    /**
     * Search criteria including filter, sort, aggregations, and paging options.
     *
     * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.
     */
    search?: CursorSearch;
}
interface CursorSearch extends CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object for narrowing search results. For example, to return only resources available at specific business locations: `"filter": {"single_resource.location_options.specific_location_options.business_locations.location_id": {"$in": ["location-id-1", "location-id-2"]}}`.
     *
     * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).
     */
    filter?: Record<string, any> | null;
    /**
     * Array of sort objects specifying result order. For example, to sort by resource name in ascending order: `"sort": [{"fieldName": "name", "order": "ASC"}]`.
     *
     * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).
     * @maxSize 10
     */
    sort?: Sorting[];
    /**
     * Aggregations for grouping data into categories (facets) and providing summaries for each category.
     * For example, use aggregations to categorize search results by resource type, management type, or location availability.
     * @maxSize 10
     */
    aggregations?: Aggregation[];
    /** Free text to match in searchable fields. */
    search?: SearchDetails;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.
     *
     * Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).
     * @maxLength 50
     */
    timeZone?: string | null;
}
/** @oneof */
interface CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Aggregation extends AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
    /**
     * Aggregation name, returned in `aggregations.results.name`.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: AggregationTypeWithLiterals;
    /**
     * Field to aggregate by. Use dot notation to specify a JSON path. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
    /**
     * Deprecated. Use `nested` instead.
     * @deprecated Deprecated. Use `nested` instead.
     * @replacedBy kind.nested
     * @targetRemovalDate 2024-03-30
     */
    groupBy?: GroupByAggregation;
}
/** @oneof */
interface AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
}
interface RangeBucket {
    /** Inclusive lower bound of the range. Required if `to` isn't specified. */
    from?: number | null;
    /** Exclusive upper bound of the range. Required if `from` isn't specified. */
    to?: number | null;
}
declare enum SortType {
    /** Number of matches in the results. */
    COUNT = "COUNT",
    /** Alphabetically by the field value. */
    VALUE = "VALUE"
}
/** @enumType */
type SortTypeWithLiterals = SortType | 'COUNT' | 'VALUE';
declare enum SortDirection {
    /** Descending order. */
    DESC = "DESC",
    /** Ascending order. */
    ASC = "ASC"
}
/** @enumType */
type SortDirectionWithLiterals = SortDirection | 'DESC' | 'ASC';
declare enum MissingValues {
    /** Exclude missing values from the aggregation results. */
    EXCLUDE = "EXCLUDE",
    /** Include missing values in the aggregation results. */
    INCLUDE = "INCLUDE"
}
/** @enumType */
type MissingValuesWithLiterals = MissingValues | 'EXCLUDE' | 'INCLUDE';
interface IncludeMissingValuesOptions {
    /**
     * Custom bucket name for missing values.
     *
     * Default values:
     * - string: `N/A`
     * - int: `0`
     * - bool: `false`
     * @maxLength 20
     */
    addToBucket?: string;
}
declare enum ScalarType {
    UNKNOWN_SCALAR_TYPE = "UNKNOWN_SCALAR_TYPE",
    /** Total number of distinct values. */
    COUNT_DISTINCT = "COUNT_DISTINCT",
    /** Minimum value. */
    MIN = "MIN",
    /** Maximum value. */
    MAX = "MAX"
}
/** @enumType */
type ScalarTypeWithLiterals = ScalarType | 'UNKNOWN_SCALAR_TYPE' | 'COUNT_DISTINCT' | 'MIN' | 'MAX';
interface ValueAggregation extends ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
    /** Whether to sort by number of matches or value of the field. */
    sortType?: SortTypeWithLiterals;
    /** Whether to sort in ascending or descending order. */
    sortDirection?: SortDirectionWithLiterals;
    /**
     * Number of aggregations to return.
     *
     * Min: `1`
     * Max: `250`
     * Default: `10`
     */
    limit?: number | null;
    /**
     * Whether missing values should be included or excluded from the aggregation results.
     *
     * Default: `EXCLUDE`
     */
    missingValues?: MissingValuesWithLiterals;
}
/** @oneof */
interface ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
}
declare enum NestedAggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM"
}
/** @enumType */
type NestedAggregationTypeWithLiterals = NestedAggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM';
interface RangeAggregation {
    /**
     * List of range buckets defining the ranges for aggregation. During aggregation, each entity is placed in the first bucket where its value falls within the specified range bounds.
     * @maxSize 50
     */
    buckets?: RangeBucket[];
}
interface ScalarAggregation {
    /** Operator for the scalar aggregation, for example `COUNT_DISTINCT`, `MIN`, `MAX`. */
    type?: ScalarTypeWithLiterals;
}
interface DateHistogramAggregation {
    /** Time interval for date histogram aggregation, for example `DAY`, `HOUR`, `MONTH`. */
    interval?: IntervalWithLiterals;
}
declare enum Interval {
    UNKNOWN_INTERVAL = "UNKNOWN_INTERVAL",
    /** Yearly interval */
    YEAR = "YEAR",
    /** Monthly interval */
    MONTH = "MONTH",
    /** Weekly interval */
    WEEK = "WEEK",
    /** Daily interval */
    DAY = "DAY",
    /** Hourly interval */
    HOUR = "HOUR",
    /** Minute interval */
    MINUTE = "MINUTE",
    /** Second interval */
    SECOND = "SECOND"
}
/** @enumType */
type IntervalWithLiterals = Interval | 'UNKNOWN_INTERVAL' | 'YEAR' | 'MONTH' | 'WEEK' | 'DAY' | 'HOUR' | 'MINUTE' | 'SECOND';
interface NestedAggregationItem extends NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: NestedAggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
}
declare enum AggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM",
    /** Flattened list of aggregations, where each aggregation is nested within previous 1. */
    NESTED = "NESTED"
}
/** @enumType */
type AggregationTypeWithLiterals = AggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM' | 'NESTED';
/** Nested aggregation for multi-level faceted search. Allows exploring large amounts of data through multiple levels of categorization, where each subsequent aggregation is nested within the previous aggregation to create hierarchical data summaries. */
interface NestedAggregation {
    /**
     * Flattened list of aggregations where each next aggregation is nested within the previous 1.
     * @minSize 2
     * @maxSize 3
     */
    nestedAggregations?: NestedAggregationItem[];
}
interface GroupByAggregation extends GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
}
interface SearchDetails {
    /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */
    mode?: ModeWithLiterals;
    /**
     * Search term or expression.
     * @maxLength 100
     */
    expression?: string | null;
    /**
     * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `locationOptions.availableInAllLocations`.
     * @maxLength 200
     * @maxSize 20
     */
    fields?: string[];
    /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */
    fuzzy?: boolean;
}
declare enum Mode {
    /** At least 1 of the search terms must be present. */
    OR = "OR",
    /** All search terms must be present. */
    AND = "AND"
}
/** @enumType */
type ModeWithLiterals = Mode | 'OR' | 'AND';
interface SearchResourcesResponse {
    /**
     * Retrieved resources that match the search criteria specified in the request.
     *
     * Each resource includes all available information such as name, type, management details,
     * location options, working hour schedules, and availability configurations.
     */
    resources?: Resource[];
    /**
     * Cursor-based paging metadata for navigating through search results.
     *
     * Contains navigation information including the current page cursor, whether additional
     * results are available, and result counts. Use the `next` cursor to fetch subsequent pages.
     */
    pagingMetadata?: CursorPagingMetadata;
    /**
     * Aggregation results derived from the aggregations specified in the search request.
     *
     * Provides analytical summaries such as resource counts by type, location distribution,
     * management type statistics, or custom groupings. Only populated when aggregations
     * are included in the search criteria.
     */
    aggregationData?: AggregationData;
}
interface AggregationData {
    /**
     * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.
     * @maxSize 10000
     */
    results?: AggregationResults[];
}
interface ValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 100
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number;
}
interface RangeAggregationResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number;
}
interface NestedAggregationResults extends NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /**
     * User-defined name of aggregation, matches the one specified in request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that matches result. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
}
interface ValueResults {
    /**
     * Array of value aggregation results, each containing a field value and the count of entities with that value.
     * @maxSize 250
     */
    results?: ValueAggregationResult[];
}
interface RangeResults {
    /**
     * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.
     * @maxSize 50
     */
    results?: RangeAggregationResult[];
}
interface AggregationResultsScalarResult {
    /** Type of scalar aggregation. */
    type?: ScalarTypeWithLiterals;
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Nested aggregations result data. */
    nestedResults?: NestedAggregationResults;
}
interface ValueResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number | null;
}
interface RangeResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number | null;
}
interface ScalarResult {
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedResultValue extends NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
/** @oneof */
interface NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
interface Results {
    /** Map of nested aggregation results, keyed by aggregation name. */
    results?: Record<string, NestedResultValue>;
}
interface DateHistogramResult {
    /**
     * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * @maxLength 100
     */
    value?: string;
    /** Count of documents in the bucket. */
    count?: number;
}
interface GroupByValueResults {
    /**
     * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.
     * @maxSize 1000
     */
    results?: NestedValueAggregationResult[];
}
interface DateHistogramResults {
    /**
     * Array of date histogram aggregation results, each containing a date bucket and its count.
     * @maxSize 200
     */
    results?: DateHistogramResult[];
}
/**
 * Results of `NESTED` aggregation type in a flattened form
 * Aggregations in resulting array are keyed by requested aggregation `name`.
 */
interface NestedResults {
    /**
     * Array of nested aggregation result groups, each containing multiple aggregation results.
     * @maxSize 1000
     */
    results?: Results[];
}
interface AggregationResults extends AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
    /**
     * User-defined name of aggregation as derived from search request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that must match specified kind as derived from search request. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
}
interface QueryResourcesRequest {
    /**
     * Query for retrieving resources. Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    query?: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 5
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface QueryResourcesResponse {
    /** Retrieved resources. */
    resources?: Resource[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CountResourcesRequest {
    /**
     * Filter to base the count on. Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
    /**
     * Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.
     * @deprecated Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.
     * @targetRemovalDate 2024-05-01
     */
    search?: SearchDetails;
}
interface CountResourcesResponse {
    /** Total number of resources matching the filter. */
    count?: number;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createResource(): __PublicMethodMetaInfo<'POST', {}, CreateResourceRequest$1, CreateResourceRequest, CreateResourceResponse$1, CreateResourceResponse>;
declare function bulkCreateResources(): __PublicMethodMetaInfo<'POST', {}, BulkCreateResourcesRequest$1, BulkCreateResourcesRequest, BulkCreateResourcesResponse$1, BulkCreateResourcesResponse>;
declare function getResource(): __PublicMethodMetaInfo<'GET', {
    resourceId: string;
}, GetResourceRequest$1, GetResourceRequest, GetResourceResponse$1, GetResourceResponse>;
declare function updateResource(): __PublicMethodMetaInfo<'PATCH', {
    resourceId: string;
}, UpdateResourceRequest$1, UpdateResourceRequest, UpdateResourceResponse$1, UpdateResourceResponse>;
declare function bulkUpdateResources(): __PublicMethodMetaInfo<'POST', {}, BulkUpdateResourcesRequest$1, BulkUpdateResourcesRequest, BulkUpdateResourcesResponse$1, BulkUpdateResourcesResponse>;
declare function deleteResource(): __PublicMethodMetaInfo<'DELETE', {
    resourceId: string;
}, DeleteResourceRequest$1, DeleteResourceRequest, DeleteResourceResponse$1, DeleteResourceResponse>;
declare function bulkDeleteResources(): __PublicMethodMetaInfo<'POST', {}, BulkDeleteResourcesRequest$1, BulkDeleteResourcesRequest, BulkDeleteResourcesResponse$1, BulkDeleteResourcesResponse>;
declare function searchResources(): __PublicMethodMetaInfo<'POST', {}, SearchResourcesRequest$1, SearchResourcesRequest, SearchResourcesResponse$1, SearchResourcesResponse>;
declare function queryResources(): __PublicMethodMetaInfo<'POST', {}, QueryResourcesRequest$1, QueryResourcesRequest, QueryResourcesResponse$1, QueryResourcesResponse>;
declare function countResources(): __PublicMethodMetaInfo<'POST', {}, CountResourcesRequest$1, CountResourcesRequest, CountResourcesResponse$1, CountResourcesResponse>;

export { type __PublicMethodMetaInfo, bulkCreateResources, bulkDeleteResources, bulkUpdateResources, countResources, createResource, deleteResource, getResource, queryResources, searchResources, updateResource };
