import { NonNullablePaths } from '@wix/sdk-types';

/**
 * A time slot represents a specific time period when a service is available for booking.
 * It provides all the information needed to display availability to customers, including whether customers can actually book it, the remaining capacity, and which staff members or resources are available.
 * Available time slots may not always be bookable due to service booking policies. When this occurs, the time slot includes information about the specific booking restrictions.
 *
 * For multi-service bookings, the top-level time slot acts as a container spanning the entire service sequence (from the start of the first service to the end of the last service). Individual service details are provided in nested time slots.
 */
interface TimeSlot {
    /**
     * Service ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).
     *
     * Available only for single-service bookings. For multi-service bookings, this field is empty and individual service IDs are provided in `nestedTimeSlots`.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     *
     * For multi-service bookings, this represents the start time of the first service in the sequence.
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string | null;
    /**
     * Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T14:00:00`.
     *
     * For multi-service bookings, this represents the end time of the last service in the sequence.
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string | null;
    /**
     * Whether customers can book the slot according to the service's booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).
     *
     * For multi-service bookings, this is `true` only when all services in the sequence comply with their respective booking policies.
     */
    bookable?: boolean | null;
    /** Information about where the business provides the service to the customer. */
    location?: Location;
    /**
     * Information about the event ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)) related to the slot.
     * Available only for classes. Not available for appointment-based services and courses.
     */
    eventInfo?: EventInfo;
    /**
     * Total number of spots for the slot.
     *
     * For multi-service bookings, this is always `1` because customers book the entire service sequence as a single unit.
     * @min 1
     * @max 1000
     */
    totalCapacity?: number | null;
    /**
     * Remaining number of spots for the slot.
     * - For appointment bookings: Either `1` (available) or `0` (unavailable).
     * - For classes: Total capacity minus booked spots. Doesn't account for waitlist reservations. For classes with waitlists, use `bookableCapacity` to get the actual number of spots customers can book.
     * - For courses: Total capacity minus booked spots. Courses don't currently support waitlists.
     * @max 1000
     */
    remainingCapacity?: number | null;
    /**
     * Number of spots that customers can book for the slot.
     * Calculated as the remaining capacity minus the spots reserved for the waitlist.
     * If the service has no waitlist, identical to `remainingCapacity`.
     *
     * For multi-service bookings, this is either `1` (sequence can be booked) or `0` (sequence cannot be booked).
     */
    bookableCapacity?: number | null;
    /**
     * Information about booking policy violations for the slot.
     *
     * For multi-service bookings, this aggregates violations from all services in the sequence.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * List of resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) available during the time slot.
     *
     * Available only for single-service bookings. For multi-service bookings, resource information is provided in `nestedTimeSlots`.
     */
    availableResources?: AvailableResources[];
    /**
     * Nested time slots for multi-service bookings.
     * Each nested slot represents 1 service in the sequence, ordered according to the service sequence specified in the request.
     *
     * Available only for multi-service bookings. Empty for single-service bookings.
     * @maxSize 8
     */
    nestedTimeSlots?: NestedTimeSlot[];
    /** Information about why customers can't book the time slot. */
    nonBookableReasons?: NonBookableReasons;
    /**
     * Schedule ID associated with this time slot.
     * Same as the service's schedule ID.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface Location {
    /**
     * Location ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * Available only for business locations.
     * @format GUID
     */
    _id?: string | null;
    /** Location name. */
    name?: string | null;
    /** Formatted location address. */
    formattedAddress?: string | null;
    /** Location type. */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNKNOWN_LOCATION_TYPE = "UNKNOWN_LOCATION_TYPE",
    /** A business location, either the default business address, or locations defined for the business by the Business Info. */
    BUSINESS = "BUSINESS",
    /** The location is unique to this service and isn't defined as 1 of the business locations. */
    CUSTOM = "CUSTOM",
    /** The location can be determined by the customer and isn't set up beforehand. */
    CUSTOMER = "CUSTOMER"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNKNOWN_LOCATION_TYPE' | 'BUSINESS' | 'CUSTOM' | 'CUSTOMER';
/** Available for event based slots, and not for availability based slots */
interface EventInfo {
    /**
     * Event ID.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
    /** Information about the event's waitlist. Available only if the service has a waitlist. */
    waitingList?: WaitingList;
}
interface WaitingList {
    /**
     * Total number of spots in the waitlist.
     * @min 1
     */
    totalCapacity?: number | null;
    /**
     * Number of remaining spots in the waitlist.
     * For example, an event with a waitlist for 10 people and 3 registrants, results in a remaining capacity of `7`.
     */
    remainingCapacity?: number | null;
}
interface BookingPolicyViolations {
    /** Whether it's too early for customers to book the slot. */
    tooEarlyToBook?: boolean | null;
    /** Earliest time for booking the slot in `YYYY-MM-DDThh:mm:ss.sssZ` format. */
    earliestBookingDate?: Date | null;
    /** Whether it's too late for customers to book the slot. */
    tooLateToBook?: boolean | null;
    /** Whether customers can book the service online. */
    bookOnlineDisabled?: boolean | null;
}
interface AvailableResources {
    /**
     * Resource type ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     * @format GUID
     */
    resourceTypeId?: string | null;
    /**
     * Details about resources available during the time slot.
     *
     * Behavior varies by method:
     *
     * List methods (List Availability Time Slots and List Multi Service Availability Time Slots):
     * - Empty by default.
     * - Up to 10 resources when specifying `includeResourceTypeIds` or `resourceIds` in the request.
     *
     * Get methods (Get Availability Time Slots and Get Multi Service Availability Time Slots):
     * - All resources by default.
     * - Filtered resources when specifying `includeResourceTypeIds` or `resourceIds` in the request.
     */
    resources?: Resource[];
    /**
     * Whether there are more available resources for the slot than those listed in `resources`.
     * @readonly
     */
    hasMoreAvailableResources?: boolean | null;
}
interface Resource {
    /**
     * Resource ID.
     * @format GUID
     */
    _id?: string;
    /**
     * Resource name.
     * @minLength 1
     */
    name?: string | null;
}
interface NestedTimeSlot {
    /**
     * Service ID of the nested time slot.
     * @format GUID
     */
    serviceId?: string;
    /**
     * Local start date of the nested time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string;
    /**
     * Local end date of the nested time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string;
    /**
     * List of resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) available during the nested time slot.
     * @maxSize 10
     */
    availableResources?: AvailableResources[];
    /**
     * The schedule ID associated with this nested time slot.
     * Same as the service's schedule ID.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface NonBookableReasons {
    /** Whether the slot is fully booked with no remaining capacity. */
    noRemainingCapacity?: boolean | null;
    /** Whether booking the slot violates any of the service's booking policies. */
    violatesBookingPolicy?: boolean | null;
    /** Whether the slot is reserved for the waitlist. A new customer can't book the reserved slot. */
    reservedForWaitingList?: boolean | null;
}
interface ListMultiServiceAvailabilityTimeSlotsRequest {
    /**
     * Services for which the multi-service time slots are returned.
     * Each service can include its own resource filters.
     *
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Min: 2 services
     * Max: 8 services
     * @maxSize 8
     */
    services?: Service[];
    /**
     * Lower boundary for `localStartDate` in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate`.
     *
     * Required unless you specify `cursorPaging.cursor`.
     * @format LOCAL_DATE_TIME
     */
    fromLocalDate?: string | null;
    /**
     * Upper boundary for `localToDate` in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * Each returned time slot has a `localEndDate` between `fromLocalDate` and `toLocalDate`.
     *
     * Required unless you specify `cursorPaging.cursor`.
     * @format LOCAL_DATE_TIME
     */
    toLocalDate?: string | null;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values. For example, `America/New_York` or `UTC`.
     *
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Location for which the multi-service time slots are returned. If you filter by `{"type": "BUSINESS"}`, you must also specify a location ID. Filters for `location.name` and `location.formattedAddress` are ignored.
     *
     * Required unless you specify `cursorPaging.cursor`.
     */
    location?: Location;
    /**
     * Whether the time slot is bookable according to all services' booking policies.
     * If not specified, returns both bookable and non-bookable time slots.
     */
    bookable?: boolean | null;
    /**
     * Indicators for service's booking policy violations.
     * Only relevant when `bookable` filter is false.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * Maximum number of slots to return for each day in the specified time range.
     * If `bookable` filter isn't specified, bookable slots are returned first.
     */
    timeSlotsPerDay?: number | null;
    /**
     * Cursor-based paging configuration.
     * Enables fetching results in smaller chunks by setting a limit on the number of results.
     * For consistent pagination behavior, use the same `limit` value throughout a pagination sequence.
     * When specifying a new `limit` in follow-up requests, the API respects the new value.
     */
    cursorPaging?: CursorPaging;
}
interface Service {
    /**
     * Service ID.
     * @format GUID
     */
    serviceId?: string;
    /**
     * ID of the resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) to include in response.
     * @format GUID
     * @maxSize 135
     */
    resourceIds?: string[];
    /**
     * Resource type IDs ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) to include in returned time slots.
     * This is in addition to the specifically requested resources.
     *
     * <blockquote>
     * Currently supported only for Staff Member resource type.
     * Staff members belong to resource type ID `cd44cf8-756f-41c3-bd90-3e2ffcaf1155`.
     * </blockquote>
     * @format GUID
     * @maxSize 100
     */
    includeResourceTypeIds?: string[];
    /**
     * Selected customer choices.
     * If specified, the selected choices are used to calculate service configuration.
     * If not specified, the service default configuration is used.
     * Enforcing this field is the responsibility of the service plugin implementer, and not the Time Slots V2 API.
     */
    customerChoices?: V2CustomerChoices;
}
/**
 * Selected customer choices.
 * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.
 */
interface V2CustomerChoices {
    /**
     * Selected customer duration in minutes.
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * Default: `15` minutes
     * @min 1
     * @max 44639
     */
    durationInMinutes?: number | null;
    /**
     * Selected add-on IDs.
     *
     * Max: Calculated as the product of the maximum number of add-on groups multiplied by the maximum number of add-ons per group. Currently 21 (3 groups × 7 add-ons per group), but may change in the future.
     * @format GUID
     * @maxSize 21
     */
    addOnIds?: string[] | null;
    /**
     * Selected duration choice ID.
     * @format GUID
     */
    durationChoiceId?: string | null;
}
interface CursorPaging {
    /**
     * Number of results to load.
     *
     * Default: `1000`.
     * Max: `1000`.
     * @max 1000
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     */
    cursor?: string | null;
}
interface ListMultiServiceAvailabilityTimeSlotsResponse {
    /**
     * Retrieved time slots.
     * Sorted by `localStartDate` in ascending order. When multiple slots have the same start time, no specific secondary sorting is guaranteed.
     * @maxSize 1000
     */
    timeSlots?: TimeSlot[];
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.
     * For example, `America/New_York` or `UTC`.
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Paging metadata for the next page of results.
     * Contains a cursor if there are more than 1000 results.
     */
    cursorPagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /** Cursor pointing to next page in the list of results. */
    next?: string | null;
    /** Cursor pointing to previous page in the list of results. */
    prev?: string | null;
}
interface GetMultiServiceAvailabilityTimeSlotRequest {
    /**
     * Services for which the multi-service time slots are returned.
     * You can specify resource filters for each service.
     * @minSize 2
     * @maxSize 8
     */
    services: Service[];
    /**
     * Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localStartDate: string;
    /**
     * Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localEndDate: string;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone: string | null;
    /**
     * Location for which the multi-service time slots are returned. If you filter by `{"type": "BUSINESS"}`, you must also specify a location ID. A filter for `location.name` is ignored.
     *
     * Required unless you specify `cursorPaging.cursor`.
     */
    location: Location;
}
interface GetMultiServiceAvailabilityTimeSlotResponse {
    /** Retrieved time slot. */
    timeSlot?: TimeSlot;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.
     * For example, `America/New_York` or `UTC`.
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
}
interface ListAvailabilityTimeSlotsRequest {
    /**
     * Service ID for which to retrieve time slots. You must specify the ID of an appointment-based service.
     * Required, unless you specify `cursorPaging.cursor`.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Lower boundary for `localStartDate` to include in response.
     * Each returned time slot has a `localStartDate` within the provided `fromLocalDate` and `toLocalDate` exclusive.
     * Required, unless you specify `cursorPaging.cursor`.
     *
     * Local start date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    fromLocalDate?: string | null;
    /**
     * Upper boundary for `localStartDate` to include in response.
     * Each returned time slot has a `localStartDate` within the provided `fromLocalDate` and `toLocalDate` exclusive.
     * Required, unless you specify `cursorPaging.cursor`.
     *
     * Local end date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    toLocalDate?: string | null;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Locations to include in response.
     * If not specified, returns time slots for all locations where the service is available.
     * @maxSize 5
     */
    locations?: Location[];
    /**
     * Resources to include in response.
     * If specified, returns up to 10 available resources out of the provided list.
     * Otherwise, returns time slots with empty `availableResources`.
     * @format GUID
     * @maxSize 135
     * @deprecated
     */
    resourceIds?: string[];
    /**
     * Resource type IDs to include in the response.
     * If specified, returns up to 10 `availableResources` with matching `resourceTypeId`.
     * This controls which resource details are included in the response but doesn't filter the time slots themselves.
     * @format GUID
     * @maxSize 100
     */
    includeResourceTypeIds?: string[];
    /**
     * Whether the time slot is bookable according to the service's booking policies.
     * If not specified, returns both bookable and un-bookable time slots.
     */
    bookable?: boolean | null;
    /**
     * Indicators for service's booking policy violations.
     * Only relevant when `bookable` filter is set to `false`. Allows filtering for time slots with specific violation types:
     * - `tooEarlyToBook`: Returns slots that violate minimum advance booking time.
     * - `tooLateToBook`: Returns slots that violate maximum advance booking time.
     * - `bookOnlineDisabled`: Returns slots where online booking is disabled.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * Maximum number of slots to return for each day in the specified time range.
     * If `bookable` filter isn't specified, bookable slots are returned first.
     */
    timeSlotsPerDay?: number | null;
    /**
     * Cursor-based paging configuration.
     * Enables fetching results in smaller chunks by setting a limit on the number of results.
     * For consistent pagination behavior, use the same `limit` value throughout a pagination sequence.
     * When specifying a new `limit` in follow-up requests, the API respects the new value.
     */
    cursorPaging?: CursorPaging;
    /**
     * Resource types to filter time slots.
     * Only returns time slots that have these specific resource types available.
     * This filters the time slots themselves, unlike `includeResourceTypeIds` which only controls response details.
     * @maxSize 3
     */
    resourceTypes?: ResourceType[];
}
/**
 * Selected customer choices.
 * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.
 */
interface CustomerChoices {
    /**
     * The selected customer duration in minutes.
     * Min: `1 minute`
     * Max: `44639 minutes` (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     */
    durationInMinutes?: number | null;
    /**
     * The selected add-ons IDs.
     * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.
     * @format GUID
     * @maxSize 21
     */
    addOnIds?: string[] | null;
    /**
     * The selected duration choice ID.
     * @format GUID
     */
    durationChoiceId?: string | null;
}
interface ResourceType {
    /**
     * Resource type ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     * @format GUID
     */
    resourceTypeId?: string | null;
    /**
     * Resource IDs.
     * Available only if there is at least 1 resource available for the slot.
     * @format GUID
     * @minSize 1
     * @maxSize 135
     */
    resourceIds?: string[] | null;
}
interface ListAvailabilityTimeSlotsResponse {
    /**
     * Retrieved time slots.
     * Sorted by `localStartDate` in ascending order. When multiple slots have the same start time, no specific secondary sorting is guaranteed.
     * @maxSize 1000
     */
    timeSlots?: TimeSlot[];
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Paging metadata for the next page of results.
     * Contains a cursor if there are more than 1000 results.
     */
    cursorPagingMetadata?: CursorPagingMetadata;
}
interface GetAvailabilityTimeSlotRequest {
    /**
     * Service ID of the time slot.
     * You must specify the ID of an appointment-based service.
     * @format GUID
     */
    serviceId?: string;
    /**
     * Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string;
    /**
     * Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Location to filter time slots by.
     *
     * For business locations, you must specify a location ID.
     * When specifying a location ID, all other location field filters are ignored.
     */
    location?: Location;
    /**
     * IDs of the resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) to check availability for.
     * If omitted, time slots are returned if there's at least 1 available resource.
     * @format GUID
     * @maxSize 135
     */
    resourceIds?: string[];
    /**
     * IDs of the resource types ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) to check availability for.
     * @format GUID
     * @maxSize 100
     */
    includeResourceTypeIds?: string[];
}
interface GetAvailabilityTimeSlotRequestCustomerChoices {
    /**
     * Selected add-on IDs.
     * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.
     * @format GUID
     * @maxSize 21
     */
    addOnIds?: string[] | null;
    /**
     * Selected duration choice ID.
     * @format GUID
     */
    durationChoiceId?: string | null;
}
interface GetAvailabilityTimeSlotResponse {
    /** Retrieved time slot. */
    timeSlot?: TimeSlot;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
}
interface ListEventTimeSlotsRequest {
    /**
     * Lower boundary for `localStartDate` to include in the response.
     * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate.
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Local start date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    fromLocalDate?: string | null;
    /**
     * Upper boundary for `localStartDate` to include in the response.
     * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate`.
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Local end date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    toLocalDate?: string | null;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @maxLength 100
     */
    timeZone?: string | null;
    /**
     * Optional service IDs to filter the response.
     * If not provided, time slots for all services are returned.
     * @format GUID
     * @maxSize 100
     */
    serviceIds?: string[] | null;
    /**
     * Whether to include time slots that aren't bookable according to the service's booking policy or that are fully booked.
     * - `true`: Both bookable and non-bookable time slots are returned.
     * - `false`: Only bookable time slots are returned.
     *
     * Default: `true`
     */
    includeNonBookable?: boolean | null;
    /**
     * Minimum bookable capacity.
     * Use to filter out sessions that can't accommodate the desired party size.
     * @min 1
     */
    minBookableCapacity?: number | null;
    /**
     * Optional filter to apply to the events, for example resource or location.
     * See the [Events API Supported Filters and Sorting article](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/supported-filters-and-sorting) for a complete list of filters.
     */
    eventFilter?: Record<string, any> | null;
    /**
     * Maximum number of slots to return for each day in the specified time range.
     * If both bookable and non-bookable slots exist on the same day, bookable slots are returned first.
     *
     * When you specify `maxSlotsPerDay`, you must also specify `toLocalDate`, and it must be no more than 1 month after `fromLocalDate`.
     * @min 1
     * @max 1000
     */
    maxSlotsPerDay?: number | null;
    /**
     * Cursor-based paging configuration.
     * Enables fetching results in smaller chunks by setting a limit on the number of returned items.
     */
    cursorPaging?: CursorPaging;
    /**
     * Indicators for service's booking policy violations.
     * Allows filtering for time slots with specific violation types:
     * - `tooEarlyToBook`: Returns slots that violate minimum advance booking time.
     * - `tooLateToBook`: Returns slots that violate maximum advance booking time.
     * - `bookOnlineDisabled`: Returns slots where online booking is disabled.
     *
     * When specified, only time slots matching the violation criteria are returned.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
}
interface ListEventTimeSlotsResponse {
    /** Retrieved time slots matching the specified filters. */
    timeSlots?: TimeSlot[];
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Paging metadata for the next page of results.
     * Contains a cursor if more data is available.
     */
    pagingMetadata?: CursorPagingMetadata;
}
interface GetEventTimeSlotRequest {
    /**
     * Event ID.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @maxLength 100
     */
    timeZone?: string | null;
}
interface GetEventTimeSlotResponse {
    /** The time slot. */
    timeSlot?: TimeSlot;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
}
/** @docsIgnore */
type ListMultiServiceAvailabilityTimeSlotsApplicationErrors = {
    code?: 'NO_IMPLEMENTERS_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTIPLE_IMPLEMENTERS_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MISSING_ARGUMENTS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_SERVICES_SIZE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNAUTHORIZED_OPERATION';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type GetMultiServiceAvailabilityTimeSlotApplicationErrors = {
    code?: 'SLOT_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NO_IMPLEMENTERS_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTIPLE_IMPLEMENTERS_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNAUTHORIZED_OPERATION';
    description?: string;
    data?: Record<string, any>;
};
/**
 * Retrieves a list of multi-service time slots that match the provided filters.
 *
 *
 * ## Required filters
 *
 * You must specify one of:
 * - `services.serviceId`, `fromLocalDate`, `toLocalDate`, `location`, and `timeZone` (additional filters are optional).
 * - `cursorPaging.cursor` returned from a previous response.
 *
 * Each returned `timeSlot` acts as a container spanning the entire service sequence, with nested time slots providing individual service details.
 *
 * ## Defaults
 *
 * - Results are sorted by `localStartDate` in ascending order.
 * - `cursorPaging.limit` is `1000`.
 * - The response contains both bookable and non-bookable slots.
 *
 * ## Service type limitations
 *
 * Only appointment-type services are supported.
 *
 * To retrieve appointment availability for a single service, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).
 * @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.AVAILABILITY_READ_MULTI_SERVICE_TIME_SLOTS
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.availability.v2.MultiServiceAvailabilityTimeSlots.ListMultiServiceAvailabilityTimeSlots
 */
declare function listMultiServiceAvailabilityTimeSlots(options?: ListMultiServiceAvailabilityTimeSlotsOptions): Promise<NonNullablePaths<ListMultiServiceAvailabilityTimeSlotsResponse, `timeSlots` | `timeSlots.${number}.location.locationType`, 5> & {
    __applicationErrorsType?: ListMultiServiceAvailabilityTimeSlotsApplicationErrors;
}>;
interface ListMultiServiceAvailabilityTimeSlotsOptions {
    /**
     * Services for which the multi-service time slots are returned.
     * Each service can include its own resource filters.
     *
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Min: 2 services
     * Max: 8 services
     * @maxSize 8
     */
    services?: Service[];
    /**
     * Lower boundary for `localStartDate` in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate`.
     *
     * Required unless you specify `cursorPaging.cursor`.
     * @format LOCAL_DATE_TIME
     */
    fromLocalDate?: string | null;
    /**
     * Upper boundary for `localToDate` in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * Each returned time slot has a `localEndDate` between `fromLocalDate` and `toLocalDate`.
     *
     * Required unless you specify `cursorPaging.cursor`.
     * @format LOCAL_DATE_TIME
     */
    toLocalDate?: string | null;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values. For example, `America/New_York` or `UTC`.
     *
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Location for which the multi-service time slots are returned. If you filter by `{"type": "BUSINESS"}`, you must also specify a location ID. Filters for `location.name` and `location.formattedAddress` are ignored.
     *
     * Required unless you specify `cursorPaging.cursor`.
     */
    location?: Location;
    /**
     * Whether the time slot is bookable according to all services' booking policies.
     * If not specified, returns both bookable and non-bookable time slots.
     */
    bookable?: boolean | null;
    /**
     * Indicators for service's booking policy violations.
     * Only relevant when `bookable` filter is false.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * Maximum number of slots to return for each day in the specified time range.
     * If `bookable` filter isn't specified, bookable slots are returned first.
     */
    timeSlotsPerDay?: number | null;
    /**
     * Cursor-based paging configuration.
     * Enables fetching results in smaller chunks by setting a limit on the number of results.
     * For consistent pagination behavior, use the same `limit` value throughout a pagination sequence.
     * When specifying a new `limit` in follow-up requests, the API respects the new value.
     */
    cursorPaging?: CursorPaging;
}
/**
 * Retrieves a multi-service time slot that matches the specified filters.
 *
 *
 * Call this method after finding a suitable slot with List Multi-Service Availability Time Slots to obtain full capacity, resource, and booking-policy details.
 *
 * The returned time slot acts as a container spanning the entire service sequence, with nested time slots providing detailed information for each individual service.
 *
 * ## Defaults
 *
 * - Returns all available resources unless you filter by `services.resourceIds` or `services.includeResourceTypeIds`.
 * - Includes full booking-status and capacity details.
 *
 * ## Service type limitations
 *
 * Only appointment-type services are supported.
 *
 * To retrieve appointment availability for a single service, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).
 * @param services - Services for which the multi-service time slots are returned.
 * You can specify resource filters for each service.
 * @param localStartDate - Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
 * For example, `2026-01-30T13:30:00`.
 * @param localEndDate - Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
 * For example, `2026-01-30T13:30:00`.
 * @param timeZone - Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
 * For example, `America/New_York` or `UTC`.
 *
 * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
 * @param location - Location for which the multi-service time slots are returned. If you filter by `{"type": "BUSINESS"}`, you must also specify a location ID. A filter for `location.name` is ignored.
 *
 * Required unless you specify `cursorPaging.cursor`.
 * @public
 * @documentationMaturity preview
 * @requiredField localEndDate
 * @requiredField localStartDate
 * @requiredField location
 * @requiredField location.locationType
 * @requiredField services
 * @requiredField services.serviceId
 * @requiredField timeZone
 * @permissionId BOOKINGS.AVAILABILITY_READ_MULTI_SERVICE_TIME_SLOTS
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.availability.v2.MultiServiceAvailabilityTimeSlots.GetMultiServiceAvailabilityTimeSlot
 */
declare function getMultiServiceAvailabilityTimeSlot(services: NonNullablePaths<Service, `serviceId`, 2>[], localStartDate: string, localEndDate: string, timeZone: string, location: NonNullablePaths<Location, `locationType`, 2>): Promise<NonNullablePaths<GetMultiServiceAvailabilityTimeSlotResponse, `timeSlot.location.locationType` | `timeSlot.availableResources` | `timeSlot.nestedTimeSlots` | `timeSlot.nestedTimeSlots.${number}.serviceId` | `timeSlot.nestedTimeSlots.${number}.localStartDate` | `timeSlot.nestedTimeSlots.${number}.localEndDate`, 5> & {
    __applicationErrorsType?: GetMultiServiceAvailabilityTimeSlotApplicationErrors;
}>;

export { type AvailableResources, type BookingPolicyViolations, type CursorPaging, type CursorPagingMetadata, type Cursors, type CustomerChoices, type EventInfo, type GetAvailabilityTimeSlotRequest, type GetAvailabilityTimeSlotRequestCustomerChoices, type GetAvailabilityTimeSlotResponse, type GetEventTimeSlotRequest, type GetEventTimeSlotResponse, type GetMultiServiceAvailabilityTimeSlotApplicationErrors, type GetMultiServiceAvailabilityTimeSlotRequest, type GetMultiServiceAvailabilityTimeSlotResponse, type ListAvailabilityTimeSlotsRequest, type ListAvailabilityTimeSlotsResponse, type ListEventTimeSlotsRequest, type ListEventTimeSlotsResponse, type ListMultiServiceAvailabilityTimeSlotsApplicationErrors, type ListMultiServiceAvailabilityTimeSlotsOptions, type ListMultiServiceAvailabilityTimeSlotsRequest, type ListMultiServiceAvailabilityTimeSlotsResponse, type Location, LocationType, type LocationTypeWithLiterals, type NestedTimeSlot, type NonBookableReasons, type Resource, type ResourceType, type Service, type TimeSlot, type V2CustomerChoices, type WaitingList, getMultiServiceAvailabilityTimeSlot, listMultiServiceAvailabilityTimeSlots };
