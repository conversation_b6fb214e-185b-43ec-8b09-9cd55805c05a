// src/bookings-v2-booking-bookings.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKFloatToRESTFloat } from "@wix/sdk-runtime/transformations/float";
import { transformRESTFloatToSDKFloat } from "@wix/sdk-runtime/transformations/float";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings-service",
        destPath: "/v2/bookings"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
function resolveComWixpressBookingsBookingsV2BookingsUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings-service",
        destPath: "/v2/bookings"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
function resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl(opts) {
  const domainToMappings = {
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-confirmator/v2/bookings/confirmation",
        destPath: "/v2/bookings/confirmation"
      },
      {
        srcPath: "/bookings/v2/confirmation",
        destPath: "/v2/confirmation"
      },
      {
        srcPath: "/bookings/v2/bulk/confirmation",
        destPath: "/v2/bulk/confirmation"
      },
      {
        srcPath: "/bookings/v2/bulk/bookings/confirmOrDecline",
        destPath: "/v2/bulk/bookings/confirmOrDecline"
      },
      {
        srcPath: "/_api/bookings-confirmator/v2/bulk/confirmation",
        destPath: "/v2/bulk/confirmation"
      },
      {
        srcPath: "/_api/bookings-confirmator/v2/bulk/bookings/confirmOrDecline",
        destPath: "/v2/bulk/bookings/confirmOrDecline"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_bookings";
function confirmOrDeclineBooking(payload) {
  function __confirmOrDeclineBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.confirmator.v2.Confirmator.ConfirmOrDeclineBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl({
        protoPath: "/v2/confirmation/{bookingId}:confirmOrDecline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __confirmOrDeclineBooking;
}
function bulkConfirmOrDeclineBooking(payload) {
  function __bulkConfirmOrDeclineBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.confirmator.v2.Confirmator.BulkConfirmOrDeclineBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl({
        protoPath: "/v2/bulk/bookings/confirmOrDecline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.startDate" },
            { path: "results.item.endDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "results.item.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkConfirmOrDeclineBooking;
}
function createBooking(payload) {
  function __createBooking({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "booking.createdDate" },
          { path: "booking.startDate" },
          { path: "booking.endDate" },
          { path: "booking.updatedDate" },
          { path: "booking.canceledDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "booking.contactDetails.fullAddress.geocode.latitude" },
          { path: "booking.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.CreateBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBooking;
}
function bulkCreateBooking(payload) {
  function __bulkCreateBooking({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "createBookingsInfo.booking.createdDate" },
          { path: "createBookingsInfo.booking.startDate" },
          { path: "createBookingsInfo.booking.endDate" },
          { path: "createBookingsInfo.booking.updatedDate" },
          { path: "createBookingsInfo.booking.canceledDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          {
            path: "createBookingsInfo.booking.contactDetails.fullAddress.geocode.latitude"
          },
          {
            path: "createBookingsInfo.booking.contactDetails.fullAddress.geocode.longitude"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.BulkCreateBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bulk/bookings/create",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.startDate" },
            { path: "results.item.endDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "results.item.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkCreateBooking;
}
function rescheduleBooking(payload) {
  function __rescheduleBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.RescheduleBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/reschedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __rescheduleBooking;
}
function bookingsConfirmBooking(payload) {
  function __bookingsConfirmBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.ConfirmBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/confirm",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsConfirmBooking;
}
function setBookingSubmissionId(payload) {
  function __setBookingSubmissionId({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.SetBookingSubmissionId",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/set-booking-submission-id",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setBookingSubmissionId;
}
function updateExtendedFields(payload) {
  function __updateExtendedFields({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.UpdateExtendedFields",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{id}/update_extended_fields",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __updateExtendedFields;
}
function bookingsDeclineBooking(payload) {
  function __bookingsDeclineBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.DeclineBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/decline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsDeclineBooking;
}
function bookingsCancelBooking(payload) {
  function __bookingsCancelBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.CancelBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/cancel",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsCancelBooking;
}
function bookingsUpdateNumberOfParticipants(payload) {
  function __bookingsUpdateNumberOfParticipants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.UpdateNumberOfParticipants",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/update_number_of_participants",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsUpdateNumberOfParticipants;
}
function markBookingAsPending(payload) {
  function __markBookingAsPending({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.MarkBookingAsPending",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/mark_booking_as_pending",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __markBookingAsPending;
}
function createMultiServiceBooking(payload) {
  function __createMultiServiceBooking({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookings.createdDate" },
          { path: "bookings.startDate" },
          { path: "bookings.endDate" },
          { path: "bookings.updatedDate" },
          { path: "bookings.canceledDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "bookings.contactDetails.fullAddress.geocode.latitude" },
          { path: "bookings.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.CreateMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createMultiServiceBooking;
}
function rescheduleMultiServiceBooking(payload) {
  function __rescheduleMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.RescheduleMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/reschedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __rescheduleMultiServiceBooking;
}
function getMultiServiceBookingAvailability(payload) {
  function __getMultiServiceBookingAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBookingAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/get_availability",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __getMultiServiceBookingAvailability;
}
function cancelMultiServiceBooking(payload) {
  function __cancelMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.CancelMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/cancel",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __cancelMultiServiceBooking;
}
function markMultiServiceBookingAsPending(payload) {
  function __markMultiServiceBookingAsPending({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.MarkMultiServiceBookingAsPending",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/mark_as_pending",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __markMultiServiceBookingAsPending;
}
function confirmMultiServiceBooking(payload) {
  function __confirmMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.ConfirmMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/confirm",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __confirmMultiServiceBooking;
}
function declineMultiServiceBooking(payload) {
  function __declineMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.DeclineMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/decline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __declineMultiServiceBooking;
}
function bulkGetMultiServiceBookingAllowedActions(payload) {
  function __bulkGetMultiServiceBookingAllowedActions({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.BulkGetMultiServiceBookingAllowedActions",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/bulk/multi_service_bookings/get_allowed_actions",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkGetMultiServiceBookingAllowedActions;
}
function getMultiServiceBooking(payload) {
  function __getMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "GET",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getMultiServiceBooking;
}
function addBookingsToMultiServiceBooking(payload) {
  function __addBookingsToMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.AddBookingsToMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/add_bookings_to_multi_service_booking",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookings.booking.createdDate" },
            { path: "bookings.booking.startDate" },
            { path: "bookings.booking.endDate" },
            { path: "bookings.booking.updatedDate" },
            { path: "bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __addBookingsToMultiServiceBooking;
}
function removeBookingsFromMultiServiceBooking(payload) {
  function __removeBookingsFromMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.RemoveBookingsFromMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/remove_bookings_from_multi_service_booking",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookings.booking.createdDate" },
            { path: "bookings.booking.startDate" },
            { path: "bookings.booking.endDate" },
            { path: "bookings.booking.updatedDate" },
            { path: "bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __removeBookingsFromMultiServiceBooking;
}

// src/bookings-v2-booking-bookings.meta.ts
function confirmOrDeclineBooking2() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = confirmOrDeclineBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/confirmation/{bookingId}:confirmOrDecline",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkConfirmOrDeclineBooking2() {
  const payload = {};
  const getRequestOptions = bulkConfirmOrDeclineBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/bookings/confirmOrDecline",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function createBooking2() {
  const payload = {};
  const getRequestOptions = createBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkCreateBooking2() {
  const payload = {};
  const getRequestOptions = bulkCreateBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/bookings/create",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function rescheduleBooking2() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = rescheduleBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{bookingId}/reschedule",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function confirmBooking() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = bookingsConfirmBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{bookingId}/confirm",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function setBookingSubmissionId2() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = setBookingSubmissionId(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{bookingId}/set-booking-submission-id",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateExtendedFields2() {
  const payload = { id: ":id" };
  const getRequestOptions = updateExtendedFields(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{id}/update_extended_fields",
    pathParams: { id: "id" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function declineBooking() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = bookingsDeclineBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{bookingId}/decline",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function cancelBooking() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = bookingsCancelBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{bookingId}/cancel",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateNumberOfParticipants() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = bookingsUpdateNumberOfParticipants(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{bookingId}/update_number_of_participants",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function markBookingAsPending2() {
  const payload = { bookingId: ":bookingId" };
  const getRequestOptions = markBookingAsPending(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bookings/{bookingId}/mark_booking_as_pending",
    pathParams: { bookingId: "bookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function createMultiServiceBooking2() {
  const payload = {};
  const getRequestOptions = createMultiServiceBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function rescheduleMultiServiceBooking2() {
  const payload = { multiServiceBookingId: ":multiServiceBookingId" };
  const getRequestOptions = rescheduleMultiServiceBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/{multiServiceBookingId}/reschedule",
    pathParams: { multiServiceBookingId: "multiServiceBookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getMultiServiceBookingAvailability2() {
  const payload = { multiServiceBookingId: ":multiServiceBookingId" };
  const getRequestOptions = getMultiServiceBookingAvailability(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/{multiServiceBookingId}/get_availability",
    pathParams: { multiServiceBookingId: "multiServiceBookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function cancelMultiServiceBooking2() {
  const payload = { multiServiceBookingId: ":multiServiceBookingId" };
  const getRequestOptions = cancelMultiServiceBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/{multiServiceBookingId}/cancel",
    pathParams: { multiServiceBookingId: "multiServiceBookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function markMultiServiceBookingAsPending2() {
  const payload = { multiServiceBookingId: ":multiServiceBookingId" };
  const getRequestOptions = markMultiServiceBookingAsPending(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/{multiServiceBookingId}/mark_as_pending",
    pathParams: { multiServiceBookingId: "multiServiceBookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function confirmMultiServiceBooking2() {
  const payload = { multiServiceBookingId: ":multiServiceBookingId" };
  const getRequestOptions = confirmMultiServiceBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/{multiServiceBookingId}/confirm",
    pathParams: { multiServiceBookingId: "multiServiceBookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function declineMultiServiceBooking2() {
  const payload = { multiServiceBookingId: ":multiServiceBookingId" };
  const getRequestOptions = declineMultiServiceBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/{multiServiceBookingId}/decline",
    pathParams: { multiServiceBookingId: "multiServiceBookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkGetMultiServiceBookingAllowedActions2() {
  const payload = {};
  const getRequestOptions = bulkGetMultiServiceBookingAllowedActions(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/multi_service_bookings/get_allowed_actions",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getMultiServiceBooking2() {
  const payload = { multiServiceBookingId: ":multiServiceBookingId" };
  const getRequestOptions = getMultiServiceBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/multi_service_bookings/{multiServiceBookingId}",
    pathParams: { multiServiceBookingId: "multiServiceBookingId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function addBookingsToMultiServiceBooking2() {
  const payload = {};
  const getRequestOptions = addBookingsToMultiServiceBooking(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/add_bookings_to_multi_service_booking",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function removeBookingsFromMultiServiceBooking2() {
  const payload = {};
  const getRequestOptions = removeBookingsFromMultiServiceBooking(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/multi_service_bookings/remove_bookings_from_multi_service_booking",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  addBookingsToMultiServiceBooking2 as addBookingsToMultiServiceBooking,
  bulkConfirmOrDeclineBooking2 as bulkConfirmOrDeclineBooking,
  bulkCreateBooking2 as bulkCreateBooking,
  bulkGetMultiServiceBookingAllowedActions2 as bulkGetMultiServiceBookingAllowedActions,
  cancelBooking,
  cancelMultiServiceBooking2 as cancelMultiServiceBooking,
  confirmBooking,
  confirmMultiServiceBooking2 as confirmMultiServiceBooking,
  confirmOrDeclineBooking2 as confirmOrDeclineBooking,
  createBooking2 as createBooking,
  createMultiServiceBooking2 as createMultiServiceBooking,
  declineBooking,
  declineMultiServiceBooking2 as declineMultiServiceBooking,
  getMultiServiceBooking2 as getMultiServiceBooking,
  getMultiServiceBookingAvailability2 as getMultiServiceBookingAvailability,
  markBookingAsPending2 as markBookingAsPending,
  markMultiServiceBookingAsPending2 as markMultiServiceBookingAsPending,
  removeBookingsFromMultiServiceBooking2 as removeBookingsFromMultiServiceBooking,
  rescheduleBooking2 as rescheduleBooking,
  rescheduleMultiServiceBooking2 as rescheduleMultiServiceBooking,
  setBookingSubmissionId2 as setBookingSubmissionId,
  updateExtendedFields2 as updateExtendedFields,
  updateNumberOfParticipants
};
//# sourceMappingURL=meta.mjs.map