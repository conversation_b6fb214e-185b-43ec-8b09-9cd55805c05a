import { NonNullablePaths } from '@wix/sdk-types';

/** Categories are used to group multiple services together. A service must be associated with a category in order to be exposed in the Wix Bookings UI. */
interface Category {
    /**
     * Category ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Category name.
     * @maxLength 500
     */
    name?: string | null;
    /**
     * @internal
     * @internal
     * @readonly
     */
    status?: StatusWithLiterals;
    /**
     * @internal
     * @internal */
    sortOrder?: number | null;
}
declare enum Status {
    /** The category was created. */
    CREATED = "CREATED",
    /** The category was deleted. */
    DELETED = "DELETED"
}
/** @enumType */
type StatusWithLiterals = Status | 'CREATED' | 'DELETED';
interface ListCategoryRequest {
    /**
     * IDs of the categories to retrieve.
     *
     * Default: All categories are retrieved.
     * @format GUID
     */
    categoryIds?: string[];
    /**
     * @internal
     * @internal */
    includeDeleted?: boolean | null;
}
interface ListCategoryResponse {
    /** Retrieved categories. */
    categories?: Category[];
}
/** An event sent every time a category entity is changed. */
interface CategoryNotification {
    category?: Category;
    event?: EventWithLiterals;
}
declare enum Event {
    /** Category was updated. */
    Updated = "Updated",
    /** Category was deleted. */
    Deleted = "Deleted",
    /** Category was created. */
    Created = "Created"
}
/** @enumType */
type EventWithLiterals = Event | 'Updated' | 'Deleted' | 'Created';
interface CreateCategoryRequest {
    /** Category to create. */
    category: Category;
}
interface CreateCategoryResponse {
    /** Created category. */
    category?: Category;
}
interface BatchCreateCategoryRequest {
    /** Categories to create. */
    categories?: Category[];
}
interface BatchCreateCategoryResponse {
    /** Created categories. */
    categories?: Category[];
}
interface UpdateCategoryRequest {
    /** Category to update. */
    category: Category;
}
interface UpdateCategoryResponse {
    /** Updated category. */
    category?: Category;
}
interface DeleteCategoryRequest {
    /**
     * ID of the category to delete.
     * @format GUID
     */
    _id: string | null;
    /**
     * Whether to delete all the services associated with the category.
     *
     * Default: `false`
     */
    deleteServices?: boolean | null;
}
interface DeleteCategoryResponse {
    /**
     * ID of the deleted category.
     * @format GUID
     */
    _id?: string | null;
}
interface BatchDeleteCategoryRequest {
    /**
     * IDs of the categories to delete.
     * @format GUID
     */
    ids?: string[] | null;
}
interface BatchDeleteCategoryResponse {
}
interface BatchUpdateCategoryRequest {
    /** List of categories to be updated. */
    categories?: Category[];
    /** Field mask of fields to update. */
    fieldMask?: string[];
}
interface BatchUpdateCategoryResponse {
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type UpdateCategoryApplicationErrors = {
    code?: 'NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface CategoryNotificationEnvelope {
    data: CategoryNotification;
    metadata: BaseEventMetadata;
}
/** @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.CATEGORIES_READ
 * @webhook
 * @eventType com.wixpress.bookings.services.api.v1.CategoryNotification
 * @serviceIdentifier com.wixpress.bookings.services.api.v1.CategoriesService
 * @slug category_notification
 * @documentationMaturity preview
 * @deprecated
 */
declare function onCategoryNotification(handler: (event: CategoryNotificationEnvelope) => void | Promise<void>): void;
/**
 * Retrieves all categories.
 * @public
 * @documentationMaturity preview
 * @param options - Options to use when listing categories.
 * @permissionId BOOKINGS.CATEGORIES_READ
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.services.api.v1.CategoriesService.List
 * @deprecated
 */
declare function listCategories(options?: ListCategoriesOptions): Promise<NonNullablePaths<ListCategoryResponse, `categories` | `categories.${number}.status`, 4>>;
interface ListCategoriesOptions {
    /**
     * IDs of the categories to retrieve.
     *
     * Default: All categories are retrieved.
     * @format GUID
     */
    categoryIds?: string[];
    /** @internal */
    includeDeleted?: boolean | null;
}
/**
 * Creates a category.
 * @param category - Category to create.
 * @public
 * @documentationMaturity preview
 * @requiredField category
 * @requiredField category.name
 * @permissionId BOOKINGS.MANAGE_CATEGORIES
 * @applicableIdentity APP
 * @returns Created category.
 * @fqn com.wixpress.bookings.services.api.v1.CategoriesService.Create
 * @deprecated
 */
declare function createCategory(category: NonNullablePaths<Category, `name`, 2>): Promise<NonNullablePaths<Category, `status`, 2>>;
/**
 * Updates a category.
 *
 *
 * Each time the category is updated, revision increments by 1. You must include
 * the number of the existing revision when updating the category. This ensures
 * you're working with the latest service information and prevents unintended
 * overwrites.
 * @param _id - Category ID.
 * @public
 * @documentationMaturity preview
 * @requiredField _id
 * @requiredField category
 * @requiredField category.name
 * @permissionId BOOKINGS.MANAGE_CATEGORIES
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.services.api.v1.CategoriesService.Update
 * @deprecated
 */
declare function updateCategory(_id: string, category: NonNullablePaths<UpdateCategory, `name`, 2>): Promise<NonNullablePaths<UpdateCategoryResponse, `category.status`, 3> & {
    __applicationErrorsType?: UpdateCategoryApplicationErrors;
}>;
interface UpdateCategory {
    /**
     * Category ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Category name.
     * @maxLength 500
     */
    name?: string | null;
    /**
     * @internal
     * @readonly
     */
    status?: StatusWithLiterals;
    /** @internal */
    sortOrder?: number | null;
}
/**
 * Deletes a category.
 *
 *
 * You can specify `{"deleteServices": true}` to also delete all associated
 * services. Learn more about *deleting a service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service)).
 * @param _id - ID of the category to delete.
 * @public
 * @documentationMaturity preview
 * @requiredField _id
 * @permissionId BOOKINGS.MANAGE_CATEGORIES
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.services.api.v1.CategoriesService._delete
 * @deprecated
 */
declare function deleteCategory(_id: string, options?: DeleteCategoryOptions): Promise<DeleteCategoryResponse>;
interface DeleteCategoryOptions {
    /**
     * Whether to delete all the services associated with the category.
     *
     * Default: `false`
     */
    deleteServices?: boolean | null;
}

export { type BaseEventMetadata, type BatchCreateCategoryRequest, type BatchCreateCategoryResponse, type BatchDeleteCategoryRequest, type BatchDeleteCategoryResponse, type BatchUpdateCategoryRequest, type BatchUpdateCategoryResponse, type Category, type CategoryNotification, type CategoryNotificationEnvelope, type CreateCategoryRequest, type CreateCategoryResponse, type DeleteCategoryOptions, type DeleteCategoryRequest, type DeleteCategoryResponse, Event, type EventWithLiterals, type IdentificationData, type IdentificationDataIdOneOf, type ListCategoriesOptions, type ListCategoryRequest, type ListCategoryResponse, type MessageEnvelope, Status, type StatusWithLiterals, type UpdateCategory, type UpdateCategoryApplicationErrors, type UpdateCategoryRequest, type UpdateCategoryResponse, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, createCategory, deleteCategory, listCategories, onCategoryNotification, updateCategory };
