{"version": 3, "sources": ["../../../src/bookings-fees-v1-booking-fee-booking-fees.universal.ts", "../../../src/bookings-fees-v1-booking-fee-booking-fees.http.ts"], "sourcesContent": ["import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsFeesV1BookingFee from './bookings-fees-v1-booking-fee-booking-fees.http.js';\n\n/**\n * Fee for a specific booking that's calculated according to the associated booking policy snapshot ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction)). Currently, only cancellation fees, including no-show fees, are supported.\n *\n * Wix Bookings automatically applies the cancellation fee to the eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer cancels the booking themselves.\n * Cancellation fees aren't automatically applied to an eCommerce order if the business owner cancels the booking in their dashboard on behalf of the customer.\n * You can call Apply Booking Fee to Order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/apply-booking-fees-to-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/apply-booking-fees-to-order)) to manually apply booking fees to an eCommerce order.\n */\nexport interface BookingFee {\n  /**\n   * Booking fee ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * ID of the booking associated with the fee.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /** Cancellation fee details. */\n  cancellationFee?: CancellationFee;\n  /**\n   * Information about the *booking policy snapshot*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))\n   * according to which the fee was created.\n   */\n  policyDetails?: PolicyDetails;\n}\n\nexport interface CancellationFee {\n  /** Price the customer must pay. */\n  price?: Money;\n  /** Status of the booking fee. */\n  status?: BookingFeeStatusWithLiterals;\n  /**\n   * Information about what triggered the creation of the booking fee.\n   * @readonly\n   */\n  trigger?: TriggerWithLiterals;\n}\n\n/**\n * Money.\n * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.\n */\nexport interface Money {\n  /**\n   * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.\n   * @format DECIMAL_VALUE\n   * @decimalValue options { gte:0, maxScale:2 }\n   */\n  value?: string;\n  /**\n   * Currency code. Must be valid ISO 4217 currency code (e.g., USD).\n   * @format CURRENCY\n   */\n  currency?: string;\n  /**\n   * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.\n   * @maxLength 50\n   * @readonly\n   */\n  formattedValue?: string | null;\n}\n\nexport enum BookingFeeStatus {\n  /** There is no eCommerce order associated with the booking. */\n  UNKNOWN_STATUS = 'UNKNOWN_STATUS',\n  /** The fee is informational only; the customer doesn't have to pay it. For example, it shows how much the customer would owe if they canceled the booking now. */\n  PREVIEW = 'PREVIEW',\n  /** The booking fee hasn't been added to the eCommerce order yet. */\n  NOT_YET_APPLIED_TO_ORDER = 'NOT_YET_APPLIED_TO_ORDER',\n  /** The booking fee has been added to the eCommerce order. The customer may not have paid it yet. */\n  APPLIED_TO_ORDER = 'APPLIED_TO_ORDER',\n}\n\n/** @enumType */\nexport type BookingFeeStatusWithLiterals =\n  | BookingFeeStatus\n  | 'UNKNOWN_STATUS'\n  | 'PREVIEW'\n  | 'NOT_YET_APPLIED_TO_ORDER'\n  | 'APPLIED_TO_ORDER';\n\n/** The domain event that triggered the booking fee calculation. */\nexport enum Trigger {\n  /** There is no information about what triggered the creation of the booking fee. */\n  UNKNOWN_TRIGGER = 'UNKNOWN_TRIGGER',\n  /** The booking fee was created because the customer didn't show up to the booking or canceled after the expiration of the last cancellation window. */\n  NOT_ATTENDED = 'NOT_ATTENDED',\n  /** The booking fee was created because the customer canceled the booking before the expiration of the last cancellation window. */\n  BOOKING_CANCELED = 'BOOKING_CANCELED',\n}\n\n/** @enumType */\nexport type TriggerWithLiterals =\n  | Trigger\n  | 'UNKNOWN_TRIGGER'\n  | 'NOT_ATTENDED'\n  | 'BOOKING_CANCELED';\n\nexport interface PolicyDetails {\n  /**\n   * ID of the booking policy.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Description of the booking policy.\n   * @maxLength 2500\n   */\n  description?: string | null;\n  /**\n   * Translated description of the booking policy according to the buyer language of the eCommerce order.\n   * @maxLength 2500\n   */\n  descriptionTranslated?: string | null;\n}\n\nexport interface FailedToApplyBookingFeeToOrder {\n  /** Booking fee which couldn't to be applied to the eCommerce order. */\n  bookingFee?: BookingFee;\n  /**\n   * IDs of the booking fees that are related to the booking fee which couldn't be\n   * applied to the eCommerce order.\n   * @format GUID\n   * @maxSize 4\n   */\n  relatedBookingFeeIds?: string[];\n  /** Information about the affected eCommerce order. */\n  ecomOrderInfo?: EcomOrderInfo;\n  /**\n   * Information about whether to notify the business about failing to apply the\n   * booking fees to the eCommerce order and the message to send.\n   */\n  businessNotification?: BusinessNotification;\n}\n\nexport interface EcomOrderInfo {\n  /**\n   * Order ID.\n   * @format GUID\n   */\n  orderId?: string | null;\n  /** Additional fee price. */\n  additionalFeePrice?: Price;\n  /**\n   * The eCommerce additional fee id that was created on the order.\n   * @format GUID\n   */\n  additionalFeeId?: string | null;\n}\n\nexport interface Price {\n  /**\n   * Amount.\n   * @decimalValue options { gte:0, lte:1000000000000000, maxScale:2 }\n   */\n  amount?: string;\n  /**\n   * Amount formatted with currency symbol.\n   * @readonly\n   * @maxLength 100\n   */\n  formattedAmount?: string;\n}\n\nexport interface BusinessNotification {\n  /**\n   * Whether to notify the business about changes made to the booking fees.\n   * Default is false.\n   */\n  notifyBusiness?: boolean | null;\n  /**\n   * Optional custom message to send.\n   * @minLength 1\n   * @maxLength 5000\n   */\n  message?: string | null;\n}\n\nexport interface FailedToCollectAppliedBookingFees {\n  /**\n   * IDs of the bookings for which the booking fees couldn't be collected from the\n   * customer.\n   * @format GUID\n   * @maxSize 4\n   */\n  bookingIds?: string[];\n  /** Information about the affected eCommerce order. */\n  ecomOrderInfo?: EcomOrderInfo;\n  /**\n   * Information about whether to notify the business about failing to collect the\n   * booking fees from the customer and the message to send.\n   */\n  businessNotification?: BusinessNotification;\n}\n\nexport interface ListBookingFeesByBookingIdsRequest {\n  /**\n   * IDs of the bookings to retrieve booking fees for.\n   * @format GUID\n   * @maxSize 5\n   */\n  bookingIds?: string[];\n  /**\n   * IDs of the multi service bookings to retrieve booking fees for.\n   * @format GUID\n   * @maxSize 1\n   */\n  multiServiceBookingIds?: string[];\n}\n\nexport interface ListBookingFeesByBookingIdsResponse {\n  /** List of retrieved booking fees. */\n  bookingFees?: BookingFee[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Cursor strings that point to the next page, previous page, or both. */\n  cursors?: Cursors;\n  /**\n   * Whether there are more pages to retrieve following the current page.\n   *\n   * + `true`: Another page of results can be retrieved.\n   * + `false`: This is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor string pointing to the next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to the previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface ApplyBookingFeesToOrderRequest {\n  /**\n   * IDs of the bookings for which to apply booking fees to an eCommerce order.\n   * @format GUID\n   * @minSize 1\n   * @maxSize 5\n   */\n  bookingIds: string[] | null;\n  /**\n   * Custom price override for the additional fee that's added to the eCommerce\n   * order. The override mustn't exceed the sum of all booking fees. You must have\n   * the `OVERRIDE_BOOKING_FEE_PRICE` permission to use this property.\n   */\n  priceOverride?: Money;\n  /**\n   * Information about the message to the business and whether to send it if the\n   * booking fee application to the eCommerce order fails.\n   */\n  businessNotification?: BusinessNotification;\n}\n\nexport interface ApplyBookingFeesToOrderResponse {\n  /**\n   * Booking fees that were applied as a single additional fee to the eCommerce\n   * order.\n   */\n  bookingFees?: BookingFee[];\n  /** Information about the eCommerce order to which the booking fees are applied. */\n  ecomOrderInfo?: EcomOrderInfo;\n}\n\nexport interface ListNonPreviewBookingFeesByBookingIdsRequest {\n  /**\n   * IDs of the bookings to retrieve booking fees for.\n   * @format GUID\n   * @minSize 1\n   * @maxSize 100\n   */\n  bookingIds?: string[];\n}\n\nexport interface ListNonPreviewBookingFeesByBookingIdsResponse {\n  /** List of retrieved booking fees. */\n  bookingFees?: BookingFee[];\n}\n\nexport interface CollectAppliedBookingFeesRequest {\n  /**\n   * ID of the eCommerce order that includes the booking fees as a single\n   * `additionalFee`.\n   * @format GUID\n   */\n  orderId: string | null;\n  /**\n   * ID of the additional fee that's related to all given booking fees.\n   * @format GUID\n   */\n  additionalFeeId: string | null;\n  /**\n   * Information about whether to notify the business about failing to collect the\n   * booking fees from the customer and the message to send.\n   */\n  businessNotification?: BusinessNotification;\n}\n\nexport interface CollectAppliedBookingFeesResponse {\n  /** Collected amount. */\n  collectedPrice?: Price;\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type ListBookingFeesByBookingIdsApplicationErrors = {\n  code?: 'MISSING_BOOKING_IDS';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type ApplyBookingFeesToOrderApplicationErrors =\n  | {\n      code?: 'BOOKING_IDS_BELONG_TO_DIFFERENT_ORDERS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ORDER_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'PRICE_OVERRIDE_EXCEEDS_MAX';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_FEE_ALREADY_APPLIED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_FEE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ORDER_IS_ALREADY_EDITED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'PARTIALLY_OR_FULLY_REFUNDED_ORDER_CANNOT_BE_EDITED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ORDER_WITH_TAXABLE_EXISTING_ADDITIONAL_FEE_CANNOT_BE_EDITED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ORDER_CANNOT_BE_EDITED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_FEE_AND_ORDER_CURRENCIES_ARE_INCONSISTENT';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'PRICE_OVERRIDE_INSUFFICIENT_PERMISSIONS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_CALCULATING_BOOKING_FEE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_APPLYING_BOOKING_FEE_TO_ORDER';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UPDATING_ITEM_WITH_RELATED_DISCOUNT_IS_FORBIDDEN';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UPDATING_ITEM_WITH_RELATED_ADDITIONAL_FEE_IS_FORBIDDEN';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type CollectAppliedBookingFeesApplicationErrors =\n  | {\n      code?: 'ORDER_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ADDITIONAL_FEE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FEE_IS_NOT_BOOKING_FEE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ORDER_BALANCE_NON_POSITIVE';\n      description?: string;\n      data?: Record<string, any>;\n    };\n\n/**\n * Retrieves booking fees by booking IDs.\n *\n *\n * Instead of returning `bookingFee` objects with the `cancellationFee.price.value`\n * set to `0.00`, the method doesn't return a `bookingFee` object for the relevant\n * booking IDs. For example, no `bookingFee` object is returned if the canceled\n * booking was free or if the booking was canceled before the start of the earliest\n * cancellation window with an associated fee.\n *\n * If the service's booking policy has been updated since the booking was created,\n * booking fees are calculated according to the *booking policy snapshot*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))\n * rather than the current version of the *policy*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).\n *\n * This method calculates the cancellation fee amount based on the time of the\n * call, you can't specify a time. Similarly, it calculates the cancellation fee\n * based on the number of participants who canceled, not a provided number.\n *\n * A `cancellationFee.status` of `PREVIEW` indicates that the booking fee is\n * informational only; the customer isn't required to pay it. When the `status` is\n * set to `UNKNOWN_STATUS` there is no eCommerce order associated with the booking. For\n * example, if a custom checkout was used for the booking instead of the\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n *\n * If multiple events would trigger the calculation of a booking fee, for example\n * when a booking is first canceled and then marked as not attended, Wix calculates\n * the booking fee based on the first trigger. In this example, the booking\n * cancellation.\n * @public\n * @param options - Options to use when listing booking fees.\n * @permissionId BOOKINGS.BOOKING_FEES_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.fees.v1.BookingFees.ListBookingFeesByBookingIds\n */\nexport async function listBookingFeesByBookingIds(\n  options?: ListBookingFeesByBookingIdsOptions\n): Promise<\n  NonNullablePaths<\n    ListBookingFeesByBookingIdsResponse,\n    | `bookingFees`\n    | `bookingFees.${number}.cancellationFee.price.value`\n    | `bookingFees.${number}.cancellationFee.price.currency`\n    | `bookingFees.${number}.cancellationFee.status`\n    | `bookingFees.${number}.cancellationFee.trigger`,\n    6\n  > & {\n    __applicationErrorsType?: ListBookingFeesByBookingIdsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingIds: options?.bookingIds,\n    multiServiceBookingIds: options?.multiServiceBookingIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsFeesV1BookingFee.listBookingFeesByBookingIds(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingIds: '$[0].bookingIds',\n          multiServiceBookingIds: '$[0].multiServiceBookingIds',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListBookingFeesByBookingIdsOptions {\n  /**\n   * IDs of the bookings to retrieve booking fees for.\n   * @format GUID\n   * @maxSize 5\n   */\n  bookingIds?: string[];\n  /**\n   * IDs of the multi service bookings to retrieve booking fees for.\n   * @format GUID\n   * @maxSize 1\n   */\n  multiServiceBookingIds?: string[];\n}\n\n/**\n * Applies booking fees to an _eCommerce order_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n *\n * The booking fees are added as a single additional fee to the eCommerce order.\n * The order's `additionalFee.lineItemIds` array is set to the list of corresponding\n * booking IDs. By default, the `additionalFee.price.amount` is the sum of all\n * booking fee prices. But you may provide a `priceOverride` instead. The override\n * price can't be higher than the sum of all booking fees.\n *\n * Apply Booking Fees to Order also updates the prices of all affected line items\n * in the relevant eCommerce order to zero. After a cancellation fee is applied\n * to an eCommerce order, the cancellation fee's `price.value` is updated to `0.00`\n * and its trigger is set to `UNKNOWN_TRIGGER`. You can retrieve the fee amount\n * from the corresponding `additionalFee` object of the eCommerce order with\n * _Search Orders_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/search-orders) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/search-orders)).\n *\n * If you apply multiple booking fees to an eCommerce order, they either all fail or\n * all succeed together. For example, the call fails if the booking fees are associated\n * with different eCommmerce orders.\n * @param bookingIds - IDs of the bookings for which to apply booking fees to an eCommerce order.\n * @public\n * @requiredField bookingIds\n * @requiredField options.priceOverride.currency\n * @requiredField options.priceOverride.value\n * @param options - Options to use when applying booking fees to an eCommerce order.\n * @permissionId BOOKINGS.BOOKING_FEE_APPLY_TO_ORDER\n * @fqn wix.bookings.fees.v1.BookingFees.ApplyBookingFeesToOrder\n */\nexport async function applyBookingFeesToOrder(\n  bookingIds: string[],\n  options?: NonNullablePaths<\n    ApplyBookingFeesToOrderOptions,\n    `priceOverride.currency` | `priceOverride.value`,\n    3\n  >\n): Promise<\n  NonNullablePaths<\n    ApplyBookingFeesToOrderResponse,\n    | `bookingFees`\n    | `bookingFees.${number}.cancellationFee.price.value`\n    | `bookingFees.${number}.cancellationFee.price.currency`\n    | `bookingFees.${number}.cancellationFee.status`\n    | `bookingFees.${number}.cancellationFee.trigger`\n    | `ecomOrderInfo.additionalFeePrice.amount`\n    | `ecomOrderInfo.additionalFeePrice.formattedAmount`,\n    6\n  > & {\n    __applicationErrorsType?: ApplyBookingFeesToOrderApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingIds: bookingIds,\n    priceOverride: options?.priceOverride,\n    businessNotification: options?.businessNotification,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsFeesV1BookingFee.applyBookingFeesToOrder(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingIds: '$[0]',\n          priceOverride: '$[1].priceOverride',\n          businessNotification: '$[1].businessNotification',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingIds', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ApplyBookingFeesToOrderOptions {\n  /**\n   * Custom price override for the additional fee that's added to the eCommerce\n   * order. The override mustn't exceed the sum of all booking fees. You must have\n   * the `OVERRIDE_BOOKING_FEE_PRICE` permission to use this property.\n   */\n  priceOverride?: Money;\n  /**\n   * Information about the message to the business and whether to send it if the\n   * booking fee application to the eCommerce order fails.\n   */\n  businessNotification?: BusinessNotification;\n}\n\n/**\n * Collects booking fees by charging the customer using the payment method that's\n * saved on the corresponding *eCommerce order*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))\n *\n *\n * <blockquote class=\"warning\">\n *\n * __Warning:__\n * Currently, there is no validation that prevents idempotent requests.\n * This means that your code must make sure to not charge customers multiple\n * times for the same booking fee. You could use\n * _List Transactions For Single Order_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/order-transactions/list-transactions-for-single-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/order-transactions/list-transactions-for-single-order))\n * to check which charges were made previously for an eCommerce order.\n *\n * </blockquote>\n *\n * An HTTP status of `200` means that all booking fees were successfully collected.\n * Any other HTPP status means that collection failed.\n *\n * Collects the order's `additionalFees.price.amount` that's related to the\n * booking fees. If there are multiple additional fees on the eCommerce order,\n * the amount that's collected differs from `priceSummary.totalAdditionalFees.amount`.\n *\n * Possible failure reasons include:\n * + The order's `status` isn't `APPROVED`.\n * + There is no payment method saved on the order.\n * + The order's `balanceSummary.balance.amount` is lower than the\n * `additionalFees.price.amount` to collect.\n * + The order's `additionalFeeId` doesn't belong to a Wix Bookings cancellation fee.\n * @param orderId - ID of the eCommerce order that includes the booking fees as a single\n * `additionalFee`.\n * @public\n * @requiredField options.additionalFeeId\n * @requiredField orderId\n * @param options - Options to use when collecting booking fees that have been applied to an eCommerce order.\n * @permissionId BOOKINGS.BOOKING_FEE_COLLECT\n * @fqn wix.bookings.fees.v1.BookingFees.CollectAppliedBookingFees\n */\nexport async function collectAppliedBookingFees(\n  orderId: string,\n  options?: NonNullablePaths<\n    CollectAppliedBookingFeesOptions,\n    `additionalFeeId`,\n    2\n  >\n): Promise<\n  NonNullablePaths<\n    CollectAppliedBookingFeesResponse,\n    `collectedPrice.amount` | `collectedPrice.formattedAmount`,\n    3\n  > & {\n    __applicationErrorsType?: CollectAppliedBookingFeesApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    orderId: orderId,\n    additionalFeeId: options?.additionalFeeId,\n    businessNotification: options?.businessNotification,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsFeesV1BookingFee.collectAppliedBookingFees(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          orderId: '$[0]',\n          additionalFeeId: '$[1].additionalFeeId',\n          businessNotification: '$[1].businessNotification',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['orderId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CollectAppliedBookingFeesOptions {\n  /**\n   * ID of the additional fee that's related to all given booking fees.\n   * @format GUID\n   */\n  additionalFeeId: string | null;\n  /**\n   * Information about whether to notify the business about failing to collect the\n   * booking fees from the customer and the message to send.\n   */\n  businessNotification?: BusinessNotification;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsFeesV1BookingFeesUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_booking-fees';\n\n/**\n * Retrieves booking fees by booking IDs.\n *\n *\n * Instead of returning `bookingFee` objects with the `cancellationFee.price.value`\n * set to `0.00`, the method doesn't return a `bookingFee` object for the relevant\n * booking IDs. For example, no `bookingFee` object is returned if the canceled\n * booking was free or if the booking was canceled before the start of the earliest\n * cancellation window with an associated fee.\n *\n * If the service's booking policy has been updated since the booking was created,\n * booking fees are calculated according to the *booking policy snapshot*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))\n * rather than the current version of the *policy*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).\n *\n * This method calculates the cancellation fee amount based on the time of the\n * call, you can't specify a time. Similarly, it calculates the cancellation fee\n * based on the number of participants who canceled, not a provided number.\n *\n * A `cancellationFee.status` of `PREVIEW` indicates that the booking fee is\n * informational only; the customer isn't required to pay it. When the `status` is\n * set to `UNKNOWN_STATUS` there is no eCommerce order associated with the booking. For\n * example, if a custom checkout was used for the booking instead of the\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n *\n * If multiple events would trigger the calculation of a booking fee, for example\n * when a booking is first canceled and then marked as not attended, Wix calculates\n * the booking fee based on the first trigger. In this example, the booking\n * cancellation.\n */\nexport function listBookingFeesByBookingIds(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listBookingFeesByBookingIds({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.fees.v1.booking_fee',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.fees.v1.BookingFees.ListBookingFeesByBookingIds',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsFeesV1BookingFeesUrl({\n        protoPath: '/v1/booking-fees',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __listBookingFeesByBookingIds;\n}\n\n/**\n * Applies booking fees to an _eCommerce order_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n *\n * The booking fees are added as a single additional fee to the eCommerce order.\n * The order's `additionalFee.lineItemIds` array is set to the list of corresponding\n * booking IDs. By default, the `additionalFee.price.amount` is the sum of all\n * booking fee prices. But you may provide a `priceOverride` instead. The override\n * price can't be higher than the sum of all booking fees.\n *\n * Apply Booking Fees to Order also updates the prices of all affected line items\n * in the relevant eCommerce order to zero. After a cancellation fee is applied\n * to an eCommerce order, the cancellation fee's `price.value` is updated to `0.00`\n * and its trigger is set to `UNKNOWN_TRIGGER`. You can retrieve the fee amount\n * from the corresponding `additionalFee` object of the eCommerce order with\n * _Search Orders_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/search-orders) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/search-orders)).\n *\n * If you apply multiple booking fees to an eCommerce order, they either all fail or\n * all succeed together. For example, the call fails if the booking fees are associated\n * with different eCommmerce orders.\n */\nexport function applyBookingFeesToOrder(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __applyBookingFeesToOrder({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.fees.v1.booking_fee',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.fees.v1.BookingFees.ApplyBookingFeesToOrder',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsFeesV1BookingFeesUrl({\n        protoPath: '/v1/booking-fees/apply',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __applyBookingFeesToOrder;\n}\n\n/**\n * Collects booking fees by charging the customer using the payment method that's\n * saved on the corresponding *eCommerce order*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))\n *\n *\n * <blockquote class=\"warning\">\n *\n * __Warning:__\n * Currently, there is no validation that prevents idempotent requests.\n * This means that your code must make sure to not charge customers multiple\n * times for the same booking fee. You could use\n * _List Transactions For Single Order_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/order-transactions/list-transactions-for-single-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/order-transactions/list-transactions-for-single-order))\n * to check which charges were made previously for an eCommerce order.\n *\n * </blockquote>\n *\n * An HTTP status of `200` means that all booking fees were successfully collected.\n * Any other HTPP status means that collection failed.\n *\n * Collects the order's `additionalFees.price.amount` that's related to the\n * booking fees. If there are multiple additional fees on the eCommerce order,\n * the amount that's collected differs from `priceSummary.totalAdditionalFees.amount`.\n *\n * Possible failure reasons include:\n * + The order's `status` isn't `APPROVED`.\n * + There is no payment method saved on the order.\n * + The order's `balanceSummary.balance.amount` is lower than the\n * `additionalFees.price.amount` to collect.\n * + The order's `additionalFeeId` doesn't belong to a Wix Bookings cancellation fee.\n */\nexport function collectAppliedBookingFees(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __collectAppliedBookingFees({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.fees.v1.booking_fee',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.fees.v1.BookingFees.CollectAppliedBookingFees',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsFeesV1BookingFeesUrl({\n        protoPath: '/v1/booking-fees/collect',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __collectAppliedBookingFees;\n}\n"], "mappings": ";AAAA,SAAS,kBAAkB,yBAAyB;AACpD;AAAA,EACE;AAAA,EACA;AAAA,OACK;;;ACJP,SAAS,yBAAyB;AAClC,SAAS,kBAAkB;AAI3B,SAAS,uCACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAkCd,SAAS,4BACd,SAC4B;AAC5B,WAAS,8BAA8B,EAAE,KAAK,GAAQ;AACpD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,uCAAuC;AAAA,QAC1C,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAyBO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,uCAAuC;AAAA,QAC1C,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAkCO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,uCAAuC;AAAA,QAC1C,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADzHO,IAAK,mBAAL,kBAAKA,sBAAL;AAEL,EAAAA,kBAAA,oBAAiB;AAEjB,EAAAA,kBAAA,aAAU;AAEV,EAAAA,kBAAA,8BAA2B;AAE3B,EAAAA,kBAAA,sBAAmB;AART,SAAAA;AAAA,GAAA;AAoBL,IAAK,UAAL,kBAAKC,aAAL;AAEL,EAAAA,SAAA,qBAAkB;AAElB,EAAAA,SAAA,kBAAe;AAEf,EAAAA,SAAA,sBAAmB;AANT,SAAAA;AAAA,GAAA;AAgXL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AA+JZ,eAAsBC,6BACpB,SAaA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,YAAY,SAAS;AAAA,IACrB,wBAAwB,SAAS;AAAA,EACnC,CAAC;AAED,QAAM,UACkC,4BAA4B,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,wBAAwB;AAAA,QAC1B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAgDA,eAAsBC,yBACpB,YACA,SAmBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,eAAe,SAAS;AAAA,IACxB,sBAAsB,SAAS;AAAA,EACjC,CAAC;AAED,QAAM,UACkC,wBAAwB,OAAO;AAEvE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,sBAAsB;AAAA,QACxB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc,SAAS;AAAA,IAC1B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAwDA,eAAsBC,2BACpB,SACA,SAaA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,iBAAiB,SAAS;AAAA,IAC1B,sBAAsB,SAAS;AAAA,EACjC,CAAC;AAED,QAAM,UACkC,0BAA0B,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,SAAS;AAAA,UACT,iBAAiB;AAAA,UACjB,sBAAsB;AAAA,QACxB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,WAAW,SAAS;AAAA,IACvB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;", "names": ["BookingFeeStatus", "<PERSON><PERSON>", "WebhookIdentityType", "listBookingFeesByBookingIds", "applyBookingFeesToOrder", "collectAppliedBookingFees"]}