import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { ListAvailabilityTimeSlotsOptions, ListAvailabilityTimeSlotsResponse, ListAvailabilityTimeSlotsApplicationErrors, Location, GetAvailabilityTimeSlotOptions, GetAvailabilityTimeSlotResponse, GetAvailabilityTimeSlotApplicationErrors } from './index.typings.js';
export { AvailableResources, BookingPolicyViolations, CursorPaging, CursorPagingMetadata, Cursors, CustomerChoices, EventInfo, GetAvailabilityTimeSlotRequest, GetAvailabilityTimeSlotRequestCustomerChoices, GetEventTimeSlotRequest, GetEventTimeSlotResponse, GetMultiServiceAvailabilityTimeSlotRequest, GetMultiServiceAvailabilityTimeSlotResponse, ListAvailabilityTimeSlotsRequest, ListEventTimeSlotsRequest, ListEventTimeSlotsResponse, ListMultiServiceAvailabilityTimeSlotsRequest, ListMultiServiceAvailabilityTimeSlotsResponse, LocationType, LocationTypeWithLiterals, NestedTimeSlot, NonBookableReasons, Resource, ResourceType, Service, TimeSlot, V2CustomerChoices, WaitingList } from './index.typings.js';

declare function listAvailabilityTimeSlots$1(httpClient: HttpClient): ListAvailabilityTimeSlotsSignature;
interface ListAvailabilityTimeSlotsSignature {
    /**
     * Retrieves a list of appointment time slots that match the provided filters.
     *
     *
     * ## Defaults
     *
     * List Availability Time Slots uses the following defaults:
     *
     * - Sorts time slots by `localStartDate` in ascending order.
     * - `cursorPaging.limit` set to `1000`.
     * - Returns both bookable and un-bookable time slots.
     * - If `bookable` filter isn't specified, bookable slots are returned first.
     * - `fromLocalDate` is set to the current time.
     *
     * ## Filtering behavior
     *
     * The method automatically filters time slots:
     *
     * - **Past time slots**: Only time slots starting from the specified `fromLocalDate` are included. When you omit `fromLocalDate`, Wix Bookings uses the current time as the default.
     * - **Resource availability**: Only time slots with at least some available resources are returned.
     *
     * ## Service type limitations
     *
     * Only appointment-based services are supported when calling List Availability Time Slots.
     *
     * To retrieve class availability, you can call List Event Time Slots([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/event-time-slots/list-event-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-event-time-slots)).
     *
     * To retrieve course availability, you can follow the End-to-End Booking Flow for course([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flow#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).
     *
     * ## Available resources
     *
     * Time slots aren't returned when they have no available resources.
     *
     * Each time slot includes details for up to 10 available resources.
     * If there are more than 10 resources, you can call Get Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot)) and filter by the resource Type ID to get details for the remaining resources.
     *
     * ## Business hours exception
     *
     * Wix Bookings disregards business opening hours when all of the following conditions are met:
     *
     * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.
     * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.
     * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).
     * <-!-- 4. The service doesn't support add-ons ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/add-ons/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/add-ons/introduction)). -->
     *
     * In these cases, the working hours of the relevant staff member are used for availability calculation.
     */
    (options?: ListAvailabilityTimeSlotsOptions): Promise<NonNullablePaths<ListAvailabilityTimeSlotsResponse, `timeSlots` | `timeSlots.${number}.location.locationType`, 5> & {
        __applicationErrorsType?: ListAvailabilityTimeSlotsApplicationErrors;
    }>;
}
declare function getAvailabilityTimeSlot$1(httpClient: HttpClient): GetAvailabilityTimeSlotSignature;
interface GetAvailabilityTimeSlotSignature {
    /**
     * Retrieves detailed information about a specific appointment time slot.
     *
     *
     * Call this method to get complete resource availability after finding a suitable slot with List Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).
     *
     * ## Defaults
     *
     * Get Availability Time Slot uses the following defaults:
     *
     * - Returns all available resources unless filtered by `resourceIds` or `includeResourceTypeIds`.
     * - Includes full booking status and capacity details
     *
     * ## Service type limitations
     *
     * Only appointment-based services are supported when calling Get Availability Time Slot.
     *
     * To retrieve class session availability, you can call Get Event Time Slot ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot)).
     *
     * To retrieve course availability, you can follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flows#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).
     *
     * ## Business hours exception
     *
     * Wix Bookings disregards business opening hours when all of the following conditions are met:
     *
     * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.
     * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.
     * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).
     *
     * In these cases, the working hours of the relevant staff member are used instead for availability calculation.
     * @param - Service ID of the time slot.
     * You must specify the ID of an appointment-based service.
     * @param - Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @param - Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @param - Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @param - Location to filter time slots by.
     *
     * For business locations, you must specify a location ID.
     * When specifying a location ID, all other location field filters are ignored.
     */
    (serviceId: string, localStartDate: string, localEndDate: string, timeZone: string, location: Location, options?: GetAvailabilityTimeSlotOptions): Promise<NonNullablePaths<GetAvailabilityTimeSlotResponse, `timeSlot.location.locationType` | `timeSlot.availableResources` | `timeSlot.nestedTimeSlots` | `timeSlot.nestedTimeSlots.${number}.serviceId` | `timeSlot.nestedTimeSlots.${number}.localStartDate` | `timeSlot.nestedTimeSlots.${number}.localEndDate`, 5> & {
        __applicationErrorsType?: GetAvailabilityTimeSlotApplicationErrors;
    }>;
}

declare const listAvailabilityTimeSlots: MaybeContext<BuildRESTFunction<typeof listAvailabilityTimeSlots$1> & typeof listAvailabilityTimeSlots$1>;
declare const getAvailabilityTimeSlot: MaybeContext<BuildRESTFunction<typeof getAvailabilityTimeSlot$1> & typeof getAvailabilityTimeSlot$1>;

export { GetAvailabilityTimeSlotApplicationErrors, GetAvailabilityTimeSlotOptions, GetAvailabilityTimeSlotResponse, ListAvailabilityTimeSlotsApplicationErrors, ListAvailabilityTimeSlotsOptions, ListAvailabilityTimeSlotsResponse, Location, getAvailabilityTimeSlot, listAvailabilityTimeSlots };
