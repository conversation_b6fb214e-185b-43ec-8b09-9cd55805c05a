{"version": 3, "sources": ["../../../meta.ts", "../../../src/bookings-calendar-v2-external-calendar-external-calendars.http.ts", "../../../src/bookings-calendar-v2-external-calendar-external-calendars.meta.ts"], "sourcesContent": ["export * from './src/bookings-calendar-v2-external-calendar-external-calendars.meta.js';\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'api._api_base_domain_': [\n      {\n        srcPath: '/external-calendar-2',\n        destPath: '',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/external-calendar',\n        destPath: '/v2/external-calendar',\n      },\n      {\n        srcPath: '/_api/bookings/v2/external-calendars',\n        destPath: '/v2/external-calendars',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/external-calendars',\n        destPath: '/v2/external-calendars',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_external-calendars';\n\n/**\n * Retrieves a list of external calendar providers supported on the Wix site.\n *\n *\n * The list of external calendar providers includes:\n *\n * + External calendar providers that are supported by default, such as Google, Apple, and Microsoft.\n * + External calenders for which the site owner has enabled integration by installing an app.\n *\n * For each provider, check `features.connectMethods` to find out whether to use\n * _Connect By Credentials_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n * or _Connect By OAuth_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n * to establish a connection.\n */\nexport function listProviders(payload: object): RequestOptionsFactory<any> {\n  function __listProviders({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListProviders',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        { protoPath: '/v2/external-calendars/providers', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n            {\n              protoPath: '/v2/external-calendars/providers',\n              data: payload,\n              host,\n            }\n          ),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __listProviders;\n}\n\n/**\n * Retrieves an external calendar connection by ID.\n *\n *\n * The `syncConfig` property contains configuration details about event import\n * from and event export to the external calendar.\n */\nexport function getConnection(payload: object): RequestOptionsFactory<any> {\n  function __getConnection({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.GetConnection',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath: '/v2/external-calendars/connections/{connectionId}',\n          data: payload,\n          host,\n        }\n      ),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n            {\n              protoPath: '/v2/external-calendars/connections/{connectionId}',\n              data: payload,\n              host,\n            }\n          ),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __getConnection;\n}\n\n/**\n * Retrieves a list of external calendar connections.\n *\n *\n * ## Filter\n *\n * You can filter by *schedule ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n *\n * ## Partial success\n *\n * By default, the call fails if details for at least 1 connection can't be\n * retrieved from the external provider. However, you can specify\n * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n * least 1 connection can be retrieved.\n */\nexport function listConnections(payload: object): RequestOptionsFactory<any> {\n  function __listConnections({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListConnections',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        { protoPath: '/v2/external-calendars/connections', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __listConnections;\n}\n\n/**\n * Connects a *Wix schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to an external calendar account following [OAuth authorization protocol](https://oauth.net/2/).\n *\n *\n * ## Authorization flow\n *\n * The full authorization flow looks like this:\n *\n * 1. Call *Connect By OAuth* with the appropriate parameters.\n * 2. Redirect the owner of the external calendar account to the returned\n * `oAUthUrl`.\n * 3. The account owner authorizes access.\n * 4. The account owner is automatically redirected to the URL you've provided\n * in `redirectUrl` of the *Connect By OAuth* call.\n * 5. Save the new `connectionId`. You find it as a query parameter in the URL\n * to which the account owner is redirected.\n *\n * See *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/sample-flows#connect-an-external-calendar-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/sample-flows#connect-an-external-calendar-by-oauth))\n * for more details.\n *\n * ## Failure consequences\n *\n * If the attempt to connect fails, the account owner is still redirected to\n * the URL you specify in `redirectUrl`, but it incudes an `error` query\n * parameter set to 1 of the following values:\n *\n * - `reject`: The external calendar owner has rejected the authorization request.\n * - `unsupported`: Connecting to the user's external account type is not supported by the provider.\n * - `internal`: An error unrelated to the client or the request that prevents the server from fulfilling the request.\n *\n * ## Next steps\n *\n * After connecting an external calendar account to a Wix schedule, you can do\n * the following.\n *\n * ### Check the 2-way-sync settings\n *\n * Call *List Connections*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n * and filter by the relevant schedule ID.\n *\n * If needed, call *Update Sync Config*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n * to adjust the syncing configuration.\n *\n * ### Retrieve external calendar events\n *\n * Once a connection is successfully created, you can call *List Events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to obtain an up-to-date list of events in the connected external calendars.\n *\n * ## When to call Connect By Credentials instead\n *\n * You can call *Connect By Credentials*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n * if:\n *\n * + The external calendar provider doesn't support OAuth.\n * + You don't want to redirect the account owner.\n *\n * Call *List Providers*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n * for a complete list of external calendar providers supported on the Wix site,\n * including which authorization methods they support.\n */\nexport function connectByOAuth(payload: object): RequestOptionsFactory<any> {\n  function __connectByOAuth({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByOAuth',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath: '/v2/external-calendars/connections:connectByOAuth',\n          data: payload,\n          host,\n        }\n      ),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __connectByOAuth;\n}\n\n/**\n * Connects a *Wix schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to an external calendar account using credentials.\n *\n *\n * ## Next steps\n *\n * After connecting an external calendar account to a Wix schedule, you can do\n * the following.\n *\n * ### Check the 2-way-sync settings\n *\n * Call *List Connections*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n * and filter by the relevant schedule ID.\n *\n * If needed, call *Update Sync Config*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n * to adjust the syncing configuration.\n *\n * ### Retrieve external calendar events\n *\n * Once a connection is successfully created, you can call *List Events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to obtain an up-to-date list of events in the connected external calendars.\n *\n * ## When to call Connect By OAuth instead\n *\n * You could call *Connect By OAuth*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n * if:\n *\n * + The external calendar provider doesn't support authorization by credentials.\n * + You don't have access to the external calendar account credentials.\n * + You want the account owner to approve the connection.\n *\n * Call *List Providers*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n * for a complete list of external calendar providers supported on the Wix site,\n * including which authorization methods they support.\n */\nexport function connectByCredentials(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __connectByCredentials({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByCredentials',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath: '/v2/external-calendars/connections:connectByCredentials',\n          data: payload,\n          host,\n        }\n      ),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __connectByCredentials;\n}\n\n/** Retrieves details about the external calendar accounts associated with the specified connection. */\nexport function listCalendars(payload: object): RequestOptionsFactory<any> {\n  function __listCalendars({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListCalendars',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath:\n            '/v2/external-calendars/connections/{connectionId}/calendars',\n          data: payload,\n          host,\n        }\n      ),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n            {\n              protoPath:\n                '/v2/external-calendars/connections/{connectionId}/calendars',\n              data: payload,\n              host,\n            }\n          ),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __listCalendars;\n}\n\n/**\n * Updates the import and export settings for an external calendar connection's\n * events.\n *\n *\n * A connection's `syncConfig` object determines:\n *\n * + Whether events from 1 or more accounts belonging to the external\n * calendar are imported to the connected *Wix schedule*.\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n * If importing events is enabled, you can call _List Events_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to retrieve external calendar events.\n * + Whether events from the Wix schedule are exported to 1 or more accounts\n * belonging to the external calendar.\n *\n * To check the current import and export settings, you can call\n * _Get Connection_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/get-connection) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/get-connection))\n * or _List Connections_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections)).\n */\nexport function updateSyncConfig(payload: object): RequestOptionsFactory<any> {\n  function __updateSyncConfig({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'fieldMask' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'PATCH' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.UpdateSyncConfig',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath:\n            '/v2/external-calendars/connections/{connectionId}/sync-config',\n          data: serializedData,\n          host,\n        }\n      ),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __updateSyncConfig;\n}\n\n/**\n * Disconnects a Wix schedule from an external calendar and deletes all Wix\n * calendar *events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * from the external calendar.\n *\n *\n * When an external calendar is disconnected, the connection's `status`\n * changes to `DISCONNECTED`.\n *\n * After disconnecting, _List Events_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * no longer returns events from the disconnected external calendar.\n */\nexport function disconnect(payload: object): RequestOptionsFactory<any> {\n  function __disconnect({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.Disconnect',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath:\n            '/v2/external-calendars/connections/{connectionId}/disconnect',\n          data: payload,\n          host,\n        }\n      ),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __disconnect;\n}\n\n/**\n * Retrieves a list of events from all external calendar accounts, based on\n * the provided filtering and paging.\n *\n *\n * ## Filters\n *\n * You must filter by specifying both `from` and `to` dates, unless you specify\n * `cursorPaging.cursor`.\n *\n * Additionally, you can specify `scheduleIds` or `userIds` to further limit\n * which events are returned. By default, events related to all schedules and\n * Wix users are returned.\n *\n * ## Sorting\n *\n * Returned events are sorted by start date in ascending order. You can't\n * adjust the sorting.\n *\n * ## Personal data\n *\n * By default, the following `event` fields aren't returned:\n *\n * + `calendarName`.\n * + `title`.\n * + `scheduleOwnerName`.\n *\n * You can retrieve these fields, by specifying `{\"fieldsets\": \"OWN_PI\"}`.\n *\n * ## Partial success\n *\n * By default, the call fails if events for at least 1 connection can't be\n * retrieved from an external provider. However, you can specify\n * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n * least 1 connection can be retrieved.\n */\nexport function listEvents(payload: object): RequestOptionsFactory<any> {\n  function __listEvents({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListEvents',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        { protoPath: '/v2/external-calendars/events', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __listEvents;\n}\n", "import * as ambassadorWixBookingsCalendarV2ExternalCalendar from './bookings-calendar-v2-external-calendar-external-calendars.http.js';\nimport * as ambassadorWixBookingsCalendarV2ExternalCalendarTypes from './bookings-calendar-v2-external-calendar-external-calendars.types.js';\nimport * as ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes from './bookings-calendar-v2-external-calendar-external-calendars.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function listProviders(): __PublicMethodMetaInfo<\n  'GET',\n  {},\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListProvidersRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListProvidersRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListProvidersResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListProvidersResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listProviders(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/external-calendars/providers',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getConnection(): __PublicMethodMetaInfo<\n  'GET',\n  { connectionId: string },\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.GetConnectionRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.GetConnectionRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.GetConnectionResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.GetConnectionResponse\n> {\n  const payload = { connectionId: ':connectionId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.getConnection(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/external-calendars/connections/{connectionId}',\n    pathParams: { connectionId: 'connectionId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function listConnections(): __PublicMethodMetaInfo<\n  'GET',\n  {},\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListConnectionsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListConnectionsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListConnectionsResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListConnectionsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listConnections(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/external-calendars/connections',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function connectByOAuth(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ConnectByOAuthRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ConnectByOAuthRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ConnectByOAuthResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ConnectByOAuthResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.connectByOAuth(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/external-calendars/connections:connectByOAuth',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function connectByCredentials(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ConnectByCredentialsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ConnectByCredentialsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ConnectByCredentialsResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ConnectByCredentialsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.connectByCredentials(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/external-calendars/connections:connectByCredentials',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function listCalendars(): __PublicMethodMetaInfo<\n  'GET',\n  { connectionId: string },\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListCalendarsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListCalendarsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListCalendarsResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListCalendarsResponse\n> {\n  const payload = { connectionId: ':connectionId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listCalendars(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/external-calendars/connections/{connectionId}/calendars',\n    pathParams: { connectionId: 'connectionId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateSyncConfig(): __PublicMethodMetaInfo<\n  'PATCH',\n  { connectionId: string },\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.UpdateSyncConfigRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.UpdateSyncConfigRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.UpdateSyncConfigResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.UpdateSyncConfigResponse\n> {\n  const payload = { connectionId: ':connectionId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.updateSyncConfig(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PATCH',\n    path: '/v2/external-calendars/connections/{connectionId}/sync-config',\n    pathParams: { connectionId: 'connectionId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function disconnect(): __PublicMethodMetaInfo<\n  'POST',\n  { connectionId: string },\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.DisconnectRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.DisconnectRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.DisconnectResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.DisconnectResponse\n> {\n  const payload = { connectionId: ':connectionId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.disconnect(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/external-calendars/connections/{connectionId}/disconnect',\n    pathParams: { connectionId: 'connectionId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function listEvents(): __PublicMethodMetaInfo<\n  'GET',\n  {},\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListEventsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListEventsRequest,\n  ambassadorWixBookingsCalendarV2ExternalCalendarUniversalTypes.ListEventsResponse,\n  ambassadorWixBookingsCalendarV2ExternalCalendarTypes.ListEventsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listEvents(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/external-calendars/events',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,8BAAAA;AAAA,EAAA,sBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,wBAAAC;AAAA;AAAA;;;ACAA,0BAAkC;AAClC,wBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,qEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAkBd,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,oCAAoC,MAAM,SAAS,KAAK;AAAA,MACvE;AAAA,MACA,YAAQ,uCAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK;AAAA,YACH;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,YAAQ,uCAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAQ,uCAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK;AAAA,YACH;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,YAAQ,uCAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAkBO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,sCAAsC,MAAM,SAAS,KAAK;AAAA,MACzE;AAAA,MACA,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsEO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA4CO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WACE;AAAA,UACF,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAQ,uCAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK;AAAA,YACH;AAAA,cACE,WACE;AAAA,cACF,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,YAAQ,uCAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAwBO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,YAAY,CAAC;AAAA,MAC/B;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WACE;AAAA,UACF,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgBO,SAAS,WAAW,SAA6C;AACtE,WAAS,aAAa,EAAE,KAAK,GAAQ;AACnC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WACE;AAAA,UACF,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsCO,SAAS,WAAW,SAA6C;AACtE,WAAS,aAAa,EAAE,KAAK,GAAQ;AACnC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,iCAAiC,MAAM,SAAS,KAAK;AAAA,MACpE;AAAA,MACA,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AChfO,SAASC,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC4C,cAAc,OAAO;AAEvE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,EAAE,cAAc,gBAAgB;AAEhD,QAAM,oBAC4C,cAAc,OAAO;AAEvE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,cAAc,eAAe;AAAA,IAC3C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,mBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC4C,gBAAgB,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC4C,eAAe,OAAO;AAExE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,wBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC4C;AAAA,IAC9C;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,EAAE,cAAc,gBAAgB;AAEhD,QAAM,oBAC4C,cAAc,OAAO;AAEvE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,cAAc,eAAe;AAAA,IAC3C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,oBAOd;AACA,QAAM,UAAU,EAAE,cAAc,gBAAgB;AAEhD,QAAM,oBAC4C,iBAAiB,OAAO;AAE1E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,cAAc,eAAe;AAAA,IAC3C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,cAOd;AACA,QAAM,UAAU,EAAE,cAAc,gBAAgB;AAEhD,QAAM,oBAC4C,WAAW,OAAO;AAEpE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,cAAc,eAAe;AAAA,IAC3C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,cAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC4C,WAAW,OAAO;AAEpE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["connectByCredentials", "connectByOAuth", "disconnect", "getConnection", "listCalendars", "listConnections", "listEvents", "listProviders", "updateSyncConfig", "import_rest_modules", "listProviders", "getConnection", "listConnections", "connectByOAuth", "connectByCredentials", "listCalendars", "updateSyncConfig", "disconnect", "listEvents"]}