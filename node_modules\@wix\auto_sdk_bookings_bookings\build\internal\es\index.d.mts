import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { ConfirmOrDeclineBookingOptions, ConfirmOrDeclineBookingResponse, ConfirmOrDeclineBookingApplicationErrors, BulkConfirmOrDeclineBookingRequestBookingDetails, BulkConfirmOrDeclineBookingOptions, BulkConfirmOrDeclineBookingResponse, BulkConfirmOrDeclineBookingApplicationErrors, Booking, CreateBookingOptions, CreateBookingResponse, CreateBookingApplicationErrors, CreateBookingInfo, BulkCreateBookingOptions, BulkCreateBookingResponse, BulkCreateBookingApplicationErrors, V2Slot, RescheduleBookingOptions, RescheduleBookingResponse, RescheduleBookingApplicationErrors, ConfirmBookingOptions, ConfirmBookingResponse, ConfirmBookingApplicationErrors, SetBookingSubmissionIdResponse, SetBookingSubmissionIdApplicationErrors, UpdateExtendedFieldsOptions, UpdateExtendedFieldsResponse, UpdateExtendedFieldsApplicationErrors, DeclineBookingOptions, DeclineBookingResponse, DeclineBookingApplicationErrors, CancelBookingOptions, CancelBookingResponse, CancelBookingApplicationErrors, UpdateNumberOfParticipantsOptions, UpdateNumberOfParticipantsResponse, UpdateNumberOfParticipantsApplicationErrors, MarkBookingAsPendingOptions, MarkBookingAsPendingResponse, MarkBookingAsPendingApplicationErrors, CreateMultiServiceBookingOptions, CreateMultiServiceBookingResponse, CreateMultiServiceBookingApplicationErrors, RescheduleBookingInfo, RescheduleMultiServiceBookingOptions, RescheduleMultiServiceBookingResponse, RescheduleMultiServiceBookingApplicationErrors, GetMultiServiceBookingAvailabilityResponse, GetMultiServiceBookingAvailabilityApplicationErrors, CancelMultiServiceBookingOptions, CancelMultiServiceBookingResponse, CancelMultiServiceBookingApplicationErrors, MarkMultiServiceBookingAsPendingOptions, MarkMultiServiceBookingAsPendingResponse, MarkMultiServiceBookingAsPendingApplicationErrors, ConfirmMultiServiceBookingOptions, ConfirmMultiServiceBookingResponse, ConfirmMultiServiceBookingApplicationErrors, DeclineMultiServiceBookingOptions, DeclineMultiServiceBookingResponse, DeclineMultiServiceBookingApplicationErrors, BulkGetMultiServiceBookingAllowedActionsResponse, MultiServiceBooking, AddBookingsToMultiServiceBookingOptions, AddBookingsToMultiServiceBookingResponse, AddBookingsToMultiServiceBookingApplicationErrors, RemoveBookingsFromMultiServiceBookingOptions, RemoveBookingsFromMultiServiceBookingResponse, RemoveBookingsFromMultiServiceBookingApplicationErrors, BookingCanceledEnvelope, BookingConfirmedEnvelope, BookingCreatedEnvelope, BookingDeclinedEnvelope, BookingNumberOfParticipantsUpdatedEnvelope, BookingRescheduledEnvelope, BookingUpdatedEnvelope } from './index.typings.mjs';
export { ActionEvent, Actor, ActorWithLiterals, AddBookingsToMultiServiceBookingRequest, Address, AddressLocation, AddressStreetOneOf, AllowedActions, ApplicationError, AvailableResources, BaseEventMetadata, BookedAddOn, BookedEntity, BookedEntityItemOneOf, BookedResource, BookedSchedule, BookedSlot, BookingCanceled, BookingChanged, BookingConfirmed, BookingDeclined, BookingDetails, BookingFormFilled, BookingIdAndRevision, BookingInfo, BookingMarkedAsPending, BookingParticipantsInfoOneOf, BookingPolicySettings, BookingPolicyViolations, BookingRescheduled, BookingRescheduledPreviousParticipantsInfoOneOf, BookingResult, BookingSource, BookingStatus, BookingStatusWithLiterals, BulkActionMetadata, BulkBookingResult, BulkCalculateAllowedActionsRequest, BulkCalculateAllowedActionsResponse, BulkCalculateAllowedActionsResult, BulkConfirmOrDeclineBookingRequest, BulkCreateBookingRequest, BulkGetMultiServiceBookingAllowedActionsRequest, BulkRescheduleBookingRequest, BulkRescheduleBookingRequestBooking, BulkRescheduleBookingResponse, BulkUpdateBookedScheduleRequest, BulkUpdateBookedScheduleResponse, BulkUpdateBookingRequest, BulkUpdateBookingResponse, CancelBookingFlowControlSettings, CancelBookingRequest, CancelBookingRequestFlowControlSettings, CancelMultiServiceBookingRequest, Clash, CommonIdentificationData, CommonIdentificationDataIdOneOf, ConfirmBookingFlowControlSettings, ConfirmBookingRequest, ConfirmMultiServiceBookingRequest, ConfirmOrDeclineBookingRequest, ConfirmRequest, ConfirmResponse, ConsistentQueryBookingsRequest, ConsistentQueryBookingsResponse, ContactDetails, CountBookingsRequest, CountBookingsResponse, CreateBookingFlowControlSettings, CreateBookingRequest, CreateBookingRequestFlowControlSettings, CreateMultiServiceBookingRequest, CursorPaging, Cursors, CustomFormField, DeclineBookingFlowControlSettings, DeclineBookingRequest, DeclineMultiServiceBookingRequest, DomainEvent, DomainEventBodyOneOf, Duration, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, EventMetadata, ExtendedFields, FlowControlSettings, GetMultiServiceBookingAvailabilityRequest, GetMultiServiceBookingAvailabilityResponseBookingInfo, GetMultiServiceBookingRequest, GetMultiServiceBookingResponse, GetScheduleAvailabilityRequest, GetScheduleAvailabilityResponse, GetSlotAvailabilityRequest, GetSlotAvailabilityResponse, IdentificationData, IdentificationDataIdOneOf, IdentificationDataIdentityType, IdentificationDataIdentityTypeWithLiterals, IdentityType, IdentityTypeWithLiterals, ItemMetadata, LegacyCreateBookingRequest, LegacyCreateBookingResponse, Location, LocationLocationType, LocationLocationTypeWithLiterals, LocationType, LocationTypeWithLiterals, MarkBookingAsPendingFlowControlSettings, MarkBookingAsPendingRequest, MarkMultiServiceBookingAsPendingRequest, MaskedBooking, MessageEnvelope, MigrationCheckIfClashesWithBlockedTimeRequest, MigrationCheckIfClashesWithBlockedTimeResponse, MsidAndBookingId, MultiServiceBookingInfo, MultiServiceBookingMetadata, MultiServiceBookingType, MultiServiceBookingTypeWithLiterals, NumberOfParticipantsUpdated, NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf, Paging, PagingMetadataV2, ParticipantChoices, ParticipantNotification, PaymentStatus, PaymentStatusWithLiterals, Platform, PlatformWithLiterals, QueryBookingsRequest, QueryBookingsResponse, QueryV2, QueryV2PagingMethodOneOf, RemoveBookingsFromMultiServiceBookingRequest, RescheduleBookingFlowControlSettings, RescheduleBookingInfoParticipantsInfoOneOf, RescheduleBookingOptionsParticipantsInfoOneOf, RescheduleBookingRequest, RescheduleBookingRequestFlowControlSettings, RescheduleBookingRequestParticipantsInfoOneOf, RescheduleMultiServiceBookingRequest, RestoreInfo, ScheduleAvailability, SelectedPaymentOption, SelectedPaymentOptionWithLiterals, ServiceChoice, ServiceChoiceChoiceOneOf, ServiceChoices, SetBookingFormAndSubmissionIdRequest, SetBookingFormAndSubmissionIdRequestCreatedByOneOf, SetBookingFormAndSubmissionIdResponse, SetBookingSessionIdRequest, SetBookingSessionIdResponse, SetBookingSubmissionIdRequest, Slot, SlotAvailability, SlotBookings, SlotLocation, SlotResource, SlotSlotResource, SortOrder, SortOrderWithLiterals, Sorting, StreetAddress, Subdivision, UpdateBookingRequest, UpdateBookingResponse, UpdateExtendedFieldsRequest, UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf, UpdateNumberOfParticipantsRequest, UpdateNumberOfParticipantsRequestParticipantsInfoOneOf, V2CancelBookingRequest, V2CancelBookingResponse, V2ConfirmBookingRequest, V2ConfirmBookingResponse, V2CreateBookingRequest, V2CreateBookingRequestBookableItemOneOf, V2CreateBookingRequestParticipantsInfoOneOf, V2CreateBookingResponse, V2DeclineBookingRequest, V2DeclineBookingResponse, V2RescheduleBookingRequest, V2RescheduleBookingRequestParticipantsInfoOneOf, V2RescheduleBookingResponse, V2UpdateNumberOfParticipantsRequest, V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf, V2UpdateNumberOfParticipantsResponse, ValueType, ValueTypeWithLiterals, WaitingList, WebhookIdentityType, WebhookIdentityTypeWithLiterals, WebhooksIdentificationData, WebhooksIdentificationDataIdOneOf } from './index.typings.mjs';

declare function confirmOrDeclineBooking$1(httpClient: HttpClient): ConfirmOrDeclineBookingSignature;
interface ConfirmOrDeclineBookingSignature {
    /**
     * Updates the booking `status` to `CONFIRMED`, `PENDING`, or `DECLINED` based
     * on the `paymentStatus` you provide, double booking conflicts, and whether
     * the service requires business approval.
     *
     * ## eCommerce checkout restriction
     *
     * Call this method only when using a custom checkout page. Don't
     * call it when using a *Wix eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     * In such cases, Wix automatically updates the booking status based on
     * the `paymentStatus` of the corresponding *Wix eCommerce order*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
     *
     * ## New booking status
     *
     * The booking `status` is set to `DECLINED` if both of the following conditions
     * are met:
     * + You provide `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT` as payment status.
     * + There is a double booking conflict.
     *
     * If only one or none of these conditions is met, `status` is set to `PENDING`
     * or `CONFIRMED` depending on whether the service requires business approval.
     *
     * ## Double bookings
     *
     * If there is a double booking conflict, but the booking has already been at least
     * partially paid, the method still marks the booking as `PENDING` or `CONFIRMED`.
     * Then, it also sets `doubleBooked` to `true`.
     *
     * ## Admin overwrites
     *
     * There are small but important differences in confirmation behavior if the
     * booking was created with special `flowControlSettings`:
     * + If the booking's `flowControlSettings.skipAvailabilityValidation` was set to
     * `true`, the booking is never declined regardless of double booking conflicts.
     * Instead, it's marked `CONFIRMED` or `PENDING`, depending on whether the
     * service requires business approval.
     * + If the booking's `flowControlSettings.skipBusinessConfirmation` was set to
     * `true`, the booking skips `PENDING` status and is marked `CONFIRMED`
     * immediately.
     * @param - ID of the booking to confirm or decline.
     */
    (bookingId: string, options?: ConfirmOrDeclineBookingOptions): Promise<NonNullablePaths<ConfirmOrDeclineBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: ConfirmOrDeclineBookingApplicationErrors;
    }>;
}
declare function bulkConfirmOrDeclineBooking$1(httpClient: HttpClient): BulkConfirmOrDeclineBookingSignature;
interface BulkConfirmOrDeclineBookingSignature {
    /**
     * Confirms or declines up to 300 bookings.
     *
     *
     * See *Confirm Or Decline Booking*
     * ([SDK](https://dev.wix.com/docs/velo/api-reference/wix-bookings-v2/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking))
     * for details about when a booking is confirmed or declined.
     * @param - Bookings to confirm or decline.
     */
    (details: NonNullablePaths<BulkConfirmOrDeclineBookingRequestBookingDetails, `bookingId`, 2>[], options?: BulkConfirmOrDeclineBookingOptions): Promise<NonNullablePaths<BulkConfirmOrDeclineBookingResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.totalParticipants` | `results.${number}.item.status` | `results.${number}.item.paymentStatus` | `results.${number}.item.selectedPaymentOption` | `results.${number}.item.createdBy.anonymousVisitorId` | `results.${number}.item.createdBy.memberId` | `results.${number}.item.createdBy.wixUserId` | `results.${number}.item.createdBy.appId` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
        __applicationErrorsType?: BulkConfirmOrDeclineBookingApplicationErrors;
    }>;
}
declare function createBooking$1(httpClient: HttpClient): CreateBookingSignature;
interface CreateBookingSignature {
    /**
     * Creates a booking.
     *
     *
     * ## Appointment booking
     *
     * For appointment-based services, specify the relevant `slot` in
     * `bookedEntity.slot`. We recommend specifying the complete
     * `availabilityEntries.slot` returned in Query Availability
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
     * in your call's request to avoid failed calls due to unavailability.
     *
     * ## Class session booking
     *
     * For class services, specify the relevant event ID
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * as `bookedEntity.slot.eventId`.
     * We recommend retrieving the event ID from Query Availability's
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
     * `availabilityEntries.slot.eventId` to avoid failed calls due to unavailability.
     * Specifying an event ID leads to automatic calculations of `slot.startDate`, `slot.endDate`,
     * `slot.timezone`, `slot.resource`, and `slot.location`. All manually specified
     * values are overridden.
     *
     * ## Course booking
     *
     * For course services, specify the course's schedule ID in `bookedEntity.schedule.scheduleId`.
     * We recommend following [this sample flow](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course)
     * to minimize failed calls due to unavailability.
     *
     * ## Related resources
     *
     * Specifying a `resource` triggers an availability check, resulting in a failed
     * call if the resource is unavailable. Omitting a resource allows Wix Bookings
     * to assign a resource belonging to the relevant type randomly when the merchant
     * confirms the booking.
     *
     * ## Participant information
     *
     * You must specify either `participantsChoices` or `totalParticipants`.
     * The call fails if the specified `participantsChoices` aren't among the supported
     * _service options and variants_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * ## Notify customers
     *
     * You can specify a `participantNotification.message` for the customer that's send
     * immediately. Ensure `participantNotification.notifyParticipants` is set to `true`
     * to send the message.
     *
     * If you specify `{"sendSmsReminder": true}`, the customer receives an SMS 24 hours
     * before the session starts. The phone number is taken from `contactDetails.phone`.
     *
     * ## Booking status
     *
     * Bookings default to the `CREATED` status, not affecting the business calendar
     * or resource availability. You can specify a different status when the calling
     * [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities)
     * has `Manage Bookings` permissions.
     *
     * ## Payment options
     *
     * The specified `selectedPaymentOption` indicates how the customer intends to
     * pay, allowing for later changes to a different method supported by the service.
     *
     * ## Payment status
     *
     * A booking is initially created with `{"paymentStatus": "UNDEFINED"}` regardless
     * of the payment status specified in Create Booking. If a customer uses an
     * _eCommerce checkout_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),
     * Wix Bookings automatically syncs the booking's payment status from
     * the corresponding eCommerce order
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
     *
     * If a booking doesn't have a corresponding eCommerce order, for example, since
     * the customer didn't use the eCommerce checkout, you can update the booking's
     * payment status with Confirm Or Decline Booking
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).
     *
     * ## Booking form data
     *
     * When customers sign up for a service, they must fill out the booking form.
     * To create a booking with a completed booking form, specify the relevant data in
     * `formSubmission`. Ensure the values of the corresponding fields in
     * `booking.contactDetails` and `formSubmission` are identical. If these values
     * don't match, Create Booking fails. Therefore, we recommend specifying
     * only `booking.contactDetails.contactId` when providing `formSubmission`.
     *
     * ## Admin overwrites
     *
     * There are small but important differences when you specify special
     * `flowControlSettings`:
     *
     * - `{"skipAvailabilityValidation": true}`: The call succeeds
     * regardless of availability. If you don't specify any resource, the call
     * succeeds even if no resource of the relevant type is available.
     * - `{"skipBusinessConfirmation": true}`: Automatically confirms `PENDING`
     * bookings that require manual confirmation.
     * - `{"skipSelectedPaymentOptionValidation": true}`: Allows customers to pay
     * with payment methods that aren't supported for the service.
     *
     * When using special `flowControlSettings`, ensure you have sufficient
     * permissions. If you encounter failed calls due to insufficient permissions,
     * consider the following options:
     *
     * - **App developers** can use a higher
     * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),
     * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.
     * - **Site developers** can utilize
     * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).
     *
     * Granting additional permissions and using elevation permits method calls that
     * would typically fail due to authorization checks. Therefore, you should use
     * them intentionally and securely.
     * @param - The booking to create.
     */
    (booking: NonNullablePaths<Booking, `additionalFields.${number}._id` | `bookedEntity`, 4>, options?: CreateBookingOptions): Promise<NonNullablePaths<CreateBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: CreateBookingApplicationErrors;
    }>;
}
declare function bulkCreateBooking$1(httpClient: HttpClient): BulkCreateBookingSignature;
interface BulkCreateBookingSignature {
    /**
     * Creates up to 8 bookings.
     *
     *
     * See Create Booking
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking))
     * for more information.
     *
     * If any of the specified bookings is missing a required field the entire call
     * fails.
     *
     * If you specify 1 or more unavailable bookings, the call succeeds
     * while the unavailable bookings aren't created. Instead, they're counted as
     * failures in the returned `bulkActionMetadata`.
     * @param - Bookings to create.
     *
     * Max: 8 bookings
     */
    (createBookingsInfo: NonNullablePaths<CreateBookingInfo, `booking` | `booking.additionalFields.${number}._id` | `booking.bookedEntity`, 5>[], options?: BulkCreateBookingOptions): Promise<NonNullablePaths<BulkCreateBookingResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.totalParticipants` | `results.${number}.item.status` | `results.${number}.item.paymentStatus` | `results.${number}.item.selectedPaymentOption` | `results.${number}.item.createdBy.anonymousVisitorId` | `results.${number}.item.createdBy.memberId` | `results.${number}.item.createdBy.wixUserId` | `results.${number}.item.createdBy.appId` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
        __applicationErrorsType?: BulkCreateBookingApplicationErrors;
    }>;
}
declare function rescheduleBooking$1(httpClient: HttpClient): RescheduleBookingSignature;
interface RescheduleBookingSignature {
    /**
     * Reschedules an appointment booking to a different slot or a class booking to
     * a different session.
     *
     *
     * ## Course booking limitation
     *
     * You can't reschedule course bookings.
     *
     * ## Appointment sessions
     *
     * For appointments, the old session is removed from the business calendar
     * while a new session is added. We recommend calling Query Availability
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
     * first and specifying the entire retrieved `slot`.
     *
     * ## Class sessions
     *
     * For classes, the new session must be an existing session belonging to the
     * same class. We recommend retrieving `availabilityEntries.slot.eventId`
     * from Query Availability
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
     * to avoid failed Reschedule Booking calls due to unavailability. Specify
     * only `slot.eventId` instead of the entire `slot` object.
     *
     * ## Notify customers
     *
     * You can specify a `participantNotification.message` for the customer. To send
     * the message, you must also specify `participantNotification.notifyParticipants`
     * as `true`.
     *
     * ## Admin overwrites
     *
     * There are small but important differences when you specify special
     * `flowControlSettings`:
     *
     * - `{"ignoreReschedulePolicy": true}`: The call succeeds even if the
     * service's `reschedulePolicy` doesn't allow it.
     * - `{"skipAvailabilityValidation": true}`: The call succeeds even if
     * the specified session, slot, or resource isn't available. If you don't
     * specify any resource, the call succeeds even if no resource of the relevant
     * type is available.
     * - `{"skipBusinessConfirmation": true}`: Any `PENDING` booking is
     * automatically confirmed even if the services requires the merchants's
     * manual confirmation.
     *
     * When using special `flowControlSettings`, ensure you have sufficient
     * permissions. If you encounter failed calls due to insufficient permissions,
     * consider the following options:
     *
     * - **App developers** can use a higher
     * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),
     * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.
     * - **Site developers** can utilize
     * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).
     *
     * Granting additional permissions and using elevation permits method calls that
     * would typically fail due to authorization checks. Therefore, you should use
     * them intentionally and securely.
     * @param - ID of the booking to reschedule.
     * @param - New slot of the booking.
     * @param - An object representing the available options for rescheduling a booking.
     */
    (bookingId: string, slot: V2Slot, options?: NonNullablePaths<RescheduleBookingOptions, `revision`, 2>): Promise<NonNullablePaths<RescheduleBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: RescheduleBookingApplicationErrors;
    }>;
}
declare function confirmBooking$1(httpClient: HttpClient): ConfirmBookingSignature;
interface ConfirmBookingSignature {
    /**
     * Updates the booking status to `CONFIRMED` without checking whether the relevant slot or schedule is still available.
     *
     *
     * ## eCommerce checkout restriction
     *
     * Call this method only when using a custom checkout page. Don't
     * call it when using a Wix eCommerce checkout
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     * In such cases, Wix automatically updates the booking status based on
     * the `paymentStatus` of the corresponding Wix eCommerce order
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
     *
     * ## When to call Confirm Or Decline Booking instead
     *
     * Confirm Booking doesn't check whether a slot or schedule is still available.
     * For these checks, call Confirm or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) instead.
     *
     * ## Original status validation
     *
     * You can only confirm bookings with a status of `PENDING`, `CREATED`, or
     * `WAITING_LIST`.
     *
     * ## Double bookings
     *
     * Confirm Booking doesn't check whether a slot or schedule is still available.
     * You can specify
     *
     * ```json
     * {
     * "flowControlSettings": {
     * "checkAvailabilityValidation": true
     * },
     * "doubleBooked": true
     * }
     * ```
     * to forcefully set the booking's `doubleBooked` flag to `true`, regardless of
     * a potential double booking conflict. You must call with `Manage Bookings`
     * permissions to do so. For the default flow control settings
     * `{"checkAvailabilityValidation": false}`, the specified `doubleBooked` value
     * is ignored.
     *
     * ## Payment status
     *
     * Also updates the booking's `paymentStatus`, if you specify a new payment
     * status.
     *
     * ## Notify customers
     *
     * You can specify a `participantNotification.message` for the customer. To send
     * the message, you must also specify `participantNotification.notifyParticipants`
     * as `true`.
     * @param - ID of the booking to confirm.
     * @param - Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be passed when
     * updating the booking.
     * @param - An object representing the available options for canceling a booking.
     */
    (bookingId: string, revision: string, options?: ConfirmBookingOptions): Promise<NonNullablePaths<ConfirmBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: ConfirmBookingApplicationErrors;
    }>;
}
declare function setBookingSubmissionId$1(httpClient: HttpClient): SetBookingSubmissionIdSignature;
interface SetBookingSubmissionIdSignature {
    /**
     * Setting `submission_id` for a booking after the form submission is created.
     * @param - ID of the booking to set `submissionId` for.
     * @param - ID of the form submission to set on the booking.
     */
    (bookingId: string, submissionId: string): Promise<NonNullablePaths<SetBookingSubmissionIdResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: SetBookingSubmissionIdApplicationErrors;
    }>;
}
declare function updateExtendedFields$1(httpClient: HttpClient): UpdateExtendedFieldsSignature;
interface UpdateExtendedFieldsSignature {
    /**
     * Updates the extended fields for a booking.
     *
     *
     * If you specify an extended field `namespace` that doesn't exist yet, it's
     * created.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/about-backend-extensions#schema-plugin-extensions).
     * @param - ID of the booking for which to update extended fields.
     * @param - [Namespace](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-reading-and-writing-schema-plugin-fields#namespaces) of the app for which to update extended fields.
     * @param - Options for updating the booking's extended fields.
     */
    (_id: string, namespace: string, options: NonNullablePaths<UpdateExtendedFieldsOptions, `namespaceData`, 2>): Promise<NonNullablePaths<UpdateExtendedFieldsResponse, `namespace`, 2> & {
        __applicationErrorsType?: UpdateExtendedFieldsApplicationErrors;
    }>;
}
declare function declineBooking$1(httpClient: HttpClient): DeclineBookingSignature;
interface DeclineBookingSignature {
    /**
     * Updates the booking status to `DECLINED` and updates the relevant session's
     * `participants.approvalStatus` to `DECLINED` without checking whether the relevant
     * slot or schedule is still available.
     *
     *
     * ## eCommerce checkout restriction
     *
     * Call this method only when using a custom checkout page. Don't
     * call it when using a Wix eCommerce checkout
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     * In such cases, Wix automatically updates the booking status based on
     * the `paymentStatus` of the corresponding Wix eCommerce order
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
     *
     * ## When to call Confirm Or Decline Booking instead
     *
     * The method doesn't check whether a slot or schedule is still available. For
     * these checks you can call Confirm or Decline Booking
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).
     *
     * ## Original status validation
     *
     * You can only decline bookings with a `status` of `PENDING`, `CREATED`, or
     * `WAITING_LIST`.
     *
     * ## Payment status
     *
     * Also updates the booking's `paymentStatus`, if you specify a new payment
     * status.
     *
     * ## Notify customers
     *
     * You can specify a `participantNotification.message` for the customer. To send
     * the message, you must also specify `participantNotification.notifyParticipants`
     * as `true`.
     * @param - ID of the booking to decline.
     * @param - Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * declining the booking.
     * @param - An object representing the available options for declining a booking.
     */
    (bookingId: string, revision: string, options?: DeclineBookingOptions): Promise<NonNullablePaths<DeclineBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: DeclineBookingApplicationErrors;
    }>;
}
declare function cancelBooking$1(httpClient: HttpClient): CancelBookingSignature;
interface CancelBookingSignature {
    /**
     * Updates the booking status to `CANCELED`.
     *
     *
     * ## Appointments
     *
     * For appointments, the corresponding event is removed from the Bookings
     * calendar.
     *
     * ## Class and course bookings
     *
     * For class or course bookings, the relevant participants are removed
     * from the class session or the course. However, the class session or course
     * remain on the business calendar.
     *
     * ## Notify customers
     *
     * You can specify a `participantNotification.message` for the customer. To send
     * the message, you must also specify `participantNotification.notifyParticipants`
     * as `true`.
     *
     * ## Admin overwrites
     *
     * There are small but important differences when you specify special
     * `flowControlSettings`:
     *
     * - `{"ignoreCancellationPolicy": true}`: The call succeeds even if the
     * service's `cancellationPolicy` doesn't allow it.
     * - `{"withRefund": true}`: The customer is refunded even if the service's
     * `refundPolicy` doesn't allow it.
     * - `{"waiveCancellationFee": true}`: The customer doesn't have to pay
     * the cancellation fee, even if the service's `cancellationPolicy` requires it.
     *
     * When using special `flowControlSettings`, ensure you have sufficient
     * permissions. If you encounter failed calls due to insufficient permissions,
     * consider the following options:
     *
     * - **App developers** can use a higher
     * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),
     * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.
     * - **Site developers** can utilize
     * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).
     *
     * Granting additional permissions and using elevation permits method calls that
     * would typically fail due to authorization checks. Therefore, you should use
     * them intentionally and securely.
     * @param - ID of the booking to cancel.
     * @param - An object representing the available options for canceling a booking.
     */
    (bookingId: string, options?: NonNullablePaths<CancelBookingOptions, `revision`, 2>): Promise<NonNullablePaths<CancelBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: CancelBookingApplicationErrors;
    }>;
}
declare function updateNumberOfParticipants$1(httpClient: HttpClient): UpdateNumberOfParticipantsSignature;
interface UpdateNumberOfParticipantsSignature {
    /**
     * Updates the number of participants for a class or course booking and changes
     * the `totalNumberOfParticipants` for the relevant sessions.
     *
     *
     * ## Appointment limitation
     *
     * You can't update the number of participants for appointment bookings.
     *
     * ## Participant information
     *
     * You must specify either `participantsChoices` or `totalParticipants`.
     * The call fails if the specified `participantsChoices` aren't among the
     * supported service options and variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @param - ID of the booking to update the number of participants for.
     */
    (bookingId: string, options?: NonNullablePaths<UpdateNumberOfParticipantsOptions, `revision`, 2>): Promise<NonNullablePaths<UpdateNumberOfParticipantsResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: UpdateNumberOfParticipantsApplicationErrors;
    }>;
}
declare function markBookingAsPending$1(httpClient: HttpClient): MarkBookingAsPendingSignature;
interface MarkBookingAsPendingSignature {
    /**
     * @param - ID of the booking to mark as `PENDING`.
     * @param - Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * managing the booking.
     */
    (bookingId: string, revision: string, options?: MarkBookingAsPendingOptions): Promise<NonNullablePaths<MarkBookingAsPendingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: MarkBookingAsPendingApplicationErrors;
    }>;
}
declare function createMultiServiceBooking$1(httpClient: HttpClient): CreateMultiServiceBookingSignature;
interface CreateMultiServiceBookingSignature {
    /**
     * Creates a multi-service booking and all included single-service bookings simultaneously.
     *
     *
     * ## When to call this method
     *
     * Create sequential appointments where customers book related services together. For adding existing single-service bookings to an existing multi-service booking, call Add Bookings to Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking)) instead.
     *
     * ## Requirements and behavior
     *
     * __Package constraints__: Multi-service bookings support 2-8 appointment-based single-service bookings only (course and class bookings aren't supported). All single-service bookings must be at the same location with sequential scheduling and no gaps between appointments.
     *
     * __Timing specification__: You must provide complete `slot` details (`scheduleId`, `startDate`, `endDate`) for each single-service booking. Wix Bookings validates sequential timing but doesn't auto-calculate it.
     *
     * __Package pricing__: The total price equals the sum of individual services. Wix Bookings automatically syncs the payment status from the corresponding Wix eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecommerce/orders/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer paid via an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     *
     * __Package notifications__: Customers receive 1 unified notification for the entire multi-service booking. Wix Bookings doesn't send notifications for the package's individual single-service bookings.
     *
     * ## Related methods
     *
     * Verify availability first with List Multi Service Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)).
     *
     * See Create Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking)) for more details about constraints and effects of creating single-service bookings.
     * @param - Between 2 to 8 new single-service booking specifications to create and combine in a multi-service booking.
     *
     * Each single-service booking specification must include `slot` details (`scheduleId`, `startDate`, and `endDate`).
     * Specify contact details, number of participants, and any additional fields as needed.
     *
     * For sequential bookings, ensure the timing allows services to be scheduled back-to-back.
     */
    (bookings: NonNullablePaths<Booking, `additionalFields.${number}._id` | `bookedEntity` | `bookedEntity.slot` | `bookedEntity.slot.endDate` | `bookedEntity.slot.location.locationType` | `bookedEntity.slot.scheduleId` | `bookedEntity.slot.startDate`, 5>[], options?: CreateMultiServiceBookingOptions): Promise<NonNullablePaths<CreateMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: CreateMultiServiceBookingApplicationErrors;
    }>;
}
declare function rescheduleMultiServiceBooking$1(httpClient: HttpClient): RescheduleMultiServiceBookingSignature;
interface RescheduleMultiServiceBookingSignature {
    /**
     * Reschedules a multi-service booking by changing the timing for all or specific single-service bookings in the package.
     *
     *
     * This method reschedules single-service bookings within the multi-service booking while maintaining sequential order. You must specify exact new timing for each service to ensure they remain back-to-back with no gaps or overlaps.
     *
     * This method fails if it can't reschedule at least 1 single-service booking. You must provide the current revision number for each single-service booking you're rescheduling to prevent conflicting changes.
     *
     * See Reschedule Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-booking)) for single-service booking rescheduling details.
     * @param - ID of the multi-service booking to reschedule.
     * @param - Information about the single-service bookings to reschedule.
     */
    (multiServiceBookingId: string, rescheduleBookingsInfo: NonNullablePaths<RescheduleBookingInfo, `bookingId` | `revision` | `slot`, 2>[], options?: RescheduleMultiServiceBookingOptions): Promise<NonNullablePaths<RescheduleMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: RescheduleMultiServiceBookingApplicationErrors;
    }>;
}
declare function getMultiServiceBookingAvailability$1(httpClient: HttpClient): GetMultiServiceBookingAvailabilitySignature;
interface GetMultiServiceBookingAvailabilitySignature {
    /**
     * Checks if the business can still accommodate an existing multi-service booking and returns overall bookability status, capacity details, and policy violations.
     *
     *
     * Wix Bookings considers:
     * - The relevant services' booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).
     * - The availability of all required resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).
     *
     * Call this method to check if an existing multi-service booking is still valid after business configuration changes.
     * For example, staff changes, policy updates, or capacity modifications.
     *
     * For checking availability before creating new multi-service bookings, call List Multi Service Availability Time Slots
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)) instead.
     * @param - ID of the multi-service booking to retrieve.
     */
    (multiServiceBookingId: string): Promise<NonNullablePaths<GetMultiServiceBookingAvailabilityResponse, `bookable` | `multiServiceBookingInfo`, 2> & {
        __applicationErrorsType?: GetMultiServiceBookingAvailabilityApplicationErrors;
    }>;
}
declare function cancelMultiServiceBooking$1(httpClient: HttpClient): CancelMultiServiceBookingSignature;
interface CancelMultiServiceBookingSignature {
    /**
     * Cancels a multi-service booking and all its associated single-service bookings.
     *
     *
     * Cancels the entire multi-service booking, updating the status of all single-service bookings to `CANCELED`.
     * The call fails if all single-service bookings are already canceled or declined.
     *
     * See Cancel Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/cancel-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/cancel-booking)) for single-service booking cancellation details.
     * @param - ID of the multi-service booking to cancel.
     */
    (multiServiceBookingId: string, options?: CancelMultiServiceBookingOptions): Promise<NonNullablePaths<CancelMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: CancelMultiServiceBookingApplicationErrors;
    }>;
}
declare function markMultiServiceBookingAsPending$1(httpClient: HttpClient): MarkMultiServiceBookingAsPendingSignature;
interface MarkMultiServiceBookingAsPendingSignature {
    /**
     * Updates the status for all single-service bookings in a multi-service booking to `PENDING`.
     *
     *
     * Call this method for multi-service bookings requiring manual business approval before confirmation.
     *
     * ## Status requirements
     *
     * - __Original status__: All single-service bookings must have `CREATED` status.
     * - __Target status__: All bookings move to `PENDING` together (all-or-nothing operation).
     *
     * ## Checkout restrictions
     *
     * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,
     * Wix Bookings automatically manages the bookings' statuses based on payment processing.
     *
     * ## Additional updates
     *
     * - __Payment status__: Updates if you specify a new `markAsPendingBookingsInfo.paymentStatus`.
     * - __Customer notifications__: Send messages using `participantNotification`.
     * - __Revision control__: Requires current revision numbers for all single-service bookings.
     *
     * See Mark Booking as Pending ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending)) for more details about marking single-service bookings as pending.
     * @param - ID of the multi-service booking to mark as `PENDING`.
     */
    (multiServiceBookingId: string, options?: MarkMultiServiceBookingAsPendingOptions): Promise<NonNullablePaths<MarkMultiServiceBookingAsPendingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: MarkMultiServiceBookingAsPendingApplicationErrors;
    }>;
}
declare function confirmMultiServiceBooking$1(httpClient: HttpClient): ConfirmMultiServiceBookingSignature;
interface ConfirmMultiServiceBookingSignature {
    /**
     * Updates the status for all single-service bookings in a multi-service booking to `CONFIRMED`.
     *
     *
     * Call this method for multi-service bookings requiring manual business approval.
     *
     * ## Status requirements
     *
     * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.
     * - __Target status__: All bookings move to `CONFIRMED` together (all-or-nothing operation).
     *
     * ## Checkout restrictions
     *
     * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,
     * Wix Bookings automatically manages the bookings' statuses based on payment processing.
     *
     * ## Additional updates
     *
     * - __Payment status__: Updates if you specify a new `confirmBookingsInfo.paymentStatus`.
     * - __Customer notifications__: Send messages using `participantNotification`.
     * - __Revision control__: Requires current revision numbers for all single-service bookings.
     *
     * See Confirm Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-booking)) for more details about confirming single-service bookings.
     * @param - ID of the multi-service booking to confirm its related bookings.
     */
    (multiServiceBookingId: string, options?: ConfirmMultiServiceBookingOptions): Promise<NonNullablePaths<ConfirmMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: ConfirmMultiServiceBookingApplicationErrors;
    }>;
}
declare function declineMultiServiceBooking$1(httpClient: HttpClient): DeclineMultiServiceBookingSignature;
interface DeclineMultiServiceBookingSignature {
    /**
     * Updates the status for all single-service bookings in a multi-service booking to `DECLINED`.
     *
     *
     * Call this method to reject multi-service bookings that can't be accommodated or don't meet business requirements.
     *
     * ## Status requirements
     *
     * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.
     * - __Target status__: All bookings move to `DECLINED` together (all-or-nothing operation).
     *
     * ## Checkout restrictions
     *
     * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,
     * Wix Bookings automatically manages the bookings' statuses based on payment processing.
     *
     * ## Additional updates
     *
     * - __Payment status__: Updates if you specify a new `declineBookingsInfo.paymentStatus`.
     * - __Customer notifications__: Send messages using `participantNotification`.
     * - __Revision control__: Requires current revision numbers for all single-service bookings.
     *
     * Refer to Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/decline-booking)) for more details about declining single-service bookings.
     * @param - ID of the multi-service booking to decline.
     */
    (multiServiceBookingId: string, options?: DeclineMultiServiceBookingOptions): Promise<NonNullablePaths<DeclineMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
        __applicationErrorsType?: DeclineMultiServiceBookingApplicationErrors;
    }>;
}
declare function bulkGetMultiServiceBookingAllowedActions$1(httpClient: HttpClient): BulkGetMultiServiceBookingAllowedActionsSignature;
interface BulkGetMultiServiceBookingAllowedActionsSignature {
    /**
     * Retrieves information about which actions the customer can perform for up to 50 multi-service bookings.
     *
     *
     * For each multi-service booking, the response indicates which actions are currently allowed:
     * - `cancel`: Whether the customer can cancel the multi-service booking.
     * - `reschedule`: Whether the customer can adjust the multi-service booking's timing.
     *
     * Bear the following considerations in mind when calling this method:
     *
     * __Real-time validation__: Wix Bookings calculates allowed actions based on current multi-service booking status, booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)), and available resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) at the time of the call.
     *
     * __Permission context__: Depending on the permissions of the calling [identity](https://dev.wix.com/build-apps/develop-your-app/about-identities), you may see different allowed actions for the same multi-service booking. For example, if the identity has permissions to read only part of the multi-service booking, the response indicates which single-service bookings the identity can read.
     *
     * __Status dependencies__: Allowed actions change as bookings progress through their lifecycle (`CREATED` → `PENDING` → `CONFIRMED`/`DECLINED` → `CANCELED`).
     * Bookings can skip `PENDING` and move directly from `CREATED` to `CONFIRMED`/`DECLINED` based on service configuration.
     * @param - IDs of the multi-service bookings to retrieve allowed actions for.
     */
    (multiServiceBookingIds: string[]): Promise<NonNullablePaths<BulkGetMultiServiceBookingAllowedActionsResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.cancel` | `results.${number}.item.reschedule` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
}
declare function getMultiServiceBooking$1(httpClient: HttpClient): GetMultiServiceBookingSignature;
interface GetMultiServiceBookingSignature {
    /**
     * Retrieves a multi-service booking and all its associated single-service bookings.
     *
     *
     * Returns the complete multi-service booking information including its ID, associated single-service bookings, and the total number of scheduled single-service bookings.
     *
     * If you call on behalf of an [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) with permissions to read only part of the multi-service booking, only the permitted single-service bookings are retrieved.
     * The returned total number includes single-service bookings for which you don't have permissions.
     *
     * See Query Extended Bookings ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/extended-bookings/query-extended-bookings) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-reader-v2/query-extended-bookings)) for details about retrieving individual single-service bookings and additional metadata.
     * @param - ID of the multi-service booking.
     * @returns Retrieved multi-service booking.
     */
    (multiServiceBookingId: string): Promise<NonNullablePaths<MultiServiceBooking, `bookings` | `bookings.${number}.booking.totalParticipants` | `bookings.${number}.booking.status` | `bookings.${number}.booking.paymentStatus` | `bookings.${number}.booking.selectedPaymentOption` | `bookings.${number}.booking.createdBy.anonymousVisitorId` | `bookings.${number}.booking.createdBy.memberId` | `bookings.${number}.booking.createdBy.wixUserId` | `bookings.${number}.booking.createdBy.appId`, 6>>;
}
declare function addBookingsToMultiServiceBooking$1(httpClient: HttpClient): AddBookingsToMultiServiceBookingSignature;
interface AddBookingsToMultiServiceBookingSignature {
    /**
     * Expands an existing multi-service booking by adding existing single-service bookings to the package.
     *
     *
     * ## When to call this method
     *
     * Call this method to add 1 or more existing single-service bookings to an existing multi-service booking.
     * For creating a new multi-service booking with new single-service bookings, call Create Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-multi-service-booking)) instead.
     *
     * ## Package integration
     *
     * The timing of the single-service bookings to add must be compatible with the multi-service booking's sequential schedule to ensure no gaps or overlaps between services in the updated package.
     *
     * ## Requirements and limitations
     *
     * - __Maximum capacity__: The total number of single-service bookings can't exceed 8.
     * - __Booking eligibility__: You can add only independent single-service bookings that aren't part of another multi-service booking.
     * - __Status compatibility__: Added bookings must have compatible status with the target multi-service booking.
     * - __Revision control__: You must provide current revision numbers for all single-service bookings to add.
     * @param - ID of the multi-service booking.
     */
    (multiServiceBookingId: string, options?: NonNullablePaths<AddBookingsToMultiServiceBookingOptions, `bookings` | `bookings.${number}.bookingId` | `bookings.${number}.revision`, 4>): Promise<NonNullablePaths<AddBookingsToMultiServiceBookingResponse, `bookings` | `bookings.${number}.booking.totalParticipants` | `bookings.${number}.booking.status` | `bookings.${number}.booking.paymentStatus` | `bookings.${number}.booking.selectedPaymentOption` | `bookings.${number}.booking.createdBy.anonymousVisitorId` | `bookings.${number}.booking.createdBy.memberId` | `bookings.${number}.booking.createdBy.wixUserId` | `bookings.${number}.booking.createdBy.appId`, 6> & {
        __applicationErrorsType?: AddBookingsToMultiServiceBookingApplicationErrors;
    }>;
}
declare function removeBookingsFromMultiServiceBooking$1(httpClient: HttpClient): RemoveBookingsFromMultiServiceBookingSignature;
interface RemoveBookingsFromMultiServiceBookingSignature {
    /**
     * Removes single-service bookings from a multi-service booking and converts them to independent single-service bookings.
     *
     *
     * ## Removal options
     *
     * __Remove all permitted bookings__: If you specify an empty `bookings` array, all single-service bookings for which the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has read permissions are removed from the multi-service booking.
     *
     * __Selective removal__: Specify single-service booking IDs and revisions to remove only specific single-service bookings from the package.
     *
     * __Sequential scheduling__: To maintain sequential scheduling, remove only first or last single-service bookings. For middle bookings, first reschedule all relevant single-service bookings to eliminate gaps. To do so, call Reschedule Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-multi-service-booking)) before removing the unwanted bookings.
     *
     * ## Removal behavior
     *
     * __Independent bookings__: Removed single-service bookings become independent bookings.
     * You can manage them using single-service booking methods.
     *
     * __Automatic cleanup__: Multi-service bookings must contain at least 2 services.
     * If removal results in only 1 remaining single-service booking for the multi-service booking, the entire multi-service booking is deleted and the remaining single-service booking becomes a standalone booking.
     *
     * __Revision control__: Specify current revision numbers to prevent conflicting modifications during the removal process.
     * @param - ID of the multi-service booking.
     */
    (multiServiceBookingId: string, options?: RemoveBookingsFromMultiServiceBookingOptions): Promise<NonNullablePaths<RemoveBookingsFromMultiServiceBookingResponse, `bookings` | `bookings.${number}.booking.totalParticipants` | `bookings.${number}.booking.status` | `bookings.${number}.booking.paymentStatus` | `bookings.${number}.booking.selectedPaymentOption` | `bookings.${number}.booking.createdBy.anonymousVisitorId` | `bookings.${number}.booking.createdBy.memberId` | `bookings.${number}.booking.createdBy.wixUserId` | `bookings.${number}.booking.createdBy.appId`, 6> & {
        __applicationErrorsType?: RemoveBookingsFromMultiServiceBookingApplicationErrors;
    }>;
}
declare const onBookingCanceled$1: EventDefinition<BookingCanceledEnvelope, "wix.bookings.v2.booking_canceled">;
declare const onBookingConfirmed$1: EventDefinition<BookingConfirmedEnvelope, "wix.bookings.v2.booking_confirmed">;
declare const onBookingCreated$1: EventDefinition<BookingCreatedEnvelope, "wix.bookings.v2.booking_created">;
declare const onBookingDeclined$1: EventDefinition<BookingDeclinedEnvelope, "wix.bookings.v2.booking_declined">;
declare const onBookingNumberOfParticipantsUpdated$1: EventDefinition<BookingNumberOfParticipantsUpdatedEnvelope, "wix.bookings.v2.booking_number_of_participants_updated">;
declare const onBookingRescheduled$1: EventDefinition<BookingRescheduledEnvelope, "wix.bookings.v2.booking_rescheduled">;
declare const onBookingUpdated$1: EventDefinition<BookingUpdatedEnvelope, "wix.bookings.v2.booking_updated">;

declare const confirmOrDeclineBooking: MaybeContext<BuildRESTFunction<typeof confirmOrDeclineBooking$1> & typeof confirmOrDeclineBooking$1>;
declare const bulkConfirmOrDeclineBooking: MaybeContext<BuildRESTFunction<typeof bulkConfirmOrDeclineBooking$1> & typeof bulkConfirmOrDeclineBooking$1>;
declare const createBooking: MaybeContext<BuildRESTFunction<typeof createBooking$1> & typeof createBooking$1>;
declare const bulkCreateBooking: MaybeContext<BuildRESTFunction<typeof bulkCreateBooking$1> & typeof bulkCreateBooking$1>;
declare const rescheduleBooking: MaybeContext<BuildRESTFunction<typeof rescheduleBooking$1> & typeof rescheduleBooking$1>;
declare const confirmBooking: MaybeContext<BuildRESTFunction<typeof confirmBooking$1> & typeof confirmBooking$1>;
declare const setBookingSubmissionId: MaybeContext<BuildRESTFunction<typeof setBookingSubmissionId$1> & typeof setBookingSubmissionId$1>;
declare const updateExtendedFields: MaybeContext<BuildRESTFunction<typeof updateExtendedFields$1> & typeof updateExtendedFields$1>;
declare const declineBooking: MaybeContext<BuildRESTFunction<typeof declineBooking$1> & typeof declineBooking$1>;
declare const cancelBooking: MaybeContext<BuildRESTFunction<typeof cancelBooking$1> & typeof cancelBooking$1>;
declare const updateNumberOfParticipants: MaybeContext<BuildRESTFunction<typeof updateNumberOfParticipants$1> & typeof updateNumberOfParticipants$1>;
declare const markBookingAsPending: MaybeContext<BuildRESTFunction<typeof markBookingAsPending$1> & typeof markBookingAsPending$1>;
declare const createMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof createMultiServiceBooking$1> & typeof createMultiServiceBooking$1>;
declare const rescheduleMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof rescheduleMultiServiceBooking$1> & typeof rescheduleMultiServiceBooking$1>;
declare const getMultiServiceBookingAvailability: MaybeContext<BuildRESTFunction<typeof getMultiServiceBookingAvailability$1> & typeof getMultiServiceBookingAvailability$1>;
declare const cancelMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof cancelMultiServiceBooking$1> & typeof cancelMultiServiceBooking$1>;
declare const markMultiServiceBookingAsPending: MaybeContext<BuildRESTFunction<typeof markMultiServiceBookingAsPending$1> & typeof markMultiServiceBookingAsPending$1>;
declare const confirmMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof confirmMultiServiceBooking$1> & typeof confirmMultiServiceBooking$1>;
declare const declineMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof declineMultiServiceBooking$1> & typeof declineMultiServiceBooking$1>;
declare const bulkGetMultiServiceBookingAllowedActions: MaybeContext<BuildRESTFunction<typeof bulkGetMultiServiceBookingAllowedActions$1> & typeof bulkGetMultiServiceBookingAllowedActions$1>;
declare const getMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof getMultiServiceBooking$1> & typeof getMultiServiceBooking$1>;
declare const addBookingsToMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof addBookingsToMultiServiceBooking$1> & typeof addBookingsToMultiServiceBooking$1>;
declare const removeBookingsFromMultiServiceBooking: MaybeContext<BuildRESTFunction<typeof removeBookingsFromMultiServiceBooking$1> & typeof removeBookingsFromMultiServiceBooking$1>;
/**
 * Triggered when a booking is canceled.
 */
declare const onBookingCanceled: BuildEventDefinition<typeof onBookingCanceled$1>;
/**
 * Triggered when a booking is confirmed.
 */
declare const onBookingConfirmed: BuildEventDefinition<typeof onBookingConfirmed$1>;
/**
 * Triggered when a booking is created.
 */
declare const onBookingCreated: BuildEventDefinition<typeof onBookingCreated$1>;
/**
 * Triggered when a booking is declined.
 */
declare const onBookingDeclined: BuildEventDefinition<typeof onBookingDeclined$1>;
/**
 * Triggered when the number of participants is updated.
 */
declare const onBookingNumberOfParticipantsUpdated: BuildEventDefinition<typeof onBookingNumberOfParticipantsUpdated$1>;
/**
 * Triggered when a booking is rescheduled.
 */
declare const onBookingRescheduled: BuildEventDefinition<typeof onBookingRescheduled$1>;
/**
 * Triggered when a booked schedule is updated.
 */
declare const onBookingUpdated: BuildEventDefinition<typeof onBookingUpdated$1>;

export { AddBookingsToMultiServiceBookingApplicationErrors, AddBookingsToMultiServiceBookingOptions, AddBookingsToMultiServiceBookingResponse, Booking, BookingCanceledEnvelope, BookingConfirmedEnvelope, BookingCreatedEnvelope, BookingDeclinedEnvelope, BookingNumberOfParticipantsUpdatedEnvelope, BookingRescheduledEnvelope, BookingUpdatedEnvelope, BulkConfirmOrDeclineBookingApplicationErrors, BulkConfirmOrDeclineBookingOptions, BulkConfirmOrDeclineBookingRequestBookingDetails, BulkConfirmOrDeclineBookingResponse, BulkCreateBookingApplicationErrors, BulkCreateBookingOptions, BulkCreateBookingResponse, BulkGetMultiServiceBookingAllowedActionsResponse, CancelBookingApplicationErrors, CancelBookingOptions, CancelBookingResponse, CancelMultiServiceBookingApplicationErrors, CancelMultiServiceBookingOptions, CancelMultiServiceBookingResponse, ConfirmBookingApplicationErrors, ConfirmBookingOptions, ConfirmBookingResponse, ConfirmMultiServiceBookingApplicationErrors, ConfirmMultiServiceBookingOptions, ConfirmMultiServiceBookingResponse, ConfirmOrDeclineBookingApplicationErrors, ConfirmOrDeclineBookingOptions, ConfirmOrDeclineBookingResponse, CreateBookingApplicationErrors, CreateBookingInfo, CreateBookingOptions, CreateBookingResponse, CreateMultiServiceBookingApplicationErrors, CreateMultiServiceBookingOptions, CreateMultiServiceBookingResponse, DeclineBookingApplicationErrors, DeclineBookingOptions, DeclineBookingResponse, DeclineMultiServiceBookingApplicationErrors, DeclineMultiServiceBookingOptions, DeclineMultiServiceBookingResponse, GetMultiServiceBookingAvailabilityApplicationErrors, GetMultiServiceBookingAvailabilityResponse, MarkBookingAsPendingApplicationErrors, MarkBookingAsPendingOptions, MarkBookingAsPendingResponse, MarkMultiServiceBookingAsPendingApplicationErrors, MarkMultiServiceBookingAsPendingOptions, MarkMultiServiceBookingAsPendingResponse, MultiServiceBooking, RemoveBookingsFromMultiServiceBookingApplicationErrors, RemoveBookingsFromMultiServiceBookingOptions, RemoveBookingsFromMultiServiceBookingResponse, RescheduleBookingApplicationErrors, RescheduleBookingInfo, RescheduleBookingOptions, RescheduleBookingResponse, RescheduleMultiServiceBookingApplicationErrors, RescheduleMultiServiceBookingOptions, RescheduleMultiServiceBookingResponse, SetBookingSubmissionIdApplicationErrors, SetBookingSubmissionIdResponse, UpdateExtendedFieldsApplicationErrors, UpdateExtendedFieldsOptions, UpdateExtendedFieldsResponse, UpdateNumberOfParticipantsApplicationErrors, UpdateNumberOfParticipantsOptions, UpdateNumberOfParticipantsResponse, V2Slot, addBookingsToMultiServiceBooking, bulkConfirmOrDeclineBooking, bulkCreateBooking, bulkGetMultiServiceBookingAllowedActions, cancelBooking, cancelMultiServiceBooking, confirmBooking, confirmMultiServiceBooking, confirmOrDeclineBooking, createBooking, createMultiServiceBooking, declineBooking, declineMultiServiceBooking, getMultiServiceBooking, getMultiServiceBookingAvailability, markBookingAsPending, markMultiServiceBookingAsPending, onBookingCanceled, onBookingConfirmed, onBookingCreated, onBookingDeclined, onBookingNumberOfParticipantsUpdated, onBookingRescheduled, onBookingUpdated, removeBookingsFromMultiServiceBooking, rescheduleBooking, rescheduleMultiServiceBooking, setBookingSubmissionId, updateExtendedFields, updateNumberOfParticipants };
