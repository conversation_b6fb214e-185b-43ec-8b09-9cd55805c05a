import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { R as Resource, C as CreateResourceValidationErrors, B as BulkCreateResourcesOptions, a as BulkCreateResourcesResponse, b as BulkCreateResourcesValidationErrors, U as UpdateResource, c as UpdateResourceValidationErrors, M as MaskedResource, d as BulkUpdateResourcesOptions, e as BulkUpdateResourcesResponse, f as BulkUpdateResourcesValidationErrors, g as BulkDeleteResourcesResponse, h as ResourceSearch, S as SearchResourcesResponse, i as ResourcesQueryBuilder, j as CountResourcesOptions, k as CountResourcesResponse, l as ResourceCreatedEnvelope, m as ResourceDeletedEnvelope, n as ResourceUpdatedEnvelope } from './bookings-resources-v2-resource-resources.universal-WjeiBRqP.mjs';
export { b5 as ActionEvent, ai as Aggregation, ax as AggregationData, aj as AggregationKindOneOf, aQ as AggregationResults, aR as AggregationResultsResultOneOf, aE as AggregationResultsScalarResult, A as AggregationType, bm as AggregationTypeWithLiterals, Q as ApplicationError, ba as BaseEventMetadata, T as BulkActionMetadata, K as BulkCreateResourcesRequest, ad as BulkDeleteResourcesRequest, O as BulkResourceResult, aa as BulkUpdateResourcesRequest, D as BusinessLocation, bp as CommonSearchWithEntityContext, aW as CountResourcesRequest, H as CreateResourceRequest, J as CreateResourceResponse, a0 as CursorPaging, a2 as CursorPagingMetadata, aT as CursorQuery, aU as CursorQueryPagingMethodOneOf, af as CursorSearch, ag as CursorSearchPagingMethodOneOf, a3 as Cursors, aq as DateHistogramAggregation, aM as DateHistogramResult, aO as DateHistogramResults, ab as DeleteResourceRequest, ac as DeleteResourceResponse, a$ as DomainEvent, b0 as DomainEventBodyOneOf, b6 as Empty, b1 as EntityCreatedEvent, b4 as EntityDeletedEvent, b3 as EntityUpdatedEvent, bb as EventMetadata, F as EventsSchedule, G as ExtendedFields, aX as FixResourceSchedulesRequest, aY as FixResourceSchedulesResponse, Z as GetDeletedResourceRequest, _ as GetDeletedResourceResponse, X as GetResourceRequest, Y as GetResourceResponse, au as GroupByAggregation, av as GroupByAggregationKindOneOf, aN as GroupByValueResults, b8 as IdentificationData, b9 as IdentificationDataIdOneOf, al as IncludeMissingValuesOptions, I as Interval, bl as IntervalWithLiterals, P as ItemMetadata, $ as ListDeletedResourcesRequest, a1 as ListDeletedResourcesResponse, L as LocationOptions, o as ManagementType, be as ManagementTypeWithLiterals, b7 as MessageEnvelope, s as MissingValues, bi as MissingValuesWithLiterals, u as Mode, bn as ModeWithLiterals, at as NestedAggregation, ar as NestedAggregationItem, as as NestedAggregationItemKindOneOf, aA as NestedAggregationResults, aB as NestedAggregationResultsResultOneOf, N as NestedAggregationType, bk as NestedAggregationTypeWithLiterals, aJ as NestedResultValue, aK as NestedResultValueResultOneOf, aP as NestedResults, aF as NestedValueAggregationResult, aS as QueryResourcesRequest, aV as QueryResourcesResponse, ao as RangeAggregation, az as RangeAggregationResult, ak as RangeBucket, aH as RangeResult, aD as RangeResults, aZ as ReindexTenantRequest, a_ as ReindexTenantResponse, a4 as RemoveResourceFromTrashBinRequest, a5 as RemoveResourceFromTrashBinResponse, v as ResourceCompositionDetailsOneOf, bc as ResourceSearchSpec, bd as ResourcesQueryResult, b2 as RestoreInfo, a6 as RestoreResourceFromTrashBinRequest, a7 as RestoreResourceFromTrashBinResponse, aL as Results, ap as ScalarAggregation, aI as ScalarResult, t as ScalarType, bj as ScalarTypeWithLiterals, y as Schedule, aw as SearchDetails, ae as SearchResourcesRequest, x as SingleResource, r as SortDirection, bh as SortDirectionWithLiterals, p as SortOrder, bf as SortOrderWithLiterals, q as SortType, bg as SortTypeWithLiterals, ah as Sorting, z as SpecificLocation, a8 as UpdateResourceRequest, a9 as UpdateResourceResponse, V as V2WorkingHoursSchedules, am as ValueAggregation, an as ValueAggregationOptionsOneOf, ay as ValueAggregationResult, aG as ValueResult, aC as ValueResults, W as WebhookIdentityType, bo as WebhookIdentityTypeWithLiterals, w as WorkingHoursSchedule, E as WorkingHoursSchedules } from './bookings-resources-v2-resource-resources.universal-WjeiBRqP.mjs';

declare function createResource$1(httpClient: HttpClient): CreateResourceSignature;
interface CreateResourceSignature {
    /**
     * Creates a new resource.
     *
     *
     * ## Connected schedules
     *
     * A new event *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration))
     * is automatically created for the resource.
     *
     * ## Locations
     *
     * If you don't specify `locationOptions`, Wix Bookings automatically sets
     * `locationOptions.availableInAllLocations` to `true`.
     *
     * If you specify 1 business location ID in `locationOptions.specificLocationOptions.businessLocations`,
     * you must specify `locationOptions.specificLocationOptions.availableInBusinessLocations`
     * as `true`. Currently, you can specify only a single business location.
     * @param - Resource to create.
     * @returns Created resource.
     */
    (resource: NonNullablePaths<Resource, `name`, 2>): Promise<NonNullablePaths<Resource, `workingHoursSchedules.values` | `locationOptions.specificLocationOptions.businessLocations`, 4> & {
        __validationErrorsType?: CreateResourceValidationErrors;
    }>;
}
declare function bulkCreateResources$1(httpClient: HttpClient): BulkCreateResourcesSignature;
interface BulkCreateResourcesSignature {
    /**
     * Creates up to 50 resources.
     *
     *
     * Refer to *Create Resource*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/create-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/create-resource))
     * for more details.
     * @param - Resources to create.
     */
    (resources: NonNullablePaths<Resource, `name`, 2>[], options?: BulkCreateResourcesOptions): Promise<NonNullablePaths<BulkCreateResourcesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
        __validationErrorsType?: BulkCreateResourcesValidationErrors;
    }>;
}
declare function getResource$1(httpClient: HttpClient): GetResourceSignature;
interface GetResourceSignature {
    /**
     * Retrieves a resource.
     * @param - ID of the resource to retrieve.
     * @returns Retrieved resource.
     */
    (resourceId: string): Promise<NonNullablePaths<Resource, `workingHoursSchedules.values` | `locationOptions.specificLocationOptions.businessLocations`, 4>>;
}
declare function updateResource$1(httpClient: HttpClient): UpdateResourceSignature;
interface UpdateResourceSignature {
    /**
     * Updates a resource.
     *
     *
     * Each time the resource is updated, `revision` increments by 1. You must include the current revision of the resource when updating it.
     * This ensures you're working with the latest service information and prevents unintended overwrites.
     * @param - Resource ID.
     * @returns Updated resource.
     */
    (_id: string, resource: NonNullablePaths<UpdateResource, `revision`, 2>): Promise<NonNullablePaths<Resource, `workingHoursSchedules.values` | `locationOptions.specificLocationOptions.businessLocations`, 4> & {
        __validationErrorsType?: UpdateResourceValidationErrors;
    }>;
}
declare function bulkUpdateResources$1(httpClient: HttpClient): BulkUpdateResourcesSignature;
interface BulkUpdateResourcesSignature {
    /**
     * Updates multiple resources.
     *
     *
     * Refer to *Update Resource*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/update-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/update-resource))
     * for more details.
     * @param - Resources to update.
     */
    (resources: NonNullablePaths<MaskedResource, `resource._id` | `resource.revision`, 3>[], options?: BulkUpdateResourcesOptions): Promise<NonNullablePaths<BulkUpdateResourcesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
        __validationErrorsType?: BulkUpdateResourcesValidationErrors;
    }>;
}
declare function deleteResource$1(httpClient: HttpClient): DeleteResourceSignature;
interface DeleteResourceSignature {
    /**
     * Deletes a resource.
     *
     *
     * Deleting a resource cancels its event schedule and all its working hour
     * schedules that aren't shared with another resource. Learn more about
     * _how Bookings uses the Calendar APIs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).
     * @param - ID of the resource to delete.
     */
    (resourceId: string): Promise<void>;
}
declare function bulkDeleteResources$1(httpClient: HttpClient): BulkDeleteResourcesSignature;
interface BulkDeleteResourcesSignature {
    /**
     * Deletes multiple resources.
     *
     *
     * Refer to *Delete Resource*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/delete-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/delete-resource))
     * for more details.
     * @param - IDs of the resources to delete.
     */
    (ids: string[]): Promise<NonNullablePaths<BulkDeleteResourcesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
}
declare function searchResources$1(httpClient: HttpClient): SearchResourcesSignature;
interface SearchResourcesSignature {
    /**
     * Retrieves a list of resources matching the provided search criteria.
     *
     *
     * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.
     * @param - Search criteria including filter, sort, aggregations, and paging options.
     *
     * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.
     */
    (search: ResourceSearch): Promise<NonNullablePaths<SearchResourcesResponse, `resources` | `aggregationData.results` | `aggregationData.results.${number}.scalar.type` | `aggregationData.results.${number}.scalar.value` | `aggregationData.results.${number}.name` | `aggregationData.results.${number}.type` | `aggregationData.results.${number}.fieldPath`, 6>>;
}
declare function queryResources$1(httpClient: HttpClient): QueryResourcesSignature;
interface QueryResourcesSignature {
    /**
     * Creates a query to retrieve a list of resources.
     *
     * The `queryResources()` function builds a query to retrieve a list of resources and returns a `ResourcesQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-builder/find) function.
     *
     * You can refine the query by chaining `ResourcesQueryBuilder` functions onto the query. `ResourcesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResources()` returns.
     *
     * `queryResources()` runs with the following `ResourcesQueryBuilder` default that you can override:
     *
     * + `limit` is `50`.
     * + Sorted by `id` in ascending order.
     *
     * The functions that are chained to `queryResources()` are applied in the order they are called. For example, if you apply `ascending("typeId")` and then `ascending("name")`, the results are sorted first by the `"typeId"`, and then, if there are multiple results with the same `"typeId"`, the items are sorted by `"name"`.
     *
     * The following `ResourcesQueryBuilder` functions are supported for the `queryResources()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-result/items) property in `ResourcesQueryResult`.
     */
    (): ResourcesQueryBuilder;
}
declare function countResources$1(httpClient: HttpClient): CountResourcesSignature;
interface CountResourcesSignature {
    /**
     * Counts resources according to given criteria.
     *
     *
     * Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters and sorting options.
     * @param - Filter to base the count on. See *the supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for a complete list of filtering options.
     */
    (options?: CountResourcesOptions): Promise<NonNullablePaths<CountResourcesResponse, `count`, 2>>;
}
declare const onResourceCreated$1: EventDefinition<ResourceCreatedEnvelope, "wix.bookings.resources.v2.resource_created">;
declare const onResourceDeleted$1: EventDefinition<ResourceDeletedEnvelope, "wix.bookings.resources.v2.resource_deleted">;
declare const onResourceUpdated$1: EventDefinition<ResourceUpdatedEnvelope, "wix.bookings.resources.v2.resource_updated">;

declare const createResource: MaybeContext<BuildRESTFunction<typeof createResource$1> & typeof createResource$1>;
declare const bulkCreateResources: MaybeContext<BuildRESTFunction<typeof bulkCreateResources$1> & typeof bulkCreateResources$1>;
declare const getResource: MaybeContext<BuildRESTFunction<typeof getResource$1> & typeof getResource$1>;
declare const updateResource: MaybeContext<BuildRESTFunction<typeof updateResource$1> & typeof updateResource$1>;
declare const bulkUpdateResources: MaybeContext<BuildRESTFunction<typeof bulkUpdateResources$1> & typeof bulkUpdateResources$1>;
declare const deleteResource: MaybeContext<BuildRESTFunction<typeof deleteResource$1> & typeof deleteResource$1>;
declare const bulkDeleteResources: MaybeContext<BuildRESTFunction<typeof bulkDeleteResources$1> & typeof bulkDeleteResources$1>;
declare const searchResources: MaybeContext<BuildRESTFunction<typeof searchResources$1> & typeof searchResources$1>;
declare const queryResources: MaybeContext<BuildRESTFunction<typeof queryResources$1> & typeof queryResources$1>;
declare const countResources: MaybeContext<BuildRESTFunction<typeof countResources$1> & typeof countResources$1>;
/**
 * Triggered when a resource is created.
 */
declare const onResourceCreated: BuildEventDefinition<typeof onResourceCreated$1>;
/**
 * Triggered when a resource is deleted.
 */
declare const onResourceDeleted: BuildEventDefinition<typeof onResourceDeleted$1>;
/**
 * Triggered when an resource is updated.
 */
declare const onResourceUpdated: BuildEventDefinition<typeof onResourceUpdated$1>;

export { BulkCreateResourcesOptions, BulkCreateResourcesResponse, BulkCreateResourcesValidationErrors, BulkDeleteResourcesResponse, BulkUpdateResourcesOptions, BulkUpdateResourcesResponse, BulkUpdateResourcesValidationErrors, CountResourcesOptions, CountResourcesResponse, CreateResourceValidationErrors, MaskedResource, Resource, ResourceCreatedEnvelope, ResourceDeletedEnvelope, ResourceSearch, ResourceUpdatedEnvelope, ResourcesQueryBuilder, SearchResourcesResponse, UpdateResource, UpdateResourceValidationErrors, bulkCreateResources, bulkDeleteResources, bulkUpdateResources, countResources, createResource, deleteResource, getResource, onResourceCreated, onResourceDeleted, onResourceUpdated, queryResources, searchResources, updateResource };
