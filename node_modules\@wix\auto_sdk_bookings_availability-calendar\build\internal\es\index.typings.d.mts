import { NonNullablePaths } from '@wix/sdk-types';

interface SlotAvailability {
    /**
     * The slot for the corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     */
    slot?: Slot;
    /**
     * Whether the slot is bookable. Bookability is determined by checking a
     * session's open slots and booking policies. Locks are not taken into
     * account.
     */
    bookable?: boolean;
    /**
     * Total number of spots for this slot.
     * For example, if a session has a total of 10 spots and 3 spots are booked,
     * `spotsTotal` is 10 and `openSpots` is 7.
     */
    totalSpots?: number | null;
    /** Number of open spots for this slot. */
    openSpots?: number | null;
    /** An object describing the slot's waitlist and its occupancy. */
    waitingList?: AvailabilityWaitingList;
    /** Booking policy violations for the slot. */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * Indicates whether the slot is locked because a waitlist exists.
     * When a slot frees up, the slot is offered to the next customer on the waitlist. Read-only.
     */
    locked?: boolean | null;
    /**
     * Deprecated. not in use since 2025-06-15.
     * @deprecated Deprecated. not in use since 2025-06-15.
     * @targetRemovalDate 2025-06-15
     */
    isFromV2?: boolean;
}
interface Slot {
    /**
     * ID for the slot's corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     *
     * Deprecated. Please use `eventId` instead.
     * @deprecated ID for the slot's corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     *
     * Deprecated. Please use `eventId` instead.
     * @replacedBy event_id
     * @targetRemovalDate 2025-09-30
     */
    sessionId?: string | null;
    /** Service ID. */
    serviceId?: string;
    /** Schedule ID. */
    scheduleId?: string;
    /**
     * The start time of this slot in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339)
     * format.
     *
     * If `timezone` is specified,
     * dates are based on the local date/time. This means that the timezone offset
     * in the `start_date` is ignored.
     */
    startDate?: string | null;
    /**
     * The end time of this slot in
     * [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339) format.
     *
     * If `timezone` is specified,
     * dates are based on the local date/time. This means that the timezone offset
     * in the `end_date` is ignored.
     */
    endDate?: string | null;
    /**
     * The timezone for which slot availability is to be calculated.
     *
     * Learn more about [handling Daylight Savings Time (DST) for local time zones](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_handling-daylight-savings-time-dst-for-local-time-zones)
     * when calculating availability.
     */
    timezone?: string | null;
    /**
     * The resource required for this slot. Currently, the only supported resource
     * is the relevant staff member for the slot.
     */
    resource?: SlotResource;
    /** Geographic location of the slot. */
    location?: Location;
    /**
     * ID for the slot's corresponding event, when the event is either a single event
     * or a specific event generated from a recurring event.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
}
interface SlotResource {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Resource name. Read only.
     * @maxLength 1200
     */
    name?: string | null;
}
interface Location {
    /**
     * Business location ID. Available only for locations that are business locations,
     * meaning the `location_type` is `"OWNER_BUSINESS"`.
     * @format GUID
     */
    _id?: string | null;
    /** Location name. */
    name?: string | null;
    /** The full address of this location. */
    formattedAddress?: string | null;
    /**
     * The full translated address of this location.
     * @maxLength 512
     */
    formattedAddressTranslated?: string | null;
    /**
     * Location type.
     *
     * - `"OWNER_BUSINESS"`: The business address, as set in the site’s general settings.
     * - `"OWNER_CUSTOM"`: The address as set when creating the service.
     * - `"CUSTOM"`: The address as set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface AvailabilityWaitingList {
    /**
     * Total number of spots and open spots for this waitlist.
     * For example, a Yoga class with 10 waitlist spots and 3 registered
     * on the waitlist has 10 `total_spots` and 7 `open_spots`.
     */
    totalSpots?: number | null;
    /** Number of open spots for this waitlist. */
    openSpots?: number | null;
}
interface BookingPolicyViolations {
    /** Bookings policy violation. Too early to book this slot. */
    tooEarlyToBook?: boolean | null;
    /** Bookings policy violation. Too late to book this slot. */
    tooLateToBook?: boolean | null;
    /** Bookings policy violation. Online booking is disabled for this slot. */
    bookOnlineDisabled?: boolean | null;
}
interface NestedTimeSlot {
    /** @format GUID */
    serviceId?: string;
    /** @maxLength 30 */
    start?: string;
    /** @maxLength 30 */
    end?: string;
    resource?: SlotResource;
    /** Schedule ID. */
    scheduleId?: string;
}
interface QueryAvailabilityRequest {
    /**
     * Query options. Refer to the
     * _supported filters article_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))
     * for a complete list of supported filters.
     */
    query: QueryV2;
    /**
     * Time zone override for the `endDate` and `startDate` filters, in
     * [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database), such as
     * `America/New_York` or `UTC`.
     *
     * For example, if you specify `("startDate": "2025-11-25T17:00:00+01:00"}` (note
     * the +01:00 offset) in combination with `{"timezone": "Europe/Bucharest"}`
     * (+02:00 offset), *Query Availability* adjusts the effective start time to
     * reflect the specified time zone. In this case, the start time in UTC would
     * be `2025-11-25T15:00:00` (using the +02:00 offset).
     *
     * Learn more about *handling Daylight Savings Time*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst) | [REST](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst)).
     *
     * Default: No modification applied to the `endDate` and `startDate` filters.
     */
    timezone?: string | null;
    /**
     * Maximum number of appointment slots to return for each date. For example, if
     * you specify `{"slotsPerDay": 3}`, a maximum of 3 appointment slots/class events is
     * returned for each day that's within the query filter's date range.
     */
    slotsPerDay?: number | null;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /**
     * Filter object. For a list of
     * fields you can filter by, see Availability Calendar supported filters ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting)).
     * You must include `serviceId`, `startDate` and `endDate` in the filter. This avoids large results that can impact performance.
     */
    filter?: Record<string, any> | null;
    /**
     * Sort options.
     * Currently, only sorting by `startDate` is supported. For details
     * on sorting, see Availability Calendar supported sorting ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting#sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting#sorting)).
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
/**
 * Selected customer choices.
 * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.
 */
interface V2CustomerChoices {
    /**
     * The selected customer duration in minutes.
     * Min: `1 minute`
     * Max: `44639 minutes` (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     */
    durationInMinutes?: number | null;
    /**
     * The selected add-ons IDs.
     * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.
     * @format GUID
     * @maxSize 21
     */
    addOnIds?: string[] | null;
    /**
     * The selected duration choice ID.
     * @format GUID
     */
    durationChoiceId?: string | null;
}
interface QueryAvailabilityResponse {
    /** List of appointment slots or class events matching the filter. */
    availabilityEntries?: SlotAvailability[];
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
    /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */
    cursors?: Cursors;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface GetSlotAvailabilityRequest {
    /** The slot for which the availability is checked. */
    slot?: Slot;
    /**
     * The timezone for which slot availability is to be calculated. If specified,
     * dates are based on the local date/time, meaning that the timezone offset
     * in the date's format is ignored.
     *
     * Learn more about [handling Daylight Savings Time (DST) for local time zones](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_handling-daylight-savings-time-dst-for-local-time-zones)
     * when calculating availability.
     */
    timezone?: string | null;
}
interface GetSlotAvailabilityResponse {
    availability?: SlotAvailability;
    bookingPolicySettings?: BookingPolicySettings;
}
interface BookingPolicySettings {
    /**
     * The policy defining the maximum number of participants that can
     * be booked for a slot or a schedule.
     */
    maxParticipantsPerBooking?: number | null;
}
interface GetScheduleAvailabilityRequest {
    /**
     * The schedule ID for which availability is being checked.
     * @format GUID
     */
    scheduleId: string;
}
interface GetScheduleAvailabilityResponse {
    availability?: ScheduleAvailability;
    bookingPolicySettings?: BookingPolicySettings;
}
interface ScheduleAvailability {
    /**
     * The total number of spots defined for the schedule, including
     * both open and non-available spots.
     */
    totalSpots?: number | null;
    /** The number of open spots defined for the schedule. */
    openSpots?: number | null;
    /** Booking policy violations for the schedule. */
    bookingPolicyViolations?: BookingPolicyViolations;
}
interface CalculateMultiSlotAvailabilityRequest {
    /** @maxLength 30 */
    from?: string;
    /** @maxLength 30 */
    to?: string;
    /** @maxLength 30 */
    timeZone?: string;
    /** TODO good definition of what bookable means https://github.com/wix-private/scheduler/pull/18267/files?file-filters%5B%5D=.proto&show-viewed-files=true#r1199809006 */
    bookable?: boolean | null;
    /**
     * each nested field is checked on its own. i.e. if `too_early_to_book` is defined and `too_late_to_book` is not defined
     * we will return slots for which `too_early_to_book` is same as on the request, regardless of `too_late_to_book`.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * support filtering by location type, or by locationId. Other fields like `name` are ignored
     * must be set, and must have locationType. If locationType is `OWNER_BUSINESS`, must have location_id
     */
    location?: Location;
    /**
     * @minSize 1
     * @maxSize 6
     */
    slots?: RuleBasedConstraints[];
    /**
     * Maximum number of slots to load for each date. For example, if `slots_per_day` is set to `3`,
     * at most 3 available slots are returned for each day in the date range specified in the query's
     * `filter`.
     *
     * When a day has both bookable and non-bookable slots, bookable slots are returned first.
     * Non-bookable slots are returned according to the specified filters, after all
     * bookable slots are already included.
     */
    slotsPerDay?: number | null;
    cursorPaging?: CursorPaging;
}
interface RuleBasedConstraints {
    /** @format GUID */
    serviceId?: string;
    resourcesFilter?: ResourcesFilter;
    /** will be passed to availability-2, and to the availability-constraints SPI */
    customerChoices?: AvailabilityV2CustomerChoices;
}
interface ResourcesFilter {
    resourceIds?: string[];
}
/**
 * Selected customer choices.
 * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.
 */
interface AvailabilityV2CustomerChoices {
    /**
     * Selected customer duration in minutes.
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * Default: `15` minutes
     * @min 1
     * @max 44639
     */
    durationInMinutes?: number | null;
    /**
     * Selected add-on IDs.
     *
     * Max: Calculated as the product of the maximum number of add-on groups multiplied by the maximum number of add-ons per group. Currently 21 (3 groups × 7 add-ons per group), but may change in the future.
     * @format GUID
     * @maxSize 21
     */
    addOnIds?: string[] | null;
    /**
     * Selected duration choice ID.
     * @format GUID
     */
    durationChoiceId?: string | null;
}
interface CalculateMultiSlotAvailabilityResponse {
    slots?: SlotAvailability[];
    cursorPagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in current page. */
    count?: number | null;
    /** Cursor strings that point to the next page, previous page, or both. */
    cursors?: Cursors;
    /**
     * Whether there are more pages to retrieve following the current page.
     *
     * + `true`: Another page of results can be retrieved.
     * + `false`: This is the last page.
     */
    hasNext?: boolean | null;
}
interface GetAvailabilityTimeSlotRequest {
    /**
     * Service ID of the time slot.
     * Currently supported only for services of type `APPOINTMENT`.
     * @format GUID
     */
    serviceId?: string;
    /**
     * Local start date of the time slot, in ISO-8601 format.
     * For example, "2024-01-30T13:30:00".
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string;
    /**
     * Local end date of the time slot, in ISO-8601 format.
     * For example, "2024-01-30T14:30:00".
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string;
    /**
     * Time zone, in IANA time zone format.
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * The location of the time slot.
     *
     * You must provide a specific `locationType`.
     * If locationType is `BUSINESS`, you __must__ also provide a `locationId`.
     *
     * <blockquote class="warning">
     * <p>Supports filtering by location type, or by location ID.
     * Other fields like <code class="grey-background">name</code> are ignored.</p>
     * </blockquote>
     */
    location?: TimeSlotLocation;
    /**
     * Resources to include in response.
     *
     * If specified,
     * the returned `TimeSlot` contains only the available resources out of provided list.
     * Otherwise, the returned `TimeSlot` returned with all possible `AvailableResources`.
     * @format GUID
     * @maxSize 135
     */
    resourceIds?: string[];
    /**
     * Resource type IDs to include in response, this is in addition to the requested `resourceIds`.
     *
     * If specified in request, the returned `TimeSlot` contains only the `AvailableResources` with `ResourceTypeId`
     * out of those specified, each contains all the available resources of this type.
     * @format GUID
     * @maxSize 100
     */
    includeResourceTypeIds?: string[];
    /**
     * Deprecated. not in use since 2025-06-15.
     * @deprecated Deprecated. not in use since 2025-06-15.
     * @targetRemovalDate 2025-06-15
     */
    fromV2?: boolean | null;
}
interface TimeSlotLocation {
    /**
     * Business Location ID. Present only if the location is a business location.
     * @format GUID
     */
    _id?: string | null;
    /**
     * The location name.
     * @maxLength 250
     */
    name?: string | null;
    /**
     * A string representation for the full address of the location.
     * @maxLength 300
     */
    formattedAddress?: string | null;
    /**
     * The type of location:
     * - `CUSTOM`: The location is specific to this service, and is not derived from the business location.
     * - `BUSINESS`: A business location, either the default business address, or locations defined for the business by the Business Info.
     * - `CUSTOMER`: The location is determined by the customer and is not set up beforehand.
     */
    locationType?: LocationLocationTypeWithLiterals;
}
declare enum LocationLocationType {
    UNKNOWN_LOCATION_TYPE = "UNKNOWN_LOCATION_TYPE",
    /** A business location, either the default business address, or locations defined for the business by the Business Info. */
    BUSINESS = "BUSINESS",
    /** The location is unique to this service and isn't defined as one of the business locations. */
    CUSTOM = "CUSTOM",
    /** The location can be determined by the customer and is not set up beforehand. */
    CUSTOMER = "CUSTOMER"
}
/** @enumType */
type LocationLocationTypeWithLiterals = LocationLocationType | 'UNKNOWN_LOCATION_TYPE' | 'BUSINESS' | 'CUSTOM' | 'CUSTOMER';
interface CustomerChoices {
    /**
     * The selected add-ons IDs.
     * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.
     * @format GUID
     * @maxSize 21
     */
    addOnIds?: string[] | null;
    /**
     * The selected duration choice ID.
     * @format GUID
     */
    durationChoiceId?: string | null;
}
interface GetAvailabilityTimeSlotResponse {
    /** Time slot. */
    timeSlot?: TimeSlot;
    /**
     * Time zone, in IANA time zone format.
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
}
/**
 * The `TimeSlot` object represents the availability information
 * for an `Appointment` service's specific slot, including:
 *
 * 1. Whether the slot is bookable for the given service?
 *
 * 2. In what location the service is available for this slot?
 *
 * 3. Which available resources can provide the service for this slot?
 *
 * 4. Does booking the slot for the service violates any of the service booking policies?
 *
 * 5. What is the total capacity and remaining capacity of the service at the time of the calculation of the `TimeSlot`?
 *
 * > __Note:__
 * > When the `TimeSlot` has a non empty `NestedTimeSlots`, it represents the availability information
 * > for a given list of `Appointment` services within a specific time slot.
 */
interface TimeSlot {
    /**
     * Service ID.
     *
     * > Not returned from `MultiServiceAvailabilityTimeSlots` API calls.
     * > Instead, each nested time slot has its own serviceId.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Local start date of the time slot in ISO-8601 format.
     * For example, "2024-01-30T13:30:00".
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string | null;
    /**
     * Local end date of the time slot in ISO-8601 format.
     * For example, "2024-01-30T14:30:00".
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string | null;
    /**
     * Whether the slot is bookable according to the service's booking policies.
     *
     * If booking this time slot does not violates any of the service's booking policies,
     * the returned value is `true`. Otherwise, returns `false`.
     */
    bookable?: boolean | null;
    /** The geographic location of the slot. */
    location?: TimeSlotLocation;
    /**
     * Total number of spots for the slot.
     * @min 1
     * @max 1000
     */
    totalCapacity?: number | null;
    /**
     * Remaining number of spots for the slot.
     * For example, for an appointment service with total capacity of 1 spot and one booked spot, the remaining capacity will be 0.
     * @max 1000
     */
    remainingCapacity?: number | null;
    /**
     * Indicators for booking policy violations for the slot.
     *
     * Each nested field is checked on its own. i.e. if `tooEarlyToBook` is defined and `bookOnlineDisabled` is not defined
     * we will return also slots for which `tooEarlyToBook` is same as on the request, regardless of `bookOnlineDisabled`.
     */
    bookingPolicyViolations?: Service_availabilityBookingPolicyViolations;
    /**
     * List of `AvailableResources` for the time slot.
     * Each `AvailableResources` contains information about available resources of the same type.
     *
     * > Not returned from `MultiServiceAvailabilityTimeSlots` API calls.
     * > Instead, each nested time slot has its own available resources.
     */
    availableResources?: AvailableResources[];
    /**
     * > Nested time slots.
     * > Returned only from `MultiServiceAvailabilityTimeSlots` API calls.
     * @maxSize 8
     */
    nestedTimeSlots?: Service_availabilityNestedTimeSlot[];
}
/** relevant for event based slots, and not for availability based slots */
interface EventInfo {
}
interface WaitingList {
    /**
     * Total number of spots in this wait list.
     * @min 1
     */
    totalCapacity?: number | null;
    /**
     * Number of remaining spots for this wait list.
     * For example, a Yoga event with 10 waitList spots and 3 registered
     * on the waitList has 10 `total_capacity` and 7 `remaining_capacity`.
     */
    remainingCapacity?: number | null;
}
interface Service_availabilityBookingPolicyViolations {
    /** Bookings policy violation. Too early to book this slot. */
    tooEarlyToBook?: boolean | null;
    /** Bookings policy violation. Too late to book this slot. */
    tooLateToBook?: boolean | null;
    /** Bookings policy violation. Online booking is disabled for the `TimeSlot` service. */
    bookOnlineDisabled?: boolean | null;
}
interface AvailableResources {
    /**
     * Resource type ID.
     * @format GUID
     */
    resourceTypeId?: string | null;
    /**
     * Available resources for the time slot.
     *
     * + When returned from `ListAvailabilityTimeSlots`, empty by default.
     * + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,
     * contains __up__ to 10 available resources out of those provided.
     *
     * + When returned from `GetAvailabilityTimeSlots`, contains all available resources by default.
     * + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,
     * contains all of the available resources out of those provided.
     *
     *
     * > + When returned from `ListMultiServiceAvailabilityTimeSlots`, empty by default.
     * > + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,
     * > contains __up__ to 10 available resources out of those provided.
     *
     * > + When returned from `GetMultiServiceAvailabilityTimeSlots`, contains all available resources by default.
     * > + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,
     * > contains all of the available resources out of those provided.
     */
    resources?: Resource[];
    /**
     * Whether there are more available resources for the slot that are not listed in `resources` due to size limitations.
     * @readonly
     */
    hasMoreAvailableResources?: boolean | null;
}
interface Resource {
    /**
     * Resource ID.
     * @format GUID
     */
    _id?: string;
    /**
     * Resource name.
     * @minLength 1
     * @maxLength 40
     */
    name?: string | null;
}
interface Service_availabilityNestedTimeSlot {
    /**
     * Service ID of the nested time slot.
     * @format GUID
     */
    serviceId?: string;
    /**
     * Local start date of the nested time slot in ISO-8601 format.
     * For example, "2024-01-30T13:30:00".
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string;
    /**
     * Local end date of the nested time slot in ISO-8601 format.
     * For example, "2024-01-30T14:30:00".
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string;
    /**
     * List of `AvailableResources` for the nested time slot.
     * Each `AvailableResources` contains information about available resources of the same type.
     * @maxSize 10
     */
    availableResources?: AvailableResources[];
}
/** @docsIgnore */
type GetScheduleAvailabilityApplicationErrors = {
    code?: 'FAILED_VALIDATING_AVAILABILITY';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_RESOLVING_SCHEDULE';
    description?: string;
    data?: Record<string, any>;
};
/**
 * Retrieves appointment slots or class events that match specified filters.
 *
 * <blockquote>
 *
 * Query Availability doesn't use a query builder, instead it uses
 * [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language).
 *
 * </blockquote>
 *
 * ## Course limitations
 *
 * Course events can't be retrieved with this method. Follow this
 * *sample flow* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/sample-flows#backend-modules_bookings_bookings_book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course))
 * to check course availability.
 *
 * ## Query filter
 *
 * You must include `serviceId` in your query filter. For performance, also specify
 * both `startDate` and `endDate`. Refer to the
 * *supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))
 * for a complete list of supported filters.
 *
 * ## Limit the number of slots returned per day
 *
 * To limit the maximum number of appointment slots returned for each date,
 * specify `slotsPerDay`. For example,  specifying `{"slotsPerDay": 3}` returns
 * a maximum of 3 appointment slots/class events for each day that's within the query
 * filter's date range.
 *
 * ## Unavailable slots
 *
 * By default, both available and unavailable appointment slots/ class events
 * are returned. To retrieve only available slots/events, specify
 * `{"bookable": true}` in the filter.
 *
 * ## Sorting
 *
 * Slots/events can be sorted only by `startDate`. Unavailable slots/events
 * always appear after available ones.
 *
 * ## Availability calculation
 *
 * Wix Bookings calculates availability differently for appointment-based
 * services and classes.
 *
 * ### Appointment availability
 *
 * An appointment slot is `bookable` if:
 * - The slot's `startDate` is later than or equal to `query.filter.startDate`.
 * - The slot's `endDate` is before or equal to `query.filter.endDate`.
 * - The service's `onlineBooking.enabled` is `true`.
 * - The service's early/late booking policies are either not enabled or the
 *   current time is within the defined limits.
 *
 * ### Class event availability
 *
 * A class event is `bookable` if:
 * - The event's `startDate` is later than or equal to `query.filter.startDate`.
 * - The event's `endDate` is before or equal to `query.filter.endDate`.
 * - The event's `remainingCapacity` is greater than `0`.
 * - The service's `onlineBooking.enabled` is `true`.
 * - The service's early/late booking policies are either not enabled or the
 *   current time is within the defined limits.
 *
 * If you want to restrict returned class events to those with a minimum number
 * of remaining spots, specify `openSpots` in the filter. By default, all class
 * events, even those with no open spot, are returned.
 *
 * ## Daylight Savings Time (DST)
 *
 * Due to DST, some local times may not exist or may occur twice. For example,
 * in Santiago, Chile, on September 7, 2025, 0:05 AM doesn't exist because the
 * clock advances from 0:00 AM to 1:00 AM.
 *
 * *Query Availability* automatically adjusts for these gaps. Non-existent times
 * are moved forward by 1 hour, while existing times remain unchanged. For
 * instance, if you call *Query Availability* with a `startDate` of
 * `2025-09-07T00:00:01.000` and an `endDate` of `2025-09-08T00:00:02.000`, the
 * adjusted query will use `2025-09-07T01:00:01.000` as the new start time.
 * @param query - Query options. Refer to the
 * _supported filters article_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))
 * for a complete list of supported filters.
 * @public
 * @documentationMaturity preview
 * @requiredField query
 * @requiredField query.filter
 * @param options - Options to use for querying availability.
 * @permissionId BOOKINGS.AVAILABILITY_READ
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.availability.AvailabilityCalendar.QueryAvailability
 */
declare function queryAvailability(query: NonNullablePaths<QueryV2, `filter`, 2>, options?: QueryAvailabilityOptions): Promise<NonNullablePaths<QueryAvailabilityResponse, `availabilityEntries` | `availabilityEntries.${number}.slot.serviceId` | `availabilityEntries.${number}.slot.scheduleId` | `availabilityEntries.${number}.slot.location.locationType` | `availabilityEntries.${number}.bookable` | `availabilityEntries.${number}.isFromV2`, 6>>;
interface QueryAvailabilityOptions {
    /**
     * Time zone override for the `endDate` and `startDate` filters, in
     * [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database), such as
     * `America/New_York` or `UTC`.
     *
     * For example, if you specify `("startDate": "2025-11-25T17:00:00+01:00"}` (note
     * the +01:00 offset) in combination with `{"timezone": "Europe/Bucharest"}`
     * (+02:00 offset), *Query Availability* adjusts the effective start time to
     * reflect the specified time zone. In this case, the start time in UTC would
     * be `2025-11-25T15:00:00` (using the +02:00 offset).
     *
     * Learn more about *handling Daylight Savings Time*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst) | [REST](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst)).
     *
     * Default: No modification applied to the `endDate` and `startDate` filters.
     */
    timezone?: string | null;
    /**
     * Maximum number of appointment slots to return for each date. For example, if
     * you specify `{"slotsPerDay": 3}`, a maximum of 3 appointment slots/class events is
     * returned for each day that's within the query filter's date range.
     */
    slotsPerDay?: number | null;
}
/**
 * Returns availability for a given schedule ID
 * The availability for a course is calculated by:
 * - Checking for total spots by the schedule's capacity
 * - Checking for open spots by subtracting the current number of participants from the total spots
 * current number of participants is calculated by summing the number of participants of all bookings booked to the schedule
 * @param scheduleId - The schedule ID for which availability is being checked.
 * @public
 * @documentationMaturity preview
 * @requiredField scheduleId
 * @fqn com.wixpress.bookings.availability.AvailabilityCalendar.GetScheduleAvailability
 * @deprecated
 */
declare function getScheduleAvailability(scheduleId: string): Promise<GetScheduleAvailabilityResponse & {
    __applicationErrorsType?: GetScheduleAvailabilityApplicationErrors;
}>;
/** @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.AVAILABILITY_READ_MULTI_SLOT
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.availability.AvailabilityCalendar.CalculateMultiSlotAvailability
 */
declare function calculateMultiSlotAvailability(options?: CalculateMultiSlotAvailabilityOptions): Promise<NonNullablePaths<CalculateMultiSlotAvailabilityResponse, `slots` | `slots.${number}.slot.serviceId` | `slots.${number}.slot.scheduleId` | `slots.${number}.slot.location.locationType` | `slots.${number}.bookable` | `slots.${number}.isFromV2`, 6>>;
interface CalculateMultiSlotAvailabilityOptions {
    /** @maxLength 30 */
    from?: string;
    /** @maxLength 30 */
    to?: string;
    /** @maxLength 30 */
    timeZone?: string;
    /** TODO good definition of what bookable means https://github.com/wix-private/scheduler/pull/18267/files?file-filters%5B%5D=.proto&show-viewed-files=true#r1199809006 */
    bookable?: boolean | null;
    /**
     * each nested field is checked on its own. i.e. if `too_early_to_book` is defined and `too_late_to_book` is not defined
     * we will return slots for which `too_early_to_book` is same as on the request, regardless of `too_late_to_book`.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * support filtering by location type, or by locationId. Other fields like `name` are ignored
     * must be set, and must have locationType. If locationType is `OWNER_BUSINESS`, must have location_id
     */
    location?: Location;
    /**
     * @minSize 1
     * @maxSize 6
     */
    slots?: RuleBasedConstraints[];
    /**
     * Maximum number of slots to load for each date. For example, if `slots_per_day` is set to `3`,
     * at most 3 available slots are returned for each day in the date range specified in the query's
     * `filter`.
     *
     * When a day has both bookable and non-bookable slots, bookable slots are returned first.
     * Non-bookable slots are returned according to the specified filters, after all
     * bookable slots are already included.
     */
    slotsPerDay?: number | null;
    cursorPaging?: CursorPaging;
}

export { type AvailabilityV2CustomerChoices, type AvailabilityWaitingList, type AvailableResources, type BookingPolicySettings, type BookingPolicyViolations, type CalculateMultiSlotAvailabilityOptions, type CalculateMultiSlotAvailabilityRequest, type CalculateMultiSlotAvailabilityResponse, type CursorPaging, type CursorPagingMetadata, type Cursors, type CustomerChoices, type EventInfo, type GetAvailabilityTimeSlotRequest, type GetAvailabilityTimeSlotResponse, type GetScheduleAvailabilityApplicationErrors, type GetScheduleAvailabilityRequest, type GetScheduleAvailabilityResponse, type GetSlotAvailabilityRequest, type GetSlotAvailabilityResponse, type Location, LocationLocationType, type LocationLocationTypeWithLiterals, LocationType, type LocationTypeWithLiterals, type NestedTimeSlot, type PagingMetadataV2, type QueryAvailabilityOptions, type QueryAvailabilityRequest, type QueryAvailabilityResponse, type QueryV2, type QueryV2PagingMethodOneOf, type Resource, type ResourcesFilter, type RuleBasedConstraints, type ScheduleAvailability, type Service_availabilityBookingPolicyViolations, type Service_availabilityNestedTimeSlot, type Slot, type SlotAvailability, type SlotResource, SortOrder, type SortOrderWithLiterals, type Sorting, type TimeSlot, type TimeSlotLocation, type V2CustomerChoices, type WaitingList, calculateMultiSlotAvailability, getScheduleAvailability, queryAvailability };
