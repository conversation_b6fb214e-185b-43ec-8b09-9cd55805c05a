import { NonNullablePaths } from '@wix/sdk-types';

/**
 * A booking policy is a set of rules that determine how customers can book a
 * service, including timeframes for booking, canceling, or rescheduling.
 */
interface BookingPolicy {
    /**
     * ID of the booking policy.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking policy is updated.
     * To prevent conflicting changes, the current `revision` must be passed when
     * updating the booking policy.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the booking policy was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the booking policy was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the booking policy.
     * @maxLength 400
     */
    name?: string | null;
    /**
     * Custom description for the booking policy and whether it's displayed to the
     * participant.
     */
    customPolicyDescription?: PolicyDescription;
    /**
     * Whether the booking policy is the default.
     * @readonly
     */
    default?: boolean | null;
    /** Rule for limiting early bookings. */
    limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;
    /**
     * Rule for limiting late bookings. This rule and `bookAfterStartPolicy` can't
     * be both enabled at the same time.
     */
    limitLateBookingPolicy?: LimitLateBookingPolicy;
    /**
     * Rule for booking after the start of a session or course. This rule and
     * `limitLateBookingPolicy` can't be both enabled at the same time.
     */
    bookAfterStartPolicy?: BookAfterStartPolicy;
    /** Rule for canceling a booking. */
    cancellationPolicy?: CancellationPolicy;
    /** Rule for rescheduling a booking. */
    reschedulePolicy?: ReschedulePolicy;
    /** Waitlist rule for the service. */
    waitlistPolicy?: WaitlistPolicy;
    /** Rule for participants per booking. */
    participantsPolicy?: ParticipantsPolicy;
    /** Rules for cancellation fees. */
    cancellationFeePolicy?: CancellationFeePolicy;
    /** Rule for saving credit card details. */
    saveCreditCardPolicy?: SaveCreditCardPolicy;
    /** Extensions enabling users to save custom data related to the booking policies. */
    extendedFields?: ExtendedFields;
}
/** A description of the booking policy to display to participants. */
interface PolicyDescription {
    /**
     * Whether the description is displayed to the participant. `true` means the
     * description is displayed.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Description of the booking policy.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    description?: string;
}
/** The rule for limiting early bookings. */
interface LimitEarlyBookingPolicy {
    /**
     * Whether there's a limit about how early a customer can book. `false` means there's
     * no limit to the earliest supported booking time.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Maximum number of minutes before the start of a session or course customers can book.
     * Must be greater than `limitLateBookingPolicy.latestBookingInMinutes`.
     *
     * Default: `10080` minutes (7 days)
     * Min: `1` minute
     * @min 1
     */
    earliestBookingInMinutes?: number;
}
/**
 * Rule limiting late bookings.
 *
 * This rule and `bookAfterStartPolicy` can't be both enabled at the same time.
 */
interface LimitLateBookingPolicy {
    /**
     * Whether there's a limit about how late customers can book. `false` means
     * customers can book up to the last minute. If specified as `true`,
     * `bookAfterStartPolicy.enabled` must be `false`.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Minimum number of minutes before the start of the session customers can book.
     * For courses, this is relative to the start time of the next session and doesn't
     * consider course sessions in the past. This value must be less than
     * `limitEarlyBookingPolicy.earliestBookingInMinutes`.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestBookingInMinutes?: number;
}
/**
 * The rule for whether a session can be booked after the start of the schedule.
 * This rule and `LimitLateBookingPolicy` cannot be enabled at the same time. So if this rule
 * is enabled, the `LimitLateBookingPolicy` rule must be disabled.
 */
interface BookAfterStartPolicy {
    /**
     * Whether customers can book after the session has started. `true` means
     * customers can book after the session has started. For courses, this refers to
     * the start of the last course session. If specified as `true`,
     * `limitLateBookingPolicy.enabled` must be `false`.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
/** The rule for canceling a booked session. */
interface CancellationPolicy {
    /**
     * Whether customers can cancel the booking. `true` means customers can cancel
     * the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest cancellation time. `false` means customers
     * can cancel the booking until the last minute before the course or session starts.
     *
     * Default: `false`
     */
    limitLatestCancellation?: boolean;
    /**
     * Minimum number of minutes before the start of the session customers can cancel.
     * For courses, this refers to the start of the first course session.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestCancellationInMinutes?: number;
}
/** The rule for rescheduling a booked session. */
interface ReschedulePolicy {
    /**
     * Whether customers can reschedule a booking for an appointment-based service.
     * `true` means customers can reschedule.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest supported rescheduling time. `false`
     * means customers can reschedule until the last minute before the session start.
     *
     * Default: `false`
     */
    limitLatestReschedule?: boolean;
    /**
     * Minimum number of minutes before the session start session customers can
     * reschedule their booking.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestRescheduleInMinutes?: number;
}
/** The rule for the waitlist. */
interface WaitlistPolicy {
    /**
     * Whether the service has a waitlist. `true` means there's a waitlist.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Number of spots in the waitlist.
     *
     * Default: `10` spots
     * Min: `1` spot
     * @min 1
     */
    capacity?: number;
    /**
     * Time in minutes the potential customer is given to book after they've been
     * notified that a spot has opened up for them. If they don't respond in time,
     * the open spot is offered to the next potential customer on the waitlist.
     *
     * Default: `10` minutes
     * Min: `1` minute
     * @min 1
     */
    reservationTimeInMinutes?: number;
}
/** The rule for the maximum number of participants per booking. */
interface ParticipantsPolicy {
    /**
     * Maximum number of participants per booking.
     *
     * Default: `1` participant
     * Min: `1` participant
     * @min 1
     */
    maxParticipantsPerBooking?: number;
}
/** The rule regarding the allocation of resources (e.g. staff members). */
interface ResourcesPolicy {
    /**
     * Whether the customer must select a resource, for example a staff member, when
     * booking. `false` means the customer can book without selecting a resource.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether Wix Bookings automatically assigns a resource, for example a *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)),
     * to the booking. `false` means the customer must select the resource
     * themselves and Wix Bookings doesn't assign it automatically.
     *
     * Default: `false`
     */
    autoAssignAllowed?: boolean;
}
interface CancellationFeePolicy {
    /**
     * Whether customers must pay a cancellation fee when canceling a booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Time windows relative to the session start during which customers can cancel
     * their booking. Each window includes details about the fee for canceling within it.
     * @maxSize 2
     */
    cancellationWindows?: CancellationWindow[];
    /**
     * Whether Wix automatically charges the cancellation fee from the customer once
     * they cancel their booking.
     *
     * Default: `true`
     */
    autoCollectFeeEnabled?: boolean | null;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gt:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     * @readonly
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface CancellationWindow extends CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within
     * this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
    /**
     * Start of the cancellation window in minutes before the session start. For
     * courses, this refers to the start of the first course session.
     * @min 1
     */
    startInMinutes?: number | null;
}
/** @oneof */
interface CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within
     * this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
}
interface SaveCreditCardPolicy {
    /**
     * Whether Wix stores credit card details of the customer. Storing the details
     * allows Wix to prefill the *eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction))
     * and thus increases the likelihood that the customer completes the booking
     * process.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
/** This event is triggered when a different booking policy has been set as the default policy. */
interface DefaultBookingPolicySet {
    /** The new default booking policy. */
    currentDefaultBookingPolicy?: BookingPolicy;
    /**
     * The booking policy that was the default before this endpoint was called.
     * This field will be empty if there was no default booking policy before this method was called.
     */
    previousDefaultBookingPolicy?: BookingPolicy;
}
interface CreateBookingPolicyRequest {
    /** Booking policy to create. */
    bookingPolicy: BookingPolicy;
}
interface CreateBookingPolicyResponse {
    /** Created booking policy. */
    bookingPolicy?: BookingPolicy;
}
interface GetBookingPolicyRequest {
    /**
     * ID of the booking policy to retrieve.
     * @format GUID
     */
    bookingPolicyId: string;
}
interface GetBookingPolicyResponse {
    /** Retrieved booking policy. */
    bookingPolicy?: BookingPolicy;
}
interface GetStrictestBookingPolicyRequest {
    /**
     * IDs of the booking policies for which to retrieve the strictest rules for.
     * @format GUID
     * @maxSize 100
     */
    bookingPolicyIds: string[];
}
interface GetStrictestBookingPolicyResponse {
    /**
     * Hypothetical `bookingPolicy` object that combines the strictest version of
     * each rule. `bookingPolicy.id` is `null` and the returned object isn't actually
     * created. To create a new policy, you can call *Create Booking Policy*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).
     */
    bookingPolicy?: BookingPolicy;
}
interface UpdateBookingPolicyRequest {
    /** Booking policy to update. */
    bookingPolicy: BookingPolicy;
}
interface UpdateBookingPolicyResponse {
    /** Updated booking policy. */
    bookingPolicy?: BookingPolicy;
}
interface SetDefaultBookingPolicyRequest {
    /**
     * ID of the booking policy that's set as default.
     * @format GUID
     */
    bookingPolicyId: string;
}
interface SetDefaultBookingPolicyResponse {
    /** New default booking policy. */
    currentDefaultBookingPolicy?: BookingPolicy;
    /**
     * Previous default booking policy. Not available if the provided booking policy
     * was already the default.
     */
    previousDefaultBookingPolicy?: BookingPolicy;
}
interface DeleteBookingPolicyRequest {
    /**
     * ID of the booking policy to delete.
     * @format GUID
     */
    bookingPolicyId: string;
}
interface DeleteBookingPolicyResponse {
}
interface QueryBookingPoliciesRequest {
    /**
     * The query by which to select booking policies. See
     * [the supported filters article](https://dev.wix.com/docs/rest/business-solutions/bookings/services/booking-policy/supported-filters)
     * for details.
     */
    query: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 10
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryBookingPoliciesResponse {
    /** Retrieved booking policies. */
    bookingPolicies?: BookingPolicy[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor pointing to next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface CountBookingPoliciesRequest {
    /**
     * Filter to base the count on. See
     * supported filters*
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
     * for details.
     */
    filter?: Record<string, any> | null;
}
interface CountBookingPoliciesResponse {
    /** Number of booking policies matching the provided filter. */
    count?: number;
}
interface UpdateAllPoliciesRequest {
    /** Fields to set to all booking policies. */
    bookingPolicy?: BookingPolicy;
}
interface UpdateAllPoliciesResponse {
}
interface CreateMissingDefaultPolicyRequest {
}
interface CreateMissingDefaultPolicyResponse {
    /** The default booking policy. */
    defaultBookingPolicy?: BookingPolicy;
    /** Whether a new default policy was created. */
    wasANewPolicyCreated?: boolean;
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface Empty {
}
/** Encapsulates all details written to the Greyhound topic when a site's properties are updated. */
interface SitePropertiesNotification {
    /** The site ID for which this update notification applies. */
    metasiteId?: string;
    /** The actual update event. */
    event?: SitePropertiesEvent;
    /**
     * A convenience set of mappings from the MetaSite ID to its constituent services.
     * @maxSize 500
     */
    translations?: Translation[];
    /** Context of the notification */
    changeContext?: ChangeContext;
}
/** The actual update event for a particular notification. */
interface SitePropertiesEvent {
    /** Version of the site's properties represented by this update. */
    version?: number;
    /** Set of properties that were updated - corresponds to the fields in "properties". */
    fields?: string[];
    /** Updated properties. */
    properties?: Properties;
}
interface Properties {
    /** Site categories. */
    categories?: Categories;
    /** Site locale. */
    locale?: Locale;
    /**
     * Site language.
     *
     * Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format.
     */
    language?: string | null;
    /**
     * Site currency format used to bill customers.
     *
     * Three-letter currency code in [ISO-4217 alphabetic](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) format.
     */
    paymentCurrency?: string | null;
    /** Timezone in `America/New_York` format. */
    timeZone?: string | null;
    /** Email address. */
    email?: string | null;
    /** Phone number. */
    phone?: string | null;
    /** Fax number. */
    fax?: string | null;
    /** Address. */
    address?: Address;
    /** Site display name. */
    siteDisplayName?: string | null;
    /** Business name. */
    businessName?: string | null;
    /** Path to the site's logo in Wix Media (without Wix Media base URL). */
    logo?: string | null;
    /** Site description. */
    description?: string | null;
    /**
     * Business schedule. Regular and exceptional time periods when the business is open or the service is available.
     *
     * __Note:__ Not supported by Wix Bookings.
     */
    businessSchedule?: BusinessSchedule;
    /** Supported languages of a site and the primary language. */
    multilingual?: Multilingual;
    /** Cookie policy the Wix user defined for their site (before the site visitor interacts with/limits it). */
    consentPolicy?: ConsentPolicy;
    /**
     * Supported values: `FITNESS SERVICE`, `RESTAURANT`, `BLOG`, `STORE`, `EVENT`, `UNKNOWN`.
     *
     * Site business type.
     */
    businessConfig?: string | null;
    /** External site URL that uses Wix as its headless business solution. */
    externalSiteUrl?: string | null;
    /** Track clicks analytics. */
    trackClicksAnalytics?: boolean;
}
interface Categories {
    /** Primary site category. */
    primary?: string;
    /**
     * Secondary site category.
     * @maxSize 50
     */
    secondary?: string[];
    /** Business Term Id */
    businessTermId?: string | null;
}
interface Locale {
    /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */
    languageCode?: string;
    /** Two-letter country code in [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements) format. */
    country?: string;
}
interface Address {
    /** Street name. */
    street?: string;
    /** City name. */
    city?: string;
    /** Two-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format. */
    country?: string;
    /** State. */
    state?: string;
    /**
     * Zip or postal code.
     * @maxLength 20
     */
    zip?: string;
    /** Extra information to be displayed in the address. */
    hint?: AddressHint;
    /** Whether this address represents a physical location. */
    isPhysical?: boolean;
    /** Google-formatted version of this address. */
    googleFormattedAddress?: string;
    /** Street number. */
    streetNumber?: string;
    /** Apartment number. */
    apartmentNumber?: string;
    /** Geographic coordinates of location. */
    coordinates?: GeoCoordinates;
}
/**
 * Extra information on displayed addresses.
 * This is used for display purposes. Used to add additional data about the address, such as "In the passage".
 * Free text. In addition, the user can state where to display the additional description - before, after, or instead of the address string.
 */
interface AddressHint {
    /** Extra text displayed next to, or instead of, the actual address. */
    text?: string;
    /** Where the extra text should be displayed. */
    placement?: PlacementTypeWithLiterals;
}
/** Where the extra text should be displayed: before, after or instead of the actual address. */
declare enum PlacementType {
    BEFORE = "BEFORE",
    AFTER = "AFTER",
    REPLACE = "REPLACE"
}
/** @enumType */
type PlacementTypeWithLiterals = PlacementType | 'BEFORE' | 'AFTER' | 'REPLACE';
/** Geocoordinates for a particular address. */
interface GeoCoordinates {
    /** Latitude of the location. Must be between -90 and 90. */
    latitude?: number;
    /** Longitude of the location. Must be between -180 and 180. */
    longitude?: number;
}
/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */
interface BusinessSchedule {
    /**
     * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.
     * @maxSize 100
     */
    periods?: TimePeriod[];
    /**
     * Exceptions to the business's regular hours. The business can be open or closed during the exception.
     * @maxSize 100
     */
    specialHourPeriod?: SpecialHourPeriod[];
}
/** Weekly recurring time periods when the business is regularly open or the service is available. */
interface TimePeriod {
    /** Day of the week the period starts on. */
    openDay?: DayOfWeekWithLiterals;
    /**
     * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     */
    openTime?: string;
    /** Day of the week the period ends on. */
    closeDay?: DayOfWeekWithLiterals;
    /**
     * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     *
     * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.
     */
    closeTime?: string;
}
/** Enumerates the days of the week. */
declare enum DayOfWeek {
    MONDAY = "MONDAY",
    TUESDAY = "TUESDAY",
    WEDNESDAY = "WEDNESDAY",
    THURSDAY = "THURSDAY",
    FRIDAY = "FRIDAY",
    SATURDAY = "SATURDAY",
    SUNDAY = "SUNDAY"
}
/** @enumType */
type DayOfWeekWithLiterals = DayOfWeek | 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY';
/** Exception to the business's regular hours. The business can be open or closed during the exception. */
interface SpecialHourPeriod {
    /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    startDate?: string;
    /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    endDate?: string;
    /**
     * Whether the business is closed (or the service is not available) during the exception.
     *
     * Default: `true`.
     */
    isClosed?: boolean;
    /** Additional info about the exception. For example, "We close earlier on New Year's Eve." */
    comment?: string;
}
interface Multilingual {
    /**
     * Supported languages list.
     * @maxSize 200
     */
    supportedLanguages?: SupportedLanguage[];
    /** Whether to redirect to user language. */
    autoRedirect?: boolean;
}
interface SupportedLanguage {
    /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */
    languageCode?: string;
    /** Locale. */
    locale?: Locale;
    /** Whether the supported language is the primary language for the site. */
    isPrimary?: boolean;
    /** Language icon. */
    countryCode?: string;
    /** How the language will be resolved. For internal use. */
    resolutionMethod?: ResolutionMethodWithLiterals;
    /** Whether the supported language is the primary language for site visitors. */
    isVisitorPrimary?: boolean | null;
}
declare enum ResolutionMethod {
    QUERY_PARAM = "QUERY_PARAM",
    SUBDOMAIN = "SUBDOMAIN",
    SUBDIRECTORY = "SUBDIRECTORY"
}
/** @enumType */
type ResolutionMethodWithLiterals = ResolutionMethod | 'QUERY_PARAM' | 'SUBDOMAIN' | 'SUBDIRECTORY';
interface ConsentPolicy {
    /** Whether the site uses cookies that are essential to site operation. Always `true`. */
    essential?: boolean | null;
    /** Whether the site uses cookies that affect site performance and other functional measurements. */
    functional?: boolean | null;
    /** Whether the site uses cookies that collect analytics about how the site is used (in order to improve it). */
    analytics?: boolean | null;
    /** Whether the site uses cookies that collect information allowing better customization of the experience for a current visitor. */
    advertising?: boolean | null;
    /** CCPA compliance flag. */
    dataToThirdParty?: boolean | null;
}
/** A single mapping from the MetaSite ID to a particular service. */
interface Translation {
    /** The service type. */
    serviceType?: string;
    /** The application definition ID; this only applies to services of type ThirdPartyApps. */
    appDefId?: string;
    /** The instance ID of the service. */
    instanceId?: string;
}
interface ChangeContext extends ChangeContextPayloadOneOf {
    /** Properties were updated. */
    propertiesChange?: PropertiesChange;
    /** Default properties were created on site creation. */
    siteCreated?: V4SiteCreated;
    /** Properties were cloned on site cloning. */
    siteCloned?: SiteCloned;
}
/** @oneof */
interface ChangeContextPayloadOneOf {
    /** Properties were updated. */
    propertiesChange?: PropertiesChange;
    /** Default properties were created on site creation. */
    siteCreated?: V4SiteCreated;
    /** Properties were cloned on site cloning. */
    siteCloned?: SiteCloned;
}
interface PropertiesChange {
}
interface V4SiteCreated {
    /** Origin template site id. */
    originTemplateId?: string | null;
}
interface SiteCloned {
    /** Origin site id. */
    originMetaSiteId?: string;
}
interface MetaSiteSpecialEvent extends MetaSiteSpecialEventPayloadOneOf {
    /** Emitted on a meta site creation. */
    siteCreated?: SiteCreated;
    /** Emitted on a meta site transfer completion. */
    siteTransferred?: SiteTransferred;
    /** Emitted on a meta site deletion. */
    siteDeleted?: SiteDeleted;
    /** Emitted on a meta site restoration. */
    siteUndeleted?: SiteUndeleted;
    /** Emitted on the first* publish of the meta site (* switching from unpublished to published state). */
    sitePublished?: SitePublished;
    /** Emitted on a meta site unpublish. */
    siteUnpublished?: SiteUnpublished;
    /** Emitted when meta site is marked as template. */
    siteMarkedAsTemplate?: SiteMarkedAsTemplate;
    /** Emitted when meta site is marked as a WixSite. */
    siteMarkedAsWixSite?: SiteMarkedAsWixSite;
    /** Emitted when an application is provisioned (installed). */
    serviceProvisioned?: ServiceProvisioned;
    /** Emitted when an application is removed (uninstalled). */
    serviceRemoved?: ServiceRemoved;
    /** Emitted when meta site name (URL slug) is changed. */
    siteRenamedPayload?: SiteRenamed;
    /** Emitted when meta site was permanently deleted. */
    hardDeleted?: SiteHardDeleted;
    /** Emitted on a namespace change. */
    namespaceChanged?: NamespaceChanged;
    /** Emitted when Studio is attached. */
    studioAssigned?: StudioAssigned;
    /** Emitted when Studio is detached. */
    studioUnassigned?: StudioUnassigned;
    /**
     * Emitted when one of the URLs is changed. After this event you may call `urls-server` to fetch
     * the actual URL.
     *
     * See: https://wix.slack.com/archives/C0UHEBPFT/p1732520791210559?thread_ts=1732027914.294059&cid=C0UHEBPFT
     * See: https://wix.slack.com/archives/C0UHEBPFT/p1744115197619459
     */
    urlChanged?: SiteUrlChanged;
    /** Site is marked as PurgedExternally */
    sitePurgedExternally?: SitePurgedExternally;
    /** Emitted when Odeditor is attached. */
    odeditorAssigned?: OdeditorAssigned;
    /** Emitted when Odeditor is detached. */
    odeditorUnassigned?: OdeditorUnassigned;
    /** Emitted when Picasso is attached. */
    picassoAssigned?: PicassoAssigned;
    /** Emitted when Picasso is detached. */
    picassoUnassigned?: PicassoUnassigned;
    /**
     * A meta site id.
     * @format GUID
     */
    metaSiteId?: string;
    /** A meta site version. Monotonically increasing. */
    version?: string;
    /** A timestamp of the event. */
    timestamp?: string;
    /**
     * TODO(meta-site): Change validation once validations are disabled for consumers
     * More context: https://wix.slack.com/archives/C0UHEBPFT/p1720957844413149 and https://wix.slack.com/archives/CFWKX325T/p1728892152855659
     * @maxSize 4000
     */
    assets?: Asset[];
}
/** @oneof */
interface MetaSiteSpecialEventPayloadOneOf {
    /** Emitted on a meta site creation. */
    siteCreated?: SiteCreated;
    /** Emitted on a meta site transfer completion. */
    siteTransferred?: SiteTransferred;
    /** Emitted on a meta site deletion. */
    siteDeleted?: SiteDeleted;
    /** Emitted on a meta site restoration. */
    siteUndeleted?: SiteUndeleted;
    /** Emitted on the first* publish of the meta site (* switching from unpublished to published state). */
    sitePublished?: SitePublished;
    /** Emitted on a meta site unpublish. */
    siteUnpublished?: SiteUnpublished;
    /** Emitted when meta site is marked as template. */
    siteMarkedAsTemplate?: SiteMarkedAsTemplate;
    /** Emitted when meta site is marked as a WixSite. */
    siteMarkedAsWixSite?: SiteMarkedAsWixSite;
    /** Emitted when an application is provisioned (installed). */
    serviceProvisioned?: ServiceProvisioned;
    /** Emitted when an application is removed (uninstalled). */
    serviceRemoved?: ServiceRemoved;
    /** Emitted when meta site name (URL slug) is changed. */
    siteRenamedPayload?: SiteRenamed;
    /** Emitted when meta site was permanently deleted. */
    hardDeleted?: SiteHardDeleted;
    /** Emitted on a namespace change. */
    namespaceChanged?: NamespaceChanged;
    /** Emitted when Studio is attached. */
    studioAssigned?: StudioAssigned;
    /** Emitted when Studio is detached. */
    studioUnassigned?: StudioUnassigned;
    /**
     * Emitted when one of the URLs is changed. After this event you may call `urls-server` to fetch
     * the actual URL.
     *
     * See: https://wix.slack.com/archives/C0UHEBPFT/p1732520791210559?thread_ts=1732027914.294059&cid=C0UHEBPFT
     * See: https://wix.slack.com/archives/C0UHEBPFT/p1744115197619459
     */
    urlChanged?: SiteUrlChanged;
    /** Site is marked as PurgedExternally */
    sitePurgedExternally?: SitePurgedExternally;
    /** Emitted when Odeditor is attached. */
    odeditorAssigned?: OdeditorAssigned;
    /** Emitted when Odeditor is detached. */
    odeditorUnassigned?: OdeditorUnassigned;
    /** Emitted when Picasso is attached. */
    picassoAssigned?: PicassoAssigned;
    /** Emitted when Picasso is detached. */
    picassoUnassigned?: PicassoUnassigned;
}
interface Asset {
    /**
     * An application definition id (app_id in dev-center). For legacy reasons may be UUID or a string (from Java Enum).
     * @maxLength 36
     */
    appDefId?: string;
    /**
     * An instance id. For legacy reasons may be UUID or a string.
     * @maxLength 200
     */
    instanceId?: string;
    /** An application state. */
    state?: StateWithLiterals;
}
declare enum State {
    UNKNOWN = "UNKNOWN",
    ENABLED = "ENABLED",
    DISABLED = "DISABLED",
    PENDING = "PENDING",
    DEMO = "DEMO"
}
/** @enumType */
type StateWithLiterals = State | 'UNKNOWN' | 'ENABLED' | 'DISABLED' | 'PENDING' | 'DEMO';
interface SiteCreated {
    /**
     * A template identifier (empty if not created from a template).
     * @maxLength 36
     */
    originTemplateId?: string;
    /**
     * An account id of the owner.
     * @format GUID
     */
    ownerId?: string;
    /** A context in which meta site was created. */
    context?: SiteCreatedContextWithLiterals;
    /**
     * A meta site id from which this site was created.
     *
     * In case of a creation from a template it's a template id.
     * In case of a site duplication ("Save As" in dashboard or duplicate in UM) it's an id of a source site.
     * @format GUID
     */
    originMetaSiteId?: string | null;
    /**
     * A meta site name (URL slug).
     * @maxLength 20
     */
    siteName?: string;
    /** A namespace. */
    namespace?: NamespaceWithLiterals;
}
declare enum SiteCreatedContext {
    /** A valid option, we don't expose all reasons why site might be created. */
    OTHER = "OTHER",
    /** A meta site was created from template. */
    FROM_TEMPLATE = "FROM_TEMPLATE",
    /** A meta site was created by copying of the transfferred meta site. */
    DUPLICATE_BY_SITE_TRANSFER = "DUPLICATE_BY_SITE_TRANSFER",
    /** A copy of existing meta site. */
    DUPLICATE = "DUPLICATE",
    /** A meta site was created as a transfferred site (copy of the original), old flow, should die soon. */
    OLD_SITE_TRANSFER = "OLD_SITE_TRANSFER",
    /** deprecated A meta site was created for Flash editor. */
    FLASH = "FLASH"
}
/** @enumType */
type SiteCreatedContextWithLiterals = SiteCreatedContext | 'OTHER' | 'FROM_TEMPLATE' | 'DUPLICATE_BY_SITE_TRANSFER' | 'DUPLICATE' | 'OLD_SITE_TRANSFER' | 'FLASH';
declare enum Namespace {
    UNKNOWN_NAMESPACE = "UNKNOWN_NAMESPACE",
    /** Default namespace for UGC sites. MetaSites with this namespace will be shown in a user's site list by default. */
    WIX = "WIX",
    /** ShoutOut stand alone product. These are siteless (no actual Wix site, no HtmlWeb). MetaSites with this namespace will *not* be shown in a user's site list by default. */
    SHOUT_OUT = "SHOUT_OUT",
    /** MetaSites created by the Albums product, they appear as part of the Albums app. MetaSites with this namespace will *not* be shown in a user's site list by default. */
    ALBUMS = "ALBUMS",
    /** Part of the WixStores migration flow, a user tries to migrate and gets this site to view and if the user likes it then stores removes this namespace and deletes the old site with the old stores. MetaSites with this namespace will *not* be shown in a user's site list by default. */
    WIX_STORES_TEST_DRIVE = "WIX_STORES_TEST_DRIVE",
    /** Hotels standalone (siteless). MetaSites with this namespace will *not* be shown in a user's site list by default. */
    HOTELS = "HOTELS",
    /** Clubs siteless MetaSites, a club without a wix website. MetaSites with this namespace will *not* be shown in a user's site list by default. */
    CLUBS = "CLUBS",
    /** A partially created ADI website. MetaSites with this namespace will *not* be shown in a user's site list by default. */
    ONBOARDING_DRAFT = "ONBOARDING_DRAFT",
    /** AppBuilder for AppStudio / shmite (c). MetaSites with this namespace will *not* be shown in a user's site list by default. */
    DEV_SITE = "DEV_SITE",
    /** LogoMaker websites offered to the user after logo purchase. MetaSites with this namespace will *not* be shown in a user's site list by default. */
    LOGOS = "LOGOS",
    /** VideoMaker websites offered to the user after video purchase. MetaSites with this namespace will *not* be shown in a user's site list by default. */
    VIDEO_MAKER = "VIDEO_MAKER",
    /** MetaSites with this namespace will *not* be shown in a user's site list by default. */
    PARTNER_DASHBOARD = "PARTNER_DASHBOARD",
    /** MetaSites with this namespace will *not* be shown in a user's site list by default. */
    DEV_CENTER_COMPANY = "DEV_CENTER_COMPANY",
    /**
     * A draft created by HTML editor on open. Upon "first save" it will be moved to be of WIX domain.
     *
     * Meta site with this namespace will *not* be shown in a user's site list by default.
     */
    HTML_DRAFT = "HTML_DRAFT",
    /**
     * the user-journey for Fitness users who want to start from managing their business instead of designing their website.
     * Will be accessible from Site List and will not have a website app.
     * Once the user attaches a site, the site will become a regular wixsite.
     */
    SITELESS_BUSINESS = "SITELESS_BUSINESS",
    /** Belongs to "strategic products" company. Supports new product in the creator's economy space. */
    CREATOR_ECONOMY = "CREATOR_ECONOMY",
    /** It is to be used in the Business First efforts. */
    DASHBOARD_FIRST = "DASHBOARD_FIRST",
    /** Bookings business flow with no site. */
    ANYWHERE = "ANYWHERE",
    /** Namespace for Headless Backoffice with no editor */
    HEADLESS = "HEADLESS",
    /**
     * Namespace for master site that will exist in parent account that will be referenced by subaccounts
     * The site will be used for account level CSM feature for enterprise
     */
    ACCOUNT_MASTER_CMS = "ACCOUNT_MASTER_CMS",
    /** Rise.ai Siteless account management for Gift Cards and Store Credit. */
    RISE = "RISE",
    /**
     * As part of the branded app new funnel, users now can create a meta site that will be branded app first.
     * There's a blank site behind the scene but it's blank).
     * The Mobile company will be the owner of this namespace.
     */
    BRANDED_FIRST = "BRANDED_FIRST",
    /** Nownia.com Siteless account management for Ai Scheduling Assistant. */
    NOWNIA = "NOWNIA",
    /**
     * UGC Templates are templates that are created by users for personal use and to sale to other users.
     * The Partners company owns this namespace.
     */
    UGC_TEMPLATE = "UGC_TEMPLATE",
    /** Codux Headless Sites */
    CODUX = "CODUX",
    /** Bobb - AI Design Creator. */
    MEDIA_DESIGN_CREATOR = "MEDIA_DESIGN_CREATOR",
    /**
     * Shared Blog Site is a unique single site across Enterprise account,
     * This site will hold all Blog posts related to the Marketing product.
     */
    SHARED_BLOG_ENTERPRISE = "SHARED_BLOG_ENTERPRISE",
    /** Standalone forms (siteless). MetaSites with this namespace will *not* be shown in a user's site list by default. */
    STANDALONE_FORMS = "STANDALONE_FORMS",
    /** Standalone events (siteless). MetaSites with this namespace will *not* be shown in a user's site list by default. */
    STANDALONE_EVENTS = "STANDALONE_EVENTS",
    /** MIMIR - Siteless account for MIMIR Ai Job runner. */
    MIMIR = "MIMIR"
}
/** @enumType */
type NamespaceWithLiterals = Namespace | 'UNKNOWN_NAMESPACE' | 'WIX' | 'SHOUT_OUT' | 'ALBUMS' | 'WIX_STORES_TEST_DRIVE' | 'HOTELS' | 'CLUBS' | 'ONBOARDING_DRAFT' | 'DEV_SITE' | 'LOGOS' | 'VIDEO_MAKER' | 'PARTNER_DASHBOARD' | 'DEV_CENTER_COMPANY' | 'HTML_DRAFT' | 'SITELESS_BUSINESS' | 'CREATOR_ECONOMY' | 'DASHBOARD_FIRST' | 'ANYWHERE' | 'HEADLESS' | 'ACCOUNT_MASTER_CMS' | 'RISE' | 'BRANDED_FIRST' | 'NOWNIA' | 'UGC_TEMPLATE' | 'CODUX' | 'MEDIA_DESIGN_CREATOR' | 'SHARED_BLOG_ENTERPRISE' | 'STANDALONE_FORMS' | 'STANDALONE_EVENTS' | 'MIMIR';
/** Site transferred to another user. */
interface SiteTransferred {
    /**
     * A previous owner id (user that transfers meta site).
     * @format GUID
     */
    oldOwnerId?: string;
    /**
     * A new owner id (user that accepts meta site).
     * @format GUID
     */
    newOwnerId?: string;
}
/** Soft deletion of the meta site. Could be restored. */
interface SiteDeleted {
    /** A deletion context. */
    deleteContext?: DeleteContext;
}
interface DeleteContext {
    /** When the meta site was deleted. */
    dateDeleted?: Date | null;
    /** A status. */
    deleteStatus?: DeleteStatusWithLiterals;
    /**
     * A reason (flow).
     * @maxLength 255
     */
    deleteOrigin?: string;
    /**
     * A service that deleted it.
     * @maxLength 255
     */
    initiatorId?: string | null;
}
declare enum DeleteStatus {
    UNKNOWN = "UNKNOWN",
    TRASH = "TRASH",
    DELETED = "DELETED",
    PENDING_PURGE = "PENDING_PURGE",
    PURGED_EXTERNALLY = "PURGED_EXTERNALLY"
}
/** @enumType */
type DeleteStatusWithLiterals = DeleteStatus | 'UNKNOWN' | 'TRASH' | 'DELETED' | 'PENDING_PURGE' | 'PURGED_EXTERNALLY';
/** Restoration of the meta site. */
interface SiteUndeleted {
}
/** First publish of a meta site. Or subsequent publish after unpublish. */
interface SitePublished {
}
interface SiteUnpublished {
    /**
     * A list of URLs previously associated with the meta site.
     * @maxLength 4000
     * @maxSize 10000
     */
    urls?: string[];
}
interface SiteMarkedAsTemplate {
}
interface SiteMarkedAsWixSite {
}
/**
 * Represents a service provisioned a site.
 *
 * Note on `origin_instance_id`:
 * There is no guarantee that you will be able to find a meta site using `origin_instance_id`.
 * This is because of the following scenario:
 *
 * Imagine you have a template where a third-party application (TPA) includes some stub data,
 * such as a product catalog. When you create a site from this template, you inherit this
 * default product catalog. However, if the template's product catalog is modified,
 * your site will retain the catalog as it was at the time of site creation. This ensures that
 * your site remains consistent with what you initially received and does not include any
 * changes made to the original template afterward.
 * To ensure this, the TPA on the template gets a new instance_id.
 */
interface ServiceProvisioned {
    /**
     * Either UUID or EmbeddedServiceType.
     * @maxLength 36
     */
    appDefId?: string;
    /**
     * Not only UUID. Something here could be something weird.
     * @maxLength 36
     */
    instanceId?: string;
    /**
     * An instance id from which this instance is originated.
     * @maxLength 36
     */
    originInstanceId?: string;
    /**
     * A version.
     * @maxLength 500
     */
    version?: string | null;
    /**
     * The origin meta site id
     * @format GUID
     */
    originMetaSiteId?: string | null;
}
interface ServiceRemoved {
    /**
     * Either UUID or EmbeddedServiceType.
     * @maxLength 36
     */
    appDefId?: string;
    /**
     * Not only UUID. Something here could be something weird.
     * @maxLength 36
     */
    instanceId?: string;
    /**
     * A version.
     * @maxLength 500
     */
    version?: string | null;
}
/** Rename of the site. Meaning, free public url has been changed as well. */
interface SiteRenamed {
    /**
     * A new meta site name (URL slug).
     * @maxLength 20
     */
    newSiteName?: string;
    /**
     * A previous meta site name (URL slug).
     * @maxLength 255
     */
    oldSiteName?: string;
}
/**
 * Hard deletion of the meta site.
 *
 * Could not be restored. Therefore it's desirable to cleanup data.
 */
interface SiteHardDeleted {
    /** A deletion context. */
    deleteContext?: DeleteContext;
}
interface NamespaceChanged {
    /** A previous namespace. */
    oldNamespace?: NamespaceWithLiterals;
    /** A new namespace. */
    newNamespace?: NamespaceWithLiterals;
}
/** Assigned Studio editor */
interface StudioAssigned {
}
/** Unassigned Studio editor */
interface StudioUnassigned {
}
/**
 * Fired in case site URLs were changed in any way: new secondary domain, published, account slug rename, site rename etc.
 *
 * This is an internal event, it's not propagated in special events, because it's non-actionable. If you need to keep up
 * with sites and its urls, you need to listen to another topic/event. Read about it:
 *
 * https://bo.wix.com/wix-docs/rest/meta-site/meta-site---urls-service
 */
interface SiteUrlChanged {
}
/**
 * Used at the end of the deletion flow for both draft sites and when a user deletes a site.
 * Consumed by other teams to remove relevant data.
 */
interface SitePurgedExternally {
    /**
     * @maxLength 2048
     * @maxSize 100
     * @deprecated
     * @targetRemovalDate 2025-04-15
     */
    appDefId?: string[];
}
/** Assigned Odeditor */
interface OdeditorAssigned {
}
/** Unassigned Odeditor */
interface OdeditorUnassigned {
}
/** Assigned Picasso editor */
interface PicassoAssigned {
}
/** Unassigned Picasso */
interface PicassoUnassigned {
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type CreateBookingPolicyValidationErrors = {
    ruleName?: 'BOTH_LATE_AND_AFTER_START_POLICIES_NOT_ALLOWED';
} | {
    ruleName?: 'INVALID_BOOKING_WINDOW';
} | {
    ruleName?: 'CANCELLATION_FEE_WITHOUT_CREDIT_CARD';
} | {
    ruleName?: 'CANCELLATION_FEE_WITHOUT_DESCRIPTION';
} | {
    ruleName?: 'INVALID_CANCELLATION_WINDOWS_ORDER';
} | {
    ruleName?: 'EMPTY_CANCELLATION_WINDOWS_LIST';
};
/** @docsIgnore */
type UpdateBookingPolicyValidationErrors = {
    ruleName?: 'BOTH_LATE_AND_AFTER_START_POLICIES_NOT_ALLOWED';
} | {
    ruleName?: 'INVALID_BOOKING_WINDOW';
} | {
    ruleName?: 'CANCELLATION_FEE_WITHOUT_CREDIT_CARD';
} | {
    ruleName?: 'CANCELLATION_FEE_WITHOUT_DESCRIPTION';
} | {
    ruleName?: 'INVALID_CANCELLATION_WINDOWS_ORDER';
} | {
    ruleName?: 'EMPTY_CANCELLATION_WINDOWS_LIST';
};
/** @docsIgnore */
type DeleteBookingPolicyApplicationErrors = {
    code?: 'DEFAULT_POLICY_CANNOT_BE_DELETED';
    description?: string;
    data?: Record<string, any>;
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface BookingPolicyCreatedEnvelope {
    entity: BookingPolicy;
    metadata: EventMetadata;
}
/**
 * Triggered when a booking policy is created.
 * @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @webhook
 * @eventType wix.bookings.v1.booking_policy_created
 * @slug created
 */
declare function onBookingPolicyCreated(handler: (event: BookingPolicyCreatedEnvelope) => void | Promise<void>): void;
interface BookingPolicyDefaultBookingPolicySetEnvelope {
    data: DefaultBookingPolicySet;
    metadata: EventMetadata;
}
/**
 * Triggered when the site's default policy changes. Then,
 * _Booking Policy Updated_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/on-booking-policy-updated) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/booking-policy-updated))
 * is also triggered both for the new and the previous default policy.
 * @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @webhook
 * @eventType wix.bookings.v1.booking_policy_default_booking_policy_set
 * @slug default_booking_policy_set
 */
declare function onBookingPolicyDefaultBookingPolicySet(handler: (event: BookingPolicyDefaultBookingPolicySetEnvelope) => void | Promise<void>): void;
interface BookingPolicyDeletedEnvelope {
    metadata: EventMetadata;
}
/**
 * Triggered when a booking policy is deleted.
 * @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @webhook
 * @eventType wix.bookings.v1.booking_policy_deleted
 * @slug deleted
 */
declare function onBookingPolicyDeleted(handler: (event: BookingPolicyDeletedEnvelope) => void | Promise<void>): void;
interface BookingPolicyUpdatedEnvelope {
    entity: BookingPolicy;
    metadata: EventMetadata;
}
/**
 * Triggered when a booking policy is updated, including when a policy's
 * `default` attribute changes.
 * @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @webhook
 * @eventType wix.bookings.v1.booking_policy_updated
 * @slug updated
 */
declare function onBookingPolicyUpdated(handler: (event: BookingPolicyUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Creates a booking policy.
 * @param bookingPolicy - Booking policy to create.
 * @public
 * @requiredField bookingPolicy
 * @permissionId BOOKINGS.BOOKING_POLICY_CREATE
 * @applicableIdentity APP
 * @returns Created booking policy.
 * @fqn wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy
 */
declare function createBookingPolicy(bookingPolicy: BookingPolicy): Promise<NonNullablePaths<BookingPolicy, `customPolicyDescription.enabled` | `customPolicyDescription.description` | `limitEarlyBookingPolicy.enabled` | `limitEarlyBookingPolicy.earliestBookingInMinutes` | `limitLateBookingPolicy.enabled` | `limitLateBookingPolicy.latestBookingInMinutes` | `bookAfterStartPolicy.enabled` | `cancellationPolicy.enabled` | `cancellationPolicy.limitLatestCancellation` | `cancellationPolicy.latestCancellationInMinutes` | `reschedulePolicy.enabled` | `reschedulePolicy.limitLatestReschedule` | `reschedulePolicy.latestRescheduleInMinutes` | `waitlistPolicy.enabled` | `waitlistPolicy.capacity` | `waitlistPolicy.reservationTimeInMinutes` | `participantsPolicy.maxParticipantsPerBooking` | `cancellationFeePolicy.enabled` | `cancellationFeePolicy.cancellationWindows` | `cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `cancellationFeePolicy.cancellationWindows.${number}.percentage` | `saveCreditCardPolicy.enabled`, 6> & {
    __validationErrorsType?: CreateBookingPolicyValidationErrors;
}>;
/**
 * Retrieves a booking policy.
 * @param bookingPolicyId - ID of the booking policy to retrieve.
 * @public
 * @requiredField bookingPolicyId
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @applicableIdentity APP
 * @returns Retrieved booking policy.
 * @fqn wix.bookings.v1.BookingPoliciesService.GetBookingPolicy
 */
declare function getBookingPolicy(bookingPolicyId: string): Promise<NonNullablePaths<BookingPolicy, `customPolicyDescription.enabled` | `customPolicyDescription.description` | `limitEarlyBookingPolicy.enabled` | `limitEarlyBookingPolicy.earliestBookingInMinutes` | `limitLateBookingPolicy.enabled` | `limitLateBookingPolicy.latestBookingInMinutes` | `bookAfterStartPolicy.enabled` | `cancellationPolicy.enabled` | `cancellationPolicy.limitLatestCancellation` | `cancellationPolicy.latestCancellationInMinutes` | `reschedulePolicy.enabled` | `reschedulePolicy.limitLatestReschedule` | `reschedulePolicy.latestRescheduleInMinutes` | `waitlistPolicy.enabled` | `waitlistPolicy.capacity` | `waitlistPolicy.reservationTimeInMinutes` | `participantsPolicy.maxParticipantsPerBooking` | `cancellationFeePolicy.enabled` | `cancellationFeePolicy.cancellationWindows` | `cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `cancellationFeePolicy.cancellationWindows.${number}.percentage` | `saveCreditCardPolicy.enabled`, 6>>;
/**
 * Retrieves the strictest version of each policy rule from a list of booking
 * policies.
 *
 *
 * Returns a hypothetical `bookingPolicy` object that combines the strictest
 * version of each rule. The `id` of the returned policy is `null` and no
 * corresponding `bookingPolicy` object is created. To create a new policy, you
 * can call *Create Booking Policy*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).
 * @param bookingPolicyIds - IDs of the booking policies for which to retrieve the strictest rules for.
 * @public
 * @requiredField bookingPolicyIds
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy
 */
declare function getStrictestBookingPolicy(bookingPolicyIds: string[]): Promise<NonNullablePaths<GetStrictestBookingPolicyResponse, `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled`, 7>>;
/**
 * Updates a booking policy.
 *
 *
 * Each time the booking policy is updated, `revision` increments by 1.
 * The current `revision` must be specified when updating the booking policy.
 * This ensures you're working with the latest booking policy and prevents unintended overwrites.
 * @param _id - ID of the booking policy.
 * @public
 * @requiredField _id
 * @requiredField bookingPolicy
 * @requiredField bookingPolicy.revision
 * @permissionId BOOKINGS.BOOKING_POLICY_UPDATE
 * @applicableIdentity APP
 * @returns Updated booking policy.
 * @fqn wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy
 */
declare function updateBookingPolicy(_id: string, bookingPolicy: NonNullablePaths<UpdateBookingPolicy, `revision`, 2>): Promise<NonNullablePaths<BookingPolicy, `customPolicyDescription.enabled` | `customPolicyDescription.description` | `limitEarlyBookingPolicy.enabled` | `limitEarlyBookingPolicy.earliestBookingInMinutes` | `limitLateBookingPolicy.enabled` | `limitLateBookingPolicy.latestBookingInMinutes` | `bookAfterStartPolicy.enabled` | `cancellationPolicy.enabled` | `cancellationPolicy.limitLatestCancellation` | `cancellationPolicy.latestCancellationInMinutes` | `reschedulePolicy.enabled` | `reschedulePolicy.limitLatestReschedule` | `reschedulePolicy.latestRescheduleInMinutes` | `waitlistPolicy.enabled` | `waitlistPolicy.capacity` | `waitlistPolicy.reservationTimeInMinutes` | `participantsPolicy.maxParticipantsPerBooking` | `cancellationFeePolicy.enabled` | `cancellationFeePolicy.cancellationWindows` | `cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `cancellationFeePolicy.cancellationWindows.${number}.percentage` | `saveCreditCardPolicy.enabled`, 6> & {
    __validationErrorsType?: UpdateBookingPolicyValidationErrors;
}>;
interface UpdateBookingPolicy {
    /**
     * ID of the booking policy.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking policy is updated.
     * To prevent conflicting changes, the current `revision` must be passed when
     * updating the booking policy.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the booking policy was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the booking policy was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the booking policy.
     * @maxLength 400
     */
    name?: string | null;
    /**
     * Custom description for the booking policy and whether it's displayed to the
     * participant.
     */
    customPolicyDescription?: PolicyDescription;
    /**
     * Whether the booking policy is the default.
     * @readonly
     */
    default?: boolean | null;
    /** Rule for limiting early bookings. */
    limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;
    /**
     * Rule for limiting late bookings. This rule and `bookAfterStartPolicy` can't
     * be both enabled at the same time.
     */
    limitLateBookingPolicy?: LimitLateBookingPolicy;
    /**
     * Rule for booking after the start of a session or course. This rule and
     * `limitLateBookingPolicy` can't be both enabled at the same time.
     */
    bookAfterStartPolicy?: BookAfterStartPolicy;
    /** Rule for canceling a booking. */
    cancellationPolicy?: CancellationPolicy;
    /** Rule for rescheduling a booking. */
    reschedulePolicy?: ReschedulePolicy;
    /** Waitlist rule for the service. */
    waitlistPolicy?: WaitlistPolicy;
    /** Rule for participants per booking. */
    participantsPolicy?: ParticipantsPolicy;
    /** Rules for cancellation fees. */
    cancellationFeePolicy?: CancellationFeePolicy;
    /** Rule for saving credit card details. */
    saveCreditCardPolicy?: SaveCreditCardPolicy;
    /** Extensions enabling users to save custom data related to the booking policies. */
    extendedFields?: ExtendedFields;
}
/**
 * Sets a booking policy as the default.
 *
 *
 * Also updates the site's current default policy by setting its `default`
 * attribute to `false`. If the provided policy is already the site's
 * default, the call succeeds without changing any `bookingPolicy` object.
 * @param bookingPolicyId - ID of the booking policy that's set as default.
 * @public
 * @requiredField bookingPolicyId
 * @permissionId BOOKINGS.BOOKING_POLICY_SET_DEFAULT
 * @applicableIdentity APP
 * @fqn wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy
 */
declare function setDefaultBookingPolicy(bookingPolicyId: string): Promise<NonNullablePaths<SetDefaultBookingPolicyResponse, `currentDefaultBookingPolicy.customPolicyDescription.enabled` | `currentDefaultBookingPolicy.customPolicyDescription.description` | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.enabled` | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `currentDefaultBookingPolicy.limitLateBookingPolicy.enabled` | `currentDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `currentDefaultBookingPolicy.bookAfterStartPolicy.enabled` | `currentDefaultBookingPolicy.cancellationPolicy.enabled` | `currentDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation` | `currentDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `currentDefaultBookingPolicy.reschedulePolicy.enabled` | `currentDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule` | `currentDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `currentDefaultBookingPolicy.waitlistPolicy.enabled` | `currentDefaultBookingPolicy.waitlistPolicy.capacity` | `currentDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `currentDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `currentDefaultBookingPolicy.cancellationFeePolicy.enabled` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `currentDefaultBookingPolicy.saveCreditCardPolicy.enabled` | `previousDefaultBookingPolicy.customPolicyDescription.enabled` | `previousDefaultBookingPolicy.customPolicyDescription.description` | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.enabled` | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `previousDefaultBookingPolicy.limitLateBookingPolicy.enabled` | `previousDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `previousDefaultBookingPolicy.bookAfterStartPolicy.enabled` | `previousDefaultBookingPolicy.cancellationPolicy.enabled` | `previousDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation` | `previousDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `previousDefaultBookingPolicy.reschedulePolicy.enabled` | `previousDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule` | `previousDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `previousDefaultBookingPolicy.waitlistPolicy.enabled` | `previousDefaultBookingPolicy.waitlistPolicy.capacity` | `previousDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `previousDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `previousDefaultBookingPolicy.cancellationFeePolicy.enabled` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `previousDefaultBookingPolicy.saveCreditCardPolicy.enabled`, 7>>;
/**
 * Deletes a booking policy.
 *
 *
 * You can't delete the default policy without first *setting a different policy as default*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/set-default-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/set-default-booking-policy)).
 * @param bookingPolicyId - ID of the booking policy to delete.
 * @public
 * @requiredField bookingPolicyId
 * @permissionId BOOKINGS.BOOKING_POLICY_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy
 */
declare function deleteBookingPolicy(bookingPolicyId: string): Promise<void & {
    __applicationErrorsType?: DeleteBookingPolicyApplicationErrors;
}>;
/**
 * Creates a query to retrieve a list of `bookingPolicy` objects.
 *
 * The `queryBookingPolicies()` function builds a query to retrieve a list of `bookingPolicy` objects and returns a `BookingPoliciesQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-builder/find) function.
 *
 * You can refine the query by chaining `BookingPoliciesQueryBuilder` functions onto the query. `BookingPoliciesQueryBuilder` functions enable you to sort, filter, and control the results that `queryBookingPolicies()` returns.
 *
 * `queryBookingPolicies()` runs with the following `BookingPoliciesQueryBuilder` defaults that you can override:
 *
 * + `limit` is `50`.
 * + Sorted by `createdDate` in ascending order.
 *
 * The functions that are chained to `queryBookingPolicies()` are applied in the order they are called. For example, if you apply `ascending("waitlistPolicy.capacity")` and then `ascending("name")`, the results are sorted first by `waitlistPolicy.capacity`, and then, if there are multiple results with the same `waitlistPolicy.capacity`, the items are sorted by `name`.
 *
 * The following `BookingPoliciesQueryBuilder` functions are supported for the `queryBookingPolicies()` function. For a full description of the `bookingPolicy` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-result/items) property in `BookingPoliciesQueryResult`.
 * @public
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies
 */
declare function queryBookingPolicies(): BookingPoliciesQueryBuilder;
interface QueryCursorResult {
    cursors: Cursors;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface BookingPoliciesQueryResult extends QueryCursorResult {
    items: BookingPolicy[];
    query: BookingPoliciesQueryBuilder;
    next: () => Promise<BookingPoliciesQueryResult>;
    prev: () => Promise<BookingPoliciesQueryResult>;
}
interface BookingPoliciesQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    eq: (propertyName: '_id' | 'name' | 'customPolicyDescription.enabled' | 'customPolicyDescription.description' | 'default' | 'limitEarlyBookingPolicy.enabled' | 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.enabled' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'bookAfterStartPolicy.enabled' | 'cancellationPolicy.enabled' | 'cancellationPolicy.limitLatestCancellation' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.enabled' | 'reschedulePolicy.limitLatestReschedule' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.enabled' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.maxParticipantsPerBooking' | 'resourcesPolicy.enabled' | 'resourcesPolicy.autoAssignAllowed', value: any) => BookingPoliciesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ne: (propertyName: '_id' | 'name' | 'customPolicyDescription.enabled' | 'customPolicyDescription.description' | 'default' | 'limitEarlyBookingPolicy.enabled' | 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.enabled' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'bookAfterStartPolicy.enabled' | 'cancellationPolicy.enabled' | 'cancellationPolicy.limitLatestCancellation' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.enabled' | 'reschedulePolicy.limitLatestReschedule' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.enabled' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.maxParticipantsPerBooking' | 'resourcesPolicy.enabled' | 'resourcesPolicy.autoAssignAllowed', value: any) => BookingPoliciesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ge: (propertyName: 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.maxParticipantsPerBooking', value: any) => BookingPoliciesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    gt: (propertyName: 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.maxParticipantsPerBooking', value: any) => BookingPoliciesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    le: (propertyName: 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.maxParticipantsPerBooking', value: any) => BookingPoliciesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    lt: (propertyName: 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.maxParticipantsPerBooking', value: any) => BookingPoliciesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `string`.
     * @param string - String to compare against. Case-insensitive.
     */
    startsWith: (propertyName: 'name' | 'customPolicyDescription.description', value: string) => BookingPoliciesQueryBuilder;
    in: (propertyName: '_id' | 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.maxParticipantsPerBooking', value: any) => BookingPoliciesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    ascending: (...propertyNames: Array<'_id' | 'name' | 'customPolicyDescription.enabled' | 'customPolicyDescription.description' | 'default' | 'limitEarlyBookingPolicy.enabled' | 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.enabled' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'bookAfterStartPolicy.enabled' | 'cancellationPolicy.enabled' | 'cancellationPolicy.limitLatestCancellation' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.enabled' | 'reschedulePolicy.limitLatestReschedule' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.enabled' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.enabled' | 'participantsPolicy.maxParticipantsPerBooking' | 'resourcesPolicy.enabled' | 'resourcesPolicy.autoAssignAllowed'>) => BookingPoliciesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    descending: (...propertyNames: Array<'_id' | 'name' | 'customPolicyDescription.enabled' | 'customPolicyDescription.description' | 'default' | 'limitEarlyBookingPolicy.enabled' | 'limitEarlyBookingPolicy.earliestBookingInMinutes' | 'limitLateBookingPolicy.enabled' | 'limitLateBookingPolicy.latestBookingInMinutes' | 'bookAfterStartPolicy.enabled' | 'cancellationPolicy.enabled' | 'cancellationPolicy.limitLatestCancellation' | 'cancellationPolicy.latestCancellationInMinutes' | 'reschedulePolicy.enabled' | 'reschedulePolicy.limitLatestReschedule' | 'reschedulePolicy.latestRescheduleInMinutes' | 'waitlistPolicy.enabled' | 'waitlistPolicy.capacity' | 'waitlistPolicy.reservationTimeInMinutes' | 'participantsPolicy.enabled' | 'participantsPolicy.maxParticipantsPerBooking' | 'resourcesPolicy.enabled' | 'resourcesPolicy.autoAssignAllowed'>) => BookingPoliciesQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */
    limit: (limit: number) => BookingPoliciesQueryBuilder;
    /** @param cursor - A pointer to specific record */
    skipTo: (cursor: string) => BookingPoliciesQueryBuilder;
    find: () => Promise<BookingPoliciesQueryResult>;
}
/**
 * Counts booking policies, given the provided filtering.
 *
 *
 * See *supported filters*
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
 * for a complete list of supported filters.
 * @public
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.v1.BookingPoliciesService.CountBookingPolicies
 */
declare function countBookingPolicies(options?: CountBookingPoliciesOptions): Promise<NonNullablePaths<CountBookingPoliciesResponse, `count`, 2>>;
interface CountBookingPoliciesOptions {
    /**
     * Filter to base the count on. See
     * supported filters*
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
     * for details.
     */
    filter?: Record<string, any> | null;
}

export { type ActionEvent, type Address, type AddressHint, type Asset, type BaseEventMetadata, type BookAfterStartPolicy, type BookingPoliciesQueryBuilder, type BookingPoliciesQueryResult, type BookingPolicy, type BookingPolicyCreatedEnvelope, type BookingPolicyDefaultBookingPolicySetEnvelope, type BookingPolicyDeletedEnvelope, type BookingPolicyUpdatedEnvelope, type BusinessSchedule, type CancellationFeePolicy, type CancellationPolicy, type CancellationWindow, type CancellationWindowFeeOneOf, type Categories, type ChangeContext, type ChangeContextPayloadOneOf, type ConsentPolicy, type CountBookingPoliciesOptions, type CountBookingPoliciesRequest, type CountBookingPoliciesResponse, type CreateBookingPolicyRequest, type CreateBookingPolicyResponse, type CreateBookingPolicyValidationErrors, type CreateMissingDefaultPolicyRequest, type CreateMissingDefaultPolicyResponse, type CursorPaging, type CursorPagingMetadata, type CursorQuery, type CursorQueryPagingMethodOneOf, type Cursors, DayOfWeek, type DayOfWeekWithLiterals, type DefaultBookingPolicySet, type DeleteBookingPolicyApplicationErrors, type DeleteBookingPolicyRequest, type DeleteBookingPolicyResponse, type DeleteContext, DeleteStatus, type DeleteStatusWithLiterals, type DomainEvent, type DomainEventBodyOneOf, type Empty, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type EventMetadata, type ExtendedFields, type GeoCoordinates, type GetBookingPolicyRequest, type GetBookingPolicyResponse, type GetStrictestBookingPolicyRequest, type GetStrictestBookingPolicyResponse, type IdentificationData, type IdentificationDataIdOneOf, type LimitEarlyBookingPolicy, type LimitLateBookingPolicy, type Locale, type MessageEnvelope, type MetaSiteSpecialEvent, type MetaSiteSpecialEventPayloadOneOf, type Money, type Multilingual, Namespace, type NamespaceChanged, type NamespaceWithLiterals, type OdeditorAssigned, type OdeditorUnassigned, type ParticipantsPolicy, type PicassoAssigned, type PicassoUnassigned, PlacementType, type PlacementTypeWithLiterals, type PolicyDescription, type Properties, type PropertiesChange, type QueryBookingPoliciesRequest, type QueryBookingPoliciesResponse, type ReschedulePolicy, ResolutionMethod, type ResolutionMethodWithLiterals, type ResourcesPolicy, type RestoreInfo, type SaveCreditCardPolicy, type ServiceProvisioned, type ServiceRemoved, type SetDefaultBookingPolicyRequest, type SetDefaultBookingPolicyResponse, type SiteCloned, type SiteCreated, SiteCreatedContext, type SiteCreatedContextWithLiterals, type SiteDeleted, type SiteHardDeleted, type SiteMarkedAsTemplate, type SiteMarkedAsWixSite, type SitePropertiesEvent, type SitePropertiesNotification, type SitePublished, type SitePurgedExternally, type SiteRenamed, type SiteTransferred, type SiteUndeleted, type SiteUnpublished, type SiteUrlChanged, SortOrder, type SortOrderWithLiterals, type Sorting, type SpecialHourPeriod, State, type StateWithLiterals, type StudioAssigned, type StudioUnassigned, type SupportedLanguage, type TimePeriod, type Translation, type UpdateAllPoliciesRequest, type UpdateAllPoliciesResponse, type UpdateBookingPolicy, type UpdateBookingPolicyRequest, type UpdateBookingPolicyResponse, type UpdateBookingPolicyValidationErrors, type V4SiteCreated, type WaitlistPolicy, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, countBookingPolicies, createBookingPolicy, deleteBookingPolicy, getBookingPolicy, getStrictestBookingPolicy, onBookingPolicyCreated, onBookingPolicyDefaultBookingPolicySet, onBookingPolicyDeleted, onBookingPolicyUpdated, queryBookingPolicies, setDefaultBookingPolicy, updateBookingPolicy };
