{"version": 3, "sources": ["../../index.ts", "../../src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.public.ts", "../../src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.ts", "../../src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.http.ts", "../../src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.context.ts"], "sourcesContent": ["export * from './src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.context.js';\n", "import { renameKeysFromRESTResponseToSDKResponse } from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { EventDefinition, HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  CloneServiceOptionsAndVariantsResponse,\n  CreateServiceOptionsAndVariantsApplicationErrors,\n  DeleteServiceOptionsAndVariantsOptions,\n  GetServiceOptionsAndVariantsByServiceIdResponse,\n  ServiceOptionsAndVariants,\n  ServiceOptionsAndVariantsCreatedEnvelope,\n  ServiceOptionsAndVariantsDeletedEnvelope,\n  ServiceOptionsAndVariantsListQueryBuilder,\n  ServiceOptionsAndVariantsUpdatedEnvelope,\n  UpdateServiceOptionsAndVariants,\n  UpdateServiceOptionsAndVariantsApplicationErrors,\n  cloneServiceOptionsAndVariants as universalCloneServiceOptionsAndVariants,\n  createServiceOptionsAndVariants as universalCreateServiceOptionsAndVariants,\n  deleteServiceOptionsAndVariants as universalDeleteServiceOptionsAndVariants,\n  getServiceOptionsAndVariants as universalGetServiceOptionsAndVariants,\n  getServiceOptionsAndVariantsByServiceId as universalGetServiceOptionsAndVariantsByServiceId,\n  queryServiceOptionsAndVariants as universalQueryServiceOptionsAndVariants,\n  updateServiceOptionsAndVariants as universalUpdateServiceOptionsAndVariants,\n} from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function createServiceOptionsAndVariants(\n  httpClient: HttpClient\n): CreateServiceOptionsAndVariantsSignature {\n  return (\n    serviceOptionsAndVariants: NonNullablePaths<\n      ServiceOptionsAndVariants,\n      `options` | `serviceId` | `variants`,\n      2\n    >\n  ) =>\n    universalCreateServiceOptionsAndVariants(\n      serviceOptionsAndVariants,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CreateServiceOptionsAndVariantsSignature {\n  /**\n   * Creates a `serviceOptionsAndVariants` object and for a service.\n   *\n   *\n   * ## Calculate variants\n   *\n   * Before creating a `serviceOptionsAndVariants` object, you need to\n   * anticipate and manually define all its variants, since Wix Bookings doesn't\n   * automatically calculate them. For the actual\n   * Create Service Options And Variants* call, specify both the `options` and\n   * `variants` arrays.\n   *\n   * ## Limitations\n   *\n   * Wix Bookings allows you to connect only a single `serviceOptionsAndVariants`\n   * object to a service. *Create Service Options And Variants* fails, if the\n   * service already has a connected `serviceOptionsAndVariants` object.\n   *\n   * Currently, you can include only a single option per\n   * `serviceOptionsAndVariants` object. Taken together, this means that services\n   * are limited to a single option.\n   *\n   * ## Option ID\n   *\n   * When creating a`serviceOptionsAndVariants` object, you must specify an ID in\n   * [UUID format](https://en.wikipedia.org/wiki/Universally_unique_identifier)\n   * for its only option. You must reference this option ID for each variant as\n   * `variants.values.choices.optionId`.\n   *\n   * ## Staff member option\n   *\n   * To creating an option based on the *staff member*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n   * providing the service, you need to specify `STAFF_MEMBER` as `options.values.type`.\n   * Also, specify all staff member IDs as `variants.values.choices.staffMemberId`.\n   * You could follow this *sample flow*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-staff-member-based-service-variants) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-staff-member-based-service-variants)).\n   *\n   * ## Custom option\n   *\n   * To create an option based on a custom parameter, specify `CUSTOM` as\n   * `options.values.type`. Provide descriptive names for all custom choices as\n   * `variants.values.choices.custom`. These names are displayed to customers\n   * during the book flow. You could follow this *sample flow*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment)).\n   *\n   * ## Duration option\n   *\n   * To create an option based on appointment duration, specify `DURATION` as\n   * `options.values.type` and set a descriptive name in `options.values.durationData.name`.\n   * Also, indicate the appointment length in `minutes` and provide a descriptive\n   * `name` for each duration choice in `variants.values.choices.duration`.\n   * @param - Service options and variants to create.\n   * @returns Information about the created service options and variants.\n   */\n  (\n    serviceOptionsAndVariants: NonNullablePaths<\n      ServiceOptionsAndVariants,\n      `options` | `serviceId` | `variants`,\n      2\n    >\n  ): Promise<\n    NonNullablePaths<\n      ServiceOptionsAndVariants,\n      | `options.values`\n      | `options.values.${number}.customData.name`\n      | `options.values.${number}._id`\n      | `options.values.${number}.type`\n      | `variants.values`\n      | `variants.values.${number}.price.value`\n      | `variants.values.${number}.price.currency`,\n      6\n    > & {\n      __applicationErrorsType?: CreateServiceOptionsAndVariantsApplicationErrors;\n    }\n  >;\n}\n\nexport function cloneServiceOptionsAndVariants(\n  httpClient: HttpClient\n): CloneServiceOptionsAndVariantsSignature {\n  return (cloneFromId: string, targetServiceId: string) =>\n    universalCloneServiceOptionsAndVariants(\n      cloneFromId,\n      targetServiceId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CloneServiceOptionsAndVariantsSignature {\n  /**\n   * Clones a `serviceOptionsAndVariants` object and connects it to a *service*\n   * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n   *\n   *\n   * The call fails if the service already has a connected\n   * `serviceOptionsAndVariants` object.\n   *\n   * The cloned `serviceOptionsAndVariants` object gets a new, unique option ID.\n   * The option ID of the existing `serviceOptionsAndVariants` object isn't reused.\n   *\n   * For example, you may call this method after *cloning a service*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/clone-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/clone-service)).\n   * @param - ID of the `serviceOptionsAndVariants` object to clone.\n   * @param - ID of the service to which the cloned `serviceOptionsAndVariants` are\n   * connected.\n   */\n  (cloneFromId: string, targetServiceId: string): Promise<\n    NonNullablePaths<\n      CloneServiceOptionsAndVariantsResponse,\n      | `serviceOptionsAndVariants.options.values`\n      | `serviceOptionsAndVariants.options.values.${number}.customData.name`\n      | `serviceOptionsAndVariants.options.values.${number}._id`\n      | `serviceOptionsAndVariants.options.values.${number}.type`\n      | `serviceOptionsAndVariants.variants.values`\n      | `serviceOptionsAndVariants.variants.values.${number}.price.value`\n      | `serviceOptionsAndVariants.variants.values.${number}.price.currency`,\n      7\n    >\n  >;\n}\n\nexport function getServiceOptionsAndVariants(\n  httpClient: HttpClient\n): GetServiceOptionsAndVariantsSignature {\n  return (serviceOptionsAndVariantsId: string) =>\n    universalGetServiceOptionsAndVariants(\n      serviceOptionsAndVariantsId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetServiceOptionsAndVariantsSignature {\n  /**\n   * Retrieves a `serviceOptionsAndVariants` object by its ID.\n   * @param - ID of the `serviceOptionsAndVariants` object to retrieve.\n   * @returns Retrieved `serviceOptionsAndVariants` object.\n   */\n  (serviceOptionsAndVariantsId: string): Promise<\n    NonNullablePaths<\n      ServiceOptionsAndVariants,\n      | `options.values`\n      | `options.values.${number}.customData.name`\n      | `options.values.${number}._id`\n      | `options.values.${number}.type`\n      | `variants.values`\n      | `variants.values.${number}.price.value`\n      | `variants.values.${number}.price.currency`,\n      6\n    >\n  >;\n}\n\nexport function getServiceOptionsAndVariantsByServiceId(\n  httpClient: HttpClient\n): GetServiceOptionsAndVariantsByServiceIdSignature {\n  return (serviceId: string) =>\n    universalGetServiceOptionsAndVariantsByServiceId(\n      serviceId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetServiceOptionsAndVariantsByServiceIdSignature {\n  /**\n   * Retrieves a `serviceOptionsAndVariants` object by *service ID*\n   * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n   * @param - ID of the service to retrieve options and variants for.\n   */\n  (serviceId: string): Promise<\n    NonNullablePaths<\n      GetServiceOptionsAndVariantsByServiceIdResponse,\n      | `serviceVariants.options.values`\n      | `serviceVariants.options.values.${number}.customData.name`\n      | `serviceVariants.options.values.${number}._id`\n      | `serviceVariants.options.values.${number}.type`\n      | `serviceVariants.variants.values`\n      | `serviceVariants.variants.values.${number}.price.value`\n      | `serviceVariants.variants.values.${number}.price.currency`,\n      7\n    >\n  >;\n}\n\nexport function updateServiceOptionsAndVariants(\n  httpClient: HttpClient\n): UpdateServiceOptionsAndVariantsSignature {\n  return (\n    _id: string,\n    serviceOptionsAndVariants: NonNullablePaths<\n      UpdateServiceOptionsAndVariants,\n      `revision`,\n      2\n    >\n  ) =>\n    universalUpdateServiceOptionsAndVariants(\n      _id,\n      serviceOptionsAndVariants,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateServiceOptionsAndVariantsSignature {\n  /**\n   * Updates a `serviceOptionsAndVariants` object.\n   *\n   *\n   * Currently, only a single option is supported per `serviceOptionsAndVariants` object.\n   *\n   * If you want to update `variants`, you must pass the entire list of supported\n   * variants, not only newly added variants.\n   *\n   * If you want to update `options`, you must pass the entire list of supported\n   * options, not only newly added options.\n   * @param - ID of the `serviceOptionsAndVariants` object.\n   * @param - Service options and variants to update.\n   * @param - Options for updating the service options and variants.\n   * @returns Updated `serviceOptionsAndVariants` object.\n   */\n  (\n    _id: string,\n    serviceOptionsAndVariants: NonNullablePaths<\n      UpdateServiceOptionsAndVariants,\n      `revision`,\n      2\n    >\n  ): Promise<\n    NonNullablePaths<\n      ServiceOptionsAndVariants,\n      | `options.values`\n      | `options.values.${number}.customData.name`\n      | `options.values.${number}._id`\n      | `options.values.${number}.type`\n      | `variants.values`\n      | `variants.values.${number}.price.value`\n      | `variants.values.${number}.price.currency`,\n      6\n    > & {\n      __applicationErrorsType?: UpdateServiceOptionsAndVariantsApplicationErrors;\n    }\n  >;\n}\n\nexport function deleteServiceOptionsAndVariants(\n  httpClient: HttpClient\n): DeleteServiceOptionsAndVariantsSignature {\n  return (\n    serviceOptionsAndVariantsId: string,\n    options?: DeleteServiceOptionsAndVariantsOptions\n  ) =>\n    universalDeleteServiceOptionsAndVariants(\n      serviceOptionsAndVariantsId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DeleteServiceOptionsAndVariantsSignature {\n  /**\n   * Deletes a `serviceOptionsAndVariants` object.\n   *\n   *\n   * Because each service can be connected to only a single `serviceOptionsAndVariants`\n   * object, the service doesn't support *varied pricing*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments#service-rates))\n   * after deleting a `serviceOptionsAndVariants` object. Instead, Wix Bookings\n   * uses its standard price calculation.\n   * @param - ID of the `serviceOptionsAndVariants` object to delete.\n   * @param - Options for deleting the service options and variants.\n   */\n  (\n    serviceOptionsAndVariantsId: string,\n    options?: DeleteServiceOptionsAndVariantsOptions\n  ): Promise<void>;\n}\n\nexport function queryServiceOptionsAndVariants(\n  httpClient: HttpClient\n): QueryServiceOptionsAndVariantsSignature {\n  return () =>\n    universalQueryServiceOptionsAndVariants(\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface QueryServiceOptionsAndVariantsSignature {\n  /**\n   * Creates a query to retrieve a list of `serviceOptionsAndVariants` objects.\n   *\n   * The `queryServiceOptionsAndVariants()` function builds a query to retrieve a list of `serviceOptionsAndVariants` objects and returns a `ServiceOptionsAndVariantsQueryBuilder` object.\n   *\n   * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-builder/find) function.\n   *\n   * You can refine the query by chaining `ServiceOptionsAndVariantsQueryBuilder` functions onto the query. `ServiceOptionsAndVariantsQueryBuilder` functions enable you to sort, filter, and control the results that `queryServiceOptionsAndVariants()` returns.\n   *\n   * `queryServiceOptionsAndVariants()` runs with the following `ServiceOptionsAndVariantsQueryBuilder` default that you can override:\n   *\n   * + `limit` is `50`.\n   * + Sorted by `id` in ascending order.\n   *\n   * The functions that are chained to `queryServiceOptionsAndVariants()` are applied in the order they are called. For example, if you apply `ascending(\"options.values.type\")` and then `ascending(\"variants.values.price\")`, the results are sorted first by the `\"type\"`, and then, if there are multiple results with the same `\"type\"`, the items are sorted by `\"price\"`.\n   *\n   * The following `ServiceOptionsAndVariantsQueryBuilder` functions are supported for the `queryServiceOptionsAndVariants()` function. For a full description of the `serviceOptionsAndVariants` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-result/items) property in `ServiceOptionsAndVariantsQueryResult`.\n   */\n  (): ServiceOptionsAndVariantsListQueryBuilder;\n}\n\nexport const onServiceOptionsAndVariantsCreated = EventDefinition(\n  'wix.bookings.catalog.v1.service_options_and_variants_created',\n  true,\n  (event: ServiceOptionsAndVariantsCreatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [{ path: 'metadata.eventTime' }],\n        },\n      ])\n    )\n)<ServiceOptionsAndVariantsCreatedEnvelope>();\nexport const onServiceOptionsAndVariantsDeleted = EventDefinition(\n  'wix.bookings.catalog.v1.service_options_and_variants_deleted',\n  true,\n  (event: ServiceOptionsAndVariantsDeletedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [{ path: 'metadata.eventTime' }],\n        },\n      ])\n    )\n)<ServiceOptionsAndVariantsDeletedEnvelope>();\nexport const onServiceOptionsAndVariantsUpdated = EventDefinition(\n  'wix.bookings.catalog.v1.service_options_and_variants_updated',\n  true,\n  (event: ServiceOptionsAndVariantsUpdatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [{ path: 'metadata.eventTime' }],\n        },\n      ])\n    )\n)<ServiceOptionsAndVariantsUpdatedEnvelope>();\n\nexport {\n  ActionEvent,\n  Address,\n  AddressHint,\n  BaseEventMetadata,\n  BusinessSchedule,\n  Categories,\n  ChangeContext,\n  ChangeContextPayloadOneOf,\n  CloneServiceOptionsAndVariantsRequest,\n  CloneServiceOptionsAndVariantsResponse,\n  ConsentPolicy,\n  CreateServiceOptionsAndVariantsRequest,\n  CreateServiceOptionsAndVariantsResponse,\n  CursorPaging,\n  Cursors,\n  CustomServiceOption,\n  DayOfWeek,\n  DeleteServiceOptionsAndVariantsOptions,\n  DeleteServiceOptionsAndVariantsRequest,\n  DeleteServiceOptionsAndVariantsResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  Duration,\n  DurationServiceOption,\n  Empty,\n  EntityCreatedEvent,\n  EntityDeletedEvent,\n  EntityUpdatedEvent,\n  EventMetadata,\n  ExtendedFields,\n  GeoCoordinates,\n  GetServiceOptionsAndVariantsByServiceIdRequest,\n  GetServiceOptionsAndVariantsByServiceIdResponse,\n  GetServiceOptionsAndVariantsRequest,\n  GetServiceOptionsAndVariantsResponse,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  Locale,\n  MessageEnvelope,\n  Money,\n  Multilingual,\n  Paging,\n  PagingMetadataV2,\n  PlacementType,\n  Properties,\n  PropertiesChange,\n  QueryServiceOptionsAndVariantsRequest,\n  QueryServiceOptionsAndVariantsResponse,\n  QueryV2,\n  QueryV2PagingMethodOneOf,\n  ResolutionMethod,\n  RestoreInfo,\n  ServiceChoice,\n  ServiceChoiceChoiceOneOf,\n  ServiceOption,\n  ServiceOptionOptionSpecificDataOneOf,\n  ServiceOptionType,\n  ServiceOptions,\n  ServiceOptionsAndVariants,\n  ServiceOptionsAndVariantsCreatedEnvelope,\n  ServiceOptionsAndVariantsDeletedEnvelope,\n  ServiceOptionsAndVariantsListQueryBuilder,\n  ServiceOptionsAndVariantsListQueryResult,\n  ServiceOptionsAndVariantsUpdatedEnvelope,\n  ServiceVariant,\n  ServiceVariants,\n  SiteCloned,\n  SiteCreated,\n  SitePropertiesEvent,\n  SitePropertiesNotification,\n  SortOrder,\n  Sorting,\n  SpecialHourPeriod,\n  SupportedLanguage,\n  TimePeriod,\n  Translation,\n  UpdateServiceOptionsAndVariants,\n  UpdateServiceOptionsAndVariantsRequest,\n  UpdateServiceOptionsAndVariantsResponse,\n  WebhookIdentityType,\n} from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, HttpResponse, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.http.js';\n// @ts-ignore\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/**\n * The `serviceOptionsAndVariants` object links a *service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))\n * to its variants.\n * You can use it to offer customers different prices for a service,\n * depending on which choices they book.\n */\nexport interface ServiceOptionsAndVariants {\n  /**\n   * ID of the `serviceOptionsAndVariants` object.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * ID of the service related to these options and variants.\n   * @format GUID\n   * @immutable\n   */\n  serviceId?: string | null;\n  /** Service options. Note that currently only a single option is supported per service. */\n  options?: ServiceOptions;\n  /** Information about the service's variants. */\n  variants?: ServiceVariants;\n  /**\n   * Price of the cheapest service variant.\n   * @readonly\n   */\n  minPrice?: Money;\n  /**\n   * Price of the most expensive service variant.\n   * @readonly\n   */\n  maxPrice?: Money;\n  /**\n   * Revision number, which increments by 1 each time the `serviceOptionsAndVariants` object is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating and deleting the `serviceOptionsAndVariants` object.\n   *\n   * Ignored when creating a `serviceOptionsAndVariants` object.\n   * @immutable\n   */\n  revision?: string | null;\n  /** Extensions enabling users to save custom data related to service options and variants. */\n  extendedFields?: ExtendedFields;\n}\n\nexport interface ServiceOption extends ServiceOptionOptionSpecificDataOneOf {\n  /** Details about the custom option. Available only for `CUSTOM` options. */\n  customData?: CustomServiceOption;\n  durationData?: DurationServiceOption;\n  /**\n   * ID of the service option.\n   * @format GUID\n   */\n  _id?: string;\n  /** Type of the service option. */\n  type?: ServiceOptionTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface ServiceOptionOptionSpecificDataOneOf {\n  /** Details about the custom option. Available only for `CUSTOM` options. */\n  customData?: CustomServiceOption;\n  durationData?: DurationServiceOption;\n}\n\nexport enum ServiceOptionType {\n  /** There is no information about the option type. */\n  UNKNOWN = 'UNKNOWN',\n  /**\n   * The service option is based on a custom parameter. For example, age group,\n   * booked equipment, or appointment timing.\n   */\n  CUSTOM = 'CUSTOM',\n  /**\n   * It's a *staff member*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n   * based option.\n   */\n  STAFF_MEMBER = 'STAFF_MEMBER',\n  /** It's a duration-based option. */\n  DURATION = 'DURATION',\n}\n\n/** @enumType */\nexport type ServiceOptionTypeWithLiterals =\n  | ServiceOptionType\n  | 'UNKNOWN'\n  | 'CUSTOM'\n  | 'STAFF_MEMBER'\n  | 'DURATION';\n\nexport interface CustomServiceOption {\n  /**\n   * Name of the service option. For example, `Age group`, `Location`, `Equipment`,\n   * or `Time`.\n   * @maxLength 255\n   */\n  name?: string;\n  /**\n   * Available choices for the service option. For example, `child`, `student`,\n   * `adult`, and `senior` for a service option named `Age group`. Each value must\n   * be unique. The value's case is ignored, meaning `Child` and `child` are\n   * considered to be identical. Currently, only a single choice is supported\n   * because a service can have only a single option.\n   *\n   * Max: 1 choice\n   * @maxSize 100\n   * @maxLength 255\n   */\n  choices?: string[];\n}\n\nexport interface DurationServiceOption {\n  /**\n   * Optional name of the duration option. For example, `Short Class`, or\n   * `Extended Class`.\n   * @maxLength 255\n   */\n  name?: string | null;\n}\n\nexport interface ServiceVariant {\n  /**\n   * Choices for the service option. Currently, only a single choice is supported\n   * because a service can have only a single option.\n   *\n   * Max: 1 choice\n   * @minSize 1\n   * @maxSize 1\n   */\n  choices?: ServiceChoice[];\n  /** Information about the service variant's price. */\n  price?: Money;\n}\n\nexport interface ServiceChoice extends ServiceChoiceChoiceOneOf {\n  /**\n   * Name of the custom choice.\n   * @maxLength 255\n   */\n  custom?: string;\n  /**\n   * ID of the *staff member*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n   * providing the service.\n   * @format GUID\n   */\n  staffMemberId?: string;\n  /** Information about the option's duration. */\n  duration?: Duration;\n  /**\n   * ID of the service option.\n   * @format GUID\n   */\n  optionId?: string;\n}\n\n/** @oneof */\nexport interface ServiceChoiceChoiceOneOf {\n  /**\n   * Name of the custom choice.\n   * @maxLength 255\n   */\n  custom?: string;\n  /**\n   * ID of the *staff member*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n   * providing the service.\n   * @format GUID\n   */\n  staffMemberId?: string;\n  /** Information about the option's duration. */\n  duration?: Duration;\n}\n\nexport interface Duration {\n  /**\n   * Duration of the service in minutes.\n   *\n   * Min: `1` minute\n   * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)\n   * @min 1\n   * @max 44639\n   */\n  minutes?: number;\n  /**\n   * Name of the duration option.\n   *\n   * Default: Human-readable text of `minutes`. For example, `1 hr 30 min`.\n   * @maxLength 255\n   */\n  name?: string | null;\n}\n\n/**\n * Money.\n * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.\n */\nexport interface Money {\n  /**\n   * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.\n   * @format DECIMAL_VALUE\n   * @decimalValue options { gte:0, maxScale:2 }\n   */\n  value?: string;\n  /**\n   * Currency code. Must be valid ISO 4217 currency code (e.g., USD).\n   * @format CURRENCY\n   */\n  currency?: string;\n  /**\n   * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.\n   * @maxLength 50\n   */\n  formattedValue?: string | null;\n}\n\nexport interface ServiceOptions {\n  /**\n   * Values of the service options.\n   *\n   * Max: 1 service option\n   * @minSize 1\n   * @maxSize 1\n   */\n  values?: ServiceOption[];\n}\n\nexport interface ServiceVariants {\n  /**\n   * Values of the service variants.\n   * @minSize 1\n   * @maxSize 100\n   */\n  values?: ServiceVariant[];\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\nexport interface CreateServiceOptionsAndVariantsRequest {\n  /** Service options and variants to create. */\n  serviceOptionsAndVariants: ServiceOptionsAndVariants;\n}\n\nexport interface CreateServiceOptionsAndVariantsResponse {\n  /** Information about the created service options and variants. */\n  serviceOptionsAndVariants?: ServiceOptionsAndVariants;\n}\n\nexport interface CloneServiceOptionsAndVariantsRequest {\n  /**\n   * ID of the `serviceOptionsAndVariants` object to clone.\n   * @format GUID\n   */\n  cloneFromId: string;\n  /**\n   * ID of the service to which the cloned `serviceOptionsAndVariants` are\n   * connected.\n   * @format GUID\n   */\n  targetServiceId: string;\n}\n\nexport interface CloneServiceOptionsAndVariantsResponse {\n  /** Cloned `serviceOptionsAndVariants` object. */\n  serviceOptionsAndVariants?: ServiceOptionsAndVariants;\n}\n\nexport interface GetServiceOptionsAndVariantsRequest {\n  /**\n   * ID of the `serviceOptionsAndVariants` object to retrieve.\n   * @format GUID\n   */\n  serviceOptionsAndVariantsId: string;\n}\n\nexport interface GetServiceOptionsAndVariantsResponse {\n  /** Retrieved `serviceOptionsAndVariants` object. */\n  serviceOptionsAndVariants?: ServiceOptionsAndVariants;\n}\n\nexport interface GetServiceOptionsAndVariantsByServiceIdRequest {\n  /**\n   * ID of the service to retrieve options and variants for.\n   * @format GUID\n   */\n  serviceId: string;\n}\n\nexport interface GetServiceOptionsAndVariantsByServiceIdResponse {\n  /** Retrieved `serviceOptionsAndVariants` object. */\n  serviceVariants?: ServiceOptionsAndVariants;\n}\n\nexport interface UpdateServiceOptionsAndVariantsRequest {\n  /** `ServiceOptionsAndVariants` object to update. */\n  serviceOptionsAndVariants: ServiceOptionsAndVariants;\n}\n\nexport interface UpdateServiceOptionsAndVariantsResponse {\n  /** Updated `serviceOptionsAndVariants` object. */\n  serviceOptionsAndVariants?: ServiceOptionsAndVariants;\n}\n\nexport interface DeleteServiceOptionsAndVariantsRequest {\n  /**\n   * ID of the `serviceOptionsAndVariants` object to delete.\n   * @format GUID\n   */\n  serviceOptionsAndVariantsId: string;\n  /** Revision of the `serviceOptionsAndVariants` object to delete. */\n  revision?: string;\n}\n\nexport interface DeleteServiceOptionsAndVariantsResponse {}\n\nexport interface QueryServiceOptionsAndVariantsRequest {\n  /** Information about filters, paging, and returned fields. */\n  query: QueryV2;\n}\n\nexport interface QueryV2 extends QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object.\n   *\n   * Learn more about the [filter section](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#the-filter-section).\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object.\n   *\n   * Learn more about the [sort section](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#the-sort-section).\n   * @maxSize 10\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface Paging {\n  /** Number of items to load. */\n  limit?: number | null;\n  /** Number of items to skip in the current sort order. */\n  offset?: number | null;\n}\n\nexport interface CursorPaging {\n  /**\n   * Maximum number of items to return in the results.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface QueryServiceOptionsAndVariantsResponse {\n  /** Retrieved `serviceOptionsAndVariants` objects. */\n  serviceOptionsAndVariantsList?: ServiceOptionsAndVariants[];\n  /** Paging metadata. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface PagingMetadataV2 {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  offset?: number | null;\n  /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */\n  total?: number | null;\n  /** Flag that indicates the server failed to calculate the `total` field. */\n  tooManyToCount?: boolean | null;\n  /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */\n  cursors?: Cursors;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor string pointing to the next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to the previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface Empty {}\n\n/** Encapsulates all details written to the Greyhound topic when a site's properties are updated. */\nexport interface SitePropertiesNotification {\n  /** The site ID for which this update notification applies. */\n  metasiteId?: string;\n  /** The actual update event. */\n  event?: SitePropertiesEvent;\n  /**\n   * A convenience set of mappings from the MetaSite ID to its constituent services.\n   * @maxSize 500\n   */\n  translations?: Translation[];\n  /** Context of the notification */\n  changeContext?: ChangeContext;\n}\n\n/** The actual update event for a particular notification. */\nexport interface SitePropertiesEvent {\n  /** Version of the site's properties represented by this update. */\n  version?: number;\n  /** Set of properties that were updated - corresponds to the fields in \"properties\". */\n  fields?: string[];\n  /** Updated properties. */\n  properties?: Properties;\n}\n\nexport interface Properties {\n  /** Site categories. */\n  categories?: Categories;\n  /** Site locale. */\n  locale?: Locale;\n  /**\n   * Site language.\n   *\n   * Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format.\n   */\n  language?: string | null;\n  /**\n   * Site currency format used to bill customers.\n   *\n   * Three-letter currency code in [ISO-4217 alphabetic](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) format.\n   */\n  paymentCurrency?: string | null;\n  /** Timezone in `America/New_York` format. */\n  timeZone?: string | null;\n  /** Email address. */\n  email?: string | null;\n  /** Phone number. */\n  phone?: string | null;\n  /** Fax number. */\n  fax?: string | null;\n  /** Address. */\n  address?: Address;\n  /** Site display name. */\n  siteDisplayName?: string | null;\n  /** Business name. */\n  businessName?: string | null;\n  /** Path to the site's logo in Wix Media (without Wix Media base URL). */\n  logo?: string | null;\n  /** Site description. */\n  description?: string | null;\n  /**\n   * Business schedule. Regular and exceptional time periods when the business is open or the service is available.\n   *\n   * __Note:__ Not supported by Wix Bookings.\n   */\n  businessSchedule?: BusinessSchedule;\n  /** Supported languages of a site and the primary language. */\n  multilingual?: Multilingual;\n  /** Cookie policy the Wix user defined for their site (before the site visitor interacts with/limits it). */\n  consentPolicy?: ConsentPolicy;\n  /**\n   * Supported values: `FITNESS SERVICE`, `RESTAURANT`, `BLOG`, `STORE`, `EVENT`, `UNKNOWN`.\n   *\n   * Site business type.\n   */\n  businessConfig?: string | null;\n  /** External site URL that uses Wix as its headless business solution. */\n  externalSiteUrl?: string | null;\n  /** Track clicks analytics. */\n  trackClicksAnalytics?: boolean;\n}\n\nexport interface Categories {\n  /** Primary site category. */\n  primary?: string;\n  /**\n   * Secondary site category.\n   * @maxSize 50\n   */\n  secondary?: string[];\n  /** Business Term Id */\n  businessTermId?: string | null;\n}\n\nexport interface Locale {\n  /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */\n  languageCode?: string;\n  /** Two-letter country code in [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements) format. */\n  country?: string;\n}\n\nexport interface Address {\n  /** Street name. */\n  street?: string;\n  /** City name. */\n  city?: string;\n  /** Two-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format. */\n  country?: string;\n  /** State. */\n  state?: string;\n  /**\n   * Zip or postal code.\n   * @maxLength 20\n   */\n  zip?: string;\n  /** Extra information to be displayed in the address. */\n  hint?: AddressHint;\n  /** Whether this address represents a physical location. */\n  isPhysical?: boolean;\n  /** Google-formatted version of this address. */\n  googleFormattedAddress?: string;\n  /** Street number. */\n  streetNumber?: string;\n  /** Apartment number. */\n  apartmentNumber?: string;\n  /** Geographic coordinates of location. */\n  coordinates?: GeoCoordinates;\n}\n\n/**\n * Extra information on displayed addresses.\n * This is used for display purposes. Used to add additional data about the address, such as \"In the passage\".\n * Free text. In addition, the user can state where to display the additional description - before, after, or instead of the address string.\n */\nexport interface AddressHint {\n  /** Extra text displayed next to, or instead of, the actual address. */\n  text?: string;\n  /** Where the extra text should be displayed. */\n  placement?: PlacementTypeWithLiterals;\n}\n\n/** Where the extra text should be displayed: before, after or instead of the actual address. */\nexport enum PlacementType {\n  BEFORE = 'BEFORE',\n  AFTER = 'AFTER',\n  REPLACE = 'REPLACE',\n}\n\n/** @enumType */\nexport type PlacementTypeWithLiterals =\n  | PlacementType\n  | 'BEFORE'\n  | 'AFTER'\n  | 'REPLACE';\n\n/** Geocoordinates for a particular address. */\nexport interface GeoCoordinates {\n  /** Latitude of the location. Must be between -90 and 90. */\n  latitude?: number;\n  /** Longitude of the location. Must be between -180 and 180. */\n  longitude?: number;\n}\n\n/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */\nexport interface BusinessSchedule {\n  /**\n   * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.\n   * @maxSize 100\n   */\n  periods?: TimePeriod[];\n  /**\n   * Exceptions to the business's regular hours. The business can be open or closed during the exception.\n   * @maxSize 100\n   */\n  specialHourPeriod?: SpecialHourPeriod[];\n}\n\n/** Weekly recurring time periods when the business is regularly open or the service is available. */\nexport interface TimePeriod {\n  /** Day of the week the period starts on. */\n  openDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   */\n  openTime?: string;\n  /** Day of the week the period ends on. */\n  closeDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   *\n   * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.\n   */\n  closeTime?: string;\n}\n\n/** Enumerates the days of the week. */\nexport enum DayOfWeek {\n  MONDAY = 'MONDAY',\n  TUESDAY = 'TUESDAY',\n  WEDNESDAY = 'WEDNESDAY',\n  THURSDAY = 'THURSDAY',\n  FRIDAY = 'FRIDAY',\n  SATURDAY = 'SATURDAY',\n  SUNDAY = 'SUNDAY',\n}\n\n/** @enumType */\nexport type DayOfWeekWithLiterals =\n  | DayOfWeek\n  | 'MONDAY'\n  | 'TUESDAY'\n  | 'WEDNESDAY'\n  | 'THURSDAY'\n  | 'FRIDAY'\n  | 'SATURDAY'\n  | 'SUNDAY';\n\n/** Exception to the business's regular hours. The business can be open or closed during the exception. */\nexport interface SpecialHourPeriod {\n  /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  startDate?: string;\n  /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  endDate?: string;\n  /**\n   * Whether the business is closed (or the service is not available) during the exception.\n   *\n   * Default: `true`.\n   */\n  isClosed?: boolean;\n  /** Additional info about the exception. For example, \"We close earlier on New Year's Eve.\" */\n  comment?: string;\n}\n\nexport interface Multilingual {\n  /**\n   * Supported languages list.\n   * @maxSize 200\n   */\n  supportedLanguages?: SupportedLanguage[];\n  /** Whether to redirect to user language. */\n  autoRedirect?: boolean;\n}\n\nexport interface SupportedLanguage {\n  /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */\n  languageCode?: string;\n  /** Locale. */\n  locale?: Locale;\n  /** Whether the supported language is the primary language for the site. */\n  isPrimary?: boolean;\n  /** Language icon. */\n  countryCode?: string;\n  /** How the language will be resolved. For internal use. */\n  resolutionMethod?: ResolutionMethodWithLiterals;\n  /** Whether the supported language is the primary language for site visitors. */\n  isVisitorPrimary?: boolean | null;\n}\n\nexport enum ResolutionMethod {\n  QUERY_PARAM = 'QUERY_PARAM',\n  SUBDOMAIN = 'SUBDOMAIN',\n  SUBDIRECTORY = 'SUBDIRECTORY',\n}\n\n/** @enumType */\nexport type ResolutionMethodWithLiterals =\n  | ResolutionMethod\n  | 'QUERY_PARAM'\n  | 'SUBDOMAIN'\n  | 'SUBDIRECTORY';\n\nexport interface ConsentPolicy {\n  /** Whether the site uses cookies that are essential to site operation. Always `true`. */\n  essential?: boolean | null;\n  /** Whether the site uses cookies that affect site performance and other functional measurements. */\n  functional?: boolean | null;\n  /** Whether the site uses cookies that collect analytics about how the site is used (in order to improve it). */\n  analytics?: boolean | null;\n  /** Whether the site uses cookies that collect information allowing better customization of the experience for a current visitor. */\n  advertising?: boolean | null;\n  /** CCPA compliance flag. */\n  dataToThirdParty?: boolean | null;\n}\n\n/** A single mapping from the MetaSite ID to a particular service. */\nexport interface Translation {\n  /** The service type. */\n  serviceType?: string;\n  /** The application definition ID; this only applies to services of type ThirdPartyApps. */\n  appDefId?: string;\n  /** The instance ID of the service. */\n  instanceId?: string;\n}\n\nexport interface ChangeContext extends ChangeContextPayloadOneOf {\n  /** Properties were updated. */\n  propertiesChange?: PropertiesChange;\n  /** Default properties were created on site creation. */\n  siteCreated?: SiteCreated;\n  /** Properties were cloned on site cloning. */\n  siteCloned?: SiteCloned;\n}\n\n/** @oneof */\nexport interface ChangeContextPayloadOneOf {\n  /** Properties were updated. */\n  propertiesChange?: PropertiesChange;\n  /** Default properties were created on site creation. */\n  siteCreated?: SiteCreated;\n  /** Properties were cloned on site cloning. */\n  siteCloned?: SiteCloned;\n}\n\nexport interface PropertiesChange {}\n\nexport interface SiteCreated {\n  /** Origin template site id. */\n  originTemplateId?: string | null;\n}\n\nexport interface SiteCloned {\n  /** Origin site id. */\n  originMetaSiteId?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type CreateServiceOptionsAndVariantsApplicationErrors =\n  | {\n      code?: 'UNKNOWN_OPTION_IDS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTIPLE_CURRENCIES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNSUPPORTED_OPTION_TYPE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNKNOWN_STAFF_IDS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MISSING_STAFF_VARIANTS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'IDENTICAL_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MISSING_CUSTOM_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNSUPPORTED_OPTION_DATA';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type UpdateServiceOptionsAndVariantsApplicationErrors =\n  | {\n      code?: 'UNKNOWN_OPTION_IDS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTIPLE_CURRENCIES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNSUPPORTED_OPTION_TYPE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNKNOWN_STAFF_IDS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MISSING_STAFF_VARIANTS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'IDENTICAL_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MISSING_CUSTOM_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNSUPPORTED_OPTION_DATA';\n      description?: string;\n      data?: Record<string, any>;\n    };\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface ServiceOptionsAndVariantsCreatedEnvelope {\n  entity: ServiceOptionsAndVariants;\n  metadata: EventMetadata;\n}\n\n/** @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ\n * @webhook\n * @eventType wix.bookings.catalog.v1.service_options_and_variants_created\n * @slug created\n */\nexport declare function onServiceOptionsAndVariantsCreated(\n  handler: (\n    event: ServiceOptionsAndVariantsCreatedEnvelope\n  ) => void | Promise<void>\n): void;\n\nexport interface ServiceOptionsAndVariantsDeletedEnvelope {\n  entity: ServiceOptionsAndVariants;\n  metadata: EventMetadata;\n}\n\n/** @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ\n * @webhook\n * @eventType wix.bookings.catalog.v1.service_options_and_variants_deleted\n * @slug deleted\n */\nexport declare function onServiceOptionsAndVariantsDeleted(\n  handler: (\n    event: ServiceOptionsAndVariantsDeletedEnvelope\n  ) => void | Promise<void>\n): void;\n\nexport interface ServiceOptionsAndVariantsUpdatedEnvelope {\n  entity: ServiceOptionsAndVariants;\n  metadata: EventMetadata;\n}\n\n/** @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ\n * @webhook\n * @eventType wix.bookings.catalog.v1.service_options_and_variants_updated\n * @slug updated\n */\nexport declare function onServiceOptionsAndVariantsUpdated(\n  handler: (\n    event: ServiceOptionsAndVariantsUpdatedEnvelope\n  ) => void | Promise<void>\n): void;\n\n/**\n * Creates a `serviceOptionsAndVariants` object and for a service.\n *\n *\n * ## Calculate variants\n *\n * Before creating a `serviceOptionsAndVariants` object, you need to\n * anticipate and manually define all its variants, since Wix Bookings doesn't\n * automatically calculate them. For the actual\n * Create Service Options And Variants* call, specify both the `options` and\n * `variants` arrays.\n *\n * ## Limitations\n *\n * Wix Bookings allows you to connect only a single `serviceOptionsAndVariants`\n * object to a service. *Create Service Options And Variants* fails, if the\n * service already has a connected `serviceOptionsAndVariants` object.\n *\n * Currently, you can include only a single option per\n * `serviceOptionsAndVariants` object. Taken together, this means that services\n * are limited to a single option.\n *\n * ## Option ID\n *\n * When creating a`serviceOptionsAndVariants` object, you must specify an ID in\n * [UUID format](https://en.wikipedia.org/wiki/Universally_unique_identifier)\n * for its only option. You must reference this option ID for each variant as\n * `variants.values.choices.optionId`.\n *\n * ## Staff member option\n *\n * To creating an option based on the *staff member*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n * providing the service, you need to specify `STAFF_MEMBER` as `options.values.type`.\n * Also, specify all staff member IDs as `variants.values.choices.staffMemberId`.\n * You could follow this *sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-staff-member-based-service-variants) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-staff-member-based-service-variants)).\n *\n * ## Custom option\n *\n * To create an option based on a custom parameter, specify `CUSTOM` as\n * `options.values.type`. Provide descriptive names for all custom choices as\n * `variants.values.choices.custom`. These names are displayed to customers\n * during the book flow. You could follow this *sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment)).\n *\n * ## Duration option\n *\n * To create an option based on appointment duration, specify `DURATION` as\n * `options.values.type` and set a descriptive name in `options.values.durationData.name`.\n * Also, indicate the appointment length in `minutes` and provide a descriptive\n * `name` for each duration choice in `variants.values.choices.duration`.\n * @param serviceOptionsAndVariants - Service options and variants to create.\n * @public\n * @requiredField serviceOptionsAndVariants\n * @requiredField serviceOptionsAndVariants.options\n * @requiredField serviceOptionsAndVariants.serviceId\n * @requiredField serviceOptionsAndVariants.variants\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_CREATE\n * @applicableIdentity APP\n * @returns Information about the created service options and variants.\n * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CreateServiceOptionsAndVariants\n */\nexport async function createServiceOptionsAndVariants(\n  serviceOptionsAndVariants: NonNullablePaths<\n    ServiceOptionsAndVariants,\n    `options` | `serviceId` | `variants`,\n    2\n  >\n): Promise<\n  NonNullablePaths<\n    ServiceOptionsAndVariants,\n    | `options.values`\n    | `options.values.${number}.customData.name`\n    | `options.values.${number}._id`\n    | `options.values.${number}.type`\n    | `variants.values`\n    | `variants.values.${number}.price.value`\n    | `variants.values.${number}.price.currency`,\n    6\n  > & {\n    __applicationErrorsType?: CreateServiceOptionsAndVariantsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceOptionsAndVariants: serviceOptionsAndVariants,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.createServiceOptionsAndVariants(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)\n      ?.serviceOptionsAndVariants!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { serviceOptionsAndVariants: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceOptionsAndVariants']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Clones a `serviceOptionsAndVariants` object and connects it to a *service*\n * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n *\n *\n * The call fails if the service already has a connected\n * `serviceOptionsAndVariants` object.\n *\n * The cloned `serviceOptionsAndVariants` object gets a new, unique option ID.\n * The option ID of the existing `serviceOptionsAndVariants` object isn't reused.\n *\n * For example, you may call this method after *cloning a service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/clone-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/clone-service)).\n * @param cloneFromId - ID of the `serviceOptionsAndVariants` object to clone.\n * @param targetServiceId - ID of the service to which the cloned `serviceOptionsAndVariants` are\n * connected.\n * @public\n * @requiredField cloneFromId\n * @requiredField targetServiceId\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_CREATE\n * @applicableIdentity APP\n * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CloneServiceOptionsAndVariants\n */\nexport async function cloneServiceOptionsAndVariants(\n  cloneFromId: string,\n  targetServiceId: string\n): Promise<\n  NonNullablePaths<\n    CloneServiceOptionsAndVariantsResponse,\n    | `serviceOptionsAndVariants.options.values`\n    | `serviceOptionsAndVariants.options.values.${number}.customData.name`\n    | `serviceOptionsAndVariants.options.values.${number}._id`\n    | `serviceOptionsAndVariants.options.values.${number}.type`\n    | `serviceOptionsAndVariants.variants.values`\n    | `serviceOptionsAndVariants.variants.values.${number}.price.value`\n    | `serviceOptionsAndVariants.variants.values.${number}.price.currency`,\n    7\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    cloneFromId: cloneFromId,\n    targetServiceId: targetServiceId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.cloneServiceOptionsAndVariants(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          cloneFromId: '$[0]',\n          targetServiceId: '$[1]',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['cloneFromId', 'targetServiceId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a `serviceOptionsAndVariants` object by its ID.\n * @param serviceOptionsAndVariantsId - ID of the `serviceOptionsAndVariants` object to retrieve.\n * @public\n * @requiredField serviceOptionsAndVariantsId\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ\n * @applicableIdentity APP\n * @returns Retrieved `serviceOptionsAndVariants` object.\n * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariants\n */\nexport async function getServiceOptionsAndVariants(\n  serviceOptionsAndVariantsId: string\n): Promise<\n  NonNullablePaths<\n    ServiceOptionsAndVariants,\n    | `options.values`\n    | `options.values.${number}.customData.name`\n    | `options.values.${number}._id`\n    | `options.values.${number}.type`\n    | `variants.values`\n    | `variants.values.${number}.price.value`\n    | `variants.values.${number}.price.currency`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceOptionsAndVariantsId: serviceOptionsAndVariantsId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.getServiceOptionsAndVariants(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)\n      ?.serviceOptionsAndVariants!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { serviceOptionsAndVariantsId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceOptionsAndVariantsId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a `serviceOptionsAndVariants` object by *service ID*\n * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n * @param serviceId - ID of the service to retrieve options and variants for.\n * @public\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariantsByServiceId\n */\nexport async function getServiceOptionsAndVariantsByServiceId(\n  serviceId: string\n): Promise<\n  NonNullablePaths<\n    GetServiceOptionsAndVariantsByServiceIdResponse,\n    | `serviceVariants.options.values`\n    | `serviceVariants.options.values.${number}.customData.name`\n    | `serviceVariants.options.values.${number}._id`\n    | `serviceVariants.options.values.${number}.type`\n    | `serviceVariants.variants.values`\n    | `serviceVariants.variants.values.${number}.price.value`\n    | `serviceVariants.variants.values.${number}.price.currency`,\n    7\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.getServiceOptionsAndVariantsByServiceId(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { serviceId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates a `serviceOptionsAndVariants` object.\n *\n *\n * Currently, only a single option is supported per `serviceOptionsAndVariants` object.\n *\n * If you want to update `variants`, you must pass the entire list of supported\n * variants, not only newly added variants.\n *\n * If you want to update `options`, you must pass the entire list of supported\n * options, not only newly added options.\n * @param _id - ID of the `serviceOptionsAndVariants` object.\n * @public\n * @requiredField _id\n * @requiredField serviceOptionsAndVariants\n * @requiredField serviceOptionsAndVariants.revision\n * @param serviceOptionsAndVariants - Service options and variants to update.\n * @param options - Options for updating the service options and variants.\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_UPDATE\n * @applicableIdentity APP\n * @returns Updated `serviceOptionsAndVariants` object.\n * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.UpdateServiceOptionsAndVariants\n */\nexport async function updateServiceOptionsAndVariants(\n  _id: string,\n  serviceOptionsAndVariants: NonNullablePaths<\n    UpdateServiceOptionsAndVariants,\n    `revision`,\n    2\n  >\n): Promise<\n  NonNullablePaths<\n    ServiceOptionsAndVariants,\n    | `options.values`\n    | `options.values.${number}.customData.name`\n    | `options.values.${number}._id`\n    | `options.values.${number}.type`\n    | `variants.values`\n    | `variants.values.${number}.price.value`\n    | `variants.values.${number}.price.currency`,\n    6\n  > & {\n    __applicationErrorsType?: UpdateServiceOptionsAndVariantsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceOptionsAndVariants: { ...serviceOptionsAndVariants, id: _id },\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.updateServiceOptionsAndVariants(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)\n      ?.serviceOptionsAndVariants!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { serviceOptionsAndVariants: '$[1]' },\n        explicitPathsToArguments: { 'serviceOptionsAndVariants.id': '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'serviceOptionsAndVariants']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateServiceOptionsAndVariants {\n  /**\n   * ID of the `serviceOptionsAndVariants` object.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * ID of the service related to these options and variants.\n   * @format GUID\n   * @immutable\n   */\n  serviceId?: string | null;\n  /** Service options. Note that currently only a single option is supported per service. */\n  options?: ServiceOptions;\n  /** Information about the service's variants. */\n  variants?: ServiceVariants;\n  /**\n   * Price of the cheapest service variant.\n   * @readonly\n   */\n  minPrice?: Money;\n  /**\n   * Price of the most expensive service variant.\n   * @readonly\n   */\n  maxPrice?: Money;\n  /**\n   * Revision number, which increments by 1 each time the `serviceOptionsAndVariants` object is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating and deleting the `serviceOptionsAndVariants` object.\n   *\n   * Ignored when creating a `serviceOptionsAndVariants` object.\n   * @immutable\n   */\n  revision?: string | null;\n  /** Extensions enabling users to save custom data related to service options and variants. */\n  extendedFields?: ExtendedFields;\n}\n\n/**\n * Deletes a `serviceOptionsAndVariants` object.\n *\n *\n * Because each service can be connected to only a single `serviceOptionsAndVariants`\n * object, the service doesn't support *varied pricing*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments#service-rates))\n * after deleting a `serviceOptionsAndVariants` object. Instead, Wix Bookings\n * uses its standard price calculation.\n * @param serviceOptionsAndVariantsId - ID of the `serviceOptionsAndVariants` object to delete.\n * @public\n * @requiredField serviceOptionsAndVariantsId\n * @param options - Options for deleting the service options and variants.\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.DeleteServiceOptionsAndVariants\n */\nexport async function deleteServiceOptionsAndVariants(\n  serviceOptionsAndVariantsId: string,\n  options?: DeleteServiceOptionsAndVariantsOptions\n): Promise<void> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceOptionsAndVariantsId: serviceOptionsAndVariantsId,\n    revision: options?.revision,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.deleteServiceOptionsAndVariants(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceOptionsAndVariantsId: '$[0]',\n          revision: '$[1].revision',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceOptionsAndVariantsId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DeleteServiceOptionsAndVariantsOptions {\n  /** Revision of the `serviceOptionsAndVariants` object to delete. */\n  revision?: string;\n}\n\n/**\n * Creates a query to retrieve a list of `serviceOptionsAndVariants` objects.\n *\n * The `queryServiceOptionsAndVariants()` function builds a query to retrieve a list of `serviceOptionsAndVariants` objects and returns a `ServiceOptionsAndVariantsQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-builder/find) function.\n *\n * You can refine the query by chaining `ServiceOptionsAndVariantsQueryBuilder` functions onto the query. `ServiceOptionsAndVariantsQueryBuilder` functions enable you to sort, filter, and control the results that `queryServiceOptionsAndVariants()` returns.\n *\n * `queryServiceOptionsAndVariants()` runs with the following `ServiceOptionsAndVariantsQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryServiceOptionsAndVariants()` are applied in the order they are called. For example, if you apply `ascending(\"options.values.type\")` and then `ascending(\"variants.values.price\")`, the results are sorted first by the `\"type\"`, and then, if there are multiple results with the same `\"type\"`, the items are sorted by `\"price\"`.\n *\n * The following `ServiceOptionsAndVariantsQueryBuilder` functions are supported for the `queryServiceOptionsAndVariants()` function. For a full description of the `serviceOptionsAndVariants` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-result/items) property in `ServiceOptionsAndVariantsQueryResult`.\n * @public\n * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.QueryServiceOptionsAndVariants\n */\nexport function queryServiceOptionsAndVariants(): ServiceOptionsAndVariantsListQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    ServiceOptionsAndVariants,\n    'CURSOR',\n    QueryServiceOptionsAndVariantsRequest,\n    QueryServiceOptionsAndVariantsResponse\n  >({\n    func: async (payload: QueryServiceOptionsAndVariantsRequest) => {\n      const reqOpts =\n        ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.queryServiceOptionsAndVariants(\n          payload\n        );\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (\n      query: QueryServiceOptionsAndVariantsRequest['query']\n    ) => {\n      const args = [query, {}] as [\n        QueryServiceOptionsAndVariantsRequest['query'],\n        {}\n      ];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({\n      data,\n    }: HttpResponse<QueryServiceOptionsAndVariantsResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [])\n      );\n\n      return {\n        items: transformedData?.serviceOptionsAndVariantsList,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'CURSOR',\n    transformationPaths: {},\n  });\n}\n\ninterface QueryCursorResult {\n  cursors: Cursors;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface ServiceOptionsAndVariantsListQueryResult\n  extends QueryCursorResult {\n  items: ServiceOptionsAndVariants[];\n  query: ServiceOptionsAndVariantsListQueryBuilder;\n  next: () => Promise<ServiceOptionsAndVariantsListQueryResult>;\n  prev: () => Promise<ServiceOptionsAndVariantsListQueryResult>;\n}\n\nexport interface ServiceOptionsAndVariantsListQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  eq: (\n    propertyName: '_id' | 'serviceId',\n    value: any\n  ) => ServiceOptionsAndVariantsListQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ne: (\n    propertyName: '_id' | 'serviceId',\n    value: any\n  ) => ServiceOptionsAndVariantsListQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   */\n  hasSome: (\n    propertyName:\n      | 'options.values.id'\n      | 'options.values.type'\n      | 'variants.values.choices.custom'\n      | 'variants.values.choices.optionId'\n      | 'variants.values.price.value'\n      | 'variants.values.price.currency',\n    value: any[]\n  ) => ServiceOptionsAndVariantsListQueryBuilder;\n  /** Refines a query to match items where the specified property is in a short list of specified values. */\n  in: (\n    propertyName: '_id' | 'serviceId',\n    value: any\n  ) => ServiceOptionsAndVariantsListQueryBuilder;\n  /** Refines a query to match items where the specified property is in a list of specified values, such as from another table. */\n  exists: (\n    propertyName: 'options.values' | 'variants.values',\n    value: boolean\n  ) => ServiceOptionsAndVariantsListQueryBuilder;\n  /**\n   * Adds a sort to a query, sorting by the specified properties in ascending order.\n   *\n   * The `ascending()` function refines a `CUSTOM_QUERY_BUILDER_NAME` to sort by the value of `propertyName` in ascending order. You can specify multiple properties for sorting in ascending order by passing each property name as an additional argument. `ascending()` sorts the results in the order the properties are passed. You can sort the following types:\n   *\n   * - Number: Sorts numerically.\n   * - Date: Sorts by date and time.\n   * - String: Sorts lexicographically, so `'abc'` comes after `'XYZ'`. If a property contains a number stored as a string (for example, `'0'`), that value is sorted alphabetically and not numerically. If a property doesn't have a value, that value is ranked lowest.\n   * @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  ascending: (\n    ...propertyNames: Array<'_id' | 'serviceId'>\n  ) => ServiceOptionsAndVariantsListQueryBuilder;\n  /**\n   * Adds a sort to a query, sorting by the specified properties in descending order.\n   *\n   * The `descending()` function refines a `CUSTOM_QUERY_BUILDER_NAME` to sort by the value of `propertyName` in descending order.\n   *\n   * You can specify multiple properties for sorting in descending order by passing each property name as an additional argument. `descending()` sorts the results in the order the properties are passed. You can sort the following types:\n   *\n   * - Number: Sorts numerically.\n   * - Date: Sorts by date and time.\n   * - String: Sorts lexicographically, so `'abc'` comes after `'XYZ'`. If a property contains a number stored as a string (for example, `'0'`), that value is sorted alphabetically and not numerically. If a property doesn't have a value, that value is ranked lowest.\n   * @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  descending: (\n    ...propertyNames: Array<'_id' | 'serviceId'>\n  ) => ServiceOptionsAndVariantsListQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */\n  limit: (limit: number) => ServiceOptionsAndVariantsListQueryBuilder;\n  /** @param cursor - A pointer to specific record */\n  skipTo: (cursor: string) => ServiceOptionsAndVariantsListQueryBuilder;\n  find: () => Promise<ServiceOptionsAndVariantsListQueryResult>;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/v1/serviceOptionsAndVariants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/v1/serviceOptionsAndVariants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '/api',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '/api',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '/api',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/serviceOptionsAndVariants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_service-options-and-variants';\n\n/**\n * Creates a `serviceOptionsAndVariants` object and for a service.\n *\n *\n * ## Calculate variants\n *\n * Before creating a `serviceOptionsAndVariants` object, you need to\n * anticipate and manually define all its variants, since Wix Bookings doesn't\n * automatically calculate them. For the actual\n * Create Service Options And Variants* call, specify both the `options` and\n * `variants` arrays.\n *\n * ## Limitations\n *\n * Wix Bookings allows you to connect only a single `serviceOptionsAndVariants`\n * object to a service. *Create Service Options And Variants* fails, if the\n * service already has a connected `serviceOptionsAndVariants` object.\n *\n * Currently, you can include only a single option per\n * `serviceOptionsAndVariants` object. Taken together, this means that services\n * are limited to a single option.\n *\n * ## Option ID\n *\n * When creating a`serviceOptionsAndVariants` object, you must specify an ID in\n * [UUID format](https://en.wikipedia.org/wiki/Universally_unique_identifier)\n * for its only option. You must reference this option ID for each variant as\n * `variants.values.choices.optionId`.\n *\n * ## Staff member option\n *\n * To creating an option based on the *staff member*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n * providing the service, you need to specify `STAFF_MEMBER` as `options.values.type`.\n * Also, specify all staff member IDs as `variants.values.choices.staffMemberId`.\n * You could follow this *sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-staff-member-based-service-variants) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-staff-member-based-service-variants)).\n *\n * ## Custom option\n *\n * To create an option based on a custom parameter, specify `CUSTOM` as\n * `options.values.type`. Provide descriptive names for all custom choices as\n * `variants.values.choices.custom`. These names are displayed to customers\n * during the book flow. You could follow this *sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment)).\n *\n * ## Duration option\n *\n * To create an option based on appointment duration, specify `DURATION` as\n * `options.values.type` and set a descriptive name in `options.values.durationData.name`.\n * Also, indicate the appointment length in `minutes` and provide a descriptive\n * `name` for each duration choice in `variants.values.choices.duration`.\n */\nexport function createServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __createServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CreateServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __createServiceOptionsAndVariants;\n}\n\n/**\n * Clones a `serviceOptionsAndVariants` object and connects it to a *service*\n * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n *\n *\n * The call fails if the service already has a connected\n * `serviceOptionsAndVariants` object.\n *\n * The cloned `serviceOptionsAndVariants` object gets a new, unique option ID.\n * The option ID of the existing `serviceOptionsAndVariants` object isn't reused.\n *\n * For example, you may call this method after *cloning a service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/clone-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/clone-service)).\n */\nexport function cloneServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __cloneServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CloneServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants/{cloneFromId}/clone',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __cloneServiceOptionsAndVariants;\n}\n\n/** Retrieves a `serviceOptionsAndVariants` object by its ID. */\nexport function getServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'GET' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath:\n          '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n            protoPath:\n              '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n            data: payload,\n            host,\n          }),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __getServiceOptionsAndVariants;\n}\n\n/**\n * Retrieves a `serviceOptionsAndVariants` object by *service ID*\n * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n */\nexport function getServiceOptionsAndVariantsByServiceId(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getServiceOptionsAndVariantsByServiceId({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'GET' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariantsByServiceId',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants/service_id/{serviceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n            protoPath: '/v1/serviceOptionsAndVariants/service_id/{serviceId}',\n            data: payload,\n            host,\n          }),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __getServiceOptionsAndVariantsByServiceId;\n}\n\n/**\n * Updates a `serviceOptionsAndVariants` object.\n *\n *\n * Currently, only a single option is supported per `serviceOptionsAndVariants` object.\n *\n * If you want to update `variants`, you must pass the entire list of supported\n * variants, not only newly added variants.\n *\n * If you want to update `options`, you must pass the entire list of supported\n * options, not only newly added options.\n */\nexport function updateServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __updateServiceOptionsAndVariants({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'PATCH' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.UpdateServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath:\n          '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __updateServiceOptionsAndVariants;\n}\n\n/**\n * Deletes a `serviceOptionsAndVariants` object.\n *\n *\n * Because each service can be connected to only a single `serviceOptionsAndVariants`\n * object, the service doesn't support *varied pricing*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments#service-rates))\n * after deleting a `serviceOptionsAndVariants` object. Instead, Wix Bookings\n * uses its standard price calculation.\n */\nexport function deleteServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __deleteServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'DELETE' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.DeleteServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath:\n          '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteServiceOptionsAndVariants;\n}\n\n/**\n * Creates a query to retrieve a list of `serviceOptionsAndVariants` objects.\n *\n * The `queryServiceOptionsAndVariants()` function builds a query to retrieve a list of `serviceOptionsAndVariants` objects and returns a `ServiceOptionsAndVariantsQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-builder/find) function.\n *\n * You can refine the query by chaining `ServiceOptionsAndVariantsQueryBuilder` functions onto the query. `ServiceOptionsAndVariantsQueryBuilder` functions enable you to sort, filter, and control the results that `queryServiceOptionsAndVariants()` returns.\n *\n * `queryServiceOptionsAndVariants()` runs with the following `ServiceOptionsAndVariantsQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryServiceOptionsAndVariants()` are applied in the order they are called. For example, if you apply `ascending(\"options.values.type\")` and then `ascending(\"variants.values.price\")`, the results are sorted first by the `\"type\"`, and then, if there are multiple results with the same `\"type\"`, the items are sorted by `\"price\"`.\n *\n * The following `ServiceOptionsAndVariantsQueryBuilder` functions are supported for the `queryServiceOptionsAndVariants()` function. For a full description of the `serviceOptionsAndVariants` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-result/items) property in `ServiceOptionsAndVariantsQueryResult`.\n */\nexport function queryServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.QueryServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryServiceOptionsAndVariants;\n}\n", "import {\n  createServiceOptionsAndVariants as publicCreateServiceOptionsAndVariants,\n  cloneServiceOptionsAndVariants as publicCloneServiceOptionsAndVariants,\n  getServiceOptionsAndVariants as publicGetServiceOptionsAndVariants,\n  getServiceOptionsAndVariantsByServiceId as publicGetServiceOptionsAndVariantsByServiceId,\n  updateServiceOptionsAndVariants as publicUpdateServiceOptionsAndVariants,\n  deleteServiceOptionsAndVariants as publicDeleteServiceOptionsAndVariants,\n  queryServiceOptionsAndVariants as publicQueryServiceOptionsAndVariants,\n} from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { createEventModule } from '@wix/sdk-runtime/event-definition-modules';\nimport {\n  BuildRESTFunction,\n  MaybeContext,\n  BuildEventDefinition,\n} from '@wix/sdk-types';\nimport { onServiceOptionsAndVariantsCreated as publicOnServiceOptionsAndVariantsCreated } from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.public.js';\nimport { onServiceOptionsAndVariantsDeleted as publicOnServiceOptionsAndVariantsDeleted } from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.public.js';\nimport { onServiceOptionsAndVariantsUpdated as publicOnServiceOptionsAndVariantsUpdated } from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.public.js';\n\nexport const createServiceOptionsAndVariants: MaybeContext<\n  BuildRESTFunction<typeof publicCreateServiceOptionsAndVariants> &\n    typeof publicCreateServiceOptionsAndVariants\n> = /*#__PURE__*/ createRESTModule(publicCreateServiceOptionsAndVariants);\nexport const cloneServiceOptionsAndVariants: MaybeContext<\n  BuildRESTFunction<typeof publicCloneServiceOptionsAndVariants> &\n    typeof publicCloneServiceOptionsAndVariants\n> = /*#__PURE__*/ createRESTModule(publicCloneServiceOptionsAndVariants);\nexport const getServiceOptionsAndVariants: MaybeContext<\n  BuildRESTFunction<typeof publicGetServiceOptionsAndVariants> &\n    typeof publicGetServiceOptionsAndVariants\n> = /*#__PURE__*/ createRESTModule(publicGetServiceOptionsAndVariants);\nexport const getServiceOptionsAndVariantsByServiceId: MaybeContext<\n  BuildRESTFunction<typeof publicGetServiceOptionsAndVariantsByServiceId> &\n    typeof publicGetServiceOptionsAndVariantsByServiceId\n> = /*#__PURE__*/ createRESTModule(\n  publicGetServiceOptionsAndVariantsByServiceId\n);\nexport const updateServiceOptionsAndVariants: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateServiceOptionsAndVariants> &\n    typeof publicUpdateServiceOptionsAndVariants\n> = /*#__PURE__*/ createRESTModule(publicUpdateServiceOptionsAndVariants);\nexport const deleteServiceOptionsAndVariants: MaybeContext<\n  BuildRESTFunction<typeof publicDeleteServiceOptionsAndVariants> &\n    typeof publicDeleteServiceOptionsAndVariants\n> = /*#__PURE__*/ createRESTModule(publicDeleteServiceOptionsAndVariants);\nexport const queryServiceOptionsAndVariants: MaybeContext<\n  BuildRESTFunction<typeof publicQueryServiceOptionsAndVariants> &\n    typeof publicQueryServiceOptionsAndVariants\n> = /*#__PURE__*/ createRESTModule(publicQueryServiceOptionsAndVariants);\n/** */\nexport const onServiceOptionsAndVariantsCreated: BuildEventDefinition<\n  typeof publicOnServiceOptionsAndVariantsCreated\n> = createEventModule(publicOnServiceOptionsAndVariantsCreated);\n/** */\nexport const onServiceOptionsAndVariantsDeleted: BuildEventDefinition<\n  typeof publicOnServiceOptionsAndVariantsDeleted\n> = createEventModule(publicOnServiceOptionsAndVariantsDeleted);\n/** */\nexport const onServiceOptionsAndVariantsUpdated: BuildEventDefinition<\n  typeof publicOnServiceOptionsAndVariantsUpdated\n> = createEventModule(publicOnServiceOptionsAndVariantsUpdated);\n\nexport {\n  ServiceOptionType,\n  SortOrder,\n  PlacementType,\n  DayOfWeek,\n  ResolutionMethod,\n  WebhookIdentityType,\n} from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.js';\nexport {\n  ServiceOptionsAndVariants,\n  ServiceOption,\n  ServiceOptionOptionSpecificDataOneOf,\n  CustomServiceOption,\n  DurationServiceOption,\n  ServiceVariant,\n  ServiceChoice,\n  ServiceChoiceChoiceOneOf,\n  Duration,\n  Money,\n  ServiceOptions,\n  ServiceVariants,\n  ExtendedFields,\n  CreateServiceOptionsAndVariantsRequest,\n  CreateServiceOptionsAndVariantsResponse,\n  CloneServiceOptionsAndVariantsRequest,\n  CloneServiceOptionsAndVariantsResponse,\n  GetServiceOptionsAndVariantsRequest,\n  GetServiceOptionsAndVariantsResponse,\n  GetServiceOptionsAndVariantsByServiceIdRequest,\n  GetServiceOptionsAndVariantsByServiceIdResponse,\n  UpdateServiceOptionsAndVariantsRequest,\n  UpdateServiceOptionsAndVariantsResponse,\n  DeleteServiceOptionsAndVariantsRequest,\n  DeleteServiceOptionsAndVariantsResponse,\n  QueryServiceOptionsAndVariantsRequest,\n  QueryV2,\n  QueryV2PagingMethodOneOf,\n  Sorting,\n  Paging,\n  CursorPaging,\n  QueryServiceOptionsAndVariantsResponse,\n  PagingMetadataV2,\n  Cursors,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  RestoreInfo,\n  EntityUpdatedEvent,\n  EntityDeletedEvent,\n  ActionEvent,\n  Empty,\n  SitePropertiesNotification,\n  SitePropertiesEvent,\n  Properties,\n  Categories,\n  Locale,\n  Address,\n  AddressHint,\n  GeoCoordinates,\n  BusinessSchedule,\n  TimePeriod,\n  SpecialHourPeriod,\n  Multilingual,\n  SupportedLanguage,\n  ConsentPolicy,\n  Translation,\n  ChangeContext,\n  ChangeContextPayloadOneOf,\n  PropertiesChange,\n  SiteCreated,\n  SiteCloned,\n  MessageEnvelope,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  BaseEventMetadata,\n  EventMetadata,\n  ServiceOptionsAndVariantsCreatedEnvelope,\n  ServiceOptionsAndVariantsDeletedEnvelope,\n  ServiceOptionsAndVariantsUpdatedEnvelope,\n  UpdateServiceOptionsAndVariants,\n  DeleteServiceOptionsAndVariantsOptions,\n  ServiceOptionsAndVariantsListQueryResult,\n  ServiceOptionsAndVariantsListQueryBuilder,\n} from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.js';\nexport {\n  ServiceOptionTypeWithLiterals,\n  SortOrderWithLiterals,\n  PlacementTypeWithLiterals,\n  DayOfWeekWithLiterals,\n  ResolutionMethodWithLiterals,\n  WebhookIdentityTypeWithLiterals,\n  CreateServiceOptionsAndVariantsApplicationErrors,\n  UpdateServiceOptionsAndVariantsApplicationErrors,\n} from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wCAAAA;AAAA,EAAA,uCAAAC;AAAA,EAAA,uCAAAC;AAAA,EAAA,oCAAAC;AAAA,EAAA,+CAAAC;AAAA,EAAA,0CAAAC;AAAA,EAAA,0CAAAC;AAAA,EAAA,0CAAAC;AAAA,EAAA,sCAAAC;AAAA,EAAA,uCAAAC;AAAA;AAAA;;;ACAA,IAAAC,iCAAwD;AACxD,uBAAqD;AACrD,IAAAC,0BAA+B;AAC/B,uBAA8D;;;ACH9D,6BAAoD;AACpD,2BAA6B;AAC7B,oCAGO;;;ACLP,0BAAkC;AAClC,wBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,+DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAuDd,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgBO,SAAS,+BACd,SAC4B;AAC5B,WAAS,iCAAiC,EAAE,KAAK,GAAQ;AACvD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,6BACd,SAC4B;AAC5B,WAAS,+BAA+B,EAAE,KAAK,GAAQ;AACrD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK,+DAA+D;AAAA,YAClE,WACE;AAAA,YACF,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,UACD,YAAQ,uCAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,wCACd,SAC4B;AAC5B,WAAS,0CAA0C,EAAE,KAAK,GAAQ;AAChE,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK,+DAA+D;AAAA,YAClE,WAAW;AAAA,YACX,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,UACD,YAAQ,uCAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAcO,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAYO,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,+BACd,SAC4B;AAC5B,WAAS,iCAAiC,EAAE,KAAK,GAAQ;AACvD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADrZA,IAAAC,0BAA+B;AAqExB,IAAK,oBAAL,kBAAKC,uBAAL;AAEL,EAAAA,mBAAA,aAAU;AAKV,EAAAA,mBAAA,YAAS;AAMT,EAAAA,mBAAA,kBAAe;AAEf,EAAAA,mBAAA,cAAW;AAfD,SAAAA;AAAA,GAAA;AAgTL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AAqRL,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,aAAU;AAHA,SAAAA;AAAA,GAAA;AAwDL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,aAAU;AACV,EAAAA,WAAA,eAAY;AACZ,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AA8DL,IAAK,mBAAL,kBAAKC,sBAAL;AACL,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,eAAY;AACZ,EAAAA,kBAAA,kBAAe;AAHL,SAAAA;AAAA,GAAA;AAqIL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAsSZ,eAAsBC,iCACpB,2BAmBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACoD;AAAA,IACtD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GACtD;AAAA,EACN,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,2BAA2B,OAAO;AAAA,QAC9D,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,2BAA2B;AAAA,IAC9B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyBA,eAAsBC,gCACpB,aACA,iBAaA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UACoD;AAAA,IACtD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,aAAa;AAAA,UACb,iBAAiB;AAAA,QACnB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,eAAe,iBAAiB;AAAA,IACnC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAYA,eAAsBE,8BACpB,6BAaA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACoD;AAAA,IACtD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GACtD;AAAA,EACN,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,6BAA6B,OAAO;AAAA,QAChE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,6BAA6B;AAAA,IAChC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAYA,eAAsBG,yCACpB,WAaA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACoD;AAAA,IACtD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAH;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,WAAW,OAAO;AAAA,QAC9C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,WAAW;AAAA,IACd;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyBA,eAAsBI,iCACpB,KACA,2BAmBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,2BAA2B,EAAE,GAAG,2BAA2B,IAAI,IAAI;AAAA,EACrE,CAAC;AAED,QAAM,UACoD;AAAA,IACtD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GACtD;AAAA,EACN,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAJ;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,2BAA2B,OAAO;AAAA,QAC5D,0BAA0B,EAAE,gCAAgC,OAAO;AAAA,QACnE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,2BAA2B;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA2DA,eAAsBK,iCACpB,6BACA,SACe;AAEf,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,UAAU,SAAS;AAAA,EACrB,CAAC;AAED,QAAM,UACoD;AAAA,IACtD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAAA,EACjC,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAL;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,6BAA6B;AAAA,UAC7B,UAAU;AAAA,QACZ;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,+BAA+B,SAAS;AAAA,IAC3C;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA6BO,SAASM,kCAA4E;AAE1F,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,aAAO,mCAKL;AAAA,IACA,MAAM,OAAO,YAAmD;AAC9D,YAAM,UACoD;AAAA,QACtD;AAAA,MACF;AAEF,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAClB,UACG;AACH,YAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AAIvB,iBAAO,qEAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC;AAAA,MACpB;AAAA,IACF,MAA4D;AAC1D,YAAM,sBAAkB;AAAA,YACtB,wCAAe,MAAM,CAAC,CAAC;AAAA,MACzB;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,uBAAmB,uBAAAN,gBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;;;ADprDO,SAASO,iCACd,YAC0C;AAC1C,SAAO,CACL,8BAMAA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiFO,SAASC,gCACd,YACyC;AACzC,SAAO,CAAC,aAAqB,oBAC3BA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAmCO,SAASC,8BACd,YACuC;AACvC,SAAO,CAAC,gCACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAuBO,SAASC,yCACd,YACkD;AAClD,SAAO,CAAC,cACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAuBO,SAASC,iCACd,YAC0C;AAC1C,SAAO,CACL,KACA,8BAMAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA2CO,SAASC,iCACd,YAC0C;AAC1C,SAAO,CACL,6BACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAqBO,SAASC,gCACd,YACyC;AACzC,SAAO,MACLA;AAAA;AAAA,IAEE,EAAE,WAAW;AAAA,EACf;AACJ;AAwBO,IAAM,yCAAqC;AAAA,EAChD;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,qBAAqB,CAAC;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA4C;AACrC,IAAM,yCAAqC;AAAA,EAChD;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,qBAAqB,CAAC;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA4C;AACrC,IAAM,yCAAqC;AAAA,EAChD;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,qBAAqB,CAAC;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA4C;;;AGnY5C,IAAAC,uBAAiC;AACjC,sCAAkC;AAU3B,IAAMC,mCAGK,2DAAiBA,gCAAqC;AACjE,IAAMC,kCAGK,2DAAiBA,+BAAoC;AAChE,IAAMC,gCAGK,2DAAiBA,6BAAkC;AAC9D,IAAMC,2CAGK;AAAA,EAChBA;AACF;AACO,IAAMC,mCAGK,2DAAiBA,gCAAqC;AACjE,IAAMC,mCAGK,2DAAiBA,gCAAqC;AACjE,IAAMC,kCAGK,2DAAiBA,+BAAoC;AAEhE,IAAMC,0CAET,mDAAkB,kCAAwC;AAEvD,IAAMC,0CAET,mDAAkB,kCAAwC;AAEvD,IAAMC,0CAET,mDAAkB,kCAAwC;", "names": ["cloneServiceOptionsAndVariants", "createServiceOptionsAndVariants", "deleteServiceOptionsAndVariants", "getServiceOptionsAndVariants", "getServiceOptionsAndVariantsByServiceId", "onServiceOptionsAndVariantsCreated", "onServiceOptionsAndVariantsDeleted", "onServiceOptionsAndVariantsUpdated", "queryServiceOptionsAndVariants", "updateServiceOptionsAndVariants", "import_rename_all_nested_keys", "import_transform_paths", "import_rest_modules", "import_transform_paths", "ServiceOptionType", "SortOrder", "PlacementType", "DayOfWeek", "ResolutionMethod", "WebhookIdentityType", "createServiceOptionsAndVariants", "sdkTransformError", "cloneServiceOptionsAndVariants", "getServiceOptionsAndVariants", "getServiceOptionsAndVariantsByServiceId", "updateServiceOptionsAndVariants", "deleteServiceOptionsAndVariants", "queryServiceOptionsAndVariants", "createServiceOptionsAndVariants", "cloneServiceOptionsAndVariants", "getServiceOptionsAndVariants", "getServiceOptionsAndVariantsByServiceId", "updateServiceOptionsAndVariants", "deleteServiceOptionsAndVariants", "queryServiceOptionsAndVariants", "import_rest_modules", "createServiceOptionsAndVariants", "cloneServiceOptionsAndVariants", "getServiceOptionsAndVariants", "getServiceOptionsAndVariantsByServiceId", "updateServiceOptionsAndVariants", "deleteServiceOptionsAndVariants", "queryServiceOptionsAndVariants", "onServiceOptionsAndVariantsCreated", "onServiceOptionsAndVariantsDeleted", "onServiceOptionsAndVariantsUpdated"]}