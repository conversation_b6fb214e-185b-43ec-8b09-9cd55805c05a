{"version": 3, "sources": ["../../index.typings.ts", "../../src/bookings-staff-v1-staff-member-staff-members.universal.ts", "../../src/bookings-staff-v1-staff-member-staff-members.http.ts"], "sourcesContent": ["import {\n  SearchStaffMembersResponse,\n  CursorSearch,\n  SearchStaffMembersOptions,\n} from './src/bookings-staff-v1-staff-member-staff-members.universal';\n\nexport * from './src/bookings-staff-v1-staff-member-staff-members.universal.js';\n\n/** @hidden */\nexport type StaffMemberSearch = {};\n\n/**\n * Retrieves a list of up to 100 staff members, given the provided filtering, paging,\n * and sorting.\n *\n *\n * ## Defaults\n *\n * Search Staff Members has the following default settings, which you can override:\n * + Sorted by `createdDate` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filter\n *\n * Refer to the _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with *Search* methods, see\n * _API Query Language_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))\n * and _Sorting and Paging_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).\n * @param search - Search criteria including filter, sort, and paging options.\n *\n * See the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)) for complete filter and sorting options.\n * @public\n * @documentationMaturity preview\n * @requiredField search\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers\n */\nexport declare function searchStaffMembers(\n  search: CursorSearch,\n  options: SearchStaffMembersOptions\n): Promise<SearchStaffMembersResponse>;\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameK<PERSON>s<PERSON>romRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport {\n  HttpClient,\n  HttpResponse,\n  Search as SearchSdkType,\n  NonNullablePaths,\n  SearchSpec,\n} from '@wix/sdk-types';\nimport * as ambassadorWixBookingsStaffV1StaffMember from './bookings-staff-v1-staff-member-staff-members.http.js';\n// @ts-ignore\nimport { transformSDKImageToRESTImage } from '@wix/sdk-runtime/transformations/image';\nimport { transformRESTImageToSDKImage } from '@wix/sdk-runtime/transformations/image';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/**\n * An individual providing services within Wix Bookings. Only staff members who\n * are also Wix users can manage their working hours in the dashboard.\n */\nexport interface StaffMember {\n  /**\n   * Staff member ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Staff member name.\n   * @minLength 1\n   * @maxLength 40\n   */\n  name?: string | null;\n  /**\n   * Staff member's email address.\n   * @maxLength 320\n   * @format EMAIL\n   */\n  email?: string | null;\n  /**\n   * Staff member's phone number.\n   * @maxLength 20\n   * @format PHONE\n   */\n  phone?: string | null;\n  /**\n   * Description for the staff member. For example,\n   * `Experienced nail technician specialized in gel and acrylic nails`.\n   * @maxLength 500\n   */\n  description?: string | null;\n  /** Staff media. */\n  mainMedia?: MediaItem;\n  /**\n   * Staff member's *resource ID*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)),\n   * identical to `resource.id`.\n   * @readonly\n   * @format GUID\n   */\n  resourceId?: string | null;\n  /**\n   * Details about the *resource object*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))\n   * associated with the staff member. Available only if you specify `RESOURCE_DETAILS`\n   * in the `fields` array.\n   * @readonly\n   */\n  resource?: Resource;\n  /**\n   * Identity of the Wix user associated with the staff member. Learn more about\n   * _identities_\n   * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).\n   * @readonly\n   */\n  associatedWixIdentity?: AssociatedWixIdentity;\n  /**\n   * Revision number, which increments by 1 each time the staff member is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating the staff member.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Time the staff member was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Time the staff member was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /** Extensions enabling users to save custom data related to the staff member. */\n  extendedFields?: ExtendedFields;\n  /**\n   * Tags allowing you to classify staff members. Learn more about *tags*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n   */\n  tags?: Tags;\n}\n\nexport interface MediaItem extends MediaItemMediaOneOf {\n  /** Staff member's main image. */\n  image?: string;\n}\n\n/** @oneof */\nexport interface MediaItemMediaOneOf {\n  /** Staff member's main image. */\n  image?: string;\n}\n\nexport interface Resource {\n  /**\n   * ID of the *resource*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))\n   * associated with the staff member.\n   * @format GUID\n   */\n  _id?: string | null;\n  /**\n   * Working hour *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * of the staff member. By default, identical the opening hours of the\n   * business's *default location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   * If the staff has custom working hours, identical to `eventsSchedule`.\n   * @maxSize 1\n   */\n  workingHoursSchedules?: WorkingHoursSchedule[];\n  /**\n   * Event *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * of the staff member.\n   */\n  eventsSchedule?: EventSchedule;\n  /**\n   * Whether the staff member works according to business's *default location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))\n   * opening hours.\n   *\n   * `false`: The staff has custom working hours.\n   *\n   * Default: `true`\n   */\n  usesDefaultWorkingHours?: boolean;\n}\n\nexport interface WorkingHoursSchedule {\n  /**\n   * ID of the working hour *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * associated with the staff member. Currently, each staff member can't have more than a\n   * single working hour schedule. Learn more about\n   * _how Wix Bookings uses the Calendar APIs_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).\n   * @format GUID\n   */\n  _id?: string | null;\n  /**\n   * Whether the working hour schedule is shared with the business. If this field\n   * isn't available, the schedule isn't shared but specific for the staff member.\n   */\n  shared?: boolean;\n}\n\nexport interface EventSchedule {\n  /**\n   * ID of the event *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * associated with the staff member. Learn more about\n   * _how Wix Bookings uses the Calendar APIs_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).\n   * @format GUID\n   */\n  _id?: string | null;\n}\n\nexport interface LocationOptions {\n  /**\n   * Whether the resource is available in all *business locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).\n   *\n   * - `true`: The resource is available in all business locations.\n   * - `false`: The resource is available only in specific locations.\n   *\n   * Default: `false`\n   */\n  availableInAllLocations?: boolean | null;\n  /** Details of resource availability in specific locations. */\n  specificLocationOptions?: SpecificLocation;\n}\n\nexport interface SpecificLocation {\n  /**\n   * Whether the resource is available in *business locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).\n   *\n   * - `true`: The resource is available in business locations.\n   * - `false`: The resource isn't available in business locations.\n   *\n   * Default: `false`\n   */\n  availableInBusinessLocations?: boolean | null;\n  /**\n   * Information about the business locations where the resource is available.\n   * Not returned, if the resource is available in either all business locations\n   * or in no business location.\n   * You can specify up to 100 business locations.\n   * @maxSize 100\n   */\n  businessLocations?: BusinessLocation[];\n}\n\nexport interface BusinessLocation {\n  /**\n   * ID of the business *location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   * @format GUID\n   */\n  locationId?: string | null;\n}\n\n/** A staff member resource can be associated with a Wix user via assignment of a permissions role in the business manager. */\nexport interface AssociatedWixIdentity {\n  /**\n   * Information about the identity connected to the staff member. Available only\n   * if the staff member is connected to a Wix user. Learn more about *identities*\n   * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).\n   */\n  identificationData?: CommonIdentificationData;\n  /**\n   * Connection status.\n   * @readonly\n   * @deprecated Connection status.\n   * @replacedBy connection\n   * @targetRemovalDate 2024-12-01\n   */\n  connectionStatus?: AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals;\n  /**\n   * Connection status. Available only if you specify `ASSOCIATED_IDENTITY_STATUS`\n   * in the `fields` array.\n   * @readonly\n   */\n  connection?: Connection;\n}\n\nexport interface CommonIdentificationData\n  extends CommonIdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /**\n   * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system. Retrievable via the [Contacts API](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/introduction)\n   * @format GUID\n   */\n  contactId?: string | null;\n}\n\n/** @oneof */\nexport interface CommonIdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum IdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type IdentityTypeWithLiterals =\n  | IdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\nexport enum AssociatedWixIdentityConnectionStatusEnumConnectionStatus {\n  /** There is no information about the connection status. */\n  UNKNOWN = 'UNKNOWN',\n  /** The Wix user is connected to the staff member. */\n  CONNECTED = 'CONNECTED',\n  /** The Wix user must accept the invitation to join **.wix.com.** or the site. */\n  PENDING = 'PENDING',\n  /** The invitation to join **.wix.com.** or the site has expired. */\n  EXPIRED = 'EXPIRED',\n  /** The Wix user was disconnected from the staff member. */\n  DISCONNECTED = 'DISCONNECTED',\n}\n\n/** @enumType */\nexport type AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals =\n\n    | AssociatedWixIdentityConnectionStatusEnumConnectionStatus\n    | 'UNKNOWN'\n    | 'CONNECTED'\n    | 'PENDING'\n    | 'EXPIRED'\n    | 'DISCONNECTED';\n\nexport interface Connection {\n  /**\n   * Connection status of the Wix user and the staff member.\n   * @readonly\n   */\n  status?: AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals;\n}\n\nexport interface AssociatedConferencingProviders {\n  /**\n   * Conferencing accounts connected to the staff member.\n   * @maxSize 10\n   */\n  items?: AssociatedConferencingProvider[];\n}\n\nexport interface AssociatedConferencingProvider {\n  /**\n   * Conferencing provider ID. For example, Zoom integration identifier.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Provider name. For example, Google Meet.\n   * @maxLength 255\n   */\n  name?: string;\n  /** Connection status. */\n  connectionStatus?: ConnectionStatusWithLiterals;\n  /**\n   * Conferencing account email. Might not match staff email.\n   * @maxLength 320\n   * @format EMAIL\n   */\n  accountEmail?: string | null;\n}\n\nexport enum ConnectionStatus {\n  /** Provider is connected to the site and the user is authenticated. */\n  CONNECTED = 'CONNECTED',\n  /** Provider is not connected to the site or the user is not authenticated. */\n  DISCONNECTED = 'DISCONNECTED',\n}\n\n/** @enumType */\nexport type ConnectionStatusWithLiterals =\n  | ConnectionStatus\n  | 'CONNECTED'\n  | 'DISCONNECTED';\n\nexport interface AssociatedConferencingAccounts {\n  /**\n   * Conferencing accounts connected to the staff member.\n   * @maxSize 10\n   */\n  items?: AssociatedConferencingAccount[];\n}\n\nexport interface AssociatedConferencingAccount\n  extends AssociatedConferencingAccountAccountOneOf {\n  /**\n   * Conferencing for the staff member, configured via an external provider.\n   * For example, a Zoom integration generating a link for sessions with the staff member.\n   */\n  providerAccount?: AssociatedConferencingProvider;\n  /** Conferencing for the staff member, configured via a custom conference link. */\n  customAccount?: CustomConferenceAccount;\n}\n\n/** @oneof */\nexport interface AssociatedConferencingAccountAccountOneOf {\n  /**\n   * Conferencing for the staff member, configured via an external provider.\n   * For example, a Zoom integration generating a link for sessions with the staff member.\n   */\n  providerAccount?: AssociatedConferencingProvider;\n  /** Conferencing for the staff member, configured via a custom conference link. */\n  customAccount?: CustomConferenceAccount;\n}\n\nexport interface CustomConferenceAccount {\n  /**\n   * Conferencing URL. For example, a Google Meet link.\n   * @maxLength 2083\n   */\n  url?: string | null;\n  /**\n   * Optional password that protects the conferencing link.\n   * @maxLength 320\n   */\n  password?: string | null;\n  /**\n   * Optional description of the conference link.\n   * @maxLength 3000\n   */\n  description?: string | null;\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\n/**\n * Common object for tags.\n * Should be use as in this example:\n * message Foo {\n * string id = 1;\n * ...\n * Tags tags = 5\n * }\n *\n * example of taggable entity\n * {\n * id: \"123\"\n * tags: {\n * tags: {\n * tag_ids:[\"11\",\"22\"]\n * },\n * private_tags: {\n * tag_ids: [\"33\", \"44\"]\n * }\n * }\n * }\n */\nexport interface Tags {\n  /** Tags that require an additional permission in order to access them, normally not given to site members or visitors. */\n  privateTags?: TagList;\n  /** Tags that are exposed to anyone who has access to the labeled entity itself, including site members and visitors. */\n  tags?: TagList;\n}\n\nexport interface TagList {\n  /**\n   * List of tag IDs\n   * @maxSize 100\n   * @maxLength 5\n   */\n  tagIds?: string[];\n}\n\nexport interface StaffMemberDisconnectedFromUser {\n  /** Updated staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface StaffMemberConnectedToUser {\n  /** Updated staff member. */\n  staffMember?: StaffMember;\n}\n\n/**\n * This message is used to notify that a staff member has been fully created. For now it is only needed for\n * producing the correct Resources V1 events. A decision will be made later whether to keep it or not.\n */\nexport interface StaffMemberFullyCreated {\n  /** Created staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface CreateStaffMemberRequest {\n  /** Staff member to create. */\n  staffMember: StaffMember;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport enum RequestedFields {\n  /** Includes `resource` in the response. */\n  RESOURCE_DETAILS = 'RESOURCE_DETAILS',\n  /** Includes `associatedIdentityStatus` in the response. */\n  ASSOCIATED_IDENTITY_STATUS = 'ASSOCIATED_IDENTITY_STATUS',\n}\n\n/** @enumType */\nexport type RequestedFieldsWithLiterals =\n  | RequestedFields\n  | 'RESOURCE_DETAILS'\n  | 'ASSOCIATED_IDENTITY_STATUS';\n\nexport interface CreateStaffMemberResponse {\n  /** Created staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface GetStaffMemberRequest {\n  /**\n   * ID of the staff member to retrieve.\n   * @format GUID\n   */\n  staffMemberId: string;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface GetStaffMemberResponse {\n  /** Retrieved staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface GetDeletedStaffMemberRequest {\n  /**\n   * ID of the deleted staff member to retrieve.\n   * @format GUID\n   */\n  staffMemberId?: string;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface GetDeletedStaffMemberResponse {\n  /** Retrieved staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface ListDeletedStaffMembersRequest {\n  /**\n   * IDs of the deleted staff members to retrieve.\n   *\n   * Default: All deleted staff members are returned, given the provided paging.\n   * @format GUID\n   * @maxSize 100\n   */\n  staffMemberIds?: string[];\n  /** Paging details. */\n  paging?: CursorPaging;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface CursorPaging {\n  /**\n   * Number of items to load.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * You can get the relevant cursor token\n   * from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface ListDeletedStaffMembersResponse {\n  /** Retrieved staff members. */\n  staffMembers?: StaffMember[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /**\n   * Number of items returned in the current response page.\n   *\n   * This count reflects the actual number of items in the current result set,\n   * which may be less than the requested limit if fewer items are available.\n   */\n  count?: number | null;\n  /**\n   * Navigation cursors for moving between result pages.\n   *\n   * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor\n   * to retrieve subsequent pages and `prev` cursor to go back to previous pages.\n   * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).\n   */\n  cursors?: Cursors;\n  /**\n   * Indicates whether additional results are available beyond the current page.\n   *\n   * - `true`: More results exist and can be retrieved using the `next` cursor.\n   * - `false`: This is the final page of results.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor token for retrieving the next page of results.\n   *\n   * Use this token in subsequent requests to continue pagination forward.\n   * Value is `null` when on the last page of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor token for retrieving the previous page of results.\n   *\n   * Use this token to navigate backwards through result pages.\n   * Value is `null` when on the first page of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface RemoveStaffMemberFromTrashBinRequest {\n  /**\n   * ID of the staff member to delete permanently.\n   * @format GUID\n   */\n  staffMemberId?: string;\n}\n\nexport interface RemoveStaffMemberFromTrashBinResponse {}\n\nexport interface RestoreStaffMemberFromTrashBinRequest {\n  /**\n   * ID of the staff member to restore from the trash bin.\n   * @format GUID\n   */\n  staffMemberId?: string;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface RestoreStaffMemberFromTrashBinResponse {\n  /** Restored staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface UpdateStaffMemberRequest {\n  /** Staff member to update. */\n  staffMember: StaffMember;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface UpdateStaffMemberResponse {\n  /** Updated staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface DeleteStaffMemberRequest {\n  /**\n   * ID of the staff member to delete.\n   * @format GUID\n   */\n  staffMemberId: string;\n}\n\nexport interface DeleteStaffMemberResponse {}\n\nexport interface QueryStaffMembersRequest {\n  /**\n   * Information about filters, paging, and sorting. See the article about\n   * booking policy filters\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n   * for all supported filters and sorting options.\n   */\n  query?: CursorQuery;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface CursorQuery extends CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   * @maxSize 10\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface QueryStaffMembersResponse {\n  /** Retrieved staff members. */\n  staffMembers?: StaffMember[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface QueryStaffMembersMultiLanguageRequest {\n  /**\n   * Information about filters, paging, and sorting. See the article about\n   * booking policy filters\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n   * for all supported filters and sorting options.\n   */\n  query?: CursorQuery;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface QueryStaffMembersMultiLanguageResponse {\n  /** Retrieved staff members. */\n  staffMembers?: StaffMember[];\n  /** The retrieved staff members in the requested language according to the provided linguist aspect. */\n  translatedStaffMembers?: StaffMember[];\n  /** Paging metadata, including offset and count. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CountStaffMembersRequest {\n  /**\n   * Filter to base the count upon. See the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountStaffMembersResponse {\n  /** Total number of staff members matching the given filter. */\n  count?: number;\n}\n\nexport interface ConnectStaffMemberToUserRequest {\n  /**\n   * ID of the staff member to connect to the Wix user.\n   * @format GUID\n   */\n  staffMemberId: string;\n  /**\n   * Email of the Wix user to connect to the staff member.\n   *\n   * Default: Email of the staff member.\n   * @maxLength 320\n   * @format EMAIL\n   */\n  email?: string | null;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface ConnectStaffMemberToUserResponse {\n  /** Updated staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface SearchStaffMembersRequest {\n  /**\n   * Search criteria including filter, sort, and paging options.\n   *\n   * See the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)) for complete filter and sorting options.\n   */\n  search: CursorSearch;\n  /**\n   * Conditional fields to return in the response.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface CursorSearch extends CursorSearchPagingMethodOneOf {\n  /**\n   * Cursor-based paging for result navigation. Can't be used together with 'paging'.\n   * `cursor_paging.cursor` can't be used together with `filter` or `sort`.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object for narrowing search results. For example, to return only staff members with specific email domains: `\"filter\": {\"email\": {\"$contains\": \"@company.com\"}}`.\n   *\n   * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Array of sort objects specifying result order. For example, to sort by creation date in descending order: `\"sort\": [{\"fieldName\": \"createdDate\", \"order\": \"DESC\"}]`.\n   *\n   * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).\n   * @maxSize 10\n   */\n  sort?: Sorting[];\n  /** Free text to match in searchable fields. */\n  search?: SearchDetails;\n}\n\n/** @oneof */\nexport interface CursorSearchPagingMethodOneOf {\n  /**\n   * Cursor-based paging for result navigation. Can't be used together with 'paging'.\n   * `cursor_paging.cursor` can't be used together with `filter` or `sort`.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface SearchDetails {\n  /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */\n  mode?: ModeWithLiterals;\n  /**\n   * Search term or expression.\n   * @maxLength 100\n   */\n  expression?: string | null;\n  /**\n   * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `resource.workingHoursSchedules.shared`.\n   * @maxLength 200\n   * @maxSize 20\n   */\n  fields?: string[];\n  /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */\n  fuzzy?: boolean;\n}\n\nexport enum Mode {\n  /** At least 1 of the search terms must be present. */\n  OR = 'OR',\n  /** All search terms must be present. */\n  AND = 'AND',\n}\n\n/** @enumType */\nexport type ModeWithLiterals = Mode | 'OR' | 'AND';\n\nexport interface SearchStaffMembersResponse {\n  /**\n   * Retrieved staff members that match the search criteria specified in the request.\n   *\n   * Each staff member includes their profile information such as name, email, phone number,\n   * working hours schedule, connected user details, and any conditional fields requested\n   * (resource details, conferencing providers, or identity status).\n   */\n  staffMembers?: StaffMember[];\n  /**\n   * Cursor-based paging metadata for navigating through search results.\n   *\n   * Contains pagination details including current cursor position, availability of additional\n   * results, and item counts. Use the `next` cursor to retrieve subsequent result pages.\n   * Note: Staff members don't support aggregations, so only cursor and count information is provided.\n   */\n  pagingMetadata?: CursorPagingMetadata;\n  /**\n   * Aggregation data results (currently reserved for future use).\n   *\n   * Staff member searches don't currently support aggregations. This field is reserved\n   * for potential future aggregation capabilities such as grouping by working hours,\n   * location assignments, or user connection status.\n   */\n  aggregationData?: AggregationData;\n}\n\nexport interface AggregationData {\n  /**\n   * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.\n   * @maxSize 10000\n   */\n  results?: AggregationResults[];\n}\n\nexport interface ValueAggregationResult {\n  /**\n   * Value of the field.\n   * @maxLength 100\n   */\n  value?: string;\n  /** Count of entities with this value. */\n  count?: number;\n}\n\nexport interface RangeAggregationResult {\n  /** Inclusive lower bound of the range. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. */\n  to?: number | null;\n  /** Count of entities in this range. */\n  count?: number;\n}\n\nexport enum ScalarType {\n  UNKNOWN_SCALAR_TYPE = 'UNKNOWN_SCALAR_TYPE',\n  /** Total number of distinct values. */\n  COUNT_DISTINCT = 'COUNT_DISTINCT',\n  /** Minimum value. */\n  MIN = 'MIN',\n  /** Maximum value. */\n  MAX = 'MAX',\n}\n\n/** @enumType */\nexport type ScalarTypeWithLiterals =\n  | ScalarType\n  | 'UNKNOWN_SCALAR_TYPE'\n  | 'COUNT_DISTINCT'\n  | 'MIN'\n  | 'MAX';\n\nexport interface NestedAggregationResults\n  extends NestedAggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /**\n   * User-defined name of aggregation, matches the one specified in request.\n   * @maxLength 100\n   */\n  name?: string;\n  /** Type of aggregation that matches result. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Field to aggregate by, matches the one specified in request.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface NestedAggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n}\n\nexport enum AggregationType {\n  UNKNOWN_AGGREGATION_TYPE = 'UNKNOWN_AGGREGATION_TYPE',\n  /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */\n  VALUE = 'VALUE',\n  /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */\n  RANGE = 'RANGE',\n  /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */\n  SCALAR = 'SCALAR',\n}\n\n/** @enumType */\nexport type AggregationTypeWithLiterals =\n  | AggregationType\n  | 'UNKNOWN_AGGREGATION_TYPE'\n  | 'VALUE'\n  | 'RANGE'\n  | 'SCALAR';\n\nexport interface ValueResults {\n  /**\n   * Array of value aggregation results, each containing a field value and the count of entities with that value.\n   * @maxSize 250\n   */\n  results?: ValueAggregationResult[];\n}\n\nexport interface RangeResults {\n  /**\n   * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.\n   * @maxSize 50\n   */\n  results?: RangeAggregationResult[];\n}\n\nexport interface AggregationResultsScalarResult {\n  /** Type of scalar aggregation. */\n  type?: ScalarTypeWithLiterals;\n  /** Value of the scalar aggregation. */\n  value?: number;\n}\n\nexport interface NestedValueAggregationResult {\n  /**\n   * Value of the field.\n   * @maxLength 1000\n   */\n  value?: string;\n  /** Nested aggregations result data. */\n  nestedResults?: NestedAggregationResults;\n}\n\nexport interface ValueResult {\n  /**\n   * Value of the field.\n   * @maxLength 1000\n   */\n  value?: string;\n  /** Count of entities with this value. */\n  count?: number | null;\n}\n\nexport interface RangeResult {\n  /** Inclusive lower bound of the range. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. */\n  to?: number | null;\n  /** Count of entities in this range. */\n  count?: number | null;\n}\n\nexport interface ScalarResult {\n  /** Value of the scalar aggregation. */\n  value?: number;\n}\n\nexport interface NestedResultValue extends NestedResultValueResultOneOf {\n  /** Value aggregation result. */\n  value?: ValueResult;\n  /** Range aggregation result. */\n  range?: RangeResult;\n  /** Scalar aggregation result. */\n  scalar?: ScalarResult;\n  /** Date histogram aggregation result. */\n  dateHistogram?: ValueResult;\n}\n\n/** @oneof */\nexport interface NestedResultValueResultOneOf {\n  /** Value aggregation result. */\n  value?: ValueResult;\n  /** Range aggregation result. */\n  range?: RangeResult;\n  /** Scalar aggregation result. */\n  scalar?: ScalarResult;\n  /** Date histogram aggregation result. */\n  dateHistogram?: ValueResult;\n}\n\nexport interface Results {\n  /** Map of nested aggregation results, keyed by aggregation name. */\n  results?: Record<string, NestedResultValue>;\n}\n\nexport interface DateHistogramResult {\n  /**\n   * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * @maxLength 100\n   */\n  value?: string;\n  /** Count of documents in the bucket. */\n  count?: number;\n}\n\nexport interface GroupByValueResults {\n  /**\n   * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.\n   * @maxSize 1000\n   */\n  results?: NestedValueAggregationResult[];\n}\n\nexport interface DateHistogramResults {\n  /**\n   * Array of date histogram aggregation results, each containing a date bucket and its count.\n   * @maxSize 200\n   */\n  results?: DateHistogramResult[];\n}\n\n/**\n * Results of `NESTED` aggregation type in a flattened form.\n * Aggregations in resulting array are keyed by requested aggregation `name`.\n */\nexport interface NestedResults {\n  /**\n   * Array of nested aggregation result groups, each containing multiple aggregation results.\n   * @maxSize 1000\n   */\n  results?: Results[];\n}\n\nexport interface AggregationResults extends AggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /** Group by value aggregation results. */\n  groupedByValue?: GroupByValueResults;\n  /** Date histogram aggregation results. */\n  dateHistogram?: DateHistogramResults;\n  /** Nested aggregation results. */\n  nested?: NestedResults;\n  /**\n   * User-defined name of aggregation as derived from search request.\n   * @maxLength 100\n   */\n  name?: string;\n  /** Type of aggregation that must match specified kind as derived from search request. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Field to aggregate by as derived from search request.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface AggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /** Group by value aggregation results. */\n  groupedByValue?: GroupByValueResults;\n  /** Date histogram aggregation results. */\n  dateHistogram?: DateHistogramResults;\n  /** Nested aggregation results. */\n  nested?: NestedResults;\n}\n\nexport interface DisconnectStaffMemberFromUserRequest {\n  /**\n   * ID of the staff member to disconnect from its Wix user.\n   * @format GUID\n   */\n  staffMemberId: string;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface DisconnectStaffMemberFromUserResponse {\n  /** Updated staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface AssignWorkingHoursScheduleRequest {\n  /**\n   * ID of the staff member to assign the schedule to.\n   * @format GUID\n   */\n  staffMemberId: string;\n  /**\n   * ID of the *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to assign to the staff member.\n   *\n   * Must be either the staff member's event schedule ID or the working hour\n   * schedule ID for a business location.\n   * @format GUID\n   */\n  scheduleId: string;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface AssignWorkingHoursScheduleResponse {\n  /** Updated staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface AssignCustomScheduleRequest {\n  /**\n   * ID of the staff member for which to assign a working hour schedule.\n   * @format GUID\n   */\n  staffMemberId: string;\n  /**\n   * ID of the *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to assign to the staff member.\n   * @format GUID\n   */\n  scheduleId: string;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface AssignCustomScheduleResponse {\n  /** Updated staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface BulkUpdateStaffMemberTagsRequest {\n  /**\n   * IDs of staff members to update tags for.\n   * @minSize 1\n   * @maxSize 100\n   * @format GUID\n   */\n  ids: string[];\n  /** Tags to add to the staff members. */\n  assignTags?: Tags;\n  /** Tags to remove from the staff members. */\n  unassignTags?: Tags;\n}\n\nexport interface BulkUpdateStaffMemberTagsResponse {\n  /**\n   * List of update results.\n   * @minSize 1\n   * @maxSize 100\n   */\n  results?: BulkUpdateStaffMemberTagsResult[];\n  /** Bulk action metadata. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface ItemMetadata {\n  /**\n   * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Index of the item within the request array. Allows for correlation between request and response items. */\n  originalIndex?: number;\n  /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */\n  success?: boolean;\n  /** Details about the error in case of failure. */\n  error?: ApplicationError;\n}\n\nexport interface ApplicationError {\n  /** Error code. */\n  code?: string;\n  /** Description of the error. */\n  description?: string;\n  /** Data related to the error. */\n  data?: Record<string, any> | null;\n}\n\nexport interface BulkUpdateStaffMemberTagsResult {\n  /** Metadata about an individual update operation. */\n  itemMetadata?: ItemMetadata;\n}\n\nexport interface BulkActionMetadata {\n  /** Number of items that were successfully processed. */\n  totalSuccesses?: number;\n  /** Number of items that couldn't be processed. */\n  totalFailures?: number;\n  /** Number of failures without details because detailed failure threshold was exceeded. */\n  undetailedFailures?: number;\n}\n\nexport interface BulkUpdateStaffMemberTagsByFilterRequest {\n  /**\n   * Filter to base the update upon. See the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  filter: Record<string, any> | null;\n  /** Tags to add to the staff members. */\n  assignTags?: Tags;\n  /** Tags to remove from the staff members. */\n  unassignTags?: Tags;\n}\n\nexport interface BulkUpdateStaffMemberTagsByFilterResponse {\n  /**\n   * Job ID for the bulk update operation.\n   * @format GUID\n   */\n  jobId?: string;\n}\n\nexport interface RestoreStaffRequest {\n  /**\n   * ID of the staff member to restore.\n   * @format GUID\n   */\n  staffMemberId?: string;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface RestoreStaffResponse {\n  /** Restored staff member. */\n  staffMember?: StaffMember;\n}\n\nexport interface Empty {}\n\nexport interface PolicyRemovedFromContributor {\n  /** @format GUID */\n  accountId?: string;\n  /** @format GUID */\n  metaSiteId?: string;\n  policyIds?: string[];\n}\n\nexport interface PolicyUpdatedForContributor {\n  /** @format GUID */\n  accountId?: string;\n  /** @format GUID */\n  metaSiteId?: string;\n  oldPolicyIds?: string[];\n  newPolicyIds?: string[];\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface ScheduleNotification extends ScheduleNotificationEventOneOf {\n  scheduleCreated?: ScheduleCreated;\n  scheduleUpdated?: ScheduleUpdated;\n  scheduleCancelled?: ScheduleCancelled;\n  sessionCreated?: SessionCreated;\n  sessionUpdated?: SessionUpdated;\n  sessionCancelled?: SessionCancelled;\n  availabilityPolicyUpdated?: AvailabilityPolicyUpdated;\n  /** @deprecated */\n  intervalSplit?: IntervalSplit;\n  recurringSessionSplit?: RecurringSessionSplit;\n  /**\n   * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.\n   * @deprecated\n   */\n  scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;\n  preserveFutureSessionsWithParticipants?: boolean | null;\n  /**\n   * Whether to notify participants about changed sessions. deprecated, use participant_notification\n   * @deprecated\n   */\n  notifyParticipants?: boolean;\n  /** site properties. Optional. Given in create schedule notification. */\n  siteProperties?: SitePropertiesOnScheduleCreation;\n  instanceId?: string;\n}\n\n/** @oneof */\nexport interface ScheduleNotificationEventOneOf {\n  scheduleCreated?: ScheduleCreated;\n  scheduleUpdated?: ScheduleUpdated;\n  scheduleCancelled?: ScheduleCancelled;\n  sessionCreated?: SessionCreated;\n  sessionUpdated?: SessionUpdated;\n  sessionCancelled?: SessionCancelled;\n  availabilityPolicyUpdated?: AvailabilityPolicyUpdated;\n  /** @deprecated */\n  intervalSplit?: IntervalSplit;\n  recurringSessionSplit?: RecurringSessionSplit;\n  /**\n   * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.\n   * @deprecated\n   */\n  scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;\n}\n\nexport interface ScheduleCreated {\n  schedule?: Schedule;\n}\n\nexport interface Schedule {\n  /** Schedule ID. */\n  _id?: string;\n  /** ID of the schedule's owner entity. This may be a resource ID or a service ID. */\n  scheduleOwnerId?: string | null;\n  /**\n   * Schedule's time zone in [Area/Location](https://en.wikipedia.org/wiki/Tz_database) format. Read-only.\n   * Derived from the Wix Business time zone.\n   * @readonly\n   */\n  timeZone?: string | null;\n  /**\n   * Deprecated. Please use the [Sessions API](https://dev.wix.com/api/rest/wix-bookings/schedules-and-sessions/session) instead.\n   * @deprecated\n   */\n  intervals?: RecurringInterval[];\n  /**\n   * Default title for the schedule's sessions. Maximum length: 6000 characters.\n   * @maxLength 6000\n   */\n  title?: string | null;\n  /**\n   * __Deprecated.__\n   * Tags for grouping schedules. These tags are the default tags for the schedule's sessions.\n   * The Wix Bookings app uses the following predefined tags to set schedule type: `\"INDIVIDUAL\"`, `\"GROUP\"`, and `\"COURSE\"`. Once the schedule type is set using these tags, you cannot update it. In addition to the app's tags, you can create and update your own tags.\n   * @deprecated\n   */\n  tags?: string[] | null;\n  /** Default location for the schedule's sessions. */\n  location?: Location;\n  /**\n   * Maximum number of participants that can be added to the schedule's sessions.\n   * Must be at most `1` for schedule whose availability is affected by another schedule. E.g, appointment schedules of the Wix Bookings app.\n   * @min 1\n   * @max 1000\n   */\n  capacity?: number | null;\n  /**\n   * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.\n   * @deprecated\n   */\n  rate?: Rate;\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  availability?: Availability;\n  /**\n   * Number of participants registered to sessions in this schedule, calculated as the sum of the party sizes.\n   * @readonly\n   */\n  totalNumberOfParticipants?: number;\n  /**\n   * *Partial list** of participants which are registered to sessions in this schedule.\n   * Participants who are registered in the schedule are automatically registered to any session that is created for the schedule.\n   * To retrieve the full list of schedule participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).\n   * @readonly\n   */\n  participants?: Participant[];\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  externalCalendarOverrides?: ExternalCalendarOverrides;\n  /**\n   * Schedule status. Default: Created\n   * @readonly\n   */\n  status?: ScheduleStatusWithLiterals;\n  /**\n   * Schedule creation date.\n   * @readonly\n   */\n  created?: Date | null;\n  /**\n   * Schedule last update date.\n   * @readonly\n   */\n  updated?: Date | null;\n  /**\n   * Schedule version number, updated each time the schedule is updated.\n   * @readonly\n   */\n  version?: number;\n  /**\n   * Fields which were inherited from the Business Info page under Settings in the Dashboard.\n   * @readonly\n   */\n  inheritedFields?: string[];\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  conferenceProvider?: ConferenceProvider;\n  /**\n   * A conference created for the schedule. This is used when a participant is added to a schedule.\n   * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.\n   * @deprecated\n   */\n  calendarConference?: CalendarConference;\n}\n\nexport interface RecurringInterval {\n  /**\n   * The recurring interval identifier.\n   * @readonly\n   */\n  _id?: string;\n  /** The start time of the recurring interval. Required. */\n  start?: Date | null;\n  /** The end time of the recurring interval. Optional. Empty value indicates that there is no end time. */\n  end?: Date | null;\n  /** The interval rules. The day, hour and minutes the interval is recurring. */\n  interval?: Interval;\n  /** The frequency of the interval. Optional. The default is frequency with the default repetition. */\n  frequency?: Frequency;\n  /** Specifies the list of linked schedules and the way this link affects the corresponding schedules' availability. Can be calculated from the schedule or overridden on the recurring interval. */\n  affectedSchedules?: LinkedSchedule[];\n  /** The type of recurring interval. */\n  intervalType?: RecurringIntervalTypeWithLiterals;\n}\n\nexport interface Interval {\n  /** The day the interval occurs. Optional. The default is the day of the recurring interval's start time. */\n  daysOfWeek?: DayWithLiterals;\n  /**\n   * The hour of the day the interval occurs. Must be consistent with the interval start time. Optional. The default is 0. Minimum: 0, maximum: 23.\n   * @max 23\n   */\n  hourOfDay?: number | null;\n  /**\n   * The minutes of the hour the interval accrues. Must be consistent with the interval end time. Optional. The default is 0. Minimum: 0, maximum: 59.\n   * @max 59\n   */\n  minuteOfHour?: number | null;\n  /** The duration of the interval in minutes. Required. Part of the session end time calculation. */\n  duration?: number;\n}\n\nexport enum Day {\n  /** Undefined. */\n  UNDEFINED = 'UNDEFINED',\n  /** Monday. */\n  MON = 'MON',\n  /** Tuesday. */\n  TUE = 'TUE',\n  /** Wednesday. */\n  WED = 'WED',\n  /** Thursday. */\n  THU = 'THU',\n  /** Friday. */\n  FRI = 'FRI',\n  /** Saturday. */\n  SAT = 'SAT',\n  /** Sunday. */\n  SUN = 'SUN',\n}\n\n/** @enumType */\nexport type DayWithLiterals =\n  | Day\n  | 'UNDEFINED'\n  | 'MON'\n  | 'TUE'\n  | 'WED'\n  | 'THU'\n  | 'FRI'\n  | 'SAT'\n  | 'SUN';\n\nexport interface Frequency {\n  /**\n   * The frequency of the recurrence in weeks. i.e. when this value is 4, the interval occurs every 4 weeks. Optional. The default is 1. minimum: 1, maximum: 52.\n   * @min 1\n   * @max 52\n   */\n  repetition?: number | null;\n}\n\nexport interface LinkedSchedule {\n  /**\n   * Schedule ID.\n   * @format GUID\n   */\n  scheduleId?: string;\n  /** Sets this schedule's availability for the duration of the linked schedule's sessions. Default is `\"BUSY\"`. */\n  transparency?: TransparencyWithLiterals;\n  /**\n   * Owner ID, of the linked schedule.\n   * @readonly\n   */\n  scheduleOwnerId?: string;\n}\n\nexport enum Transparency {\n  UNDEFINED = 'UNDEFINED',\n  /** The schedule can have available slots during the linked schedule's sessions. */\n  FREE = 'FREE',\n  /** The schedule can't have available slots during the linked schedule's sessions. */\n  BUSY = 'BUSY',\n}\n\n/** @enumType */\nexport type TransparencyWithLiterals =\n  | Transparency\n  | 'UNDEFINED'\n  | 'FREE'\n  | 'BUSY';\n\nexport enum RecurringIntervalType {\n  /** The default value. Sessions for this interval will be of type EVENT. */\n  UNDEFINED = 'UNDEFINED',\n  /** A recurring interval of events. */\n  EVENT = 'EVENT',\n  /** Deprecated. */\n  TIME_AVAILABILITY = 'TIME_AVAILABILITY',\n  /** A recurring interval for availability. */\n  AVAILABILITY = 'AVAILABILITY',\n}\n\n/** @enumType */\nexport type RecurringIntervalTypeWithLiterals =\n  | RecurringIntervalType\n  | 'UNDEFINED'\n  | 'EVENT'\n  | 'TIME_AVAILABILITY'\n  | 'AVAILABILITY';\n\nexport interface Location {\n  /**\n   * Location type.\n   * One of:\n   * - `\"OWNER_BUSINESS\"` The business address as set in the site’s general settings.\n   * - `\"OWNER_CUSTOM\"` The address as set when creating the service.\n   * - `\"CUSTOM\"` The address set for the individual session.\n   */\n  locationType?: LocationTypeWithLiterals;\n  /**\n   * Free text address used when locationType is `OWNER_CUSTOM`.\n   * @deprecated\n   */\n  address?: string | null;\n  /** Custom address, used when locationType is `\"OWNER_CUSTOM\"`. Might be used when locationType is `\"CUSTOM\"` in case the owner sets a custom address for the session which is different from the default. */\n  customAddress?: Address;\n}\n\nexport enum LocationType {\n  UNDEFINED = 'UNDEFINED',\n  OWNER_BUSINESS = 'OWNER_BUSINESS',\n  OWNER_CUSTOM = 'OWNER_CUSTOM',\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNDEFINED'\n  | 'OWNER_BUSINESS'\n  | 'OWNER_CUSTOM'\n  | 'CUSTOM';\n\n/** Physical address */\nexport interface Address extends AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n  /**\n   * Country code.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  subdivision?: string | null;\n  /** City name. */\n  city?: string | null;\n  /** Zip/postal code. */\n  postalCode?: string | null;\n  /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */\n  addressLine2?: string | null;\n  /** A string containing the full address of this location. */\n  formattedAddress?: string | null;\n  /** Free text to help find the address. */\n  hint?: string | null;\n  /** Coordinates of the physical address. */\n  geocode?: AddressLocation;\n  /** Country full name. */\n  countryFullname?: string | null;\n  /** Multi-level subdivisions from top to bottom. */\n  subdivisions?: Subdivision[];\n}\n\n/** @oneof */\nexport interface AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n}\n\nexport interface StreetAddress {\n  /** Street number. */\n  number?: string;\n  /** Street name. */\n  name?: string;\n  /** Apartment number. */\n  apt?: string;\n}\n\nexport interface AddressLocation {\n  /** Address latitude. */\n  latitude?: number | null;\n  /** Address longitude. */\n  longitude?: number | null;\n}\n\nexport interface Subdivision {\n  /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  code?: string;\n  /** Subdivision full name. */\n  name?: string;\n}\n\nexport interface LocationsLocation {\n  /**\n   * Location ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Location name.\n   * @maxLength 150\n   */\n  name?: string;\n  /**\n   * Location description.\n   * @maxLength 500\n   */\n  description?: string | null;\n  /**\n   * Whether this is the default location. There can only be one default location per site. The default location can't be archived.\n   * @readonly\n   */\n  default?: boolean;\n  /**\n   * Location status. Defaults to `ACTIVE`.\n   * __Notes:__\n   * - [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)\n   * doesn't affect the location's status.\n   * - `INACTIVE` status is currently not supported.\n   */\n  status?: LocationStatusWithLiterals;\n  /**\n   * Location type.\n   *\n   * **Note:** Currently not supported.\n   * @deprecated\n   */\n  locationType?: LocationsLocationTypeWithLiterals;\n  /** Fax number. */\n  fax?: string | null;\n  /** Timezone in `America/New_York` format. */\n  timeZone?: string | null;\n  /** Email address. */\n  email?: string | null;\n  /** Phone number. */\n  phone?: string | null;\n  /** Address. */\n  address?: LocationsAddress;\n  /**\n   * Business schedule. Array of weekly recurring time periods when the location is open for business. Limited to 100 time periods.\n   *\n   * __Note:__ Not supported by Wix Bookings.\n   */\n  businessSchedule?: BusinessSchedule;\n  /**\n   * Revision number, which increments by 1 each time the location is updated.\n   * To prevent conflicting changes, the existing revision must be used when updating a location.\n   */\n  revision?: string | null;\n  /**\n   * Whether the location is archived. Archived locations can't be updated.\n   * __Note:__ [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)\n   * doesn't affect its `status`.\n   * @readonly\n   */\n  archived?: boolean;\n  /**\n   * Location types.\n   * @maxSize 10\n   */\n  locationTypes?: LocationsLocationTypeWithLiterals[];\n}\n\n/** For future use */\nexport enum LocationStatus {\n  ACTIVE = 'ACTIVE',\n  INACTIVE = 'INACTIVE',\n}\n\n/** @enumType */\nexport type LocationStatusWithLiterals = LocationStatus | 'ACTIVE' | 'INACTIVE';\n\n/** For future use */\nexport enum LocationsLocationType {\n  UNKNOWN = 'UNKNOWN',\n  BRANCH = 'BRANCH',\n  OFFICES = 'OFFICES',\n  RECEPTION = 'RECEPTION',\n  HEADQUARTERS = 'HEADQUARTERS',\n  INVENTORY = 'INVENTORY',\n}\n\n/** @enumType */\nexport type LocationsLocationTypeWithLiterals =\n  | LocationsLocationType\n  | 'UNKNOWN'\n  | 'BRANCH'\n  | 'OFFICES'\n  | 'RECEPTION'\n  | 'HEADQUARTERS'\n  | 'INVENTORY';\n\nexport interface LocationsAddress {\n  /**\n   * 2-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /** Code for a subdivision (such as state, prefecture, or province) in [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) format. */\n  subdivision?: string | null;\n  /** City name. */\n  city?: string | null;\n  /**\n   * Postal or zip code.\n   * @maxLength 20\n   */\n  postalCode?: string | null;\n  /** Street address. Includes street name, number, and apartment number in separate fields. */\n  streetAddress?: LocationsStreetAddress;\n  /** Full address of the location. */\n  formatted?: string | null;\n  /** Geographic coordinates of location. */\n  location?: LocationsAddressLocation;\n}\n\n/** Street address. Includes street name, number, and apartment number in separate fields. */\nexport interface LocationsStreetAddress {\n  /** Street number. */\n  number?: string;\n  /** Street name. */\n  name?: string;\n  /** Apartment number. */\n  apt?: string;\n}\n\n/** Address Geolocation */\nexport interface LocationsAddressLocation {\n  /** Latitude of the location. Must be between -90 and 90. */\n  latitude?: number | null;\n  /** Longitude of the location. Must be between -180 and 180. */\n  longitude?: number | null;\n}\n\n/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */\nexport interface BusinessSchedule {\n  /**\n   * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.\n   * @maxSize 100\n   */\n  periods?: TimePeriod[];\n  /**\n   * Exceptions to the business's regular hours. The business can be open or closed during the exception.\n   * @maxSize 100\n   */\n  specialHourPeriod?: SpecialHourPeriod[];\n}\n\n/** Weekly recurring time periods when the business is regularly open or the service is available. */\nexport interface TimePeriod {\n  /** Day of the week the period starts on. */\n  openDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   */\n  openTime?: string;\n  /** Day of the week the period ends on. */\n  closeDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   *\n   * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.\n   */\n  closeTime?: string;\n}\n\n/** Enumerates the days of the week. */\nexport enum DayOfWeek {\n  MONDAY = 'MONDAY',\n  TUESDAY = 'TUESDAY',\n  WEDNESDAY = 'WEDNESDAY',\n  THURSDAY = 'THURSDAY',\n  FRIDAY = 'FRIDAY',\n  SATURDAY = 'SATURDAY',\n  SUNDAY = 'SUNDAY',\n}\n\n/** @enumType */\nexport type DayOfWeekWithLiterals =\n  | DayOfWeek\n  | 'MONDAY'\n  | 'TUESDAY'\n  | 'WEDNESDAY'\n  | 'THURSDAY'\n  | 'FRIDAY'\n  | 'SATURDAY'\n  | 'SUNDAY';\n\n/** Exception to the business's regular hours. The business can be open or closed during the exception. */\nexport interface SpecialHourPeriod {\n  /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  startDate?: string;\n  /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  endDate?: string;\n  /**\n   * Whether the business is closed (or the service is not available) during the exception.\n   *\n   * Default: `true`.\n   */\n  isClosed?: boolean;\n  /** Additional info about the exception. For example, \"We close earlier on New Year's Eve.\" */\n  comment?: string;\n}\n\nexport interface Rate {\n  /**\n   * Mapping between a named price option, for example, adult or child prices, and the price, currency, and down payment amount.\n   * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.\n   */\n  labeledPriceOptions?: Record<string, Price>;\n  /**\n   * Textual price information used when **Price Per Session** is set to **Custom Price** in the app's service details page.\n   * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.\n   */\n  priceText?: string | null;\n}\n\nexport interface Price {\n  /**\n   * Required payment amount.\n   * @format DECIMAL_VALUE\n   */\n  amount?: string;\n  /**\n   * Currency in which the amount is quoted.\n   * @format CURRENCY\n   */\n  currency?: string;\n  /**\n   * Amount of a down payment or deposit as part of the transaction.\n   * @format DECIMAL_VALUE\n   */\n  downPayAmount?: string;\n}\n\n/**\n * <!-- Needs updating when recurrence has been tested\n * Schedule's availability calculation is executed by the schedule's available intervals and this additional information.\n * Schedule's available intervals are recurring intervals (defined in the schedule) minus sessions that has no more spots for bookings (including time between_slots), or schedule's sessions with open spots for bookings.-->\n */\nexport interface Availability {\n  /** Date and time the schedule starts to be available for booking. */\n  start?: Date | null;\n  /** Date and time the schedule stops being available for booking. No value indicates no end time. */\n  end?: Date | null;\n  /** Other schedules that impact the availability calculation. Relevant only when there are availability constraints. */\n  linkedSchedules?: LinkedSchedule[];\n  /** Constraints for calculating the schedule's availability. */\n  constraints?: AvailabilityConstraints;\n}\n\n/** Describes how to calculate the specific slots that are available for booking. */\nexport interface AvailabilityConstraints {\n  /**\n   * A list of duration options for slots, in minutes. Minimum value for a duration is 1.\n   * The availability calculation generates slots with these durations, where there is no conflict with existing sessions or other availability constraints.\n   * @min 1\n   */\n  slotDurations?: number[];\n  /**\n   * The number of minutes between the `end` of one slot, and the `start` of the next.\n   * Minimum value is 0, maximum value is 120.\n   * @max 720\n   */\n  timeBetweenSlots?: number;\n  /**\n   * Specify how to split the slots in intervals of minutes.\n   * This value indicates the time between available slots' start time. e.g., from 5 minute slots (3:00, 3:05, 3:15) and 1 hour slots (3:00, 4:00, 5:00).\n   * Optional. The default is the first duration in slot_durations field.\n   * Deprecated. Use the `split_slots_interval.value_in_minutes`.\n   * @deprecated\n   */\n  splitInterval?: number | null;\n  /**\n   * An object defining the time between available slots' start times.  For example, a slot with slots_split_interval=5 can start every 5 minutes. The default is the slot duration.\n   * @readonly\n   */\n  slotsSplitInterval?: SplitInterval;\n}\n\n/** The time between available slots' start times. For example, For 5 minute slots, 3:00, 3:05, 3:15 etc. For 1 hour slots, 3:00, 4:00, 5:00 etc. */\nexport interface SplitInterval {\n  /**\n   * Whether the slot duration is used as the split interval value.\n   * If `same_as_duration` is `true`, the `value_in_minutes` is the sum of the first duration in\n   * `schedule.availabilityConstraints.SlotDurations` field, and `schedule.availabilityConstraints.TimeBetweenSlots` field.\n   */\n  sameAsDuration?: boolean | null;\n  /** Number of minutes between available slots' start times when `same_as_duration` is `false`. */\n  valueInMinutes?: number | null;\n}\n\nexport interface Participant {\n  /**\n   * Participant ID. Currently represents the booking.id.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Contact ID.\n   * @format GUID\n   */\n  contactId?: string | null;\n  /** Participant's name. */\n  name?: string | null;\n  /** Participant's phone number. */\n  phone?: string | null;\n  /** Participant's email address. */\n  email?: string | null;\n  /** Group or party size. The number of people attending. Defaults to 0. Maximum is 250. */\n  partySize?: number;\n  /**\n   * Approval status for the participant.\n   * <!-- Commented out untill updateParticipant is exposed Generally the same status as the booking, unless updated using the `updateParticipant()` API. Defaults to `\"UNDEFINED\"`.-->\n   */\n  approvalStatus?: ApprovalStatusWithLiterals;\n  /**\n   * Whether the participant was inherited from the schedule, as opposed to being booked directly to the session.\n   * @readonly\n   */\n  inherited?: boolean;\n}\n\nexport enum ApprovalStatus {\n  /** Default. */\n  UNDEFINED = 'UNDEFINED',\n  /** Pending business approval. */\n  PENDING = 'PENDING',\n  /** Approved by the business. */\n  APPROVED = 'APPROVED',\n  /** Declined by the business. */\n  DECLINED = 'DECLINED',\n}\n\n/** @enumType */\nexport type ApprovalStatusWithLiterals =\n  | ApprovalStatus\n  | 'UNDEFINED'\n  | 'PENDING'\n  | 'APPROVED'\n  | 'DECLINED';\n\nexport interface ExternalCalendarOverrides {\n  /** Synced title of the external calendar event. */\n  title?: string | null;\n  /** Synced description of the external calendar event. */\n  description?: string | null;\n}\n\nexport enum ScheduleStatus {\n  /** Undefined schedule status. */\n  UNDEFINED = 'UNDEFINED',\n  /** The schedule was created. */\n  CREATED = 'CREATED',\n  /** The schedule was cancelled. */\n  CANCELLED = 'CANCELLED',\n}\n\n/** @enumType */\nexport type ScheduleStatusWithLiterals =\n  | ScheduleStatus\n  | 'UNDEFINED'\n  | 'CREATED'\n  | 'CANCELLED';\n\nexport interface Version {\n  /** Schedule version number, updated each time the schedule is updated. */\n  scheduleVersion?: number | null;\n  /** Participants version number, updated each time the schedule participants are updated. */\n  participantsVersion?: number | null;\n}\n\nexport interface ConferenceProvider {\n  /** Conferencing provider ID */\n  providerId?: string;\n}\n\nexport interface CalendarConference {\n  /** Wix Calendar conference ID. */\n  _id?: string;\n  /** Conference meeting ID in the provider's conferencing system. */\n  externalId?: string;\n  /** Conference provider ID. */\n  providerId?: string;\n  /** URL used by the host to start the conference. */\n  hostUrl?: string;\n  /** URL used by a guest to join the conference. */\n  guestUrl?: string;\n  /** Password to join the conference. */\n  password?: string | null;\n  /** Conference description. */\n  description?: string | null;\n  /** Conference type. */\n  conferenceType?: ConferenceTypeWithLiterals;\n  /** ID of the account owner in the video conferencing service. */\n  accountOwnerId?: string | null;\n}\n\nexport enum ConferenceType {\n  /** Undefined conference type. */\n  UNDEFINED = 'UNDEFINED',\n  /** API-generated online meeting. */\n  ONLINE_MEETING_PROVIDER = 'ONLINE_MEETING_PROVIDER',\n  /** User-defined meeting. */\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type ConferenceTypeWithLiterals =\n  | ConferenceType\n  | 'UNDEFINED'\n  | 'ONLINE_MEETING_PROVIDER'\n  | 'CUSTOM';\n\nexport interface ScheduleUpdated {\n  /** The old schedule before the update. */\n  oldSchedule?: Schedule;\n  /** The new schedule after the update. */\n  newSchedule?: Schedule;\n  /**\n   * Recurring sessions updated event. If this field is given, the reason for the schedule updated event was\n   * updating at least one of the given schedule's recurring sessions.\n   * This event is triggered by create/update/delete recurring session apis.\n   */\n  recurringSessions?: RecurringSessionsUpdated;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether this notification was created as a result of an anonymization request, such as GDPR.\n   * An anonymized participant will have the following details:\n   * name = \"deleted\"\n   * phone = \"deleted\"\n   * email = \"<EMAIL>\"\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n}\n\nexport interface RecurringSessionsUpdated {\n  /** Old schedule's recurring session list. */\n  oldRecurringSessions?: Session[];\n  /** New schedule's recurring session list. */\n  newRecurringSessions?: Session[];\n}\n\nexport interface Session {\n  /**\n   * Session ID.\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * ID of the schedule that the session belongs to.\n   * @immutable\n   */\n  scheduleId?: string;\n  /**\n   * ID of the resource or service that the session's schedule belongs to.\n   * @readonly\n   */\n  scheduleOwnerId?: string | null;\n  /** Original start date and time of the session in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. */\n  originalStart?: Date | null;\n  /** An object specifying the start date and time of the session. If the session is a recurring session, `start` must contain a `localDateTime`. */\n  start?: CalendarDateTime;\n  /**\n   * An object specifying the end date and time of the session. The `end` time must be after the `start` time and be same type as `start`.\n   * If the session is a recurring session, `end` must contain a `localDateTime`.\n   */\n  end?: CalendarDateTime;\n  /**\n   * An object specifying a list of schedules and the way each schedule's availability is affected by the session. For example, the schedule of an instructor is affected by sessions of the class that they instruct.\n   * The array is inherited from the schedule and can be overridden even if the session is a recurring session.\n   */\n  affectedSchedules?: LinkedSchedule[];\n  /**\n   * Session title.\n   * The value is inherited from the schedule and can be overridden unless the session is a recurring session.\n   * @maxLength 6000\n   */\n  title?: string | null;\n  /**\n   * __Deprecated.__\n   * Tags for the session.\n   * The value is inherited from the schedule and can be overridden unless the session is a recurring session.\n   * @maxLength 200\n   * @deprecated\n   */\n  tags?: string[] | null;\n  /**\n   * An object describing the location where the session takes place.\n   * Defaults to the schedule location.\n   * For single sessions, `session.location.businessLocation` can only be provided for locations that are defined in the schedule using `schedule.location` or `schedule.availability.locations`.\n   */\n  location?: Location;\n  /**\n   * Maximum number of participants that can be added to the session. Defaults to the schedule capacity.\n   * The value is inherited from the schedule and can be overridden unless the session is a recurring session.\n   * @max 1000\n   */\n  capacity?: number | null;\n  /**\n   * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.\n   * @deprecated\n   */\n  rate?: Rate;\n  /**\n   * Time reserved after the session end time, derived from the schedule availability constraints and the time between slots. Read-only.\n   * If the session is a recurring session, this field must be empty.\n   */\n  timeReservedAfter?: number | null;\n  /**\n   * Additional information about the session.\n   * Notes are not supported for recurring sessions.\n   * @maxLength 10000\n   */\n  notes?: string;\n  /**\n   * The number of participants booked for the session. Read-only.\n   * Calculated as the sum of the party sizes.\n   * @readonly\n   */\n  totalNumberOfParticipants?: number;\n  /**\n   * *Partial list** list of participants booked for the session.\n   * The list includes participants who have registered for this specific session, and participants who have registered for a schedule that includes this session.\n   * If the session is a recurring session, this field must be empty.\n   * To retrieve the full list of session participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).\n   */\n  participants?: Participant[];\n  /**\n   * A list of properties for which values were inherited from the schedule.\n   * This does not include participants that were inherited from the schedule.\n   * @readonly\n   */\n  inheritedFields?: string[];\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  externalCalendarOverrides?: ExternalCalendarOverrides;\n  /**\n   * Session status.\n   * @readonly\n   */\n  status?: StatusWithLiterals;\n  /**\n   * Recurring interval ID. Defined when a session will be a recurring session. read-only. Optional.\n   * For example, when creating a class service  with recurring sessions, you add a recurrence rule to create recurring sessions.\n   * This field is omitted for single sessions or instances of recurring sessions.\n   * Specified when the session was originally generated from a schedule recurring interval.\n   * Deprecated. Use `recurringSessionId`.\n   * @readonly\n   * @deprecated\n   */\n  recurringIntervalId?: string | null;\n  /**\n   * The ID of the recurring session if this session is an instance of a recurrence. Use this ID to update the recurrence and all of the instances.\n   * @readonly\n   */\n  recurringSessionId?: string | null;\n  /** Session type. */\n  type?: SessionTypeWithLiterals;\n  /**\n   * A conference created for the session according to the details set in the schedule's conference provider information.\n   * If the session is a recurring session, this field is inherited from the schedule.\n   * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.\n   * @deprecated\n   */\n  calendarConference?: CalendarConference;\n  /**\n   * A string representing a recurrence rule (RRULE) for a recurring session, as defined in [iCalendar RFC 5545](https://icalendar.org/iCalendar-RFC-5545/3-3-10-recurrence-rule.html).\n   * If the session is an instance of a recurrence pattern, the `instanceOfRecurrence` property will be contain the recurrence rule and this property will be empty.\n   * The RRULE defines a rule for repeating a session.\n   * Supported parameters are:\n   *\n   * |Keyword|Description|Supported values|\n   * |--|--|---|\n   * |`FREQ`|The frequency at which the session is recurs. Required.|`WEEKLY`|\n   * |`INTERVAL`|How often, in terms of `FREQ`, the session recurs. Default is 1. Optional.|\n   * |`UNTIL`|The UTC end date and time of the recurrence. Optional.|\n   * |`BYDAY`|Day of the week when the event should recur. Required.|One of: `MO`, `TU`, `WE`, `TH`, `FR`, `SA`, `SU`|\n   *\n   *\n   * For example, a session that repeats every second week on a Monday until January 7, 2022 at 8 AM:\n   * `\"FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20220107T080000Z\"`\n   *\n   * <!--ORIGINAL COMMENTS:\n   * `FREQ` — The frequency with which the session should be repeated (such as DAILY or WEEKLY).\n   * Supported `WEEKLY` value is supported.\n   * INTERVAL — Works together with FREQ to specify how often the session should be repeated. For example, FREQ=WEEKLY;INTERVAL=2 means once every two weeks. Optional. Default value is 1.\n   * COUNT — The number of times this event should be repeated. Not yet supported.\n   * UNTIL — The UTC date & time until which the session should be repeated. This parameter is optional. When it is not specified, the event repeats forever.\n   * The format is a short ISO date, followed by 'T' and a short time with seconds and without milliseconds, terminated by the UTC designator 'Z'. For example, until Jan. 19th 2018 at 7:00 AM: 'UNTIL=20180119T070000Z'.\n   * BYDAY - The days of the week when the event should be repeated. Currently, only a single day is supported. This parameter is mandatory.\n   * Possible values are: MO, TU, WE, TH, FR, SA, SU\n   * Note that DTSTART and DTEND lines are not allowed in this field; session start and end times are specified in the start and end fields.\n   * **Example**: FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20200427T070000Z\n   * ORIGINAL COMMENTS-->\n   */\n  recurrence?: string | null;\n  /**\n   * A string representing a recurrence rule (RRULE) if the session is an instance of a recurrence pattern.\n   * Empty when the session is not an instance of a recurrence rule, or if the session defines a recurrence pattern, and `recurrence` is not empty.\n   * @readonly\n   */\n  instanceOfRecurrence?: string | null;\n  /**\n   * The session version.\n   * Composed by the schedule, session and participants versions.\n   * @readonly\n   */\n  version?: SessionVersion;\n}\n\nexport interface CalendarDateTime {\n  /**\n   * UTC date-time in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. If a time zone offset is specified, the time is converted to UTC. For example, if you specify  `new Date('2021-01-06T16:00:00.000-07:00')`, the stored value will be `\"2021-01-06T23:00:00.000Z\"`.\n   * Required if `localDateTime` is not specified.\n   * If `localDateTime` is specified, `timestamp` is calculated as `localDateTime`, using the business's time zone.\n   */\n  timestamp?: Date | null;\n  /** An object containing the local date and time for the business's time zone. */\n  localDateTime?: LocalDateTime;\n  /**\n   * The time zone. Optional. Derived from the schedule's time zone.\n   * In case this field is associated with recurring session, this field is empty.\n   * @readonly\n   */\n  timeZone?: string | null;\n}\n\nexport interface LocalDateTime {\n  /** Year. 4-digit format. */\n  year?: number | null;\n  /**\n   * Month number, from 1-12.\n   * @min 1\n   * @max 12\n   */\n  monthOfYear?: number | null;\n  /** Day of the month, from 1-31. */\n  dayOfMonth?: number | null;\n  /**\n   * Hour of the day in 24-hour format, from 0-23.\n   * @max 23\n   */\n  hourOfDay?: number | null;\n  /**\n   * Minute, from 0-59.\n   * @max 59\n   */\n  minutesOfHour?: number | null;\n}\n\nexport interface ExternalCalendarInfo {\n  /** The external calendar type (e.g. Google Calendar, iCal, etc). */\n  calendarType?: CalendarTypeWithLiterals;\n}\n\nexport enum CalendarType {\n  /** There is no information about the external calendar type. */\n  UNDEFINED = 'UNDEFINED',\n  /** [Google Calendar](https://developers.google.com/calendar/api/guides/overview). */\n  GOOGLE = 'GOOGLE',\n  /** Apple iCalendar. */\n  I_CAL = 'I_CAL',\n  /** __Deprecated__. Use `MICROSOFT` instead. */\n  OUTLOOK = 'OUTLOOK',\n  /** __Deprecated__. Use `MICROSOFT` instead. */\n  OFFICE_365 = 'OFFICE_365',\n  /** Microsoft Calendar. For example, Office 365 calendar or Outlook calendar. */\n  MICROSOFT = 'MICROSOFT',\n  /** A different type of external calendar, not listed here. */\n  OTHER = 'OTHER',\n}\n\n/** @enumType */\nexport type CalendarTypeWithLiterals =\n  | CalendarType\n  | 'UNDEFINED'\n  | 'GOOGLE'\n  | 'I_CAL'\n  | 'OUTLOOK'\n  | 'OFFICE_365'\n  | 'MICROSOFT'\n  | 'OTHER';\n\nexport enum Status {\n  /** Undefined status. */\n  UNDEFINED = 'UNDEFINED',\n  /** Session is confirmed. Default status. */\n  CONFIRMED = 'CONFIRMED',\n  /**\n   * Session is cancelled.\n   * A cancelled session can be the cancellation of a recurring session that should no longer be displayed or a deleted single session.\n   * The ListSessions returns cancelled sessions only if 'includeDelete' flag is set to true.\n   */\n  CANCELLED = 'CANCELLED',\n}\n\n/** @enumType */\nexport type StatusWithLiterals =\n  | Status\n  | 'UNDEFINED'\n  | 'CONFIRMED'\n  | 'CANCELLED';\n\nexport enum SessionType {\n  UNDEFINED = 'UNDEFINED',\n  /**\n   * Creates an event on the calendar for the owner of the schedule that the session belongs to.\n   * Default type.\n   */\n  EVENT = 'EVENT',\n  /** Represents a resource's available working hours. */\n  WORKING_HOURS = 'WORKING_HOURS',\n  /** Deprecated. Please use WORKING_HOURS. */\n  TIME_AVAILABILITY = 'TIME_AVAILABILITY',\n  /** Deprecated. Represents a resource's available hours. Please use WORKING_HOURS. */\n  AVAILABILITY = 'AVAILABILITY',\n}\n\n/** @enumType */\nexport type SessionTypeWithLiterals =\n  | SessionType\n  | 'UNDEFINED'\n  | 'EVENT'\n  | 'WORKING_HOURS'\n  | 'TIME_AVAILABILITY'\n  | 'AVAILABILITY';\n\nexport interface SessionVersion {\n  /** Incremental version number, which is updated on each change to the session or on changes affecting the session. */\n  number?: string | null;\n}\n\nexport interface ParticipantNotification {\n  /**\n   * Whether to send a message about the changes to the customer.\n   *\n   * Default: `false`\n   */\n  notifyParticipants?: boolean;\n  /** Custom message to send to the participants about the changes to the booking. */\n  message?: string | null;\n}\n\nexport interface ScheduleCancelled {\n  schedule?: Schedule;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n  oldSchedule?: Schedule;\n}\n\nexport interface SessionCreated {\n  session?: Session;\n}\n\nexport interface SessionUpdated {\n  oldSession?: Session;\n  newSession?: Session;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether this notification was created as a result of an anonymization request, such as GDPR.\n   * An anonymized participant will have the following details:\n   * name = \"deleted\"\n   * phone = \"deleted\"\n   * email = \"<EMAIL>\"\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n}\n\nexport interface SessionCancelled {\n  session?: Session;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface AvailabilityPolicyUpdated {\n  availabilityPolicy?: AvailabilityPolicy;\n}\n\n/** Availability policy applied to all site schedules. */\nexport interface AvailabilityPolicy {\n  /** Specify how to split the schedule slots in intervals of minutes. */\n  splitInterval?: SplitInterval;\n}\n\nexport interface IntervalSplit {\n  scheduleId?: string;\n  intervals?: RecurringInterval[];\n  newScheduleVersion?: number | null;\n  oldScheduleVersion?: number | null;\n}\n\nexport interface RecurringSessionSplit {\n  scheduleId?: string;\n  recurringSessions?: Session[];\n  newScheduleVersion?: number | null;\n  oldScheduleVersion?: number | null;\n}\n\n/** Schedule unassigned from user. */\nexport interface ScheduleUnassignedFromUser {\n  /**\n   * The Wix user id.\n   * @format GUID\n   */\n  userId?: string | null;\n  /** The schedule that was unassigned from the user. */\n  schedule?: Schedule;\n}\n\nexport interface MultipleSessionsCreated {\n  schedulesWithSessions?: ScheduleWithSessions[];\n}\n\nexport interface ScheduleWithSessions {\n  schedule?: Schedule;\n  siteProperties?: SitePropertiesOnScheduleCreation;\n  sessions?: Session[];\n}\n\nexport interface SitePropertiesOnScheduleCreation {\n  /** The global time zone value. */\n  timeZone?: string | null;\n}\n\nexport interface MigrationEvent {\n  migrationData?: MigrationData;\n}\n\nexport interface MigrationData {\n  businessId?: string | null;\n  staffs?: StaffData[];\n}\n\nexport interface StaffData {\n  resourceId?: string;\n  syncRequestEmail?: string;\n  refreshToken?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface StaffMemberConnectedToUserEnvelope {\n  data: StaffMemberConnectedToUser;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a Wix user is connected to a staff member.\n *\n *\n * The event is triggered when the connection status changes to `CONNECTED`. It\n * isn't triggered when Wix Bookings sends an invitation to become a Wix user or\n * join a site.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @webhook\n * @eventType wix.bookings.staff.v1.staff_member_connected_to_user\n * @slug connected_to_user\n */\nexport declare function onStaffMemberConnectedToUser(\n  handler: (event: StaffMemberConnectedToUserEnvelope) => void | Promise<void>\n): void;\n\nexport interface StaffMemberCreatedEnvelope {\n  entity: StaffMember;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a staff member is created.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @webhook\n * @eventType wix.bookings.staff.v1.staff_member_created\n * @slug created\n */\nexport declare function onStaffMemberCreated(\n  handler: (event: StaffMemberCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface StaffMemberDeletedEnvelope {\n  entity: StaffMember;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a staff member is deleted.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @webhook\n * @eventType wix.bookings.staff.v1.staff_member_deleted\n * @slug deleted\n */\nexport declare function onStaffMemberDeleted(\n  handler: (event: StaffMemberDeletedEnvelope) => void | Promise<void>\n): void;\n\nexport interface StaffMemberDisconnectedFromUserEnvelope {\n  data: StaffMemberDisconnectedFromUser;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a Wix user is disconnected from a staff member.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @webhook\n * @eventType wix.bookings.staff.v1.staff_member_disconnected_from_user\n * @slug disconnected_from_user\n */\nexport declare function onStaffMemberDisconnectedFromUser(\n  handler: (\n    event: StaffMemberDisconnectedFromUserEnvelope\n  ) => void | Promise<void>\n): void;\n\nexport interface StaffMemberFullyCreatedEnvelope {\n  data: StaffMemberFullyCreated;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a staff member is created.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @webhook\n * @eventType wix.bookings.staff.v1.staff_member_fully_created\n * @slug fully_created\n */\nexport declare function onStaffMemberFullyCreated(\n  handler: (event: StaffMemberFullyCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface StaffMemberUpdatedEnvelope {\n  entity: StaffMember;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a staff member is updated.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @webhook\n * @eventType wix.bookings.staff.v1.staff_member_updated\n * @slug updated\n */\nexport declare function onStaffMemberUpdated(\n  handler: (event: StaffMemberUpdatedEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Creates a staff member.\n *\n *\n * By default, the staff member works during the business working hours. You\n * could follow *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))\n * to set custom working hours.\n * @param staffMember - Staff member to create.\n * @public\n * @documentationMaturity preview\n * @requiredField staffMember\n * @requiredField staffMember.name\n * @param options - Options for creating the staff member.\n * @permissionId BOOKINGS.STAFF_MEMBER_CREATE\n * @applicableIdentity APP\n * @returns Created staff member.\n * @fqn wix.bookings.staff.v1.StaffMembersService.CreateStaffMember\n */\nexport async function createStaffMember(\n  staffMember: NonNullablePaths<StaffMember, `name`, 2>,\n  options?: CreateStaffMemberOptions\n): Promise<\n  NonNullablePaths<\n    StaffMember,\n    | `resource.workingHoursSchedules`\n    | `resource.workingHoursSchedules.${number}.shared`\n    | `resource.usesDefaultWorkingHours`\n    | `associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `associatedWixIdentity.identificationData.memberId`\n    | `associatedWixIdentity.identificationData.wixUserId`\n    | `associatedWixIdentity.identificationData.appId`\n    | `associatedWixIdentity.connectionStatus`\n    | `associatedWixIdentity.connection.status`\n    | `tags.privateTags.tagIds`,\n    5\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({\n      staffMember: staffMember,\n      fields: options?.fields,\n    }),\n    [\n      {\n        transformFn: transformSDKImageToRESTImage,\n        paths: [{ path: 'staffMember.mainMedia.image' }],\n      },\n    ]\n  );\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.createStaffMember(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMember.mainMedia.image' }],\n        },\n      ])\n    )?.staffMember!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          staffMember: '$[0]',\n          fields: '$[1].fields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['staffMember', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CreateStaffMemberOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Retrieves a staff member.\n * @param staffMemberId - ID of the staff member to retrieve.\n * @public\n * @documentationMaturity preview\n * @requiredField staffMemberId\n * @param options - Option for retrieving the staff member.\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @applicableIdentity APP\n * @returns Retrieved staff member.\n * @fqn wix.bookings.staff.v1.StaffMembersService.GetStaffMember\n */\nexport async function getStaffMember(\n  staffMemberId: string,\n  options?: GetStaffMemberOptions\n): Promise<\n  NonNullablePaths<\n    StaffMember,\n    | `resource.workingHoursSchedules`\n    | `resource.workingHoursSchedules.${number}.shared`\n    | `resource.usesDefaultWorkingHours`\n    | `associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `associatedWixIdentity.identificationData.memberId`\n    | `associatedWixIdentity.identificationData.wixUserId`\n    | `associatedWixIdentity.identificationData.appId`\n    | `associatedWixIdentity.connectionStatus`\n    | `associatedWixIdentity.connection.status`\n    | `tags.privateTags.tagIds`,\n    5\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    staffMemberId: staffMemberId,\n    fields: options?.fields,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.getStaffMember(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMember.mainMedia.image' }],\n        },\n      ])\n    )?.staffMember!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          staffMemberId: '$[0]',\n          fields: '$[1].fields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['staffMemberId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface GetStaffMemberOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Updates a staff member.\n *\n *\n * Each time the staff member is updated, `revision` increments by 1. You must\n * include current revision of the staff member when updating it. This ensures\n * you're working with the latest service information and prevents unintended\n * overwrites.\n * @param _id - Staff member ID.\n * @public\n * @documentationMaturity preview\n * @requiredField _id\n * @requiredField staffMember\n * @requiredField staffMember.revision\n * @param staffMember - The staff member to update\n * @param options - Options for updating the staff member.\n * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE\n * @applicableIdentity APP\n * @returns Updated staff member.\n * @fqn wix.bookings.staff.v1.StaffMembersService.UpdateStaffMember\n */\nexport async function updateStaffMember(\n  _id: string,\n  staffMember: NonNullablePaths<UpdateStaffMember, `revision`, 2>,\n  options?: UpdateStaffMemberOptions\n): Promise<\n  NonNullablePaths<\n    StaffMember,\n    | `resource.workingHoursSchedules`\n    | `resource.workingHoursSchedules.${number}.shared`\n    | `resource.usesDefaultWorkingHours`\n    | `associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `associatedWixIdentity.identificationData.memberId`\n    | `associatedWixIdentity.identificationData.wixUserId`\n    | `associatedWixIdentity.identificationData.appId`\n    | `associatedWixIdentity.connectionStatus`\n    | `associatedWixIdentity.connection.status`\n    | `tags.privateTags.tagIds`,\n    5\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({\n      staffMember: { ...staffMember, id: _id },\n      fields: options?.fields,\n    }),\n    [\n      {\n        transformFn: transformSDKImageToRESTImage,\n        paths: [{ path: 'staffMember.mainMedia.image' }],\n      },\n    ]\n  );\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.updateStaffMember(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMember.mainMedia.image' }],\n        },\n      ])\n    )?.staffMember!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { staffMember: '$[1]' },\n        explicitPathsToArguments: {\n          'staffMember.id': '$[0]',\n          fields: '$[2].fields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'staffMember', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateStaffMember {\n  /**\n   * Staff member ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Staff member name.\n   * @minLength 1\n   * @maxLength 40\n   */\n  name?: string | null;\n  /**\n   * Staff member's email address.\n   * @maxLength 320\n   * @format EMAIL\n   */\n  email?: string | null;\n  /**\n   * Staff member's phone number.\n   * @maxLength 20\n   * @format PHONE\n   */\n  phone?: string | null;\n  /**\n   * Description for the staff member. For example,\n   * `Experienced nail technician specialized in gel and acrylic nails`.\n   * @maxLength 500\n   */\n  description?: string | null;\n  /** Staff media. */\n  mainMedia?: MediaItem;\n  /**\n   * Staff member's *resource ID*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)),\n   * identical to `resource.id`.\n   * @readonly\n   * @format GUID\n   */\n  resourceId?: string | null;\n  /**\n   * Details about the *resource object*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))\n   * associated with the staff member. Available only if you specify `RESOURCE_DETAILS`\n   * in the `fields` array.\n   * @readonly\n   */\n  resource?: Resource;\n  /**\n   * Identity of the Wix user associated with the staff member. Learn more about\n   * _identities_\n   * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).\n   * @readonly\n   */\n  associatedWixIdentity?: AssociatedWixIdentity;\n  /**\n   * Revision number, which increments by 1 each time the staff member is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating the staff member.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Time the staff member was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Time the staff member was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /** Extensions enabling users to save custom data related to the staff member. */\n  extendedFields?: ExtendedFields;\n  /**\n   * Tags allowing you to classify staff members. Learn more about *tags*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n   */\n  tags?: Tags;\n}\n\nexport interface UpdateStaffMemberOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Deletes a staff member.\n *\n *\n * Also deletes the *resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))\n * associated with the staff member.\n * @param staffMemberId - ID of the staff member to delete.\n * @public\n * @documentationMaturity preview\n * @requiredField staffMemberId\n * @permissionId BOOKINGS.STAFF_MEMBER_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.DeleteStaffMember\n */\nexport async function deleteStaffMember(staffMemberId: string): Promise<void> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    staffMemberId: staffMemberId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.deleteStaffMember(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { staffMemberId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['staffMemberId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Creates a query to retrieve a list of staff members.\n *\n * The `queryStaffMembers()` function builds a query to retrieve a list of staff members and returns a `StaffMembersQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-builder/find) function.\n *\n * You can refine the query by chaining `StaffMembersQueryBuilder` functions onto the query. `StaffMembersQueryBuilder` functions enable you to sort, filter, and control the results that `queryStaffMembers()` returns.\n *\n * `queryStaffMembers()` runs with the following `StaffMembersQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `staffMembersTypes()` are applied in the order they are called.\n *\n * The following `StaffMembersQueryBuilder` functions are supported for the `queryStaffMembers()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-result/items) property in `StaffMembersQueryResult`.\n * @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.QueryStaffMembers\n */\nexport function queryStaffMembers(\n  options?: QueryStaffMembersOptions\n): StaffMembersQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    StaffMember,\n    'CURSOR',\n    QueryStaffMembersRequest,\n    QueryStaffMembersResponse\n  >({\n    func: async (payload: QueryStaffMembersRequest) => {\n      const reqOpts = ambassadorWixBookingsStaffV1StaffMember.queryStaffMembers(\n        { ...payload, ...(options ?? {}) }\n      );\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (query: QueryStaffMembersRequest['query']) => {\n      const args = [query, options] as [\n        QueryStaffMembersRequest['query'],\n        QueryStaffMembersOptions\n      ];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({\n      data,\n    }: HttpResponse<QueryStaffMembersResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [\n          {\n            transformFn: transformRESTImageToSDKImage,\n            paths: [{ path: 'staffMembers.mainMedia.image' }],\n          },\n        ])\n      );\n\n      return {\n        items: transformedData?.staffMembers,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'CURSOR',\n    transformationPaths: {},\n  });\n}\n\nexport interface QueryStaffMembersOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[] | undefined;\n}\n\ninterface QueryCursorResult {\n  cursors: Cursors;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface StaffMembersQueryResult extends QueryCursorResult {\n  items: StaffMember[];\n  query: StaffMembersQueryBuilder;\n  next: () => Promise<StaffMembersQueryResult>;\n  prev: () => Promise<StaffMembersQueryResult>;\n}\n\nexport interface StaffMembersQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  eq: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'email'\n      | 'phone'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  ne: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'email'\n      | 'phone'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  ge: (\n    propertyName:\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  gt: (\n    propertyName:\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  le: (\n    propertyName:\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   * @documentationMaturity preview\n   */\n  lt: (\n    propertyName:\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `string`.\n   * @param string - String to compare against. Case-insensitive.\n   * @documentationMaturity preview\n   */\n  startsWith: (\n    propertyName:\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId',\n    value: string\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   * @documentationMaturity preview\n   */\n  hasSome: (\n    propertyName:\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any[]\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   * @documentationMaturity preview\n   */\n  hasAll: (propertyName: string, value: any[]) => StaffMembersQueryBuilder;\n  /** @documentationMaturity preview */\n  in: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'email'\n      | 'phone'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: any\n  ) => StaffMembersQueryBuilder;\n  /** @documentationMaturity preview */\n  exists: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'email'\n      | 'phone'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | '_createdDate'\n      | '_updatedDate',\n    value: boolean\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments.\n   * @documentationMaturity preview\n   */\n  ascending: (\n    ...propertyNames: Array<\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | 'associatedWixIdentity.identificationData.identityType'\n      | 'default'\n      | '_createdDate'\n      | '_updatedDate'\n    >\n  ) => StaffMembersQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments.\n   * @documentationMaturity preview\n   */\n  descending: (\n    ...propertyNames: Array<\n      | '_id'\n      | 'description'\n      | 'resourceId'\n      | 'resource.id'\n      | 'associatedWixIdentity.identificationData.wixUserId'\n      | 'associatedWixIdentity.identificationData.contactId'\n      | 'associatedWixIdentity.identificationData.identityType'\n      | 'default'\n      | '_createdDate'\n      | '_updatedDate'\n    >\n  ) => StaffMembersQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object.\n   * @documentationMaturity preview\n   */\n  limit: (limit: number) => StaffMembersQueryBuilder;\n  /** @param cursor - A pointer to specific record\n   * @documentationMaturity preview\n   */\n  skipTo: (cursor: string) => StaffMembersQueryBuilder;\n  /** @documentationMaturity preview */\n  find: () => Promise<StaffMembersQueryResult>;\n}\n\n/**\n * Counts how many staff members match the given filter.\n *\n *\n * ## Filter\n *\n * Refer to the _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).\n * @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.CountStaffMembers\n */\nexport async function countStaffMembers(\n  options?: CountStaffMembersOptions\n): Promise<NonNullablePaths<CountStaffMembersResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.countStaffMembers(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountStaffMembersOptions {\n  /**\n   * Filter to base the count upon. See the *supported filters article*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n   * for a complete list of supported filters.\n   */\n  filter?: Record<string, any> | null;\n}\n\n/**\n * Connects a Wix user to a staff member, enabling them to manage their own\n * working hour schedule in the dashboard.\n *\n *\n * By default, Wix Bookings uses the staff member's `email`. However, you can\n * specify an alternative email address. If no existing Wix user is associated\n * with that email, Wix sends them an invitation to become a Wix user. If an\n * existing user is found but not linked to the site, Wix Bookings sends an\n * invitation to join the site.\n *\n * To check the connection status, call *Get Staff Member*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/get-staff-member) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/get-staff-member)),\n * and specify `ASSOCIATED_IDENTITY_STATUS` in the `fields` parameter.\n *\n * You must call *Disconnect Staff Member From User*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/disconnect-staff-member-from-user) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/disconnect-staff-member-from-user))\n * before connecting a different Wix user to the staff member.\n * @param staffMemberId - ID of the staff member to connect to the Wix user.\n * @public\n * @documentationMaturity preview\n * @requiredField staffMemberId\n * @param options - Options for connecting the staff member to a Wix user.\n * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.ConnectStaffMemberToUser\n */\nexport async function connectStaffMemberToUser(\n  staffMemberId: string,\n  options?: ConnectStaffMemberToUserOptions\n): Promise<\n  NonNullablePaths<\n    ConnectStaffMemberToUserResponse,\n    | `staffMember.resource.workingHoursSchedules`\n    | `staffMember.resource.workingHoursSchedules.${number}.shared`\n    | `staffMember.resource.usesDefaultWorkingHours`\n    | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `staffMember.associatedWixIdentity.identificationData.memberId`\n    | `staffMember.associatedWixIdentity.identificationData.wixUserId`\n    | `staffMember.associatedWixIdentity.identificationData.appId`\n    | `staffMember.associatedWixIdentity.connectionStatus`\n    | `staffMember.associatedWixIdentity.connection.status`\n    | `staffMember.tags.privateTags.tagIds`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    staffMemberId: staffMemberId,\n    email: options?.email,\n    fields: options?.fields,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.connectStaffMemberToUser(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMember.mainMedia.image' }],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          staffMemberId: '$[0]',\n          email: '$[1].email',\n          fields: '$[1].fields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['staffMemberId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ConnectStaffMemberToUserOptions {\n  /**\n   * Email of the Wix user to connect to the staff member.\n   *\n   * Default: Email of the staff member.\n   * @maxLength 320\n   * @format EMAIL\n   */\n  email?: string | null;\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Retrieves a list of up to 100 staff members, given the provided filtering, paging,\n * and sorting.\n *\n *\n * ## Defaults\n *\n * Search Staff Members has the following default settings, which you can override:\n * + Sorted by `createdDate` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filter\n *\n * Refer to the _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with *Search* methods, see\n * _API Query Language_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))\n * and _Sorting and Paging_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).\n * @param search - Search criteria including filter, sort, and paging options.\n *\n * See the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)) for complete filter and sorting options.\n * @public\n * @documentationMaturity preview\n * @requiredField search\n * @permissionId BOOKINGS.STAFF_MEMBER_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers\n */\nexport async function searchStaffMembers(\n  search: StaffMemberSearch,\n  options?: SearchStaffMembersOptions\n): Promise<\n  NonNullablePaths<\n    SearchStaffMembersResponse,\n    | `staffMembers`\n    | `staffMembers.${number}.resource.usesDefaultWorkingHours`\n    | `staffMembers.${number}.associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `staffMembers.${number}.associatedWixIdentity.identificationData.memberId`\n    | `staffMembers.${number}.associatedWixIdentity.identificationData.wixUserId`\n    | `staffMembers.${number}.associatedWixIdentity.identificationData.appId`\n    | `staffMembers.${number}.associatedWixIdentity.connectionStatus`\n    | `staffMembers.${number}.associatedWixIdentity.connection.status`\n    | `aggregationData.results`\n    | `aggregationData.results.${number}.scalar.type`\n    | `aggregationData.results.${number}.scalar.value`\n    | `aggregationData.results.${number}.name`\n    | `aggregationData.results.${number}.type`\n    | `aggregationData.results.${number}.fieldPath`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    search: search,\n    fields: options?.fields,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.searchStaffMembers(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMembers.mainMedia.image' }],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { search: '$[0]', fields: '$[1].fields' },\n        singleArgumentUnchanged: false,\n      },\n      ['search', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface SearchStaffMembersOptions {\n  /**\n   * Conditional fields to return in the response.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\nexport interface StaffMemberSearchSpec extends SearchSpec {\n  searchable: ['name'];\n  paging: 'cursor';\n  wql: [\n    {\n      operators: '*';\n      fields: ['description', 'email', 'name', 'phone', 'resourceId'];\n      sort: 'BOTH';\n    }\n  ];\n}\n\nexport type CommonSearchWithEntityContext = SearchSdkType<\n  StaffMember,\n  StaffMemberSearchSpec\n>;\nexport type StaffMemberSearch = {\n  /** \n  Cursor-based paging for result navigation. Can't be used together with 'paging'.\n  `cursor_paging.cursor` can't be used together with `filter` or `sort`.  \n  */\n  cursorPaging?: {\n    /** \n  Number of items to load. \n  @max: 100 \n  */\n    limit?:\n      | NonNullable<CommonSearchWithEntityContext['cursorPaging']>['limit']\n      | null;\n    /** \n  Pointer to the next or previous page in the list of results.\n\n  You can get the relevant cursor token\n  from the `pagingMetadata` object in the previous call's response.\n  Not relevant for the first request. \n  @maxLength: 16000 \n  */\n    cursor?:\n      | NonNullable<CommonSearchWithEntityContext['cursorPaging']>['cursor']\n      | null;\n  };\n  /** \n  Filter object for narrowing search results. For example, to return only staff members with specific email domains: `\"filter\": {\"email\": {\"$contains\": \"@company.com\"}}`.\n\n  Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).  \n  */\n  filter?: CommonSearchWithEntityContext['filter'] | null;\n  /** \n  Array of sort objects specifying result order. For example, to sort by creation date in descending order: `\"sort\": [{\"fieldName\": \"createdDate\", \"order\": \"DESC\"}]`.\n\n  Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)). \n  @maxSize: 10 \n  */\n  sort?: {\n    /** \n  Name of the field to sort by. \n  @maxLength: 512 \n  */\n    fieldName?: NonNullable<\n      CommonSearchWithEntityContext['sort']\n    >[number]['fieldName'];\n    /** \n  Sort order.  \n  */\n    order?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['order'];\n  }[];\n  /** \n  Free text to match in searchable fields.  \n  */\n  search?: {\n    /** \n  Search mode. Defines the search logic for combining multiple terms in the `expression`.  \n  */\n    mode?: NonNullable<CommonSearchWithEntityContext['search']>['mode'];\n    /** \n  Search term or expression. \n  @maxLength: 100 \n  */\n    expression?:\n      | NonNullable<CommonSearchWithEntityContext['search']>['expression']\n      | null;\n    /** \n  Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `resource.workingHoursSchedules.shared`. \n  @maxLength: 200,\n  @maxSize: 20 \n  */\n    fields?: NonNullable<CommonSearchWithEntityContext['search']>['fields'];\n    /** \n  Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions.  \n  */\n    fuzzy?: NonNullable<CommonSearchWithEntityContext['search']>['fuzzy'];\n  };\n};\n\n/**\n * Disconnects a staff member from a Wix user, clearing the `associatedWixIdentity`\n * field. Consequently, the user can no longer manage the staff member's working\n * hour schedule in the dashboard.\n *\n *\n * You must call *Disconnect Staff Member From User* before connecting a\n * different Wix user to the staff member.\n * @param staffMemberId - ID of the staff member to disconnect from its Wix user.\n * @public\n * @documentationMaturity preview\n * @requiredField staffMemberId\n * @param options - Options to disconnect the staff member from a Wix user.\n * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.DisconnectStaffMemberFromUser\n */\nexport async function disconnectStaffMemberFromUser(\n  staffMemberId: string,\n  options?: DisconnectStaffMemberFromUserOptions\n): Promise<\n  NonNullablePaths<\n    DisconnectStaffMemberFromUserResponse,\n    | `staffMember.resource.workingHoursSchedules`\n    | `staffMember.resource.workingHoursSchedules.${number}.shared`\n    | `staffMember.resource.usesDefaultWorkingHours`\n    | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `staffMember.associatedWixIdentity.identificationData.memberId`\n    | `staffMember.associatedWixIdentity.identificationData.wixUserId`\n    | `staffMember.associatedWixIdentity.identificationData.appId`\n    | `staffMember.associatedWixIdentity.connectionStatus`\n    | `staffMember.associatedWixIdentity.connection.status`\n    | `staffMember.tags.privateTags.tagIds`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    staffMemberId: staffMemberId,\n    fields: options?.fields,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.disconnectStaffMemberFromUser(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMember.mainMedia.image' }],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          staffMemberId: '$[0]',\n          fields: '$[1].fields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['staffMemberId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DisconnectStaffMemberFromUserOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Updates a staff member's working hours based on the specified *schedule ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n *\n *\n * ## Default working hours\n *\n * By default, staff members work during the opening hours of the business's\n * _default location_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n *\n * ## Schedule ID\n *\n * You can specify either the ID of the business's working hour schedule or the\n * staff member's event schedule. The call fails if you specify a different\n * schedule ID.\n *\n * ### Staff event schedule\n *\n * To customize a staff member's working hours, specify their event schedule ID\n * as `scheduleId`. Refer to *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))\n * for more details.\n *\n * ### Business working hour schedule\n *\n * To reset a staff member's working hours to the default business hours,\n * specify the ID of the business working hour schedule as `scheduleId`.\n * @param staffMemberId - ID of the staff member to assign the schedule to.\n * @param scheduleId - ID of the *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to assign to the staff member.\n *\n * Must be either the staff member's event schedule ID or the working hour\n * schedule ID for a business location.\n * @public\n * @documentationMaturity preview\n * @requiredField scheduleId\n * @requiredField staffMemberId\n * @param options - Options for setting the staff member's working hours schedule.\n * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule\n */\nexport async function assignWorkingHoursSchedule(\n  staffMemberId: string,\n  scheduleId: string,\n  options?: AssignWorkingHoursScheduleOptions\n): Promise<\n  NonNullablePaths<\n    AssignWorkingHoursScheduleResponse,\n    | `staffMember.resource.workingHoursSchedules`\n    | `staffMember.resource.workingHoursSchedules.${number}.shared`\n    | `staffMember.resource.usesDefaultWorkingHours`\n    | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `staffMember.associatedWixIdentity.identificationData.memberId`\n    | `staffMember.associatedWixIdentity.identificationData.wixUserId`\n    | `staffMember.associatedWixIdentity.identificationData.appId`\n    | `staffMember.associatedWixIdentity.connectionStatus`\n    | `staffMember.associatedWixIdentity.connection.status`\n    | `staffMember.tags.privateTags.tagIds`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    staffMemberId: staffMemberId,\n    scheduleId: scheduleId,\n    fields: options?.fields,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.assignWorkingHoursSchedule(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMember.mainMedia.image' }],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          staffMemberId: '$[0]',\n          scheduleId: '$[1]',\n          fields: '$[2].fields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['staffMemberId', 'scheduleId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface AssignWorkingHoursScheduleOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Assigns a custom working hours schedule to the staff member\n *\n *\n * The working hours schedule is a schedule that defines the working hours of a staff member,\n * and dictate when the staff member is available for bookings.\n *\n * By default staff members use the shared business working hours schedule. By assigning a custom working hours schedule to a staff member,\n * you can define specific working hours for that staff member.\n *\n * To create and manage schedules and working hours sessions, use [Events API](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction).\n * @param staffMemberId - ID of the staff member for which to assign a working hour schedule.\n * @param scheduleId - ID of the *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to assign to the staff member.\n * @public\n * @documentationMaturity preview\n * @requiredField scheduleId\n * @requiredField staffMemberId\n * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.AssignCustomSchedule\n * @deprecated\n * @replacedBy wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule\n * @targetRemovalDate 2024-12-31\n */\nexport async function assignCustomSchedule(\n  staffMemberId: string,\n  scheduleId: string,\n  options?: AssignCustomScheduleOptions\n): Promise<\n  NonNullablePaths<\n    AssignCustomScheduleResponse,\n    | `staffMember.resource.workingHoursSchedules`\n    | `staffMember.resource.workingHoursSchedules.${number}.shared`\n    | `staffMember.resource.usesDefaultWorkingHours`\n    | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId`\n    | `staffMember.associatedWixIdentity.identificationData.memberId`\n    | `staffMember.associatedWixIdentity.identificationData.wixUserId`\n    | `staffMember.associatedWixIdentity.identificationData.appId`\n    | `staffMember.associatedWixIdentity.connectionStatus`\n    | `staffMember.associatedWixIdentity.connection.status`\n    | `staffMember.tags.privateTags.tagIds`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    staffMemberId: staffMemberId,\n    scheduleId: scheduleId,\n    fields: options?.fields,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.assignCustomSchedule(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [{ path: 'staffMember.mainMedia.image' }],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          staffMemberId: '$[0]',\n          scheduleId: '$[1]',\n          fields: '$[2].fields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['staffMemberId', 'scheduleId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface AssignCustomScheduleOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 3\n   */\n  fields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Synchronously updates *tags*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n * for up to 100 staff members.\n *\n *\n * If you specify a tag both in `assignTags` and `unassignTags`, the call\n * succeeds and the tag is assigned.\n * @param ids - IDs of staff members to update tags for.\n * @public\n * @documentationMaturity preview\n * @requiredField ids\n * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTags\n */\nexport async function bulkUpdateStaffMemberTags(\n  ids: string[],\n  options?: BulkUpdateStaffMemberTagsOptions\n): Promise<\n  NonNullablePaths<\n    BulkUpdateStaffMemberTagsResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    ids: ids,\n    assignTags: options?.assignTags,\n    unassignTags: options?.unassignTags,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.bulkUpdateStaffMemberTags(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          ids: '$[0]',\n          assignTags: '$[1].assignTags',\n          unassignTags: '$[1].unassignTags',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['ids', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkUpdateStaffMemberTagsOptions {\n  /** Tags to add to the staff members. */\n  assignTags?: Tags;\n  /** Tags to remove from the staff members. */\n  unassignTags?: Tags;\n}\n\n/**\n * Asynchronously updates *tags*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n * for staff members, given the provided filtering.\n *\n *\n * If you specify a tag both in `assignTags` and `unassignTags`, the call\n * succeeds and the tag is assigned.\n *\n * ## Filter\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n * @param filter - Filter to base the update upon. See the *supported filters article*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters.\n * @public\n * @documentationMaturity preview\n * @requiredField filter\n * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTagsByFilter\n */\nexport async function bulkUpdateStaffMemberTagsByFilter(\n  filter: Record<string, any>,\n  options?: BulkUpdateStaffMemberTagsByFilterOptions\n): Promise<\n  NonNullablePaths<BulkUpdateStaffMemberTagsByFilterResponse, `jobId`, 2>\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: filter,\n    assignTags: options?.assignTags,\n    unassignTags: options?.unassignTags,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsStaffV1StaffMember.bulkUpdateStaffMemberTagsByFilter(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          filter: '$[0]',\n          assignTags: '$[1].assignTags',\n          unassignTags: '$[1].unassignTags',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['filter', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkUpdateStaffMemberTagsByFilterOptions {\n  /** Tags to add to the staff members. */\n  assignTags?: Tags;\n  /** Tags to remove from the staff members. */\n  unassignTags?: Tags;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsStaffV1StaffMembersServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/staff-members',\n        destPath: '/v1/staff-members',\n      },\n      {\n        srcPath: '/bookings/v1/bulk/staff-members',\n        destPath: '/v1/bulk/staff-members',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_staff-members';\n\n/**\n * Creates a staff member.\n *\n *\n * By default, the staff member works during the business working hours. You\n * could follow *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))\n * to set custom working hours.\n */\nexport function createStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __createStaffMember({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'staffMember.createdDate' },\n          { path: 'staffMember.updatedDate' },\n          { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.CreateStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createStaffMember;\n}\n\n/** Retrieves a staff member. */\nexport function getStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __getStaffMember({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.GetStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMemberId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getStaffMember;\n}\n\n/**\n * Updates a staff member.\n *\n *\n * Each time the staff member is updated, `revision` increments by 1. You must\n * include current revision of the staff member when updating it. This ensures\n * you're working with the latest service information and prevents unintended\n * overwrites.\n */\nexport function updateStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __updateStaffMember({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'fieldMask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'staffMember.createdDate' },\n          { path: 'staffMember.updatedDate' },\n          { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.UpdateStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMember.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateStaffMember;\n}\n\n/**\n * Deletes a staff member.\n *\n *\n * Also deletes the *resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))\n * associated with the staff member.\n */\nexport function deleteStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __deleteStaffMember({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.DeleteStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMemberId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteStaffMember;\n}\n\n/**\n * Creates a query to retrieve a list of staff members.\n *\n * The `queryStaffMembers()` function builds a query to retrieve a list of staff members and returns a `StaffMembersQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-builder/find) function.\n *\n * You can refine the query by chaining `StaffMembersQueryBuilder` functions onto the query. `StaffMembersQueryBuilder` functions enable you to sort, filter, and control the results that `queryStaffMembers()` returns.\n *\n * `queryStaffMembers()` runs with the following `StaffMembersQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `staffMembersTypes()` are applied in the order they are called.\n *\n * The following `StaffMembersQueryBuilder` functions are supported for the `queryStaffMembers()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-result/items) property in `StaffMembersQueryResult`.\n */\nexport function queryStaffMembers(payload: object): RequestOptionsFactory<any> {\n  function __queryStaffMembers({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.QueryStaffMembers',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMembers.createdDate' },\n              { path: 'staffMembers.updatedDate' },\n              { path: 'staffMembers.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryStaffMembers;\n}\n\n/**\n * Counts how many staff members match the given filter.\n *\n *\n * ## Filter\n *\n * Refer to the _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).\n */\nexport function countStaffMembers(payload: object): RequestOptionsFactory<any> {\n  function __countStaffMembers({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.CountStaffMembers',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countStaffMembers;\n}\n\n/**\n * Connects a Wix user to a staff member, enabling them to manage their own\n * working hour schedule in the dashboard.\n *\n *\n * By default, Wix Bookings uses the staff member's `email`. However, you can\n * specify an alternative email address. If no existing Wix user is associated\n * with that email, Wix sends them an invitation to become a Wix user. If an\n * existing user is found but not linked to the site, Wix Bookings sends an\n * invitation to join the site.\n *\n * To check the connection status, call *Get Staff Member*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/get-staff-member) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/get-staff-member)),\n * and specify `ASSOCIATED_IDENTITY_STATUS` in the `fields` parameter.\n *\n * You must call *Disconnect Staff Member From User*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/disconnect-staff-member-from-user) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/disconnect-staff-member-from-user))\n * before connecting a different Wix user to the staff member.\n */\nexport function connectStaffMemberToUser(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __connectStaffMemberToUser({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.ConnectStaffMemberToUser',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath:\n          '/v1/staff-members/{staffMemberId}/connect-staff-member-to-user',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __connectStaffMemberToUser;\n}\n\n/**\n * Retrieves a list of up to 100 staff members, given the provided filtering, paging,\n * and sorting.\n *\n *\n * ## Defaults\n *\n * Search Staff Members has the following default settings, which you can override:\n * + Sorted by `createdDate` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filter\n *\n * Refer to the _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with *Search* methods, see\n * _API Query Language_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))\n * and _Sorting and Paging_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).\n */\nexport function searchStaffMembers(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __searchStaffMembers({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/search',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMembers.createdDate' },\n              { path: 'staffMembers.updatedDate' },\n              { path: 'staffMembers.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'aggregationData.results.ranges.results.from' },\n              { path: 'aggregationData.results.ranges.results.to' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from',\n              },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.from',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.to',\n              },\n              { path: 'aggregationData.results.scalar.value' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.scalar.value',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.scalar.value',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __searchStaffMembers;\n}\n\n/**\n * Disconnects a staff member from a Wix user, clearing the `associatedWixIdentity`\n * field. Consequently, the user can no longer manage the staff member's working\n * hour schedule in the dashboard.\n *\n *\n * You must call *Disconnect Staff Member From User* before connecting a\n * different Wix user to the staff member.\n */\nexport function disconnectStaffMemberFromUser(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __disconnectStaffMemberFromUser({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.DisconnectStaffMemberFromUser',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath:\n          '/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __disconnectStaffMemberFromUser;\n}\n\n/**\n * Updates a staff member's working hours based on the specified *schedule ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n *\n *\n * ## Default working hours\n *\n * By default, staff members work during the opening hours of the business's\n * _default location_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n *\n * ## Schedule ID\n *\n * You can specify either the ID of the business's working hour schedule or the\n * staff member's event schedule. The call fails if you specify a different\n * schedule ID.\n *\n * ### Staff event schedule\n *\n * To customize a staff member's working hours, specify their event schedule ID\n * as `scheduleId`. Refer to *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))\n * for more details.\n *\n * ### Business working hour schedule\n *\n * To reset a staff member's working hours to the default business hours,\n * specify the ID of the business working hour schedule as `scheduleId`.\n */\nexport function assignWorkingHoursSchedule(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __assignWorkingHoursSchedule({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath:\n          '/v1/staff-members/{staffMemberId}/assign-working-hours-schedule',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __assignWorkingHoursSchedule;\n}\n\n/**\n * Assigns a custom working hours schedule to the staff member\n *\n *\n * The working hours schedule is a schedule that defines the working hours of a staff member,\n * and dictate when the staff member is available for bookings.\n *\n * By default staff members use the shared business working hours schedule. By assigning a custom working hours schedule to a staff member,\n * you can define specific working hours for that staff member.\n *\n * To create and manage schedules and working hours sessions, use [Events API](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction).\n * @deprecated It has been replaced with wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule(), and will be removed on 2024-12-31.\n */\nexport function assignCustomSchedule(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __assignCustomSchedule({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.AssignCustomSchedule',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMemberId}/assign-custom-schedule',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __assignCustomSchedule;\n}\n\n/**\n * Synchronously updates *tags*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n * for up to 100 staff members.\n *\n *\n * If you specify a tag both in `assignTags` and `unassignTags`, the call\n * succeeds and the tag is assigned.\n */\nexport function bulkUpdateStaffMemberTags(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateStaffMemberTags({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTags',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/bulk/staff-members/update-tags',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateStaffMemberTags;\n}\n\n/**\n * Asynchronously updates *tags*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n * for staff members, given the provided filtering.\n *\n *\n * If you specify a tag both in `assignTags` and `unassignTags`, the call\n * succeeds and the tag is assigned.\n *\n * ## Filter\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n */\nexport function bulkUpdateStaffMemberTagsByFilter(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateStaffMemberTagsByFilter({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTagsByFilter',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/bulk/staff-members/update-tags-by-filter',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateStaffMemberTagsByFilter;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAAAA;AAAA,EAAA,kCAAAC;AAAA,EAAA,iCAAAC;AAAA,EAAA,yCAAAC;AAAA,EAAA,gCAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,qCAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,0BAAAC;AAAA,EAAA,yBAAAC;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,2BAA6B;AAC7B,oCAGO;;;ACLP,0BAAkC;AAClC,mBAA6C;AAC7C,uBAAqD;AACrD,IAAAC,oBAAqD;AACrD,wBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,gDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAWd,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,gDAAgD;AAAA,QAC1D;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,YAAY,CAAC;AAAA,MAC/B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,gDAAgD;AAAA,QAC1D;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,iDAAiD;AAAA,UAC3D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiBO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAqBO,SAAS,yBACd,SAC4B;AAC5B,WAAS,2BAA2B,EAAE,KAAK,GAAQ;AACjD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA2BO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,iDAAiD;AAAA,UAC3D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,8BACd,SAC4B;AAC5B,WAAS,gCAAgC,EAAE,KAAK,GAAQ;AACtD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA+BO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAeO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiBO,SAAS,kCACd,SAC4B;AAC5B,WAAS,oCAAoC,EAAE,KAAK,GAAQ;AAC1D,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADxqBA,mBAA6C;AAC7C,IAAAC,gBAA6C;AAC7C,IAAAC,0BAA+B;AA+RxB,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,uBAAoB;AACpB,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,cAAW;AACX,EAAAA,cAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAiBL,IAAK,4DAAL,kBAAKC,+DAAL;AAEL,EAAAA,2DAAA,aAAU;AAEV,EAAAA,2DAAA,eAAY;AAEZ,EAAAA,2DAAA,aAAU;AAEV,EAAAA,2DAAA,aAAU;AAEV,EAAAA,2DAAA,kBAAe;AAVL,SAAAA;AAAA,GAAA;AA4DL,IAAK,mBAAL,kBAAKC,sBAAL;AAEL,EAAAA,kBAAA,eAAY;AAEZ,EAAAA,kBAAA,kBAAe;AAJL,SAAAA;AAAA,GAAA;AA4IL,IAAK,kBAAL,kBAAKC,qBAAL;AAEL,EAAAA,iBAAA,sBAAmB;AAEnB,EAAAA,iBAAA,gCAA6B;AAJnB,SAAAA;AAAA,GAAA;AAoPL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AA+IL,IAAK,OAAL,kBAAKC,UAAL;AAEL,EAAAA,MAAA,QAAK;AAEL,EAAAA,MAAA,SAAM;AAJI,SAAAA;AAAA,GAAA;AAgEL,IAAK,aAAL,kBAAKC,gBAAL;AACL,EAAAA,YAAA,yBAAsB;AAEtB,EAAAA,YAAA,oBAAiB;AAEjB,EAAAA,YAAA,SAAM;AAEN,EAAAA,YAAA,SAAM;AAPI,SAAAA;AAAA,GAAA;AAkDL,IAAK,kBAAL,kBAAKC,qBAAL;AACL,EAAAA,iBAAA,8BAA2B;AAE3B,EAAAA,iBAAA,WAAQ;AAER,EAAAA,iBAAA,WAAQ;AAER,EAAAA,iBAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AAynBL,IAAK,MAAL,kBAAKC,SAAL;AAEL,EAAAA,KAAA,eAAY;AAEZ,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAhBI,SAAAA;AAAA,GAAA;AAuDL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,UAAO;AAEP,EAAAA,cAAA,UAAO;AALG,SAAAA;AAAA,GAAA;AAeL,IAAK,wBAAL,kBAAKC,2BAAL;AAEL,EAAAA,uBAAA,eAAY;AAEZ,EAAAA,uBAAA,WAAQ;AAER,EAAAA,uBAAA,uBAAoB;AAEpB,EAAAA,uBAAA,kBAAe;AARL,SAAAA;AAAA,GAAA;AAqCL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,oBAAiB;AACjB,EAAAA,cAAA,kBAAe;AACf,EAAAA,cAAA,YAAS;AAJC,SAAAA;AAAA,GAAA;AAsJL,IAAK,iBAAL,kBAAKC,oBAAL;AACL,EAAAA,gBAAA,YAAS;AACT,EAAAA,gBAAA,cAAW;AAFD,SAAAA;AAAA,GAAA;AASL,IAAK,wBAAL,kBAAKC,2BAAL;AACL,EAAAA,uBAAA,aAAU;AACV,EAAAA,uBAAA,YAAS;AACT,EAAAA,uBAAA,aAAU;AACV,EAAAA,uBAAA,eAAY;AACZ,EAAAA,uBAAA,kBAAe;AACf,EAAAA,uBAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AA+FL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,aAAU;AACV,EAAAA,WAAA,eAAY;AACZ,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AA4JL,IAAK,iBAAL,kBAAKC,oBAAL;AAEL,EAAAA,gBAAA,eAAY;AAEZ,EAAAA,gBAAA,aAAU;AAEV,EAAAA,gBAAA,cAAW;AAEX,EAAAA,gBAAA,cAAW;AARD,SAAAA;AAAA,GAAA;AA0BL,IAAK,iBAAL,kBAAKC,oBAAL;AAEL,EAAAA,gBAAA,eAAY;AAEZ,EAAAA,gBAAA,aAAU;AAEV,EAAAA,gBAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AAiDL,IAAK,iBAAL,kBAAKC,oBAAL;AAEL,EAAAA,gBAAA,eAAY;AAEZ,EAAAA,gBAAA,6BAA0B;AAE1B,EAAAA,gBAAA,YAAS;AANC,SAAAA;AAAA,GAAA;AAqQL,IAAK,eAAL,kBAAKC,kBAAL;AAEL,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,YAAS;AAET,EAAAA,cAAA,WAAQ;AAER,EAAAA,cAAA,aAAU;AAEV,EAAAA,cAAA,gBAAa;AAEb,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,WAAQ;AAdE,SAAAA;AAAA,GAAA;AA4BL,IAAK,SAAL,kBAAKC,YAAL;AAEL,EAAAA,QAAA,eAAY;AAEZ,EAAAA,QAAA,eAAY;AAMZ,EAAAA,QAAA,eAAY;AAVF,SAAAA;AAAA,GAAA;AAoBL,IAAK,cAAL,kBAAKC,iBAAL;AACL,EAAAA,aAAA,eAAY;AAKZ,EAAAA,aAAA,WAAQ;AAER,EAAAA,aAAA,mBAAgB;AAEhB,EAAAA,aAAA,uBAAoB;AAEpB,EAAAA,aAAA,kBAAe;AAZL,SAAAA;AAAA,GAAA;AA2ML,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAqPZ,eAAsBC,mBACpB,aACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU;AAAA,QACd,qEAAsC;AAAA,MACpC;AAAA,MACA,QAAQ,SAAS;AAAA,IACnB,CAAC;AAAA,IACD;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UACoC,kBAAkB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AAAA,EACL,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,aAAa;AAAA,UACb,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,eAAe,SAAS;AAAA,IAC3B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsBA,eAAsBC,gBACpB,eACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACoC,eAAe,OAAO;AAEhE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AAAA,EACL,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,eAAe;AAAA,UACf,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB,SAAS;AAAA,IAC7B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA+BA,eAAsBE,mBACpB,KACA,aACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU;AAAA,QACd,qEAAsC;AAAA,MACpC,aAAa,EAAE,GAAG,aAAa,IAAI,IAAI;AAAA,MACvC,QAAQ,SAAS;AAAA,IACnB,CAAC;AAAA,IACD;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UACoC,kBAAkB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AAAA,EACL,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,aAAa,OAAO;AAAA,QAC9C,0BAA0B;AAAA,UACxB,kBAAkB;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,eAAe,SAAS;AAAA,IAClC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA2GA,eAAsBG,mBAAkB,eAAsC;AAE5E,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACoC,kBAAkB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAAA,EACjC,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAH;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,eAAe,OAAO;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,eAAe;AAAA,IAClB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyBO,SAASI,mBACd,SAC0B;AAE1B,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,aAAO,mCAKL;AAAA,IACA,MAAM,OAAO,YAAsC;AACjD,YAAM,UAAkD;AAAA,QACtD,EAAE,GAAG,SAAS,GAAI,WAAW,CAAC,EAAG;AAAA,MACnC;AAEA,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,UAA6C;AAChE,YAAM,OAAO,CAAC,OAAO,OAAO;AAI5B,iBAAO,qEAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC;AAAA,MACpB;AAAA,IACF,MAA+C;AAC7C,YAAM,sBAAkB;AAAA,YACtB,wCAAe,MAAM;AAAA,UACnB;AAAA,YACE,aAAa;AAAA,YACb,OAAO,CAAC,EAAE,MAAM,+BAA+B,CAAC;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,uBAAmB,uBAAAJ,gBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;AAqQA,eAAsBK,mBACpB,SACkE;AAElE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACoC,kBAAkB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAL;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsCA,eAAsBM,0BACpB,eACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACoC,yBAAyB,OAAO;AAE1E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAN;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,eAAe;AAAA,UACf,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB,SAAS;AAAA,IAC7B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoDA,eAAsBO,oBACpB,QACA,SAoBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACoC,mBAAmB,OAAO;AAEpE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,+BAA+B,CAAC;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAP;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,QAAQ,QAAQ,cAAc;AAAA,QAClE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU,SAAS;AAAA,IACtB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyHA,eAAsBQ,+BACpB,eACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACoC;AAAA,IACtC;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAR;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,eAAe;AAAA,UACf,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB,SAAS;AAAA,IAC7B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsDA,eAAsBS,4BACpB,eACA,YACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACoC,2BAA2B,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAT;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB,cAAc,SAAS;AAAA,IAC3C;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoCA,eAAsBU,sBACpB,eACA,YACA,SAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACoC,qBAAqB,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO;AAAA,UACL,wCAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO,CAAC,EAAE,MAAM,8BAA8B,CAAC;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAV;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB,cAAc,SAAS;AAAA,IAC3C;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA0BA,eAAsBW,2BACpB,KACA,SAcA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,YAAY,SAAS;AAAA,IACrB,cAAc,SAAS;AAAA,EACzB,CAAC;AAED,QAAM,UACoC,0BAA0B,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAX;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,SAAS;AAAA,IACnB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAiCA,eAAsBY,mCACpB,QACA,SAGA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,YAAY,SAAS;AAAA,IACrB,cAAc,SAAS;AAAA,EACzB,CAAC;AAED,QAAM,UACoC;AAAA,IACtC;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAZ;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU,SAAS;AAAA,IACtB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;", "names": ["assignCustomSchedule", "assignWorkingHoursSchedule", "bulkUpdateStaffMemberTags", "bulkUpdateStaffMemberTagsByFilter", "connectStaffMemberToUser", "countStaffMembers", "createStaffMember", "deleteStaffMember", "disconnectStaffMemberFromUser", "getStaffMember", "queryStaffMembers", "searchStaffMembers", "updateStaffMember", "import_timestamp", "import_rest_modules", "payload", "import_image", "import_transform_paths", "IdentityType", "AssociatedWixIdentityConnectionStatusEnumConnectionStatus", "ConnectionStatus", "RequestedFields", "SortOrder", "Mode", "ScalarType", "AggregationType", "Day", "Transparency", "RecurringIntervalType", "LocationType", "LocationStatus", "LocationsLocationType", "DayOfWeek", "ApprovalStatus", "ScheduleStatus", "ConferenceType", "CalendarType", "Status", "SessionType", "WebhookIdentityType", "createStaffMember", "sdkTransformError", "getStaffMember", "updateStaffMember", "deleteStaffMember", "queryStaffMembers", "countStaffMembers", "connectStaffMemberToUser", "searchStaffMembers", "disconnectStaffMemberFromUser", "assignWorkingHoursSchedule", "assignCustomSchedule", "bulkUpdateStaffMemberTags", "bulkUpdateStaffMemberTagsByFilter"]}