{"version": 3, "sources": ["../../index.typings.ts", "../../src/bookings-v2-attendance-attendance.universal.ts", "../../src/bookings-v2-attendance-attendance.http.ts"], "sourcesContent": ["export * from './src/bookings-v2-attendance-attendance.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeys<PERSON>romRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, HttpResponse, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsV2Attendance from './bookings-v2-attendance-attendance.http.js';\n// @ts-ignore\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/**\n * The `attendance` object represents the attendance information\n * for a booked session, such as:\n *\n * + Did anyone attend the session?\n * + How many people attended the session?\n *\n * The number of session `attendance` objects available depends on the booking type:\n * + Appointment bookings have 1 `attendance` object per appointment session.\n * + Class bookings have 1 `attendance` object for each session of the class. The number of sessions for a class is defined in Schedule and Sessions `schedule.capacity` property.\n * + Course bookings have an `attendance` object for each session of the course. For example, if there are 12 sessions in a course, there are 12 `attendance` objects. The number of sessions for a class is defined in Schedule and Sessions `schedule.capacity` property.\n */\nexport interface Attendance {\n  /**\n   * ID of the `attendance` object.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Corresponding booking ID.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /** Corresponding session ID. */\n  sessionId?: string | null;\n  /** Status indicating if any participants attended the session. */\n  status?: AttendanceStatusWithLiterals;\n  /**\n   * Total number of participants that attended the session. By default, the number\n   * of attendees is set to `1`, but you can set a number to greater than `1` if multiple\n   * participants attended.\n   *\n   * Do not set to `0` to indicate that no one attended the session. Instead, set the `status` field to `NOT_ATTENDED`.\n   *\n   * Default: 1\n   */\n  numberOfAttendees?: number;\n}\n\nexport enum AttendanceStatus {\n  /** There is no available attendance information. */\n  NOT_SET = 'NOT_SET',\n  /** At least a single participant attended the session. */\n  ATTENDED = 'ATTENDED',\n  /** No participants attended the session. */\n  NOT_ATTENDED = 'NOT_ATTENDED',\n}\n\n/** @enumType */\nexport type AttendanceStatusWithLiterals =\n  | AttendanceStatus\n  | 'NOT_SET'\n  | 'ATTENDED'\n  | 'NOT_ATTENDED';\n\nexport interface GetAttendanceRequest {\n  /**\n   * ID of the attendance object to retrieve.\n   * @format GUID\n   */\n  attendanceId: string;\n}\n\nexport interface GetAttendanceResponse {\n  /** Retrieved attendance. */\n  attendance?: Attendance;\n}\n\nexport interface SetAttendanceRequest {\n  /** Attendance to create or update. */\n  attendance: Attendance;\n  /** Information about whether to send a message to a customer after their attendance was set. */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface ParticipantNotification {\n  /**\n   * Specify whether to send a message about the changes to the customer.\n   *\n   * Default: `false`\n   */\n  notifyParticipants?: boolean | null;\n  /**\n   * Optional custom message to send to the participants about the changes to the booking.\n   * @minLength 1\n   * @maxLength 5000\n   */\n  message?: string | null;\n}\n\nexport interface SetAttendanceResponse {\n  /** Created or updated attendance. */\n  attendance?: Attendance;\n}\n\nexport interface AttendanceMarkedAsNotAttended {\n  /** The attendance information for a booked session that you want to create or update. */\n  attendance?: Attendance;\n  /** Information about whether to send a message to a customer after their attendance was set. */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface BulkSetAttendanceRequest {\n  returnFullEntity?: boolean;\n  /**\n   * List of attendance details for booking sessions to create or update.\n   * @maxSize 8\n   */\n  attendanceDetails?: AttendanceDetails[];\n}\n\nexport interface AttendanceDetails {\n  /** Created or updated attendance information for a booking session. */\n  attendance?: Attendance;\n  /** Information about whether to send a message to the customer after their attendance was set. */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface BulkSetAttendanceResponse {\n  /**\n   * List of created or updated `attendance` objects.\n   * @minSize 1\n   * @maxSize 8\n   */\n  results?: BulkAttendanceResult[];\n  /** Information about the total number of successes and failures for the Bulk Set Attendance call. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkAttendanceResult {\n  /** Created or updated `attendance` object. */\n  item?: Attendance;\n  /** Metadata for the created or updated `attendance` object. */\n  itemMetadata?: ItemMetadata;\n}\n\nexport interface ItemMetadata {\n  /** Item ID. Should always be available, unless it's impossible (for example, when failing to create an item). */\n  _id?: string | null;\n  /** Index of the item within the request array. Allows for correlation between request and response items. */\n  originalIndex?: number;\n  /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */\n  success?: boolean;\n  /** Details about the error in case of failure. */\n  error?: ApplicationError;\n}\n\nexport interface ApplicationError {\n  /** Error code. */\n  code?: string;\n  /** Description of the error. */\n  description?: string;\n  /** Data related to the error. */\n  data?: Record<string, any> | null;\n}\n\nexport interface BulkActionMetadata {\n  /** Number of items that were successfully processed. */\n  totalSuccesses?: number;\n  /** Number of items that couldn't be processed. */\n  totalFailures?: number;\n  /** Number of failures without details because detailed failure threshold was exceeded. */\n  undetailedFailures?: number;\n}\n\nexport interface QueryAttendanceRequest {\n  /** Query options. */\n  query: QueryV2;\n}\n\nexport interface QueryV2 extends QueryV2PagingMethodOneOf {\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object. See [API Query Language](https://dev.wix.com/api/rest/getting-started/api-query-language) for more information.\n   *\n   * For a detailed list of supported fields and operators, see [Supported Filters and Sorting](https://dev.wix.com/api/rest/wix-bookings/attendance/supported-filters).\n   *\n   * Max: 1 filter\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[ {\"fieldName\":\"sortField1\",\"order\":\"ASC\"}, {\"fieldName\":\"sortField2\",\"order\":\"DESC\"} ]`\n   *\n   * For details about sorting, see [Supported Filters and Sorting](https://dev.wix.com/api/rest/wix-bookings/attendance/supported-filters).\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface QueryV2PagingMethodOneOf {\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /** Name of the field to sort by. */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\n/**\n * Sort order. Use `ASC` for ascending order or `DESC` for descending order.\n *\n * Default: `ASC`.\n */\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface Paging {\n  /** Number of items to load. */\n  limit?: number | null;\n  /** Number of items to skip in the current sort order. */\n  offset?: number | null;\n}\n\nexport interface CursorPaging {\n  /**\n   * Number of `Attendance` objects to return.\n   *\n   * Default: `50`\n   * Maximum: `1000`\n   * @max 1000\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * You can get the relevant cursor token\n   * from the `pagingMetadata` object in the previous call's response.\n   *\n   * Not relevant for the first request.\n   */\n  cursor?: string | null;\n}\n\n/** List of objects that contain attendance information. */\nexport interface QueryAttendanceResponse {\n  /** List of `attendance` objects that contain attendance information for a booked session. */\n  attendances?: Attendance[];\n  /** Metadata for the paged set of results. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\n/** This is the preferred message for cursor-paging enabled services */\nexport interface CursorPagingMetadata {\n  /** Use these cursors to paginate between results. [Read more](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging). */\n  cursors?: Cursors;\n  /**\n   * Indicates if there are more results after the current page.\n   * If `true`, another page of results can be retrieved.\n   * If `false`, this is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /** Cursor pointing to next page in the list of results. */\n  next?: string | null;\n  /** Cursor pointing to previous page in the list of results. */\n  prev?: string | null;\n}\n\nexport interface CountAttendancesRequest {\n  /** Filter criteria for counting attendance records. If not provided, counts all attendance records for the contact. */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountAttendancesResponse {\n  /** Total number of attendance records matching the filters. */\n  count?: number;\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type GetAttendanceApplicationErrors = {\n  code?: 'ATTENDANCE_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type SetAttendanceApplicationErrors = {\n  code?: 'SESSION_ID_NOT_PROVIDED';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type BulkSetAttendanceApplicationErrors = {\n  code?: 'ATTENDANCES_MISSING_IN_REQUEST';\n  description?: string;\n  data?: Record<string, any>;\n};\n\n/**\n * Retrieves attendance information.\n * @param attendanceId - ID of the attendance object to retrieve.\n * @public\n * @requiredField attendanceId\n * @permissionId BOOKINGS.ATTENDANCE_READ\n * @applicableIdentity APP\n * @returns Retrieved attendance.\n * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.GetAttendance\n */\nexport async function getAttendance(attendanceId: string): Promise<\n  NonNullablePaths<Attendance, `status` | `numberOfAttendees`, 2> & {\n    __applicationErrorsType?: GetAttendanceApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    attendanceId: attendanceId,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Attendance.getAttendance(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.attendance!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { attendanceId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['attendanceId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Sets or updates attendance information for a booking session. This\n * information is stored in an `attendance` object.\n *\n * If an `attendance` object already exists for the session, it's updated.\n * Otherwise, a new object is created.\n *\n * By default, `numberOfAttendees` is set to `1`, but you can specify a higher\n * number if multiple participants attended. Do not set `numberOfAttendees` to\n * `0` to indicate no attendance, instead specify `{\"status\": \"NOT_ATTENDED\"}`.\n *\n * Validation guidelines:\n *\n * + The call succeeds for mismatches between `numberOfAttendees`\n * and `status`. For example, make sure that your code doesn't specify\n * `{\"status\": \"NOT_ATTENDED\"}` with `{\"numberOfAttendees\": 5}`.\n * + The API also allows `numberOfAttendees` to exceed the booking's\n * `numberOfParticipants`. Use higher values only when scenarios like\n * walk-ins justify the exception.\n * @param attendance - Attendance to create or update.\n * @public\n * @requiredField attendance\n * @requiredField attendance.bookingId\n * @requiredField attendance.status\n * @param options - Options to use when setting an attendance.\n * @permissionId BOOKINGS.ATTENDANCE_SET\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.SetAttendance\n */\nexport async function setAttendance(\n  attendance: NonNullablePaths<Attendance, `bookingId` | `status`, 2>,\n  options?: SetAttendanceOptions\n): Promise<\n  NonNullablePaths<\n    SetAttendanceResponse,\n    `attendance.status` | `attendance.numberOfAttendees`,\n    3\n  > & {\n    __applicationErrorsType?: SetAttendanceApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    attendance: attendance,\n    participantNotification: options?.participantNotification,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Attendance.setAttendance(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          attendance: '$[0]',\n          participantNotification: '$[1].participantNotification',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['attendance', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface SetAttendanceOptions {\n  /** Information about whether to send a message to a customer after their attendance was set. */\n  participantNotification?: ParticipantNotification;\n}\n\n/**\n * Sets or updates attendance information for multiple booking sessions.\n *\n *\n * Refer to Set Attendance for detailed behavior of individual attendance\n * entries.\n *\n * The call fails entirely if any entry in `attendanceDetails` is missing a\n * required field.\n *\n * If attendance details are provided for a non-existent session, the call\n * succeeds for valid sessions while marking the unavailable session as a\n * failure in the response.\n * @public\n * @param options - Options to use when setting multiple attendances in bulk.\n * @permissionId BOOKINGS.ATTENDANCE_SET\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.BulkSetAttendance\n */\nexport async function bulkSetAttendance(\n  options?: BulkSetAttendanceOptions\n): Promise<\n  NonNullablePaths<\n    BulkSetAttendanceResponse,\n    | `results`\n    | `results.${number}.item.status`\n    | `results.${number}.item.numberOfAttendees`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  > & {\n    __applicationErrorsType?: BulkSetAttendanceApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    returnFullEntity: options?.returnFullEntity,\n    attendanceDetails: options?.attendanceDetails,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Attendance.bulkSetAttendance(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          returnFullEntity: '$[0].returnFullEntity',\n          attendanceDetails: '$[0].attendanceDetails',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkSetAttendanceOptions {\n  returnFullEntity?: boolean;\n  /**\n   * List of attendance details for booking sessions to create or update.\n   * @maxSize 8\n   */\n  attendanceDetails?: AttendanceDetails[];\n}\n\n/**\n * Creates a query to retrieve a list of attendances.\n *\n * The `queryAttendances()` function builds a query to retrieve a list of attendances and returns a `AttendancesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to call the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-builder/find) function.\n *\n * You can refine the query by chaining `AttendancesQueryBuilder` functions onto the query. `AttendancesQueryBuilder` functions enable you to sort, filter, and control the results that `queryAttendances()` returns.\n *\n * `queryAttendances()` uses the following `AttendancesQueryBuilder` default values that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryAttendances()` are applied in the order they are called. For example, if you apply `ascending(\"status\")` and then `ascending(\"numberOfAttendees\")`, the results are sorted first by the `\"status\"`, and then, if there are multiple results with the same `\"status\"`, the items are sorted by `\"numberOfAttendees\"`.\n *\n * The following `AttendancesQueryBuilder` functions are supported for the `queryAttendances()` function. For a full description of the tip settings object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-result/items) property in `AttendancesQueryResult`.\n * @public\n * @permissionId BOOKINGS.ATTENDANCE_READ\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.QueryAttendance\n */\nexport function queryAttendance(): AttendancesQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    Attendance,\n    'CURSOR',\n    QueryAttendanceRequest,\n    QueryAttendanceResponse\n  >({\n    func: async (payload: QueryAttendanceRequest) => {\n      const reqOpts =\n        ambassadorWixBookingsV2Attendance.queryAttendance(payload);\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (query: QueryAttendanceRequest['query']) => {\n      const args = [query, {}] as [QueryAttendanceRequest['query'], {}];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({ data }: HttpResponse<QueryAttendanceResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [])\n      );\n\n      return {\n        items: transformedData?.attendances,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'CURSOR',\n    transformationPaths: {},\n  });\n}\n\ninterface QueryCursorResult {\n  cursors: Cursors;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface AttendancesQueryResult extends QueryCursorResult {\n  items: Attendance[];\n  query: AttendancesQueryBuilder;\n  next: () => Promise<AttendancesQueryResult>;\n  prev: () => Promise<AttendancesQueryResult>;\n}\n\nexport interface AttendancesQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  eq: (\n    propertyName:\n      | '_id'\n      | 'bookingId'\n      | 'sessionId'\n      | 'status'\n      | 'numberOfAttendees',\n    value: any\n  ) => AttendancesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ne: (\n    propertyName:\n      | '_id'\n      | 'bookingId'\n      | 'sessionId'\n      | 'status'\n      | 'numberOfAttendees',\n    value: any\n  ) => AttendancesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ge: (\n    propertyName: 'numberOfAttendees',\n    value: any\n  ) => AttendancesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  gt: (\n    propertyName: 'numberOfAttendees',\n    value: any\n  ) => AttendancesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  le: (\n    propertyName: 'numberOfAttendees',\n    value: any\n  ) => AttendancesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  lt: (\n    propertyName: 'numberOfAttendees',\n    value: any\n  ) => AttendancesQueryBuilder;\n  in: (\n    propertyName:\n      | '_id'\n      | 'bookingId'\n      | 'sessionId'\n      | 'status'\n      | 'numberOfAttendees',\n    value: any\n  ) => AttendancesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  ascending: (\n    ...propertyNames: Array<\n      '_id' | 'bookingId' | 'sessionId' | 'status' | 'numberOfAttendees'\n    >\n  ) => AttendancesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  descending: (\n    ...propertyNames: Array<'numberOfAttendees'>\n  ) => AttendancesQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */\n  limit: (limit: number) => AttendancesQueryBuilder;\n  /** @param cursor - A pointer to specific record */\n  skipTo: (cursor: string) => AttendancesQueryBuilder;\n  find: () => Promise<AttendancesQueryResult>;\n}\n\n/**\n * Counts attendance records by contact.\n *\n * Returns the total number of attendance records for the contact,\n * with optional filtering by attendance status and service IDs.\n *\n * Filtering options:\n * + **attendance_status**: Filter by attendance status (ATTENDED, NOT_ATTENDED)\n * + **service_id**: Filter by one or more service IDs from the bookings\n *\n * If no filters are provided, returns the total count of all attendance records\n * for the contact.\n * @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.ATTENDANCE_READ\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.attendance.v2.AttendanceService.CountAttendances\n */\nexport async function countAttendances(\n  options?: CountAttendancesOptions\n): Promise<NonNullablePaths<CountAttendancesResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Attendance.countAttendances(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountAttendancesOptions {\n  /** Filter criteria for counting attendance records. If not provided, counts all attendance records for the contact. */\n  filter?: Record<string, any> | null;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '',\n      },\n      {\n        srcPath: '/bookings-attendance',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/v2/attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_attendance';\n\n/** Retrieves attendance information. */\nexport function getAttendance(payload: object): RequestOptionsFactory<any> {\n  function __getAttendance({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.GetAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/{attendanceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'attendance.updatedDate' },\n              { path: 'attendance.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getAttendance;\n}\n\n/**\n * Sets or updates attendance information for a booking session. This\n * information is stored in an `attendance` object.\n *\n * If an `attendance` object already exists for the session, it's updated.\n * Otherwise, a new object is created.\n *\n * By default, `numberOfAttendees` is set to `1`, but you can specify a higher\n * number if multiple participants attended. Do not set `numberOfAttendees` to\n * `0` to indicate no attendance, instead specify `{\"status\": \"NOT_ATTENDED\"}`.\n *\n * Validation guidelines:\n *\n * + The call succeeds for mismatches between `numberOfAttendees`\n * and `status`. For example, make sure that your code doesn't specify\n * `{\"status\": \"NOT_ATTENDED\"}` with `{\"numberOfAttendees\": 5}`.\n * + The API also allows `numberOfAttendees` to exceed the booking's\n * `numberOfParticipants`. Use higher values only when scenarios like\n * walk-ins justify the exception.\n */\nexport function setAttendance(payload: object): RequestOptionsFactory<any> {\n  function __setAttendance({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'attendance.updatedDate' },\n          { path: 'attendance.statusUpdatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.SetAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/set',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'attendance.updatedDate' },\n              { path: 'attendance.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setAttendance;\n}\n\n/**\n * Sets or updates attendance information for multiple booking sessions.\n *\n *\n * Refer to Set Attendance for detailed behavior of individual attendance\n * entries.\n *\n * The call fails entirely if any entry in `attendanceDetails` is missing a\n * required field.\n *\n * If attendance details are provided for a non-existent session, the call\n * succeeds for valid sessions while marking the unavailable session as a\n * failure in the response.\n */\nexport function bulkSetAttendance(payload: object): RequestOptionsFactory<any> {\n  function __bulkSetAttendance({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'attendanceList.updatedDate' },\n          { path: 'attendanceList.statusUpdatedDate' },\n          { path: 'attendanceDetails.attendance.updatedDate' },\n          { path: 'attendanceDetails.attendance.statusUpdatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.BulkSetAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/bulk/attendance/set',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkSetAttendance;\n}\n\n/**\n * Creates a query to retrieve a list of attendances.\n *\n * The `queryAttendances()` function builds a query to retrieve a list of attendances and returns a `AttendancesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to call the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-builder/find) function.\n *\n * You can refine the query by chaining `AttendancesQueryBuilder` functions onto the query. `AttendancesQueryBuilder` functions enable you to sort, filter, and control the results that `queryAttendances()` returns.\n *\n * `queryAttendances()` uses the following `AttendancesQueryBuilder` default values that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryAttendances()` are applied in the order they are called. For example, if you apply `ascending(\"status\")` and then `ascending(\"numberOfAttendees\")`, the results are sorted first by the `\"status\"`, and then, if there are multiple results with the same `\"status\"`, the items are sorted by `\"numberOfAttendees\"`.\n *\n * The following `AttendancesQueryBuilder` functions are supported for the `queryAttendances()` function. For a full description of the tip settings object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-result/items) property in `AttendancesQueryResult`.\n */\nexport function queryAttendance(payload: object): RequestOptionsFactory<any> {\n  function __queryAttendance({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.QueryAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'attendances.updatedDate' },\n              { path: 'attendances.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryAttendance;\n}\n\n/**\n * Counts attendance records by contact.\n *\n * Returns the total number of attendance records for the contact,\n * with optional filtering by attendance status and service IDs.\n *\n * Filtering options:\n * + **attendance_status**: Filter by attendance status (ATTENDED, NOT_ATTENDED)\n * + **service_id**: Filter by one or more service IDs from the bookings\n *\n * If no filters are provided, returns the total count of all attendance records\n * for the contact.\n */\nexport function countAttendances(payload: object): RequestOptionsFactory<any> {\n  function __countAttendances({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.CountAttendances',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countAttendances;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAAA;AAAA,EAAA,wBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,qBAAAC;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,2BAA6B;AAC7B,oCAGO;;;ACLP,0BAAkC;AAClC,uBAAqD;AACrD,IAAAC,oBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,2DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAGd,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,yBAAyB;AAAA,YACjC,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsBO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,yBAAyB;AAAA,YACjC,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgBO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,6BAA6B;AAAA,UACrC,EAAE,MAAM,mCAAmC;AAAA,UAC3C,EAAE,MAAM,2CAA2C;AAAA,UACnD,EAAE,MAAM,iDAAiD;AAAA,QAC3D;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,iCAAiC;AAAA,UAC3C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAeO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADxVA,IAAAC,0BAA+B;AA0CxB,IAAK,mBAAL,kBAAKC,sBAAL;AAEL,EAAAA,kBAAA,aAAU;AAEV,EAAAA,kBAAA,cAAW;AAEX,EAAAA,kBAAA,kBAAe;AANL,SAAAA;AAAA,GAAA;AAmLL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AA+ML,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AA6CZ,eAAsBC,eAAc,cAIlC;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAA4C,cAAc,OAAO;AAEvE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,cAAc,OAAO;AAAA,QACjD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc;AAAA,IACjB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA+BA,eAAsBC,eACpB,YACA,SASA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,yBAAyB,SAAS;AAAA,EACpC,CAAC;AAED,QAAM,UAA4C,cAAc,OAAO;AAEvE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,yBAAyB;AAAA,QAC3B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc,SAAS;AAAA,IAC1B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA0BA,eAAsBE,mBACpB,SAkBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,kBAAkB,SAAS;AAAA,IAC3B,mBAAmB,SAAS;AAAA,EAC9B,CAAC;AAED,QAAM,UAA4C,kBAAkB,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,kBAAkB;AAAA,UAClB,mBAAmB;AAAA,QACrB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAiCO,SAASG,mBAA2C;AAEzD,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,aAAO,mCAKL;AAAA,IACA,MAAM,OAAO,YAAoC;AAC/C,YAAM,UAC8B,gBAAgB,OAAO;AAE3D,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,UAA2C;AAC9D,YAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AACvB,iBAAO,qEAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC,EAAE,KAAK,MAA6C;AACxE,YAAM,sBAAkB;AAAA,YACtB,wCAAe,MAAM,CAAC,CAAC;AAAA,MACzB;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,uBAAmB,uBAAAH,gBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;AAkHA,eAAsBI,kBACpB,SACiE;AAEjE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UAA4C,iBAAiB,OAAO;AAE1E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAJ;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;", "names": ["bulkSetAttendance", "countAttendances", "getAttendance", "queryAttendance", "setAttendance", "import_timestamp", "import_rest_modules", "payload", "import_transform_paths", "AttendanceStatus", "SortOrder", "WebhookIdentityType", "getAttendance", "sdkTransformError", "setAttendance", "bulkSetAttendance", "queryAttendance", "countAttendances"]}