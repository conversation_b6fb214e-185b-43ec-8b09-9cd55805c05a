import { ListProvidersRequest as ListProvidersRequest$1, ListProvidersResponse as ListProvidersResponse$1, GetConnectionRequest as GetConnectionRequest$1, GetConnectionResponse as GetConnectionResponse$1, ListConnectionsRequest as ListConnectionsRequest$1, ListConnectionsResponse as ListConnectionsResponse$1, ConnectByOAuthRequest as ConnectByOAuthRequest$1, ConnectByOAuthResponse as ConnectByOAuthResponse$1, ConnectByCredentialsRequest as ConnectByCredentialsRequest$1, ConnectByCredentialsResponse as ConnectByCredentialsResponse$1, ListCalendarsRequest as ListCalendarsRequest$1, ListCalendarsResponse as ListCalendarsResponse$1, UpdateSyncConfigRequest as UpdateSyncConfigRequest$1, UpdateSyncConfigResponse as UpdateSyncConfigResponse$1, DisconnectRequest as DisconnectRequest$1, DisconnectResponse as DisconnectResponse$1, ListEventsRequest as ListEventsRequest$1, ListEventsResponse as ListEventsResponse$1 } from './index.typings.mjs';
import '@wix/sdk-types';

interface ListProvidersRequest {
}
interface ListProvidersResponse {
    /** List of external calendar providers supported on the site. */
    providers?: Provider[];
}
interface Provider {
    /**
     * ID of the external calendar provider.
     * @format GUID
     */
    id?: string | null;
    /** Name of the external calendar provider. */
    name?: string | null;
    /** External calendar provider type. */
    calendarType?: CalendarTypeWithLiterals;
    /**
     * Information about how you can connect the external calendar to the Wix site,
     * and which functionality it supports.
     */
    features?: ProviderFeatures;
}
declare enum CalendarType {
    /** There is no information about the external calendar type. */
    UNDEFINED = "UNDEFINED",
    /** [Google Calendar](https://developers.google.com/calendar/api/guides/overview). */
    GOOGLE = "GOOGLE",
    /** Apple iCalendar. */
    I_CAL = "I_CAL",
    /** __Deprecated__. Use `MICROSOFT` instead. */
    OUTLOOK = "OUTLOOK",
    /** __Deprecated__. Use `MICROSOFT` instead. */
    OFFICE_365 = "OFFICE_365",
    /** Microsoft Calendar. For example, Office 365 calendar or Outlook calendar. */
    MICROSOFT = "MICROSOFT",
    /** A different type of external calendar, not listed here. */
    OTHER = "OTHER"
}
/** @enumType */
type CalendarTypeWithLiterals = CalendarType | 'UNDEFINED' | 'GOOGLE' | 'I_CAL' | 'OUTLOOK' | 'OFFICE_365' | 'MICROSOFT' | 'OTHER';
interface ProviderFeatures {
    /** List of supported connection methods. */
    connectMethods?: ConnectMethodWithLiterals[];
    /**
     * Whether you can update `syncConfic` for all external calendar connections
     * by calling _Update Sync Config_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config)).
     */
    updateSyncConfig?: boolean | null;
    /**
     * Information about which events you can import from the external calendar to
     * the Wix site.
     */
    listEventFromCalendars?: ListEventFromCalendarsWithLiterals;
    /**
     * Whether you can export Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * to the external calendar.
     */
    syncToCalendar?: SyncToCalendarWithLiterals;
}
declare enum ConnectMethod {
    /**
     * There is no information about how you can connect the external calendar
     * to the Wix site.
     */
    UNDEFINED = "UNDEFINED",
    /**
     * You can connect the external calendar to the Wix site by calling *Connect By O Auth*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth)).
     */
    OAUTH = "OAUTH",
    /**
     * You can connect the external calendar to the Wix site by calling *Connect By Credentials*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials)).
     */
    CREDENTIALS = "CREDENTIALS"
}
/** @enumType */
type ConnectMethodWithLiterals = ConnectMethod | 'UNDEFINED' | 'OAUTH' | 'CREDENTIALS';
declare enum ListEventFromCalendars {
    /**
     * There is no information about which external calendar events you
     * can import to the Wix site.
     */
    UNDEFINED = "UNDEFINED",
    /** You can't import any events from the external calendar to the Wix site. */
    NOT_SUPPORTED = "NOT_SUPPORTED",
    /**
     * You can import events only for the external calendar that's
     * designated as primary.
     */
    PRIMARY_CALENDAR_ONLY = "PRIMARY_CALENDAR_ONLY",
    /**
     * You can import events only for a specific external calendar account.
     * You can call _List Calendars_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-calendars) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-calendars))
     * to retrieve a complete list of external calendar accounts.
     */
    SPECIFIC_CALENDARS = "SPECIFIC_CALENDARS"
}
/** @enumType */
type ListEventFromCalendarsWithLiterals = ListEventFromCalendars | 'UNDEFINED' | 'NOT_SUPPORTED' | 'PRIMARY_CALENDAR_ONLY' | 'SPECIFIC_CALENDARS';
declare enum SyncToCalendar {
    /**
     * There is no information about which Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * you can export to the external calendar.
     */
    UNDEFINED = "UNDEFINED",
    /**
     * You can't export any Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * to the external calendar.
     */
    NOT_SUPPORTED = "NOT_SUPPORTED",
    /**
     * You can export Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * only to the external calendar that's designated as primary.
     */
    PRIMARY_CALENDAR_ONLY = "PRIMARY_CALENDAR_ONLY",
    /** Events can be exported to specific external calendars. Use [List Calendars](#list-calendars) to get a list of calendars for a connected external calendar account. */
    SPECIFIC_CALENDAR = "SPECIFIC_CALENDAR",
    /**
     * You can export Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * only to a dedicated external calendar account. You can
     * call _List Calendars_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-calendars) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-calendars))
     * to retrieve a complete list of external calendar accounts.
     */
    DEDICATED_CALENDAR = "DEDICATED_CALENDAR"
}
/** @enumType */
type SyncToCalendarWithLiterals = SyncToCalendar | 'UNDEFINED' | 'NOT_SUPPORTED' | 'PRIMARY_CALENDAR_ONLY' | 'SPECIFIC_CALENDAR' | 'DEDICATED_CALENDAR';
interface GetConnectionRequest {
    /** ID of the connection to retrieve. */
    connectionId: string | null;
}
interface GetConnectionResponse {
    /** Retrieved external calendar connection. */
    connection?: Connection;
}
interface Connection {
    /** ID of the connection between the external calendar and the Wix site. */
    id?: string | null;
    /**
     * ID of the external calendar provider.
     * @format GUID
     */
    providerId?: string | null;
    /** External calendar type. */
    calendarType?: CalendarTypeWithLiterals;
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * that's connected to the external calendar.
     * @format GUID
     */
    scheduleId?: string | null;
    /**
     * ID of the *Wix user*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))
     * to whom the external calendar connection belongs.
     * @format GUID
     */
    userId?: string | null;
    /**
     * ID of the app which created the external calendar connection.
     * @format GUID
     */
    appId?: string | null;
    /**
     * Email address associated with the external calendar account. Available only
     * after completed successfully.
     * @format EMAIL
     */
    externalAccountEmail?: string | null;
    /** Connection status. */
    status?: StatusWithLiterals;
    /** Reason for the error. Available only if `status` is `ERROR`. */
    errorReason?: ErrorReasonWithLiterals;
    /** Settings about which sync operations are supported. */
    syncConfig?: ConnectionSyncConfig;
}
declare enum Status {
    /** There is no information about the connection status. */
    UNDEFINED = "UNDEFINED",
    /**
     * The external calendar is connected to the Wix *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),
     * but the sync process hasn't started yet.
     */
    CONNECTED = "CONNECTED",
    /** Sync process is ongoing. */
    SYNC_IN_PROGRESS = "SYNC_IN_PROGRESS",
    /** The Wix calendar and the external calendar are in sync. */
    SYNCED = "SYNCED",
    /**
     * The external calender has been disconnected from the Wix *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     */
    DISCONNECTED = "DISCONNECTED",
    /** The calendars sync is in error state. */
    ERROR = "ERROR"
}
/** @enumType */
type StatusWithLiterals = Status | 'UNDEFINED' | 'CONNECTED' | 'SYNC_IN_PROGRESS' | 'SYNCED' | 'DISCONNECTED' | 'ERROR';
declare enum ErrorReason {
    /** There is no information about the connection error. */
    UNDEFINED = "UNDEFINED",
    /** The external calendar's access token has been revoked. */
    TOKEN_REVOKED = "TOKEN_REVOKED",
    /** The external calendar couldn't be created. */
    EXTERNAL_CALENDAR_CREATION_FAILED = "EXTERNAL_CALENDAR_CREATION_FAILED",
    /** The external calendar was deleted. */
    EXTERNAL_CALENDAR_DELETED = "EXTERNAL_CALENDAR_DELETED"
}
/** @enumType */
type ErrorReasonWithLiterals = ErrorReason | 'UNDEFINED' | 'TOKEN_REVOKED' | 'EXTERNAL_CALENDAR_CREATION_FAILED' | 'EXTERNAL_CALENDAR_DELETED';
interface ConnectionSyncConfig {
    /**
     * Configuration for importing events from the external calendar to the Wix
     * site.
     */
    listEventFromCalendars?: ConnectionSyncConfigListEventFromCalendars;
    /**
     * Configuration for exporting events from the Wix site to the external
     * calendar.
     */
    syncToCalendar?: ConnectionSyncConfigSyncToCalendar;
}
interface Calendar {
    /**
     * ID of the external calendar account.
     * @readonly
     */
    id?: string | null;
    /**
     * Display name of the external calendar account.
     * For example, `Primary` or `Birthdays`.
     */
    name?: string | null;
}
interface PrimaryCalendar {
}
interface Calendars {
    calendars?: Calendar[];
}
interface DedicatedCalendar {
}
interface ConnectionSyncConfigListEventFromCalendars extends ConnectionSyncConfigListEventFromCalendarsListFromOneOf {
    /**
     * An empty `primaryCalendar` object indicates that you can import
     * events only from the primary external calendar. Keep in mind that
     * not all external calendar providers support primary calendars.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * You can import events from the list of specified external calendar
     * accounts. The list may include the primary calendar.
     */
    calendars?: Calendars;
    /**
     * Whether you can call *List Events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
     * to import events from the external calendar to the Wix site.
     */
    enabled?: boolean | null;
}
/** @oneof */
interface ConnectionSyncConfigListEventFromCalendarsListFromOneOf {
    /**
     * An empty `primaryCalendar` object indicates that you can import
     * events only from the primary external calendar. Keep in mind that
     * not all external calendar providers support primary calendars.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * You can import events from the list of specified external calendar
     * accounts. The list may include the primary calendar.
     */
    calendars?: Calendars;
}
interface ConnectionSyncConfigSyncToCalendar extends ConnectionSyncConfigSyncToCalendarSyncToOneOf {
    /**
     * An empty `primaryCalendar` object indicates that Wix events are
     * exported only to the primary account of the external calendar.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * An empty `dedicatedCalendar` object indicates that Wix events are
     * exported only to the dedicated account of the external calendar.
     */
    dedicatedCalendar?: DedicatedCalendar;
    /**
     * Whether Wix *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * are exported to the external calendar.
     */
    enabled?: boolean | null;
}
/** @oneof */
interface ConnectionSyncConfigSyncToCalendarSyncToOneOf {
    /**
     * An empty `primaryCalendar` object indicates that Wix events are
     * exported only to the primary account of the external calendar.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * An empty `dedicatedCalendar` object indicates that Wix events are
     * exported only to the dedicated account of the external calendar.
     */
    dedicatedCalendar?: DedicatedCalendar;
}
interface ListConnectionsRequest {
    /**
     * _Schedule IDs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to filter by.
     *
     * Default: Returns all connections.
     * @format GUID
     */
    scheduleIds?: string[] | null;
    /**
     * Whether to return a partial list of connections if details can't be
     * retrieved for all connections.
     *
     * Default: `false`
     */
    partialFailure?: boolean | null;
}
interface ListConnectionsResponse {
    /** Retrieved external calendar connections. */
    connections?: Connection[];
    /**
     * List of provider IDs for which connection retrieval failed. Returned only
     * if you specify `{"partialFailure": true}`.
     */
    failedProviderIds?: string[] | null;
}
interface ConnectByOAuthRequest {
    /**
     * ID of the schedule to connect with the external calendar account.
     * @format GUID
     */
    scheduleId: string | null;
    /**
     * ID of the external calendar provider. Find this with the [List Providers](#list-providers) endpoint.
     * @format GUID
     */
    providerId: string | null;
    /**
     * URL to redirect the user to after they authorize access to the external calendar account.
     *
     * If the connection is successfully established, the user is redirected to this URL with a query parameter `connectionId` containing the new connection ID.
     * If the attempt to connect fails, the user is redirected to this URL with a query parameter `error` containing the error type.
     * @format WEB_URL
     */
    redirectUrl: string | null;
}
interface ConnectByOAuthResponse {
    /**
     * URL of the external calendar authorization page to redirect the user to.
     * @format WEB_URL
     */
    oauthUrl?: string | null;
}
interface ConnectByCredentialsRequest {
    /**
     * ID of the schedule to connect with the external calendar account.
     * @format GUID
     */
    scheduleId: string | null;
    /**
     * ID of the external calendar provider. Find this with the [List Providers](#list-providers) endpoint.
     * @format GUID
     */
    providerId: string | null;
    /**
     * Email address for the external calendar account.
     * @format EMAIL
     */
    email: string | null;
    /** Password for the external calendar account. */
    password: string | null;
}
interface ConnectByCredentialsResponse {
    /** Established connection details. */
    connection?: Connection;
}
interface ListCalendarsRequest {
    /** ID of the external calendar connection to list calendars for. */
    connectionId: string | null;
}
interface ListCalendarsResponse {
    /** List of calendars belonging to the external calendar account. */
    calendars?: Calendar[];
}
interface UpdateSyncConfigRequest {
    /** ID of the external calendar connection to update. */
    connectionId: string | null;
    /** Updated sync configuration details. */
    syncConfig: ConnectionSyncConfig;
}
interface UpdateSyncConfigResponse {
    /** Connection with updated sync configuration. */
    connection?: Connection;
}
interface DisconnectRequest {
    /** ID of the external calendar connection to disconnect. */
    connectionId: string | null;
}
interface DisconnectResponse {
    /** Updated connection details. */
    connection?: Connection;
}
interface ListEventsRequest {
    /**
     * Date and time from which to retrieve events,
     * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).
     * Required, unless `cursorPaging.cursor` is provided.
     *
     * Events which start before the `from` time and end after it are included in the returned list.
     */
    from?: string | null;
    /**
     * Date and time until which to retrieve events,
     * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).
     * Required, unless `cursorPaging.cursor` is provided.
     *
     * Events which start before the `to` time and end after it are included in the returned list.
     */
    to?: string | null;
    /**
     * Schedule IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to the specified schedules.
     * Maximum of 100 schedule IDs per request.
     * @format GUID
     * @maxSize 100
     */
    scheduleIds?: string[] | null;
    /**
     * Wix user IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to schedules belonging to the specified Wix users.
     * Maximum of 100 Wix user IDs per request.
     * @format GUID
     * @maxSize 100
     */
    userIds?: string[] | null;
    /**
     * Whether to include only all-day events in the returned list.
     * If `true`, only all-day events are returned.
     * If `false`, only events with a specified time are returned.
     *
     * Default: All events are returned.
     */
    allDay?: boolean | null;
    /**
     * Predefined sets of fields to return.
     * - `NO_PI`: Returns event objects without personal information.
     * - `OWN_PI`: Returns complete event objects, including personal information.
     *
     * Default: `NO_PI`
     */
    fieldsets?: string[];
    /** Pagination options. */
    cursorPaging?: CursorPaging;
    /**
     * Whether to return a partial list of events if details can't be retrieved for some connections.
     *
     * Default: `false`
     */
    partialFailure?: boolean | null;
}
interface CursorPaging {
    /**
     * Number of events to load.
     * Max: `1000`
     * @max 1000
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     */
    cursor?: string | null;
}
interface ListEventsResponse {
    /** List of external calendar events matching the filters. */
    events?: Event[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
    /** List of provider IDs for connections for which retrieval of events failed. Returned only if `partialFailure` body parameter is `true` in the request. */
    failedProviderIds?: string[] | null;
}
/** An external calendar event. */
interface Event {
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to which the event belongs.
     * @format GUID
     */
    scheduleId?: string | null;
    /** External calendar type. */
    calendarType?: CalendarTypeWithLiterals;
    /**
     * Display name of the external calendar.
     * For example, `Primary` or `Birthdays`.
     */
    calendarName?: string | null;
    /** Event title. */
    title?: string | null;
    /** Start date and time of the event (inclusive), formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt). */
    start?: string | null;
    /** End date and time of the event (exclusive), formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt). */
    end?: string | null;
    /**
     * Whether the event is an all-day event.
     *
     * Default: `false`
     */
    allDay?: boolean | null;
    /**
     * ID of the *Wix user*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))
     * to whom the schedule belongs.
     * For Bookings *staff members*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * identical to their *resource ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).
     */
    scheduleOwnerId?: string | null;
    /**
     * Name of the *Wix user*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))
     * to whom the schedule belongs.
     * For example, the `name` of a Bookings *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)).
     */
    scheduleOwnerName?: string | null;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /** Cursor pointing to next page in the list of results. */
    next?: string | null;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function listProviders(): __PublicMethodMetaInfo<'GET', {}, ListProvidersRequest$1, ListProvidersRequest, ListProvidersResponse$1, ListProvidersResponse>;
declare function getConnection(): __PublicMethodMetaInfo<'GET', {
    connectionId: string;
}, GetConnectionRequest$1, GetConnectionRequest, GetConnectionResponse$1, GetConnectionResponse>;
declare function listConnections(): __PublicMethodMetaInfo<'GET', {}, ListConnectionsRequest$1, ListConnectionsRequest, ListConnectionsResponse$1, ListConnectionsResponse>;
declare function connectByOAuth(): __PublicMethodMetaInfo<'POST', {}, ConnectByOAuthRequest$1, ConnectByOAuthRequest, ConnectByOAuthResponse$1, ConnectByOAuthResponse>;
declare function connectByCredentials(): __PublicMethodMetaInfo<'POST', {}, ConnectByCredentialsRequest$1, ConnectByCredentialsRequest, ConnectByCredentialsResponse$1, ConnectByCredentialsResponse>;
declare function listCalendars(): __PublicMethodMetaInfo<'GET', {
    connectionId: string;
}, ListCalendarsRequest$1, ListCalendarsRequest, ListCalendarsResponse$1, ListCalendarsResponse>;
declare function updateSyncConfig(): __PublicMethodMetaInfo<'PATCH', {
    connectionId: string;
}, UpdateSyncConfigRequest$1, UpdateSyncConfigRequest, UpdateSyncConfigResponse$1, UpdateSyncConfigResponse>;
declare function disconnect(): __PublicMethodMetaInfo<'POST', {
    connectionId: string;
}, DisconnectRequest$1, DisconnectRequest, DisconnectResponse$1, DisconnectResponse>;
declare function listEvents(): __PublicMethodMetaInfo<'GET', {}, ListEventsRequest$1, ListEventsRequest, ListEventsResponse$1, ListEventsResponse>;

export { type __PublicMethodMetaInfo, connectByCredentials, connectByOAuth, disconnect, getConnection, listCalendars, listConnections, listEvents, listProviders, updateSyncConfig };
