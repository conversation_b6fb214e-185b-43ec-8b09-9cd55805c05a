import { CreateBookingPolicyRequest as CreateBookingPolicyRequest$1, CreateBookingPolicyResponse as CreateBookingPolicyResponse$1, GetBookingPolicyRequest as GetBookingPolicyRequest$1, GetBookingPolicyResponse as GetBookingPolicyResponse$1, GetStrictestBookingPolicyRequest as GetStrictestBookingPolicyRequest$1, GetStrictestBookingPolicyResponse as GetStrictestBookingPolicyResponse$1, UpdateBookingPolicyRequest as UpdateBookingPolicyRequest$1, UpdateBookingPolicyResponse as UpdateBookingPolicyResponse$1, SetDefaultBookingPolicyRequest as SetDefaultBookingPolicyRequest$1, SetDefaultBookingPolicyResponse as SetDefaultBookingPolicyResponse$1, DeleteBookingPolicyRequest as DeleteBookingPolicyRequest$1, DeleteBookingPolicyResponse as DeleteBookingPolicyResponse$1, QueryBookingPoliciesRequest as QueryBookingPoliciesRequest$1, QueryBookingPoliciesResponse as QueryBookingPoliciesResponse$1, CountBookingPoliciesRequest as CountBookingPoliciesRequest$1, CountBookingPoliciesResponse as CountBookingPoliciesResponse$1 } from './index.typings.mjs';
import '@wix/sdk-types';

/**
 * A booking policy is a set of rules that determine how customers can book a
 * service, including timeframes for booking, canceling, or rescheduling.
 */
interface BookingPolicy {
    /**
     * ID of the booking policy.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking policy is updated.
     * To prevent conflicting changes, the current `revision` must be passed when
     * updating the booking policy.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the booking policy was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Date and time the booking policy was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Name of the booking policy.
     * @maxLength 400
     */
    name?: string | null;
    /**
     * Custom description for the booking policy and whether it's displayed to the
     * participant.
     */
    customPolicyDescription?: PolicyDescription;
    /**
     * Whether the booking policy is the default.
     * @readonly
     */
    default?: boolean | null;
    /** Rule for limiting early bookings. */
    limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;
    /**
     * Rule for limiting late bookings. This rule and `bookAfterStartPolicy` can't
     * be both enabled at the same time.
     */
    limitLateBookingPolicy?: LimitLateBookingPolicy;
    /**
     * Rule for booking after the start of a session or course. This rule and
     * `limitLateBookingPolicy` can't be both enabled at the same time.
     */
    bookAfterStartPolicy?: BookAfterStartPolicy;
    /** Rule for canceling a booking. */
    cancellationPolicy?: CancellationPolicy;
    /** Rule for rescheduling a booking. */
    reschedulePolicy?: ReschedulePolicy;
    /** Waitlist rule for the service. */
    waitlistPolicy?: WaitlistPolicy;
    /** Rule for participants per booking. */
    participantsPolicy?: ParticipantsPolicy;
    /** Rules for cancellation fees. */
    cancellationFeePolicy?: CancellationFeePolicy;
    /** Rule for saving credit card details. */
    saveCreditCardPolicy?: SaveCreditCardPolicy;
    /** Extensions enabling users to save custom data related to the booking policies. */
    extendedFields?: ExtendedFields;
}
/** A description of the booking policy to display to participants. */
interface PolicyDescription {
    /**
     * Whether the description is displayed to the participant. `true` means the
     * description is displayed.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Description of the booking policy.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    description?: string;
}
/** The rule for limiting early bookings. */
interface LimitEarlyBookingPolicy {
    /**
     * Whether there's a limit about how early a customer can book. `false` means there's
     * no limit to the earliest supported booking time.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Maximum number of minutes before the start of a session or course customers can book.
     * Must be greater than `limitLateBookingPolicy.latestBookingInMinutes`.
     *
     * Default: `10080` minutes (7 days)
     * Min: `1` minute
     * @min 1
     */
    earliestBookingInMinutes?: number;
}
/**
 * Rule limiting late bookings.
 *
 * This rule and `bookAfterStartPolicy` can't be both enabled at the same time.
 */
interface LimitLateBookingPolicy {
    /**
     * Whether there's a limit about how late customers can book. `false` means
     * customers can book up to the last minute. If specified as `true`,
     * `bookAfterStartPolicy.enabled` must be `false`.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Minimum number of minutes before the start of the session customers can book.
     * For courses, this is relative to the start time of the next session and doesn't
     * consider course sessions in the past. This value must be less than
     * `limitEarlyBookingPolicy.earliestBookingInMinutes`.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestBookingInMinutes?: number;
}
/**
 * The rule for whether a session can be booked after the start of the schedule.
 * This rule and `LimitLateBookingPolicy` cannot be enabled at the same time. So if this rule
 * is enabled, the `LimitLateBookingPolicy` rule must be disabled.
 */
interface BookAfterStartPolicy {
    /**
     * Whether customers can book after the session has started. `true` means
     * customers can book after the session has started. For courses, this refers to
     * the start of the last course session. If specified as `true`,
     * `limitLateBookingPolicy.enabled` must be `false`.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
/** The rule for canceling a booked session. */
interface CancellationPolicy {
    /**
     * Whether customers can cancel the booking. `true` means customers can cancel
     * the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest cancellation time. `false` means customers
     * can cancel the booking until the last minute before the course or session starts.
     *
     * Default: `false`
     */
    limitLatestCancellation?: boolean;
    /**
     * Minimum number of minutes before the start of the session customers can cancel.
     * For courses, this refers to the start of the first course session.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestCancellationInMinutes?: number;
}
/** The rule for rescheduling a booked session. */
interface ReschedulePolicy {
    /**
     * Whether customers can reschedule a booking for an appointment-based service.
     * `true` means customers can reschedule.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest supported rescheduling time. `false`
     * means customers can reschedule until the last minute before the session start.
     *
     * Default: `false`
     */
    limitLatestReschedule?: boolean;
    /**
     * Minimum number of minutes before the session start session customers can
     * reschedule their booking.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestRescheduleInMinutes?: number;
}
/** The rule for the waitlist. */
interface WaitlistPolicy {
    /**
     * Whether the service has a waitlist. `true` means there's a waitlist.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Number of spots in the waitlist.
     *
     * Default: `10` spots
     * Min: `1` spot
     * @min 1
     */
    capacity?: number;
    /**
     * Time in minutes the potential customer is given to book after they've been
     * notified that a spot has opened up for them. If they don't respond in time,
     * the open spot is offered to the next potential customer on the waitlist.
     *
     * Default: `10` minutes
     * Min: `1` minute
     * @min 1
     */
    reservationTimeInMinutes?: number;
}
/** The rule for the maximum number of participants per booking. */
interface ParticipantsPolicy {
    /**
     * Maximum number of participants per booking.
     *
     * Default: `1` participant
     * Min: `1` participant
     * @min 1
     */
    maxParticipantsPerBooking?: number;
}
interface CancellationFeePolicy {
    /**
     * Whether customers must pay a cancellation fee when canceling a booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Time windows relative to the session start during which customers can cancel
     * their booking. Each window includes details about the fee for canceling within it.
     * @maxSize 2
     */
    cancellationWindows?: CancellationWindow[];
    /**
     * Whether Wix automatically charges the cancellation fee from the customer once
     * they cancel their booking.
     *
     * Default: `true`
     */
    autoCollectFeeEnabled?: boolean | null;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gt:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     * @readonly
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface CancellationWindow extends CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within
     * this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
    /**
     * Start of the cancellation window in minutes before the session start. For
     * courses, this refers to the start of the first course session.
     * @min 1
     */
    startInMinutes?: number | null;
}
/** @oneof */
interface CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within
     * this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
}
interface SaveCreditCardPolicy {
    /**
     * Whether Wix stores credit card details of the customer. Storing the details
     * allows Wix to prefill the *eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction))
     * and thus increases the likelihood that the customer completes the booking
     * process.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateBookingPolicyRequest {
    /** Booking policy to create. */
    bookingPolicy: BookingPolicy;
}
interface CreateBookingPolicyResponse {
    /** Created booking policy. */
    bookingPolicy?: BookingPolicy;
}
interface GetBookingPolicyRequest {
    /**
     * ID of the booking policy to retrieve.
     * @format GUID
     */
    bookingPolicyId: string;
}
interface GetBookingPolicyResponse {
    /** Retrieved booking policy. */
    bookingPolicy?: BookingPolicy;
}
interface GetStrictestBookingPolicyRequest {
    /**
     * IDs of the booking policies for which to retrieve the strictest rules for.
     * @format GUID
     * @maxSize 100
     */
    bookingPolicyIds: string[];
}
interface GetStrictestBookingPolicyResponse {
    /**
     * Hypothetical `bookingPolicy` object that combines the strictest version of
     * each rule. `bookingPolicy.id` is `null` and the returned object isn't actually
     * created. To create a new policy, you can call *Create Booking Policy*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).
     */
    bookingPolicy?: BookingPolicy;
}
interface UpdateBookingPolicyRequest {
    /** Booking policy to update. */
    bookingPolicy: BookingPolicy;
}
interface UpdateBookingPolicyResponse {
    /** Updated booking policy. */
    bookingPolicy?: BookingPolicy;
}
interface SetDefaultBookingPolicyRequest {
    /**
     * ID of the booking policy that's set as default.
     * @format GUID
     */
    bookingPolicyId: string;
}
interface SetDefaultBookingPolicyResponse {
    /** New default booking policy. */
    currentDefaultBookingPolicy?: BookingPolicy;
    /**
     * Previous default booking policy. Not available if the provided booking policy
     * was already the default.
     */
    previousDefaultBookingPolicy?: BookingPolicy;
}
interface DeleteBookingPolicyRequest {
    /**
     * ID of the booking policy to delete.
     * @format GUID
     */
    bookingPolicyId: string;
}
interface DeleteBookingPolicyResponse {
}
interface QueryBookingPoliciesRequest {
    /**
     * The query by which to select booking policies. See
     * [the supported filters article](https://dev.wix.com/docs/rest/business-solutions/bookings/services/booking-policy/supported-filters)
     * for details.
     */
    query: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 10
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryBookingPoliciesResponse {
    /** Retrieved booking policies. */
    bookingPolicies?: BookingPolicy[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor pointing to next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface CountBookingPoliciesRequest {
    /**
     * Filter to base the count on. See
     * supported filters*
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
     * for details.
     */
    filter?: Record<string, any> | null;
}
interface CountBookingPoliciesResponse {
    /** Number of booking policies matching the provided filter. */
    count?: number;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createBookingPolicy(): __PublicMethodMetaInfo<'POST', {}, CreateBookingPolicyRequest$1, CreateBookingPolicyRequest, CreateBookingPolicyResponse$1, CreateBookingPolicyResponse>;
declare function getBookingPolicy(): __PublicMethodMetaInfo<'GET', {
    bookingPolicyId: string;
}, GetBookingPolicyRequest$1, GetBookingPolicyRequest, GetBookingPolicyResponse$1, GetBookingPolicyResponse>;
declare function getStrictestBookingPolicy(): __PublicMethodMetaInfo<'POST', {}, GetStrictestBookingPolicyRequest$1, GetStrictestBookingPolicyRequest, GetStrictestBookingPolicyResponse$1, GetStrictestBookingPolicyResponse>;
declare function updateBookingPolicy(): __PublicMethodMetaInfo<'PATCH', {
    bookingPolicyId: string;
}, UpdateBookingPolicyRequest$1, UpdateBookingPolicyRequest, UpdateBookingPolicyResponse$1, UpdateBookingPolicyResponse>;
declare function setDefaultBookingPolicy(): __PublicMethodMetaInfo<'POST', {
    bookingPolicyId: string;
}, SetDefaultBookingPolicyRequest$1, SetDefaultBookingPolicyRequest, SetDefaultBookingPolicyResponse$1, SetDefaultBookingPolicyResponse>;
declare function deleteBookingPolicy(): __PublicMethodMetaInfo<'DELETE', {
    bookingPolicyId: string;
}, DeleteBookingPolicyRequest$1, DeleteBookingPolicyRequest, DeleteBookingPolicyResponse$1, DeleteBookingPolicyResponse>;
declare function queryBookingPolicies(): __PublicMethodMetaInfo<'POST', {}, QueryBookingPoliciesRequest$1, QueryBookingPoliciesRequest, QueryBookingPoliciesResponse$1, QueryBookingPoliciesResponse>;
declare function countBookingPolicies(): __PublicMethodMetaInfo<'POST', {}, CountBookingPoliciesRequest$1, CountBookingPoliciesRequest, CountBookingPoliciesResponse$1, CountBookingPoliciesResponse>;

export { type __PublicMethodMetaInfo, countBookingPolicies, createBookingPolicy, deleteBookingPolicy, getBookingPolicy, getStrictestBookingPolicy, queryBookingPolicies, setDefaultBookingPolicy, updateBookingPolicy };
