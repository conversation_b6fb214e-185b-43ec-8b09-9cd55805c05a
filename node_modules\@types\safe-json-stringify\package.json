{"name": "@types/safe-json-stringify", "version": "1.1.5", "description": "TypeScript definitions for safe-json-stringify", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/safe-json-stringify", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ericbyers"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/safe-json-stringify"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "b4df9708550d6faf866fabd8d75079793e2211fa1be5c23fb32e8055aa067859", "typeScriptVersion": "4.5"}