{"version": 3, "sources": ["../../../index.ts", "../../../src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.ts", "../../../src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.ts", "../../../src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.public.ts", "../../../src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.context.ts"], "sourcesContent": ["export * from './src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.context.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshot from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.js';\n\n/**\n * The `bookingPolicySnapshot` object is the version of a service's booking policy\n * at the time a booking is created. This allows you for example to charge\n * customers the correct cancellation fee even after a service's cancellation\n * policy has been updated.\n */\nexport interface BookingPolicySnapshot {\n  /**\n   * Booking policy snapshot ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * ID of the booking that's associated with this policy snapshot.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /**\n   * Snapshot of the [booking policy](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/get-booking-policy).\n   * at the time the corresponding booking was created.\n   */\n  policy?: BookingPolicy;\n  /**\n   * Date and time the booking policy snapshot was created in `YYYY-MM-DDThh:mm:ssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n}\n\n/**\n * `BookingPolicy` is the main entity of `BookingPolicyService` and specifies a set of rules for booking a service\n * by visitors and members.\n *\n * Each `BookingPolicy` consists of a number of sub-policies. When the Bookings App is provisioned to a meta site then a\n * default `BookingPolicy` will be created with defaults for each of these sub-policies. This also applies when a request\n * is received to create a new `BookingPolicy` and one or more of these sub-policies are not provided.\n *\n * Sub-policies are defined in separate objects as specified below.\n * - The `CancellationPolicy` object defines the policy for canceling a booked session.\n * - The `ReschedulePolicy` object defines the policy for rescheduling booked session.\n *\n * By default each sub-policy is disabled. A more detailed specification of the default settings of each sub-policy\n * can be found in the description of the corresponding object.\n *\n * Partial updates are supported on the main entity level, however in order to update a sub-policy the client needs to provide the whole sub-policy object.\n */\nexport interface BookingPolicy {\n  /**\n   * Booking policy ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the booking policy is updated. To prevent conflicting changes, the current `revision` must be passed when updating the booking policy.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Date and time the booking policy was created.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the booking policy was updated.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the booking policy.\n   * @maxLength 400\n   */\n  name?: string | null;\n  /** Custom description for the booking policy and whether the booking policy is displayed to the participant. */\n  customPolicyDescription?: PolicyDescription;\n  /**\n   * Whether the booking policy is the default.\n   * @readonly\n   */\n  default?: boolean | null;\n  /** Rule for canceling a booking. */\n  cancellationPolicy?: CancellationPolicy;\n  /** Rule for rescheduling a booking. */\n  reschedulePolicy?: ReschedulePolicy;\n  /** Rules for cancellation fees. */\n  cancellationFeePolicy?: CancellationFeePolicy;\n  /** Rule for saving credit card details. */\n  saveCreditCardPolicy?: SaveCreditCardPolicy;\n}\n\n/** A description of the booking policy to display to participants. */\nexport interface PolicyDescription {\n  /**\n   * Whether the description is displayed to the participant. `true` means the\n   * description is displayed.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Description of the booking policy.\n   *\n   * Default: Empty\n   * Max length: 2500 characters\n   * @maxLength 2500\n   */\n  description?: string;\n  /**\n   * Description of the translated booking policy.\n   *\n   * Default: Empty\n   * Max length: 2500 characters\n   * @maxLength 2500\n   */\n  descriptionTranslated?: string | null;\n}\n\n/** The rule for canceling a booked session. */\nexport interface CancellationPolicy {\n  /**\n   * Whether customers can cancel the booking. `true` means customers can cancel\n   * the booking.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Whether there's a limit on the latest cancellation time. `false` means\n   * customers can cancel the booking until the last minute before the course or\n   * session starts.\n   *\n   * Default: `false`\n   */\n  limitLatestCancellation?: boolean;\n  /**\n   * Minimum number of minutes before the start of the session customers can cancel.\n   * For courses, this refers to the start of the first course session.\n   *\n   * Default: `1440` minutes (1 day)\n   * Min: `1` minute\n   * @min 1\n   */\n  latestCancellationInMinutes?: number;\n}\n\n/** The rule for rescheduling a booked session. */\nexport interface ReschedulePolicy {\n  /**\n   * Whether customers can reschedule a booking for an appointment-based service.\n   * `true` means customers can reschedule.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Whether there's a limit on the latest supported rescheduling time. `false`\n   * means customers can reschedule until the last minute before the session start.\n   *\n   * Default: `false`\n   */\n  limitLatestReschedule?: boolean;\n  /**\n   * Minimum number of minutes before the session start session customers can\n   * reschedule their booking.\n   *\n   * Default: `1440` minutes (1 day)\n   * Min: `1` minute\n   * @min 1\n   */\n  latestRescheduleInMinutes?: number;\n}\n\nexport interface CancellationFeePolicy {\n  /**\n   * Whether customers must pay a cancellation fee when canceling a booking.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Time windows relative to the session start during which customers can cancel\n   * their booking. Each window includes details about the fee for canceling\n   * within it.\n   * @maxSize 2\n   */\n  cancellationWindows?: CancellationWindow[];\n  /**\n   * Whether Wix automatically charges the cancellation fee from the customer once\n   * they cancel their booking.\n   *\n   * Default: `true`\n   */\n  autoCollectFeeEnabled?: boolean | null;\n}\n\n/**\n * Money.\n * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.\n */\nexport interface Money {\n  /**\n   * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.\n   * @format DECIMAL_VALUE\n   * @decimalValue options { gt:0, maxScale:2 }\n   */\n  value?: string;\n  /**\n   * Currency code. Must be valid ISO 4217 currency code (e.g., USD).\n   * @format CURRENCY\n   */\n  currency?: string;\n  /**\n   * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.\n   * @maxLength 50\n   */\n  formattedValue?: string | null;\n}\n\nexport interface CancellationWindow extends CancellationWindowFeeOneOf {\n  /** Fixed amount customers must pay when canceling the booking within this window. */\n  amount?: Money;\n  /**\n   * Percentage of the booking price customers must pay when canceling within this window.\n   *\n   * Min: `0.01` percent\n   * Max: `100` percent\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentage?: string;\n  /**\n   * Start of the cancellation window in minutes before the session start. For\n   * courses, this refers to the start of the first course session.\n   * @min 1\n   */\n  startInMinutes?: number | null;\n}\n\n/** @oneof */\nexport interface CancellationWindowFeeOneOf {\n  /** Fixed amount customers must pay when canceling the booking within this window. */\n  amount?: Money;\n  /**\n   * Percentage of the booking price customers must pay when canceling within this window.\n   *\n   * Min: `0.01` percent\n   * Max: `100` percent\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentage?: string;\n}\n\nexport interface SaveCreditCardPolicy {\n  /**\n   * Whether Wix stores credit card details of the customer. Storing the details\n   * allows Wix to prefill the checkout and thus increases the likelihood that the\n   * customer completes the booking process.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n}\n\nexport interface ListPolicySnapshotsByBookingIdsRequest {\n  /**\n   * List of booking IDs to retrieve policy snapshots for.\n   * @minSize 1\n   * @maxSize 100\n   * @format GUID\n   */\n  bookingIds: string[] | null;\n}\n\nexport interface ListPolicySnapshotsByBookingIdsResponse {\n  /** Retrieved booking policy snapshots. */\n  bookingPolicySnapshots?: BookingPolicySnapshot[];\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface Empty {}\n\n/** @docsIgnore */\nexport type ListPolicySnapshotsByBookingIdsApplicationErrors = {\n  code?: 'BOOKING_POLICY_SNAPSHOT_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n\n/**\n * Retrieves a list of booking policy snapshots by booking IDs\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).\n * @param bookingIds - List of booking IDs to retrieve policy snapshots for.\n * @public\n * @requiredField bookingIds\n * @permissionId BOOKINGS.BOOKING_POLICY_SNAPSHOT_READ\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds\n */\nexport async function listPolicySnapshotsByBookingIds(\n  bookingIds: string[]\n): Promise<\n  NonNullablePaths<\n    ListPolicySnapshotsByBookingIdsResponse,\n    | `bookingPolicySnapshots`\n    | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.enabled`\n    | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.description`\n    | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.enabled`\n    | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.limitLatestCancellation`\n    | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.latestCancellationInMinutes`\n    | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.enabled`\n    | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.limitLatestReschedule`\n    | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.latestRescheduleInMinutes`\n    | `bookingPolicySnapshots.${number}.policy.cancellationFeePolicy.enabled`\n    | `bookingPolicySnapshots.${number}.policy.saveCreditCardPolicy.enabled`,\n    6\n  > & {\n    __applicationErrorsType?: ListPolicySnapshotsByBookingIdsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingIds: bookingIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshot.listPolicySnapshotsByBookingIds(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingIds: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingIds']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/booking-policy-snapshots',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/booking-policy-snapshots',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/booking-policy-snapshots',\n        destPath: '',\n      },\n      {\n        srcPath: '/booking-policy-snapshots',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_booking-policy-snapshots';\n\n/**\n * Retrieves a list of booking policy snapshots by booking IDs\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).\n */\nexport function listPolicySnapshotsByBookingIds(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listPolicySnapshotsByBookingIds({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.policy_snapshots.v1.booking_policy_snapshot',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(\n        { protoPath: '/v1/policy-snapshots', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicySnapshots.createdDate' },\n              { path: 'bookingPolicySnapshots.policy.createdDate' },\n              { path: 'bookingPolicySnapshots.policy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listPolicySnapshotsByBookingIds;\n}\n", "import { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  ListPolicySnapshotsByBookingIdsApplicationErrors,\n  ListPolicySnapshotsByBookingIdsResponse,\n  listPolicySnapshotsByBookingIds as universalListPolicySnapshotsByBookingIds,\n} from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function listPolicySnapshotsByBookingIds(\n  httpClient: HttpClient\n): ListPolicySnapshotsByBookingIdsSignature {\n  return (bookingIds: string[]) =>\n    universalListPolicySnapshotsByBookingIds(\n      bookingIds,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ListPolicySnapshotsByBookingIdsSignature {\n  /**\n   * Retrieves a list of booking policy snapshots by booking IDs\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).\n   * @param - List of booking IDs to retrieve policy snapshots for.\n   */\n  (bookingIds: string[]): Promise<\n    NonNullablePaths<\n      ListPolicySnapshotsByBookingIdsResponse,\n      | `bookingPolicySnapshots`\n      | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.enabled`\n      | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.description`\n      | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.enabled`\n      | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.limitLatestCancellation`\n      | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.latestCancellationInMinutes`\n      | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.enabled`\n      | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.limitLatestReschedule`\n      | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.latestRescheduleInMinutes`\n      | `bookingPolicySnapshots.${number}.policy.cancellationFeePolicy.enabled`\n      | `bookingPolicySnapshots.${number}.policy.saveCreditCardPolicy.enabled`,\n      6\n    > & {\n      __applicationErrorsType?: ListPolicySnapshotsByBookingIdsApplicationErrors;\n    }\n  >;\n}\n\nexport {\n  ActionEvent,\n  BookingPolicy,\n  BookingPolicySnapshot,\n  CancellationFeePolicy,\n  CancellationPolicy,\n  CancellationWindow,\n  CancellationWindowFeeOneOf,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  Empty,\n  EntityCreatedEvent,\n  EntityDeletedEvent,\n  EntityUpdatedEvent,\n  ListPolicySnapshotsByBookingIdsRequest,\n  ListPolicySnapshotsByBookingIdsResponse,\n  Money,\n  PolicyDescription,\n  ReschedulePolicy,\n  RestoreInfo,\n  SaveCreditCardPolicy,\n} from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.js';\n", "import { listPolicySnapshotsByBookingIds as publicListPolicySnapshotsByBookingIds } from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { BuildRESTFunction, MaybeContext } from '@wix/sdk-types';\n\nexport const listPolicySnapshotsByBookingIds: MaybeContext<\n  BuildRESTFunction<typeof publicListPolicySnapshotsByBookingIds> &\n    typeof publicListPolicySnapshotsByBookingIds\n> = /*#__PURE__*/ createRESTModule(publicListPolicySnapshotsByBookingIds);\n\nexport {\n  BookingPolicySnapshot,\n  BookingPolicy,\n  PolicyDescription,\n  CancellationPolicy,\n  ReschedulePolicy,\n  CancellationFeePolicy,\n  Money,\n  CancellationWindow,\n  CancellationWindowFeeOneOf,\n  SaveCreditCardPolicy,\n  ListPolicySnapshotsByBookingIdsRequest,\n  ListPolicySnapshotsByBookingIdsResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  RestoreInfo,\n  EntityUpdatedEvent,\n  EntityDeletedEvent,\n  ActionEvent,\n  Empty,\n} from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.js';\nexport { ListPolicySnapshotsByBookingIdsApplicationErrors } from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,yCAAAA;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,oCAGO;;;ACJP,0BAAkC;AAClC,uBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,qEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAMd,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,wBAAwB,MAAM,SAAS,KAAK;AAAA,MAC3D;AAAA,MACA,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,4CAA4C;AAAA,UACtD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AD2SA,eAAsBC,iCACpB,YAmBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACwD;AAAA,IAC1D;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,YAAY,OAAO;AAAA,QAC/C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY;AAAA,IACf;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;AEraO,SAASC,iCACd,YAC0C;AAC1C,SAAO,CAAC,eACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;;;ACjBA,IAAAC,uBAAiC;AAG1B,IAAMC,mCAGK,2DAAiBA,gCAAqC;", "names": ["listPolicySnapshotsByBookingIds", "import_rest_modules", "payload", "listPolicySnapshotsByBookingIds", "sdkTransformError", "listPolicySnapshotsByBookingIds", "import_rest_modules", "listPolicySnapshotsByBookingIds"]}