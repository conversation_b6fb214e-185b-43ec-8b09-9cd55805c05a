{"version": 3, "sources": ["../../src/bookings-v1-booking-policy-booking-policies.public.ts", "../../src/bookings-v1-booking-policy-booking-policies.universal.ts", "../../src/bookings-v1-booking-policy-booking-policies.http.ts", "../../src/bookings-v1-booking-policy-booking-policies.context.ts"], "sourcesContent": ["import { renameKeysFromRESTResponseToSDKResponse } from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { EventDefinition, HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  BookingPoliciesQueryBuilder,\n  BookingPolicy,\n  BookingPolicyCreatedEnvelope,\n  BookingPolicyDefaultBookingPolicySetEnvelope,\n  BookingPolicyDeletedEnvelope,\n  BookingPolicyUpdatedEnvelope,\n  CountBookingPoliciesOptions,\n  CountBookingPoliciesResponse,\n  CreateBookingPolicyValidationErrors,\n  DeleteBookingPolicyApplicationErrors,\n  GetStrictestBookingPolicyResponse,\n  SetDefaultBookingPolicyResponse,\n  UpdateBookingPolicy,\n  UpdateBookingPolicyValidationErrors,\n  countBookingPolicies as universalCountBookingPolicies,\n  createBookingPolicy as universalCreateBookingPolicy,\n  deleteBookingPolicy as universalDeleteBookingPolicy,\n  getBookingPolicy as universalGetBookingPolicy,\n  getStrictestBookingPolicy as universalGetStrictestBookingPolicy,\n  queryBookingPolicies as universalQueryBookingPolicies,\n  setDefaultBookingPolicy as universalSetDefaultBookingPolicy,\n  updateBookingPolicy as universalUpdateBookingPolicy,\n} from './bookings-v1-booking-policy-booking-policies.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function createBookingPolicy(\n  httpClient: HttpClient\n): CreateBookingPolicySignature {\n  return (bookingPolicy: BookingPolicy) =>\n    universalCreateBookingPolicy(\n      bookingPolicy,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CreateBookingPolicySignature {\n  /**\n   * Creates a booking policy.\n   * @param - Booking policy to create.\n   * @returns Created booking policy.\n   */\n  (bookingPolicy: BookingPolicy): Promise<\n    NonNullablePaths<\n      BookingPolicy,\n      | `customPolicyDescription.enabled`\n      | `customPolicyDescription.description`\n      | `limitEarlyBookingPolicy.enabled`\n      | `limitEarlyBookingPolicy.earliestBookingInMinutes`\n      | `limitLateBookingPolicy.enabled`\n      | `limitLateBookingPolicy.latestBookingInMinutes`\n      | `bookAfterStartPolicy.enabled`\n      | `cancellationPolicy.enabled`\n      | `cancellationPolicy.limitLatestCancellation`\n      | `cancellationPolicy.latestCancellationInMinutes`\n      | `reschedulePolicy.enabled`\n      | `reschedulePolicy.limitLatestReschedule`\n      | `reschedulePolicy.latestRescheduleInMinutes`\n      | `waitlistPolicy.enabled`\n      | `waitlistPolicy.capacity`\n      | `waitlistPolicy.reservationTimeInMinutes`\n      | `participantsPolicy.maxParticipantsPerBooking`\n      | `cancellationFeePolicy.enabled`\n      | `cancellationFeePolicy.cancellationWindows`\n      | `cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n      | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n      | `cancellationFeePolicy.cancellationWindows.${number}.percentage`\n      | `saveCreditCardPolicy.enabled`,\n      6\n    > & {\n      __validationErrorsType?: CreateBookingPolicyValidationErrors;\n    }\n  >;\n}\n\nexport function getBookingPolicy(\n  httpClient: HttpClient\n): GetBookingPolicySignature {\n  return (bookingPolicyId: string) =>\n    universalGetBookingPolicy(\n      bookingPolicyId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetBookingPolicySignature {\n  /**\n   * Retrieves a booking policy.\n   * @param - ID of the booking policy to retrieve.\n   * @returns Retrieved booking policy.\n   */\n  (bookingPolicyId: string): Promise<\n    NonNullablePaths<\n      BookingPolicy,\n      | `customPolicyDescription.enabled`\n      | `customPolicyDescription.description`\n      | `limitEarlyBookingPolicy.enabled`\n      | `limitEarlyBookingPolicy.earliestBookingInMinutes`\n      | `limitLateBookingPolicy.enabled`\n      | `limitLateBookingPolicy.latestBookingInMinutes`\n      | `bookAfterStartPolicy.enabled`\n      | `cancellationPolicy.enabled`\n      | `cancellationPolicy.limitLatestCancellation`\n      | `cancellationPolicy.latestCancellationInMinutes`\n      | `reschedulePolicy.enabled`\n      | `reschedulePolicy.limitLatestReschedule`\n      | `reschedulePolicy.latestRescheduleInMinutes`\n      | `waitlistPolicy.enabled`\n      | `waitlistPolicy.capacity`\n      | `waitlistPolicy.reservationTimeInMinutes`\n      | `participantsPolicy.maxParticipantsPerBooking`\n      | `cancellationFeePolicy.enabled`\n      | `cancellationFeePolicy.cancellationWindows`\n      | `cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n      | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n      | `cancellationFeePolicy.cancellationWindows.${number}.percentage`\n      | `saveCreditCardPolicy.enabled`,\n      6\n    >\n  >;\n}\n\nexport function getStrictestBookingPolicy(\n  httpClient: HttpClient\n): GetStrictestBookingPolicySignature {\n  return (bookingPolicyIds: string[]) =>\n    universalGetStrictestBookingPolicy(\n      bookingPolicyIds,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetStrictestBookingPolicySignature {\n  /**\n   * Retrieves the strictest version of each policy rule from a list of booking\n   * policies.\n   *\n   *\n   * Returns a hypothetical `bookingPolicy` object that combines the strictest\n   * version of each rule. The `id` of the returned policy is `null` and no\n   * corresponding `bookingPolicy` object is created. To create a new policy, you\n   * can call *Create Booking Policy*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).\n   * @param - IDs of the booking policies for which to retrieve the strictest rules for.\n   */\n  (bookingPolicyIds: string[]): Promise<\n    NonNullablePaths<\n      GetStrictestBookingPolicyResponse,\n      | `bookingPolicy.customPolicyDescription.enabled`\n      | `bookingPolicy.customPolicyDescription.description`\n      | `bookingPolicy.limitEarlyBookingPolicy.enabled`\n      | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n      | `bookingPolicy.limitLateBookingPolicy.enabled`\n      | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n      | `bookingPolicy.bookAfterStartPolicy.enabled`\n      | `bookingPolicy.cancellationPolicy.enabled`\n      | `bookingPolicy.cancellationPolicy.limitLatestCancellation`\n      | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n      | `bookingPolicy.reschedulePolicy.enabled`\n      | `bookingPolicy.reschedulePolicy.limitLatestReschedule`\n      | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n      | `bookingPolicy.waitlistPolicy.enabled`\n      | `bookingPolicy.waitlistPolicy.capacity`\n      | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n      | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n      | `bookingPolicy.cancellationFeePolicy.enabled`\n      | `bookingPolicy.cancellationFeePolicy.cancellationWindows`\n      | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n      | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n      | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n      | `bookingPolicy.saveCreditCardPolicy.enabled`,\n      7\n    >\n  >;\n}\n\nexport function updateBookingPolicy(\n  httpClient: HttpClient\n): UpdateBookingPolicySignature {\n  return (\n    _id: string,\n    bookingPolicy: NonNullablePaths<UpdateBookingPolicy, `revision`, 2>\n  ) =>\n    universalUpdateBookingPolicy(\n      _id,\n      bookingPolicy,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateBookingPolicySignature {\n  /**\n   * Updates a booking policy.\n   *\n   *\n   * Each time the booking policy is updated, `revision` increments by 1.\n   * The current `revision` must be specified when updating the booking policy.\n   * This ensures you're working with the latest booking policy and prevents unintended overwrites.\n   * @param - ID of the booking policy.\n   * @returns Updated booking policy.\n   */\n  (\n    _id: string,\n    bookingPolicy: NonNullablePaths<UpdateBookingPolicy, `revision`, 2>\n  ): Promise<\n    NonNullablePaths<\n      BookingPolicy,\n      | `customPolicyDescription.enabled`\n      | `customPolicyDescription.description`\n      | `limitEarlyBookingPolicy.enabled`\n      | `limitEarlyBookingPolicy.earliestBookingInMinutes`\n      | `limitLateBookingPolicy.enabled`\n      | `limitLateBookingPolicy.latestBookingInMinutes`\n      | `bookAfterStartPolicy.enabled`\n      | `cancellationPolicy.enabled`\n      | `cancellationPolicy.limitLatestCancellation`\n      | `cancellationPolicy.latestCancellationInMinutes`\n      | `reschedulePolicy.enabled`\n      | `reschedulePolicy.limitLatestReschedule`\n      | `reschedulePolicy.latestRescheduleInMinutes`\n      | `waitlistPolicy.enabled`\n      | `waitlistPolicy.capacity`\n      | `waitlistPolicy.reservationTimeInMinutes`\n      | `participantsPolicy.maxParticipantsPerBooking`\n      | `cancellationFeePolicy.enabled`\n      | `cancellationFeePolicy.cancellationWindows`\n      | `cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n      | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n      | `cancellationFeePolicy.cancellationWindows.${number}.percentage`\n      | `saveCreditCardPolicy.enabled`,\n      6\n    > & {\n      __validationErrorsType?: UpdateBookingPolicyValidationErrors;\n    }\n  >;\n}\n\nexport function setDefaultBookingPolicy(\n  httpClient: HttpClient\n): SetDefaultBookingPolicySignature {\n  return (bookingPolicyId: string) =>\n    universalSetDefaultBookingPolicy(\n      bookingPolicyId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface SetDefaultBookingPolicySignature {\n  /**\n   * Sets a booking policy as the default.\n   *\n   *\n   * Also updates the site's current default policy by setting its `default`\n   * attribute to `false`. If the provided policy is already the site's\n   * default, the call succeeds without changing any `bookingPolicy` object.\n   * @param - ID of the booking policy that's set as default.\n   */\n  (bookingPolicyId: string): Promise<\n    NonNullablePaths<\n      SetDefaultBookingPolicyResponse,\n      | `currentDefaultBookingPolicy.customPolicyDescription.enabled`\n      | `currentDefaultBookingPolicy.customPolicyDescription.description`\n      | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.enabled`\n      | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n      | `currentDefaultBookingPolicy.limitLateBookingPolicy.enabled`\n      | `currentDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n      | `currentDefaultBookingPolicy.bookAfterStartPolicy.enabled`\n      | `currentDefaultBookingPolicy.cancellationPolicy.enabled`\n      | `currentDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation`\n      | `currentDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n      | `currentDefaultBookingPolicy.reschedulePolicy.enabled`\n      | `currentDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule`\n      | `currentDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n      | `currentDefaultBookingPolicy.waitlistPolicy.enabled`\n      | `currentDefaultBookingPolicy.waitlistPolicy.capacity`\n      | `currentDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n      | `currentDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n      | `currentDefaultBookingPolicy.cancellationFeePolicy.enabled`\n      | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows`\n      | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n      | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n      | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n      | `currentDefaultBookingPolicy.saveCreditCardPolicy.enabled`\n      | `previousDefaultBookingPolicy.customPolicyDescription.enabled`\n      | `previousDefaultBookingPolicy.customPolicyDescription.description`\n      | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.enabled`\n      | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n      | `previousDefaultBookingPolicy.limitLateBookingPolicy.enabled`\n      | `previousDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n      | `previousDefaultBookingPolicy.bookAfterStartPolicy.enabled`\n      | `previousDefaultBookingPolicy.cancellationPolicy.enabled`\n      | `previousDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation`\n      | `previousDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n      | `previousDefaultBookingPolicy.reschedulePolicy.enabled`\n      | `previousDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule`\n      | `previousDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n      | `previousDefaultBookingPolicy.waitlistPolicy.enabled`\n      | `previousDefaultBookingPolicy.waitlistPolicy.capacity`\n      | `previousDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n      | `previousDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n      | `previousDefaultBookingPolicy.cancellationFeePolicy.enabled`\n      | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows`\n      | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n      | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n      | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n      | `previousDefaultBookingPolicy.saveCreditCardPolicy.enabled`,\n      7\n    >\n  >;\n}\n\nexport function deleteBookingPolicy(\n  httpClient: HttpClient\n): DeleteBookingPolicySignature {\n  return (bookingPolicyId: string) =>\n    universalDeleteBookingPolicy(\n      bookingPolicyId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DeleteBookingPolicySignature {\n  /**\n   * Deletes a booking policy.\n   *\n   *\n   * You can't delete the default policy without first *setting a different policy as default*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/set-default-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/set-default-booking-policy)).\n   * @param - ID of the booking policy to delete.\n   */\n  (bookingPolicyId: string): Promise<\n    void & {\n      __applicationErrorsType?: DeleteBookingPolicyApplicationErrors;\n    }\n  >;\n}\n\nexport function queryBookingPolicies(\n  httpClient: HttpClient\n): QueryBookingPoliciesSignature {\n  return () =>\n    universalQueryBookingPolicies(\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface QueryBookingPoliciesSignature {\n  /**\n   * Creates a query to retrieve a list of `bookingPolicy` objects.\n   *\n   * The `queryBookingPolicies()` function builds a query to retrieve a list of `bookingPolicy` objects and returns a `BookingPoliciesQueryBuilder` object.\n   *\n   * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-builder/find) function.\n   *\n   * You can refine the query by chaining `BookingPoliciesQueryBuilder` functions onto the query. `BookingPoliciesQueryBuilder` functions enable you to sort, filter, and control the results that `queryBookingPolicies()` returns.\n   *\n   * `queryBookingPolicies()` runs with the following `BookingPoliciesQueryBuilder` defaults that you can override:\n   *\n   * + `limit` is `50`.\n   * + Sorted by `createdDate` in ascending order.\n   *\n   * The functions that are chained to `queryBookingPolicies()` are applied in the order they are called. For example, if you apply `ascending(\"waitlistPolicy.capacity\")` and then `ascending(\"name\")`, the results are sorted first by `waitlistPolicy.capacity`, and then, if there are multiple results with the same `waitlistPolicy.capacity`, the items are sorted by `name`.\n   *\n   * The following `BookingPoliciesQueryBuilder` functions are supported for the `queryBookingPolicies()` function. For a full description of the `bookingPolicy` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-result/items) property in `BookingPoliciesQueryResult`.\n   */\n  (): BookingPoliciesQueryBuilder;\n}\n\nexport function countBookingPolicies(\n  httpClient: HttpClient\n): CountBookingPoliciesSignature {\n  return (options?: CountBookingPoliciesOptions) =>\n    universalCountBookingPolicies(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CountBookingPoliciesSignature {\n  /**\n   * Counts booking policies, given the provided filtering.\n   *\n   *\n   * See *supported filters*\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n   * for a complete list of supported filters.\n   */\n  (options?: CountBookingPoliciesOptions): Promise<\n    NonNullablePaths<CountBookingPoliciesResponse, `count`, 2>\n  >;\n}\n\nexport const onBookingPolicyCreated = EventDefinition(\n  'wix.bookings.v1.booking_policy_created',\n  true,\n  (event: BookingPolicyCreatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<BookingPolicyCreatedEnvelope>();\nexport const onBookingPolicyDefaultBookingPolicySet = EventDefinition(\n  'wix.bookings.v1.booking_policy_default_booking_policy_set',\n  true,\n  (event: BookingPolicyDefaultBookingPolicySetEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'data.currentDefaultBookingPolicy.createdDate' },\n            { path: 'data.currentDefaultBookingPolicy.updatedDate' },\n            { path: 'data.previousDefaultBookingPolicy.createdDate' },\n            { path: 'data.previousDefaultBookingPolicy.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<BookingPolicyDefaultBookingPolicySetEnvelope>();\nexport const onBookingPolicyDeleted = EventDefinition(\n  'wix.bookings.v1.booking_policy_deleted',\n  true,\n  (event: BookingPolicyDeletedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'undefined.createdDate' },\n            { path: 'undefined.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<BookingPolicyDeletedEnvelope>();\nexport const onBookingPolicyUpdated = EventDefinition(\n  'wix.bookings.v1.booking_policy_updated',\n  true,\n  (event: BookingPolicyUpdatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<BookingPolicyUpdatedEnvelope>();\n\nexport {\n  ActionEvent,\n  Address,\n  AddressHint,\n  Asset,\n  BaseEventMetadata,\n  BookAfterStartPolicy,\n  BookingPoliciesQueryBuilder,\n  BookingPoliciesQueryResult,\n  BookingPolicy,\n  BookingPolicyCreatedEnvelope,\n  BookingPolicyDefaultBookingPolicySetEnvelope,\n  BookingPolicyDeletedEnvelope,\n  BookingPolicyUpdatedEnvelope,\n  BusinessSchedule,\n  CancellationFeePolicy,\n  CancellationPolicy,\n  CancellationWindow,\n  CancellationWindowFeeOneOf,\n  Categories,\n  ChangeContext,\n  ChangeContextPayloadOneOf,\n  ConsentPolicy,\n  CountBookingPoliciesOptions,\n  CountBookingPoliciesRequest,\n  CountBookingPoliciesResponse,\n  CreateBookingPolicyRequest,\n  CreateBookingPolicyResponse,\n  CreateMissingDefaultPolicyRequest,\n  CreateMissingDefaultPolicyResponse,\n  CursorPaging,\n  CursorPagingMetadata,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  Cursors,\n  DayOfWeek,\n  DefaultBookingPolicySet,\n  DeleteBookingPolicyRequest,\n  DeleteBookingPolicyResponse,\n  DeleteContext,\n  DeleteStatus,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  Empty,\n  EntityCreatedEvent,\n  EntityDeletedEvent,\n  EntityUpdatedEvent,\n  EventMetadata,\n  ExtendedFields,\n  GeoCoordinates,\n  GetBookingPolicyRequest,\n  GetBookingPolicyResponse,\n  GetStrictestBookingPolicyRequest,\n  GetStrictestBookingPolicyResponse,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  LimitEarlyBookingPolicy,\n  LimitLateBookingPolicy,\n  Locale,\n  MessageEnvelope,\n  MetaSiteSpecialEvent,\n  MetaSiteSpecialEventPayloadOneOf,\n  Money,\n  Multilingual,\n  Namespace,\n  NamespaceChanged,\n  OdeditorAssigned,\n  OdeditorUnassigned,\n  ParticipantsPolicy,\n  PicassoAssigned,\n  PicassoUnassigned,\n  PlacementType,\n  PolicyDescription,\n  Properties,\n  PropertiesChange,\n  QueryBookingPoliciesRequest,\n  QueryBookingPoliciesResponse,\n  ReschedulePolicy,\n  ResolutionMethod,\n  ResourcesPolicy,\n  RestoreInfo,\n  SaveCreditCardPolicy,\n  ServiceProvisioned,\n  ServiceRemoved,\n  SetDefaultBookingPolicyRequest,\n  SetDefaultBookingPolicyResponse,\n  SiteCloned,\n  SiteCreated,\n  SiteCreatedContext,\n  SiteDeleted,\n  SiteHardDeleted,\n  SiteMarkedAsTemplate,\n  SiteMarkedAsWixSite,\n  SitePropertiesEvent,\n  SitePropertiesNotification,\n  SitePublished,\n  SitePurgedExternally,\n  SiteRenamed,\n  SiteTransferred,\n  SiteUndeleted,\n  SiteUnpublished,\n  SiteUrlChanged,\n  SortOrder,\n  Sorting,\n  SpecialHourPeriod,\n  State,\n  StudioAssigned,\n  StudioUnassigned,\n  SupportedLanguage,\n  TimePeriod,\n  Translation,\n  UpdateAllPoliciesRequest,\n  UpdateAllPoliciesResponse,\n  UpdateBookingPolicy,\n  UpdateBookingPolicyRequest,\n  UpdateBookingPolicyResponse,\n  V4SiteCreated,\n  WaitlistPolicy,\n  WebhookIdentityType,\n} from './bookings-v1-booking-policy-booking-policies.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameK<PERSON>s<PERSON>romRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, HttpResponse, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsV1BookingPolicy from './bookings-v1-booking-policy-booking-policies.http.js';\n// @ts-ignore\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/**\n * A booking policy is a set of rules that determine how customers can book a\n * service, including timeframes for booking, canceling, or rescheduling.\n */\nexport interface BookingPolicy {\n  /**\n   * ID of the booking policy.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the booking policy is updated.\n   * To prevent conflicting changes, the current `revision` must be passed when\n   * updating the booking policy.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Date and time the booking policy was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the booking policy was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the booking policy.\n   * @maxLength 400\n   */\n  name?: string | null;\n  /**\n   * Custom description for the booking policy and whether it's displayed to the\n   * participant.\n   */\n  customPolicyDescription?: PolicyDescription;\n  /**\n   * Whether the booking policy is the default.\n   * @readonly\n   */\n  default?: boolean | null;\n  /** Rule for limiting early bookings. */\n  limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;\n  /**\n   * Rule for limiting late bookings. This rule and `bookAfterStartPolicy` can't\n   * be both enabled at the same time.\n   */\n  limitLateBookingPolicy?: LimitLateBookingPolicy;\n  /**\n   * Rule for booking after the start of a session or course. This rule and\n   * `limitLateBookingPolicy` can't be both enabled at the same time.\n   */\n  bookAfterStartPolicy?: BookAfterStartPolicy;\n  /** Rule for canceling a booking. */\n  cancellationPolicy?: CancellationPolicy;\n  /** Rule for rescheduling a booking. */\n  reschedulePolicy?: ReschedulePolicy;\n  /** Waitlist rule for the service. */\n  waitlistPolicy?: WaitlistPolicy;\n  /** Rule for participants per booking. */\n  participantsPolicy?: ParticipantsPolicy;\n  /** Rules for cancellation fees. */\n  cancellationFeePolicy?: CancellationFeePolicy;\n  /** Rule for saving credit card details. */\n  saveCreditCardPolicy?: SaveCreditCardPolicy;\n  /** Extensions enabling users to save custom data related to the booking policies. */\n  extendedFields?: ExtendedFields;\n}\n\n/** A description of the booking policy to display to participants. */\nexport interface PolicyDescription {\n  /**\n   * Whether the description is displayed to the participant. `true` means the\n   * description is displayed.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Description of the booking policy.\n   *\n   * Default: Empty\n   * Max length: 2500 characters\n   * @maxLength 2500\n   */\n  description?: string;\n}\n\n/** The rule for limiting early bookings. */\nexport interface LimitEarlyBookingPolicy {\n  /**\n   * Whether there's a limit about how early a customer can book. `false` means there's\n   * no limit to the earliest supported booking time.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Maximum number of minutes before the start of a session or course customers can book.\n   * Must be greater than `limitLateBookingPolicy.latestBookingInMinutes`.\n   *\n   * Default: `10080` minutes (7 days)\n   * Min: `1` minute\n   * @min 1\n   */\n  earliestBookingInMinutes?: number;\n}\n\n/**\n * Rule limiting late bookings.\n *\n * This rule and `bookAfterStartPolicy` can't be both enabled at the same time.\n */\nexport interface LimitLateBookingPolicy {\n  /**\n   * Whether there's a limit about how late customers can book. `false` means\n   * customers can book up to the last minute. If specified as `true`,\n   * `bookAfterStartPolicy.enabled` must be `false`.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Minimum number of minutes before the start of the session customers can book.\n   * For courses, this is relative to the start time of the next session and doesn't\n   * consider course sessions in the past. This value must be less than\n   * `limitEarlyBookingPolicy.earliestBookingInMinutes`.\n   *\n   * Default: `1440` minutes (1 day)\n   * Min: `1` minute\n   * @min 1\n   */\n  latestBookingInMinutes?: number;\n}\n\n/**\n * The rule for whether a session can be booked after the start of the schedule.\n * This rule and `LimitLateBookingPolicy` cannot be enabled at the same time. So if this rule\n * is enabled, the `LimitLateBookingPolicy` rule must be disabled.\n */\nexport interface BookAfterStartPolicy {\n  /**\n   * Whether customers can book after the session has started. `true` means\n   * customers can book after the session has started. For courses, this refers to\n   * the start of the last course session. If specified as `true`,\n   * `limitLateBookingPolicy.enabled` must be `false`.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n}\n\n/** The rule for canceling a booked session. */\nexport interface CancellationPolicy {\n  /**\n   * Whether customers can cancel the booking. `true` means customers can cancel\n   * the booking.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Whether there's a limit on the latest cancellation time. `false` means customers\n   * can cancel the booking until the last minute before the course or session starts.\n   *\n   * Default: `false`\n   */\n  limitLatestCancellation?: boolean;\n  /**\n   * Minimum number of minutes before the start of the session customers can cancel.\n   * For courses, this refers to the start of the first course session.\n   *\n   * Default: `1440` minutes (1 day)\n   * Min: `1` minute\n   * @min 1\n   */\n  latestCancellationInMinutes?: number;\n}\n\n/** The rule for rescheduling a booked session. */\nexport interface ReschedulePolicy {\n  /**\n   * Whether customers can reschedule a booking for an appointment-based service.\n   * `true` means customers can reschedule.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Whether there's a limit on the latest supported rescheduling time. `false`\n   * means customers can reschedule until the last minute before the session start.\n   *\n   * Default: `false`\n   */\n  limitLatestReschedule?: boolean;\n  /**\n   * Minimum number of minutes before the session start session customers can\n   * reschedule their booking.\n   *\n   * Default: `1440` minutes (1 day)\n   * Min: `1` minute\n   * @min 1\n   */\n  latestRescheduleInMinutes?: number;\n}\n\n/** The rule for the waitlist. */\nexport interface WaitlistPolicy {\n  /**\n   * Whether the service has a waitlist. `true` means there's a waitlist.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Number of spots in the waitlist.\n   *\n   * Default: `10` spots\n   * Min: `1` spot\n   * @min 1\n   */\n  capacity?: number;\n  /**\n   * Time in minutes the potential customer is given to book after they've been\n   * notified that a spot has opened up for them. If they don't respond in time,\n   * the open spot is offered to the next potential customer on the waitlist.\n   *\n   * Default: `10` minutes\n   * Min: `1` minute\n   * @min 1\n   */\n  reservationTimeInMinutes?: number;\n}\n\n/** The rule for the maximum number of participants per booking. */\nexport interface ParticipantsPolicy {\n  /**\n   * Maximum number of participants per booking.\n   *\n   * Default: `1` participant\n   * Min: `1` participant\n   * @min 1\n   */\n  maxParticipantsPerBooking?: number;\n}\n\n/** The rule regarding the allocation of resources (e.g. staff members). */\nexport interface ResourcesPolicy {\n  /**\n   * Whether the customer must select a resource, for example a staff member, when\n   * booking. `false` means the customer can book without selecting a resource.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Whether Wix Bookings automatically assigns a resource, for example a *staff member*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)),\n   * to the booking. `false` means the customer must select the resource\n   * themselves and Wix Bookings doesn't assign it automatically.\n   *\n   * Default: `false`\n   */\n  autoAssignAllowed?: boolean;\n}\n\nexport interface CancellationFeePolicy {\n  /**\n   * Whether customers must pay a cancellation fee when canceling a booking.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Time windows relative to the session start during which customers can cancel\n   * their booking. Each window includes details about the fee for canceling within it.\n   * @maxSize 2\n   */\n  cancellationWindows?: CancellationWindow[];\n  /**\n   * Whether Wix automatically charges the cancellation fee from the customer once\n   * they cancel their booking.\n   *\n   * Default: `true`\n   */\n  autoCollectFeeEnabled?: boolean | null;\n}\n\n/**\n * Money.\n * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.\n */\nexport interface Money {\n  /**\n   * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.\n   * @format DECIMAL_VALUE\n   * @decimalValue options { gt:0, maxScale:2 }\n   */\n  value?: string;\n  /**\n   * Currency code. Must be valid ISO 4217 currency code (e.g., USD).\n   * @format CURRENCY\n   * @readonly\n   */\n  currency?: string;\n  /**\n   * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.\n   * @maxLength 50\n   */\n  formattedValue?: string | null;\n}\n\nexport interface CancellationWindow extends CancellationWindowFeeOneOf {\n  /** Fixed amount customers must pay when canceling the booking within this window. */\n  amount?: Money;\n  /**\n   * Percentage of the booking price customers must pay when canceling within\n   * this window.\n   *\n   * Min: `0.01` percent\n   * Max: `100` percent\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentage?: string;\n  /**\n   * Start of the cancellation window in minutes before the session start. For\n   * courses, this refers to the start of the first course session.\n   * @min 1\n   */\n  startInMinutes?: number | null;\n}\n\n/** @oneof */\nexport interface CancellationWindowFeeOneOf {\n  /** Fixed amount customers must pay when canceling the booking within this window. */\n  amount?: Money;\n  /**\n   * Percentage of the booking price customers must pay when canceling within\n   * this window.\n   *\n   * Min: `0.01` percent\n   * Max: `100` percent\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentage?: string;\n}\n\nexport interface SaveCreditCardPolicy {\n  /**\n   * Whether Wix stores credit card details of the customer. Storing the details\n   * allows Wix to prefill the *eCommerce checkout*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction))\n   * and thus increases the likelihood that the customer completes the booking\n   * process.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\n/** This event is triggered when a different booking policy has been set as the default policy. */\nexport interface DefaultBookingPolicySet {\n  /** The new default booking policy. */\n  currentDefaultBookingPolicy?: BookingPolicy;\n  /**\n   * The booking policy that was the default before this endpoint was called.\n   * This field will be empty if there was no default booking policy before this method was called.\n   */\n  previousDefaultBookingPolicy?: BookingPolicy;\n}\n\nexport interface CreateBookingPolicyRequest {\n  /** Booking policy to create. */\n  bookingPolicy: BookingPolicy;\n}\n\nexport interface CreateBookingPolicyResponse {\n  /** Created booking policy. */\n  bookingPolicy?: BookingPolicy;\n}\n\nexport interface GetBookingPolicyRequest {\n  /**\n   * ID of the booking policy to retrieve.\n   * @format GUID\n   */\n  bookingPolicyId: string;\n}\n\nexport interface GetBookingPolicyResponse {\n  /** Retrieved booking policy. */\n  bookingPolicy?: BookingPolicy;\n}\n\nexport interface GetStrictestBookingPolicyRequest {\n  /**\n   * IDs of the booking policies for which to retrieve the strictest rules for.\n   * @format GUID\n   * @maxSize 100\n   */\n  bookingPolicyIds: string[];\n}\n\nexport interface GetStrictestBookingPolicyResponse {\n  /**\n   * Hypothetical `bookingPolicy` object that combines the strictest version of\n   * each rule. `bookingPolicy.id` is `null` and the returned object isn't actually\n   * created. To create a new policy, you can call *Create Booking Policy*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).\n   */\n  bookingPolicy?: BookingPolicy;\n}\n\nexport interface UpdateBookingPolicyRequest {\n  /** Booking policy to update. */\n  bookingPolicy: BookingPolicy;\n}\n\nexport interface UpdateBookingPolicyResponse {\n  /** Updated booking policy. */\n  bookingPolicy?: BookingPolicy;\n}\n\nexport interface SetDefaultBookingPolicyRequest {\n  /**\n   * ID of the booking policy that's set as default.\n   * @format GUID\n   */\n  bookingPolicyId: string;\n}\n\nexport interface SetDefaultBookingPolicyResponse {\n  /** New default booking policy. */\n  currentDefaultBookingPolicy?: BookingPolicy;\n  /**\n   * Previous default booking policy. Not available if the provided booking policy\n   * was already the default.\n   */\n  previousDefaultBookingPolicy?: BookingPolicy;\n}\n\nexport interface DeleteBookingPolicyRequest {\n  /**\n   * ID of the booking policy to delete.\n   * @format GUID\n   */\n  bookingPolicyId: string;\n}\n\nexport interface DeleteBookingPolicyResponse {}\n\nexport interface QueryBookingPoliciesRequest {\n  /**\n   * The query by which to select booking policies. See\n   * [the supported filters article](https://dev.wix.com/docs/rest/business-solutions/bookings/services/booking-policy/supported-filters)\n   * for details.\n   */\n  query: CursorQuery;\n}\n\nexport interface CursorQuery extends CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   * @maxSize 10\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface CursorPaging {\n  /**\n   * Number of items to load.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * You can get the relevant cursor token\n   * from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface QueryBookingPoliciesResponse {\n  /** Retrieved booking policies. */\n  bookingPolicies?: BookingPolicy[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  cursors?: Cursors;\n  /**\n   * Indicates if there are more results after the current page.\n   * If `true`, another page of results can be retrieved.\n   * If `false`, this is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor pointing to next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface CountBookingPoliciesRequest {\n  /**\n   * Filter to base the count on. See\n   * supported filters*\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n   * for details.\n   */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountBookingPoliciesResponse {\n  /** Number of booking policies matching the provided filter. */\n  count?: number;\n}\n\nexport interface UpdateAllPoliciesRequest {\n  /** Fields to set to all booking policies. */\n  bookingPolicy?: BookingPolicy;\n}\n\nexport interface UpdateAllPoliciesResponse {}\n\nexport interface CreateMissingDefaultPolicyRequest {}\n\nexport interface CreateMissingDefaultPolicyResponse {\n  /** The default booking policy. */\n  defaultBookingPolicy?: BookingPolicy;\n  /** Whether a new default policy was created. */\n  wasANewPolicyCreated?: boolean;\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface Empty {}\n\n/** Encapsulates all details written to the Greyhound topic when a site's properties are updated. */\nexport interface SitePropertiesNotification {\n  /** The site ID for which this update notification applies. */\n  metasiteId?: string;\n  /** The actual update event. */\n  event?: SitePropertiesEvent;\n  /**\n   * A convenience set of mappings from the MetaSite ID to its constituent services.\n   * @maxSize 500\n   */\n  translations?: Translation[];\n  /** Context of the notification */\n  changeContext?: ChangeContext;\n}\n\n/** The actual update event for a particular notification. */\nexport interface SitePropertiesEvent {\n  /** Version of the site's properties represented by this update. */\n  version?: number;\n  /** Set of properties that were updated - corresponds to the fields in \"properties\". */\n  fields?: string[];\n  /** Updated properties. */\n  properties?: Properties;\n}\n\nexport interface Properties {\n  /** Site categories. */\n  categories?: Categories;\n  /** Site locale. */\n  locale?: Locale;\n  /**\n   * Site language.\n   *\n   * Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format.\n   */\n  language?: string | null;\n  /**\n   * Site currency format used to bill customers.\n   *\n   * Three-letter currency code in [ISO-4217 alphabetic](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) format.\n   */\n  paymentCurrency?: string | null;\n  /** Timezone in `America/New_York` format. */\n  timeZone?: string | null;\n  /** Email address. */\n  email?: string | null;\n  /** Phone number. */\n  phone?: string | null;\n  /** Fax number. */\n  fax?: string | null;\n  /** Address. */\n  address?: Address;\n  /** Site display name. */\n  siteDisplayName?: string | null;\n  /** Business name. */\n  businessName?: string | null;\n  /** Path to the site's logo in Wix Media (without Wix Media base URL). */\n  logo?: string | null;\n  /** Site description. */\n  description?: string | null;\n  /**\n   * Business schedule. Regular and exceptional time periods when the business is open or the service is available.\n   *\n   * __Note:__ Not supported by Wix Bookings.\n   */\n  businessSchedule?: BusinessSchedule;\n  /** Supported languages of a site and the primary language. */\n  multilingual?: Multilingual;\n  /** Cookie policy the Wix user defined for their site (before the site visitor interacts with/limits it). */\n  consentPolicy?: ConsentPolicy;\n  /**\n   * Supported values: `FITNESS SERVICE`, `RESTAURANT`, `BLOG`, `STORE`, `EVENT`, `UNKNOWN`.\n   *\n   * Site business type.\n   */\n  businessConfig?: string | null;\n  /** External site URL that uses Wix as its headless business solution. */\n  externalSiteUrl?: string | null;\n  /** Track clicks analytics. */\n  trackClicksAnalytics?: boolean;\n}\n\nexport interface Categories {\n  /** Primary site category. */\n  primary?: string;\n  /**\n   * Secondary site category.\n   * @maxSize 50\n   */\n  secondary?: string[];\n  /** Business Term Id */\n  businessTermId?: string | null;\n}\n\nexport interface Locale {\n  /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */\n  languageCode?: string;\n  /** Two-letter country code in [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements) format. */\n  country?: string;\n}\n\nexport interface Address {\n  /** Street name. */\n  street?: string;\n  /** City name. */\n  city?: string;\n  /** Two-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format. */\n  country?: string;\n  /** State. */\n  state?: string;\n  /**\n   * Zip or postal code.\n   * @maxLength 20\n   */\n  zip?: string;\n  /** Extra information to be displayed in the address. */\n  hint?: AddressHint;\n  /** Whether this address represents a physical location. */\n  isPhysical?: boolean;\n  /** Google-formatted version of this address. */\n  googleFormattedAddress?: string;\n  /** Street number. */\n  streetNumber?: string;\n  /** Apartment number. */\n  apartmentNumber?: string;\n  /** Geographic coordinates of location. */\n  coordinates?: GeoCoordinates;\n}\n\n/**\n * Extra information on displayed addresses.\n * This is used for display purposes. Used to add additional data about the address, such as \"In the passage\".\n * Free text. In addition, the user can state where to display the additional description - before, after, or instead of the address string.\n */\nexport interface AddressHint {\n  /** Extra text displayed next to, or instead of, the actual address. */\n  text?: string;\n  /** Where the extra text should be displayed. */\n  placement?: PlacementTypeWithLiterals;\n}\n\n/** Where the extra text should be displayed: before, after or instead of the actual address. */\nexport enum PlacementType {\n  BEFORE = 'BEFORE',\n  AFTER = 'AFTER',\n  REPLACE = 'REPLACE',\n}\n\n/** @enumType */\nexport type PlacementTypeWithLiterals =\n  | PlacementType\n  | 'BEFORE'\n  | 'AFTER'\n  | 'REPLACE';\n\n/** Geocoordinates for a particular address. */\nexport interface GeoCoordinates {\n  /** Latitude of the location. Must be between -90 and 90. */\n  latitude?: number;\n  /** Longitude of the location. Must be between -180 and 180. */\n  longitude?: number;\n}\n\n/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */\nexport interface BusinessSchedule {\n  /**\n   * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.\n   * @maxSize 100\n   */\n  periods?: TimePeriod[];\n  /**\n   * Exceptions to the business's regular hours. The business can be open or closed during the exception.\n   * @maxSize 100\n   */\n  specialHourPeriod?: SpecialHourPeriod[];\n}\n\n/** Weekly recurring time periods when the business is regularly open or the service is available. */\nexport interface TimePeriod {\n  /** Day of the week the period starts on. */\n  openDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   */\n  openTime?: string;\n  /** Day of the week the period ends on. */\n  closeDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   *\n   * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.\n   */\n  closeTime?: string;\n}\n\n/** Enumerates the days of the week. */\nexport enum DayOfWeek {\n  MONDAY = 'MONDAY',\n  TUESDAY = 'TUESDAY',\n  WEDNESDAY = 'WEDNESDAY',\n  THURSDAY = 'THURSDAY',\n  FRIDAY = 'FRIDAY',\n  SATURDAY = 'SATURDAY',\n  SUNDAY = 'SUNDAY',\n}\n\n/** @enumType */\nexport type DayOfWeekWithLiterals =\n  | DayOfWeek\n  | 'MONDAY'\n  | 'TUESDAY'\n  | 'WEDNESDAY'\n  | 'THURSDAY'\n  | 'FRIDAY'\n  | 'SATURDAY'\n  | 'SUNDAY';\n\n/** Exception to the business's regular hours. The business can be open or closed during the exception. */\nexport interface SpecialHourPeriod {\n  /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  startDate?: string;\n  /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  endDate?: string;\n  /**\n   * Whether the business is closed (or the service is not available) during the exception.\n   *\n   * Default: `true`.\n   */\n  isClosed?: boolean;\n  /** Additional info about the exception. For example, \"We close earlier on New Year's Eve.\" */\n  comment?: string;\n}\n\nexport interface Multilingual {\n  /**\n   * Supported languages list.\n   * @maxSize 200\n   */\n  supportedLanguages?: SupportedLanguage[];\n  /** Whether to redirect to user language. */\n  autoRedirect?: boolean;\n}\n\nexport interface SupportedLanguage {\n  /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */\n  languageCode?: string;\n  /** Locale. */\n  locale?: Locale;\n  /** Whether the supported language is the primary language for the site. */\n  isPrimary?: boolean;\n  /** Language icon. */\n  countryCode?: string;\n  /** How the language will be resolved. For internal use. */\n  resolutionMethod?: ResolutionMethodWithLiterals;\n  /** Whether the supported language is the primary language for site visitors. */\n  isVisitorPrimary?: boolean | null;\n}\n\nexport enum ResolutionMethod {\n  QUERY_PARAM = 'QUERY_PARAM',\n  SUBDOMAIN = 'SUBDOMAIN',\n  SUBDIRECTORY = 'SUBDIRECTORY',\n}\n\n/** @enumType */\nexport type ResolutionMethodWithLiterals =\n  | ResolutionMethod\n  | 'QUERY_PARAM'\n  | 'SUBDOMAIN'\n  | 'SUBDIRECTORY';\n\nexport interface ConsentPolicy {\n  /** Whether the site uses cookies that are essential to site operation. Always `true`. */\n  essential?: boolean | null;\n  /** Whether the site uses cookies that affect site performance and other functional measurements. */\n  functional?: boolean | null;\n  /** Whether the site uses cookies that collect analytics about how the site is used (in order to improve it). */\n  analytics?: boolean | null;\n  /** Whether the site uses cookies that collect information allowing better customization of the experience for a current visitor. */\n  advertising?: boolean | null;\n  /** CCPA compliance flag. */\n  dataToThirdParty?: boolean | null;\n}\n\n/** A single mapping from the MetaSite ID to a particular service. */\nexport interface Translation {\n  /** The service type. */\n  serviceType?: string;\n  /** The application definition ID; this only applies to services of type ThirdPartyApps. */\n  appDefId?: string;\n  /** The instance ID of the service. */\n  instanceId?: string;\n}\n\nexport interface ChangeContext extends ChangeContextPayloadOneOf {\n  /** Properties were updated. */\n  propertiesChange?: PropertiesChange;\n  /** Default properties were created on site creation. */\n  siteCreated?: V4SiteCreated;\n  /** Properties were cloned on site cloning. */\n  siteCloned?: SiteCloned;\n}\n\n/** @oneof */\nexport interface ChangeContextPayloadOneOf {\n  /** Properties were updated. */\n  propertiesChange?: PropertiesChange;\n  /** Default properties were created on site creation. */\n  siteCreated?: V4SiteCreated;\n  /** Properties were cloned on site cloning. */\n  siteCloned?: SiteCloned;\n}\n\nexport interface PropertiesChange {}\n\nexport interface V4SiteCreated {\n  /** Origin template site id. */\n  originTemplateId?: string | null;\n}\n\nexport interface SiteCloned {\n  /** Origin site id. */\n  originMetaSiteId?: string;\n}\n\nexport interface MetaSiteSpecialEvent extends MetaSiteSpecialEventPayloadOneOf {\n  /** Emitted on a meta site creation. */\n  siteCreated?: SiteCreated;\n  /** Emitted on a meta site transfer completion. */\n  siteTransferred?: SiteTransferred;\n  /** Emitted on a meta site deletion. */\n  siteDeleted?: SiteDeleted;\n  /** Emitted on a meta site restoration. */\n  siteUndeleted?: SiteUndeleted;\n  /** Emitted on the first* publish of the meta site (* switching from unpublished to published state). */\n  sitePublished?: SitePublished;\n  /** Emitted on a meta site unpublish. */\n  siteUnpublished?: SiteUnpublished;\n  /** Emitted when meta site is marked as template. */\n  siteMarkedAsTemplate?: SiteMarkedAsTemplate;\n  /** Emitted when meta site is marked as a WixSite. */\n  siteMarkedAsWixSite?: SiteMarkedAsWixSite;\n  /** Emitted when an application is provisioned (installed). */\n  serviceProvisioned?: ServiceProvisioned;\n  /** Emitted when an application is removed (uninstalled). */\n  serviceRemoved?: ServiceRemoved;\n  /** Emitted when meta site name (URL slug) is changed. */\n  siteRenamedPayload?: SiteRenamed;\n  /** Emitted when meta site was permanently deleted. */\n  hardDeleted?: SiteHardDeleted;\n  /** Emitted on a namespace change. */\n  namespaceChanged?: NamespaceChanged;\n  /** Emitted when Studio is attached. */\n  studioAssigned?: StudioAssigned;\n  /** Emitted when Studio is detached. */\n  studioUnassigned?: StudioUnassigned;\n  /**\n   * Emitted when one of the URLs is changed. After this event you may call `urls-server` to fetch\n   * the actual URL.\n   *\n   * See: https://wix.slack.com/archives/C0UHEBPFT/p1732520791210559?thread_ts=1732027914.294059&cid=C0UHEBPFT\n   * See: https://wix.slack.com/archives/C0UHEBPFT/p1744115197619459\n   */\n  urlChanged?: SiteUrlChanged;\n  /** Site is marked as PurgedExternally */\n  sitePurgedExternally?: SitePurgedExternally;\n  /** Emitted when Odeditor is attached. */\n  odeditorAssigned?: OdeditorAssigned;\n  /** Emitted when Odeditor is detached. */\n  odeditorUnassigned?: OdeditorUnassigned;\n  /** Emitted when Picasso is attached. */\n  picassoAssigned?: PicassoAssigned;\n  /** Emitted when Picasso is detached. */\n  picassoUnassigned?: PicassoUnassigned;\n  /**\n   * A meta site id.\n   * @format GUID\n   */\n  metaSiteId?: string;\n  /** A meta site version. Monotonically increasing. */\n  version?: string;\n  /** A timestamp of the event. */\n  timestamp?: string;\n  /**\n   * TODO(meta-site): Change validation once validations are disabled for consumers\n   * More context: https://wix.slack.com/archives/C0UHEBPFT/p1720957844413149 and https://wix.slack.com/archives/CFWKX325T/p1728892152855659\n   * @maxSize 4000\n   */\n  assets?: Asset[];\n}\n\n/** @oneof */\nexport interface MetaSiteSpecialEventPayloadOneOf {\n  /** Emitted on a meta site creation. */\n  siteCreated?: SiteCreated;\n  /** Emitted on a meta site transfer completion. */\n  siteTransferred?: SiteTransferred;\n  /** Emitted on a meta site deletion. */\n  siteDeleted?: SiteDeleted;\n  /** Emitted on a meta site restoration. */\n  siteUndeleted?: SiteUndeleted;\n  /** Emitted on the first* publish of the meta site (* switching from unpublished to published state). */\n  sitePublished?: SitePublished;\n  /** Emitted on a meta site unpublish. */\n  siteUnpublished?: SiteUnpublished;\n  /** Emitted when meta site is marked as template. */\n  siteMarkedAsTemplate?: SiteMarkedAsTemplate;\n  /** Emitted when meta site is marked as a WixSite. */\n  siteMarkedAsWixSite?: SiteMarkedAsWixSite;\n  /** Emitted when an application is provisioned (installed). */\n  serviceProvisioned?: ServiceProvisioned;\n  /** Emitted when an application is removed (uninstalled). */\n  serviceRemoved?: ServiceRemoved;\n  /** Emitted when meta site name (URL slug) is changed. */\n  siteRenamedPayload?: SiteRenamed;\n  /** Emitted when meta site was permanently deleted. */\n  hardDeleted?: SiteHardDeleted;\n  /** Emitted on a namespace change. */\n  namespaceChanged?: NamespaceChanged;\n  /** Emitted when Studio is attached. */\n  studioAssigned?: StudioAssigned;\n  /** Emitted when Studio is detached. */\n  studioUnassigned?: StudioUnassigned;\n  /**\n   * Emitted when one of the URLs is changed. After this event you may call `urls-server` to fetch\n   * the actual URL.\n   *\n   * See: https://wix.slack.com/archives/C0UHEBPFT/p1732520791210559?thread_ts=1732027914.294059&cid=C0UHEBPFT\n   * See: https://wix.slack.com/archives/C0UHEBPFT/p1744115197619459\n   */\n  urlChanged?: SiteUrlChanged;\n  /** Site is marked as PurgedExternally */\n  sitePurgedExternally?: SitePurgedExternally;\n  /** Emitted when Odeditor is attached. */\n  odeditorAssigned?: OdeditorAssigned;\n  /** Emitted when Odeditor is detached. */\n  odeditorUnassigned?: OdeditorUnassigned;\n  /** Emitted when Picasso is attached. */\n  picassoAssigned?: PicassoAssigned;\n  /** Emitted when Picasso is detached. */\n  picassoUnassigned?: PicassoUnassigned;\n}\n\nexport interface Asset {\n  /**\n   * An application definition id (app_id in dev-center). For legacy reasons may be UUID or a string (from Java Enum).\n   * @maxLength 36\n   */\n  appDefId?: string;\n  /**\n   * An instance id. For legacy reasons may be UUID or a string.\n   * @maxLength 200\n   */\n  instanceId?: string;\n  /** An application state. */\n  state?: StateWithLiterals;\n}\n\nexport enum State {\n  UNKNOWN = 'UNKNOWN',\n  ENABLED = 'ENABLED',\n  DISABLED = 'DISABLED',\n  PENDING = 'PENDING',\n  DEMO = 'DEMO',\n}\n\n/** @enumType */\nexport type StateWithLiterals =\n  | State\n  | 'UNKNOWN'\n  | 'ENABLED'\n  | 'DISABLED'\n  | 'PENDING'\n  | 'DEMO';\n\nexport interface SiteCreated {\n  /**\n   * A template identifier (empty if not created from a template).\n   * @maxLength 36\n   */\n  originTemplateId?: string;\n  /**\n   * An account id of the owner.\n   * @format GUID\n   */\n  ownerId?: string;\n  /** A context in which meta site was created. */\n  context?: SiteCreatedContextWithLiterals;\n  /**\n   * A meta site id from which this site was created.\n   *\n   * In case of a creation from a template it's a template id.\n   * In case of a site duplication (\"Save As\" in dashboard or duplicate in UM) it's an id of a source site.\n   * @format GUID\n   */\n  originMetaSiteId?: string | null;\n  /**\n   * A meta site name (URL slug).\n   * @maxLength 20\n   */\n  siteName?: string;\n  /** A namespace. */\n  namespace?: NamespaceWithLiterals;\n}\n\nexport enum SiteCreatedContext {\n  /** A valid option, we don't expose all reasons why site might be created. */\n  OTHER = 'OTHER',\n  /** A meta site was created from template. */\n  FROM_TEMPLATE = 'FROM_TEMPLATE',\n  /** A meta site was created by copying of the transfferred meta site. */\n  DUPLICATE_BY_SITE_TRANSFER = 'DUPLICATE_BY_SITE_TRANSFER',\n  /** A copy of existing meta site. */\n  DUPLICATE = 'DUPLICATE',\n  /** A meta site was created as a transfferred site (copy of the original), old flow, should die soon. */\n  OLD_SITE_TRANSFER = 'OLD_SITE_TRANSFER',\n  /** deprecated A meta site was created for Flash editor. */\n  FLASH = 'FLASH',\n}\n\n/** @enumType */\nexport type SiteCreatedContextWithLiterals =\n  | SiteCreatedContext\n  | 'OTHER'\n  | 'FROM_TEMPLATE'\n  | 'DUPLICATE_BY_SITE_TRANSFER'\n  | 'DUPLICATE'\n  | 'OLD_SITE_TRANSFER'\n  | 'FLASH';\n\nexport enum Namespace {\n  UNKNOWN_NAMESPACE = 'UNKNOWN_NAMESPACE',\n  /** Default namespace for UGC sites. MetaSites with this namespace will be shown in a user's site list by default. */\n  WIX = 'WIX',\n  /** ShoutOut stand alone product. These are siteless (no actual Wix site, no HtmlWeb). MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  SHOUT_OUT = 'SHOUT_OUT',\n  /** MetaSites created by the Albums product, they appear as part of the Albums app. MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  ALBUMS = 'ALBUMS',\n  /** Part of the WixStores migration flow, a user tries to migrate and gets this site to view and if the user likes it then stores removes this namespace and deletes the old site with the old stores. MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  WIX_STORES_TEST_DRIVE = 'WIX_STORES_TEST_DRIVE',\n  /** Hotels standalone (siteless). MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  HOTELS = 'HOTELS',\n  /** Clubs siteless MetaSites, a club without a wix website. MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  CLUBS = 'CLUBS',\n  /** A partially created ADI website. MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  ONBOARDING_DRAFT = 'ONBOARDING_DRAFT',\n  /** AppBuilder for AppStudio / shmite (c). MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  DEV_SITE = 'DEV_SITE',\n  /** LogoMaker websites offered to the user after logo purchase. MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  LOGOS = 'LOGOS',\n  /** VideoMaker websites offered to the user after video purchase. MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  VIDEO_MAKER = 'VIDEO_MAKER',\n  /** MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  PARTNER_DASHBOARD = 'PARTNER_DASHBOARD',\n  /** MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  DEV_CENTER_COMPANY = 'DEV_CENTER_COMPANY',\n  /**\n   * A draft created by HTML editor on open. Upon \"first save\" it will be moved to be of WIX domain.\n   *\n   * Meta site with this namespace will *not* be shown in a user's site list by default.\n   */\n  HTML_DRAFT = 'HTML_DRAFT',\n  /**\n   * the user-journey for Fitness users who want to start from managing their business instead of designing their website.\n   * Will be accessible from Site List and will not have a website app.\n   * Once the user attaches a site, the site will become a regular wixsite.\n   */\n  SITELESS_BUSINESS = 'SITELESS_BUSINESS',\n  /** Belongs to \"strategic products\" company. Supports new product in the creator's economy space. */\n  CREATOR_ECONOMY = 'CREATOR_ECONOMY',\n  /** It is to be used in the Business First efforts. */\n  DASHBOARD_FIRST = 'DASHBOARD_FIRST',\n  /** Bookings business flow with no site. */\n  ANYWHERE = 'ANYWHERE',\n  /** Namespace for Headless Backoffice with no editor */\n  HEADLESS = 'HEADLESS',\n  /**\n   * Namespace for master site that will exist in parent account that will be referenced by subaccounts\n   * The site will be used for account level CSM feature for enterprise\n   */\n  ACCOUNT_MASTER_CMS = 'ACCOUNT_MASTER_CMS',\n  /** Rise.ai Siteless account management for Gift Cards and Store Credit. */\n  RISE = 'RISE',\n  /**\n   * As part of the branded app new funnel, users now can create a meta site that will be branded app first.\n   * There's a blank site behind the scene but it's blank).\n   * The Mobile company will be the owner of this namespace.\n   */\n  BRANDED_FIRST = 'BRANDED_FIRST',\n  /** Nownia.com Siteless account management for Ai Scheduling Assistant. */\n  NOWNIA = 'NOWNIA',\n  /**\n   * UGC Templates are templates that are created by users for personal use and to sale to other users.\n   * The Partners company owns this namespace.\n   */\n  UGC_TEMPLATE = 'UGC_TEMPLATE',\n  /** Codux Headless Sites */\n  CODUX = 'CODUX',\n  /** Bobb - AI Design Creator. */\n  MEDIA_DESIGN_CREATOR = 'MEDIA_DESIGN_CREATOR',\n  /**\n   * Shared Blog Site is a unique single site across Enterprise account,\n   * This site will hold all Blog posts related to the Marketing product.\n   */\n  SHARED_BLOG_ENTERPRISE = 'SHARED_BLOG_ENTERPRISE',\n  /** Standalone forms (siteless). MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  STANDALONE_FORMS = 'STANDALONE_FORMS',\n  /** Standalone events (siteless). MetaSites with this namespace will *not* be shown in a user's site list by default. */\n  STANDALONE_EVENTS = 'STANDALONE_EVENTS',\n  /** MIMIR - Siteless account for MIMIR Ai Job runner. */\n  MIMIR = 'MIMIR',\n}\n\n/** @enumType */\nexport type NamespaceWithLiterals =\n  | Namespace\n  | 'UNKNOWN_NAMESPACE'\n  | 'WIX'\n  | 'SHOUT_OUT'\n  | 'ALBUMS'\n  | 'WIX_STORES_TEST_DRIVE'\n  | 'HOTELS'\n  | 'CLUBS'\n  | 'ONBOARDING_DRAFT'\n  | 'DEV_SITE'\n  | 'LOGOS'\n  | 'VIDEO_MAKER'\n  | 'PARTNER_DASHBOARD'\n  | 'DEV_CENTER_COMPANY'\n  | 'HTML_DRAFT'\n  | 'SITELESS_BUSINESS'\n  | 'CREATOR_ECONOMY'\n  | 'DASHBOARD_FIRST'\n  | 'ANYWHERE'\n  | 'HEADLESS'\n  | 'ACCOUNT_MASTER_CMS'\n  | 'RISE'\n  | 'BRANDED_FIRST'\n  | 'NOWNIA'\n  | 'UGC_TEMPLATE'\n  | 'CODUX'\n  | 'MEDIA_DESIGN_CREATOR'\n  | 'SHARED_BLOG_ENTERPRISE'\n  | 'STANDALONE_FORMS'\n  | 'STANDALONE_EVENTS'\n  | 'MIMIR';\n\n/** Site transferred to another user. */\nexport interface SiteTransferred {\n  /**\n   * A previous owner id (user that transfers meta site).\n   * @format GUID\n   */\n  oldOwnerId?: string;\n  /**\n   * A new owner id (user that accepts meta site).\n   * @format GUID\n   */\n  newOwnerId?: string;\n}\n\n/** Soft deletion of the meta site. Could be restored. */\nexport interface SiteDeleted {\n  /** A deletion context. */\n  deleteContext?: DeleteContext;\n}\n\nexport interface DeleteContext {\n  /** When the meta site was deleted. */\n  dateDeleted?: Date | null;\n  /** A status. */\n  deleteStatus?: DeleteStatusWithLiterals;\n  /**\n   * A reason (flow).\n   * @maxLength 255\n   */\n  deleteOrigin?: string;\n  /**\n   * A service that deleted it.\n   * @maxLength 255\n   */\n  initiatorId?: string | null;\n}\n\nexport enum DeleteStatus {\n  UNKNOWN = 'UNKNOWN',\n  TRASH = 'TRASH',\n  DELETED = 'DELETED',\n  PENDING_PURGE = 'PENDING_PURGE',\n  PURGED_EXTERNALLY = 'PURGED_EXTERNALLY',\n}\n\n/** @enumType */\nexport type DeleteStatusWithLiterals =\n  | DeleteStatus\n  | 'UNKNOWN'\n  | 'TRASH'\n  | 'DELETED'\n  | 'PENDING_PURGE'\n  | 'PURGED_EXTERNALLY';\n\n/** Restoration of the meta site. */\nexport interface SiteUndeleted {}\n\n/** First publish of a meta site. Or subsequent publish after unpublish. */\nexport interface SitePublished {}\n\nexport interface SiteUnpublished {\n  /**\n   * A list of URLs previously associated with the meta site.\n   * @maxLength 4000\n   * @maxSize 10000\n   */\n  urls?: string[];\n}\n\nexport interface SiteMarkedAsTemplate {}\n\nexport interface SiteMarkedAsWixSite {}\n\n/**\n * Represents a service provisioned a site.\n *\n * Note on `origin_instance_id`:\n * There is no guarantee that you will be able to find a meta site using `origin_instance_id`.\n * This is because of the following scenario:\n *\n * Imagine you have a template where a third-party application (TPA) includes some stub data,\n * such as a product catalog. When you create a site from this template, you inherit this\n * default product catalog. However, if the template's product catalog is modified,\n * your site will retain the catalog as it was at the time of site creation. This ensures that\n * your site remains consistent with what you initially received and does not include any\n * changes made to the original template afterward.\n * To ensure this, the TPA on the template gets a new instance_id.\n */\nexport interface ServiceProvisioned {\n  /**\n   * Either UUID or EmbeddedServiceType.\n   * @maxLength 36\n   */\n  appDefId?: string;\n  /**\n   * Not only UUID. Something here could be something weird.\n   * @maxLength 36\n   */\n  instanceId?: string;\n  /**\n   * An instance id from which this instance is originated.\n   * @maxLength 36\n   */\n  originInstanceId?: string;\n  /**\n   * A version.\n   * @maxLength 500\n   */\n  version?: string | null;\n  /**\n   * The origin meta site id\n   * @format GUID\n   */\n  originMetaSiteId?: string | null;\n}\n\nexport interface ServiceRemoved {\n  /**\n   * Either UUID or EmbeddedServiceType.\n   * @maxLength 36\n   */\n  appDefId?: string;\n  /**\n   * Not only UUID. Something here could be something weird.\n   * @maxLength 36\n   */\n  instanceId?: string;\n  /**\n   * A version.\n   * @maxLength 500\n   */\n  version?: string | null;\n}\n\n/** Rename of the site. Meaning, free public url has been changed as well. */\nexport interface SiteRenamed {\n  /**\n   * A new meta site name (URL slug).\n   * @maxLength 20\n   */\n  newSiteName?: string;\n  /**\n   * A previous meta site name (URL slug).\n   * @maxLength 255\n   */\n  oldSiteName?: string;\n}\n\n/**\n * Hard deletion of the meta site.\n *\n * Could not be restored. Therefore it's desirable to cleanup data.\n */\nexport interface SiteHardDeleted {\n  /** A deletion context. */\n  deleteContext?: DeleteContext;\n}\n\nexport interface NamespaceChanged {\n  /** A previous namespace. */\n  oldNamespace?: NamespaceWithLiterals;\n  /** A new namespace. */\n  newNamespace?: NamespaceWithLiterals;\n}\n\n/** Assigned Studio editor */\nexport interface StudioAssigned {}\n\n/** Unassigned Studio editor */\nexport interface StudioUnassigned {}\n\n/**\n * Fired in case site URLs were changed in any way: new secondary domain, published, account slug rename, site rename etc.\n *\n * This is an internal event, it's not propagated in special events, because it's non-actionable. If you need to keep up\n * with sites and its urls, you need to listen to another topic/event. Read about it:\n *\n * https://bo.wix.com/wix-docs/rest/meta-site/meta-site---urls-service\n */\nexport interface SiteUrlChanged {}\n\n/**\n * Used at the end of the deletion flow for both draft sites and when a user deletes a site.\n * Consumed by other teams to remove relevant data.\n */\nexport interface SitePurgedExternally {\n  /**\n   * @maxLength 2048\n   * @maxSize 100\n   * @deprecated\n   * @targetRemovalDate 2025-04-15\n   */\n  appDefId?: string[];\n}\n\n/** Assigned Odeditor */\nexport interface OdeditorAssigned {}\n\n/** Unassigned Odeditor */\nexport interface OdeditorUnassigned {}\n\n/** Assigned Picasso editor */\nexport interface PicassoAssigned {}\n\n/** Unassigned Picasso */\nexport interface PicassoUnassigned {}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type CreateBookingPolicyValidationErrors =\n  | {\n      ruleName?: 'BOTH_LATE_AND_AFTER_START_POLICIES_NOT_ALLOWED';\n    }\n  | {\n      ruleName?: 'INVALID_BOOKING_WINDOW';\n    }\n  | {\n      ruleName?: 'CANCELLATION_FEE_WITHOUT_CREDIT_CARD';\n    }\n  | {\n      ruleName?: 'CANCELLATION_FEE_WITHOUT_DESCRIPTION';\n    }\n  | {\n      ruleName?: 'INVALID_CANCELLATION_WINDOWS_ORDER';\n    }\n  | {\n      ruleName?: 'EMPTY_CANCELLATION_WINDOWS_LIST';\n    };\n/** @docsIgnore */\nexport type UpdateBookingPolicyValidationErrors =\n  | {\n      ruleName?: 'BOTH_LATE_AND_AFTER_START_POLICIES_NOT_ALLOWED';\n    }\n  | {\n      ruleName?: 'INVALID_BOOKING_WINDOW';\n    }\n  | {\n      ruleName?: 'CANCELLATION_FEE_WITHOUT_CREDIT_CARD';\n    }\n  | {\n      ruleName?: 'CANCELLATION_FEE_WITHOUT_DESCRIPTION';\n    }\n  | {\n      ruleName?: 'INVALID_CANCELLATION_WINDOWS_ORDER';\n    }\n  | {\n      ruleName?: 'EMPTY_CANCELLATION_WINDOWS_LIST';\n    };\n/** @docsIgnore */\nexport type DeleteBookingPolicyApplicationErrors = {\n  code?: 'DEFAULT_POLICY_CANNOT_BE_DELETED';\n  description?: string;\n  data?: Record<string, any>;\n};\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface BookingPolicyCreatedEnvelope {\n  entity: BookingPolicy;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking policy is created.\n * @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @webhook\n * @eventType wix.bookings.v1.booking_policy_created\n * @slug created\n */\nexport declare function onBookingPolicyCreated(\n  handler: (event: BookingPolicyCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface BookingPolicyDefaultBookingPolicySetEnvelope {\n  data: DefaultBookingPolicySet;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when the site's default policy changes. Then,\n * _Booking Policy Updated_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/on-booking-policy-updated) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/booking-policy-updated))\n * is also triggered both for the new and the previous default policy.\n * @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @webhook\n * @eventType wix.bookings.v1.booking_policy_default_booking_policy_set\n * @slug default_booking_policy_set\n */\nexport declare function onBookingPolicyDefaultBookingPolicySet(\n  handler: (\n    event: BookingPolicyDefaultBookingPolicySetEnvelope\n  ) => void | Promise<void>\n): void;\n\nexport interface BookingPolicyDeletedEnvelope {\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking policy is deleted.\n * @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @webhook\n * @eventType wix.bookings.v1.booking_policy_deleted\n * @slug deleted\n */\nexport declare function onBookingPolicyDeleted(\n  handler: (event: BookingPolicyDeletedEnvelope) => void | Promise<void>\n): void;\n\nexport interface BookingPolicyUpdatedEnvelope {\n  entity: BookingPolicy;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking policy is updated, including when a policy's\n * `default` attribute changes.\n * @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @webhook\n * @eventType wix.bookings.v1.booking_policy_updated\n * @slug updated\n */\nexport declare function onBookingPolicyUpdated(\n  handler: (event: BookingPolicyUpdatedEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Creates a booking policy.\n * @param bookingPolicy - Booking policy to create.\n * @public\n * @requiredField bookingPolicy\n * @permissionId BOOKINGS.BOOKING_POLICY_CREATE\n * @applicableIdentity APP\n * @returns Created booking policy.\n * @fqn wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy\n */\nexport async function createBookingPolicy(\n  bookingPolicy: BookingPolicy\n): Promise<\n  NonNullablePaths<\n    BookingPolicy,\n    | `customPolicyDescription.enabled`\n    | `customPolicyDescription.description`\n    | `limitEarlyBookingPolicy.enabled`\n    | `limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `limitLateBookingPolicy.enabled`\n    | `limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookAfterStartPolicy.enabled`\n    | `cancellationPolicy.enabled`\n    | `cancellationPolicy.limitLatestCancellation`\n    | `cancellationPolicy.latestCancellationInMinutes`\n    | `reschedulePolicy.enabled`\n    | `reschedulePolicy.limitLatestReschedule`\n    | `reschedulePolicy.latestRescheduleInMinutes`\n    | `waitlistPolicy.enabled`\n    | `waitlistPolicy.capacity`\n    | `waitlistPolicy.reservationTimeInMinutes`\n    | `participantsPolicy.maxParticipantsPerBooking`\n    | `cancellationFeePolicy.enabled`\n    | `cancellationFeePolicy.cancellationWindows`\n    | `cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n    | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n    | `cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `saveCreditCardPolicy.enabled`,\n    6\n  > & {\n    __validationErrorsType?: CreateBookingPolicyValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingPolicy: bookingPolicy,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV1BookingPolicy.createBookingPolicy(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.bookingPolicy!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingPolicy: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingPolicy']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a booking policy.\n * @param bookingPolicyId - ID of the booking policy to retrieve.\n * @public\n * @requiredField bookingPolicyId\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @applicableIdentity APP\n * @returns Retrieved booking policy.\n * @fqn wix.bookings.v1.BookingPoliciesService.GetBookingPolicy\n */\nexport async function getBookingPolicy(\n  bookingPolicyId: string\n): Promise<\n  NonNullablePaths<\n    BookingPolicy,\n    | `customPolicyDescription.enabled`\n    | `customPolicyDescription.description`\n    | `limitEarlyBookingPolicy.enabled`\n    | `limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `limitLateBookingPolicy.enabled`\n    | `limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookAfterStartPolicy.enabled`\n    | `cancellationPolicy.enabled`\n    | `cancellationPolicy.limitLatestCancellation`\n    | `cancellationPolicy.latestCancellationInMinutes`\n    | `reschedulePolicy.enabled`\n    | `reschedulePolicy.limitLatestReschedule`\n    | `reschedulePolicy.latestRescheduleInMinutes`\n    | `waitlistPolicy.enabled`\n    | `waitlistPolicy.capacity`\n    | `waitlistPolicy.reservationTimeInMinutes`\n    | `participantsPolicy.maxParticipantsPerBooking`\n    | `cancellationFeePolicy.enabled`\n    | `cancellationFeePolicy.cancellationWindows`\n    | `cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n    | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n    | `cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `saveCreditCardPolicy.enabled`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingPolicyId: bookingPolicyId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV1BookingPolicy.getBookingPolicy(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.bookingPolicy!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingPolicyId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingPolicyId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves the strictest version of each policy rule from a list of booking\n * policies.\n *\n *\n * Returns a hypothetical `bookingPolicy` object that combines the strictest\n * version of each rule. The `id` of the returned policy is `null` and no\n * corresponding `bookingPolicy` object is created. To create a new policy, you\n * can call *Create Booking Policy*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).\n * @param bookingPolicyIds - IDs of the booking policies for which to retrieve the strictest rules for.\n * @public\n * @requiredField bookingPolicyIds\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy\n */\nexport async function getStrictestBookingPolicy(\n  bookingPolicyIds: string[]\n): Promise<\n  NonNullablePaths<\n    GetStrictestBookingPolicyResponse,\n    | `bookingPolicy.customPolicyDescription.enabled`\n    | `bookingPolicy.customPolicyDescription.description`\n    | `bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `bookingPolicy.limitLateBookingPolicy.enabled`\n    | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookingPolicy.bookAfterStartPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `bookingPolicy.reschedulePolicy.enabled`\n    | `bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `bookingPolicy.waitlistPolicy.enabled`\n    | `bookingPolicy.waitlistPolicy.capacity`\n    | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `bookingPolicy.cancellationFeePolicy.enabled`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `bookingPolicy.saveCreditCardPolicy.enabled`,\n    7\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingPolicyIds: bookingPolicyIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV1BookingPolicy.getStrictestBookingPolicy(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingPolicyIds: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingPolicyIds']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates a booking policy.\n *\n *\n * Each time the booking policy is updated, `revision` increments by 1.\n * The current `revision` must be specified when updating the booking policy.\n * This ensures you're working with the latest booking policy and prevents unintended overwrites.\n * @param _id - ID of the booking policy.\n * @public\n * @requiredField _id\n * @requiredField bookingPolicy\n * @requiredField bookingPolicy.revision\n * @permissionId BOOKINGS.BOOKING_POLICY_UPDATE\n * @applicableIdentity APP\n * @returns Updated booking policy.\n * @fqn wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy\n */\nexport async function updateBookingPolicy(\n  _id: string,\n  bookingPolicy: NonNullablePaths<UpdateBookingPolicy, `revision`, 2>\n): Promise<\n  NonNullablePaths<\n    BookingPolicy,\n    | `customPolicyDescription.enabled`\n    | `customPolicyDescription.description`\n    | `limitEarlyBookingPolicy.enabled`\n    | `limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `limitLateBookingPolicy.enabled`\n    | `limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookAfterStartPolicy.enabled`\n    | `cancellationPolicy.enabled`\n    | `cancellationPolicy.limitLatestCancellation`\n    | `cancellationPolicy.latestCancellationInMinutes`\n    | `reschedulePolicy.enabled`\n    | `reschedulePolicy.limitLatestReschedule`\n    | `reschedulePolicy.latestRescheduleInMinutes`\n    | `waitlistPolicy.enabled`\n    | `waitlistPolicy.capacity`\n    | `waitlistPolicy.reservationTimeInMinutes`\n    | `participantsPolicy.maxParticipantsPerBooking`\n    | `cancellationFeePolicy.enabled`\n    | `cancellationFeePolicy.cancellationWindows`\n    | `cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n    | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n    | `cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `saveCreditCardPolicy.enabled`,\n    6\n  > & {\n    __validationErrorsType?: UpdateBookingPolicyValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingPolicy: { ...bookingPolicy, id: _id },\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV1BookingPolicy.updateBookingPolicy(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.bookingPolicy!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { bookingPolicy: '$[1]' },\n        explicitPathsToArguments: { 'bookingPolicy.id': '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'bookingPolicy']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateBookingPolicy {\n  /**\n   * ID of the booking policy.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the booking policy is updated.\n   * To prevent conflicting changes, the current `revision` must be passed when\n   * updating the booking policy.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Date and time the booking policy was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the booking policy was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the booking policy.\n   * @maxLength 400\n   */\n  name?: string | null;\n  /**\n   * Custom description for the booking policy and whether it's displayed to the\n   * participant.\n   */\n  customPolicyDescription?: PolicyDescription;\n  /**\n   * Whether the booking policy is the default.\n   * @readonly\n   */\n  default?: boolean | null;\n  /** Rule for limiting early bookings. */\n  limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;\n  /**\n   * Rule for limiting late bookings. This rule and `bookAfterStartPolicy` can't\n   * be both enabled at the same time.\n   */\n  limitLateBookingPolicy?: LimitLateBookingPolicy;\n  /**\n   * Rule for booking after the start of a session or course. This rule and\n   * `limitLateBookingPolicy` can't be both enabled at the same time.\n   */\n  bookAfterStartPolicy?: BookAfterStartPolicy;\n  /** Rule for canceling a booking. */\n  cancellationPolicy?: CancellationPolicy;\n  /** Rule for rescheduling a booking. */\n  reschedulePolicy?: ReschedulePolicy;\n  /** Waitlist rule for the service. */\n  waitlistPolicy?: WaitlistPolicy;\n  /** Rule for participants per booking. */\n  participantsPolicy?: ParticipantsPolicy;\n  /** Rules for cancellation fees. */\n  cancellationFeePolicy?: CancellationFeePolicy;\n  /** Rule for saving credit card details. */\n  saveCreditCardPolicy?: SaveCreditCardPolicy;\n  /** Extensions enabling users to save custom data related to the booking policies. */\n  extendedFields?: ExtendedFields;\n}\n\n/**\n * Sets a booking policy as the default.\n *\n *\n * Also updates the site's current default policy by setting its `default`\n * attribute to `false`. If the provided policy is already the site's\n * default, the call succeeds without changing any `bookingPolicy` object.\n * @param bookingPolicyId - ID of the booking policy that's set as default.\n * @public\n * @requiredField bookingPolicyId\n * @permissionId BOOKINGS.BOOKING_POLICY_SET_DEFAULT\n * @applicableIdentity APP\n * @fqn wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy\n */\nexport async function setDefaultBookingPolicy(\n  bookingPolicyId: string\n): Promise<\n  NonNullablePaths<\n    SetDefaultBookingPolicyResponse,\n    | `currentDefaultBookingPolicy.customPolicyDescription.enabled`\n    | `currentDefaultBookingPolicy.customPolicyDescription.description`\n    | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `currentDefaultBookingPolicy.limitLateBookingPolicy.enabled`\n    | `currentDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `currentDefaultBookingPolicy.bookAfterStartPolicy.enabled`\n    | `currentDefaultBookingPolicy.cancellationPolicy.enabled`\n    | `currentDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `currentDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `currentDefaultBookingPolicy.reschedulePolicy.enabled`\n    | `currentDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `currentDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `currentDefaultBookingPolicy.waitlistPolicy.enabled`\n    | `currentDefaultBookingPolicy.waitlistPolicy.capacity`\n    | `currentDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `currentDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `currentDefaultBookingPolicy.cancellationFeePolicy.enabled`\n    | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n    | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n    | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `currentDefaultBookingPolicy.saveCreditCardPolicy.enabled`\n    | `previousDefaultBookingPolicy.customPolicyDescription.enabled`\n    | `previousDefaultBookingPolicy.customPolicyDescription.description`\n    | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `previousDefaultBookingPolicy.limitLateBookingPolicy.enabled`\n    | `previousDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `previousDefaultBookingPolicy.bookAfterStartPolicy.enabled`\n    | `previousDefaultBookingPolicy.cancellationPolicy.enabled`\n    | `previousDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `previousDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `previousDefaultBookingPolicy.reschedulePolicy.enabled`\n    | `previousDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `previousDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `previousDefaultBookingPolicy.waitlistPolicy.enabled`\n    | `previousDefaultBookingPolicy.waitlistPolicy.capacity`\n    | `previousDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `previousDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `previousDefaultBookingPolicy.cancellationFeePolicy.enabled`\n    | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value`\n    | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency`\n    | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `previousDefaultBookingPolicy.saveCreditCardPolicy.enabled`,\n    7\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingPolicyId: bookingPolicyId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV1BookingPolicy.setDefaultBookingPolicy(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingPolicyId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingPolicyId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Deletes a booking policy.\n *\n *\n * You can't delete the default policy without first *setting a different policy as default*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/set-default-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/set-default-booking-policy)).\n * @param bookingPolicyId - ID of the booking policy to delete.\n * @public\n * @requiredField bookingPolicyId\n * @permissionId BOOKINGS.BOOKING_POLICY_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy\n */\nexport async function deleteBookingPolicy(bookingPolicyId: string): Promise<\n  void & {\n    __applicationErrorsType?: DeleteBookingPolicyApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingPolicyId: bookingPolicyId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV1BookingPolicy.deleteBookingPolicy(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingPolicyId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingPolicyId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Creates a query to retrieve a list of `bookingPolicy` objects.\n *\n * The `queryBookingPolicies()` function builds a query to retrieve a list of `bookingPolicy` objects and returns a `BookingPoliciesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-builder/find) function.\n *\n * You can refine the query by chaining `BookingPoliciesQueryBuilder` functions onto the query. `BookingPoliciesQueryBuilder` functions enable you to sort, filter, and control the results that `queryBookingPolicies()` returns.\n *\n * `queryBookingPolicies()` runs with the following `BookingPoliciesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryBookingPolicies()` are applied in the order they are called. For example, if you apply `ascending(\"waitlistPolicy.capacity\")` and then `ascending(\"name\")`, the results are sorted first by `waitlistPolicy.capacity`, and then, if there are multiple results with the same `waitlistPolicy.capacity`, the items are sorted by `name`.\n *\n * The following `BookingPoliciesQueryBuilder` functions are supported for the `queryBookingPolicies()` function. For a full description of the `bookingPolicy` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-result/items) property in `BookingPoliciesQueryResult`.\n * @public\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies\n */\nexport function queryBookingPolicies(): BookingPoliciesQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    BookingPolicy,\n    'CURSOR',\n    QueryBookingPoliciesRequest,\n    QueryBookingPoliciesResponse\n  >({\n    func: async (payload: QueryBookingPoliciesRequest) => {\n      const reqOpts =\n        ambassadorWixBookingsV1BookingPolicy.queryBookingPolicies(payload);\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (query: QueryBookingPoliciesRequest['query']) => {\n      const args = [query, {}] as [QueryBookingPoliciesRequest['query'], {}];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({\n      data,\n    }: HttpResponse<QueryBookingPoliciesResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [])\n      );\n\n      return {\n        items: transformedData?.bookingPolicies,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'CURSOR',\n    transformationPaths: {},\n  });\n}\n\ninterface QueryCursorResult {\n  cursors: Cursors;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface BookingPoliciesQueryResult extends QueryCursorResult {\n  items: BookingPolicy[];\n  query: BookingPoliciesQueryBuilder;\n  next: () => Promise<BookingPoliciesQueryResult>;\n  prev: () => Promise<BookingPoliciesQueryResult>;\n}\n\nexport interface BookingPoliciesQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  eq: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'customPolicyDescription.enabled'\n      | 'customPolicyDescription.description'\n      | 'default'\n      | 'limitEarlyBookingPolicy.enabled'\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.enabled'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'bookAfterStartPolicy.enabled'\n      | 'cancellationPolicy.enabled'\n      | 'cancellationPolicy.limitLatestCancellation'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.enabled'\n      | 'reschedulePolicy.limitLatestReschedule'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.enabled'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.maxParticipantsPerBooking'\n      | 'resourcesPolicy.enabled'\n      | 'resourcesPolicy.autoAssignAllowed',\n    value: any\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ne: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'customPolicyDescription.enabled'\n      | 'customPolicyDescription.description'\n      | 'default'\n      | 'limitEarlyBookingPolicy.enabled'\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.enabled'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'bookAfterStartPolicy.enabled'\n      | 'cancellationPolicy.enabled'\n      | 'cancellationPolicy.limitLatestCancellation'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.enabled'\n      | 'reschedulePolicy.limitLatestReschedule'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.enabled'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.maxParticipantsPerBooking'\n      | 'resourcesPolicy.enabled'\n      | 'resourcesPolicy.autoAssignAllowed',\n    value: any\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ge: (\n    propertyName:\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.maxParticipantsPerBooking',\n    value: any\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  gt: (\n    propertyName:\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.maxParticipantsPerBooking',\n    value: any\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  le: (\n    propertyName:\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.maxParticipantsPerBooking',\n    value: any\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  lt: (\n    propertyName:\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.maxParticipantsPerBooking',\n    value: any\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `string`.\n   * @param string - String to compare against. Case-insensitive.\n   */\n  startsWith: (\n    propertyName: 'name' | 'customPolicyDescription.description',\n    value: string\n  ) => BookingPoliciesQueryBuilder;\n  in: (\n    propertyName:\n      | '_id'\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.maxParticipantsPerBooking',\n    value: any\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  ascending: (\n    ...propertyNames: Array<\n      | '_id'\n      | 'name'\n      | 'customPolicyDescription.enabled'\n      | 'customPolicyDescription.description'\n      | 'default'\n      | 'limitEarlyBookingPolicy.enabled'\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.enabled'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'bookAfterStartPolicy.enabled'\n      | 'cancellationPolicy.enabled'\n      | 'cancellationPolicy.limitLatestCancellation'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.enabled'\n      | 'reschedulePolicy.limitLatestReschedule'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.enabled'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.enabled'\n      | 'participantsPolicy.maxParticipantsPerBooking'\n      | 'resourcesPolicy.enabled'\n      | 'resourcesPolicy.autoAssignAllowed'\n    >\n  ) => BookingPoliciesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  descending: (\n    ...propertyNames: Array<\n      | '_id'\n      | 'name'\n      | 'customPolicyDescription.enabled'\n      | 'customPolicyDescription.description'\n      | 'default'\n      | 'limitEarlyBookingPolicy.enabled'\n      | 'limitEarlyBookingPolicy.earliestBookingInMinutes'\n      | 'limitLateBookingPolicy.enabled'\n      | 'limitLateBookingPolicy.latestBookingInMinutes'\n      | 'bookAfterStartPolicy.enabled'\n      | 'cancellationPolicy.enabled'\n      | 'cancellationPolicy.limitLatestCancellation'\n      | 'cancellationPolicy.latestCancellationInMinutes'\n      | 'reschedulePolicy.enabled'\n      | 'reschedulePolicy.limitLatestReschedule'\n      | 'reschedulePolicy.latestRescheduleInMinutes'\n      | 'waitlistPolicy.enabled'\n      | 'waitlistPolicy.capacity'\n      | 'waitlistPolicy.reservationTimeInMinutes'\n      | 'participantsPolicy.enabled'\n      | 'participantsPolicy.maxParticipantsPerBooking'\n      | 'resourcesPolicy.enabled'\n      | 'resourcesPolicy.autoAssignAllowed'\n    >\n  ) => BookingPoliciesQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */\n  limit: (limit: number) => BookingPoliciesQueryBuilder;\n  /** @param cursor - A pointer to specific record */\n  skipTo: (cursor: string) => BookingPoliciesQueryBuilder;\n  find: () => Promise<BookingPoliciesQueryResult>;\n}\n\n/**\n * Counts booking policies, given the provided filtering.\n *\n *\n * See *supported filters*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n * for a complete list of supported filters.\n * @public\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.v1.BookingPoliciesService.CountBookingPolicies\n */\nexport async function countBookingPolicies(\n  options?: CountBookingPoliciesOptions\n): Promise<NonNullablePaths<CountBookingPoliciesResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV1BookingPolicy.countBookingPolicies(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountBookingPoliciesOptions {\n  /**\n   * Filter to base the count on. See\n   * supported filters*\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n   * for details.\n   */\n  filter?: Record<string, any> | null;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsV1BookingPoliciesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/booking-policies',\n        destPath: '/v1/booking-policies',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/booking-policies',\n        destPath: '/v1/booking-policies',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings/v1/booking-policies/strictest',\n        destPath: '/v1/booking-policies/strictest',\n      },\n      {\n        srcPath: '/_api/bookings/v1/booking-policies/query',\n        destPath: '/v1/booking-policies/query',\n      },\n      {\n        srcPath: '/bookings/v1/booking-policies',\n        destPath: '/v1/booking-policies',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_booking-policies';\n\n/** Creates a booking policy. */\nexport function createBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __createBookingPolicy({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'bookingPolicy.createdDate' },\n          { path: 'bookingPolicy.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createBookingPolicy;\n}\n\n/** Retrieves a booking policy. */\nexport function getBookingPolicy(payload: object): RequestOptionsFactory<any> {\n  function __getBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.GetBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicyId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getBookingPolicy;\n}\n\n/**\n * Retrieves the strictest version of each policy rule from a list of booking\n * policies.\n *\n *\n * Returns a hypothetical `bookingPolicy` object that combines the strictest\n * version of each rule. The `id` of the returned policy is `null` and no\n * corresponding `bookingPolicy` object is created. To create a new policy, you\n * can call *Create Booking Policy*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).\n */\nexport function getStrictestBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getStrictestBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/strictest',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getStrictestBookingPolicy;\n}\n\n/**\n * Updates a booking policy.\n *\n *\n * Each time the booking policy is updated, `revision` increments by 1.\n * The current `revision` must be specified when updating the booking policy.\n * This ensures you're working with the latest booking policy and prevents unintended overwrites.\n */\nexport function updateBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __updateBookingPolicy({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'bookingPolicy.createdDate' },\n          { path: 'bookingPolicy.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicy.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicy.createdDate' },\n              { path: 'bookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateBookingPolicy;\n}\n\n/**\n * Sets a booking policy as the default.\n *\n *\n * Also updates the site's current default policy by setting its `default`\n * attribute to `false`. If the provided policy is already the site's\n * default, the call succeeds without changing any `bookingPolicy` object.\n */\nexport function setDefaultBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __setDefaultBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicyId}:setDefault',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'currentDefaultBookingPolicy.createdDate' },\n              { path: 'currentDefaultBookingPolicy.updatedDate' },\n              { path: 'previousDefaultBookingPolicy.createdDate' },\n              { path: 'previousDefaultBookingPolicy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setDefaultBookingPolicy;\n}\n\n/**\n * Deletes a booking policy.\n *\n *\n * You can't delete the default policy without first *setting a different policy as default*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/set-default-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/set-default-booking-policy)).\n */\nexport function deleteBookingPolicy(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __deleteBookingPolicy({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/{bookingPolicyId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteBookingPolicy;\n}\n\n/**\n * Creates a query to retrieve a list of `bookingPolicy` objects.\n *\n * The `queryBookingPolicies()` function builds a query to retrieve a list of `bookingPolicy` objects and returns a `BookingPoliciesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-builder/find) function.\n *\n * You can refine the query by chaining `BookingPoliciesQueryBuilder` functions onto the query. `BookingPoliciesQueryBuilder` functions enable you to sort, filter, and control the results that `queryBookingPolicies()` returns.\n *\n * `queryBookingPolicies()` runs with the following `BookingPoliciesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryBookingPolicies()` are applied in the order they are called. For example, if you apply `ascending(\"waitlistPolicy.capacity\")` and then `ascending(\"name\")`, the results are sorted first by `waitlistPolicy.capacity`, and then, if there are multiple results with the same `waitlistPolicy.capacity`, the items are sorted by `name`.\n *\n * The following `BookingPoliciesQueryBuilder` functions are supported for the `queryBookingPolicies()` function. For a full description of the `bookingPolicy` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-result/items) property in `BookingPoliciesQueryResult`.\n */\nexport function queryBookingPolicies(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryBookingPolicies({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicies.createdDate' },\n              { path: 'bookingPolicies.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryBookingPolicies;\n}\n\n/**\n * Counts booking policies, given the provided filtering.\n *\n *\n * See *supported filters*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n * for a complete list of supported filters.\n */\nexport function countBookingPolicies(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __countBookingPolicies({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.booking_policy',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.v1.BookingPoliciesService.CountBookingPolicies',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsV1BookingPoliciesServiceUrl({\n        protoPath: '/v1/booking-policies/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countBookingPolicies;\n}\n", "import {\n  createBookingPolicy as publicCreateBookingPolicy,\n  getBookingPolicy as publicGetBookingPolicy,\n  getStrictestBookingPolicy as publicGetStrictestBookingPolicy,\n  updateBookingPolicy as publicUpdateBookingPolicy,\n  setDefaultBookingPolicy as publicSetDefaultBookingPolicy,\n  deleteBookingPolicy as publicDeleteBookingPolicy,\n  queryBookingPolicies as publicQueryBookingPolicies,\n  countBookingPolicies as publicCountBookingPolicies,\n} from './bookings-v1-booking-policy-booking-policies.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { createEventModule } from '@wix/sdk-runtime/event-definition-modules';\nimport {\n  BuildRESTFunction,\n  MaybeContext,\n  BuildEventDefinition,\n} from '@wix/sdk-types';\nimport { onBookingPolicyCreated as publicOnBookingPolicyCreated } from './bookings-v1-booking-policy-booking-policies.public.js';\nimport { onBookingPolicyDefaultBookingPolicySet as publicOnBookingPolicyDefaultBookingPolicySet } from './bookings-v1-booking-policy-booking-policies.public.js';\nimport { onBookingPolicyDeleted as publicOnBookingPolicyDeleted } from './bookings-v1-booking-policy-booking-policies.public.js';\nimport { onBookingPolicyUpdated as publicOnBookingPolicyUpdated } from './bookings-v1-booking-policy-booking-policies.public.js';\n\nexport const createBookingPolicy: MaybeContext<\n  BuildRESTFunction<typeof publicCreateBookingPolicy> &\n    typeof publicCreateBookingPolicy\n> = /*#__PURE__*/ createRESTModule(publicCreateBookingPolicy);\nexport const getBookingPolicy: MaybeContext<\n  BuildRESTFunction<typeof publicGetBookingPolicy> &\n    typeof publicGetBookingPolicy\n> = /*#__PURE__*/ createRESTModule(publicGetBookingPolicy);\nexport const getStrictestBookingPolicy: MaybeContext<\n  BuildRESTFunction<typeof publicGetStrictestBookingPolicy> &\n    typeof publicGetStrictestBookingPolicy\n> = /*#__PURE__*/ createRESTModule(publicGetStrictestBookingPolicy);\nexport const updateBookingPolicy: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateBookingPolicy> &\n    typeof publicUpdateBookingPolicy\n> = /*#__PURE__*/ createRESTModule(publicUpdateBookingPolicy);\nexport const setDefaultBookingPolicy: MaybeContext<\n  BuildRESTFunction<typeof publicSetDefaultBookingPolicy> &\n    typeof publicSetDefaultBookingPolicy\n> = /*#__PURE__*/ createRESTModule(publicSetDefaultBookingPolicy);\nexport const deleteBookingPolicy: MaybeContext<\n  BuildRESTFunction<typeof publicDeleteBookingPolicy> &\n    typeof publicDeleteBookingPolicy\n> = /*#__PURE__*/ createRESTModule(publicDeleteBookingPolicy);\nexport const queryBookingPolicies: MaybeContext<\n  BuildRESTFunction<typeof publicQueryBookingPolicies> &\n    typeof publicQueryBookingPolicies\n> = /*#__PURE__*/ createRESTModule(publicQueryBookingPolicies);\nexport const countBookingPolicies: MaybeContext<\n  BuildRESTFunction<typeof publicCountBookingPolicies> &\n    typeof publicCountBookingPolicies\n> = /*#__PURE__*/ createRESTModule(publicCountBookingPolicies);\n/**\n * Triggered when a booking policy is created.\n */\nexport const onBookingPolicyCreated: BuildEventDefinition<\n  typeof publicOnBookingPolicyCreated\n> = createEventModule(publicOnBookingPolicyCreated);\n/**\n * Triggered when the site's default policy changes. Then,\n * _Booking Policy Updated_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/on-booking-policy-updated) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/booking-policy-updated))\n * is also triggered both for the new and the previous default policy.\n */\nexport const onBookingPolicyDefaultBookingPolicySet: BuildEventDefinition<\n  typeof publicOnBookingPolicyDefaultBookingPolicySet\n> = createEventModule(publicOnBookingPolicyDefaultBookingPolicySet);\n/**\n * Triggered when a booking policy is deleted.\n */\nexport const onBookingPolicyDeleted: BuildEventDefinition<\n  typeof publicOnBookingPolicyDeleted\n> = createEventModule(publicOnBookingPolicyDeleted);\n/**\n * Triggered when a booking policy is updated, including when a policy's\n * `default` attribute changes.\n */\nexport const onBookingPolicyUpdated: BuildEventDefinition<\n  typeof publicOnBookingPolicyUpdated\n> = createEventModule(publicOnBookingPolicyUpdated);\n\nexport {\n  SortOrder,\n  PlacementType,\n  DayOfWeek,\n  ResolutionMethod,\n  State,\n  SiteCreatedContext,\n  Namespace,\n  DeleteStatus,\n  WebhookIdentityType,\n} from './bookings-v1-booking-policy-booking-policies.universal.js';\nexport {\n  BookingPolicy,\n  PolicyDescription,\n  LimitEarlyBookingPolicy,\n  LimitLateBookingPolicy,\n  BookAfterStartPolicy,\n  CancellationPolicy,\n  ReschedulePolicy,\n  WaitlistPolicy,\n  ParticipantsPolicy,\n  ResourcesPolicy,\n  CancellationFeePolicy,\n  Money,\n  CancellationWindow,\n  CancellationWindowFeeOneOf,\n  SaveCreditCardPolicy,\n  ExtendedFields,\n  DefaultBookingPolicySet,\n  CreateBookingPolicyRequest,\n  CreateBookingPolicyResponse,\n  GetBookingPolicyRequest,\n  GetBookingPolicyResponse,\n  GetStrictestBookingPolicyRequest,\n  GetStrictestBookingPolicyResponse,\n  UpdateBookingPolicyRequest,\n  UpdateBookingPolicyResponse,\n  SetDefaultBookingPolicyRequest,\n  SetDefaultBookingPolicyResponse,\n  DeleteBookingPolicyRequest,\n  DeleteBookingPolicyResponse,\n  QueryBookingPoliciesRequest,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  Sorting,\n  CursorPaging,\n  QueryBookingPoliciesResponse,\n  CursorPagingMetadata,\n  Cursors,\n  CountBookingPoliciesRequest,\n  CountBookingPoliciesResponse,\n  UpdateAllPoliciesRequest,\n  UpdateAllPoliciesResponse,\n  CreateMissingDefaultPolicyRequest,\n  CreateMissingDefaultPolicyResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  RestoreInfo,\n  EntityUpdatedEvent,\n  EntityDeletedEvent,\n  ActionEvent,\n  Empty,\n  SitePropertiesNotification,\n  SitePropertiesEvent,\n  Properties,\n  Categories,\n  Locale,\n  Address,\n  AddressHint,\n  GeoCoordinates,\n  BusinessSchedule,\n  TimePeriod,\n  SpecialHourPeriod,\n  Multilingual,\n  SupportedLanguage,\n  ConsentPolicy,\n  Translation,\n  ChangeContext,\n  ChangeContextPayloadOneOf,\n  PropertiesChange,\n  V4SiteCreated,\n  SiteCloned,\n  MetaSiteSpecialEvent,\n  MetaSiteSpecialEventPayloadOneOf,\n  Asset,\n  SiteCreated,\n  SiteTransferred,\n  SiteDeleted,\n  DeleteContext,\n  SiteUndeleted,\n  SitePublished,\n  SiteUnpublished,\n  SiteMarkedAsTemplate,\n  SiteMarkedAsWixSite,\n  ServiceProvisioned,\n  ServiceRemoved,\n  SiteRenamed,\n  SiteHardDeleted,\n  NamespaceChanged,\n  StudioAssigned,\n  StudioUnassigned,\n  SiteUrlChanged,\n  SitePurgedExternally,\n  OdeditorAssigned,\n  OdeditorUnassigned,\n  PicassoAssigned,\n  PicassoUnassigned,\n  MessageEnvelope,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  BaseEventMetadata,\n  EventMetadata,\n  BookingPolicyCreatedEnvelope,\n  BookingPolicyDefaultBookingPolicySetEnvelope,\n  BookingPolicyDeletedEnvelope,\n  BookingPolicyUpdatedEnvelope,\n  UpdateBookingPolicy,\n  BookingPoliciesQueryResult,\n  BookingPoliciesQueryBuilder,\n  CountBookingPoliciesOptions,\n} from './bookings-v1-booking-policy-booking-policies.universal.js';\nexport {\n  SortOrderWithLiterals,\n  PlacementTypeWithLiterals,\n  DayOfWeekWithLiterals,\n  ResolutionMethodWithLiterals,\n  StateWithLiterals,\n  SiteCreatedContextWithLiterals,\n  NamespaceWithLiterals,\n  DeleteStatusWithLiterals,\n  WebhookIdentityTypeWithLiterals,\n  CreateBookingPolicyValidationErrors,\n  UpdateBookingPolicyValidationErrors,\n  DeleteBookingPolicyApplicationErrors,\n} from './bookings-v1-booking-policy-booking-policies.universal.js';\n"], "mappings": ";AAAA,SAAS,2CAAAA,gDAA+C;AACxD,SAAS,wCAAAC,6CAA4C;AACrD,SAAS,kBAAAC,uBAAsB;AAC/B,SAAS,uBAAqD;;;ACH9D,SAAS,kBAAkB,yBAAyB;AACpD,SAAS,oBAAoB;AAC7B;AAAA,EACE;AAAA,EACA;AAAA,OACK;;;ACLP,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,8CACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAGd,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,4BAA4B;AAAA,QACtC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,4BAA4B;AAAA,QACtC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,2CAA2C;AAAA,YACnD,EAAE,MAAM,2CAA2C;AAAA,UACrD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,8BAA8B;AAAA,YACtC,EAAE,MAAM,8BAA8B;AAAA,UACxC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,8CAA8C;AAAA,QACjD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AD7WA,SAAS,kBAAAC,uBAAsB;AAggBxB,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AA8SL,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,aAAU;AAHA,SAAAA;AAAA,GAAA;AAwDL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,aAAU;AACV,EAAAA,WAAA,eAAY;AACZ,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AA8DL,IAAK,mBAAL,kBAAKC,sBAAL;AACL,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,eAAY;AACZ,EAAAA,kBAAA,kBAAe;AAHL,SAAAA;AAAA,GAAA;AAwML,IAAK,QAAL,kBAAKC,WAAL;AACL,EAAAA,OAAA,aAAU;AACV,EAAAA,OAAA,aAAU;AACV,EAAAA,OAAA,cAAW;AACX,EAAAA,OAAA,aAAU;AACV,EAAAA,OAAA,UAAO;AALG,SAAAA;AAAA,GAAA;AA+CL,IAAK,qBAAL,kBAAKC,wBAAL;AAEL,EAAAA,oBAAA,WAAQ;AAER,EAAAA,oBAAA,mBAAgB;AAEhB,EAAAA,oBAAA,gCAA6B;AAE7B,EAAAA,oBAAA,eAAY;AAEZ,EAAAA,oBAAA,uBAAoB;AAEpB,EAAAA,oBAAA,WAAQ;AAZE,SAAAA;AAAA,GAAA;AAyBL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,uBAAoB;AAEpB,EAAAA,WAAA,SAAM;AAEN,EAAAA,WAAA,eAAY;AAEZ,EAAAA,WAAA,YAAS;AAET,EAAAA,WAAA,2BAAwB;AAExB,EAAAA,WAAA,YAAS;AAET,EAAAA,WAAA,WAAQ;AAER,EAAAA,WAAA,sBAAmB;AAEnB,EAAAA,WAAA,cAAW;AAEX,EAAAA,WAAA,WAAQ;AAER,EAAAA,WAAA,iBAAc;AAEd,EAAAA,WAAA,uBAAoB;AAEpB,EAAAA,WAAA,wBAAqB;AAMrB,EAAAA,WAAA,gBAAa;AAMb,EAAAA,WAAA,uBAAoB;AAEpB,EAAAA,WAAA,qBAAkB;AAElB,EAAAA,WAAA,qBAAkB;AAElB,EAAAA,WAAA,cAAW;AAEX,EAAAA,WAAA,cAAW;AAKX,EAAAA,WAAA,wBAAqB;AAErB,EAAAA,WAAA,UAAO;AAMP,EAAAA,WAAA,mBAAgB;AAEhB,EAAAA,WAAA,YAAS;AAKT,EAAAA,WAAA,kBAAe;AAEf,EAAAA,WAAA,WAAQ;AAER,EAAAA,WAAA,0BAAuB;AAKvB,EAAAA,WAAA,4BAAyB;AAEzB,EAAAA,WAAA,sBAAmB;AAEnB,EAAAA,WAAA,uBAAoB;AAEpB,EAAAA,WAAA,WAAQ;AAhFE,SAAAA;AAAA,GAAA;AA0JL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,WAAQ;AACR,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,mBAAgB;AAChB,EAAAA,cAAA,uBAAoB;AALV,SAAAA;AAAA,GAAA;AA4OL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAoPZ,eAAsBC,qBACpB,eA+BA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACiC,oBAAoB,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,eAAe,OAAO;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,eAAe;AAAA,IAClB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAYA,eAAsBC,kBACpB,iBA6BA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACiC,iBAAiB,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,iBAAiB,OAAO;AAAA,QACpD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB;AAAA,IACpB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmBA,eAAsBC,2BACpB,kBA6BA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACiC,0BAA0B,OAAO;AAExE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,kBAAkB,OAAO;AAAA,QACrD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,kBAAkB;AAAA,IACrB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmBA,eAAsBC,qBACpB,KACA,eA+BA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,eAAe,EAAE,GAAG,eAAe,IAAI,IAAI;AAAA,EAC7C,CAAC;AAED,QAAM,UACiC,oBAAoB,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,eAAe,OAAO;AAAA,QAChD,0BAA0B,EAAE,oBAAoB,OAAO;AAAA,QACvD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,eAAe;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmFA,eAAsBC,yBACpB,iBAoDA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACiC,wBAAwB,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,iBAAiB,OAAO;AAAA,QACpD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB;AAAA,IACpB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAeA,eAAsBC,qBAAoB,iBAIxC;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACiC,oBAAoB,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,iBAAiB,OAAO;AAAA,QACpD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB;AAAA,IACpB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAwBO,SAASC,wBAAoD;AAElE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,SAAO,aAKL;AAAA,IACA,MAAM,OAAO,YAAyC;AACpD,YAAM,UACiC,qBAAqB,OAAO;AAEnE,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,UAAgD;AACnE,YAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AACvB,aAAO,sCAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC;AAAA,MACpB;AAAA,IACF,MAAkD;AAChD,YAAM,kBAAkB;AAAA,QACtBhB,gBAAe,MAAM,CAAC,CAAC;AAAA,MACzB;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,mBAAmB,kBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;AAkOA,eAAsBiB,sBACpB,SACqE;AAErE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACiC,qBAAqB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;ADtoFO,SAASC,qBACd,YAC8B;AAC9B,SAAO,CAAC,kBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAyCO,SAASC,kBACd,YAC2B;AAC3B,SAAO,CAAC,oBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAuCO,SAASC,2BACd,YACoC;AACpC,SAAO,CAAC,qBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA8CO,SAASC,qBACd,YAC8B;AAC9B,SAAO,CACL,KACA,kBAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiDO,SAASC,yBACd,YACkC;AAClC,SAAO,CAAC,oBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAkEO,SAASC,qBACd,YAC8B;AAC9B,SAAO,CAAC,oBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAkBO,SAASC,sBACd,YAC+B;AAC/B,SAAO,MACLA;AAAA;AAAA,IAEE,EAAE,WAAW;AAAA,EACf;AACJ;AAwBO,SAASC,sBACd,YAC+B;AAC/B,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAgBO,IAAM,yBAAyB;AAAA,EACpC;AAAA,EACA;AAAA,EACA,CAAC,UACCC;AAAA,IACEC,gBAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAaC;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAAgC;AACzB,IAAM,yCAAyC;AAAA,EACpD;AAAA,EACA;AAAA,EACA,CAAC,UACCF;AAAA,IACEC,gBAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAaC;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,gDAAgD;AAAA,UACxD,EAAE,MAAM,gDAAgD;AAAA,UACxD,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAAgD;AACzC,IAAM,yBAAyB;AAAA,EACpC;AAAA,EACA;AAAA,EACA,CAAC,UACCF;AAAA,IACEC,gBAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAaC;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,wBAAwB;AAAA,UAChC,EAAE,MAAM,wBAAwB;AAAA,UAChC,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAAgC;AACzB,IAAM,yBAAyB;AAAA,EACpC;AAAA,EACA;AAAA,EACA,CAAC,UACCF;AAAA,IACEC,gBAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAaC;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAAgC;;;AGhdhC,SAAS,wBAAwB;AACjC,SAAS,yBAAyB;AAW3B,IAAMC,uBAGK,iCAAiBA,oBAAyB;AACrD,IAAMC,oBAGK,iCAAiBA,iBAAsB;AAClD,IAAMC,6BAGK,iCAAiBA,0BAA+B;AAC3D,IAAMC,uBAGK,iCAAiBA,oBAAyB;AACrD,IAAMC,2BAGK,iCAAiBA,wBAA6B;AACzD,IAAMC,uBAGK,iCAAiBA,oBAAyB;AACrD,IAAMC,wBAGK,iCAAiBA,qBAA0B;AACtD,IAAMC,wBAGK,iCAAiBA,qBAA0B;AAItD,IAAMC,0BAET,kBAAkB,sBAA4B;AAO3C,IAAMC,0CAET,kBAAkB,sCAA4C;AAI3D,IAAMC,0BAET,kBAAkB,sBAA4B;AAK3C,IAAMC,0BAET,kBAAkB,sBAA4B;", "names": ["renameKeysFromRESTResponseToSDKResponse", "transformRESTTimestampToSDKTimestamp", "transformPaths", "payload", "transformPaths", "SortOrder", "PlacementType", "DayOfWeek", "ResolutionMethod", "State", "SiteCreatedContext", "Namespace", "DeleteStatus", "WebhookIdentityType", "createBookingPolicy", "getBookingPolicy", "getStrictestBookingPolicy", "updateBookingPolicy", "setDefaultBookingPolicy", "deleteBookingPolicy", "queryBookingPolicies", "countBookingPolicies", "createBookingPolicy", "getBookingPolicy", "getStrictestBookingPolicy", "updateBookingPolicy", "setDefaultBookingPolicy", "deleteBookingPolicy", "queryBookingPolicies", "countBookingPolicies", "renameKeysFromRESTResponseToSDKResponse", "transformPaths", "transformRESTTimestampToSDKTimestamp", "createBookingPolicy", "getBookingPolicy", "getStrictestBookingPolicy", "updateBookingPolicy", "setDefaultBookingPolicy", "deleteBookingPolicy", "queryBookingPolicies", "countBookingPolicies", "onBookingPolicyCreated", "onBookingPolicyDefaultBookingPolicySet", "onBookingPolicyDeleted", "onBookingPolicyUpdated"]}