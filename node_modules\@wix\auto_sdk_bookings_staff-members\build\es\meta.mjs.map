{"version": 3, "sources": ["../../src/bookings-staff-v1-staff-member-staff-members.http.ts", "../../src/bookings-staff-v1-staff-member-staff-members.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsStaffV1StaffMembersServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/staff-members',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/staff-members',\n        destPath: '/v1/staff-members',\n      },\n      {\n        srcPath: '/bookings/v1/bulk/staff-members',\n        destPath: '/v1/bulk/staff-members',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_staff-members';\n\n/**\n * Creates a staff member.\n *\n *\n * By default, the staff member works during the business working hours. You\n * could follow *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))\n * to set custom working hours.\n */\nexport function createStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __createStaffMember({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'staffMember.createdDate' },\n          { path: 'staffMember.updatedDate' },\n          { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.CreateStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createStaffMember;\n}\n\n/** Retrieves a staff member. */\nexport function getStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __getStaffMember({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.GetStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMemberId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getStaffMember;\n}\n\n/**\n * Updates a staff member.\n *\n *\n * Each time the staff member is updated, `revision` increments by 1. You must\n * include current revision of the staff member when updating it. This ensures\n * you're working with the latest service information and prevents unintended\n * overwrites.\n */\nexport function updateStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __updateStaffMember({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'fieldMask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'staffMember.createdDate' },\n          { path: 'staffMember.updatedDate' },\n          { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.UpdateStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMember.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateStaffMember;\n}\n\n/**\n * Deletes a staff member.\n *\n *\n * Also deletes the *resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))\n * associated with the staff member.\n */\nexport function deleteStaffMember(payload: object): RequestOptionsFactory<any> {\n  function __deleteStaffMember({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.DeleteStaffMember',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMemberId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteStaffMember;\n}\n\n/**\n * Creates a query to retrieve a list of staff members.\n *\n * The `queryStaffMembers()` function builds a query to retrieve a list of staff members and returns a `StaffMembersQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-builder/find) function.\n *\n * You can refine the query by chaining `StaffMembersQueryBuilder` functions onto the query. `StaffMembersQueryBuilder` functions enable you to sort, filter, and control the results that `queryStaffMembers()` returns.\n *\n * `queryStaffMembers()` runs with the following `StaffMembersQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `staffMembersTypes()` are applied in the order they are called.\n *\n * The following `StaffMembersQueryBuilder` functions are supported for the `queryStaffMembers()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-result/items) property in `StaffMembersQueryResult`.\n */\nexport function queryStaffMembers(payload: object): RequestOptionsFactory<any> {\n  function __queryStaffMembers({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.QueryStaffMembers',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMembers.createdDate' },\n              { path: 'staffMembers.updatedDate' },\n              { path: 'staffMembers.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryStaffMembers;\n}\n\n/**\n * Counts how many staff members match the given filter.\n *\n *\n * ## Filter\n *\n * Refer to the _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).\n */\nexport function countStaffMembers(payload: object): RequestOptionsFactory<any> {\n  function __countStaffMembers({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.CountStaffMembers',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countStaffMembers;\n}\n\n/**\n * Connects a Wix user to a staff member, enabling them to manage their own\n * working hour schedule in the dashboard.\n *\n *\n * By default, Wix Bookings uses the staff member's `email`. However, you can\n * specify an alternative email address. If no existing Wix user is associated\n * with that email, Wix sends them an invitation to become a Wix user. If an\n * existing user is found but not linked to the site, Wix Bookings sends an\n * invitation to join the site.\n *\n * To check the connection status, call *Get Staff Member*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/get-staff-member) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/get-staff-member)),\n * and specify `ASSOCIATED_IDENTITY_STATUS` in the `fields` parameter.\n *\n * You must call *Disconnect Staff Member From User*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/disconnect-staff-member-from-user) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/disconnect-staff-member-from-user))\n * before connecting a different Wix user to the staff member.\n */\nexport function connectStaffMemberToUser(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __connectStaffMemberToUser({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.ConnectStaffMemberToUser',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath:\n          '/v1/staff-members/{staffMemberId}/connect-staff-member-to-user',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __connectStaffMemberToUser;\n}\n\n/**\n * Retrieves a list of up to 100 staff members, given the provided filtering, paging,\n * and sorting.\n *\n *\n * ## Defaults\n *\n * Search Staff Members has the following default settings, which you can override:\n * + Sorted by `createdDate` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filter\n *\n * Refer to the _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with *Search* methods, see\n * _API Query Language_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))\n * and _Sorting and Paging_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).\n */\nexport function searchStaffMembers(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __searchStaffMembers({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/search',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMembers.createdDate' },\n              { path: 'staffMembers.updatedDate' },\n              { path: 'staffMembers.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'aggregationData.results.ranges.results.from' },\n              { path: 'aggregationData.results.ranges.results.to' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from',\n              },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.from',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.to',\n              },\n              { path: 'aggregationData.results.scalar.value' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.scalar.value',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.scalar.value',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __searchStaffMembers;\n}\n\n/**\n * Disconnects a staff member from a Wix user, clearing the `associatedWixIdentity`\n * field. Consequently, the user can no longer manage the staff member's working\n * hour schedule in the dashboard.\n *\n *\n * You must call *Disconnect Staff Member From User* before connecting a\n * different Wix user to the staff member.\n */\nexport function disconnectStaffMemberFromUser(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __disconnectStaffMemberFromUser({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.DisconnectStaffMemberFromUser',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath:\n          '/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __disconnectStaffMemberFromUser;\n}\n\n/**\n * Updates a staff member's working hours based on the specified *schedule ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n *\n *\n * ## Default working hours\n *\n * By default, staff members work during the opening hours of the business's\n * _default location_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n *\n * ## Schedule ID\n *\n * You can specify either the ID of the business's working hour schedule or the\n * staff member's event schedule. The call fails if you specify a different\n * schedule ID.\n *\n * ### Staff event schedule\n *\n * To customize a staff member's working hours, specify their event schedule ID\n * as `scheduleId`. Refer to *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))\n * for more details.\n *\n * ### Business working hour schedule\n *\n * To reset a staff member's working hours to the default business hours,\n * specify the ID of the business working hour schedule as `scheduleId`.\n */\nexport function assignWorkingHoursSchedule(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __assignWorkingHoursSchedule({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath:\n          '/v1/staff-members/{staffMemberId}/assign-working-hours-schedule',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __assignWorkingHoursSchedule;\n}\n\n/**\n * Assigns a custom working hours schedule to the staff member\n *\n *\n * The working hours schedule is a schedule that defines the working hours of a staff member,\n * and dictate when the staff member is available for bookings.\n *\n * By default staff members use the shared business working hours schedule. By assigning a custom working hours schedule to a staff member,\n * you can define specific working hours for that staff member.\n *\n * To create and manage schedules and working hours sessions, use [Events API](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction).\n * @deprecated It has been replaced with wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule(), and will be removed on 2024-12-31.\n */\nexport function assignCustomSchedule(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __assignCustomSchedule({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.AssignCustomSchedule',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/staff-members/{staffMemberId}/assign-custom-schedule',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'staffMember.createdDate' },\n              { path: 'staffMember.updatedDate' },\n              { path: 'staffMember.mainMedia.image.urlExpirationDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __assignCustomSchedule;\n}\n\n/**\n * Synchronously updates *tags*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n * for up to 100 staff members.\n *\n *\n * If you specify a tag both in `assignTags` and `unassignTags`, the call\n * succeeds and the tag is assigned.\n */\nexport function bulkUpdateStaffMemberTags(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateStaffMemberTags({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTags',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/bulk/staff-members/update-tags',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateStaffMemberTags;\n}\n\n/**\n * Asynchronously updates *tags*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).\n * for staff members, given the provided filtering.\n *\n *\n * If you specify a tag both in `assignTags` and `unassignTags`, the call\n * succeeds and the tag is assigned.\n *\n * ## Filter\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n */\nexport function bulkUpdateStaffMemberTagsByFilter(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateStaffMemberTagsByFilter({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.staff.v1.staff_member',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTagsByFilter',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({\n        protoPath: '/v1/bulk/staff-members/update-tags-by-filter',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateStaffMemberTagsByFilter;\n}\n", "import * as ambassadorWixBookingsStaffV1StaffMember from './bookings-staff-v1-staff-member-staff-members.http.js';\nimport * as ambassadorWixBookingsStaffV1StaffMemberTypes from './bookings-staff-v1-staff-member-staff-members.types.js';\nimport * as ambassadorWixBookingsStaffV1StaffMemberUniversalTypes from './bookings-staff-v1-staff-member-staff-members.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function createStaffMember(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.CreateStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.CreateStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.CreateStaffMemberResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.CreateStaffMemberResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.createStaffMember(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getStaffMember(): __PublicMethodMetaInfo<\n  'GET',\n  { staffMemberId: string },\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.GetStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.GetStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.GetStaffMemberResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.GetStaffMemberResponse\n> {\n  const payload = { staffMemberId: ':staffMemberId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.getStaffMember(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v1/staff-members/{staffMemberId}',\n    pathParams: { staffMemberId: 'staffMemberId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateStaffMember(): __PublicMethodMetaInfo<\n  'PATCH',\n  { staffMemberId: string },\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.UpdateStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.UpdateStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.UpdateStaffMemberResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.UpdateStaffMemberResponse\n> {\n  const payload = { staffMember: { id: ':staffMemberId' } } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.updateStaffMember(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PATCH',\n    path: '/v1/staff-members/{staffMember.id}',\n    pathParams: { staffMemberId: 'staffMemberId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteStaffMember(): __PublicMethodMetaInfo<\n  'DELETE',\n  { staffMemberId: string },\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.DeleteStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.DeleteStaffMemberRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.DeleteStaffMemberResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.DeleteStaffMemberResponse\n> {\n  const payload = { staffMemberId: ':staffMemberId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.deleteStaffMember(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'DELETE',\n    path: '/v1/staff-members/{staffMemberId}',\n    pathParams: { staffMemberId: 'staffMemberId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryStaffMembers(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.QueryStaffMembersRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.QueryStaffMembersRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.QueryStaffMembersResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.QueryStaffMembersResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.queryStaffMembers(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function countStaffMembers(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.CountStaffMembersRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.CountStaffMembersRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.CountStaffMembersResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.CountStaffMembersResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.countStaffMembers(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members/count',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function connectStaffMemberToUser(): __PublicMethodMetaInfo<\n  'POST',\n  { staffMemberId: string },\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.ConnectStaffMemberToUserRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.ConnectStaffMemberToUserRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.ConnectStaffMemberToUserResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.ConnectStaffMemberToUserResponse\n> {\n  const payload = { staffMemberId: ':staffMemberId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.connectStaffMemberToUser(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members/{staffMemberId}/connect-staff-member-to-user',\n    pathParams: { staffMemberId: 'staffMemberId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function searchStaffMembers(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.SearchStaffMembersRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.SearchStaffMembersRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.SearchStaffMembersResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.SearchStaffMembersResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.searchStaffMembers(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members/search',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function disconnectStaffMemberFromUser(): __PublicMethodMetaInfo<\n  'POST',\n  { staffMemberId: string },\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.DisconnectStaffMemberFromUserRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.DisconnectStaffMemberFromUserRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.DisconnectStaffMemberFromUserResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.DisconnectStaffMemberFromUserResponse\n> {\n  const payload = { staffMemberId: ':staffMemberId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.disconnectStaffMemberFromUser(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user',\n    pathParams: { staffMemberId: 'staffMemberId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function assignWorkingHoursSchedule(): __PublicMethodMetaInfo<\n  'POST',\n  { staffMemberId: string },\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.AssignWorkingHoursScheduleRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.AssignWorkingHoursScheduleRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.AssignWorkingHoursScheduleResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.AssignWorkingHoursScheduleResponse\n> {\n  const payload = { staffMemberId: ':staffMemberId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.assignWorkingHoursSchedule(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members/{staffMemberId}/assign-working-hours-schedule',\n    pathParams: { staffMemberId: 'staffMemberId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function assignCustomSchedule(): __PublicMethodMetaInfo<\n  'POST',\n  { staffMemberId: string },\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.AssignCustomScheduleRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.AssignCustomScheduleRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.AssignCustomScheduleResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.AssignCustomScheduleResponse\n> {\n  const payload = { staffMemberId: ':staffMemberId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.assignCustomSchedule(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/staff-members/{staffMemberId}/assign-custom-schedule',\n    pathParams: { staffMemberId: 'staffMemberId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkUpdateStaffMemberTags(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.BulkUpdateStaffMemberTagsRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.BulkUpdateStaffMemberTagsRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.BulkUpdateStaffMemberTagsResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.BulkUpdateStaffMemberTagsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.bulkUpdateStaffMemberTags(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/bulk/staff-members/update-tags',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkUpdateStaffMemberTagsByFilter(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.BulkUpdateStaffMemberTagsByFilterRequest,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.BulkUpdateStaffMemberTagsByFilterRequest,\n  ambassadorWixBookingsStaffV1StaffMemberUniversalTypes.BulkUpdateStaffMemberTagsByFilterResponse,\n  ambassadorWixBookingsStaffV1StaffMemberTypes.BulkUpdateStaffMemberTagsByFilterResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsStaffV1StaffMember.bulkUpdateStaffMemberTagsByFilter(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/bulk/staff-members/update-tags-by-filter',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,gDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAWd,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,gDAAgD;AAAA,QAC1D;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,YAAY,CAAC;AAAA,MAC/B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,0BAA0B;AAAA,UAClC,EAAE,MAAM,gDAAgD;AAAA,QAC1D;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,iDAAiD;AAAA,UAC3D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiBO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAqBO,SAAS,yBACd,SAC4B;AAC5B,WAAS,2BAA2B,EAAE,KAAK,GAAQ;AACjD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA2BO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,iDAAiD;AAAA,UAC3D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,8BACd,SAC4B;AAC5B,WAAS,gCAAgC,EAAE,KAAK,GAAQ;AACtD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA+BO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAeO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gDAAgD;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiBO,SAAS,kCACd,SAC4B;AAC5B,WAAS,oCAAoC,EAAE,KAAK,GAAQ;AAC1D,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACjqBO,SAASC,qBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoC,kBAAkB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,EAAE,eAAe,iBAAiB;AAElD,QAAM,oBACoC,eAAe,OAAO;AAEhE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,eAAe,gBAAgB;AAAA,IAC7C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qBAOd;AACA,QAAM,UAAU,EAAE,aAAa,EAAE,IAAI,iBAAiB,EAAE;AAExD,QAAM,oBACoC,kBAAkB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,eAAe,gBAAgB;AAAA,IAC7C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qBAOd;AACA,QAAM,UAAU,EAAE,eAAe,iBAAiB;AAElD,QAAM,oBACoC,kBAAkB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,eAAe,gBAAgB;AAAA,IAC7C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoC,kBAAkB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoC,kBAAkB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,4BAOd;AACA,QAAM,UAAU,EAAE,eAAe,iBAAiB;AAElD,QAAM,oBACoC,yBAAyB,OAAO;AAE1E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,eAAe,gBAAgB;AAAA,IAC7C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoC,mBAAmB,OAAO;AAEpE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iCAOd;AACA,QAAM,UAAU,EAAE,eAAe,iBAAiB;AAElD,QAAM,oBACoC;AAAA,IACtC;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,eAAe,gBAAgB;AAAA,IAC7C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,8BAOd;AACA,QAAM,UAAU,EAAE,eAAe,iBAAiB;AAElD,QAAM,oBACoC,2BAA2B,OAAO;AAE5E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,eAAe,gBAAgB;AAAA,IAC7C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,wBAOd;AACA,QAAM,UAAU,EAAE,eAAe,iBAAiB;AAElD,QAAM,oBACoC,qBAAqB,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,eAAe,gBAAgB;AAAA,IAC7C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,6BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoC,0BAA0B,OAAO;AAE3E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qCAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoC;AAAA,IACtC;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "createStaffMember", "getStaffMember", "updateStaffMember", "deleteStaffMember", "queryStaffMembers", "countStaffMembers", "connectStaffMemberToUser", "searchStaffMembers", "disconnectStaffMemberFromUser", "assignWorkingHoursSchedule", "assignCustomSchedule", "bulkUpdateStaffMemberTags", "bulkUpdateStaffMemberTagsByFilter"]}