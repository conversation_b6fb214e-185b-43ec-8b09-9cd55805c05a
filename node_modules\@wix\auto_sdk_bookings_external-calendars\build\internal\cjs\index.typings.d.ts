import { NonNullablePaths } from '@wix/sdk-types';

/**
 * You can use the *External Calendar API*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/introduction))
 * to connect external calendars to a Wix site. Supported external calendars
 * include Google, Microsoft, and Apple calendars. The `externalCalendar` object
 * doesn't have any fields.
 */
interface ExternalCalendar {
}
interface ListProvidersRequest {
}
interface ListProvidersResponse {
    /** List of external calendar providers supported on the site. */
    providers?: Provider[];
}
interface Provider {
    /**
     * ID of the external calendar provider.
     * @format GUID
     */
    _id?: string | null;
    /** Name of the external calendar provider. */
    name?: string | null;
    /** External calendar provider type. */
    calendarType?: CalendarTypeWithLiterals;
    /**
     * Information about how you can connect the external calendar to the Wix site,
     * and which functionality it supports.
     */
    features?: ProviderFeatures;
}
declare enum CalendarType {
    /** There is no information about the external calendar type. */
    UNDEFINED = "UNDEFINED",
    /** [Google Calendar](https://developers.google.com/calendar/api/guides/overview). */
    GOOGLE = "GOOGLE",
    /** Apple iCalendar. */
    I_CAL = "I_CAL",
    /** __Deprecated__. Use `MICROSOFT` instead. */
    OUTLOOK = "OUTLOOK",
    /** __Deprecated__. Use `MICROSOFT` instead. */
    OFFICE_365 = "OFFICE_365",
    /** Microsoft Calendar. For example, Office 365 calendar or Outlook calendar. */
    MICROSOFT = "MICROSOFT",
    /** A different type of external calendar, not listed here. */
    OTHER = "OTHER"
}
/** @enumType */
type CalendarTypeWithLiterals = CalendarType | 'UNDEFINED' | 'GOOGLE' | 'I_CAL' | 'OUTLOOK' | 'OFFICE_365' | 'MICROSOFT' | 'OTHER';
interface ProviderFeatures {
    /** List of supported connection methods. */
    connectMethods?: ConnectMethodWithLiterals[];
    /**
     * Whether you can update `syncConfic` for all external calendar connections
     * by calling _Update Sync Config_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config)).
     */
    updateSyncConfig?: boolean | null;
    /**
     * Information about which events you can import from the external calendar to
     * the Wix site.
     */
    listEventFromCalendars?: ListEventFromCalendarsWithLiterals;
    /**
     * Whether you can export Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * to the external calendar.
     */
    syncToCalendar?: SyncToCalendarWithLiterals;
}
declare enum ConnectMethod {
    /**
     * There is no information about how you can connect the external calendar
     * to the Wix site.
     */
    UNDEFINED = "UNDEFINED",
    /**
     * You can connect the external calendar to the Wix site by calling *Connect By O Auth*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth)).
     */
    OAUTH = "OAUTH",
    /**
     * You can connect the external calendar to the Wix site by calling *Connect By Credentials*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials)).
     */
    CREDENTIALS = "CREDENTIALS"
}
/** @enumType */
type ConnectMethodWithLiterals = ConnectMethod | 'UNDEFINED' | 'OAUTH' | 'CREDENTIALS';
declare enum ListEventFromCalendars {
    /**
     * There is no information about which external calendar events you
     * can import to the Wix site.
     */
    UNDEFINED = "UNDEFINED",
    /** You can't import any events from the external calendar to the Wix site. */
    NOT_SUPPORTED = "NOT_SUPPORTED",
    /**
     * You can import events only for the external calendar that's
     * designated as primary.
     */
    PRIMARY_CALENDAR_ONLY = "PRIMARY_CALENDAR_ONLY",
    /**
     * You can import events only for a specific external calendar account.
     * You can call _List Calendars_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-calendars) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-calendars))
     * to retrieve a complete list of external calendar accounts.
     */
    SPECIFIC_CALENDARS = "SPECIFIC_CALENDARS"
}
/** @enumType */
type ListEventFromCalendarsWithLiterals = ListEventFromCalendars | 'UNDEFINED' | 'NOT_SUPPORTED' | 'PRIMARY_CALENDAR_ONLY' | 'SPECIFIC_CALENDARS';
declare enum SyncToCalendar {
    /**
     * There is no information about which Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * you can export to the external calendar.
     */
    UNDEFINED = "UNDEFINED",
    /**
     * You can't export any Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * to the external calendar.
     */
    NOT_SUPPORTED = "NOT_SUPPORTED",
    /**
     * You can export Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * only to the external calendar that's designated as primary.
     */
    PRIMARY_CALENDAR_ONLY = "PRIMARY_CALENDAR_ONLY",
    /** Events can be exported to specific external calendars. Use [List Calendars](#list-calendars) to get a list of calendars for a connected external calendar account. */
    SPECIFIC_CALENDAR = "SPECIFIC_CALENDAR",
    /**
     * You can export Wix calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * only to a dedicated external calendar account. You can
     * call _List Calendars_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-calendars) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-calendars))
     * to retrieve a complete list of external calendar accounts.
     */
    DEDICATED_CALENDAR = "DEDICATED_CALENDAR"
}
/** @enumType */
type SyncToCalendarWithLiterals = SyncToCalendar | 'UNDEFINED' | 'NOT_SUPPORTED' | 'PRIMARY_CALENDAR_ONLY' | 'SPECIFIC_CALENDAR' | 'DEDICATED_CALENDAR';
interface GetConnectionRequest {
    /** ID of the connection to retrieve. */
    connectionId: string | null;
}
interface GetConnectionResponse {
    /** Retrieved external calendar connection. */
    connection?: Connection;
}
interface Connection {
    /** ID of the connection between the external calendar and the Wix site. */
    _id?: string | null;
    /**
     * ID of the external calendar provider.
     * @format GUID
     */
    providerId?: string | null;
    /** External calendar type. */
    calendarType?: CalendarTypeWithLiterals;
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * that's connected to the external calendar.
     * @format GUID
     */
    scheduleId?: string | null;
    /**
     * ID of the *Wix user*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))
     * to whom the external calendar connection belongs.
     * @format GUID
     */
    userId?: string | null;
    /**
     * ID of the app which created the external calendar connection.
     * @format GUID
     */
    appId?: string | null;
    /**
     * Email address associated with the external calendar account. Available only
     * after completed successfully.
     * @format EMAIL
     */
    externalAccountEmail?: string | null;
    /** Connection status. */
    status?: StatusWithLiterals;
    /** Reason for the error. Available only if `status` is `ERROR`. */
    errorReason?: ErrorReasonWithLiterals;
    /** Settings about which sync operations are supported. */
    syncConfig?: ConnectionSyncConfig;
}
declare enum Status {
    /** There is no information about the connection status. */
    UNDEFINED = "UNDEFINED",
    /**
     * The external calendar is connected to the Wix *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),
     * but the sync process hasn't started yet.
     */
    CONNECTED = "CONNECTED",
    /** Sync process is ongoing. */
    SYNC_IN_PROGRESS = "SYNC_IN_PROGRESS",
    /** The Wix calendar and the external calendar are in sync. */
    SYNCED = "SYNCED",
    /**
     * The external calender has been disconnected from the Wix *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     */
    DISCONNECTED = "DISCONNECTED",
    /** The calendars sync is in error state. */
    ERROR = "ERROR"
}
/** @enumType */
type StatusWithLiterals = Status | 'UNDEFINED' | 'CONNECTED' | 'SYNC_IN_PROGRESS' | 'SYNCED' | 'DISCONNECTED' | 'ERROR';
declare enum ErrorReason {
    /** There is no information about the connection error. */
    UNDEFINED = "UNDEFINED",
    /** The external calendar's access token has been revoked. */
    TOKEN_REVOKED = "TOKEN_REVOKED",
    /** The external calendar couldn't be created. */
    EXTERNAL_CALENDAR_CREATION_FAILED = "EXTERNAL_CALENDAR_CREATION_FAILED",
    /** The external calendar was deleted. */
    EXTERNAL_CALENDAR_DELETED = "EXTERNAL_CALENDAR_DELETED"
}
/** @enumType */
type ErrorReasonWithLiterals = ErrorReason | 'UNDEFINED' | 'TOKEN_REVOKED' | 'EXTERNAL_CALENDAR_CREATION_FAILED' | 'EXTERNAL_CALENDAR_DELETED';
interface ConnectionSyncConfig {
    /**
     * Configuration for importing events from the external calendar to the Wix
     * site.
     */
    listEventFromCalendars?: ConnectionSyncConfigListEventFromCalendars;
    /**
     * Configuration for exporting events from the Wix site to the external
     * calendar.
     */
    syncToCalendar?: ConnectionSyncConfigSyncToCalendar;
}
interface Calendar {
    /**
     * ID of the external calendar account.
     * @readonly
     */
    _id?: string | null;
    /**
     * Display name of the external calendar account.
     * For example, `Primary` or `Birthdays`.
     */
    name?: string | null;
}
interface PrimaryCalendar {
}
interface Calendars {
    calendars?: Calendar[];
}
interface DedicatedCalendar {
}
interface ConnectionSyncConfigListEventFromCalendars extends ConnectionSyncConfigListEventFromCalendarsListFromOneOf {
    /**
     * An empty `primaryCalendar` object indicates that you can import
     * events only from the primary external calendar. Keep in mind that
     * not all external calendar providers support primary calendars.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * You can import events from the list of specified external calendar
     * accounts. The list may include the primary calendar.
     */
    calendars?: Calendars;
    /**
     * Whether you can call *List Events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
     * to import events from the external calendar to the Wix site.
     */
    enabled?: boolean | null;
}
/** @oneof */
interface ConnectionSyncConfigListEventFromCalendarsListFromOneOf {
    /**
     * An empty `primaryCalendar` object indicates that you can import
     * events only from the primary external calendar. Keep in mind that
     * not all external calendar providers support primary calendars.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * You can import events from the list of specified external calendar
     * accounts. The list may include the primary calendar.
     */
    calendars?: Calendars;
}
interface ConnectionSyncConfigSyncToCalendar extends ConnectionSyncConfigSyncToCalendarSyncToOneOf {
    /**
     * An empty `primaryCalendar` object indicates that Wix events are
     * exported only to the primary account of the external calendar.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * An empty `dedicatedCalendar` object indicates that Wix events are
     * exported only to the dedicated account of the external calendar.
     */
    dedicatedCalendar?: DedicatedCalendar;
    /**
     * Whether Wix *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * are exported to the external calendar.
     */
    enabled?: boolean | null;
}
/** @oneof */
interface ConnectionSyncConfigSyncToCalendarSyncToOneOf {
    /**
     * An empty `primaryCalendar` object indicates that Wix events are
     * exported only to the primary account of the external calendar.
     */
    primaryCalendar?: PrimaryCalendar;
    /**
     * An empty `dedicatedCalendar` object indicates that Wix events are
     * exported only to the dedicated account of the external calendar.
     */
    dedicatedCalendar?: DedicatedCalendar;
}
declare enum SyncToErrorReason {
    /** No sync error. */
    UNDEFINED = "UNDEFINED",
    /** Could not create calendar to sync sessions to. */
    CALENDAR_CREATION_FAILURE = "CALENDAR_CREATION_FAILURE",
    /** Calendar was deleted while sync was in progress. */
    CALENDAR_DELETED = "CALENDAR_DELETED"
}
/** @enumType */
type SyncToErrorReasonWithLiterals = SyncToErrorReason | 'UNDEFINED' | 'CALENDAR_CREATION_FAILURE' | 'CALENDAR_DELETED';
interface ListConnectionsRequest {
    /**
     * _Schedule IDs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to filter by.
     *
     * Default: Returns all connections.
     * @format GUID
     */
    scheduleIds?: string[] | null;
    /**
     * Whether to return a partial list of connections if details can't be
     * retrieved for all connections.
     *
     * Default: `false`
     */
    partialFailure?: boolean | null;
}
interface ListConnectionsResponse {
    /** Retrieved external calendar connections. */
    connections?: Connection[];
    /**
     * List of provider IDs for which connection retrieval failed. Returned only
     * if you specify `{"partialFailure": true}`.
     */
    failedProviderIds?: string[] | null;
}
interface ConnectByOAuthRequest {
    /**
     * ID of the schedule to connect with the external calendar account.
     * @format GUID
     */
    scheduleId: string | null;
    /**
     * ID of the external calendar provider. Find this with the [List Providers](#list-providers) endpoint.
     * @format GUID
     */
    providerId: string | null;
    /**
     * URL to redirect the user to after they authorize access to the external calendar account.
     *
     * If the connection is successfully established, the user is redirected to this URL with a query parameter `connectionId` containing the new connection ID.
     * If the attempt to connect fails, the user is redirected to this URL with a query parameter `error` containing the error type.
     * @format WEB_URL
     */
    redirectUrl: string | null;
}
interface ConnectByOAuthResponse {
    /**
     * URL of the external calendar authorization page to redirect the user to.
     * @format WEB_URL
     */
    oauthUrl?: string | null;
}
interface RawHttpRequest {
    body?: Uint8Array;
    pathParams?: PathParametersEntry[];
    queryParams?: QueryParametersEntry[];
    headers?: HeadersEntry[];
    method?: string;
    rawPath?: string;
    rawQuery?: string;
}
interface PathParametersEntry {
    key?: string;
    value?: string;
}
interface QueryParametersEntry {
    key?: string;
    value?: string;
}
interface HeadersEntry {
    key?: string;
    value?: string;
}
interface RawHttpResponse {
    body?: Uint8Array;
    statusCode?: number | null;
    headers?: HeadersEntry[];
}
interface ConnectByCredentialsRequest {
    /**
     * ID of the schedule to connect with the external calendar account.
     * @format GUID
     */
    scheduleId: string | null;
    /**
     * ID of the external calendar provider. Find this with the [List Providers](#list-providers) endpoint.
     * @format GUID
     */
    providerId: string | null;
    /**
     * Email address for the external calendar account.
     * @format EMAIL
     */
    email: string | null;
    /** Password for the external calendar account. */
    password: string | null;
}
interface ConnectByCredentialsResponse {
    /** Established connection details. */
    connection?: Connection;
}
interface ListCalendarsRequest {
    /** ID of the external calendar connection to list calendars for. */
    connectionId: string | null;
}
interface ListCalendarsResponse {
    /** List of calendars belonging to the external calendar account. */
    calendars?: Calendar[];
}
interface UpdateSyncConfigRequest {
    /** ID of the external calendar connection to update. */
    connectionId: string | null;
    /** Updated sync configuration details. */
    syncConfig: ConnectionSyncConfig;
}
interface UpdateSyncConfigResponse {
    /** Connection with updated sync configuration. */
    connection?: Connection;
}
interface DisconnectRequest {
    /** ID of the external calendar connection to disconnect. */
    connectionId: string | null;
}
interface DisconnectResponse {
    /** Updated connection details. */
    connection?: Connection;
}
interface ListEventsRequest {
    /**
     * Date and time from which to retrieve events,
     * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).
     * Required, unless `cursorPaging.cursor` is provided.
     *
     * Events which start before the `from` time and end after it are included in the returned list.
     */
    from?: string | null;
    /**
     * Date and time until which to retrieve events,
     * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).
     * Required, unless `cursorPaging.cursor` is provided.
     *
     * Events which start before the `to` time and end after it are included in the returned list.
     */
    to?: string | null;
    /**
     * Schedule IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to the specified schedules.
     * Maximum of 100 schedule IDs per request.
     * @format GUID
     * @maxSize 100
     */
    scheduleIds?: string[] | null;
    /**
     * Wix user IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to schedules belonging to the specified Wix users.
     * Maximum of 100 Wix user IDs per request.
     * @format GUID
     * @maxSize 100
     */
    userIds?: string[] | null;
    /**
     * Whether to include only all-day events in the returned list.
     * If `true`, only all-day events are returned.
     * If `false`, only events with a specified time are returned.
     *
     * Default: All events are returned.
     */
    allDay?: boolean | null;
    /**
     * Predefined sets of fields to return.
     * - `NO_PI`: Returns event objects without personal information.
     * - `OWN_PI`: Returns complete event objects, including personal information.
     *
     * Default: `NO_PI`
     */
    fieldsets?: string[];
    /** Pagination options. */
    cursorPaging?: CursorPaging;
    /**
     * Whether to return a partial list of events if details can't be retrieved for some connections.
     *
     * Default: `false`
     */
    partialFailure?: boolean | null;
}
interface CursorPaging {
    /**
     * Number of events to load.
     * Max: `1000`
     * @max 1000
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     */
    cursor?: string | null;
}
interface ListEventsResponse {
    /** List of external calendar events matching the filters. */
    events?: Event[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
    /** List of provider IDs for connections for which retrieval of events failed. Returned only if `partialFailure` body parameter is `true` in the request. */
    failedProviderIds?: string[] | null;
}
/** An external calendar event. */
interface Event {
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to which the event belongs.
     * @format GUID
     */
    scheduleId?: string | null;
    /** External calendar type. */
    calendarType?: CalendarTypeWithLiterals;
    /**
     * Display name of the external calendar.
     * For example, `Primary` or `Birthdays`.
     */
    calendarName?: string | null;
    /** Event title. */
    title?: string | null;
    /** Start date and time of the event (inclusive), formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt). */
    start?: string | null;
    /** End date and time of the event (exclusive), formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt). */
    end?: string | null;
    /**
     * Whether the event is an all-day event.
     *
     * Default: `false`
     */
    allDay?: boolean | null;
    /**
     * ID of the *Wix user*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))
     * to whom the schedule belongs.
     * For Bookings *staff members*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * identical to their *resource ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).
     */
    scheduleOwnerId?: string | null;
    /**
     * Name of the *Wix user*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))
     * to whom the schedule belongs.
     * For example, the `name` of a Bookings *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)).
     */
    scheduleOwnerName?: string | null;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /** Cursor pointing to next page in the list of results. */
    next?: string | null;
}
interface ScheduleNotification extends ScheduleNotificationEventOneOf {
    scheduleCreated?: ScheduleCreated;
    scheduleUpdated?: ScheduleUpdated;
    scheduleCancelled?: ScheduleCancelled;
    sessionCreated?: SessionCreated;
    sessionUpdated?: SessionUpdated;
    sessionCancelled?: SessionCancelled;
    availabilityPolicyUpdated?: AvailabilityPolicyUpdated;
    /** @deprecated */
    intervalSplit?: IntervalSplit;
    recurringSessionSplit?: RecurringSessionSplit;
    /**
     * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.
     * @deprecated
     */
    scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;
    preserveFutureSessionsWithParticipants?: boolean | null;
    /**
     * Whether to notify participants about changed sessions. deprecated, use participant_notification
     * @deprecated
     */
    notifyParticipants?: boolean;
    /** site properties. Optional. Given in create schedule notification. */
    siteProperties?: SitePropertiesOnScheduleCreation;
    instanceId?: string;
}
/** @oneof */
interface ScheduleNotificationEventOneOf {
    scheduleCreated?: ScheduleCreated;
    scheduleUpdated?: ScheduleUpdated;
    scheduleCancelled?: ScheduleCancelled;
    sessionCreated?: SessionCreated;
    sessionUpdated?: SessionUpdated;
    sessionCancelled?: SessionCancelled;
    availabilityPolicyUpdated?: AvailabilityPolicyUpdated;
    /** @deprecated */
    intervalSplit?: IntervalSplit;
    recurringSessionSplit?: RecurringSessionSplit;
    /**
     * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.
     * @deprecated
     */
    scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;
}
interface ScheduleCreated {
    schedule?: Schedule;
}
interface Schedule {
    /** Schedule ID. */
    _id?: string;
    /** ID of the schedule's owner entity. This may be a resource ID or a service ID. */
    scheduleOwnerId?: string | null;
    /**
     * Schedule's time zone in [Area/Location](https://en.wikipedia.org/wiki/Tz_database) format. Read-only.
     * Derived from the Wix Business time zone.
     * @readonly
     */
    timeZone?: string | null;
    /**
     * Deprecated. Please use the [Sessions API](https://dev.wix.com/api/rest/wix-bookings/schedules-and-sessions/session) instead.
     * @deprecated
     */
    intervals?: RecurringInterval[];
    /**
     * Default title for the schedule's sessions. Maximum length: 6000 characters.
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * __Deprecated.__
     * Tags for grouping schedules. These tags are the default tags for the schedule's sessions.
     * The Wix Bookings app uses the following predefined tags to set schedule type: `"INDIVIDUAL"`, `"GROUP"`, and `"COURSE"`. Once the schedule type is set using these tags, you cannot update it. In addition to the app's tags, you can create and update your own tags.
     * @deprecated
     */
    tags?: string[] | null;
    /** Default location for the schedule's sessions. */
    location?: Location;
    /**
     * Maximum number of participants that can be added to the schedule's sessions.
     * Must be at most `1` for schedule whose availability is affected by another schedule. E.g, appointment schedules of the Wix Bookings app.
     * @min 1
     * @max 1000
     */
    capacity?: number | null;
    /**
     * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.
     * @deprecated
     */
    rate?: Rate;
    /**
     * __Deprecated.__
     * @deprecated
     */
    availability?: Availability;
    /**
     * Number of participants registered to sessions in this schedule, calculated as the sum of the party sizes.
     * @readonly
     */
    totalNumberOfParticipants?: number;
    /**
     * *Partial list** of participants which are registered to sessions in this schedule.
     * Participants who are registered in the schedule are automatically registered to any session that is created for the schedule.
     * To retrieve the full list of schedule participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).
     * @readonly
     */
    participants?: Participant[];
    /**
     * __Deprecated.__
     * @deprecated
     */
    externalCalendarOverrides?: ExternalCalendarOverrides;
    /**
     * Schedule status. Default: Created
     * @readonly
     */
    status?: ScheduleStatusWithLiterals;
    /**
     * Schedule creation date.
     * @readonly
     */
    created?: Date | null;
    /**
     * Schedule last update date.
     * @readonly
     */
    updated?: Date | null;
    /**
     * Schedule version number, updated each time the schedule is updated.
     * @readonly
     */
    version?: number;
    /**
     * Fields which were inherited from the Business Info page under Settings in the Dashboard.
     * @readonly
     */
    inheritedFields?: string[];
    /**
     * __Deprecated.__
     * @deprecated
     */
    conferenceProvider?: ConferenceProvider;
    /**
     * A conference created for the schedule. This is used when a participant is added to a schedule.
     * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.
     * @deprecated
     */
    calendarConference?: CalendarConference;
}
interface RecurringInterval {
    /**
     * The recurring interval identifier.
     * @readonly
     */
    _id?: string;
    /** The start time of the recurring interval. Required. */
    start?: Date | null;
    /** The end time of the recurring interval. Optional. Empty value indicates that there is no end time. */
    end?: Date | null;
    /** The interval rules. The day, hour and minutes the interval is recurring. */
    interval?: Interval;
    /** The frequency of the interval. Optional. The default is frequency with the default repetition. */
    frequency?: Frequency;
    /** Specifies the list of linked schedules and the way this link affects the corresponding schedules' availability. Can be calculated from the schedule or overridden on the recurring interval. */
    affectedSchedules?: LinkedSchedule[];
    /** The type of recurring interval. */
    intervalType?: RecurringIntervalTypeWithLiterals;
}
interface Interval {
    /** The day the interval occurs. Optional. The default is the day of the recurring interval's start time. */
    daysOfWeek?: DayWithLiterals;
    /**
     * The hour of the day the interval occurs. Must be consistent with the interval start time. Optional. The default is 0. Minimum: 0, maximum: 23.
     * @max 23
     */
    hourOfDay?: number | null;
    /**
     * The minutes of the hour the interval accrues. Must be consistent with the interval end time. Optional. The default is 0. Minimum: 0, maximum: 59.
     * @max 59
     */
    minuteOfHour?: number | null;
    /** The duration of the interval in minutes. Required. Part of the session end time calculation. */
    duration?: number;
}
declare enum Day {
    /** Undefined. */
    UNDEFINED = "UNDEFINED",
    /** Monday. */
    MON = "MON",
    /** Tuesday. */
    TUE = "TUE",
    /** Wednesday. */
    WED = "WED",
    /** Thursday. */
    THU = "THU",
    /** Friday. */
    FRI = "FRI",
    /** Saturday. */
    SAT = "SAT",
    /** Sunday. */
    SUN = "SUN"
}
/** @enumType */
type DayWithLiterals = Day | 'UNDEFINED' | 'MON' | 'TUE' | 'WED' | 'THU' | 'FRI' | 'SAT' | 'SUN';
interface Frequency {
    /**
     * The frequency of the recurrence in weeks. i.e. when this value is 4, the interval occurs every 4 weeks. Optional. The default is 1. minimum: 1, maximum: 52.
     * @min 1
     * @max 52
     */
    repetition?: number | null;
}
interface LinkedSchedule {
    /**
     * Schedule ID.
     * @format GUID
     */
    scheduleId?: string;
    /** Sets this schedule's availability for the duration of the linked schedule's sessions. Default is `"BUSY"`. */
    transparency?: TransparencyWithLiterals;
    /**
     * Owner ID, of the linked schedule.
     * @readonly
     */
    scheduleOwnerId?: string;
}
declare enum Transparency {
    UNDEFINED = "UNDEFINED",
    /** The schedule can have available slots during the linked schedule's sessions. */
    FREE = "FREE",
    /** The schedule can't have available slots during the linked schedule's sessions. */
    BUSY = "BUSY"
}
/** @enumType */
type TransparencyWithLiterals = Transparency | 'UNDEFINED' | 'FREE' | 'BUSY';
declare enum RecurringIntervalType {
    /** The default value. Sessions for this interval will be of type EVENT. */
    UNDEFINED = "UNDEFINED",
    /** A recurring interval of events. */
    EVENT = "EVENT",
    /** Deprecated. */
    TIME_AVAILABILITY = "TIME_AVAILABILITY",
    /** A recurring interval for availability. */
    AVAILABILITY = "AVAILABILITY"
}
/** @enumType */
type RecurringIntervalTypeWithLiterals = RecurringIntervalType | 'UNDEFINED' | 'EVENT' | 'TIME_AVAILABILITY' | 'AVAILABILITY';
interface Location {
    /**
     * Location type.
     * One of:
     * - `"OWNER_BUSINESS"` The business address as set in the site’s general settings.
     * - `"OWNER_CUSTOM"` The address as set when creating the service.
     * - `"CUSTOM"` The address set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
    /**
     * Free text address used when locationType is `OWNER_CUSTOM`.
     * @deprecated
     */
    address?: string | null;
    /** Custom address, used when locationType is `"OWNER_CUSTOM"`. Might be used when locationType is `"CUSTOM"` in case the owner sets a custom address for the session which is different from the default. */
    customAddress?: Address;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
/** Physical address */
interface Address extends AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
    /**
     * Country code.
     * @format COUNTRY
     */
    country?: string | null;
    /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /** Zip/postal code. */
    postalCode?: string | null;
    /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */
    addressLine2?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Free text to help find the address. */
    hint?: string | null;
    /** Coordinates of the physical address. */
    geocode?: AddressLocation;
    /** Country full name. */
    countryFullname?: string | null;
    /** Multi-level subdivisions from top to bottom. */
    subdivisions?: Subdivision[];
}
/** @oneof */
interface AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
}
interface StreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
interface AddressLocation {
    /** Address latitude. */
    latitude?: number | null;
    /** Address longitude. */
    longitude?: number | null;
}
interface Subdivision {
    /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    code?: string;
    /** Subdivision full name. */
    name?: string;
}
interface LocationsLocation {
    /**
     * Location ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Location name.
     * @maxLength 150
     */
    name?: string;
    /**
     * Location description.
     * @maxLength 500
     */
    description?: string | null;
    /**
     * Whether this is the default location. There can only be one default location per site. The default location can't be archived.
     * @readonly
     */
    default?: boolean;
    /**
     * Location status. Defaults to `ACTIVE`.
     * __Notes:__
     * - [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)
     * doesn't affect the location's status.
     * - `INACTIVE` status is currently not supported.
     */
    status?: LocationStatusWithLiterals;
    /**
     * Location type.
     *
     * **Note:** Currently not supported.
     * @deprecated
     */
    locationType?: LocationsLocationTypeWithLiterals;
    /** Fax number. */
    fax?: string | null;
    /** Timezone in `America/New_York` format. */
    timeZone?: string | null;
    /** Email address. */
    email?: string | null;
    /** Phone number. */
    phone?: string | null;
    /** Address. */
    address?: LocationsAddress;
    /**
     * Business schedule. Array of weekly recurring time periods when the location is open for business. Limited to 100 time periods.
     *
     * __Note:__ Not supported by Wix Bookings.
     */
    businessSchedule?: BusinessSchedule;
    /**
     * Revision number, which increments by 1 each time the location is updated.
     * To prevent conflicting changes, the existing revision must be used when updating a location.
     */
    revision?: string | null;
    /**
     * Whether the location is archived. Archived locations can't be updated.
     * __Note:__ [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)
     * doesn't affect its `status`.
     * @readonly
     */
    archived?: boolean;
    /**
     * Location types.
     * @maxSize 10
     */
    locationTypes?: LocationsLocationTypeWithLiterals[];
}
/** For future use */
declare enum LocationStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE"
}
/** @enumType */
type LocationStatusWithLiterals = LocationStatus | 'ACTIVE' | 'INACTIVE';
/** For future use */
declare enum LocationsLocationType {
    UNKNOWN = "UNKNOWN",
    BRANCH = "BRANCH",
    OFFICES = "OFFICES",
    RECEPTION = "RECEPTION",
    HEADQUARTERS = "HEADQUARTERS",
    INVENTORY = "INVENTORY"
}
/** @enumType */
type LocationsLocationTypeWithLiterals = LocationsLocationType | 'UNKNOWN' | 'BRANCH' | 'OFFICES' | 'RECEPTION' | 'HEADQUARTERS' | 'INVENTORY';
interface LocationsAddress {
    /**
     * 2-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format.
     * @format COUNTRY
     */
    country?: string | null;
    /** Code for a subdivision (such as state, prefecture, or province) in [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) format. */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /**
     * Postal or zip code.
     * @maxLength 20
     */
    postalCode?: string | null;
    /** Street address. Includes street name, number, and apartment number in separate fields. */
    streetAddress?: LocationsStreetAddress;
    /** Full address of the location. */
    formatted?: string | null;
    /** Geographic coordinates of location. */
    location?: LocationsAddressLocation;
}
/** Street address. Includes street name, number, and apartment number in separate fields. */
interface LocationsStreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
/** Address Geolocation */
interface LocationsAddressLocation {
    /** Latitude of the location. Must be between -90 and 90. */
    latitude?: number | null;
    /** Longitude of the location. Must be between -180 and 180. */
    longitude?: number | null;
}
/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */
interface BusinessSchedule {
    /**
     * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.
     * @maxSize 100
     */
    periods?: TimePeriod[];
    /**
     * Exceptions to the business's regular hours. The business can be open or closed during the exception.
     * @maxSize 100
     */
    specialHourPeriod?: SpecialHourPeriod[];
}
/** Weekly recurring time periods when the business is regularly open or the service is available. */
interface TimePeriod {
    /** Day of the week the period starts on. */
    openDay?: DayOfWeekWithLiterals;
    /**
     * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     */
    openTime?: string;
    /** Day of the week the period ends on. */
    closeDay?: DayOfWeekWithLiterals;
    /**
     * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     *
     * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.
     */
    closeTime?: string;
}
/** Enumerates the days of the week. */
declare enum DayOfWeek {
    MONDAY = "MONDAY",
    TUESDAY = "TUESDAY",
    WEDNESDAY = "WEDNESDAY",
    THURSDAY = "THURSDAY",
    FRIDAY = "FRIDAY",
    SATURDAY = "SATURDAY",
    SUNDAY = "SUNDAY"
}
/** @enumType */
type DayOfWeekWithLiterals = DayOfWeek | 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY';
/** Exception to the business's regular hours. The business can be open or closed during the exception. */
interface SpecialHourPeriod {
    /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    startDate?: string;
    /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    endDate?: string;
    /**
     * Whether the business is closed (or the service is not available) during the exception.
     *
     * Default: `true`.
     */
    isClosed?: boolean;
    /** Additional info about the exception. For example, "We close earlier on New Year's Eve." */
    comment?: string;
}
interface Rate {
    /**
     * Mapping between a named price option, for example, adult or child prices, and the price, currency, and down payment amount.
     * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.
     */
    labeledPriceOptions?: Record<string, Price>;
    /**
     * Textual price information used when **Price Per Session** is set to **Custom Price** in the app's service details page.
     * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.
     */
    priceText?: string | null;
}
interface Price {
    /**
     * Required payment amount.
     * @format DECIMAL_VALUE
     */
    amount?: string;
    /**
     * Currency in which the amount is quoted.
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Amount of a down payment or deposit as part of the transaction.
     * @format DECIMAL_VALUE
     */
    downPayAmount?: string;
}
/**
 * <!-- Needs updating when recurrence has been tested
 * Schedule's availability calculation is executed by the schedule's available intervals and this additional information.
 * Schedule's available intervals are recurring intervals (defined in the schedule) minus sessions that has no more spots for bookings (including time between_slots), or schedule's sessions with open spots for bookings.-->
 */
interface Availability {
    /** Date and time the schedule starts to be available for booking. */
    start?: Date | null;
    /** Date and time the schedule stops being available for booking. No value indicates no end time. */
    end?: Date | null;
    /** Other schedules that impact the availability calculation. Relevant only when there are availability constraints. */
    linkedSchedules?: LinkedSchedule[];
    /** Constraints for calculating the schedule's availability. */
    constraints?: AvailabilityConstraints;
}
/** Describes how to calculate the specific slots that are available for booking. */
interface AvailabilityConstraints {
    /**
     * A list of duration options for slots, in minutes. Minimum value for a duration is 1.
     * The availability calculation generates slots with these durations, where there is no conflict with existing sessions or other availability constraints.
     * @min 1
     */
    slotDurations?: number[];
    /**
     * The number of minutes between the `end` of one slot, and the `start` of the next.
     * Minimum value is 0, maximum value is 120.
     * @max 720
     */
    timeBetweenSlots?: number;
    /**
     * Specify how to split the slots in intervals of minutes.
     * This value indicates the time between available slots' start time. e.g., from 5 minute slots (3:00, 3:05, 3:15) and 1 hour slots (3:00, 4:00, 5:00).
     * Optional. The default is the first duration in slot_durations field.
     * Deprecated. Use the `split_slots_interval.value_in_minutes`.
     * @deprecated
     */
    splitInterval?: number | null;
    /**
     * An object defining the time between available slots' start times.  For example, a slot with slots_split_interval=5 can start every 5 minutes. The default is the slot duration.
     * @readonly
     */
    slotsSplitInterval?: SplitInterval;
}
/** The time between available slots' start times. For example, For 5 minute slots, 3:00, 3:05, 3:15 etc. For 1 hour slots, 3:00, 4:00, 5:00 etc. */
interface SplitInterval {
    /**
     * Whether the slot duration is used as the split interval value.
     * If `same_as_duration` is `true`, the `value_in_minutes` is the sum of the first duration in
     * `schedule.availabilityConstraints.SlotDurations` field, and `schedule.availabilityConstraints.TimeBetweenSlots` field.
     */
    sameAsDuration?: boolean | null;
    /** Number of minutes between available slots' start times when `same_as_duration` is `false`. */
    valueInMinutes?: number | null;
}
interface Participant {
    /**
     * Participant ID. Currently represents the booking.id.
     * @format GUID
     */
    _id?: string;
    /**
     * Contact ID.
     * @format GUID
     */
    contactId?: string | null;
    /** Participant's name. */
    name?: string | null;
    /** Participant's phone number. */
    phone?: string | null;
    /** Participant's email address. */
    email?: string | null;
    /** Group or party size. The number of people attending. Defaults to 0. Maximum is 250. */
    partySize?: number;
    /**
     * Approval status for the participant.
     * <!-- Commented out untill updateParticipant is exposed Generally the same status as the booking, unless updated using the `updateParticipant()` API. Defaults to `"UNDEFINED"`.-->
     */
    approvalStatus?: ApprovalStatusWithLiterals;
    /**
     * Whether the participant was inherited from the schedule, as opposed to being booked directly to the session.
     * @readonly
     */
    inherited?: boolean;
}
declare enum ApprovalStatus {
    /** Default. */
    UNDEFINED = "UNDEFINED",
    /** Pending business approval. */
    PENDING = "PENDING",
    /** Approved by the business. */
    APPROVED = "APPROVED",
    /** Declined by the business. */
    DECLINED = "DECLINED"
}
/** @enumType */
type ApprovalStatusWithLiterals = ApprovalStatus | 'UNDEFINED' | 'PENDING' | 'APPROVED' | 'DECLINED';
interface ExternalCalendarOverrides {
    /** Synced title of the external calendar event. */
    title?: string | null;
    /** Synced description of the external calendar event. */
    description?: string | null;
}
declare enum ScheduleStatus {
    /** Undefined schedule status. */
    UNDEFINED = "UNDEFINED",
    /** The schedule was created. */
    CREATED = "CREATED",
    /** The schedule was cancelled. */
    CANCELLED = "CANCELLED"
}
/** @enumType */
type ScheduleStatusWithLiterals = ScheduleStatus | 'UNDEFINED' | 'CREATED' | 'CANCELLED';
interface Version {
    /** Schedule version number, updated each time the schedule is updated. */
    scheduleVersion?: number | null;
    /** Participants version number, updated each time the schedule participants are updated. */
    participantsVersion?: number | null;
}
interface ConferenceProvider {
    /** Conferencing provider ID */
    providerId?: string;
}
interface CalendarConference {
    /** Wix Calendar conference ID. */
    _id?: string;
    /** Conference meeting ID in the provider's conferencing system. */
    externalId?: string;
    /** Conference provider ID. */
    providerId?: string;
    /** URL used by the host to start the conference. */
    hostUrl?: string;
    /** URL used by a guest to join the conference. */
    guestUrl?: string;
    /** Password to join the conference. */
    password?: string | null;
    /** Conference description. */
    description?: string | null;
    /** Conference type. */
    conferenceType?: ConferenceTypeWithLiterals;
    /** ID of the account owner in the video conferencing service. */
    accountOwnerId?: string | null;
}
declare enum ConferenceType {
    /** Undefined conference type. */
    UNDEFINED = "UNDEFINED",
    /** API-generated online meeting. */
    ONLINE_MEETING_PROVIDER = "ONLINE_MEETING_PROVIDER",
    /** User-defined meeting. */
    CUSTOM = "CUSTOM"
}
/** @enumType */
type ConferenceTypeWithLiterals = ConferenceType | 'UNDEFINED' | 'ONLINE_MEETING_PROVIDER' | 'CUSTOM';
interface ScheduleUpdated {
    /** The old schedule before the update. */
    oldSchedule?: Schedule;
    /** The new schedule after the update. */
    newSchedule?: Schedule;
    /**
     * Recurring sessions updated event. If this field is given, the reason for the schedule updated event was
     * updating at least one of the given schedule's recurring sessions.
     * This event is triggered by create/update/delete recurring session apis.
     */
    recurringSessions?: RecurringSessionsUpdated;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
    /**
     * Whether this notification was created as a result of an anonymization request, such as GDPR.
     * An anonymized participant will have the following details:
     * name = "deleted"
     * phone = "deleted"
     * email = "<EMAIL>"
     */
    triggeredByAnonymizeRequest?: boolean | null;
}
interface RecurringSessionsUpdated {
    /** Old schedule's recurring session list. */
    oldRecurringSessions?: Session[];
    /** New schedule's recurring session list. */
    newRecurringSessions?: Session[];
}
interface Session {
    /**
     * Session ID.
     * @readonly
     */
    _id?: string | null;
    /**
     * ID of the schedule that the session belongs to.
     * @immutable
     */
    scheduleId?: string;
    /**
     * ID of the resource or service that the session's schedule belongs to.
     * @readonly
     */
    scheduleOwnerId?: string | null;
    /** Original start date and time of the session in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. */
    originalStart?: Date | null;
    /** An object specifying the start date and time of the session. If the session is a recurring session, `start` must contain a `localDateTime`. */
    start?: CalendarDateTime;
    /**
     * An object specifying the end date and time of the session. The `end` time must be after the `start` time and be same type as `start`.
     * If the session is a recurring session, `end` must contain a `localDateTime`.
     */
    end?: CalendarDateTime;
    /**
     * An object specifying a list of schedules and the way each schedule's availability is affected by the session. For example, the schedule of an instructor is affected by sessions of the class that they instruct.
     * The array is inherited from the schedule and can be overridden even if the session is a recurring session.
     */
    affectedSchedules?: LinkedSchedule[];
    /**
     * Session title.
     * The value is inherited from the schedule and can be overridden unless the session is a recurring session.
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * __Deprecated.__
     * Tags for the session.
     * The value is inherited from the schedule and can be overridden unless the session is a recurring session.
     * @maxLength 200
     * @deprecated
     */
    tags?: string[] | null;
    /**
     * An object describing the location where the session takes place.
     * Defaults to the schedule location.
     * For single sessions, `session.location.businessLocation` can only be provided for locations that are defined in the schedule using `schedule.location` or `schedule.availability.locations`.
     */
    location?: Location;
    /**
     * Maximum number of participants that can be added to the session. Defaults to the schedule capacity.
     * The value is inherited from the schedule and can be overridden unless the session is a recurring session.
     * @max 1000
     */
    capacity?: number | null;
    /**
     * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.
     * @deprecated
     */
    rate?: Rate;
    /**
     * Time reserved after the session end time, derived from the schedule availability constraints and the time between slots. Read-only.
     * If the session is a recurring session, this field must be empty.
     */
    timeReservedAfter?: number | null;
    /**
     * Additional information about the session.
     * Notes are not supported for recurring sessions.
     * @maxLength 10000
     */
    notes?: string;
    /**
     * The number of participants booked for the session. Read-only.
     * Calculated as the sum of the party sizes.
     * @readonly
     */
    totalNumberOfParticipants?: number;
    /**
     * *Partial list** list of participants booked for the session.
     * The list includes participants who have registered for this specific session, and participants who have registered for a schedule that includes this session.
     * If the session is a recurring session, this field must be empty.
     * To retrieve the full list of session participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).
     */
    participants?: Participant[];
    /**
     * A list of properties for which values were inherited from the schedule.
     * This does not include participants that were inherited from the schedule.
     * @readonly
     */
    inheritedFields?: string[];
    /**
     * __Deprecated.__
     * @deprecated
     */
    externalCalendarOverrides?: ExternalCalendarOverrides;
    /**
     * Session status.
     * @readonly
     */
    status?: SessionStatusWithLiterals;
    /**
     * Recurring interval ID. Defined when a session will be a recurring session. read-only. Optional.
     * For example, when creating a class service  with recurring sessions, you add a recurrence rule to create recurring sessions.
     * This field is omitted for single sessions or instances of recurring sessions.
     * Specified when the session was originally generated from a schedule recurring interval.
     * Deprecated. Use `recurringSessionId`.
     * @readonly
     * @deprecated
     */
    recurringIntervalId?: string | null;
    /**
     * The ID of the recurring session if this session is an instance of a recurrence. Use this ID to update the recurrence and all of the instances.
     * @readonly
     */
    recurringSessionId?: string | null;
    /** Session type. */
    type?: SessionTypeWithLiterals;
    /**
     * A conference created for the session according to the details set in the schedule's conference provider information.
     * If the session is a recurring session, this field is inherited from the schedule.
     * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.
     * @deprecated
     */
    calendarConference?: CalendarConference;
    /**
     * A string representing a recurrence rule (RRULE) for a recurring session, as defined in [iCalendar RFC 5545](https://icalendar.org/iCalendar-RFC-5545/3-3-10-recurrence-rule.html).
     * If the session is an instance of a recurrence pattern, the `instanceOfRecurrence` property will be contain the recurrence rule and this property will be empty.
     * The RRULE defines a rule for repeating a session.
     * Supported parameters are:
     *
     * |Keyword|Description|Supported values|
     * |--|--|---|
     * |`FREQ`|The frequency at which the session is recurs. Required.|`WEEKLY`|
     * |`INTERVAL`|How often, in terms of `FREQ`, the session recurs. Default is 1. Optional.|
     * |`UNTIL`|The UTC end date and time of the recurrence. Optional.|
     * |`BYDAY`|Day of the week when the event should recur. Required.|One of: `MO`, `TU`, `WE`, `TH`, `FR`, `SA`, `SU`|
     *
     *
     * For example, a session that repeats every second week on a Monday until January 7, 2022 at 8 AM:
     * `"FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20220107T080000Z"`
     *
     * <!--ORIGINAL COMMENTS:
     * `FREQ` — The frequency with which the session should be repeated (such as DAILY or WEEKLY).
     * Supported `WEEKLY` value is supported.
     * INTERVAL — Works together with FREQ to specify how often the session should be repeated. For example, FREQ=WEEKLY;INTERVAL=2 means once every two weeks. Optional. Default value is 1.
     * COUNT — The number of times this event should be repeated. Not yet supported.
     * UNTIL — The UTC date & time until which the session should be repeated. This parameter is optional. When it is not specified, the event repeats forever.
     * The format is a short ISO date, followed by 'T' and a short time with seconds and without milliseconds, terminated by the UTC designator 'Z'. For example, until Jan. 19th 2018 at 7:00 AM: 'UNTIL=20180119T070000Z'.
     * BYDAY - The days of the week when the event should be repeated. Currently, only a single day is supported. This parameter is mandatory.
     * Possible values are: MO, TU, WE, TH, FR, SA, SU
     * Note that DTSTART and DTEND lines are not allowed in this field; session start and end times are specified in the start and end fields.
     * **Example**: FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20200427T070000Z
     * ORIGINAL COMMENTS-->
     */
    recurrence?: string | null;
    /**
     * A string representing a recurrence rule (RRULE) if the session is an instance of a recurrence pattern.
     * Empty when the session is not an instance of a recurrence rule, or if the session defines a recurrence pattern, and `recurrence` is not empty.
     * @readonly
     */
    instanceOfRecurrence?: string | null;
    /**
     * The session version.
     * Composed by the schedule, session and participants versions.
     * @readonly
     */
    version?: SessionVersion;
}
interface CalendarDateTime {
    /**
     * UTC date-time in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. If a time zone offset is specified, the time is converted to UTC. For example, if you specify  `new Date('2021-01-06T16:00:00.000-07:00')`, the stored value will be `"2021-01-06T23:00:00.000Z"`.
     * Required if `localDateTime` is not specified.
     * If `localDateTime` is specified, `timestamp` is calculated as `localDateTime`, using the business's time zone.
     */
    timestamp?: Date | null;
    /** An object containing the local date and time for the business's time zone. */
    localDateTime?: LocalDateTime;
    /**
     * The time zone. Optional. Derived from the schedule's time zone.
     * In case this field is associated with recurring session, this field is empty.
     * @readonly
     */
    timeZone?: string | null;
}
interface LocalDateTime {
    /** Year. 4-digit format. */
    year?: number | null;
    /**
     * Month number, from 1-12.
     * @min 1
     * @max 12
     */
    monthOfYear?: number | null;
    /** Day of the month, from 1-31. */
    dayOfMonth?: number | null;
    /**
     * Hour of the day in 24-hour format, from 0-23.
     * @max 23
     */
    hourOfDay?: number | null;
    /**
     * Minute, from 0-59.
     * @max 59
     */
    minutesOfHour?: number | null;
}
interface ExternalCalendarInfo {
    /** The external calendar type (e.g. Google Calendar, iCal, etc). */
    calendarType?: CalendarTypeWithLiterals;
}
declare enum SessionStatus {
    /** Undefined status. */
    UNDEFINED = "UNDEFINED",
    /** Session is confirmed. Default status. */
    CONFIRMED = "CONFIRMED",
    /**
     * Session is cancelled.
     * A cancelled session can be the cancellation of a recurring session that should no longer be displayed or a deleted single session.
     * The ListSessions returns cancelled sessions only if 'includeDelete' flag is set to true.
     */
    CANCELLED = "CANCELLED"
}
/** @enumType */
type SessionStatusWithLiterals = SessionStatus | 'UNDEFINED' | 'CONFIRMED' | 'CANCELLED';
declare enum SessionType {
    UNDEFINED = "UNDEFINED",
    /**
     * Creates an event on the calendar for the owner of the schedule that the session belongs to.
     * Default type.
     */
    EVENT = "EVENT",
    /** Represents a resource's available working hours. */
    WORKING_HOURS = "WORKING_HOURS",
    /** Deprecated. Please use WORKING_HOURS. */
    TIME_AVAILABILITY = "TIME_AVAILABILITY",
    /** Deprecated. Represents a resource's available hours. Please use WORKING_HOURS. */
    AVAILABILITY = "AVAILABILITY"
}
/** @enumType */
type SessionTypeWithLiterals = SessionType | 'UNDEFINED' | 'EVENT' | 'WORKING_HOURS' | 'TIME_AVAILABILITY' | 'AVAILABILITY';
interface SessionVersion {
    /** Incremental version number, which is updated on each change to the session or on changes affecting the session. */
    number?: string | null;
}
interface ParticipantNotification {
    /**
     * Whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean;
    /** Custom message to send to the participants about the changes to the booking. */
    message?: string | null;
}
interface ScheduleCancelled {
    schedule?: Schedule;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
    oldSchedule?: Schedule;
}
interface SessionCreated {
    session?: Session;
}
interface SessionUpdated {
    oldSession?: Session;
    newSession?: Session;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
    /**
     * Whether this notification was created as a result of an anonymization request, such as GDPR.
     * An anonymized participant will have the following details:
     * name = "deleted"
     * phone = "deleted"
     * email = "<EMAIL>"
     */
    triggeredByAnonymizeRequest?: boolean | null;
}
interface SessionCancelled {
    session?: Session;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
}
interface AvailabilityPolicyUpdated {
    availabilityPolicy?: AvailabilityPolicy;
}
/** Availability policy applied to all site schedules. */
interface AvailabilityPolicy {
    /** Specify how to split the schedule slots in intervals of minutes. */
    splitInterval?: SplitInterval;
}
interface IntervalSplit {
    scheduleId?: string;
    intervals?: RecurringInterval[];
    newScheduleVersion?: number | null;
    oldScheduleVersion?: number | null;
}
interface RecurringSessionSplit {
    scheduleId?: string;
    recurringSessions?: Session[];
    newScheduleVersion?: number | null;
    oldScheduleVersion?: number | null;
}
/** Schedule unassigned from user. */
interface ScheduleUnassignedFromUser {
    /**
     * The Wix user id.
     * @format GUID
     */
    userId?: string | null;
    /** The schedule that was unassigned from the user. */
    schedule?: Schedule;
}
interface MultipleSessionsCreated {
    schedulesWithSessions?: ScheduleWithSessions[];
}
interface ScheduleWithSessions {
    schedule?: Schedule;
    siteProperties?: SitePropertiesOnScheduleCreation;
    sessions?: Session[];
}
interface SitePropertiesOnScheduleCreation {
    /** The global time zone value. */
    timeZone?: string | null;
}
interface MigrationEvent {
    migrationData?: MigrationData;
}
interface MigrationData {
    businessId?: string | null;
    staffs?: StaffData[];
}
interface StaffData {
    resourceId?: string;
    syncRequestEmail?: string;
    refreshToken?: string;
}
interface Empty {
}
/** @docsIgnore */
type ConnectByOAuthApplicationErrors = {
    code?: 'NOT_SUPPORTED_BY_PROVIDER';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type ConnectByCredentialsApplicationErrors = {
    code?: 'BAD_CREDENTIALS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_SUPPORTED_BY_PROVIDER';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'PREMIUM_FEATURE_NOT_ENABLED';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type ListCalendarsApplicationErrors = {
    code?: 'CONNECTION_NOT_CONNECTED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_SUPPORTED_BY_PROVIDER';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'PREMIUM_FEATURE_NOT_ENABLED';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type UpdateSyncConfigApplicationErrors = {
    code?: 'NOT_SUPPORTED_BY_PROVIDER';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type DisconnectApplicationErrors = {
    code?: 'CONNECTION_ALREADY_DISCONNECTED';
    description?: string;
    data?: Record<string, any>;
};
/**
 * Retrieves a list of external calendar providers supported on the Wix site.
 *
 *
 * The list of external calendar providers includes:
 *
 * + External calendar providers that are supported by default, such as Google, Apple, and Microsoft.
 * + External calenders for which the site owner has enabled integration by installing an app.
 *
 * For each provider, check `features.connectMethods` to find out whether to use
 * _Connect By Credentials_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))
 * or _Connect By OAuth_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))
 * to establish a connection.
 * @public
 * @permissionId CALENDAR.LIST_EXTERNAL_CALENDAR_PROVIDERS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListProviders
 */
declare function listProviders(): Promise<NonNullablePaths<ListProvidersResponse, `providers` | `providers.${number}.calendarType` | `providers.${number}.features.listEventFromCalendars` | `providers.${number}.features.syncToCalendar`, 5>>;
/**
 * Retrieves an external calendar connection by ID.
 *
 *
 * The `syncConfig` property contains configuration details about event import
 * from and event export to the external calendar.
 * @param connectionId - ID of the connection to retrieve.
 * @public
 * @requiredField connectionId
 * @permissionId CALENDAR.READ_OWN_EXTERNAL_CALENDAR_CONNECTIONS
 * @permissionId CALENDAR.READ_ALL_EXTERNAL_CALENDAR_CONNECTIONS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.GetConnection
 */
declare function getConnection(connectionId: string): Promise<NonNullablePaths<GetConnectionResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6>>;
/**
 * Retrieves a list of external calendar connections.
 *
 *
 * ## Filter
 *
 * You can filter by *schedule ID*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
 *
 * ## Partial success
 *
 * By default, the call fails if details for at least 1 connection can't be
 * retrieved from the external provider. However, you can specify
 * `{"partialFailure": true}` to allow the call to succeed, if details for at
 * least 1 connection can be retrieved.
 * @public
 * @param options - Options to use when listing connections.
 * @permissionId CALENDAR.READ_OWN_EXTERNAL_CALENDAR_CONNECTIONS
 * @permissionId CALENDAR.READ_ALL_EXTERNAL_CALENDAR_CONNECTIONS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListConnections
 */
declare function listConnections(options?: ListConnectionsOptions): Promise<NonNullablePaths<ListConnectionsResponse, `connections` | `connections.${number}.calendarType` | `connections.${number}.status` | `connections.${number}.errorReason` | `failedProviderIds`, 4>>;
interface ListConnectionsOptions {
    /**
     * _Schedule IDs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to filter by.
     *
     * Default: Returns all connections.
     * @format GUID
     */
    scheduleIds?: string[] | null;
    /**
     * Whether to return a partial list of connections if details can't be
     * retrieved for all connections.
     *
     * Default: `false`
     */
    partialFailure?: boolean | null;
}
/**
 * Connects a *Wix schedule*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
 * to an external calendar account following [OAuth authorization protocol](https://oauth.net/2/).
 *
 *
 * ## Authorization flow
 *
 * The full authorization flow looks like this:
 *
 * 1. Call *Connect By OAuth* with the appropriate parameters.
 * 2. Redirect the owner of the external calendar account to the returned
 * `oAUthUrl`.
 * 3. The account owner authorizes access.
 * 4. The account owner is automatically redirected to the URL you've provided
 * in `redirectUrl` of the *Connect By OAuth* call.
 * 5. Save the new `connectionId`. You find it as a query parameter in the URL
 * to which the account owner is redirected.
 *
 * See *this sample flow*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/sample-flows#connect-an-external-calendar-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/sample-flows#connect-an-external-calendar-by-oauth))
 * for more details.
 *
 * ## Failure consequences
 *
 * If the attempt to connect fails, the account owner is still redirected to
 * the URL you specify in `redirectUrl`, but it incudes an `error` query
 * parameter set to 1 of the following values:
 *
 * - `reject`: The external calendar owner has rejected the authorization request.
 * - `unsupported`: Connecting to the user's external account type is not supported by the provider.
 * - `internal`: An error unrelated to the client or the request that prevents the server from fulfilling the request.
 *
 * ## Next steps
 *
 * After connecting an external calendar account to a Wix schedule, you can do
 * the following.
 *
 * ### Check the 2-way-sync settings
 *
 * Call *List Connections*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))
 * and filter by the relevant schedule ID.
 *
 * If needed, call *Update Sync Config*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))
 * to adjust the syncing configuration.
 *
 * ### Retrieve external calendar events
 *
 * Once a connection is successfully created, you can call *List Events*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
 * to obtain an up-to-date list of events in the connected external calendars.
 *
 * ## When to call Connect By Credentials instead
 *
 * You can call *Connect By Credentials*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))
 * if:
 *
 * + The external calendar provider doesn't support OAuth.
 * + You don't want to redirect the account owner.
 *
 * Call *List Providers*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))
 * for a complete list of external calendar providers supported on the Wix site,
 * including which authorization methods they support.
 * @param scheduleId - ID of the schedule to connect with the external calendar account.
 * @param redirectUrl - URL to redirect the user to after they authorize access to the external calendar account.
 *
 * If the connection is successfully established, the user is redirected to this URL with a query parameter `connectionId` containing the new connection ID.
 * If the attempt to connect fails, the user is redirected to this URL with a query parameter `error` containing the error type.
 * @public
 * @requiredField providerId
 * @requiredField redirectUrl
 * @requiredField scheduleId
 * @param providerId - ID of the external calendar provider. Find this with the `listProviders()` function.
 * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS
 * @permissionId CALENDAR.MANAGE_ALL_EXTERNAL_CALENDAR_CONNECTIONS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByOAuth
 */
declare function connectByOAuth(providerId: string, scheduleId: string, redirectUrl: string): Promise<ConnectByOAuthResponse & {
    __applicationErrorsType?: ConnectByOAuthApplicationErrors;
}>;
/**
 * Connects a *Wix schedule*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
 * to an external calendar account using credentials.
 *
 *
 * ## Next steps
 *
 * After connecting an external calendar account to a Wix schedule, you can do
 * the following.
 *
 * ### Check the 2-way-sync settings
 *
 * Call *List Connections*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))
 * and filter by the relevant schedule ID.
 *
 * If needed, call *Update Sync Config*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))
 * to adjust the syncing configuration.
 *
 * ### Retrieve external calendar events
 *
 * Once a connection is successfully created, you can call *List Events*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
 * to obtain an up-to-date list of events in the connected external calendars.
 *
 * ## When to call Connect By OAuth instead
 *
 * You could call *Connect By OAuth*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))
 * if:
 *
 * + The external calendar provider doesn't support authorization by credentials.
 * + You don't have access to the external calendar account credentials.
 * + You want the account owner to approve the connection.
 *
 * Call *List Providers*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))
 * for a complete list of external calendar providers supported on the Wix site,
 * including which authorization methods they support.
 * @param scheduleId - ID of the schedule to connect with the external calendar account.
 * @param email - Email address for the external calendar account.
 * @param password - Password for the external calendar account.
 * @public
 * @requiredField email
 * @requiredField password
 * @requiredField providerId
 * @requiredField scheduleId
 * @param providerId - ID of the external calendar provider. Find this with the `listProviders()` function.
 * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS
 * @permissionId CALENDAR.MANAGE_ALL_EXTERNAL_CALENDAR_CONNECTIONS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByCredentials
 */
declare function connectByCredentials(providerId: string, scheduleId: string, email: string, password: string): Promise<NonNullablePaths<ConnectByCredentialsResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6> & {
    __applicationErrorsType?: ConnectByCredentialsApplicationErrors;
}>;
/**
 * Retrieves details about the external calendar accounts associated with the specified connection.
 * @param connectionId - ID of the external calendar connection to list calendars for.
 * @public
 * @requiredField connectionId
 * @permissionId CALENDAR.READ_OWN_EXTERNAL_CALENDAR_CONNECTIONS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListCalendars
 */
declare function listCalendars(connectionId: string): Promise<NonNullablePaths<ListCalendarsResponse, `calendars`, 2> & {
    __applicationErrorsType?: ListCalendarsApplicationErrors;
}>;
/**
 * Updates the import and export settings for an external calendar connection's
 * events.
 *
 *
 * A connection's `syncConfig` object determines:
 *
 * + Whether events from 1 or more accounts belonging to the external
 * calendar are imported to the connected *Wix schedule*.
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
 * If importing events is enabled, you can call _List Events_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
 * to retrieve external calendar events.
 * + Whether events from the Wix schedule are exported to 1 or more accounts
 * belonging to the external calendar.
 *
 * To check the current import and export settings, you can call
 * _Get Connection_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/get-connection) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/get-connection))
 * or _List Connections_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections)).
 * @param connectionId - ID of the external calendar connection to update.
 * @param syncConfig - Updated sync configuration details.
 * @public
 * @requiredField connectionId
 * @requiredField syncConfig
 * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.UpdateSyncConfig
 */
declare function updateSyncConfig(connectionId: string, syncConfig: ConnectionSyncConfig): Promise<NonNullablePaths<UpdateSyncConfigResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6> & {
    __applicationErrorsType?: UpdateSyncConfigApplicationErrors;
}>;
/**
 * Disconnects a Wix schedule from an external calendar and deletes all Wix
 * calendar *events*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
 * from the external calendar.
 *
 *
 * When an external calendar is disconnected, the connection's `status`
 * changes to `DISCONNECTED`.
 *
 * After disconnecting, _List Events_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
 * no longer returns events from the disconnected external calendar.
 * @param connectionId - ID of the external calendar connection to disconnect.
 * @public
 * @requiredField connectionId
 * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS
 * @permissionId CALENDAR.MANAGE_ALL_EXTERNAL_CALENDAR_CONNECTIONS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.Disconnect
 */
declare function disconnect(connectionId: string): Promise<NonNullablePaths<DisconnectResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6> & {
    __applicationErrorsType?: DisconnectApplicationErrors;
}>;
/**
 * Retrieves a list of events from all external calendar accounts, based on
 * the provided filtering and paging.
 *
 *
 * ## Filters
 *
 * You must filter by specifying both `from` and `to` dates, unless you specify
 * `cursorPaging.cursor`.
 *
 * Additionally, you can specify `scheduleIds` or `userIds` to further limit
 * which events are returned. By default, events related to all schedules and
 * Wix users are returned.
 *
 * ## Sorting
 *
 * Returned events are sorted by start date in ascending order. You can't
 * adjust the sorting.
 *
 * ## Personal data
 *
 * By default, the following `event` fields aren't returned:
 *
 * + `calendarName`.
 * + `title`.
 * + `scheduleOwnerName`.
 *
 * You can retrieve these fields, by specifying `{"fieldsets": "OWN_PI"}`.
 *
 * ## Partial success
 *
 * By default, the call fails if events for at least 1 connection can't be
 * retrieved from an external provider. However, you can specify
 * `{"partialFailure": true}` to allow the call to succeed, if details for at
 * least 1 connection can be retrieved.
 * @public
 * @param options - Options to use when listing events.
 * @permissionId CALENDAR.READ_EXTERNAL_CALENDAR_EVENTS
 * @applicableIdentity APP
 * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListEvents
 */
declare function listEvents(options?: ListEventsOptions): Promise<NonNullablePaths<ListEventsResponse, `events` | `events.${number}.calendarType` | `failedProviderIds`, 4>>;
interface ListEventsOptions {
    /**
     * Date and time from which to retrieve events,
     * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).
     * Required, unless `cursorPaging.cursor` is provided.
     *
     * Events which start before the `from` time and end after it are included in the returned list.
     */
    from?: string | null;
    /**
     * Date and time until which to retrieve events,
     * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).
     * Required, unless `cursorPaging.cursor` is provided.
     *
     * Events which start before the `to` time and end after it are included in the returned list.
     */
    to?: string | null;
    /**
     * Schedule IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to the specified schedules.
     * Maximum of 100 schedule IDs per request.
     * @format GUID
     * @maxSize 100
     */
    scheduleIds?: string[] | null;
    /**
     * Wix user IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to schedules belonging to the specified Wix users.
     * Maximum of 100 Wix user IDs per request.
     * @format GUID
     * @maxSize 100
     */
    userIds?: string[] | null;
    /**
     * Whether to include only all-day events in the returned list.
     * If `true`, only all-day events are returned.
     * If `false`, only events with a specified time are returned.
     *
     * Default: All events are returned.
     */
    allDay?: boolean | null;
    /**
     * Predefined sets of fields to return.
     * - `NO_PI`: Returns event objects without personal information.
     * - `OWN_PI`: Returns complete event objects, including personal information.
     *
     * Default: `NO_PI`
     */
    fieldsets?: string[];
    /** Pagination options. */
    cursorPaging?: CursorPaging;
    /**
     * Whether to return a partial list of events if details can't be retrieved for some connections.
     *
     * Default: `false`
     */
    partialFailure?: boolean | null;
}

export { type Address, type AddressLocation, type AddressStreetOneOf, ApprovalStatus, type ApprovalStatusWithLiterals, type Availability, type AvailabilityConstraints, type AvailabilityPolicy, type AvailabilityPolicyUpdated, type BusinessSchedule, type Calendar, type CalendarConference, type CalendarDateTime, CalendarType, type CalendarTypeWithLiterals, type Calendars, type ConferenceProvider, ConferenceType, type ConferenceTypeWithLiterals, type ConnectByCredentialsApplicationErrors, type ConnectByCredentialsRequest, type ConnectByCredentialsResponse, type ConnectByOAuthApplicationErrors, type ConnectByOAuthRequest, type ConnectByOAuthResponse, ConnectMethod, type ConnectMethodWithLiterals, type Connection, type ConnectionSyncConfig, type ConnectionSyncConfigListEventFromCalendars, type ConnectionSyncConfigListEventFromCalendarsListFromOneOf, type ConnectionSyncConfigSyncToCalendar, type ConnectionSyncConfigSyncToCalendarSyncToOneOf, type CursorPaging, type CursorPagingMetadata, type Cursors, Day, DayOfWeek, type DayOfWeekWithLiterals, type DayWithLiterals, type DedicatedCalendar, type DisconnectApplicationErrors, type DisconnectRequest, type DisconnectResponse, type Empty, ErrorReason, type ErrorReasonWithLiterals, type Event, type ExternalCalendar, type ExternalCalendarInfo, type ExternalCalendarOverrides, type Frequency, type GetConnectionRequest, type GetConnectionResponse, type HeadersEntry, type Interval, type IntervalSplit, type LinkedSchedule, type ListCalendarsApplicationErrors, type ListCalendarsRequest, type ListCalendarsResponse, type ListConnectionsOptions, type ListConnectionsRequest, type ListConnectionsResponse, ListEventFromCalendars, type ListEventFromCalendarsWithLiterals, type ListEventsOptions, type ListEventsRequest, type ListEventsResponse, type ListProvidersRequest, type ListProvidersResponse, type LocalDateTime, type Location, LocationStatus, type LocationStatusWithLiterals, LocationType, type LocationTypeWithLiterals, type LocationsAddress, type LocationsAddressLocation, type LocationsLocation, LocationsLocationType, type LocationsLocationTypeWithLiterals, type LocationsStreetAddress, type MigrationData, type MigrationEvent, type MultipleSessionsCreated, type Participant, type ParticipantNotification, type PathParametersEntry, type Price, type PrimaryCalendar, type Provider, type ProviderFeatures, type QueryParametersEntry, type Rate, type RawHttpRequest, type RawHttpResponse, type RecurringInterval, RecurringIntervalType, type RecurringIntervalTypeWithLiterals, type RecurringSessionSplit, type RecurringSessionsUpdated, type Schedule, type ScheduleCancelled, type ScheduleCreated, type ScheduleNotification, type ScheduleNotificationEventOneOf, ScheduleStatus, type ScheduleStatusWithLiterals, type ScheduleUnassignedFromUser, type ScheduleUpdated, type ScheduleWithSessions, type Session, type SessionCancelled, type SessionCreated, SessionStatus, type SessionStatusWithLiterals, SessionType, type SessionTypeWithLiterals, type SessionUpdated, type SessionVersion, type SitePropertiesOnScheduleCreation, type SpecialHourPeriod, type SplitInterval, type StaffData, Status, type StatusWithLiterals, type StreetAddress, type Subdivision, SyncToCalendar, type SyncToCalendarWithLiterals, SyncToErrorReason, type SyncToErrorReasonWithLiterals, type TimePeriod, Transparency, type TransparencyWithLiterals, type UpdateSyncConfigApplicationErrors, type UpdateSyncConfigRequest, type UpdateSyncConfigResponse, type Version, connectByCredentials, connectByOAuth, disconnect, getConnection, listCalendars, listConnections, listEvents, listProviders, updateSyncConfig };
