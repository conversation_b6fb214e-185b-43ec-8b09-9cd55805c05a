// src/bookings-fees-v1-booking-fee-booking-fees.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsFeesV1BookingFeesUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-fees";
function listBookingFeesByBookingIds(payload) {
  function __listBookingFeesByBookingIds({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "GET",
      methodFqn: "wix.bookings.fees.v1.BookingFees.ListBookingFeesByBookingIds",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __listBookingFeesByBookingIds;
}
function applyBookingFeesToOrder(payload) {
  function __applyBookingFeesToOrder({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "POST",
      methodFqn: "wix.bookings.fees.v1.BookingFees.ApplyBookingFeesToOrder",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees/apply",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __applyBookingFeesToOrder;
}
function collectAppliedBookingFees(payload) {
  function __collectAppliedBookingFees({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "POST",
      methodFqn: "wix.bookings.fees.v1.BookingFees.CollectAppliedBookingFees",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees/collect",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __collectAppliedBookingFees;
}

// src/bookings-fees-v1-booking-fee-booking-fees.meta.ts
function listBookingFeesByBookingIds2() {
  const payload = {};
  const getRequestOptions = listBookingFeesByBookingIds(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/booking-fees",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function applyBookingFeesToOrder2() {
  const payload = {};
  const getRequestOptions = applyBookingFeesToOrder(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-fees/apply",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function collectAppliedBookingFees2() {
  const payload = {};
  const getRequestOptions = collectAppliedBookingFees(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-fees/collect",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  applyBookingFeesToOrder2 as applyBookingFeesToOrder,
  collectAppliedBookingFees2 as collectAppliedBookingFees,
  listBookingFeesByBookingIds2 as listBookingFeesByBookingIds
};
//# sourceMappingURL=meta.mjs.map