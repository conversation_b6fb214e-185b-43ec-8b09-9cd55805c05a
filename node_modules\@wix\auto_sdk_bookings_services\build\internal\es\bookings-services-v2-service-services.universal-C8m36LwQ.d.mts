import { SearchSpec, Search, NonNullablePaths } from '@wix/sdk-types';

/** The `service` object represents an offering that a business provides to its customers. */
interface Service {
    /**
     * Service ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Service type.
     * Learn more about *service types*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)).
     */
    type?: ServiceTypeWithLiterals;
    /**
     * Order of the service within a *category*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object)).
     */
    sortOrder?: number | null;
    /**
     * Service name.
     * @maxLength 400
     * @minLength 1
     */
    name?: string | null;
    /**
     * Service description. For example, `High-class hair styling, cuts, straightening and color`.
     * @maxLength 7000
     */
    description?: string | null;
    /**
     * Short service description, such as `Hair styling`.
     * @maxLength 6000
     */
    tagLine?: string | null;
    /**
     * Default maximum number of customers that can book the service. The service cannot be booked beyond this capacity.
     * @min 1
     * @max 1000
     */
    defaultCapacity?: number | null;
    /** Media associated with the service. */
    media?: Media;
    /** Whether the service is hidden from Wix Bookings pages and widgets. */
    hidden?: boolean | null;
    /**
     * _Category_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object))
     * the service is associated with.
     */
    category?: V2Category;
    /** Form the customer filled out when booking the service. */
    form?: Form;
    /**
     * Payment options for booking the service.
     * Learn more about *service payments*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)).
     */
    payment?: Payment;
    /** Online booking settings. */
    onlineBooking?: OnlineBooking;
    /** Conferencing options for the service. */
    conferencing?: Conferencing;
    /**
     * The locations this service is offered at. Read more about *service locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations)).
     * @immutable
     * @maxSize 500
     */
    locations?: Location[];
    /**
     * _Policy_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction))
     * determining under what conditions this service can be booked. For example, whether the service can only be booked up to 30 minutes before it begins.
     */
    bookingPolicy?: BookingPolicy;
    /**
     * The service's *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),
     * which can be used to manage the service's *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).
     */
    schedule?: Schedule;
    /**
     * IDs of the *staff members*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service. Available only for appointment-based services.
     * @maxSize 220
     * @format GUID
     */
    staffMemberIds?: string[];
    /**
     * Information about which resources must be available so customers can book the service.
     * For example, a meeting room or equipment.
     * @maxSize 3
     */
    serviceResources?: ServiceResource[];
    /**
     * A slug is the last part of the URL address that serves as a unique identifier of the service.
     * The list of supported slugs includes past service names for backwards compatibility, and a custom slug if one was set by the business owner.
     * @readonly
     * @maxSize 100
     */
    supportedSlugs?: Slug[];
    /**
     * Active slug for the service.
     * Learn more about *service slugs*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-slugs) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-slugs)).
     * @readonly
     */
    mainSlug?: Slug;
    /**
     * URLs to various service-related pages, such as the calendar page and the booking page.
     * @readonly
     */
    urls?: URLs;
    /** Extensions enabling users to save custom data related to the service. */
    extendedFields?: ExtendedFields;
    /** Custom SEO data for the service. */
    seoData?: SeoSchema;
    /**
     * Date and time the service was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the service was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Revision number, which increments by 1 each time the service is updated. To
     * prevent conflicting changes, the existing revision must be used when updating
     * a service.
     * @readonly
     */
    revision?: string | null;
}
declare enum ServiceType {
    /** Appointment-based service. */
    APPOINTMENT = "APPOINTMENT",
    /** Class service. */
    CLASS = "CLASS",
    /** Course service. */
    COURSE = "COURSE"
}
/** @enumType */
type ServiceTypeWithLiterals = ServiceType | 'APPOINTMENT' | 'CLASS' | 'COURSE';
interface Media {
    /**
     * Media items associated with the service.
     * @maxSize 100
     */
    items?: MediaItem[];
    /** Primary media associated with the service. */
    mainMedia?: MediaItem;
    /** Cover media associated with the service. */
    coverMedia?: MediaItem;
}
interface MediaItem extends MediaItemItemOneOf {
    /** Details of the image associated with the service, such as URL and size. */
    image?: string;
}
/** @oneof */
interface MediaItemItemOneOf {
    /** Details of the image associated with the service, such as URL and size. */
    image?: string;
}
interface V2Category {
    /**
     * Category ID.
     * @format GUID
     */
    _id?: string;
    /**
     * Category name.
     * @maxLength 500
     * @readonly
     */
    name?: string | null;
    /**
     * Order of a category within a category list.
     * @readonly
     */
    sortOrder?: number | null;
}
interface Form {
    /**
     * ID of the form associated with the service.
     * The form information that you submit when booking includes contact details, participants, and other form fields set up for the service.
     * You can manage the service booking form fields using the Bookings Forms API.
     * @format GUID
     */
    _id?: string;
}
interface FormSettings {
    /** Whether the service booking form should be hidden from the site. */
    hidden?: boolean | null;
}
interface Payment extends PaymentRateOneOf {
    /**
     * The details for the fixed price of the service.
     *
     * Required when: `rateType` is `FIXED`
     */
    fixed?: FixedPayment;
    /**
     * The details for the custom price of the service.
     *
     * Required when: `rateType` is `CUSTOM`
     */
    custom?: CustomPayment;
    /**
     * The details for the varied pricing of the service.
     * Read more about [varied price options](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online#offering-varied-price-options).
     *
     * Required when: `rateType` is `VARIED`
     */
    varied?: VariedPayment;
    /** The rate the customer is expected to pay for the service. */
    rateType?: RateTypeWithLiterals;
    /** The payment options a customer can use to pay for the service. */
    options?: PaymentOptions;
    /**
     * IDs of pricing plans that can be used as payment for the service.
     * @readonly
     * @maxSize 75
     * @format GUID
     */
    pricingPlanIds?: string[];
}
/** @oneof */
interface PaymentRateOneOf {
    /**
     * The details for the fixed price of the service.
     *
     * Required when: `rateType` is `FIXED`
     */
    fixed?: FixedPayment;
    /**
     * The details for the custom price of the service.
     *
     * Required when: `rateType` is `CUSTOM`
     */
    custom?: CustomPayment;
    /**
     * The details for the varied pricing of the service.
     * Read more about [varied price options](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online#offering-varied-price-options).
     *
     * Required when: `rateType` is `VARIED`
     */
    varied?: VariedPayment;
}
declare enum RateType {
    /** Unknown rate type. */
    UNKNOWN_RATE_TYPE = "UNKNOWN_RATE_TYPE",
    /** The service has a fixed price. */
    FIXED = "FIXED",
    /** The service has a custom price, expressed as a price description. */
    CUSTOM = "CUSTOM",
    /** This service is offered with a set of different prices based on different terms. */
    VARIED = "VARIED",
    /** This service is offered free of charge. */
    NO_FEE = "NO_FEE"
}
/** @enumType */
type RateTypeWithLiterals = RateType | 'UNKNOWN_RATE_TYPE' | 'FIXED' | 'CUSTOM' | 'VARIED' | 'NO_FEE';
interface FixedPayment {
    /**
     * The fixed price required to book the service.
     *
     * Required when: `rateType` is `FIXED`
     */
    price?: Money;
    /**
     * The deposit price required to book the service.
     *
     * Required when: `rateType` is `FIXED` and `paymentOptions.deposit` is `true`
     */
    deposit?: Money;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gt:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     * @readonly
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface CustomPayment {
    /**
     * A custom description explaining to the customer how to pay for the service.
     * @maxLength 50
     */
    description?: string | null;
}
interface VariedPayment {
    /** The default price for the service without any variants. It will also be used as the default price for any new variant. */
    defaultPrice?: Money;
    /**
     * The deposit price required to book the service.
     *
     * Required when: `rateType` is `VARIED` and `paymentOptions.deposit` is `true`
     */
    deposit?: Money;
    /**
     * The minimal price a customer may pay for this service, based on its variants.
     * @readonly
     */
    minPrice?: Money;
    /**
     * The maximum price a customer may pay for this service, based on its variants.
     * @readonly
     */
    maxPrice?: Money;
}
interface PaymentOptions {
    /**
     * Customers can pay for the service online.
     * When `true`:
     * + `rateType` must be either `FIXED` or `VARIED`.
     * + `fixed.price` or `varied.default_price` must be specified respectively. Read more about [getting paid online](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online).
     */
    online?: boolean | null;
    /** Customers can pay for the service in person. */
    inPerson?: boolean | null;
    /**
     * This service requires a deposit to be made online in order to book it.
     * When `true`:
     * + `rateType` must be `VARIED` or `FIXED`.
     * + A `deposit` must be specified.
     */
    deposit?: boolean | null;
    /** Customers can pay for the service using a pricing plan. */
    pricingPlan?: boolean | null;
}
interface OnlineBooking {
    /**
     * Whether the service can be booked online.
     * When set to `true`, customers can book the service online. Configure the payment options via the `service.payment` property.
     * When set to `false`, customers cannot book the service online, and the service can only be paid for in person.
     */
    enabled?: boolean | null;
    /** Booking the service requires approval by the Wix user. */
    requireManualApproval?: boolean | null;
    /** Multiple customers can request to book the same time slot. This is relevant when `requireManualApproval` is `true`. */
    allowMultipleRequests?: boolean | null;
}
interface Conferencing {
    /** Whether a conference link is generated for the service's sessions. */
    enabled?: boolean | null;
}
interface Location extends LocationOptionsOneOf {
    /** Information about business locations. */
    business?: BusinessLocationOptions;
    /** Information about custom locations. */
    custom?: CustomLocationOptions;
    /**
     * Location ID.
     * @format GUID
     * @readonly
     */
    _id?: string;
    /**
     * Location type.
     *
     * Default: `CUSTOM`
     */
    type?: LocationTypeWithLiterals;
    /**
     * Location address. Empty for `{"type": "CUSTOMER"}`.
     * @readonly
     */
    calculatedAddress?: CommonAddress;
}
/** @oneof */
interface LocationOptionsOneOf {
    /** Information about business locations. */
    business?: BusinessLocationOptions;
    /** Information about custom locations. */
    custom?: CustomLocationOptions;
}
declare enum LocationType {
    UNKNOWN_LOCATION_TYPE = "UNKNOWN_LOCATION_TYPE",
    /**
     * Location set by the business that is not a standard business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     */
    CUSTOM = "CUSTOM",
    /**
     * Business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     */
    BUSINESS = "BUSINESS",
    /**
     * The customer specifies any address when booking. Available only for
     * appointment-based services.
     */
    CUSTOMER = "CUSTOMER"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNKNOWN_LOCATION_TYPE' | 'CUSTOM' | 'BUSINESS' | 'CUSTOMER';
interface CommonAddress extends CommonAddressStreetOneOf {
    /** Street name and number. */
    streetAddress?: StreetAddress;
    /** @maxLength 255 */
    addressLine1?: string | null;
    /**
     * 2-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format.
     * @format COUNTRY
     */
    country?: string | null;
    /**
     * Code for a subdivision (such as state, prefecture, or province) in [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) format.
     * @maxLength 255
     */
    subdivision?: string | null;
    /**
     * City name.
     * @maxLength 255
     */
    city?: string | null;
    /**
     * Postal or zip code.
     * @maxLength 255
     */
    postalCode?: string | null;
    /**
     * Full address of the location.
     * @maxLength 512
     */
    formatted?: string | null;
}
/** @oneof */
interface CommonAddressStreetOneOf {
    /** Street name and number. */
    streetAddress?: StreetAddress;
    /** @maxLength 255 */
    addressLine?: string | null;
}
/** Street address. Includes street name, number, and apartment number in separate fields. */
interface StreetAddress {
    /**
     * Street number.
     * @maxLength 255
     */
    number?: string;
    /**
     * Street name.
     * @maxLength 255
     */
    name?: string;
    /**
     * Apartment number.
     * @maxLength 255
     */
    apt?: string;
}
interface AddressLocation {
    /** Address latitude. */
    latitude?: number | null;
    /** Address longitude. */
    longitude?: number | null;
}
interface BusinessLocationOptions {
    /**
     * ID of the business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * When setting a business location, specify only the location ID. Other location details are overwritten.
     * @format GUID
     */
    _id?: string;
    /**
     * Business location name.
     * @readonly
     * @maxLength 150
     */
    name?: string;
    /**
     * Whether this is the default location. There can only be a single default location per site.
     * @readonly
     */
    default?: boolean | null;
    /**
     * Business location address.
     * @readonly
     */
    address?: CommonAddress;
    /**
     * Business location email.
     * @format EMAIL
     * @readonly
     */
    email?: string | null;
    /**
     * Business location phone.
     * @format PHONE
     * @readonly
     */
    phone?: string | null;
}
interface CustomLocationOptions {
    /**
     * ID of the custom location.
     * @format GUID
     * @readonly
     */
    _id?: string;
    /** Address of the custom location. */
    address?: CommonAddress;
}
/**
 * `BookingPolicy` is the main entity of `BookingPolicyService` and specifies a set of rules for booking a service
 * by visitors and members.
 *
 * Each `BookingPolicy` consists of a number of sub-policies. When the Bookings App is provisioned to a meta site then a
 * default `BookingPolicy` will be created with defaults for each of these sub-policies. This also applies when a request
 * is received to create a new `BookingPolicy` and one or more of these sub-policies are not provided.
 *
 * Sub-policies are defined in separate objects as specified below.
 *
 * - The `LimitEarlyBookingPolicy` object defines the policy for limiting early bookings.
 * - The `LimitLateBookingPolicy` object defines the policy for limiting late bookings.
 * - The `BookAfterStartPolicy` object defines the policy for booking after the start of the schedule.
 * - The `CancellationPolicy` object defines the policy for canceling a booked entity.
 * - The `ReschedulePolicy` object defines the policy for rescheduling booked entity.
 * - The `WaitlistPolicy` object defines the policy for a waitlist.
 * - The `ParticipantsPolicy` object defines the policy regarding the participants per booking.
 * - The `ResourcesPolicy` object defines the policy regarding the resources per booking.
 * - The `CancellationFeePolicy` object defines the policy regarding cancellation fees.
 * - The `SaveCreditCardPolicy` object defines the policy for saving credit card details.
 *
 * By default each sub-policy is disabled. A more detailed specification of the default settings of each sub-policy
 * can be found in the description of the corresponding object.
 *
 * Partial updates are supported on the main entity level, however in order to update a sub-policy the client needs to provide the whole sub-policy object.
 */
interface BookingPolicy {
    /**
     * The ID to the policy for the booking.
     * @format GUID
     */
    _id?: string;
    /**
     * Date and time the policy was created.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the policy was updated.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the policy.
     * @maxLength 400
     * @readonly
     */
    name?: string | null;
    /**
     * Custom description for the policy. This policy is displayed to the participant.
     * @readonly
     */
    customPolicyDescription?: PolicyDescription;
    /**
     * Whether the policy is the default for the meta site.
     * @readonly
     */
    default?: boolean | null;
    /**
     * Policy for limiting early bookings.
     * @readonly
     */
    limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;
    /**
     * Policy for limiting late bookings.
     * @readonly
     */
    limitLateBookingPolicy?: LimitLateBookingPolicy;
    /**
     * Policy on booking an entity after the start of the schedule.
     * @readonly
     */
    bookAfterStartPolicy?: BookAfterStartPolicy;
    /**
     * Policy for canceling a booked entity.
     * @readonly
     */
    cancellationPolicy?: CancellationPolicy;
    /**
     * Policy for rescheduling a booked entity.
     * @readonly
     */
    reschedulePolicy?: ReschedulePolicy;
    /**
     * Waitlist policy for the service.
     * @readonly
     */
    waitlistPolicy?: WaitlistPolicy;
    /**
     * Policy regarding the participants per booking.
     * @readonly
     */
    participantsPolicy?: ParticipantsPolicy;
    /**
     * Policy for allocating resources.
     * @readonly
     */
    resourcesPolicy?: ResourcesPolicy;
    /**
     * Rules for cancellation fees.
     * @readonly
     */
    cancellationFeePolicy?: CancellationFeePolicy;
    /**
     * Rule for saving credit card.
     * @readonly
     */
    saveCreditCardPolicy?: SaveCreditCardPolicy;
}
/** A description of the policy to display to participants. */
interface PolicyDescription {
    /**
     * Whether the description should be displayed. If `true`, the description is displayed.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * The description to display.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    description?: string;
}
/** The policy for limiting early bookings. */
interface LimitEarlyBookingPolicy {
    /**
     * Whether there is a limit on how early a customer
     * can book. When `false`, there is no limit on the earliest
     * booking time and customers can book in advance, as early as they like.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Maximum number of minutes before the start of the session that a booking can be made. This value must be greater
     * than `latest_booking_in_minutes` in the `LimitLateBookingPolicy` policy.
     *
     * Default: 10080 minutes (7 days)
     * Min: 1 minute
     * @min 1
     */
    earliestBookingInMinutes?: number;
}
/**
 * The policy for limiting late bookings.
 *
 * This policy and the `BookAfterStartPolicy` policy cannot be enabled at the same time. So if this policy
 * is enabled, `BookAfterStartPolicy` must be disabled.
 */
interface LimitLateBookingPolicy {
    /**
     * Whether there is a limit on how late a customer
     * can book. When `false`, there is no limit on the latest
     * booking time and customers can book up to the last minute.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Minimum number of minutes before the start of the session that a booking can be made.
     * For a schedule, this is relative to the start time of the next booked session, excluding past-booked sessions.
     * This value must be less than `earliest_booking_in_minutes` in the `LimitEarlyBookingPolicy` policy.
     *
     * Default: 1440 minutes (1 day)
     * Min: 1 minute
     * @min 1
     */
    latestBookingInMinutes?: number;
}
/**
 * The policy for whether a session can be booked after the start of the schedule.
 * This policy and `LimitLateBookingPolicy` cannot be enabled at the same time. So if this policy
 * is enabled, the `LimitLateBookingPolicy` policy must be disabled.
 */
interface BookAfterStartPolicy {
    /**
     * Whether booking is allowed after the start of the schedule. When `true`,
     * customers can book after the start of the schedule.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
/** The policy for canceling a booked session. */
interface CancellationPolicy {
    /**
     * Whether canceling a booking is allowed. When `true`, customers
     * can cancel the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there is a limit on the latest cancellation time. When `true`,
     * a time limit is enforced.
     *
     * Default: `false`
     */
    limitLatestCancellation?: boolean;
    /**
     * Minimum number of minutes before the start of the booked session that the booking can be canceled.
     *
     * Default: 1440 minutes (1 day)
     * Min: 1 minute
     * @min 1
     */
    latestCancellationInMinutes?: number;
}
/** The policy for rescheduling a booked session. */
interface ReschedulePolicy {
    /**
     * Whether rescheduling a booking is allowed. When `true`, customers
     * can reschedule the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there is a limit on the latest reschedule time. When `true`,
     * a time limit is enforced.
     *
     * Default: `false`
     */
    limitLatestReschedule?: boolean;
    /**
     * Minimum number of minutes before the start of the booked session that the booking can be rescheduled.
     *
     * Default: 1440 minutes (1 day)
     * Min: 1 minute
     * @min 1
     */
    latestRescheduleInMinutes?: number;
}
/** The policy for the waitlist. */
interface WaitlistPolicy {
    /**
     * Whether the session has a waitlist. If `true`, there is a waitlist.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Number of spots available in the waitlist.
     *
     * Default: 10 spots
     * Min: 1 spot
     * @min 1
     */
    capacity?: number;
    /**
     * Amount of time a participant is given to book, once notified that a spot is available.
     *
     * Default: 10 minutes
     * Min: 1 spot
     * @min 1
     */
    reservationTimeInMinutes?: number;
}
/** The policy for the maximum number of participants per booking. */
interface ParticipantsPolicy {
    /**
     * Maximum number of participants allowed.
     *
     * Default: 1 participant
     * Min: 1 participant
     * @min 1
     */
    maxParticipantsPerBooking?: number;
}
/** The policy regarding the allocation of resources (e.g. staff members). */
interface ResourcesPolicy {
    /**
     * `true` if this policy is enabled, `false` otherwise.
     * When `false` then the client must always select a resource when booking an appointment.
     */
    enabled?: boolean;
    /**
     * `true`, if it is allowed to automatically assign a resource when booking an appointment,
     * `false`, if the client must always select a resource.
     *
     * Default: `false`
     */
    autoAssignAllowed?: boolean;
}
interface CancellationFeePolicy {
    /**
     * Whether canceling a booking will result in a cancellation fee
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Cancellation windows describing the time of cancellation and the fee to charge.
     * @maxSize 2
     */
    cancellationWindows?: CancellationWindow[];
    /**
     * Whether the cancellation fee should not be automatically collected when customer cancels the booking.
     *
     * Default: `true`
     */
    autoCollectFeeEnabled?: boolean | null;
}
interface CancellationWindow extends CancellationWindowFeeOneOf {
    /** Amount to be charged as a cancellation fee. */
    amount?: Money;
    /**
     * Percentage of the original price to be charged as a cancellation fee.
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
    /**
     * The fee will be applied if the booked session starts within this start time in minutes.
     * @min 1
     */
    startInMinutes?: number | null;
}
/** @oneof */
interface CancellationWindowFeeOneOf {
    /** Amount to be charged as a cancellation fee. */
    amount?: Money;
    /**
     * Percentage of the original price to be charged as a cancellation fee.
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
}
interface SaveCreditCardPolicy {
    /** Default: `false` */
    enabled?: boolean;
}
interface Schedule {
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to which the service's events belong.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Start time of the first session in the schedule. For courses only.
     * @readonly
     */
    firstSessionStart?: Date | null;
    /**
     * End time of the last session in the schedule. For courses only.
     * @readonly
     */
    lastSessionEnd?: Date | null;
    /** Limitations affecting the service availability. */
    availabilityConstraints?: AvailabilityConstraints;
}
interface AvailabilityConstraints {
    /**
     * Calculated list of all supported session durations for the service. For
     * appointment-based services without varied pricing based on session length, it
     * matches the single value in the `sessionDurations` array. For appointment-based
     * services with varied pricing based on session length, it includes session
     * durations for all *variants*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),
     * while ignoring `sessionDurations`.
     * For courses and classes, it includes durations for all future
     * recurring sessions but excludes durations for one-off or past recurring sessions.
     * @readonly
     * @maxSize 50
     */
    durations?: Duration[];
    /**
     * List of supported session durations in minutes.
     *
     * - For appointment-based services, specify `sessionDurations` when creating a service.
     * - For appointment-based services with varied pricing by session length, you must still specify `sessionDurations`, but the values are ignored. Actual durations are taken from the service variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * - For classes or courses, don't specify `sessionDurations` when creating a service.
     *
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     * @maxSize 50
     */
    sessionDurations?: number[];
    /**
     * The number of minutes between the end of a session and the start of the next.
     *
     *
     * Min: `0` minutes
     * Max: `720` minutes
     * @max 720
     */
    timeBetweenSessions?: number;
}
interface Duration {
    /**
     * The duration of the service in minutes.
     *
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     */
    minutes?: number;
}
interface StaffMember {
    /**
     * ID of the staff member providing the service, can be used to retrieve resource information using wix-bookings-backend resources API.
     * @format GUID
     * @readonly
     */
    staffMemberId?: string;
    /**
     * Name of the staff member
     * @maxLength 40
     * @readonly
     */
    name?: string | null;
    /**
     * Main media associated with the service.
     * @readonly
     */
    mainMedia?: StaffMediaItem;
}
interface StaffMediaItem extends StaffMediaItemItemOneOf {
    /** Details of the image associated with the staff, such as URL and size. */
    image?: string;
}
/** @oneof */
interface StaffMediaItemItemOneOf {
    /** Details of the image associated with the staff, such as URL and size. */
    image?: string;
}
interface StaffMemberDetails {
    /**
     * Staff members providing the service. For appointments only.
     * @maxSize 220
     */
    staffMembers?: StaffMember[];
}
interface ResourceGroup {
    /**
     * An optional resource group ID. If specified, it references a resource group in the resource groups API.
     * TODO - referenced_entity annotation
     * @format GUID
     */
    resourceGroupId?: string | null;
    /**
     * Resource IDs. Each ID references a resource in the resources API and may be a subset of resources within a resource group.
     * TODO - referenced_entity annotation
     */
    resourceIds?: ResourceIds;
    /**
     * Specifies how many resources in the group / resource IDs are required to book the service.
     * Defaults to 1.
     * @min 1
     */
    requiredResourcesNumber?: number | null;
    /**
     * If set to `true`, the customer can select the specific resources while booking the service.
     * If set to `false`, the resources required to book the service will be auto-selected at the time of the booking.
     * Defaults to false.
     * @readonly
     */
    selectableResource?: boolean | null;
}
interface ResourceIds {
    /**
     * Values of the resource IDs.
     * @maxSize 100
     * @format GUID
     */
    values?: string[];
}
interface ServiceResource extends ServiceResourceSelectionOneOf {
    /**
     * Details about the required *resource type*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     */
    resourceType?: ResourceType;
}
/** @oneof */
interface ServiceResourceSelectionOneOf {
}
interface ResourceType {
    /**
     * The type of the resource.
     * @format GUID
     */
    _id?: string | null;
    /**
     * The name of the resource type.
     * @readonly
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
}
interface Slug {
    /**
     * The unique part of service's URL that identifies the service's information page. For example, `service-1` in `https:/example.com/services/service-1`.
     * @maxLength 500
     */
    name?: string;
    /**
     * Whether the slug was generated or customized. If `true`, the slug was customized manually by the business owner. Otherwise, the slug was automatically generated from the service name.
     * @readonly
     */
    custom?: boolean | null;
    /**
     * Date and time the slug was created. This is a system field.
     * @readonly
     */
    _createdDate?: Date | null;
}
interface URLs {
    /**
     * The URL for the service page.
     * @readonly
     */
    servicePage?: string;
    /**
     * The URL for the booking entry point. It can be either to the calendar or to the service page.
     * @readonly
     */
    bookingPage?: string;
    /**
     * The URL for the calendar. Can be empty if no calendar exists.
     * @readonly
     */
    calendarPage?: string;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
/**
 * The SEO schema object contains data about different types of meta tags. It makes sure that the information about your page is presented properly to search engines.
 * The search engines use this information for ranking purposes, or to display snippets in the search results.
 * This data will override other sources of tags (for example patterns) and will be included in the <head> section of the HTML document, while not being displayed on the page itself.
 */
interface SeoSchema {
    /** SEO tag information. */
    tags?: Tag[];
    /** SEO general settings. */
    settings?: Settings;
}
interface Keyword {
    /** Keyword value. */
    term?: string;
    /** Whether the keyword is the main focus keyword. */
    isMain?: boolean;
    /**
     * The source that added the keyword terms to the SEO settings.
     * @maxLength 1000
     */
    origin?: string | null;
}
interface Tag {
    /**
     * SEO tag type.
     *
     *
     * Supported values: `title`, `meta`, `script`, `link`.
     */
    type?: string;
    /**
     * A `{"key": "value"}` pair object where each SEO tag property (`"name"`, `"content"`, `"rel"`, `"href"`) contains a value.
     * For example: `{"name": "description", "content": "the description itself"}`.
     */
    props?: Record<string, any> | null;
    /** SEO tag metadata. For example, `{"height": 300, "width": 240}`. */
    meta?: Record<string, any> | null;
    /** SEO tag inner content. For example, `<title> inner content </title>`. */
    children?: string;
    /** Whether the tag is a [custom tag](https://support.wix.com/en/article/adding-additional-meta-tags-to-your-sites-pages). */
    custom?: boolean;
    /** Whether the tag is disabled. If the tag is disabled, people can't find your page when searching for this phrase in search engines. */
    disabled?: boolean;
}
interface Settings {
    /**
     * Whether the [automatical redirect visits](https://support.wix.com/en/article/customizing-your-pages-seo-settings-in-the-seo-panel) from the old URL to the new one is enabled.
     *
     *
     * Default: `false` (automatical redirect is enabled).
     */
    preventAutoRedirect?: boolean;
    /**
     * User-selected keyword terms for a specific page.
     * @maxSize 5
     */
    keywords?: Keyword[];
}
interface AddOnGroup {
    /**
     * ID of the group.
     * @readonly
     * @format GUID
     */
    _id?: string | null;
    /**
     * The name of the group.
     * @maxLength 100
     */
    name?: string | null;
    /**
     * The maximum number of AddOns that can be selected from this group.
     * If not set, there is no upper limit.
     */
    maxNumberOfAddOns?: number | null;
    /**
     * List of AddOn IDs that are part of this group.
     * @format GUID
     * @maxSize 7
     */
    addOnIds?: string[] | null;
    /**
     * The group prompt.
     * @maxLength 200
     */
    prompt?: string | null;
}
interface AddOnDetails {
    /**
     * ID of the AddOn.
     * @format GUID
     */
    addOnId?: string | null;
    /**
     * The duration of the AddOn in minutes.
     * This field can be empty if the AddOn has no specific duration.
     */
    durationInMinutes?: number | null;
}
/**
 * Message for reindexing search data to a given search schema. Support both upsert and delete flows as well as
 * performs context manipulation with adding tenant, provided in message to callscope.
 */
interface ReindexMessage extends ReindexMessageActionOneOf {
    upsert?: Upsert;
    delete?: Delete;
    entityFqdn?: string;
    tenantId?: string;
    eventTime?: Date | null;
    entityEventSequence?: string | null;
    schema?: Schema;
}
/** @oneof */
interface ReindexMessageActionOneOf {
    upsert?: Upsert;
    delete?: Delete;
}
interface Upsert {
    entityId?: string;
    entityAsJson?: string;
}
interface Delete {
    entityId?: string;
}
interface Schema {
    label?: string;
    clusterName?: string;
}
interface SetCustomSlugEvent {
    /** The main slug for the service after the update */
    mainSlug?: Slug;
}
interface ServicesUrlsChanged {
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
interface CreateAddOnGroupRequest {
    /** AddOnGroup to create. */
    addOnGroup: AddOnGroup;
    /**
     * ID of the service to create the AddOnGroup for.
     * @format GUID
     */
    serviceId?: string | null;
}
interface CreateAddOnGroupResponse {
    /** Created AddOnGroup. */
    addOnGroup?: AddOnGroup;
}
interface DeleteAddOnGroupRequest {
    /**
     * ID of the AddOnGroup to delete.
     * @format GUID
     */
    addOnGroupId: string | null;
    /**
     * ID of the service from which to delete the AddOnGroup.
     * @format GUID
     */
    serviceId: string | null;
}
interface DeleteAddOnGroupResponse {
}
interface UpdateAddOnGroupRequest {
    /** AddOnGroup to update. */
    addOnGroup: AddOnGroup;
    /**
     * ID of the service that contains the AddOnGroup.
     * @format GUID
     */
    serviceId: string | null;
}
interface UpdateAddOnGroupResponse {
    /** Updated AddOnGroup */
    addOnGroup?: AddOnGroup;
}
interface ListAddOnGroupsByServiceIdRequest {
    /**
     * ID of the service to retrieve AddOnGroups for.
     * @format GUID
     */
    serviceId: string | null;
    /**
     * List of group ids to return. If not provided, all groups are returned.
     * @format GUID
     * @maxSize 3
     */
    groupIds?: string[] | null;
}
interface ListAddOnGroupsByServiceIdResponse {
    /**
     * List of group IDs and their linked AddOns.
     * @maxSize 3
     */
    addOnGroupsDetails?: AddOnGroupDetail[];
}
interface AddOn extends AddOnAddOnInfoOneOf {
    /** The AddOn description. */
    durationInMinutes?: number;
    /** The AddOn max quantity. */
    maxQuantity?: number;
    /**
     * The AddOn ID.
     * @format GUID
     */
    addOnId?: string | null;
    /**
     * The AddOn name.
     * @maxLength 100
     */
    name?: string | null;
    /** The AddOn price. */
    price?: Money;
}
/** @oneof */
interface AddOnAddOnInfoOneOf {
    /** The AddOn description. */
    durationInMinutes?: number;
    /** The AddOn max quantity. */
    maxQuantity?: number;
}
interface AddOnGroupDetail {
    /**
     * The group ID.
     * @format GUID
     */
    groupId?: string | null;
    /** The group max number of AddOns. */
    maxNumberOfAddOns?: number | null;
    /**
     * The group name.
     * @maxLength 100
     */
    groupName?: string | null;
    /**
     * The AddOns information linked to the group.
     * @maxSize 7
     */
    addOns?: AddOn[];
    /**
     * The group prompt.
     * @maxLength 200
     */
    prompt?: string | null;
}
interface SetAddOnsForGroupRequest {
    /**
     * The service ID to set AddOns for.
     * @format GUID
     */
    serviceId: string | null;
    /**
     * The group ID to set AddOns for.
     * @format GUID
     */
    groupId: string | null;
    /**
     * The IDs of AddOns to set.
     * @format GUID
     * @minSize 1
     * @maxSize 7
     */
    addOnIds: string[] | null;
}
interface SetAddOnsForGroupResponse {
    /** The updated AddOnGroup. */
    addOnGroup?: AddOnGroup;
}
interface CreateServiceRequest {
    /** Service to create. */
    service: Service;
}
interface CreateServiceResponse {
    /** Created service. */
    service?: Service;
}
interface ValidateServiceRequest {
    /** Service to validate. */
    service?: Service;
}
interface ValidateServiceResponse {
    /** Whether the service is valid. */
    valid?: boolean;
    /** Field violations. */
    fieldViolations?: FieldViolation[];
}
interface FieldViolation {
    /**
     * Path to the invalid field.
     * @maxLength 2000
     */
    fieldName?: string;
    /**
     * Description of the error.
     * @maxLength 2000
     */
    description?: string;
    /**
     * Rule name of the violation
     * @maxLength 2000
     */
    ruleName?: string;
}
interface BulkCreateServicesRequest {
    /**
     * Services to create.
     * @maxSize 100
     */
    services: Service[];
    /** Whether to return the created service objects. */
    returnEntity?: boolean;
}
interface BulkCreateServicesResponse {
    /** The result of each service creation. */
    results?: BulkServiceResult[];
    /** Create statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkServiceResult {
    /** Update metadata. */
    itemMetadata?: ItemMetadata;
    /** Updated service. */
    item?: Service;
}
interface ItemMetadata {
    /**
     * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).
     * @format GUID
     */
    _id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface GetServiceRequest {
    /**
     * ID of the service to retrieve.
     * @format GUID
     */
    serviceId: string;
}
declare enum V2RequestedFields {
    /** Unknown requested field. */
    UNKNOWN_REQUESTED_FIELD = "UNKNOWN_REQUESTED_FIELD",
    /** When passed, `service.staff_members` is returned. */
    STAFF_MEMBER_DETAILS = "STAFF_MEMBER_DETAILS",
    /** When passed, `service.service_resources.resource_type.name` is returned. */
    RESOURCE_TYPE_DETAILS = "RESOURCE_TYPE_DETAILS"
}
/** @enumType */
type V2RequestedFieldsWithLiterals = V2RequestedFields | 'UNKNOWN_REQUESTED_FIELD' | 'STAFF_MEMBER_DETAILS' | 'RESOURCE_TYPE_DETAILS';
interface GetServiceResponse {
    /** Retrieved service. */
    service?: Service;
}
interface GetServiceAvailabilityConstraintsRequest {
    /**
     * ID of the service to retrieve.
     * @format GUID
     */
    serviceId?: string;
}
interface GetServiceAvailabilityConstraintsResponse {
    /** The retrieved availability constraints of the service. */
    constraints?: ServiceAvailabilityConstraints;
}
interface ServiceAvailabilityConstraints {
    /**
     * The booking policy.
     * @readonly
     */
    bookingPolicy?: BookingPolicy;
    /**
     * The service schedule, including the schedule ID and availability constraints.
     * @readonly
     */
    schedule?: Schedule;
    /**
     * The locations this service is offered at.
     * Only multiple locations of type `BUSINESS` are supported. Multiple locations of type `CUSTOM` or `CUSTOMER` are not supported.
     * For courses only: Currently, only one location is supported for all location types.
     * Use the `Set Service Locations` method to change the locations this service is offered at.
     * @readonly
     * @maxSize 100
     */
    locations?: Location[];
    /**
     * Resource groups required to book the service. For backward compatibility only. Use `Service Resources` instead.
     * @maxSize 3
     * @readonly
     * @deprecated Resource groups required to book the service. For backward compatibility only. Use `Service Resources` instead.
     * @replacedBy service_resources
     * @targetRemovalDate 2024-08-19
     */
    resourceGroups?: ResourceGroup[];
    /**
     * Resource groups required to book the service.
     * @maxSize 3
     * @readonly
     */
    serviceResources?: ServiceResource[];
    /**
     * The time between available slots' start times.
     * For example, for 5-minute slots: 3:00, 3:05, 3:10 etc. For 1-hour slots: 3:00, 4:00, 5:00 etc.
     * Applied to all schedules of the site.
     * For appointments only.
     * @readonly
     */
    slotsSplitInterval?: SplitInterval;
    /**
     * Online booking settings.
     * @readonly
     */
    onlineBooking?: OnlineBooking;
}
/** The time between available slots' start times. For example, For 5 minute slots, 3:00, 3:05, 3:15 etc. For 1 hour slots, 3:00, 4:00, 5:00 etc. */
interface SplitInterval {
    /**
     * Whether the slot duration is used as the split interval value.
     * If `same_as_duration` is `true`, the `value_in_minutes` is the sum of the first duration in
     * `schedule.availabilityConstraints.SlotDurations` field, and `schedule.availabilityConstraints.TimeBetweenSlots` field.
     */
    sameAsDuration?: boolean | null;
    /** Number of minutes between available slots' start times when `same_as_duration` is `false`. */
    valueInMinutes?: number | null;
}
interface UpdateServiceRequest {
    /** Service to update. */
    service: Service;
}
interface UpdateServiceResponse {
    /** Updated service. */
    service?: Service;
}
interface BulkUpdateServicesRequest {
    /**
     * Services to update.
     * @maxSize 100
     */
    services?: MaskedService[];
    /** Whether to include the updated services in the response. Default: `false` */
    returnEntity?: boolean;
}
interface MaskedService {
    /** Service to update. */
    service?: Service;
    /** Explicit list of fields to update. */
    mask?: string[];
}
interface BulkUpdateServicesResponse {
    /** The result of each service update. */
    results?: BulkServiceResult[];
    /** Update statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkUpdateServicesByFilterRequest {
    /** Filter to identify the services to update. */
    filter: Record<string, any> | null;
    /** Service to update. */
    service: Service;
}
interface BulkUpdateServicesByFilterResponse {
    /**
     * ID of the service update job.
     *
     * Pass this ID to [Get Async Job](https://dev.wix.com/docs/rest/business-management/async-job/introduction) to retrieve job details and metadata.
     * @format GUID
     */
    jobId?: string;
}
interface DeleteServiceRequest {
    /**
     * ID of the service to delete.
     * @format GUID
     */
    serviceId: string;
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /**
     * Whether to notify participants about the change and an optional
     * custom message.
     */
    participantNotification?: ParticipantNotification;
}
interface ParticipantNotification {
    /**
     * Whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean | null;
    /**
     * Custom message to send to the participants about the changes to the booking.
     * @maxLength 2000
     */
    message?: string | null;
}
interface DeleteServiceResponse {
}
interface BulkDeleteServicesRequest {
    /**
     * IDs of the services to delete.
     * @format GUID
     * @maxSize 100
     */
    ids: string[];
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`.
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /**
     * Whether to notify participants about the change and an optional
     * custom message.
     */
    participantNotification?: ParticipantNotification;
}
interface BulkDeleteServicesResponse {
    /** The result of each service removal. */
    results?: BulkServiceResult[];
    /** Delete statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkDeleteServicesByFilterRequest {
    /** Filter to identify the services that need to be deleted. */
    filter: Record<string, any> | null;
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`.
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /** Whether to notify participants about the change and an optional custom message. */
    participantNotification?: ParticipantNotification;
}
interface BulkDeleteServicesByFilterResponse {
    /**
     * ID of the service deletion job.
     *
     * Pass this ID to [Get Async Job](https://dev.wix.com/docs/rest/business-management/async-job/introduction) to retrieve job details and metadata.
     * @format GUID
     */
    jobId?: string;
}
interface QueryServicesRequest {
    /** WQL expression. */
    query: QueryV2;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /**
     * Filter object in the following format:
     *
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     *
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`
     *
     * Read more about [supported fields and operators](https://dev.wix.com/api/rest/wix-bookings/services-v2/filtering-and-sorting).
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[ {"fieldName":"sortField1","order":"ASC"},
     * {"fieldName":"sortField2","order":"DESC"} ]`
     *
     * Read more about [sorting](https://dev.wix.com/api/rest/wix-bookings/services-v2/filtering-and-sorting#wix-bookings_services-v2_filtering-and-sorting_sorting).
     * @maxSize 50
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryServicesResponse {
    /** The retrieved services. */
    services?: Service[];
    /** Paging metadata, including offset and count. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
    /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */
    cursors?: Cursors;
}
interface Cursors {
    /**
     * Cursor token for retrieving the next page of results.
     *
     * Use this token in subsequent requests to continue pagination forward.
     * Value is `null` when on the last page of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor token for retrieving the previous page of results.
     *
     * Use this token to navigate backwards through result pages.
     * Value is `null` when on the first page of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface SearchServicesRequest {
    /**
     * Search criteria including filter, sort, aggregations, and paging options.
     *
     * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)) for complete filter and sorting options.
     */
    search: CursorSearch;
}
interface CursorSearch extends CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object for narrowing search results. For example, to return only services with specific payment options: `"filter": {"payment.options.online": true, "payment.options.in_person": false}`.
     *
     * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).
     */
    filter?: Record<string, any> | null;
    /**
     * Array of sort objects specifying result order. For example, to sort by creation date in descending order: `"sort": [{"fieldName": "createdDate", "order": "DESC"}]`.
     *
     * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).
     * @maxSize 10
     */
    sort?: Sorting[];
    /**
     * Aggregations for grouping data into categories (facets) and providing summaries for each category.
     * For example, use aggregations to categorize search results by service type, payment options, or locations.
     * @maxSize 10
     */
    aggregations?: Aggregation[];
    /** Free text to match in searchable fields. */
    search?: SearchDetails;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.
     *
     * Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).
     * @maxLength 50
     */
    timeZone?: string | null;
}
/** @oneof */
interface CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
}
interface Aggregation extends AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
    /**
     * Deprecated, use `nested` instead.
     * @deprecated Deprecated, use `nested` instead.
     * @replacedBy kind.nested
     * @targetRemovalDate 2025-01-01
     */
    groupBy?: GroupByAggregation;
}
/** @oneof */
interface AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
}
interface RangeBucket {
    /** Inclusive lower bound of the range. Required if `to` isn't specified. */
    from?: number | null;
    /** Exclusive upper bound of the range. Required if `from` isn't specified. */
    to?: number | null;
}
declare enum SortType {
    COUNT = "COUNT",
    VALUE = "VALUE"
}
/** @enumType */
type SortTypeWithLiterals = SortType | 'COUNT' | 'VALUE';
declare enum SortDirection {
    DESC = "DESC",
    ASC = "ASC"
}
/** @enumType */
type SortDirectionWithLiterals = SortDirection | 'DESC' | 'ASC';
declare enum MissingValues {
    EXCLUDE = "EXCLUDE",
    INCLUDE = "INCLUDE"
}
/** @enumType */
type MissingValuesWithLiterals = MissingValues | 'EXCLUDE' | 'INCLUDE';
interface IncludeMissingValuesOptions {
    /**
     * Custom bucket name for missing values.
     *
     * Default values:
     * - string: `N/A`
     * - int: `0`
     * - bool: `false`
     * @maxLength 20
     */
    addToBucket?: string;
}
declare enum ScalarType {
    UNKNOWN_SCALAR_TYPE = "UNKNOWN_SCALAR_TYPE",
    /** Total number of distinct values. */
    COUNT_DISTINCT = "COUNT_DISTINCT",
    /** Minimum value. */
    MIN = "MIN",
    /** Maximum value. */
    MAX = "MAX"
}
/** @enumType */
type ScalarTypeWithLiterals = ScalarType | 'UNKNOWN_SCALAR_TYPE' | 'COUNT_DISTINCT' | 'MIN' | 'MAX';
interface ValueAggregation extends ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
    /** Whether to sort by number of matches or value of the field. */
    sortType?: SortTypeWithLiterals;
    /** Whether to sort in ascending or descending order. */
    sortDirection?: SortDirectionWithLiterals;
    /**
     * Number of aggregations to return.
     *
     * Min: `1`
     * Max: `250`
     * Default: `10`
     */
    limit?: number | null;
    /**
     * Whether missing values should be included or excluded from the aggregation results.
     *
     * Default: `EXCLUDE`
     */
    missingValues?: MissingValuesWithLiterals;
}
/** @oneof */
interface ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
}
declare enum NestedAggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM"
}
/** @enumType */
type NestedAggregationTypeWithLiterals = NestedAggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM';
interface RangeAggregation {
    /**
     * List of range buckets defining the ranges for aggregation. During aggregation, each entity is placed in the first bucket where its value falls within the specified range bounds.
     * @maxSize 50
     */
    buckets?: RangeBucket[];
}
interface ScalarAggregation {
    /** Operator for the scalar aggregation, for example `COUNT_DISTINCT`, `MIN`, `MAX`. */
    type?: ScalarTypeWithLiterals;
}
interface DateHistogramAggregation {
    /** Time interval for date histogram aggregation, for example `DAY`, `HOUR`, `MONTH`. */
    interval?: IntervalWithLiterals;
}
declare enum Interval {
    /** Unknown interval. */
    UNKNOWN_INTERVAL = "UNKNOWN_INTERVAL",
    /** Yearly interval. */
    YEAR = "YEAR",
    /** Monthly interval. */
    MONTH = "MONTH",
    /** Weekly interval. */
    WEEK = "WEEK",
    /** Daily interval. */
    DAY = "DAY",
    /** Hourly interval. */
    HOUR = "HOUR",
    /** Minute interval. */
    MINUTE = "MINUTE",
    /** Second interval. */
    SECOND = "SECOND"
}
/** @enumType */
type IntervalWithLiterals = Interval | 'UNKNOWN_INTERVAL' | 'YEAR' | 'MONTH' | 'WEEK' | 'DAY' | 'HOUR' | 'MINUTE' | 'SECOND';
interface NestedAggregationItem extends NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: NestedAggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
}
declare enum AggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM",
    /** Flattened list of aggregations, where each aggregation is nested within previous 1. */
    NESTED = "NESTED"
}
/** @enumType */
type AggregationTypeWithLiterals = AggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM' | 'NESTED';
/** Nested aggregation for multi-level faceted search. Allows exploring large amounts of data through multiple levels of categorization, where each subsequent aggregation is nested within the previous aggregation to create hierarchical data summaries. */
interface NestedAggregation {
    /**
     * Flattened list of aggregations where each next aggregation is nested within the previous 1.
     * @minSize 2
     * @maxSize 10
     */
    nestedAggregations?: NestedAggregationItem[];
}
interface GroupByAggregation extends GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
}
interface SearchDetails {
    /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */
    mode?: ModeWithLiterals;
    /**
     * Search term or expression.
     * @maxLength 200
     */
    expression?: string | null;
    /**
     * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `order.address.streetName`.
     * @maxSize 10
     * @maxLength 200
     */
    fields?: string[];
    /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */
    fuzzy?: boolean;
}
declare enum Mode {
    /** At least 1 of the search terms must be present. */
    OR = "OR",
    /** All search terms must be present. */
    AND = "AND"
}
/** @enumType */
type ModeWithLiterals = Mode | 'OR' | 'AND';
interface SearchServicesResponse {
    /**
     * Retrieved services that match the search criteria specified in the request.
     *
     * Each service includes all standard service information including name, description,
     * pricing details, location options, schedule information, and payment configuration.
     */
    services?: Service[];
    /**
     * Cursor-based paging metadata for navigating search results.
     *
     * Contains the current page's cursor information, whether there are more results available,
     * and count details. Use the `next` cursor to retrieve subsequent pages of results.
     */
    pagingMetadata?: CursorPagingMetadata;
    /**
     * Aggregation results based on the aggregations specified in the search request.
     *
     * Provides categorized data summaries such as service counts by type, location distribution,
     * payment method statistics, or custom aggregations. Available only when aggregations
     * are requested in the search criteria.
     */
    aggregationData?: AggregationData;
}
interface CursorPagingMetadata {
    /**
     * Number of items returned in the current response page.
     *
     * This count reflects the actual number of items in the current result set,
     * which may be less than the requested limit if fewer items are available.
     */
    count?: number | null;
    /**
     * Navigation cursors for moving between result pages.
     *
     * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor
     * to retrieve subsequent pages and `prev` cursor to go back to previous pages.
     * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).
     */
    cursors?: Cursors;
    /**
     * Indicates whether additional results are available beyond the current page.
     *
     * - `true`: More results exist and can be retrieved using the `next` cursor.
     * - `false`: This is the final page of results.
     */
    hasNext?: boolean | null;
}
interface AggregationData {
    /**
     * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.
     * @maxSize 10000
     */
    results?: AggregationResults[];
}
interface ValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 100
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number;
}
interface RangeAggregationResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number;
}
interface NestedAggregationResults extends NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /**
     * User-defined name of aggregation, matches the one specified in request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that matches result. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
}
interface ValueResults {
    /**
     * Array of value aggregation results, each containing a field value and the count of entities with that value.
     * @maxSize 250
     */
    results?: ValueAggregationResult[];
}
interface RangeResults {
    /**
     * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.
     * @maxSize 50
     */
    results?: RangeAggregationResult[];
}
interface AggregationResultsScalarResult {
    /** Type of scalar aggregation. */
    type?: ScalarTypeWithLiterals;
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Nested aggregations. */
    nestedResults?: NestedAggregationResults;
}
interface ValueResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number | null;
}
interface RangeResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number | null;
}
interface ScalarResult {
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedResultValue extends NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
/** @oneof */
interface NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
interface Results {
    /** Map of nested aggregation results, keyed by aggregation name. */
    results?: Record<string, NestedResultValue>;
}
interface DateHistogramResult {
    /**
     * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * @maxLength 100
     */
    value?: string;
    /** Count of documents in the bucket. */
    count?: number;
}
interface GroupByValueResults {
    /**
     * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.
     * @maxSize 1000
     */
    results?: NestedValueAggregationResult[];
}
interface DateHistogramResults {
    /**
     * Array of date histogram aggregation results, each containing a date bucket and its count.
     * @maxSize 200
     */
    results?: DateHistogramResult[];
}
/**
 * Results of `NESTED` aggregation type in a flattened form.
 * Aggregations in resulting array are keyed by requested aggregation `name`.
 */
interface NestedResults {
    /**
     * Array of nested aggregation result groups, each containing multiple aggregation results.
     * @maxSize 1000
     */
    results?: Results[];
}
interface AggregationResults extends AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
    /**
     * User-defined name of aggregation as derived from search request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that must match specified kind as derived from search request. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
}
interface QueryPoliciesRequest {
    /**
     * Information about filters, paging, and sorting. See the article about
     * booking policy filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
     * for all supported filters and sorting options.
     */
    query: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 50
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface QueryPoliciesResponse {
    /** Retrieved booking policies and information about the services using them. */
    bookingPolicies?: BookingPolicyWithServices[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface BookingPolicyWithServices {
    /** Retrieved booking policy. */
    bookingPolicy?: BookingPolicy;
    /**
     * Up to 5 services connected to the booking policy. If `totalServiceCount` is
     * greater than 5, there are additional services connected to the policy.
     * You can call *Search Services*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))
     * specifying the relevant policy ID in the filter, to retrieve all services that are
     * connected to a booking policy.
     * @maxSize 5
     */
    connectedServices?: Service[];
    /** Total number of services connected to the booking policy. */
    totalServiceCount?: number;
}
interface QueryBookingFormsRequest {
    /**
     * Information about filters, paging, and sorting. See the article about
     * form filters ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object))
     * for all supported filters and sorting options.
     */
    query: CursorQuery;
    /**
     * Conditional fields to return.
     * @maxSize 1
     */
    conditionalFields?: RequestedFieldsWithLiterals[];
}
declare enum RequestedFields {
    /** Unknown requested conditional field. */
    UNKNOWN_REQUESTED_FIELD = "UNKNOWN_REQUESTED_FIELD",
    /** Whether to return the site's default booking form. */
    DEFAULT_BOOKING_FORM = "DEFAULT_BOOKING_FORM"
}
/** @enumType */
type RequestedFieldsWithLiterals = RequestedFields | 'UNKNOWN_REQUESTED_FIELD' | 'DEFAULT_BOOKING_FORM';
interface QueryBookingFormsResponse {
    /** Retrieved booking forms and information about connected services. */
    bookingForms?: BookingForm[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
    /** The site's default booking form and information about connected services. */
    defaultBookingForm?: BookingForm;
}
interface BookingForm {
    /** Information about the retrieved booking form. */
    formDetails?: FormDetails;
    /**
     * Up to 5 services connected to the booking form. If `totalServiceCount` is
     * greater than 5, there are additional services connected to the policy.
     * You can call *Search Services*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))
     * specifying the relevant policy ID in the filter, to retrieve all services that are
     * connected to a booking policy.
     * @maxSize 5
     */
    connectedServices?: ConnectedService[];
    /** Total number of services connected to the booking form. */
    totalServiceCount?: number;
}
interface FormDetails {
    /**
     * Form ID.
     * @format GUID
     */
    formId?: string;
    /**
     * Form name.
     * @maxLength 200
     */
    name?: string | null;
    /**
     * Revision number, which increments by 1 each time the form is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the form.
     * @readonly
     */
    revision?: string | null;
}
interface ConnectedService {
    /**
     * ID of the service connected to the booking form.
     * @format GUID
     */
    _id?: string | null;
    /**
     * Name of the service connected to the booking form.
     * @maxLength 400
     * @minLength 1
     */
    name?: string | null;
}
interface CountServicesRequest {
    /**
     * Query filter to base the count on. See supported filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     */
    filter?: Record<string, any> | null;
}
interface CountServicesResponse {
    /** Number of services matching the specified filter. */
    count?: number;
}
interface QueryLocationsRequest {
    /** Filter. */
    filter?: QueryLocationsFilter;
}
interface QueryLocationsFilter {
    /**
     * Service filter. See supported filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     */
    services?: Record<string, any> | null;
    /**
     * List of business IDs to filter by.
     * @format GUID
     * @maxSize 100
     */
    businessLocationIds?: string[];
}
interface QueryLocationsResponse {
    /**
     * Retrieved business locations and whether each location is connected to at
     * least one service.
     */
    businessLocations?: BusinessLocations;
    /**
     * Retrieved custom locations and whether each location is connected to at
     * least one service.
     */
    customLocations?: CustomLocations;
    /**
     * Retrieved customer locations and whether each location is connected to at
     * least one service.
     */
    customerLocations?: CustomerLocations;
}
interface BusinessLocations {
    /**
     * Whether at least one service matching the filter is connected to any of the
     * retrieved business locations.
     */
    exists?: boolean;
    /**
     * Retrieved business locations.
     * @maxSize 100
     */
    locations?: Location[];
}
interface CustomLocations {
    /**
     * Whether at least one service matching the filter is connected to any of the
     * retrieved custom locations.
     */
    exists?: boolean;
}
interface CustomerLocations {
    /**
     * Whether at least one service matching the filter is connected to any of the
     * retrieved customer locations.
     */
    exists?: boolean;
}
interface QueryCategoriesRequest {
    /** Filter. */
    filter?: QueryCategoriesFilter;
}
interface QueryCategoriesFilter {
    /**
     * Service filter. See supported filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     */
    services?: Record<string, any> | null;
    /**
     * List of category IDs to filter by.
     * @format GUID
     * @maxSize 100
     */
    categoryIds?: string[];
}
interface QueryCategoriesResponse {
    /** Retrieved categories. */
    categories?: V2Category[];
}
interface QueryServicesMultiLanguageRequest {
    /** WQL expression. */
    query?: QueryV2;
}
interface QueryServicesMultiLanguageResponse {
    /** The retrieved services in the main language */
    services?: Service[];
    /**
     * the retrieved services in the requested language according to the
     * provided linguist aspect
     */
    translatedServices?: Service[];
    /** Paging metadata, including offset and count. */
    pagingMetadata?: PagingMetadataV2;
}
interface SetServiceLocationsRequest {
    /**
     * ID of the service.
     * @format GUID
     */
    serviceId: string;
    /**
     * List of locations replacing existing service locations. Omitting an existing location removes it from the service. Specify `removedLocationSessionsAction` to determine the handling of future sessions scheduled at that location.
     * @maxSize 100
     */
    locations: Location[];
    /**
     * The action to perform on sessions currently set to a removed location. For
     * example, move existing sessions to a new specified location.
     * If not specified, sessions will not be moved to a new location.
     */
    removedLocationSessionsAction?: RemovedLocationSessionsAction;
    /**
     * Whether to notify participants about the change of location, and an
     * Optional custom message. The notification is sent only to participants of sessions that are affected by the change.
     */
    participantNotification?: ParticipantNotification;
}
interface RemovedLocationSessionsAction extends RemovedLocationSessionsActionActionOptionsOneOf {
    /**
     * Details about the new location of future events that were scheduled to take
     * place at a removed location.
     */
    moveToLocationOptions?: MoveToNewLocationsOptions;
    /** Information about what to do with future events at the removed location. */
    action?: ActionWithLiterals;
}
/** @oneof */
interface RemovedLocationSessionsActionActionOptionsOneOf {
    /**
     * Details about the new location of future events that were scheduled to take
     * place at a removed location.
     */
    moveToLocationOptions?: MoveToNewLocationsOptions;
}
declare enum Action {
    UNKNOWN_ACTION_TYPE = "UNKNOWN_ACTION_TYPE",
    /** Retain all future sessions at their current location. This is the default. */
    KEEP_AT_CURRENT_LOCATION = "KEEP_AT_CURRENT_LOCATION",
    /** Move future events to a new location. */
    MOVE_TO_LOCATION = "MOVE_TO_LOCATION",
    /**
     * Cancel all future events at the removed location.
     * Currently not supported.
     */
    DELETE = "DELETE"
}
/** @enumType */
type ActionWithLiterals = Action | 'UNKNOWN_ACTION_TYPE' | 'KEEP_AT_CURRENT_LOCATION' | 'MOVE_TO_LOCATION' | 'DELETE';
interface MoveToNewLocationsOptions {
    /** The new location to move existing sessions currently set to a removed location, used when `action` is `MOVE_TO_LOCATION`. */
    newLocation?: Location;
}
interface SetServiceLocationsResponse {
    /** The updated service with the newly set locations. */
    service?: Service;
}
interface EnablePricingPlansForServiceRequest {
    /**
     * ID of the service to update.
     * @format GUID
     */
    serviceId: string;
    /**
     * IDs of the *pricing plans*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * to add to the service's `payment.pricingPlanIds` array.
     * @format GUID
     * @maxSize 100
     */
    pricingPlanIds: string[];
}
interface EnablePricingPlansForServiceResponse {
    /** Updated service. */
    service?: Service;
}
interface InvalidPricingPlan {
    /**
     * ID of the invalid pricing plan.
     * @format GUID
     */
    _id?: string;
    /**
     * Explanation why the pricing plan is considered invalid.
     * @maxLength 2000
     */
    message?: string;
}
interface DisablePricingPlansForServiceRequest {
    /**
     * ID of the service to update.
     * @format GUID
     */
    serviceId: string;
    /**
     * IDs of the *pricing plans*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * to remove from the service's `payment.pricingPlanIds` array.
     * @format GUID
     * @maxSize 75
     */
    pricingPlanIds?: string[];
}
interface DisablePricingPlansForServiceResponse {
    /** Updated service. */
    service?: Service;
}
interface SetCustomSlugRequest {
    /**
     * ID of the service for which to update the active slug.
     * @format GUID
     */
    serviceId: string;
    /**
     * Slug to set as the active service slug.
     * @maxLength 500
     */
    slug?: string;
}
interface SetCustomSlugResponse {
    /** Updated active service slug. */
    slug?: Slug;
    /** Updated service. */
    service?: Service;
}
interface ValidateSlugRequest {
    /**
     * IO of the service to check custom slug validity for.
     * @format GUID
     */
    serviceId: string;
    /**
     * Custom slug to validate.
     * @maxLength 500
     */
    slug?: string;
}
interface ValidateSlugResponse {
    /** Whether the slug is valid. */
    valid?: boolean;
    /**
     * Valid slug. Available only if `{"valid": true}`.
     * @maxLength 500
     */
    slug?: string | null;
    /**
     * Reasons why the slug is invalid. Available only if `{"valid": false}`.
     * @maxSize 3
     */
    errors?: InvalidSlugErrorWithLiterals[];
}
declare enum InvalidSlugError {
    /** Unknown slug error. */
    UNKNOWN_SLUG_ERROR = "UNKNOWN_SLUG_ERROR",
    /** Slug contains illegal characters. */
    SLUG_CONTAINS_ILLEGAL_CHARACTERS = "SLUG_CONTAINS_ILLEGAL_CHARACTERS",
    /** Slug is already associated with another service. */
    SLUG_ALREADY_EXISTS = "SLUG_ALREADY_EXISTS"
}
/** @enumType */
type InvalidSlugErrorWithLiterals = InvalidSlugError | 'UNKNOWN_SLUG_ERROR' | 'SLUG_CONTAINS_ILLEGAL_CHARACTERS' | 'SLUG_ALREADY_EXISTS';
interface CloneServiceRequest {
    /**
     * ID of the service to clone.
     * @format GUID
     */
    sourceServiceId: string;
}
interface CloneServiceResponse {
    /** Cloned service. */
    service?: Service;
    /**
     * Information about connected entities that couldn't be cloned. For example,
     * future recurring events, the booking form, service variants, and connected
     * pricing plans.
     */
    errors?: CloneErrorsWithLiterals[];
}
declare enum CloneErrors {
    /**
     * Failed to clone the original service's *options and variants*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     */
    OPTIONS_AND_VARIANTS = "OPTIONS_AND_VARIANTS",
    /** Failed to clone the original service's *booking form*. */
    FORM = "FORM"
}
/** @enumType */
type CloneErrorsWithLiterals = CloneErrors | 'OPTIONS_AND_VARIANTS' | 'FORM';
/** An event sent every time a category entity is changed. */
interface CategoryNotification {
    category?: Category;
    event?: CategoryNotificationEventWithLiterals;
}
/** Categories are used to group multiple services together. A service must be associated with a category in order to be exposed in the Wix Bookings UI. */
interface Category {
    /**
     * Category ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Category name.
     * @maxLength 500
     */
    name?: string | null;
    /**
     * Category status.
     *
     * Default: `CREATED`
     * @readonly
     */
    status?: StatusWithLiterals;
    /** Sort order of the category in the live site and dashboard. */
    sortOrder?: number | null;
}
declare enum Status {
    /** The category was created. */
    CREATED = "CREATED",
    /** The category was deleted. */
    DELETED = "DELETED"
}
/** @enumType */
type StatusWithLiterals = Status | 'CREATED' | 'DELETED';
declare enum CategoryNotificationEvent {
    /** Category was updated. */
    Updated = "Updated",
    /** Category was deleted. */
    Deleted = "Deleted",
    /** Category was created. */
    Created = "Created"
}
/** @enumType */
type CategoryNotificationEventWithLiterals = CategoryNotificationEvent | 'Updated' | 'Deleted' | 'Created';
interface Empty {
}
interface BenefitNotification {
    /**
     * Plan unique ID
     * @format GUID
     */
    planId?: string;
    /**
     * App def ID
     * @format GUID
     */
    appDefId?: string;
    /** Current benefit details */
    benefit?: Benefit;
    /** Previous benefit */
    prevBenefit?: Benefit;
    /** Notification event */
    event?: EventWithLiterals;
}
interface Benefit {
    /**
     * Benefit unique ID
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /** Benefit Type */
    benefitType?: BenefitTypeWithLiterals;
    /**
     * Resource IDs that serves by this benefit
     * @format GUID
     */
    resourceIds?: string[];
    /** Amount of credits that provided by this benefit */
    creditAmount?: number | null;
    /**
     * additional details related to benefit; limited to 20 entries, 20 symbols for key and 20 symbols for value
     * @maxSize 20
     */
    customFields?: Record<string, string>;
    /** return value only in case it required in the ListRequest, true means that benefit's type could be updated */
    editable?: boolean | null;
    /** Benefit behavior */
    behavior?: Behavior;
    /**
     * Id of the app associated with this benefit
     * @format GUID
     * @readonly
     */
    appDefId?: string | null;
}
interface EntryPass {
}
interface Discount extends DiscountDiscountOneOf {
    /**
     * Fixed-rate percent off discount
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentOffRate?: string;
    /**
     * Absolute amount discount
     * @decimalValue options { gt:0, maxScale:2 }
     */
    moneyOffAmount?: string;
}
/** @oneof */
interface DiscountDiscountOneOf {
    /**
     * Fixed-rate percent off discount
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentOffRate?: string;
    /**
     * Absolute amount discount
     * @decimalValue options { gt:0, maxScale:2 }
     */
    moneyOffAmount?: string;
}
declare enum BenefitType {
    /** Should never be used */
    UNDEFINED = "UNDEFINED",
    /** Limited benefit type */
    LIMITED = "LIMITED",
    /** Unlimited benefit type */
    UNLIMITED = "UNLIMITED"
}
/** @enumType */
type BenefitTypeWithLiterals = BenefitType | 'UNDEFINED' | 'LIMITED' | 'UNLIMITED';
interface Behavior extends BehaviorBehaviorOneOf {
    /** Entry pass for resources, e.g. a ticket for Bookings service or a ticket for Events. */
    defaultBehavior?: EntryPass;
    /** Discount applied to paid resources */
    discount?: Discount;
}
/** @oneof */
interface BehaviorBehaviorOneOf {
    /** Entry pass for resources, e.g. a ticket for Bookings service or a ticket for Events. */
    defaultBehavior?: EntryPass;
    /** Discount applied to paid resources */
    discount?: Discount;
}
declare enum Event {
    Updated = "Updated",
    Deleted = "Deleted",
    Created = "Created"
}
/** @enumType */
type EventWithLiterals = Event | 'Updated' | 'Deleted' | 'Created';
interface UserDomainInfoChangedEvent {
    domainName?: string;
    crudType?: CrudTypeWithLiterals;
    /** @format GUID */
    metaSiteId?: string | null;
    changeTime?: Date | null;
}
declare enum CrudType {
    INVALID_CRUD_TYPE = "INVALID_CRUD_TYPE",
    CREATE = "CREATE",
    UPDATE = "UPDATE",
    DELETE = "DELETE",
    /** Unfortunately this action is used by hibernate save in wix-war */
    CREATE_OR_UPDATE = "CREATE_OR_UPDATE"
}
/** @enumType */
type CrudTypeWithLiterals = CrudType | 'INVALID_CRUD_TYPE' | 'CREATE' | 'UPDATE' | 'DELETE' | 'CREATE_OR_UPDATE';
interface HtmlSitePublished {
    /**
     * Application instance ID
     * @maxLength 50
     */
    appInstanceId?: string;
    /**
     * Application type
     * @maxLength 100
     */
    appType?: string;
    /** Revision */
    revision?: string;
    /**
     * MSID
     * @maxLength 100
     */
    metaSiteId?: string | null;
    /**
     * optional branch id if publish is done from branch
     * @format GUID
     */
    branchId?: string | null;
    /** The site's last transactionId */
    lastTransactionId?: string | null;
    /** A list of the site's pages */
    pages?: Page[];
    /** Site's publish date */
    publishDate?: string;
}
interface Page {
    /**
     * Page's Id
     * @maxLength 100
     */
    _id?: string;
}
/** Encapsulates all details written to the Greyhound topic when a site's properties are updated. */
interface SitePropertiesNotification {
    /** The site ID for which this update notification applies. */
    metasiteId?: string;
    /** The actual update event. */
    event?: SitePropertiesEvent;
    /**
     * A convenience set of mappings from the MetaSite ID to its constituent services.
     * @maxSize 500
     */
    translations?: Translation[];
    /** Context of the notification */
    changeContext?: ChangeContext;
}
/** The actual update event for a particular notification. */
interface SitePropertiesEvent {
    /** Version of the site's properties represented by this update. */
    version?: number;
    /** Set of properties that were updated - corresponds to the fields in "properties". */
    fields?: string[];
    /** Updated properties. */
    properties?: Properties;
}
interface Properties {
    /** Site categories. */
    categories?: Categories;
    /** Site locale. */
    locale?: Locale;
    /**
     * Site language.
     *
     * Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format.
     */
    language?: string | null;
    /**
     * Site currency format used to bill customers.
     *
     * Three-letter currency code in [ISO-4217 alphabetic](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) format.
     */
    paymentCurrency?: string | null;
    /** Timezone in `America/New_York` format. */
    timeZone?: string | null;
    /** Email address. */
    email?: string | null;
    /** Phone number. */
    phone?: string | null;
    /** Fax number. */
    fax?: string | null;
    /** Address. */
    address?: Address;
    /** Site display name. */
    siteDisplayName?: string | null;
    /** Business name. */
    businessName?: string | null;
    /** Path to the site's logo in Wix Media (without Wix Media base URL). */
    logo?: string | null;
    /** Site description. */
    description?: string | null;
    /**
     * Business schedule. Regular and exceptional time periods when the business is open or the service is available.
     *
     * __Note:__ Not supported by Wix Bookings.
     */
    businessSchedule?: BusinessSchedule;
    /** Supported languages of a site and the primary language. */
    multilingual?: Multilingual;
    /** Cookie policy the Wix user defined for their site (before the site visitor interacts with/limits it). */
    consentPolicy?: ConsentPolicy;
    /**
     * Supported values: `FITNESS SERVICE`, `RESTAURANT`, `BLOG`, `STORE`, `EVENT`, `UNKNOWN`.
     *
     * Site business type.
     */
    businessConfig?: string | null;
    /** External site URL that uses Wix as its headless business solution. */
    externalSiteUrl?: string | null;
    /** Track clicks analytics. */
    trackClicksAnalytics?: boolean;
}
interface Categories {
    /** Primary site category. */
    primary?: string;
    /**
     * Secondary site category.
     * @maxSize 50
     */
    secondary?: string[];
    /** Business Term Id */
    businessTermId?: string | null;
}
interface Locale {
    /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */
    languageCode?: string;
    /** Two-letter country code in [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements) format. */
    country?: string;
}
interface Address {
    /** Street name. */
    street?: string;
    /** City name. */
    city?: string;
    /** Two-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format. */
    country?: string;
    /** State. */
    state?: string;
    /**
     * Zip or postal code.
     * @maxLength 20
     */
    zip?: string;
    /** Extra information to be displayed in the address. */
    hint?: AddressHint;
    /** Whether this address represents a physical location. */
    isPhysical?: boolean;
    /** Google-formatted version of this address. */
    googleFormattedAddress?: string;
    /** Street number. */
    streetNumber?: string;
    /** Apartment number. */
    apartmentNumber?: string;
    /** Geographic coordinates of location. */
    coordinates?: GeoCoordinates;
}
/**
 * Extra information on displayed addresses.
 * This is used for display purposes. Used to add additional data about the address, such as "In the passage".
 * Free text. In addition, the user can state where to display the additional description - before, after, or instead of the address string.
 */
interface AddressHint {
    /** Extra text displayed next to, or instead of, the actual address. */
    text?: string;
    /** Where the extra text should be displayed. */
    placement?: PlacementTypeWithLiterals;
}
/** Where the extra text should be displayed: before, after or instead of the actual address. */
declare enum PlacementType {
    BEFORE = "BEFORE",
    AFTER = "AFTER",
    REPLACE = "REPLACE"
}
/** @enumType */
type PlacementTypeWithLiterals = PlacementType | 'BEFORE' | 'AFTER' | 'REPLACE';
/** Geocoordinates for a particular address. */
interface GeoCoordinates {
    /** Latitude of the location. Must be between -90 and 90. */
    latitude?: number;
    /** Longitude of the location. Must be between -180 and 180. */
    longitude?: number;
}
/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */
interface BusinessSchedule {
    /**
     * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.
     * @maxSize 100
     */
    periods?: TimePeriod[];
    /**
     * Exceptions to the business's regular hours. The business can be open or closed during the exception.
     * @maxSize 100
     */
    specialHourPeriod?: SpecialHourPeriod[];
}
/** Weekly recurring time periods when the business is regularly open or the service is available. */
interface TimePeriod {
    /** Day of the week the period starts on. */
    openDay?: DayOfWeekWithLiterals;
    /**
     * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     */
    openTime?: string;
    /** Day of the week the period ends on. */
    closeDay?: DayOfWeekWithLiterals;
    /**
     * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     *
     * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.
     */
    closeTime?: string;
}
/** Enumerates the days of the week. */
declare enum DayOfWeek {
    MONDAY = "MONDAY",
    TUESDAY = "TUESDAY",
    WEDNESDAY = "WEDNESDAY",
    THURSDAY = "THURSDAY",
    FRIDAY = "FRIDAY",
    SATURDAY = "SATURDAY",
    SUNDAY = "SUNDAY"
}
/** @enumType */
type DayOfWeekWithLiterals = DayOfWeek | 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY';
/** Exception to the business's regular hours. The business can be open or closed during the exception. */
interface SpecialHourPeriod {
    /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    startDate?: string;
    /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    endDate?: string;
    /**
     * Whether the business is closed (or the service is not available) during the exception.
     *
     * Default: `true`.
     */
    isClosed?: boolean;
    /** Additional info about the exception. For example, "We close earlier on New Year's Eve." */
    comment?: string;
}
interface Multilingual {
    /**
     * Supported languages list.
     * @maxSize 200
     */
    supportedLanguages?: SupportedLanguage[];
    /** Whether to redirect to user language. */
    autoRedirect?: boolean;
}
interface SupportedLanguage {
    /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */
    languageCode?: string;
    /** Locale. */
    locale?: Locale;
    /** Whether the supported language is the primary language for the site. */
    isPrimary?: boolean;
    /** Language icon. */
    countryCode?: string;
    /** How the language will be resolved. For internal use. */
    resolutionMethod?: ResolutionMethodWithLiterals;
    /** Whether the supported language is the primary language for site visitors. */
    isVisitorPrimary?: boolean | null;
}
declare enum ResolutionMethod {
    QUERY_PARAM = "QUERY_PARAM",
    SUBDOMAIN = "SUBDOMAIN",
    SUBDIRECTORY = "SUBDIRECTORY"
}
/** @enumType */
type ResolutionMethodWithLiterals = ResolutionMethod | 'QUERY_PARAM' | 'SUBDOMAIN' | 'SUBDIRECTORY';
interface ConsentPolicy {
    /** Whether the site uses cookies that are essential to site operation. Always `true`. */
    essential?: boolean | null;
    /** Whether the site uses cookies that affect site performance and other functional measurements. */
    functional?: boolean | null;
    /** Whether the site uses cookies that collect analytics about how the site is used (in order to improve it). */
    analytics?: boolean | null;
    /** Whether the site uses cookies that collect information allowing better customization of the experience for a current visitor. */
    advertising?: boolean | null;
    /** CCPA compliance flag. */
    dataToThirdParty?: boolean | null;
}
/** A single mapping from the MetaSite ID to a particular service. */
interface Translation {
    /** The service type. */
    serviceType?: string;
    /** The application definition ID; this only applies to services of type ThirdPartyApps. */
    appDefId?: string;
    /** The instance ID of the service. */
    instanceId?: string;
}
interface ChangeContext extends ChangeContextPayloadOneOf {
    /** Properties were updated. */
    propertiesChange?: PropertiesChange;
    /** Default properties were created on site creation. */
    siteCreated?: SiteCreated;
    /** Properties were cloned on site cloning. */
    siteCloned?: SiteCloned;
}
/** @oneof */
interface ChangeContextPayloadOneOf {
    /** Properties were updated. */
    propertiesChange?: PropertiesChange;
    /** Default properties were created on site creation. */
    siteCreated?: SiteCreated;
    /** Properties were cloned on site cloning. */
    siteCloned?: SiteCloned;
}
interface PropertiesChange {
}
interface SiteCreated {
    /** Origin template site id. */
    originTemplateId?: string | null;
}
interface SiteCloned {
    /** Origin site id. */
    originMetaSiteId?: string;
}
/** @docsIgnore */
type DeleteAddOnGroupApplicationErrors = {
    code?: 'GROUP_NOT_ON_SERVICE';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type UpdateAddOnGroupApplicationErrors = {
    code?: 'GROUP_NOT_ON_SERVICE';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type SetAddOnsForGroupApplicationErrors = {
    code?: 'ADD_ON_GROUP_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type CreateServiceValidationErrors = {
    ruleName?: 'INVALID_FORM';
} | {
    ruleName?: 'INVALID_CATEGORY';
} | {
    ruleName?: 'INVALID_BOOKING_POLICY';
} | {
    ruleName?: 'INVALID_SERVICE_TYPE';
} | {
    ruleName?: 'INVALID_SERVICE_NAME';
} | {
    ruleName?: 'INVALID_ONLINE_BOOKING';
} | {
    ruleName?: 'INVALID_STAFF_MEMBER_IDS';
} | {
    ruleName?: 'PAYMENT_REQUIRED';
} | {
    ruleName?: 'INVALID_PAYMENT_TYPE';
} | {
    ruleName?: 'INVALID_RATE';
} | {
    ruleName?: 'INVALID_PAYMENT_OPTIONS';
} | {
    ruleName?: 'INVALID_BUSINESS_LOCATIONS';
} | {
    ruleName?: 'INVALID_LOCATIONS';
} | {
    ruleName?: 'INVALID_BUSINESS_LOCATION';
} | {
    ruleName?: 'INVALID_CUSTOM_LOCATION';
} | {
    ruleName?: 'INVALID_CUSTOMER_LOCATION';
} | {
    ruleName?: 'INVALID_UNKNOWN_LOCATION';
} | {
    ruleName?: 'INVALID_MANUAL_APPROVAL_WITH_PRICING_PLANS';
} | {
    ruleName?: 'INVALID_DEFAULT_CAPACITY';
} | {
    ruleName?: 'INVALID_APPOINTMENT_CAPACITY';
} | {
    ruleName?: 'INVALID_SESSION_DURATION';
};
/** @docsIgnore */
type UpdateServiceValidationErrors = {
    ruleName?: 'INVALID_FORM';
} | {
    ruleName?: 'INVALID_CATEGORY';
} | {
    ruleName?: 'INVALID_BOOKING_POLICY';
} | {
    ruleName?: 'INVALID_SERVICE_TYPE';
} | {
    ruleName?: 'INVALID_SERVICE_NAME';
} | {
    ruleName?: 'INVALID_ONLINE_BOOKING';
} | {
    ruleName?: 'INVALID_STAFF_MEMBER_IDS';
} | {
    ruleName?: 'PAYMENT_REQUIRED';
} | {
    ruleName?: 'INVALID_PAYMENT_TYPE';
} | {
    ruleName?: 'INVALID_RATE';
} | {
    ruleName?: 'INVALID_PAYMENT_OPTIONS';
} | {
    ruleName?: 'INVALID_BUSINESS_LOCATIONS';
} | {
    ruleName?: 'INVALID_LOCATIONS';
} | {
    ruleName?: 'INVALID_BUSINESS_LOCATION';
} | {
    ruleName?: 'INVALID_CUSTOM_LOCATION';
} | {
    ruleName?: 'INVALID_CUSTOMER_LOCATION';
} | {
    ruleName?: 'INVALID_UNKNOWN_LOCATION';
} | {
    ruleName?: 'INVALID_MANUAL_APPROVAL_WITH_PRICING_PLANS';
} | {
    ruleName?: 'INVALID_DEFAULT_CAPACITY';
} | {
    ruleName?: 'INVALID_APPOINTMENT_CAPACITY';
} | {
    ruleName?: 'INVALID_SESSION_DURATION';
};
/** @docsIgnore */
type QueryBookingFormsApplicationErrors = {
    code?: 'DEFAULT_BOOKING_FORM_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type EnablePricingPlansForServiceApplicationErrors = {
    code?: 'INVALID_PRICING_PLAN';
    description?: string;
    data?: InvalidPricingPlan;
} | {
    code?: 'SERVICE_DOES_NOT_SUPPORT_PRICING_PLANS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type DisablePricingPlansForServiceApplicationErrors = {
    code?: 'INVALID_PRICING_PLAN';
    description?: string;
    data?: InvalidPricingPlan;
};
/** @docsIgnore */
type SetCustomSlugApplicationErrors = {
    code?: 'SLUG_ALREADY_EXISTS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type SetCustomSlugValidationErrors = {
    ruleName?: 'SLUG_CONTAINS_ILLEGAL_CHARACTERS';
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface ServiceCreatedEnvelope {
    entity: Service;
    metadata: EventMetadata;
}
/**
 * Triggered when a service is created.
 * @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.SERVICES_READ
 * @webhook
 * @eventType wix.bookings.services.v2.service_created
 * @slug created
 */
declare function onServiceCreated(handler: (event: ServiceCreatedEnvelope) => void | Promise<void>): void;
interface ServiceDeletedEnvelope {
    entity: Service;
    metadata: EventMetadata;
}
/**
 * Triggered when a service is deleted.
 * @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.SERVICES_READ
 * @webhook
 * @eventType wix.bookings.services.v2.service_deleted
 * @slug deleted
 */
declare function onServiceDeleted(handler: (event: ServiceDeletedEnvelope) => void | Promise<void>): void;
interface ServiceUpdatedEnvelope {
    entity: Service;
    metadata: EventMetadata;
}
/**
 * Triggered when a service is updated.
 * @permissionScope Manage Stores
 * @permissionScopeId SCOPE.STORES.MANAGE-STORES
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.SERVICES_READ
 * @webhook
 * @eventType wix.bookings.services.v2.service_updated
 * @slug updated
 */
declare function onServiceUpdated(handler: (event: ServiceUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Create a new AddOns group.
 * An AddOns group defines a collection of AddOns that can be linked to a Service,
 * with constraints such as minimum and maximum selections.
 * @param addOnGroup - AddOnGroup to create.
 * @public
 * @documentationMaturity preview
 * @requiredField addOnGroup
 * @requiredField addOnGroup.name
 * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUP_CREATE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.AddOnGroupsService.CreateAddOnGroup
 */
declare function createAddOnGroup(addOnGroup: NonNullablePaths<AddOnGroup, `name`, 2>, options?: CreateAddOnGroupOptions): Promise<NonNullablePaths<CreateAddOnGroupResponse, `addOnGroup.addOnIds`, 3>>;
interface CreateAddOnGroupOptions {
    /**
     * ID of the service to create the AddOnGroup for.
     * @format GUID
     */
    serviceId?: string | null;
}
/**
 * Delete an existing AddOns group.
 * This will remove the group and unlink all associated AddOns.
 * @param addOnGroupId - ID of the AddOnGroup to delete.
 * @public
 * @documentationMaturity preview
 * @requiredField addOnGroupId
 * @requiredField options
 * @requiredField options.serviceId
 * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUP_CREATE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.AddOnGroupsService.DeleteAddOnGroup
 */
declare function deleteAddOnGroup(addOnGroupId: string, options: NonNullablePaths<DeleteAddOnGroupOptions, `serviceId`, 2>): Promise<void & {
    __applicationErrorsType?: DeleteAddOnGroupApplicationErrors;
}>;
interface DeleteAddOnGroupOptions {
    /**
     * ID of the service from which to delete the AddOnGroup.
     * @format GUID
     */
    serviceId: string | null;
}
/**
 * Update an existing AddOns group.
 * This allows modifying group settings such as its name, prompt, constraints, or associated AddOns.
 * @param addOnGroup - AddOnGroup to update.
 * @public
 * @documentationMaturity preview
 * @requiredField addOnGroup
 * @requiredField addOnGroup._id
 * @requiredField options
 * @requiredField options.serviceId
 * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUP_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.AddOnGroupsService.UpdateAddOnGroup
 */
declare function updateAddOnGroup(addOnGroup: NonNullablePaths<AddOnGroup, `_id`, 2>, options: NonNullablePaths<UpdateAddOnGroupOptions, `serviceId`, 2>): Promise<NonNullablePaths<UpdateAddOnGroupResponse, `addOnGroup.addOnIds`, 3> & {
    __applicationErrorsType?: UpdateAddOnGroupApplicationErrors;
}>;
interface UpdateAddOnGroupOptions {
    /**
     * ID of the service that contains the AddOnGroup.
     * @format GUID
     */
    serviceId: string | null;
}
/**
 * Retrieves a list of AddOnGroups including enriched AddOn details in the correct order.
 * If the group_id is specified, only the AddOns for the specified group will be returned,
 * otherwise all groups will be returned.
 * @param serviceId - ID of the service to retrieve AddOnGroups for.
 * @public
 * @documentationMaturity preview
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUPS_LIST
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.AddOnGroupsService.ListAddOnGroupsByServiceId
 */
declare function listAddOnGroupsByServiceId(serviceId: string, options?: ListAddOnGroupsByServiceIdOptions): Promise<NonNullablePaths<ListAddOnGroupsByServiceIdResponse, `addOnGroupsDetails`, 2>>;
interface ListAddOnGroupsByServiceIdOptions {
    /**
     * List of group ids to return. If not provided, all groups are returned.
     * @format GUID
     * @maxSize 3
     */
    groupIds?: string[] | null;
}
/**
 * Sets the AddOns for a specific group.
 * The order of the AddOns in the list will be used to determine their display order.
 * @param serviceId - The service ID to set AddOns for.
 * @public
 * @documentationMaturity preview
 * @requiredField options
 * @requiredField options.addOnIds
 * @requiredField options.groupId
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUPS_SET_ADD_ONS
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.AddOnGroupsService.SetAddOnsForGroup
 */
declare function setAddOnsForGroup(serviceId: string, options: NonNullablePaths<SetAddOnsForGroupOptions, `addOnIds` | `groupId`, 2>): Promise<NonNullablePaths<SetAddOnsForGroupResponse, `addOnGroup.addOnIds`, 3> & {
    __applicationErrorsType?: SetAddOnsForGroupApplicationErrors;
}>;
interface SetAddOnsForGroupOptions {
    /**
     * The group ID to set AddOns for.
     * @format GUID
     */
    groupId: string | null;
    /**
     * The IDs of AddOns to set.
     * @format GUID
     * @minSize 1
     * @maxSize 7
     */
    addOnIds: string[] | null;
}
/**
 * Creates a service.
 *
 *
 * ## Required fields
 *
 * When creating a service you must specify the following fields:
 * - `type`
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)):
 * Whether it's an appointment-based service, class, or course.
 * - `name`: Service name that's displayed to customers.
 * - `onlineBooking`:
 * Settings determining whether customers can book online, whether the business
 * must manually confirm bookings, and whether customers can request to book an
 * appointment time slot that already has a booking request awaiting business
 * confirmation.
 * - `payment`
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)):
 * How customers can pay when signing up for the service.
 *
 * ### Session durations
 *
 * Depending on which type of service you're creating, you may also need to specify
 * supported session durations.
 *
 * __Classes and courses__
 *
 * Don't specify `schedule.availabilityConstraints.sessionDurations`.
 *
 * __Appointment-based services without varied pricing based on session length__
 *
 * Specify the single supported session duration in the
 * `schedule.availabilityConstraints.sessionDurations` array.
 *
 * __Appointment-based services with varied pricing based on session length__
 *
 * - Specify all supported session durations in `schedule.availabilityConstraints.sessionDurations`.
 * - Note that Wix Bookings doesn't display these values to customers and
 * ignores them in pricing and availability calculations. Instead session
 * durations are retrieved from the relevant service *variants*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
 * - It is mandatory to specify `schedule.availabilityConstraints.sessionDurations`,
 * even though these values are ignored.
 * @param service - Service to create.
 * @public
 * @requiredField service
 * @permissionId BOOKINGS.SERVICES_CREATE
 * @applicableIdentity APP
 * @returns Created service.
 * @fqn wix.bookings.services.v2.ServicesService.CreateService
 */
declare function createService(service: Service): Promise<NonNullablePaths<Service, `type` | `media.items` | `category._id` | `form._id` | `payment.fixed.price.value` | `payment.fixed.price.currency` | `payment.rateType` | `payment.pricingPlanIds` | `locations` | `locations.${number}.business._id` | `locations.${number}.business.name` | `locations.${number}.custom._id` | `locations.${number}._id` | `locations.${number}.type` | `bookingPolicy._id` | `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.resourcesPolicy.enabled` | `bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled` | `schedule.availabilityConstraints.durations` | `schedule.availabilityConstraints.durations.${number}.minutes` | `schedule.availabilityConstraints.sessionDurations` | `schedule.availabilityConstraints.timeBetweenSessions` | `staffMemberIds` | `serviceResources` | `supportedSlugs` | `supportedSlugs.${number}.name` | `seoData.tags` | `seoData.tags.${number}.type` | `seoData.tags.${number}.children` | `seoData.tags.${number}.custom` | `seoData.tags.${number}.disabled` | `seoData.settings.preventAutoRedirect` | `seoData.settings.keywords` | `seoData.settings.keywords.${number}.term` | `seoData.settings.keywords.${number}.isMain`, 6> & {
    __validationErrorsType?: CreateServiceValidationErrors;
}>;
/**
 * Creates multiple services.
 *
 *
 * See *Create Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service))
 * for more details.
 * @param services - Services to create.
 * @public
 * @requiredField services
 * @permissionId BOOKINGS.SERVICES_CREATE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.BulkCreateServices
 */
declare function bulkCreateServices(services: Service[], options?: BulkCreateServicesOptions): Promise<NonNullablePaths<BulkCreateServicesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.type` | `results.${number}.item.category._id` | `results.${number}.item.form._id` | `results.${number}.item.payment.rateType` | `results.${number}.item.bookingPolicy._id` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
interface BulkCreateServicesOptions {
    /** Whether to return the created service objects. */
    returnEntity?: boolean;
}
/**
 * Retrieves a service.
 * @param serviceId - ID of the service to retrieve.
 * @public
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_READ
 * @applicableIdentity APP
 * @returns Retrieved service.
 * @fqn wix.bookings.services.v2.ServicesService.GetService
 */
declare function getService(serviceId: string): Promise<NonNullablePaths<Service, `type` | `media.items` | `category._id` | `form._id` | `payment.fixed.price.value` | `payment.fixed.price.currency` | `payment.rateType` | `payment.pricingPlanIds` | `locations` | `locations.${number}.business._id` | `locations.${number}.business.name` | `locations.${number}.custom._id` | `locations.${number}._id` | `locations.${number}.type` | `bookingPolicy._id` | `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.resourcesPolicy.enabled` | `bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled` | `schedule.availabilityConstraints.durations` | `schedule.availabilityConstraints.durations.${number}.minutes` | `schedule.availabilityConstraints.sessionDurations` | `schedule.availabilityConstraints.timeBetweenSessions` | `staffMemberIds` | `serviceResources` | `supportedSlugs` | `supportedSlugs.${number}.name` | `seoData.tags` | `seoData.tags.${number}.type` | `seoData.tags.${number}.children` | `seoData.tags.${number}.custom` | `seoData.tags.${number}.disabled` | `seoData.settings.preventAutoRedirect` | `seoData.settings.keywords` | `seoData.settings.keywords.${number}.term` | `seoData.settings.keywords.${number}.isMain`, 6>>;
/**
 * Updates a service.
 *
 *
 * Each time the service is updated, `revision` increments by 1. You must
 * include the number of the existing revision when updating the service.
 * This ensures you're working with the latest service information and
 * prevents unintended overwrites.
 *
 * ## Session durations
 *
 * Specify `schedule.availabilityConstraints.sessionDurations`
 * only if you want to update it for appointment-based services without varied
 * pricing based on session length. Don't specify `schedule.availabilityConstraints.sessionDurations`
 * for all other appointment-based services, classes, or courses. See *Create Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service#session-durations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service#session-durations))
 * for more details.
 * @param _id - Service ID.
 * @public
 * @requiredField _id
 * @requiredField service
 * @requiredField service.revision
 * @permissionId BOOKINGS.SERVICES_UPDATE
 * @applicableIdentity APP
 * @returns Updated service.
 * @fqn wix.bookings.services.v2.ServicesService.UpdateService
 */
declare function updateService(_id: string, service: NonNullablePaths<UpdateService, `revision`, 2>): Promise<NonNullablePaths<Service, `type` | `media.items` | `category._id` | `form._id` | `payment.fixed.price.value` | `payment.fixed.price.currency` | `payment.rateType` | `payment.pricingPlanIds` | `locations` | `locations.${number}.business._id` | `locations.${number}.business.name` | `locations.${number}.custom._id` | `locations.${number}._id` | `locations.${number}.type` | `bookingPolicy._id` | `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.resourcesPolicy.enabled` | `bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled` | `schedule.availabilityConstraints.durations` | `schedule.availabilityConstraints.durations.${number}.minutes` | `schedule.availabilityConstraints.sessionDurations` | `schedule.availabilityConstraints.timeBetweenSessions` | `staffMemberIds` | `serviceResources` | `supportedSlugs` | `supportedSlugs.${number}.name` | `seoData.tags` | `seoData.tags.${number}.type` | `seoData.tags.${number}.children` | `seoData.tags.${number}.custom` | `seoData.tags.${number}.disabled` | `seoData.settings.preventAutoRedirect` | `seoData.settings.keywords` | `seoData.settings.keywords.${number}.term` | `seoData.settings.keywords.${number}.isMain`, 6> & {
    __validationErrorsType?: UpdateServiceValidationErrors;
}>;
interface UpdateService {
    /**
     * Service ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Service type.
     * Learn more about *service types*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)).
     */
    type?: ServiceTypeWithLiterals;
    /**
     * Order of the service within a *category*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object)).
     */
    sortOrder?: number | null;
    /**
     * Service name.
     * @maxLength 400
     * @minLength 1
     */
    name?: string | null;
    /**
     * Service description. For example, `High-class hair styling, cuts, straightening and color`.
     * @maxLength 7000
     */
    description?: string | null;
    /**
     * Short service description, such as `Hair styling`.
     * @maxLength 6000
     */
    tagLine?: string | null;
    /**
     * Default maximum number of customers that can book the service. The service cannot be booked beyond this capacity.
     * @min 1
     * @max 1000
     */
    defaultCapacity?: number | null;
    /** Media associated with the service. */
    media?: Media;
    /** Whether the service is hidden from Wix Bookings pages and widgets. */
    hidden?: boolean | null;
    /**
     * _Category_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object))
     * the service is associated with.
     */
    category?: V2Category;
    /** Form the customer filled out when booking the service. */
    form?: Form;
    /**
     * Payment options for booking the service.
     * Learn more about *service payments*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)).
     */
    payment?: Payment;
    /** Online booking settings. */
    onlineBooking?: OnlineBooking;
    /** Conferencing options for the service. */
    conferencing?: Conferencing;
    /**
     * The locations this service is offered at. Read more about *service locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations)).
     * @immutable
     * @maxSize 500
     */
    locations?: Location[];
    /**
     * _Policy_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction))
     * determining under what conditions this service can be booked. For example, whether the service can only be booked up to 30 minutes before it begins.
     */
    bookingPolicy?: BookingPolicy;
    /**
     * The service's *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),
     * which can be used to manage the service's *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).
     */
    schedule?: Schedule;
    /**
     * IDs of the *staff members*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service. Available only for appointment-based services.
     * @maxSize 220
     * @format GUID
     */
    staffMemberIds?: string[];
    /**
     * Information about which resources must be available so customers can book the service.
     * For example, a meeting room or equipment.
     * @maxSize 3
     */
    serviceResources?: ServiceResource[];
    /**
     * A slug is the last part of the URL address that serves as a unique identifier of the service.
     * The list of supported slugs includes past service names for backwards compatibility, and a custom slug if one was set by the business owner.
     * @readonly
     * @maxSize 100
     */
    supportedSlugs?: Slug[];
    /**
     * Active slug for the service.
     * Learn more about *service slugs*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-slugs) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-slugs)).
     * @readonly
     */
    mainSlug?: Slug;
    /**
     * URLs to various service-related pages, such as the calendar page and the booking page.
     * @readonly
     */
    urls?: URLs;
    /** Extensions enabling users to save custom data related to the service. */
    extendedFields?: ExtendedFields;
    /** Custom SEO data for the service. */
    seoData?: SeoSchema;
    /**
     * Date and time the service was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the service was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Revision number, which increments by 1 each time the service is updated. To
     * prevent conflicting changes, the existing revision must be used when updating
     * a service.
     * @readonly
     */
    revision?: string | null;
}
/**
 * Updates up to 100 services.
 *
 *
 * See *Update Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))
 * for more details.
 *
 * The call succeeds even if one or more individual services can't be updated.
 * Information about failures is returned in `bulkActionMetadata`.
 * @public
 * @requiredField options.services.service
 * @requiredField options.services.service._id
 * @requiredField options.services.service.revision
 * @permissionId BOOKINGS.SERVICES_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.BulkUpdateServices
 */
declare function bulkUpdateServices(options?: NonNullablePaths<BulkUpdateServicesOptions, `services.${number}.service` | `services.${number}.service._id` | `services.${number}.service.revision`, 5>): Promise<NonNullablePaths<BulkUpdateServicesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.type` | `results.${number}.item.category._id` | `results.${number}.item.form._id` | `results.${number}.item.payment.rateType` | `results.${number}.item.bookingPolicy._id` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
interface BulkUpdateServicesOptions {
    /**
     * Services to update.
     * @maxSize 100
     */
    services?: MaskedService[];
    /** Whether to include the updated services in the response. Default: `false` */
    returnEntity?: boolean;
}
/**
 * Updates multiple services by filter.
 *
 *
 * Refer to the supported filters article
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
 * for more details.
 *
 * See *Update Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))
 * for more details about updating a service.
 *
 * The call succeeds even if one or more individual services can't be updated.
 * Information about failures is returned in `bulkActionMetadata`.
 * @param filter - Filter to identify the services to update.
 * @public
 * @requiredField filter
 * @requiredField options
 * @requiredField options.service
 * @permissionId BOOKINGS.SERVICES_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.BulkUpdateServicesByFilter
 */
declare function bulkUpdateServicesByFilter(filter: Record<string, any>, options: NonNullablePaths<BulkUpdateServicesByFilterOptions, `service`, 2>): Promise<NonNullablePaths<BulkUpdateServicesByFilterResponse, `jobId`, 2>>;
interface BulkUpdateServicesByFilterOptions {
    /** Service to update. */
    service: Service;
}
/**
 * Deletes a service.
 *
 *
 * Specify `{"preserveFutureSessionsWithParticipants": true}` to retain all
 * future sessions for the service. By default, all future sessions are canceled.
 * @param serviceId - ID of the service to delete.
 * @public
 * @requiredField serviceId
 * @param options - Allows you to configure how to handle the deleted service's future sessions and how to notify the sessions participants.
 * @permissionId BOOKINGS.SERVICES_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.DeleteService
 */
declare function deleteService(serviceId: string, options?: DeleteServiceOptions): Promise<void>;
interface DeleteServiceOptions {
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /**
     * Whether to notify participants about the change and an optional
     * custom message.
     */
    participantNotification?: ParticipantNotification;
}
/**
 * Deletes multiple services.
 *
 *
 * See *Delete Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))
 * for more details about deleting a service.
 *
 * The call succeeds even if one or more individual services can't be deleted.
 * Information about failures is returned in `bulkActionMetadata`.
 * @param ids - IDs of the services to delete.
 * @public
 * @requiredField ids
 * @permissionId BOOKINGS.SERVICES_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.BulkDeleteServices
 */
declare function bulkDeleteServices(ids: string[], options?: BulkDeleteServicesOptions): Promise<NonNullablePaths<BulkDeleteServicesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.type` | `results.${number}.item.category._id` | `results.${number}.item.form._id` | `results.${number}.item.payment.rateType` | `results.${number}.item.bookingPolicy._id` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
interface BulkDeleteServicesOptions {
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`.
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /**
     * Whether to notify participants about the change and an optional
     * custom message.
     */
    participantNotification?: ParticipantNotification;
}
/**
 * Deletes multiple services by filter.
 *
 *
 * See *Delete Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))
 * for more details about deleting a service.
 *
 * The call succeeds even if one or more individual services can't be deleted.
 * Information about failures is returned in `bulkActionMetadata`.
 *
 * Refer to the supported filters article
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
 * for more details.
 *
 * To learn about working with filters in general, see
 * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).
 * @param filter - Filter to identify the services that need to be deleted.
 * @public
 * @requiredField filter
 * @permissionId BOOKINGS.SERVICES_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.BulkDeleteServicesByFilter
 */
declare function bulkDeleteServicesByFilter(filter: Record<string, any>, options?: BulkDeleteServicesByFilterOptions): Promise<NonNullablePaths<BulkDeleteServicesByFilterResponse, `jobId`, 2>>;
interface BulkDeleteServicesByFilterOptions {
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`.
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /** Whether to notify participants about the change and an optional custom message. */
    participantNotification?: ParticipantNotification;
}
/**
 * Creates a query to retrieve a list of `service` objects.
 *
 * The `queryServices()` function builds a query to retrieve a list of `service` objects and returns a `ServicesQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-builder/find) function.
 *
 * You can refine the query by chaining `ServicesQueryBuilder` functions onto the query. `ServicesQueryBuilder` functions enable you to sort, filter, and control the results that `queryServices()` returns.
 *
 * `queryServices()` runs with the following `ServicesQueryBuilder` defaults that you can override:
 *
 * + `limit` is `100`.
 * + Sorted by `createdDate` in ascending order.
 *
 * The functions that are chained to `queryServices()` are applied in the order they are called. For example, if you apply `ascending("category.name")` and then `ascending("name")`, the results are sorted first by `category.name`, and then, if there are multiple results with the same `category.name`, the items are sorted by `name`.
 *
 * The following `ServicesQueryBuilder` functions are supported for the `queryServices()` function. For a full description of the `service` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-result/items) property in `ServicesQueryResult`.
 * @public
 * @permissionId BOOKINGS.SERVICES_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.QueryServices
 */
declare function queryServices(): ServicesQueryBuilder;
interface QueryOffsetResult {
    currentPage: number | undefined;
    totalPages: number | undefined;
    totalCount: number | undefined;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface ServicesQueryResult extends QueryOffsetResult {
    items: Service[];
    query: ServicesQueryBuilder;
    next: () => Promise<ServicesQueryResult>;
    prev: () => Promise<ServicesQueryResult>;
}
interface ServicesQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    eq: (propertyName: '_id' | 'type' | 'name' | 'description' | 'tagLine' | 'hidden' | 'category.id' | 'category.name' | 'form.id' | 'payment.options.online' | 'payment.options.inPerson' | 'payment.options.pricingPlan' | 'onlineBooking.enabled' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'staffMemberIds' | 'supportedSlugs.name' | 'mainSlug.name', value: any) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ne: (propertyName: '_id' | 'type' | 'name' | 'description' | 'tagLine' | 'hidden' | 'category.id' | 'category.name' | 'form.id' | 'payment.options.online' | 'payment.options.inPerson' | 'payment.options.pricingPlan' | 'onlineBooking.enabled' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'staffMemberIds' | 'supportedSlugs.name' | 'mainSlug.name', value: any) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ge: (propertyName: '_id' | 'name' | 'description' | 'tagLine' | 'category.id' | 'category.name' | 'form.id' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'supportedSlugs.name' | 'mainSlug.name', value: any) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    gt: (propertyName: '_id' | 'name' | 'description' | 'tagLine' | 'category.id' | 'category.name' | 'form.id' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'supportedSlugs.name' | 'mainSlug.name', value: any) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    le: (propertyName: '_id' | 'name' | 'description' | 'tagLine' | 'category.id' | 'category.name' | 'form.id' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'supportedSlugs.name' | 'mainSlug.name', value: any) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    lt: (propertyName: '_id' | 'name' | 'description' | 'tagLine' | 'category.id' | 'category.name' | 'form.id' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'supportedSlugs.name' | 'mainSlug.name', value: any) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `string`.
     * @param string - String to compare against. Case-insensitive.
     */
    startsWith: (propertyName: '_id' | 'name' | 'description' | 'tagLine' | 'category.id' | 'category.name' | 'form.id' | 'locations.business.id' | 'supportedSlugs.name' | 'mainSlug.name', value: string) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     */
    hasSome: (propertyName: '_id' | 'type' | 'name' | 'description' | 'tagLine' | 'hidden' | 'category.id' | 'category.name' | 'form.id' | 'payment.options.online' | 'payment.options.inPerson' | 'payment.options.pricingPlan' | 'onlineBooking.enabled' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'staffMemberIds' | 'supportedSlugs.name' | 'mainSlug.name', value: any[]) => ServicesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     */
    hasAll: (propertyName: 'staffMemberIds', value: any[]) => ServicesQueryBuilder;
    in: (propertyName: '_id' | 'type' | 'name' | 'description' | 'tagLine' | 'hidden' | 'category.id' | 'category.name' | 'form.id' | 'payment.options.online' | 'payment.options.inPerson' | 'payment.options.pricingPlan' | 'onlineBooking.enabled' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'staffMemberIds' | 'supportedSlugs.name' | 'mainSlug.name', value: any) => ServicesQueryBuilder;
    exists: (propertyName: '_id' | 'type' | 'name' | 'description' | 'tagLine' | 'hidden' | 'category.id' | 'category.name' | 'form.id' | 'payment.options.online' | 'payment.options.inPerson' | 'payment.options.pricingPlan' | 'onlineBooking.enabled' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'staffMemberIds' | 'supportedSlugs.name' | 'mainSlug.name', value: boolean) => ServicesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    ascending: (...propertyNames: Array<'_id' | 'appId' | 'type' | 'name' | 'description' | 'tagLine' | 'hidden' | 'category.id' | 'category.name' | 'form.id' | 'payment.options.online' | 'payment.options.inPerson' | 'payment.options.pricingPlan' | 'onlineBooking.enabled' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'staffMemberIds' | 'supportedSlugs.name' | 'mainSlug.name'>) => ServicesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    descending: (...propertyNames: Array<'_id' | 'appId' | 'type' | 'name' | 'description' | 'tagLine' | 'hidden' | 'category.id' | 'category.name' | 'form.id' | 'payment.options.online' | 'payment.options.inPerson' | 'payment.options.pricingPlan' | 'onlineBooking.enabled' | 'locations.business.id' | 'schedule.firstSessionStart' | 'schedule.lastSessionEnd' | 'staffMemberIds' | 'supportedSlugs.name' | 'mainSlug.name'>) => ServicesQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */
    limit: (limit: number) => ServicesQueryBuilder;
    /** @param skip - Number of items to skip in the query results before returning the results. */
    skip: (skip: number) => ServicesQueryBuilder;
    find: () => Promise<ServicesQueryResult>;
}
interface ServiceSearchSpec extends SearchSpec {
    searchable: ['name'];
    aggregatable: [
        '_id',
        'addOnDetails.addOnId',
        'addOnDetails.durationInMinutes',
        'appId',
        'category._id',
        'category.name',
        'category.sortOrder',
        'description',
        'form._id',
        'hidden',
        'locations.business._id',
        'locations.business.name',
        'locations.type',
        'mainSlug.name',
        'name',
        'onlineBooking.enabled',
        'payment.options.inPerson',
        'payment.options.online',
        'payment.options.pricingPlan',
        'staffMemberIds',
        'supportedSlugs.name',
        'tagLine',
        'type'
    ];
    paging: 'cursor';
    wql: [
        {
            operators: ['$hasAll', '$hasSome'];
            fields: [
                'locations.business._id',
                'locations.business.name',
                'locations.calculatedAddress.formattedAddress',
                'locations.type',
                'staffMemberIds',
                'supportedSlugs.name'
            ];
            sort: 'BOTH';
        },
        {
            operators: '*';
            fields: [
                '_id',
                'addOnDetails.addOnId',
                'addOnDetails.durationInMinutes',
                'appId',
                'category._id',
                'category.name',
                'category.sortOrder',
                'description',
                'form._id',
                'hidden',
                'mainSlug.name',
                'name',
                'onlineBooking.enabled',
                'payment.options.inPerson',
                'payment.options.online',
                'payment.options.pricingPlan',
                'tagLine',
                'type'
            ];
            sort: 'BOTH';
        }
    ];
}
type CommonSearchWithEntityContext = Search<Service, ServiceSearchSpec>;
type ServiceSearch = {
    /**
    Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
    `filter`, `sort`, or `search` can't be specified.
    */
    cursorPaging?: {
        /**
      Number of items to load.
      @max: 100
      */
        limit?: NonNullable<CommonSearchWithEntityContext['cursorPaging']>['limit'] | null;
        /**
      Pointer to the next or previous page in the list of results.
    
      You can get the relevant cursor token
      from the `pagingMetadata` object in the previous call's response.
      Not relevant for the first request.
      @maxLength: 16000
      */
        cursor?: NonNullable<CommonSearchWithEntityContext['cursorPaging']>['cursor'] | null;
    };
    /**
    Filter object for narrowing search results. For example, to return only services with specific payment options: `"filter": {"payment.options.online": true, "payment.options.in_person": false}`.
  
    Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).
    */
    filter?: CommonSearchWithEntityContext['filter'] | null;
    /**
    Array of sort objects specifying result order. For example, to sort by creation date in descending order: `"sort": [{"fieldName": "createdDate", "order": "DESC"}]`.
  
    Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).
    @maxSize: 10
    */
    sort?: {
        /**
      Name of the field to sort by.
      @maxLength: 512
      */
        fieldName?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['fieldName'];
        /**
      Sort order.
      */
        order?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['order'];
    }[];
    /**
    Aggregations for grouping data into categories (facets) and providing summaries for each category.
    For example, use aggregations to categorize search results by service type, payment options, or locations.
    @maxSize: 10
    */
    aggregations?: {
        /**
      Value aggregation configuration.
      */
        value?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['value'];
        /**
      Range aggregation configuration.
      */
        range?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['range'];
        /**
      Scalar aggregation configuration.
      */
        scalar?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['scalar'];
        /**
      Date histogram aggregation configuration.
      */
        dateHistogram?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['dateHistogram'];
        /**
      Nested aggregation configuration.
      */
        nested?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['nested'];
        /**
      User-defined name of aggregation. Must be unique and will appear in aggregation results.
      @maxLength: 100
      */
        name?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['name'] | null;
        /**
      Type of aggregation. Client must specify matching aggregation field below.
      */
        type?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['type'];
        /**
      Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
      @maxLength: 200
      */
        fieldPath?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['fieldPath'];
        /**
      Deprecated, use `nested` instead.
      @deprecated: Deprecated, use `nested` instead.,
      @replacedBy: kind.nested,
      @targetRemovalDate: 2025-01-01
      */
        groupBy?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['groupBy'];
    }[];
    /**
    Free text to match in searchable fields.
    */
    search?: {
        /**
      Search mode. Defines the search logic for combining multiple terms in the `expression`.
      */
        mode?: NonNullable<CommonSearchWithEntityContext['search']>['mode'];
        /**
      Search term or expression.
      @maxLength: 200
      */
        expression?: NonNullable<CommonSearchWithEntityContext['search']>['expression'] | null;
        /**
      Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `order.address.streetName`.
      @maxSize: 10,
      @maxLength: 200
      */
        fields?: NonNullable<CommonSearchWithEntityContext['search']>['fields'];
        /**
      Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions.
      */
        fuzzy?: NonNullable<CommonSearchWithEntityContext['search']>['fuzzy'];
    };
    /**
    Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.
  
    Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).
    @maxLength: 50
    */
    timeZone?: CommonSearchWithEntityContext['timeZone'] | null;
};
/**
 * Retrieves a list of up to 100 *booking policies*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)),
 * and information about the services that are connected to them,
 * given the provided filtering, paging, and sorting.
 *
 * ## Defaults
 *
 * Query Policies has the following default settings, which you can override:
 *
 * + Sorted by `id` in ascending order.
 * + `cursorPaging.limit` set to `100`.
 *
 * ## Filters
 *
 * For a complete list of supported filters, refer to **Booking Policies API: Supported Filters**
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters)).
 *
 * When using date filters, you must use
 * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
 *
 * ## Returned services
 *
 * If a booking policy is connected to more than 5 services, only a subset of
 * those services is returned. The `bookingPolicies.totalServiceCount` values
 * indicate the total number of services linked to each policy. You can call *Search Services*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))
 * and specify the relevant policy ID in the filter to retrieve all services
 * connected to the booking policy.
 *
 * ## See also
 *
 * To learn about working with *Query* endpoints, see
 * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
 * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
 * @param query - Information about filters, paging, and sorting. See the article about
 * booking policy filters
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
 * for all supported filters and sorting options.
 * @public
 * @documentationMaturity preview
 * @requiredField query
 * @permissionId BOOKINGS.BOOKING_POLICY_READ
 * @permissionId BOOKINGS.SERVICES_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.QueryPolicies
 */
declare function queryPolicies(query: CursorQuery): Promise<NonNullablePaths<QueryPoliciesResponse, `bookingPolicies` | `bookingPolicies.${number}.bookingPolicy._id` | `bookingPolicies.${number}.bookingPolicy.customPolicyDescription.enabled` | `bookingPolicies.${number}.bookingPolicy.customPolicyDescription.description` | `bookingPolicies.${number}.bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicies.${number}.bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicies.${number}.bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.capacity` | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicies.${number}.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicies.${number}.bookingPolicy.resourcesPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicies.${number}.bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.saveCreditCardPolicy.enabled` | `bookingPolicies.${number}.totalServiceCount`, 6>>;
/**
 * Retrieves a list of up to 100 *booking forms*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/forms/introduction) | [REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)),
 * and information about the services that are connected to them,
 * given the provided filtering, paging, and sorting.
 *
 *
 * ## Defaults
 *
 * Query Booking Forms has the following default settings, which you can override:
 *
 * + Sorted by `id` in ascending order.
 * + `cursorPaging.limit` set to `100`.
 *
 * ## Filters
 *
 * For a complete list of supported filters, refer to **Forms API: Supported Filters**
 * ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)).
 *
 * When using date filters, you must use
 * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
 *
 * ## Returned services
 *
 * If a booking policy is connected to more than 5 services, only a subset of
 * these service IDs and names is returned. The `bookingForms.totalServiceCount`
 * values indicate the total number of services linked to each form. You can call *Query Services*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/query-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/query-services))
 * and specify the relevant form ID in the filter to retrieve all services
 * connected to the booking form.
 *
 * ## Default booking forms
 *
 * By default, all Wix Bookings services use a standard booking form. To retrieve
 * a site's default booking form with Query Booking Forms, specify
 * `{"conditionalFields": ["DEFAULT_BOOKING_FORM"]}`.
 *
 * ## See also
 *
 * To learn about working with *Query* endpoints, see
 * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
 * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
 * @param query - Information about filters, paging, and sorting. See the article about
 * form filters ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object))
 * for all supported filters and sorting options.
 * @public
 * @requiredField query
 * @permissionId BOOKINGS.SERVICE_BOOKING_FORMS_READ
 * @permissionId BOOKINGS.SERVICES_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.QueryBookingForms
 */
declare function queryBookingForms(query: CursorQuery, options?: QueryBookingFormsOptions): Promise<NonNullablePaths<QueryBookingFormsResponse, `bookingForms` | `bookingForms.${number}.formDetails.formId` | `bookingForms.${number}.totalServiceCount` | `defaultBookingForm.formDetails.formId` | `defaultBookingForm.connectedServices` | `defaultBookingForm.totalServiceCount`, 5> & {
    __applicationErrorsType?: QueryBookingFormsApplicationErrors;
}>;
interface QueryBookingFormsOptions {
    /**
     * Conditional fields to return.
     * @maxSize 1
     */
    conditionalFields?: RequestedFieldsWithLiterals[];
}
/**
 * Counts how many services match the given filter.
 *
 *
 * Refer to the supported filters article
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
 * for more details.
 *
 * To learn about working with filters in general, see
 * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters)
 * @public
 * @permissionId BOOKINGS.SERVICES_COUNT
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.CountServices
 */
declare function countServices(options?: CountServicesOptions): Promise<NonNullablePaths<CountServicesResponse, `count`, 2>>;
interface CountServicesOptions {
    /**
     * Query filter to base the count on. See supported filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     */
    filter?: Record<string, any> | null;
}
/**
 * Retrieves 3 separate lists of business, custom, and customer *locations*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)),
 * given the provided filtering, and whether each location is connected to at
 * least one of the site's services.
 *
 *
 * ## Defaults
 *
 * Query Locations has the following default setting, which you can't override:
 * Sorted by `id` in ascending order.
 *
 * ## Filters
 *
 * For a complete list of supported filters, refer to the `location` object
 * ([REST](https://dev.wix.com/docs/rest/business-management/locations/location-object)).
 *
 * When using date filters, you must use
 * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
 *
 * ## See also
 *
 * To learn about working with *Query* endpoints, see
 * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
 * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
 * @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.SERVICES_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.QueryLocations
 */
declare function queryLocations(options?: QueryLocationsOptions): Promise<NonNullablePaths<QueryLocationsResponse, `businessLocations.exists` | `businessLocations.locations` | `businessLocations.locations.${number}.business._id` | `businessLocations.locations.${number}.business.name` | `businessLocations.locations.${number}.custom._id` | `businessLocations.locations.${number}._id` | `businessLocations.locations.${number}.type` | `customLocations.exists` | `customerLocations.exists`, 6>>;
interface QueryLocationsOptions {
    /** Filter. */
    filter?: QueryLocationsFilter;
}
/**
 * Retrieves a list of service *categories*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/introduction)),
 * given the provided filtering.
 *
 *
 * ## Defaults
 *
 * Query Categories has the following default setting, which you can't override:
 * Sorted by `id` in ascending order.
 *
 * ## Filters
 *
 * For a complete list of supported filters, refer to the supported filters article
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering)).
 *
 * When using date filters, you must use
 * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
 *
 * ## See also
 *
 * To learn about working with *Query* endpoints, see
 * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
 * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
 * @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.SERVICES_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.QueryCategories
 */
declare function queryCategories(options?: QueryCategoriesOptions): Promise<NonNullablePaths<QueryCategoriesResponse, `categories` | `categories.${number}._id`, 4>>;
interface QueryCategoriesOptions {
    /** Filter. */
    filter?: QueryCategoriesFilter;
}
/**
 * Replaces the list of the *locations*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))
 * where the business offers the service.
 *
 *
 * ## Consequences for customers
 *
 * Removing a service location may impact existing sessions and their
 * participants. If you remove at least one service location, you must
 * specify `removedLocationSessionsAction` to indicate what happens to all
 * future sessions scheduled at this location.
 *
 * - **Keep existing location**: If you want to retain future sessions at their
 * originally scheduled location, specify
 * `{"removedLocationSessionsAction.action": "KEEP_AT_CURRENT_LOCATION"}`.
 * This ensures nothing changes for the customer, but the business must be
 * able to provide access to the removed location in the future.
 * - **Update location**: If you want to update the location for future sessions
 * scheduled at the removed location, specify
 * `{"removedLocationSessionsAction.action": "MOVE_TO_LOCATION"}` and
 * `moveToLocationOptions.newLocation`.
 *
 * You can't mix and match to keep some sessions at the previous location while
 * moving other sessions to an updated location.
 *
 * Past session details aren't changed, no matter which option you choose for
 * future sessions.
 *
 * Future sessions scheduled for a location defined by the customer are also not
 * updated.
 *
 * ## Specify location details
 *
 * Depending on whether the new or updated location is a business or custom location,
 * you need to specify different fields.
 *
 * - **Business location**: Specify the relevant *location ID*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))
 * in `locations.business.id`.
 * - **Custom location**: Specify the complete address object as
 * `locations.custom.address`.
 *
 * ## Participant notifications
 *
 * You can specify a `participantNotification.message` that's immediately send
 * to all customers who had booked at a changed location. Ensure
 * `participantNotification.notifyParticipants` is set to `true` to send the
 * message.
 * @param serviceId - ID of the service.
 * @param locations - List of locations replacing existing service locations. Omitting an existing location removes it from the service. Specify `removedLocationSessionsAction` to determine the handling of future sessions scheduled at that location.
 * @public
 * @documentationMaturity preview
 * @requiredField locations
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_LOCATIONS_SET
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.SetServiceLocations
 */
declare function setServiceLocations(serviceId: string, locations: Location[], options?: SetServiceLocationsOptions): Promise<NonNullablePaths<SetServiceLocationsResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7>>;
interface SetServiceLocationsOptions {
    /**
     * The action to perform on sessions currently set to a removed location. For
     * example, move existing sessions to a new specified location.
     * If not specified, sessions will not be moved to a new location.
     */
    removedLocationSessionsAction?: RemovedLocationSessionsAction;
    /**
     * Whether to notify participants about the change of location, and an
     * Optional custom message. The notification is sent only to participants of sessions that are affected by the change.
     */
    participantNotification?: ParticipantNotification;
}
/**
 * Adds a list of *pricing plan IDs*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
 * to a service's `payment.pricingPlanIds` array.
 *
 *
 * The call doesn't validate whether the service's `payment.options.pricingPlan`
 * is set to `true`. If it's set to `false`, customers aren't able to pay
 * for their bookings using pricing plans. You can call *Update Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))
 * to change a service's supported payment methods.
 * @param serviceId - ID of the service to update.
 * @param pricingPlanIds - IDs of the *pricing plans*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
 * to add to the service's `payment.pricingPlanIds` array.
 * @public
 * @requiredField pricingPlanIds
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_PRICING_PLANS_ADD
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.EnablePricingPlansForService
 */
declare function enablePricingPlansForService(serviceId: string, pricingPlanIds: string[]): Promise<NonNullablePaths<EnablePricingPlansForServiceResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7> & {
    __applicationErrorsType?: EnablePricingPlansForServiceApplicationErrors;
}>;
/**
 * Removes a list of *pricing plan IDs*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
 * from a service's `payment.pricingPlanIds` array.
 *
 *
 * If you remove all pricing plan IDs from `payment.pricingPlanIds` and the
 * service supports only payments by pricing plan, customers will no longer be
 * able to book the service, as they will have no payment options available.
 * @param serviceId - ID of the service to update.
 * @public
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_PRICING_PLANS_REMOVE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.DisablePricingPlansForService
 */
declare function disablePricingPlansForService(serviceId: string, options?: DisablePricingPlansForServiceOptions): Promise<NonNullablePaths<DisablePricingPlansForServiceResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7> & {
    __applicationErrorsType?: DisablePricingPlansForServiceApplicationErrors;
}>;
interface DisablePricingPlansForServiceOptions {
    /**
     * IDs of the *pricing plans*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * to remove from the service's `payment.pricingPlanIds` array.
     * @format GUID
     * @maxSize 75
     */
    pricingPlanIds?: string[];
}
/**
 * Sets a new active slug for the service.
 *
 *
 * The call fails if at least one of these conditions is met:
 * - The slug doesn't adheres to the supported format.
 * - Another service is currently using the slug.
 * - Another service has used the slug in the past.
 * @param serviceId - ID of the service for which to update the active slug.
 * @public
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_CUSTOM_SLUGS_SET
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.SetCustomSlug
 */
declare function setCustomSlug(serviceId: string, options?: SetCustomSlugOptions): Promise<NonNullablePaths<SetCustomSlugResponse, `slug.name` | `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7> & {
    __applicationErrorsType?: SetCustomSlugApplicationErrors;
    __validationErrorsType?: SetCustomSlugValidationErrors;
}>;
interface SetCustomSlugOptions {
    /**
     * Slug to set as the active service slug.
     * @maxLength 500
     */
    slug?: string;
}
/**
 * Checks whether a custom slug is validate for the service.
 *
 *
 * The checks include:
 * - The slug adheres to the supported format.
 * - No other service is currently using the slug.
 * - No other service has used the slug in the past.
 *
 * The call fails if at least one of the checks fails.
 * @param serviceId - IO of the service to check custom slug validity for.
 * @public
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICES_CUSTOM_SLUGS_SET
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.ValidateSlug
 */
declare function validateSlug(serviceId: string, options?: ValidateSlugOptions): Promise<NonNullablePaths<ValidateSlugResponse, `valid` | `errors`, 2>>;
interface ValidateSlugOptions {
    /**
     * Custom slug to validate.
     * @maxLength 500
     */
    slug?: string;
}
/**
 * Clones a service.
 *
 *
 * ## Connected entities
 *
 * By default, not all entities connected to the service are cloned.
 *
 * ### Schedule
 *
 * Wix Bookings automatically creates a new active *schedule*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
 * for the cloned service. If Wix Bookings can't create this schedule, the
 * Clone Service* call fails.
 *
 * - __For appointment-based services__: Future appointments aren't added to the
 * cloned service's schedule. Use the *Events API*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
 * to add them as needed.
 * - __For classes and courses__: Future one-time events aren't added to the
 * cloned service's schedule, while future recurring events are added
 * asynchronously. The *Clone Service* call always succeeds, whether or not
 * recurring events are added.
 * If the response includes `RECURRING_EVENTS` in the `errors` array, it means the
 * cloned service doesn't have future recurring events, even though the original
 * service does. You can either delete the cloned service and try again or use
 * the *Events API*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
 * to add missing events to the schedule.
 *
 * Wix Bookings never adds past events to the cloned service's schedule.
 *
 * ### Service options and variants
 *
 * If the original service has *variants*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),
 * they're cloned asynchronously. The *Clone Service* call always succeeds,
 * regardless of whether variants were cloned.
 *
 * If the response includes `OPTIONS_AND_VARIANTS` in the `errors` array, the cloned
 * service doesn't have variants, even though the original service does. You can
 * delete the cloned service and call *Clone Service* again, or use the
 * _Service Options And Variants API_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction))
 * to add variants.
 *
 * ### Booking form
 *
 * The original service's booking form isn't cloned, whether it's
 * the default or a custom booking form.
 *
 * ### Pricing plans
 *
 * If the original service's accepts payments via *pricing plans*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/plans/introduction)),
 * the cloned service's `payment.options.pricingPlan` is also set to `true`. To
 * accept specific pricing plans, call *Enable Pricing Plans For Service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/enable-pricing-plans-for-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/enable-pricing-plans-for-service)),
 * after cloning the service. If the original service accepts only
 * pricing plan payments and you don't call *Enable Pricing Plans For Service*
 * after cloning the service, customers will be unable to book the service.
 * @param sourceServiceId - ID of the service to clone.
 * @public
 * @requiredField sourceServiceId
 * @permissionId BOOKINGS.SERVICES_CREATE
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.CloneService
 */
declare function cloneService(sourceServiceId: string): Promise<NonNullablePaths<CloneServiceResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain` | `errors`, 7>>;

export { type SetCustomSlugValidationErrors as $, type AddOnGroup as A, type BulkCreateServicesOptions as B, type CreateAddOnGroupOptions as C, type DeleteAddOnGroupOptions as D, type QueryBookingFormsResponse as E, type QueryBookingFormsApplicationErrors as F, type CountServicesOptions as G, type CountServicesResponse as H, type QueryLocationsOptions as I, type QueryLocationsResponse as J, type QueryCategoriesOptions as K, type ListAddOnGroupsByServiceIdOptions as L, type QueryCategoriesResponse as M, type Location as N, type SetServiceLocationsOptions as O, type SetServiceLocationsResponse as P, type QueryPoliciesResponse as Q, type EnablePricingPlansForServiceResponse as R, type SetAddOnsForGroupOptions as S, type EnablePricingPlansForServiceApplicationErrors as T, type UpdateAddOnGroupOptions as U, type DisablePricingPlansForServiceOptions as V, type DisablePricingPlansForServiceResponse as W, type DisablePricingPlansForServiceApplicationErrors as X, type SetCustomSlugOptions as Y, type SetCustomSlugResponse as Z, type SetCustomSlugApplicationErrors as _, type CreateAddOnGroupResponse as a, type ResourcesPolicy as a$, type ValidateSlugOptions as a0, type ValidateSlugResponse as a1, type CloneServiceResponse as a2, type ServiceCreatedEnvelope as a3, type ServiceDeletedEnvelope as a4, type ServiceUpdatedEnvelope as a5, ServiceType as a6, RateType as a7, LocationType as a8, WebhookIdentityType as a9, type Form as aA, type FormSettings as aB, type Payment as aC, type PaymentRateOneOf as aD, type FixedPayment as aE, type Money as aF, type CustomPayment as aG, type VariedPayment as aH, type PaymentOptions as aI, type OnlineBooking as aJ, type Conferencing as aK, type LocationOptionsOneOf as aL, type CommonAddress as aM, type CommonAddressStreetOneOf as aN, type StreetAddress as aO, type AddressLocation as aP, type BusinessLocationOptions as aQ, type CustomLocationOptions as aR, type BookingPolicy as aS, type PolicyDescription as aT, type LimitEarlyBookingPolicy as aU, type LimitLateBookingPolicy as aV, type BookAfterStartPolicy as aW, type CancellationPolicy as aX, type ReschedulePolicy as aY, type WaitlistPolicy as aZ, type ParticipantsPolicy as a_, V2RequestedFields as aa, SortOrder as ab, SortType as ac, SortDirection as ad, MissingValues as ae, ScalarType as af, NestedAggregationType as ag, Interval as ah, AggregationType as ai, Mode as aj, RequestedFields as ak, Action as al, InvalidSlugError as am, CloneErrors as an, Status as ao, CategoryNotificationEvent as ap, BenefitType as aq, Event as ar, CrudType as as, PlacementType as at, DayOfWeek as au, ResolutionMethod as av, type Media as aw, type MediaItem as ax, type MediaItemItemOneOf as ay, type V2Category as az, type DeleteAddOnGroupApplicationErrors as b, type GetServiceAvailabilityConstraintsResponse as b$, type CancellationFeePolicy as b0, type CancellationWindow as b1, type CancellationWindowFeeOneOf as b2, type SaveCreditCardPolicy as b3, type Schedule as b4, type AvailabilityConstraints as b5, type Duration as b6, type StaffMember as b7, type StaffMediaItem as b8, type StaffMediaItemItemOneOf as b9, type EntityDeletedEvent as bA, type ActionEvent as bB, type MessageEnvelope as bC, type IdentificationData as bD, type IdentificationDataIdOneOf as bE, type CreateAddOnGroupRequest as bF, type DeleteAddOnGroupRequest as bG, type DeleteAddOnGroupResponse as bH, type UpdateAddOnGroupRequest as bI, type ListAddOnGroupsByServiceIdRequest as bJ, type AddOn as bK, type AddOnAddOnInfoOneOf as bL, type AddOnGroupDetail as bM, type SetAddOnsForGroupRequest as bN, type CreateServiceRequest as bO, type CreateServiceResponse as bP, type ValidateServiceRequest as bQ, type ValidateServiceResponse as bR, type FieldViolation as bS, type BulkCreateServicesRequest as bT, type BulkServiceResult as bU, type ItemMetadata as bV, type ApplicationError as bW, type BulkActionMetadata as bX, type GetServiceRequest as bY, type GetServiceResponse as bZ, type GetServiceAvailabilityConstraintsRequest as b_, type StaffMemberDetails as ba, type ResourceGroup as bb, type ResourceIds as bc, type ServiceResource as bd, type ServiceResourceSelectionOneOf as be, type ResourceType as bf, type Slug as bg, type URLs as bh, type ExtendedFields as bi, type SeoSchema as bj, type Keyword as bk, type Tag as bl, type Settings as bm, type AddOnDetails as bn, type ReindexMessage as bo, type ReindexMessageActionOneOf as bp, type Upsert as bq, type Delete as br, type Schema as bs, type SetCustomSlugEvent as bt, type ServicesUrlsChanged as bu, type DomainEvent as bv, type DomainEventBodyOneOf as bw, type EntityCreatedEvent as bx, type RestoreInfo as by, type EntityUpdatedEvent as bz, type UpdateAddOnGroupResponse as c, type BookingPolicyWithServices as c$, type ServiceAvailabilityConstraints as c0, type SplitInterval as c1, type UpdateServiceRequest as c2, type UpdateServiceResponse as c3, type BulkUpdateServicesRequest as c4, type MaskedService as c5, type BulkUpdateServicesByFilterRequest as c6, type DeleteServiceRequest as c7, type ParticipantNotification as c8, type DeleteServiceResponse as c9, type GroupByAggregation as cA, type GroupByAggregationKindOneOf as cB, type SearchDetails as cC, type CursorPagingMetadata as cD, type AggregationData as cE, type ValueAggregationResult as cF, type RangeAggregationResult as cG, type NestedAggregationResults as cH, type NestedAggregationResultsResultOneOf as cI, type ValueResults as cJ, type RangeResults as cK, type AggregationResultsScalarResult as cL, type NestedValueAggregationResult as cM, type ValueResult as cN, type RangeResult as cO, type ScalarResult as cP, type NestedResultValue as cQ, type NestedResultValueResultOneOf as cR, type Results as cS, type DateHistogramResult as cT, type GroupByValueResults as cU, type DateHistogramResults as cV, type NestedResults as cW, type AggregationResults as cX, type AggregationResultsResultOneOf as cY, type QueryPoliciesRequest as cZ, type CursorQueryPagingMethodOneOf as c_, type BulkDeleteServicesRequest as ca, type BulkDeleteServicesByFilterRequest as cb, type QueryServicesRequest as cc, type QueryV2 as cd, type QueryV2PagingMethodOneOf as ce, type Sorting as cf, type Paging as cg, type CursorPaging as ch, type QueryServicesResponse as ci, type PagingMetadataV2 as cj, type Cursors as ck, type SearchServicesRequest as cl, type CursorSearch as cm, type CursorSearchPagingMethodOneOf as cn, type Aggregation as co, type AggregationKindOneOf as cp, type RangeBucket as cq, type IncludeMissingValuesOptions as cr, type ValueAggregation as cs, type ValueAggregationOptionsOneOf as ct, type RangeAggregation as cu, type ScalarAggregation as cv, type DateHistogramAggregation as cw, type NestedAggregationItem as cx, type NestedAggregationItemKindOneOf as cy, type NestedAggregation as cz, type UpdateAddOnGroupApplicationErrors as d, type RateTypeWithLiterals as d$, type QueryBookingFormsRequest as d0, type BookingForm as d1, type FormDetails as d2, type ConnectedService as d3, type CountServicesRequest as d4, type QueryLocationsRequest as d5, type QueryLocationsFilter as d6, type BusinessLocations as d7, type CustomLocations as d8, type CustomerLocations as d9, type HtmlSitePublished as dA, type Page as dB, type SitePropertiesNotification as dC, type SitePropertiesEvent as dD, type Properties as dE, type Categories as dF, type Locale as dG, type Address as dH, type AddressHint as dI, type GeoCoordinates as dJ, type BusinessSchedule as dK, type TimePeriod as dL, type SpecialHourPeriod as dM, type Multilingual as dN, type SupportedLanguage as dO, type ConsentPolicy as dP, type Translation as dQ, type ChangeContext as dR, type ChangeContextPayloadOneOf as dS, type PropertiesChange as dT, type SiteCreated as dU, type SiteCloned as dV, type BaseEventMetadata as dW, type EventMetadata as dX, type ServicesQueryResult as dY, type ServiceSearchSpec as dZ, type ServiceTypeWithLiterals as d_, type QueryCategoriesRequest as da, type QueryCategoriesFilter as db, type QueryServicesMultiLanguageRequest as dc, type QueryServicesMultiLanguageResponse as dd, type SetServiceLocationsRequest as de, type RemovedLocationSessionsAction as df, type RemovedLocationSessionsActionActionOptionsOneOf as dg, type MoveToNewLocationsOptions as dh, type EnablePricingPlansForServiceRequest as di, type InvalidPricingPlan as dj, type DisablePricingPlansForServiceRequest as dk, type SetCustomSlugRequest as dl, type ValidateSlugRequest as dm, type CloneServiceRequest as dn, type CategoryNotification as dp, type Category as dq, type Empty as dr, type BenefitNotification as ds, type Benefit as dt, type EntryPass as du, type Discount as dv, type DiscountDiscountOneOf as dw, type Behavior as dx, type BehaviorBehaviorOneOf as dy, type UserDomainInfoChangedEvent as dz, type ListAddOnGroupsByServiceIdResponse as e, type LocationTypeWithLiterals as e0, type WebhookIdentityTypeWithLiterals as e1, type V2RequestedFieldsWithLiterals as e2, type SortOrderWithLiterals as e3, type SortTypeWithLiterals as e4, type SortDirectionWithLiterals as e5, type MissingValuesWithLiterals as e6, type ScalarTypeWithLiterals as e7, type NestedAggregationTypeWithLiterals as e8, type IntervalWithLiterals as e9, updateService as eA, bulkUpdateServices as eB, bulkUpdateServicesByFilter as eC, deleteService as eD, bulkDeleteServices as eE, bulkDeleteServicesByFilter as eF, queryServices as eG, queryPolicies as eH, queryBookingForms as eI, countServices as eJ, queryLocations as eK, queryCategories as eL, setServiceLocations as eM, enablePricingPlansForService as eN, disablePricingPlansForService as eO, setCustomSlug as eP, validateSlug as eQ, cloneService as eR, type AggregationTypeWithLiterals as ea, type ModeWithLiterals as eb, type RequestedFieldsWithLiterals as ec, type ActionWithLiterals as ed, type InvalidSlugErrorWithLiterals as ee, type CloneErrorsWithLiterals as ef, type StatusWithLiterals as eg, type CategoryNotificationEventWithLiterals as eh, type BenefitTypeWithLiterals as ei, type EventWithLiterals as ej, type CrudTypeWithLiterals as ek, type PlacementTypeWithLiterals as el, type DayOfWeekWithLiterals as em, type ResolutionMethodWithLiterals as en, type CommonSearchWithEntityContext as eo, onServiceCreated as ep, onServiceDeleted as eq, onServiceUpdated as er, createAddOnGroup as es, deleteAddOnGroup as et, updateAddOnGroup as eu, listAddOnGroupsByServiceId as ev, setAddOnsForGroup as ew, createService as ex, bulkCreateServices as ey, getService as ez, type SetAddOnsForGroupResponse as f, type SetAddOnsForGroupApplicationErrors as g, type Service as h, type CreateServiceValidationErrors as i, type BulkCreateServicesResponse as j, type UpdateService as k, type UpdateServiceValidationErrors as l, type BulkUpdateServicesOptions as m, type BulkUpdateServicesResponse as n, type BulkUpdateServicesByFilterOptions as o, type BulkUpdateServicesByFilterResponse as p, type DeleteServiceOptions as q, type BulkDeleteServicesOptions as r, type BulkDeleteServicesResponse as s, type BulkDeleteServicesByFilterOptions as t, type BulkDeleteServicesByFilterResponse as u, type ServicesQueryBuilder as v, type ServiceSearch as w, type SearchServicesResponse as x, type CursorQuery as y, type QueryBookingFormsOptions as z };
