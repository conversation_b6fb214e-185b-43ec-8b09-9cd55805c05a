"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  Actor: () => Actor,
  BookingStatus: () => BookingStatus,
  IdentityType: () => IdentityType,
  LocationType: () => LocationType,
  MultiServiceBookingType: () => MultiServiceBookingType,
  PaymentStatus: () => PaymentStatus,
  Platform: () => Platform,
  SelectedPaymentOption: () => SelectedPaymentOption,
  ValueType: () => ValueType,
  calculatePrice: () => calculatePrice4,
  previewPrice: () => previewPrice4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-v2-price-info-pricing.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-v2-price-info-pricing.http.ts
var import_float = require("@wix/sdk-runtime/transformations/float");
var import_float2 = require("@wix/sdk-runtime/transformations/float");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsPricingBookingsPricingServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-pricing",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings-pricing/v2/price",
        destPath: "/v2/pricing"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings-pricing",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings-pricing/v2/price",
        destPath: "/v2/pricing"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/pricing/v2/pricing",
        destPath: "/v2/pricing"
      },
      {
        srcPath: "/bookings/v2/pricing",
        destPath: "/v2/pricing"
      }
    ]
  };
  return (0, import_rest_modules.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_pricing";
function previewPrice(payload) {
  function __previewPrice({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "bookingLineItems.pricePerParticipant" },
          { path: "bookingLineItems.serviceChoices.pricePerParticipant" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.price_info",
      method: "POST",
      methodFqn: "com.wixpress.bookings.pricing.BookingsPricingService.PreviewPrice",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({
        protoPath: "/v2/pricing/preview",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "priceInfo.calculatedPrice" },
            { path: "priceInfo.deposit" },
            { path: "priceInfo.bookingLineItems.pricePerParticipant" },
            {
              path: "priceInfo.bookingLineItems.serviceChoices.pricePerParticipant"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __previewPrice;
}
function calculatePrice(payload) {
  function __calculatePrice({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "booking.createdDate" },
          { path: "booking.startDate" },
          { path: "booking.endDate" },
          { path: "booking.updatedDate" },
          { path: "booking.canceledDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "booking.contactDetails.fullAddress.geocode.latitude" },
          { path: "booking.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.price_info",
      method: "POST",
      methodFqn: "com.wixpress.bookings.pricing.BookingsPricingService.CalculatePrice",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({
        protoPath: "/v2/pricing/calculate",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "priceInfo.calculatedPrice" },
            { path: "priceInfo.deposit" },
            { path: "priceInfo.bookingLineItems.pricePerParticipant" },
            {
              path: "priceInfo.bookingLineItems.serviceChoices.pricePerParticipant"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __calculatePrice;
}

// src/bookings-v2-price-info-pricing.universal.ts
var MultiServiceBookingType = /* @__PURE__ */ ((MultiServiceBookingType2) => {
  MultiServiceBookingType2["SEQUENTIAL_BOOKINGS"] = "SEQUENTIAL_BOOKINGS";
  MultiServiceBookingType2["SEPARATE_BOOKINGS"] = "SEPARATE_BOOKINGS";
  MultiServiceBookingType2["PARALLEL_BOOKINGS"] = "PARALLEL_BOOKINGS";
  return MultiServiceBookingType2;
})(MultiServiceBookingType || {});
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNDEFINED"] = "UNDEFINED";
  LocationType2["OWNER_BUSINESS"] = "OWNER_BUSINESS";
  LocationType2["OWNER_CUSTOM"] = "OWNER_CUSTOM";
  LocationType2["CUSTOM"] = "CUSTOM";
  return LocationType2;
})(LocationType || {});
var ValueType = /* @__PURE__ */ ((ValueType2) => {
  ValueType2["SHORT_TEXT"] = "SHORT_TEXT";
  ValueType2["LONG_TEXT"] = "LONG_TEXT";
  ValueType2["CHECK_BOX"] = "CHECK_BOX";
  return ValueType2;
})(ValueType || {});
var BookingStatus = /* @__PURE__ */ ((BookingStatus2) => {
  BookingStatus2["CREATED"] = "CREATED";
  BookingStatus2["CONFIRMED"] = "CONFIRMED";
  BookingStatus2["CANCELED"] = "CANCELED";
  BookingStatus2["PENDING"] = "PENDING";
  BookingStatus2["DECLINED"] = "DECLINED";
  BookingStatus2["WAITING_LIST"] = "WAITING_LIST";
  return BookingStatus2;
})(BookingStatus || {});
var PaymentStatus = /* @__PURE__ */ ((PaymentStatus2) => {
  PaymentStatus2["UNDEFINED"] = "UNDEFINED";
  PaymentStatus2["NOT_PAID"] = "NOT_PAID";
  PaymentStatus2["PAID"] = "PAID";
  PaymentStatus2["PARTIALLY_PAID"] = "PARTIALLY_PAID";
  PaymentStatus2["REFUNDED"] = "REFUNDED";
  PaymentStatus2["EXEMPT"] = "EXEMPT";
  return PaymentStatus2;
})(PaymentStatus || {});
var SelectedPaymentOption = /* @__PURE__ */ ((SelectedPaymentOption2) => {
  SelectedPaymentOption2["UNDEFINED"] = "UNDEFINED";
  SelectedPaymentOption2["OFFLINE"] = "OFFLINE";
  SelectedPaymentOption2["ONLINE"] = "ONLINE";
  SelectedPaymentOption2["MEMBERSHIP"] = "MEMBERSHIP";
  SelectedPaymentOption2["MEMBERSHIP_OFFLINE"] = "MEMBERSHIP_OFFLINE";
  return SelectedPaymentOption2;
})(SelectedPaymentOption || {});
var Platform = /* @__PURE__ */ ((Platform2) => {
  Platform2["UNDEFINED_PLATFORM"] = "UNDEFINED_PLATFORM";
  Platform2["WEB"] = "WEB";
  Platform2["MOBILE_APP"] = "MOBILE_APP";
  return Platform2;
})(Platform || {});
var Actor = /* @__PURE__ */ ((Actor2) => {
  Actor2["UNDEFINED_ACTOR"] = "UNDEFINED_ACTOR";
  Actor2["BUSINESS"] = "BUSINESS";
  Actor2["CUSTOMER"] = "CUSTOMER";
  return Actor2;
})(Actor || {});
var IdentityType = /* @__PURE__ */ ((IdentityType2) => {
  IdentityType2["UNKNOWN"] = "UNKNOWN";
  IdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  IdentityType2["MEMBER"] = "MEMBER";
  IdentityType2["WIX_USER"] = "WIX_USER";
  IdentityType2["APP"] = "APP";
  return IdentityType2;
})(IdentityType || {});
async function previewPrice2(bookingLineItems) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingLineItems
  });
  const reqOpts = previewPrice(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingLineItems: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingLineItems"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function calculatePrice2(booking) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ booking });
  const reqOpts = calculatePrice(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { booking: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["booking"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-v2-price-info-pricing.public.ts
function previewPrice3(httpClient) {
  return (bookingLineItems) => previewPrice2(
    bookingLineItems,
    // @ts-ignore
    { httpClient }
  );
}
function calculatePrice3(httpClient) {
  return (booking) => calculatePrice2(
    booking,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-v2-price-info-pricing.context.ts
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
var previewPrice4 = /* @__PURE__ */ (0, import_rest_modules2.createRESTModule)(previewPrice3);
var calculatePrice4 = /* @__PURE__ */ (0, import_rest_modules2.createRESTModule)(calculatePrice3);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Actor,
  BookingStatus,
  IdentityType,
  LocationType,
  MultiServiceBookingType,
  PaymentStatus,
  Platform,
  SelectedPaymentOption,
  ValueType,
  calculatePrice,
  previewPrice
});
//# sourceMappingURL=index.js.map