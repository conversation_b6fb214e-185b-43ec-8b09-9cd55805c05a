"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  createBackup: () => createBackup2,
  deleteBackup: () => deleteBackup2,
  listBackups: () => listBackups2,
  listRestorations: () => listRestorations2,
  restoreBackup: () => restoreBackup2,
  restorePartialBackup: () => restorePartialBackup2
});
module.exports = __toCommonJS(meta_exports);

// src/data-v2-backup-backups.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressDstoreApiBackupV2BackupServiceUrl(opts) {
  const domainToMappings = {
    "www._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/wix-data/v1/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/wix-data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/data/v2/backups",
        destPath: "/v2/backups"
      },
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      }
    ],
    _: [
      {
        srcPath: "/data/v2/backups",
        destPath: "/v2/backups"
      },
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_data_backups";
function createBackup(payload) {
  function __createBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "POST",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.CreateBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "backup.requestedDate" },
            { path: "backup.startedDate" },
            { path: "backup.finishedDate" },
            { path: "backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBackup;
}
function listBackups(payload) {
  function __listBackups({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "GET",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.ListBackups",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "backups.requestedDate" },
            { path: "backups.startedDate" },
            { path: "backups.finishedDate" },
            { path: "backups.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listBackups;
}
function restoreBackup(payload) {
  function __restoreBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "POST",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.RestoreBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/{backupId}/restore",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "restoration.requestedDate" },
            { path: "restoration.startedDate" },
            { path: "restoration.finishedDate" },
            { path: "restoration.backup.requestedDate" },
            { path: "restoration.backup.startedDate" },
            { path: "restoration.backup.finishedDate" },
            { path: "restoration.backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __restoreBackup;
}
function restorePartialBackup(payload) {
  function __restorePartialBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "POST",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.RestorePartialBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/{backupId}/partial-restore",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "restoration.requestedDate" },
            { path: "restoration.startedDate" },
            { path: "restoration.finishedDate" },
            { path: "restoration.backup.requestedDate" },
            { path: "restoration.backup.startedDate" },
            { path: "restoration.backup.finishedDate" },
            { path: "restoration.backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __restorePartialBackup;
}
function listRestorations(payload) {
  function __listRestorations({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "GET",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.ListRestorations",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/restorations",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "restorations.requestedDate" },
            { path: "restorations.startedDate" },
            { path: "restorations.finishedDate" },
            { path: "restorations.backup.requestedDate" },
            { path: "restorations.backup.startedDate" },
            { path: "restorations.backup.finishedDate" },
            { path: "restorations.backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listRestorations;
}
function deleteBackup(payload) {
  function __deleteBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "DELETE",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.DeleteBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/{backupId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteBackup;
}

// src/data-v2-backup-backups.meta.ts
function createBackup2() {
  const payload = {};
  const getRequestOptions = createBackup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/backups",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function listBackups2() {
  const payload = {};
  const getRequestOptions = listBackups(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/backups",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function restoreBackup2() {
  const payload = { backupId: ":backupId" };
  const getRequestOptions = restoreBackup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/backups/{backupId}/restore",
    pathParams: { backupId: "backupId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function restorePartialBackup2() {
  const payload = { backupId: ":backupId" };
  const getRequestOptions = restorePartialBackup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/backups/{backupId}/partial-restore",
    pathParams: { backupId: "backupId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function listRestorations2() {
  const payload = {};
  const getRequestOptions = listRestorations(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/backups/restorations",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteBackup2() {
  const payload = { backupId: ":backupId" };
  const getRequestOptions = deleteBackup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v2/backups/{backupId}",
    pathParams: { backupId: "backupId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  createBackup,
  deleteBackup,
  listBackups,
  listRestorations,
  restoreBackup,
  restorePartialBackup
});
//# sourceMappingURL=meta.js.map