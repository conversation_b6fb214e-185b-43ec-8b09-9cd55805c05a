{"version": 3, "sources": ["../../../src/bookings-availability-v2-time-slot-event-time-slots.http.ts", "../../../src/bookings-availability-v2-time-slot-event-time-slots.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAvailabilityV2EventTimeSlotsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_event-time-slots';\n\n/**\n * Retrieves a list of class session time slots that match the provided filters.\n *\n * ## Defaults\n *\n * List Event Time Slots uses the following defaults:\n *\n * - `providerId` set to the Bookings app ID.\n * - `timeZone` set to the business time zone.\n * - `cursorPaging.limit` set to `50`.\n * - Returns both bookable and non-bookable time slots.\n *\n * Time slots are always sorted by their `localStartDate` in ascending order.\n *\n * ## Service type limitations\n *\n * To retrieve appointment availability, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).\n *\n * To retrieve course availability, follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flows#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course)).\n */\nexport function listEventTimeSlots(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listEventTimeSlots({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [{ path: 'bookingPolicyViolations.earliestBookingDate' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.EventTimeSlots.ListEventTimeSlots',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2EventTimeSlotsUrl({\n        protoPath: '/v2/time-slots/event',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlots.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listEventTimeSlots;\n}\n\n/**\n * Retrieves detailed information about a specific class session time slot.\n *\n *\n * Call this method after selecting a suitable slot with List Event Time Slots to obtain its full capacity and booking-policy status.\n *\n * ## Service type limitations\n *\n * To retrieve appointment availability, call Get Availability Time Slot ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability/availability-time-slots/get-availability-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot)).\n *\n * To retrieve course availability, follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flow#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n */\nexport function getEventTimeSlot(payload: object): RequestOptionsFactory<any> {\n  function __getEventTimeSlot({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.EventTimeSlots.GetEventTimeSlot',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2EventTimeSlotsUrl({\n        protoPath: '/v2/time-slots/event/{eventId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlot.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getEventTimeSlot;\n}\n", "import * as ambassadorWixBookingsAvailabilityV2TimeSlot from './bookings-availability-v2-time-slot-event-time-slots.http.js';\nimport * as ambassadorWixBookingsAvailabilityV2TimeSlotTypes from './bookings-availability-v2-time-slot-event-time-slots.types.js';\nimport * as ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes from './bookings-availability-v2-time-slot-event-time-slots.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function listEventTimeSlots(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.ListEventTimeSlotsRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.ListEventTimeSlotsRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.ListEventTimeSlotsResponse,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.ListEventTimeSlotsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.listEventTimeSlots(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/time-slots/event',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getEventTimeSlot(): __PublicMethodMetaInfo<\n  'GET',\n  { eventId: string },\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.GetEventTimeSlotRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.GetEventTimeSlotRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.GetEventTimeSlotResponse,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.GetEventTimeSlotResponse\n> {\n  const payload = { eventId: ':eventId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.getEventTimeSlot(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/time-slots/event/{eventId}',\n    pathParams: { eventId: 'eventId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,0DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAsBd,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,8CAA8C,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,0DAA0D;AAAA,QAC7D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAcO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,0DAA0D;AAAA,QAC7D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC/NO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACwC,mBAAmB,OAAO;AAExE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,oBAOd;AACA,QAAM,UAAU,EAAE,SAAS,WAAW;AAEtC,QAAM,oBACwC,iBAAiB,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,SAAS,UAAU;AAAA,IACjC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "listEventTimeSlots", "getEventTimeSlot"]}