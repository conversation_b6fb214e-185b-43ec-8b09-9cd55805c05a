import { ListCategoryRequest as ListCategoryRequest$1, ListCategoryResponse as ListCategoryResponse$1, CreateCategoryRequest as CreateCategoryRequest$1, CreateCategoryResponse as CreateCategoryResponse$1, UpdateCategoryRequest as UpdateCategoryRequest$1, UpdateCategoryResponse as UpdateCategoryResponse$1, DeleteCategoryRequest as DeleteCategoryRequest$1, DeleteCategoryResponse as DeleteCategoryResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

/** Categories are used to group multiple services together. A service must be associated with a category in order to be exposed in the Wix Bookings UI. */
interface Category {
    /**
     * Category ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Category name.
     * @maxLength 500
     */
    name?: string | null;
    /**
     * Category status.
     *
     * Default: `CREATED`
     * @readonly
     */
    status?: StatusWithLiterals;
    /** Sort order of the category in the live site and dashboard. */
    sortOrder?: number | null;
}
declare enum Status {
    /** The category was created. */
    CREATED = "CREATED",
    /** The category was deleted. */
    DELETED = "DELETED"
}
/** @enumType */
type StatusWithLiterals = Status | 'CREATED' | 'DELETED';
interface ListCategoryRequest {
    /**
     * IDs of the categories to retrieve.
     *
     * Default: All categories are retrieved.
     * @format GUID
     */
    categoryIds?: string[];
    /**
     * Whether to include deleted categories in the response.
     *
     * Default: `false`
     */
    includeDeleted?: boolean | null;
}
interface ListCategoryResponse {
    /** Retrieved categories. */
    categories?: Category[];
}
interface CreateCategoryRequest {
    /** Category to create. */
    category: Category;
}
interface CreateCategoryResponse {
    /** Created category. */
    category?: Category;
}
interface UpdateCategoryRequest {
    /** Category to update. */
    category: Category;
}
interface UpdateCategoryResponse {
    /** Updated category. */
    category?: Category;
}
interface DeleteCategoryRequest {
    /**
     * ID of the category to delete.
     * @format GUID
     */
    id: string | null;
    /**
     * Whether to delete all the services associated with the category.
     *
     * Default: `false`
     */
    deleteServices?: boolean | null;
}
interface DeleteCategoryResponse {
    /**
     * ID of the deleted category.
     * @format GUID
     */
    id?: string | null;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function listCategories(): __PublicMethodMetaInfo<'GET', {}, ListCategoryRequest$1, ListCategoryRequest, ListCategoryResponse$1, ListCategoryResponse>;
declare function createCategory(): __PublicMethodMetaInfo<'POST', {}, CreateCategoryRequest$1, CreateCategoryRequest, CreateCategoryResponse$1, CreateCategoryResponse>;
declare function updateCategory(): __PublicMethodMetaInfo<'PUT', {
    categoryId: string;
}, UpdateCategoryRequest$1, UpdateCategoryRequest, UpdateCategoryResponse$1, UpdateCategoryResponse>;
declare function deleteCategory(): __PublicMethodMetaInfo<'DELETE', {
    id: string;
}, DeleteCategoryRequest$1, DeleteCategoryRequest, DeleteCategoryResponse$1, DeleteCategoryResponse>;

export { type __PublicMethodMetaInfo, createCategory, deleteCategory, listCategories, updateCategory };
