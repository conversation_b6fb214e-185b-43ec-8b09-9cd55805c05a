import { QueryExtendedBookingRequest as QueryExtendedBookingRequest$1, QueryExtendedBookingResponse as QueryExtendedBookingResponse$1, QueryExtendedBookingsRequest as QueryExtendedBookingsRequest$1, QueryExtendedBookingsResponse as QueryExtendedBookingsResponse$1, CountExtendedBookingsRequest as CountExtendedBookingsRequest$1, CountExtendedBookingsResponse as CountExtendedBookingsResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

/** Extended bookings. */
interface ExtendedBooking {
    /** Booking. */
    booking?: Booking;
    /**
     * Information about the actions the customer can perform for the
     * booking. Available only when you've specified `withBookingAllowedActions` as
     * `true`.
     */
    allowedActions?: AllowedActions;
    /**
     * Information about the attendance. Available only when passing
     * `withBookingAttendanceInfo` as `true`.
     */
    attendance?: Attendance;
    /**
     * Information about the online conferencing details. Available only when passing
     * `withBookingConferencingDetails` as `true`.
     */
    conferencingDetails?: ConferencingDetails;
    /**
     * Information about the booking's policy settings according to the relevant
     * booking policy snapshot.
     *
     * Available only when you've specified `withBookingPolicySettings` as `true`.
     */
    bookingPolicySettings?: BookingPolicySettings;
    /**
     * Information about booking fee
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction))
     * statuses. Available only if you've specified
     * `withBookingFeeDetails` as `true`.
     *
     * Only details for fees that have already been applied to a booking are
     * returned. No details are provided for the following scenarios:
     * - Hypothetical fees related to a potential future cancellation by the customer.
     * - Scenarios where the booking has been canceled before the start of the earliest cancellation window that includes a penalty.
     *
     * To retrieve hypothetical booking fees, call List Booking Fees By Booking Ids
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/list-booking-fees-by-booking-ids) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/list-booking-fees-by-booking-ids))
     * instead.
     */
    bookingFeeDetails?: BookingFeeDetails;
    /**
     * Information about the booking order.
     * Available only when specified `withEcomOrder` as `true`.
     */
    order?: Order;
    /**
     * Information about the booking waiver.
     * Available only when specified `withWaiver` as `true`.
     */
    waiver?: Waiver;
    /**
     * Information about the booking order invoices.
     * Available only when specified `withEcomInvoices` as `true`.
     */
    invoices?: OrderInvoices;
    /**
     * Information about the booking order transactions.
     * Available only when specified `withEcomTransactions` as `true`.
     */
    transactions?: OrderTransactions;
    /**
     * Information about the booking form submission.
     * Available only when specified `withFormSubmissions` as `true`.
     * @maxSize 100
     */
    formSubmissions?: FormSubmission[];
}
declare enum AttendanceStatus {
    /** There is no available attendance information. */
    NOT_SET = "NOT_SET",
    /** At least a single participant attended the session. */
    ATTENDED = "ATTENDED",
    /** No participants attended the session. */
    NOT_ATTENDED = "NOT_ATTENDED"
}
/** @enumType */
type AttendanceStatusWithLiterals = AttendanceStatus | 'NOT_SET' | 'ATTENDED' | 'NOT_ATTENDED';
/** An entity representing a scheduled appointment, class session, or course. */
interface Booking extends BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
    /**
     * Booking ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * An object describing the bookable entity - either a specific time slot or a recurring schedule.
     *
     * The structure depends on the type of service being booked:
     *
     * *For appointment services:** Use `slot` to book a specific time slot with a
     * service provider. Appointments are typically one-time sessions at a specific date and time.
     *
     * *For class services:** Use `slot` to book a specific class session. Classes
     * are individual sessions that can have multiple participants.
     *
     * *For course services:** Use `schedule` to book an entire course consisting of
     * multiple sessions over time. Courses are recurring, multi-session offerings.
     *
     * Choose the appropriate field based on your service type and booking requirements.
     */
    bookedEntity?: BookedEntity;
    /**
     * Contact details of the site visitor or member
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/members/members/introduction) | [REST](https://dev.wix.com/docs/rest/crm/members-contacts/members/members/introduction))
     * making the booking.
     */
    contactDetails?: ContactDetails;
    /** Additional custom fields submitted with the booking form. */
    additionalFields?: CustomFormField[];
    /**
     * Booking status. A booking is automatically confirmed if the service allows it
     * and an eCommerce order is created. It is automatically declined if there is a
     * double booking and the customer hasn't paid or is eligible for an automatic
     * refund. Currently, only payments with pricing plans are automatically refundable.
     */
    status?: BookingStatusWithLiterals;
    /**
     * The payment status of the booking. This field automatically syncs with the
     * `paymentStatus` of the corresponding eCommerce order
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup)| [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))
     * when customers use Wix eCommerce checkout.
     *
     * ## Integration patterns
     *
     * *When using Wix eCommerce checkout:** Wix Bookings automatically syncs the payment status based on the eCommerce order's payment status.
     * Do not manually update this field.
     *
     * *When using custom payment flows:** You can manually update the payment status with Confirm Booking or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) to reflect the customer's payment state.
     *
     * *For membership/pricing plan payments:** Wix Bookings automatically manages the payment status when customers pay with an active pricing plan ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/introduction)) subscription.
     *
     * All payment statuses are supported for every booking `status`.
     */
    paymentStatus?: BookingPaymentStatusWithLiterals;
    /**
     * Payment option selected by the customer. If the customer hasn't completed their checkout, they may still change the payment method. Must be one of the payment options offered by the service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)), unless `skipSelectedPaymentOptionValidation` is `true`.
     *
     * When undefined during an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)), Wix Bookings uses the service's default payment option
     */
    selectedPaymentOption?: SelectedPaymentOptionWithLiterals;
    /**
     * Date and time the booking was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
    /** External user ID that you can provide. */
    externalUserId?: string | null;
    /**
     * Revision number to be used when updating, rescheduling, or cancelling the booking.
     * Increments by 1 each time the booking is updated, rescheduled, or canceled. To prevent conflicting changes, the current revision must be specified when updating the booking.
     */
    revision?: string | null;
    /**
     * ID of the creator of the booking.
     * If `appId` and another ID are present, the other ID takes precedence.
     * @readonly
     */
    createdBy?: IdentificationData;
    /**
     * The start date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the start date of the slot. For a schedule, this is the start date of the first session.
     * @readonly
     */
    startDate?: Date | null;
    /**
     * The end date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the end date of the slot. For a schedule, this is the end date of the last session.
     * @readonly
     */
    endDate?: Date | null;
    /**
     * Date and time the booking was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Custom field data for this object.
     * Extended fields must be configured in the app dashboard before they can be accessed with API calls.
     */
    extendedFields?: ExtendedFields;
    /**
     * Whether this booking overlaps with another confirmed booking. Returned
     * only if set to `true`.
     * @readonly
     */
    doubleBooked?: boolean | null;
}
/** @oneof */
interface BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
}
interface BookedEntity extends BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
    /**
     * Session title at the time of booking. If there is no pre-existing session,
     * for example for appointment-based services, Wix Bookings sets `title` to the service name.
     * @readonly
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * List of tags for the booking.
     *
     * - "INDIVIDUAL": For bookings of appointment-based services. Including when the appointment is for a group of participants.
     * - "GROUP": For bookings of individual class sessions.
     * - "COURSE": For course bookings.
     */
    tags?: string[] | null;
}
/** @oneof */
interface BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
}
interface BookedSlot {
    /** Session ID. */
    sessionId?: string | null;
    /** Service ID. */
    serviceId?: string;
    /** Schedule ID. */
    scheduleId?: string;
    /**
     * ID of the corresponding event
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).
     * Available for both appointment and class bookings, not available for course bookings.
     * For appointment-based services, Wix Bookings automatically populates `eventId` when the booking `status` changes to `CONFIRMED`.
     * For class bookings, Wix Bookings automatically populates `eventId` upon booking creation.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
    /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    startDate?: string | null;
    /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    endDate?: string | null;
    /** The time zone according to which the slot was shown to the user when booking, and should be shown in the future. */
    timezone?: string | null;
    /**
     * Primary resource
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) for the booking.
     * For example, the staff member ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) providing the service.
     */
    resource?: BookedResource;
    /** Location where the session takes place. */
    location?: Location;
}
interface BookedResource {
    /**
     * ID of the booking's primary resource.
     * @format GUID
     */
    id?: string;
    /**
     * Resource's name at the time of booking.
     * @maxLength 40
     */
    name?: string | null;
    /**
     * Resource's email at the time of booking.
     * @maxLength 500
     */
    email?: string | null;
    /**
     * ID of the schedule belonging to the booking's primary resource.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface Location {
    /**
     * Business location ID. Available only for locations that are business locations,
     * meaning the `location_type` is `"OWNER_BUSINESS"`.
     * @format GUID
     */
    id?: string | null;
    /** Location name. */
    name?: string | null;
    /** The full address of this location. */
    formattedAddress?: string | null;
    /**
     * The full translated address of this location.
     * @maxLength 512
     */
    formattedAddressTranslated?: string | null;
    /**
     * Location type.
     *
     * - `"OWNER_BUSINESS"`: The business address, as set in the site’s general settings.
     * - `"OWNER_CUSTOM"`: The address as set when creating the service.
     * - `"CUSTOM"`: The address as set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface BookedSchedule {
    /** Schedule ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)). */
    scheduleId?: string;
    /**
     * Booked service ID.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Location ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)) where the schedule's sessions take place.
     * @readonly
     */
    location?: Location;
    /**
     * Time zone in which the slot or session was shown to the customer when they booked.
     * Also used whenever the customer reviews the booking's timing in the future.
     */
    timezone?: string | null;
    /**
     * Start time of the first session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    firstSessionStart?: string | null;
    /**
     * End time of the last session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    lastSessionEnd?: string | null;
}
interface ContactDetails {
    /**
     * Contact ID.
     * @format GUID
     */
    contactId?: string | null;
    /**
     * Contact's first name. When populated from a standard booking form, this
     * property corresponds to the `name` field.
     */
    firstName?: string | null;
    /** Contact's last name. */
    lastName?: string | null;
    /**
     * Contact's email. If no [contact](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/contacts/contact-v4/contact-object)
     * with this email exist, a new contact is created.
     * Used to validate coupon usage limitations per contact. If not specified,
     * the coupon usage limitation will not be enforced. (Coupon usage limitation
     * validation is not supported yet).
     * @format EMAIL
     */
    email?: string | null;
    /** Contact's phone number. */
    phone?: string | null;
    /** Contact's full address. */
    fullAddress?: Address;
    /**
     * Contact's time zone.
     * @deprecated
     */
    timeZone?: string | null;
    /**
     * Contact's country in [ISO 3166-1 alpha-2 code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
     * format.
     * @format COUNTRY
     */
    countryCode?: string | null;
}
/** Physical address */
interface Address extends AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
    /**
     * Country code.
     * @format COUNTRY
     */
    country?: string | null;
    /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /** Zip/postal code. */
    postalCode?: string | null;
    /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */
    addressLine2?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Free text to help find the address. */
    hint?: string | null;
    /** Coordinates of the physical address. */
    geocode?: AddressLocation;
    /** Country full name. */
    countryFullname?: string | null;
    /** Multi-level subdivisions from top to bottom. */
    subdivisions?: Subdivision[];
}
/** @oneof */
interface AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
}
interface StreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
interface AddressLocation {
    /** Address latitude. */
    latitude?: number | null;
    /** Address longitude. */
    longitude?: number | null;
}
interface Subdivision {
    /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    code?: string;
    /** Subdivision full name. */
    name?: string;
}
interface CustomFormField {
    /**
     * ID of the form field as defined in the form.
     * @format GUID
     */
    id?: string;
    /** Value that was submitted for this field. */
    value?: string | null;
    /**
     * Form field's label at the time of submission.
     * @readonly
     */
    label?: string | null;
    valueType?: ValueTypeWithLiterals;
}
declare enum ValueType {
    /** Short text. This is the default value type. */
    SHORT_TEXT = "SHORT_TEXT",
    /** Long text. */
    LONG_TEXT = "LONG_TEXT",
    /** A text that represents the check box value. If selected the value is `true`, otherwise `false`. */
    CHECK_BOX = "CHECK_BOX"
}
/** @enumType */
type ValueTypeWithLiterals = ValueType | 'SHORT_TEXT' | 'LONG_TEXT' | 'CHECK_BOX';
/** Booking status. */
declare enum BookingStatus {
    /** The booking was created, but the customer hasn't completed the related eCommerce order yet. */
    CREATED = "CREATED",
    /**
     * The merchant has confirmed the booking and it appears in the business calendar. Merchants can set up their services
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) to automatically confirm all `PENDING` bookings.
     */
    CONFIRMED = "CONFIRMED",
    /**
     * The customer has canceled the booking. Depending on the relevant service's policy snapshot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
     * they may have to pay a cancellation fee ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction)).
     */
    CANCELED = "CANCELED",
    /** The merchant must manually confirm the booking before it appears in the business calendar. */
    PENDING = "PENDING",
    /** The merchant has declined the booking before the customer was charged. */
    DECLINED = "DECLINED",
    /**
     * The booking is on a waitlist.
     * Currently, you can't call Register to Waitlist ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/waitlist/register-to-waitlist)) for course or appointment bookings, even though this is supported in live sites.
     * You can call Register to Waitlist only for class session bookings.
     */
    WAITING_LIST = "WAITING_LIST"
}
/** @enumType */
type BookingStatusWithLiterals = BookingStatus | 'CREATED' | 'CONFIRMED' | 'CANCELED' | 'PENDING' | 'DECLINED' | 'WAITING_LIST';
/**
 * Payment status.
 * Automatically updated when using eCommerce checkout APIs.
 */
declare enum BookingPaymentStatus {
    /** Undefined payment status. */
    UNDEFINED = "UNDEFINED",
    /** The booking isn't paid. */
    NOT_PAID = "NOT_PAID",
    /** The booking is fully paid. */
    PAID = "PAID",
    /** The booking is partially paid. */
    PARTIALLY_PAID = "PARTIALLY_PAID",
    /** The booking is refunded. */
    REFUNDED = "REFUNDED",
    /** The booking is free of charge. */
    EXEMPT = "EXEMPT"
}
/** @enumType */
type BookingPaymentStatusWithLiterals = BookingPaymentStatus | 'UNDEFINED' | 'NOT_PAID' | 'PAID' | 'PARTIALLY_PAID' | 'REFUNDED' | 'EXEMPT';
/**
 * Selected payment option.
 *
 * One of the payment options offered by the service.
 * This field is set when the user selects an option during booking.
 * If left undefined, the payment option is resolved by the service configuration on checkout.
 */
declare enum SelectedPaymentOption {
    /** Undefined payment option. */
    UNDEFINED = "UNDEFINED",
    /** Offline payment. */
    OFFLINE = "OFFLINE",
    /** Online payment. */
    ONLINE = "ONLINE",
    /** Payment using a Wix Pricing Plan. */
    MEMBERSHIP = "MEMBERSHIP",
    /**
     * Customers can pay only in person with a Wix Pricing Plan, while the Wix user
     * must manually redeem the pricing plan in the dashboard.
     */
    MEMBERSHIP_OFFLINE = "MEMBERSHIP_OFFLINE"
}
/** @enumType */
type SelectedPaymentOptionWithLiterals = SelectedPaymentOption | 'UNDEFINED' | 'OFFLINE' | 'ONLINE' | 'MEMBERSHIP' | 'MEMBERSHIP_OFFLINE';
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /**
     * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.
     * @format GUID
     */
    contactId?: string | null;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface ParticipantChoices {
    /**
     * Information about the booked service choices. Includes the number of participants.
     * @minSize 1
     * @maxSize 20
     */
    serviceChoices?: ServiceChoices[];
}
interface ServiceChoices {
    /**
     * Number of participants for this variant ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    numberOfParticipants?: number | null;
    /**
     * Service choices for these participants.
     * @maxSize 5
     */
    choices?: ServiceChoice[];
}
interface ServiceChoice extends ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
    /**
     * ID of the corresponding option for the choice. For example, the choice `child`
     * could correspond to the option `ageGroup`. In this case, `optionId` is the ID
     * for the `ageGroup` option.
     * @format GUID
     */
    optionId?: string;
}
/** @oneof */
interface ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
}
interface Duration {
    /**
     * Duration of the service in minutes.
     * Min: 1 minute, Max: 30 days, 23 hours, and 59 minutes
     * @min 1
     * @max 44639
     */
    minutes?: number;
    /**
     * Name of the duration option.
     * Defaults to the formatted duration e.g. "1 hour, 30 minutes".
     * @maxLength 255
     */
    name?: string | null;
}
/** Possible allowed actions for a Booking */
interface AllowedActions {
    /** Whether the customer is allowed to cancel the booking. */
    cancel?: boolean;
    /** Whether the customer is allowed to reschedule the booking. */
    reschedule?: boolean;
    /**
     * Deprecated.
     * Whether the customer is entitled to a refund when canceling the booking.
     * @deprecated
     */
    refund?: boolean | null;
}
interface Attendance {
    /**
     * ID of the attendance object.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /** General information about the booking's attendance. */
    status?: AttendanceStatusWithLiterals;
    /**
     * Total number of participants who attended the session. Can
     * be greater than `1` for bookings with multiple participants.
     */
    numberOfAttendees?: number;
}
interface ConferencingDetails {
    /**
     * URL used by a guest to join the conference.
     * @minLength 1
     * @maxLength 2000
     */
    guestUrl?: string | null;
    /**
     * Conference password.
     * @minLength 1
     * @maxLength 100
     */
    password?: string | null;
}
interface BookingPolicySettings {
    /** Whether the booking has an active cancellation fee policy. */
    cancellationFeeEnabled?: boolean | null;
}
interface BookingFeeDetails {
    /** Cancellation fee status. */
    cancellationFeeStatus?: BookingFeeStatusWithLiterals;
}
declare enum BookingFeeStatus {
    /** There is no information about the booking fee status. */
    UNKNOWN_STATUS = "UNKNOWN_STATUS",
    /** The booking fee hasn't been applied to the eCommerce order yet. */
    NOT_YET_APPLIED_TO_ORDER = "NOT_YET_APPLIED_TO_ORDER",
    /** The booking fee has already been applied as an additional fee to the eCommerce order. */
    APPLIED_TO_ORDER = "APPLIED_TO_ORDER"
}
/** @enumType */
type BookingFeeStatusWithLiterals = BookingFeeStatus | 'UNKNOWN_STATUS' | 'NOT_YET_APPLIED_TO_ORDER' | 'APPLIED_TO_ORDER';
interface Order {
    /**
     * Order ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Order number displayed in the site owner's dashboard (auto-generated).
     * @readonly
     */
    number?: string;
    /**
     * Order price summary.
     * @readonly
     */
    priceSummary?: PriceSummary;
    /**
     * Order line items.
     * @minSize 1
     * @maxSize 300
     * @readonly
     */
    lineItems?: OrderLineItem[];
    /** Order status. */
    status?: OrderStatusWithLiterals;
    /** Order payment status. */
    paymentStatus?: PaymentStatusWithLiterals;
    /**
     * Balance summary.
     * @readonly
     */
    balanceSummary?: BalanceSummary;
    /**
     * Currency used for the pricing of this order in [ISO-4217](https://en.wikipedia.org/wiki/ISO_4217#List_of_ISO_4217_currency_codes) format.
     * @format CURRENCY
     */
    currency?: string | null;
}
interface PriceSummary {
    /** Subtotal of all the line items, before discounts and before tax. */
    subtotal?: Price;
    /** Total shipping price, before discounts and before tax. */
    shipping?: Price;
    /** Total tax on this order. */
    tax?: Price;
    /** Total calculated discount value. */
    discount?: Price;
    /** Order’s total price after discounts and tax. */
    total?: Price;
    /** Total price of additional fees before tax. */
    totalAdditionalFees?: Price;
}
interface Price {
    /**
     * Amount.
     * @decimalValue options { gte:0, lte:1000000000000000 }
     */
    amount?: string;
    /**
     * Amount formatted with currency symbol.
     * @readonly
     */
    formattedAmount?: string;
}
interface OrderLineItem {
    /**
     * Line item ID.
     * @immutable
     */
    id?: string;
    /**
     * References to the line item's origin catalog.
     * This field may be empty in the case of a custom line item.
     */
    catalogReference?: CatalogReference;
    /** Line item price after line item discounts for display purposes. */
    price?: Price;
    /**
     * Type of selected payment option for current item.
     *
     * Default: `FULL_PAYMENT_ONLINE`
     */
    paymentOption?: PaymentOptionTypeWithLiterals;
    /** Additional description for the price. For example, when price is 0 but additional details about the actual price are needed - "Starts at $67". */
    priceDescription?: PriceDescription;
}
/** Used for grouping line items. Sent when an item is added to a cart, checkout, or order. */
interface CatalogReference {
    /**
     * ID of the item within the catalog it belongs to.
     * @minLength 1
     * @maxLength 36
     */
    catalogItemId?: string;
    /**
     * ID of the app providing the catalog.
     *
     * You can get your app's ID from its page in the [app dashboard](https://dev.wix.com/dc3/my-apps/).
     *
     * For items from Wix catalogs, the following values always apply:
     * + Wix Stores: `"215238eb-22a5-4c36-9e7b-e7c08025e04e"`
     * + Wix Bookings: `"13d21c63-b5ec-5912-8397-c3a5ddb27a97"`
     * + Wix Restaurants: `"9a5d83fd-8570-482e-81ab-cfa88942ee60"`
     * @minLength 1
     */
    appId?: string;
    /**
     * Additional item details in key:value pairs. Use this optional field to provide more specificity with item selection. The `options` field values differ depending on which catalog is providing the items.
     *
     * For products and variants from a Wix Stores catalog, learn more about eCommerce integration ([SDK](https://dev.wix.com/docs/sdk/backend-modules/stores/catalog-v3/e-commerce-integration) | [REST](https://dev.wix.com/docs/rest/business-solutions/stores/catalog/e-commerce-integration)).
     */
    options?: Record<string, any> | null;
}
/** Type of selected payment option for catalog item */
declare enum PaymentOptionType {
    /** The entire payment for this item happens as part of the checkout. */
    FULL_PAYMENT_ONLINE = "FULL_PAYMENT_ONLINE",
    /** The entire payment for this item happens after checkout. For example, when using cash, check, or other offline payment methods. */
    FULL_PAYMENT_OFFLINE = "FULL_PAYMENT_OFFLINE",
    /** Payment for this item is done by charging a membership. Any remaining amount not covered by the membership, such as item modifiers, is paid online. */
    MEMBERSHIP = "MEMBERSHIP",
    /** Partial payment to be paid upfront during checkout. The initial amount to be paid for each line item is specified in `depositAmount`. */
    DEPOSIT_ONLINE = "DEPOSIT_ONLINE",
    /** Payment for this item can only be done by charging a membership and must be manually redeemed in the dashboard by the site admin. When selected, `price` is `0`. */
    MEMBERSHIP_OFFLINE = "MEMBERSHIP_OFFLINE",
    /**
     * Item price is charged to online membership. Any remaining amount not covered by the membership, such as item modifiers, is paid offline.
     * @documentationMaturity preview
     */
    MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER = "MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER"
}
/** @enumType */
type PaymentOptionTypeWithLiterals = PaymentOptionType | 'FULL_PAYMENT_ONLINE' | 'FULL_PAYMENT_OFFLINE' | 'MEMBERSHIP' | 'DEPOSIT_ONLINE' | 'MEMBERSHIP_OFFLINE' | 'MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER';
interface PriceDescription {
    /**
     * __Required.__ Price description in the site's default language as defined in the [request envelope](https://dev.wix.com/docs/build-apps/develop-your-app/frameworks/self-hosting/supported-extensions/backend-extensions/add-self-hosted-service-plugin-extensions#request-envelope).
     * @minLength 1
     * @maxLength 100
     */
    original?: string;
    /**
     * Price description translated into the buyer's language.
     *
     * Default: Same as `original`.
     * @minLength 1
     * @maxLength 100
     */
    translated?: string | null;
}
declare enum OrderStatus {
    /** Order created, but not yet approved or canceled. */
    INITIALIZED = "INITIALIZED",
    /**
     * Order approved.
     *
     * This happens when either an online payment is received **or** when `order.priceSummary.total = 0` (a zero-total order).
     * Offline orders (cash payment) are automatically approved.
     */
    APPROVED = "APPROVED",
    /** Order canceled by the user. */
    CANCELED = "CANCELED",
    /**
     * Order pending.
     * @documentationMaturity preview
     */
    PENDING = "PENDING",
    /**
     * Order rejected.
     *
     * This happens when pending payments fail.
     * @documentationMaturity preview
     */
    REJECTED = "REJECTED"
}
/** @enumType */
type OrderStatusWithLiterals = OrderStatus | 'INITIALIZED' | 'APPROVED' | 'CANCELED' | 'PENDING' | 'REJECTED';
declare enum PaymentStatus {
    UNSPECIFIED = "UNSPECIFIED",
    /**
     * `NOT_PAID` can apply to an order made online, but not yet paid. In such cases `order.status` will be `INITIALIZED`.
     * This status also applies when an offline order needs to be manually marked as paid. In such cases `order.status` will be `APPROVED`.
     */
    NOT_PAID = "NOT_PAID",
    /** All required payments associated with this order are paid. */
    PAID = "PAID",
    /** Order partially refunded, but the refunded amount is less than the order's total price. See `order.balanceSummary` for more details. */
    PARTIALLY_REFUNDED = "PARTIALLY_REFUNDED",
    /** Order fully refunded. Refund amount equals total price. See `order.balanceSummary` for more details. */
    FULLY_REFUNDED = "FULLY_REFUNDED",
    /**
     * All payments pending.
     *
     * This can happen with two-step payments, when a payment requires manual review, or when a payment is in progress and will be concluded shortly.
     * Learn more about [pending orders](https://support.wix.com/en/article/pending-orders).
     */
    PENDING = "PENDING",
    /** At least one payment received and approved, but it covers less than the order's total price. See `order.balanceSummary` for more details. */
    PARTIALLY_PAID = "PARTIALLY_PAID",
    /**
     * Payment received, but not yet confirmed by the payment provider.
     *
     * In most cases, when a payment provider is holding payment it's because setup hasn't been successfully completed by the merchant/site owner.
     * To solve this, the merchant/site owner should log in to the payment provider's dashboard and make sure their account is set up correctly, or contact their support for further assistance.
     * @documentationMaturity preview
     */
    PENDING_MERCHANT = "PENDING_MERCHANT",
    /**
     * One or more payments canceled.
     * @documentationMaturity preview
     */
    CANCELED = "CANCELED",
    /**
     * One or more payments declined.
     * @documentationMaturity preview
     */
    DECLINED = "DECLINED"
}
/** @enumType */
type PaymentStatusWithLiterals = PaymentStatus | 'UNSPECIFIED' | 'NOT_PAID' | 'PAID' | 'PARTIALLY_REFUNDED' | 'FULLY_REFUNDED' | 'PENDING' | 'PARTIALLY_PAID' | 'PENDING_MERCHANT' | 'CANCELED' | 'DECLINED';
interface BalanceSummary {
    /**
     * Current amount left to pay.
     * @readonly
     */
    balance?: Balance;
    /**
     * The value includes payments that have subsequently been fully or partially refunded.
     * @readonly
     */
    paid?: Price;
}
/**
 * Order balance. Reflects amount left to be paid on order and is calculated dynamically. Can be negative per balance definition.
 * `amount` field depends on order payment status:
 * + UNSPECIFIED, NOT_PAID: price_summary.total_price
 * + PARTIALLY_PAID : price_summary.total_price - pay_now.total_price
 * + PENDING, REFUNDED, PARTIALLY_REFUNDED, PAID : 0
 */
interface Balance {
    /**
     * Balance amount.
     *
     * A negative `amount` represents the amount to be refunded. This can happen due to overcharging or the order being modified after a payment has been made.
     * @decimalValue options { }
     * @readonly
     */
    amount?: string;
    /**
     * Amount formatted with currency symbol.
     * @readonly
     */
    formattedAmount?: string;
}
interface Waiver {
    /**
     * The ID of the waiver. This is a unique identifier for the waiver.
     * @format GUID
     */
    id?: string | null;
    /**
     * The ID of the waiver template.
     * @format GUID
     */
    waiverTemplateId?: string | null;
    /**
     * The ID of the contact associated with the waiver.
     * @format GUID
     */
    contactId?: string | null;
    /** The status of the waiver. */
    status?: StatusWithLiterals;
}
declare enum Status {
    UNDEFINED = "UNDEFINED",
    SIGNED = "SIGNED",
    NOT_SIGNED = "NOT_SIGNED"
}
/** @enumType */
type StatusWithLiterals = Status | 'UNDEFINED' | 'SIGNED' | 'NOT_SIGNED';
interface OrderInvoices {
    /** Order ID. */
    orderId?: string;
    /**
     * Invoices info.
     * @maxSize 300
     */
    invoicesInfo?: Invoice[];
}
interface Invoice {
    /** Invoice ID. */
    id?: string;
    /** ID of the app that set the invoice. */
    appId?: string;
}
interface OrderTransactions {
    /**
     * Order ID.
     * @format GUID
     */
    orderId?: string;
    /**
     * Record of payments made to the merchant.
     * @maxSize 100
     */
    payments?: Payment[];
    /**
     * Record of refunds made to the buyer.
     * @maxSize 300
     */
    refunds?: Refund[];
}
interface Payment extends PaymentPaymentDetailsOneOf, PaymentReceiptInfoOneOf {
    /** Regular payment details. */
    regularPaymentDetails?: RegularPaymentDetails;
    /** Gift card payment details. */
    giftcardPaymentDetails?: GiftCardPaymentDetails;
    /**
     * Payment ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /** Date and time the payment was created in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided. */
    createdDate?: Date | null;
    /**
     * Date and time the payment was last updated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format.
     * @readonly
     */
    updatedDate?: Date | null;
    /** Payment amount. */
    amount?: Price;
    /**
     * Whether refunds for this payment are disabled.
     * + `true`: This payment is not refundable.
     * + `false`: This payment may be refunded. However, this ultimately depends on the payment provider.
     */
    refundDisabled?: boolean;
}
/** @oneof */
interface PaymentPaymentDetailsOneOf {
    /** Regular payment details. */
    regularPaymentDetails?: RegularPaymentDetails;
    /** Gift card payment details. */
    giftcardPaymentDetails?: GiftCardPaymentDetails;
}
/** @oneof */
interface PaymentReceiptInfoOneOf {
}
interface RegularPaymentDetails extends RegularPaymentDetailsPaymentMethodDetailsOneOf {
    /** Whether regular card used */
    creditCardDetails?: CreditCardPaymentMethodDetails;
    /**
     * Wix Payments order ID.
     * @maxLength 100
     */
    paymentOrderId?: string | null;
    /**
     * Payment gateway's transaction ID.
     * This field is only returned when the value of `offline_payment` is `false`.
     * @maxLength 100
     */
    gatewayTransactionId?: string | null;
    /**
     * Payment method. Non-exhaustive list of supported values:
     * + `CreditCard`, `Alipay`, `AstropayCash`, `AstropayDBT`, `AstropayMBT`, `Bitcoin`, `BitPay`, `Cash`, `ConvenienceStore`, `EPay`, `Fake`, `Giropay`, `IDeal`, `InPerson`, `Klarna`, `MercadoPago`, `Netpay`, `NordeaSolo`, `Offline`, `PagSeguro`, `PayEasy`, `PayPal`, `Paysafecard`, `Paysafecash`, `PointOfSale`, `Poli`, `Privat24`, `Przelewy24`, `RapidTransfer`, `Sepa`, `Skrill`, `Sofort`, `Trustly`, `Neteller`, `Unionpay`, `UniPay`, `Yandex`
     * @maxLength 100
     */
    paymentMethod?: string | null;
    /**
     * Transaction ID in the payment provider's system. For example, at PayPal, Square, Stripe, etc. Not returned for offline payments.
     * @maxLength 100
     */
    providerTransactionId?: string | null;
    /** Whether the payment was made offline. For example, when using cash or when marked as paid in the Business Manager. */
    offlinePayment?: boolean;
    /** Payment status. */
    status?: TransactionStatusWithLiterals;
    /**
     * Whether there is a payment agreement that allows for future charges.
     * @immutable
     */
    savedPaymentMethod?: boolean;
    /** Authorization details. */
    authorizationDetails?: AuthorizationDetails;
    /**
     * Record of chargebacks made by the buyer.
     * @maxSize 6
     */
    chargebacks?: Chargeback[];
}
/** @oneof */
interface RegularPaymentDetailsPaymentMethodDetailsOneOf {
    /** Whether regular card used */
    creditCardDetails?: CreditCardPaymentMethodDetails;
}
declare enum TransactionStatus {
    UNDEFINED = "UNDEFINED",
    APPROVED = "APPROVED",
    PENDING = "PENDING",
    PENDING_MERCHANT = "PENDING_MERCHANT",
    CANCELED = "CANCELED",
    DECLINED = "DECLINED",
    REFUNDED = "REFUNDED",
    PARTIALLY_REFUNDED = "PARTIALLY_REFUNDED",
    AUTHORIZED = "AUTHORIZED",
    VOIDED = "VOIDED"
}
/** @enumType */
type TransactionStatusWithLiterals = TransactionStatus | 'UNDEFINED' | 'APPROVED' | 'PENDING' | 'PENDING_MERCHANT' | 'CANCELED' | 'DECLINED' | 'REFUNDED' | 'PARTIALLY_REFUNDED' | 'AUTHORIZED' | 'VOIDED';
interface CreditCardPaymentMethodDetails {
    /**
     * The last 4 digits of the card number.
     * @maxLength 4
     */
    lastFourDigits?: string | null;
    /**
     * Card issuer's brand.
     * @maxLength 100
     */
    brand?: string | null;
}
interface AuthorizationDetails {
    /**
     * Whether the authorized payment is of a delayed capture.
     * @readonly
     */
    delayedCapture?: boolean;
    /** Date and time the payment was authorized in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. */
    authorizedDate?: Date | null;
    /**
     * List of captures associated with payment
     * In case of failed it can be replaced with new one with PENDING or SUCCESS statuses
     * @maxSize 1
     */
    captures?: AuthorizationCapture[];
    /** Void associated with payment */
    void?: AuthorizationVoid;
    /** Scheduled action for this transaction */
    scheduledAction?: ScheduledAction;
}
interface AuthorizationCapture {
    /**
     * Capture ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /** Status of this capture action */
    status?: AuthorizationCaptureStatusWithLiterals;
    /**
     * Amount of this capture
     * @immutable
     */
    amount?: Price;
    /** Date and time the capture was initiated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. */
    createdDate?: Date | null;
    /** In case of status is FAILED may contain failure details */
    failureDetails?: AuthorizationActionFailureDetails;
}
declare enum AuthorizationCaptureStatus {
    UNKNOWN_STATUS = "UNKNOWN_STATUS",
    /** Capture operation still in progress. */
    PENDING = "PENDING",
    /** Capture operation succeeded. */
    SUCCEEDED = "SUCCEEDED",
    /** Capture operation failed. */
    FAILED = "FAILED"
}
/** @enumType */
type AuthorizationCaptureStatusWithLiterals = AuthorizationCaptureStatus | 'UNKNOWN_STATUS' | 'PENDING' | 'SUCCEEDED' | 'FAILED';
interface AuthorizationActionFailureDetails {
    /** @maxLength 100 */
    failureCode?: string;
}
interface AuthorizationVoid {
    /** Status of this void action */
    status?: AuthorizationVoidStatusWithLiterals;
    /** Date and time the void was initiated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. */
    voidedDate?: Date | null;
    /** In case of status is FAILED may contain failure details */
    failureDetails?: AuthorizationActionFailureDetails;
    /** Reason of void action */
    reason?: ReasonWithLiterals;
}
declare enum AuthorizationVoidStatus {
    UNKNOWN_STATUS = "UNKNOWN_STATUS",
    /** Void operation still in progress. */
    PENDING = "PENDING",
    /** Void operation succeeded. */
    SUCCEEDED = "SUCCEEDED",
    /** Void operation failed. */
    FAILED = "FAILED"
}
/** @enumType */
type AuthorizationVoidStatusWithLiterals = AuthorizationVoidStatus | 'UNKNOWN_STATUS' | 'PENDING' | 'SUCCEEDED' | 'FAILED';
/** Reason the authorization was voided. */
declare enum Reason {
    UNKNOWN_REASON = "UNKNOWN_REASON",
    /** Authorization was voided by user. */
    MANUAL = "MANUAL",
    /** Authorization passed execution date. */
    SCHEDULED = "SCHEDULED"
}
/** @enumType */
type ReasonWithLiterals = Reason | 'UNKNOWN_REASON' | 'MANUAL' | 'SCHEDULED';
interface ScheduledAction {
    /** Type of the action. */
    actionType?: ActionTypeWithLiterals;
    /** The date and time of the action. */
    executionDate?: Date | null;
}
declare enum ActionType {
    UNKNOWN_ACTION_TYPE = "UNKNOWN_ACTION_TYPE",
    VOID = "VOID",
    CAPTURE = "CAPTURE"
}
/** @enumType */
type ActionTypeWithLiterals = ActionType | 'UNKNOWN_ACTION_TYPE' | 'VOID' | 'CAPTURE';
interface Chargeback {
    /**
     * Chargeback ID.
     * @format GUID
     * @readonly
     * @immutable
     */
    id?: string;
    /**
     * Date and time the chargeback was created in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided.
     * @readonly
     * @immutable
     */
    createdDate?: Date | null;
    /**
     * Date and time the chargeback was updated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Amount.
     * @readonly
     * @immutable
     */
    amount?: Price;
    /**
     * Reversal amount. Present only when status is REVERSED.
     * @readonly
     */
    reversalAmount?: Price;
    /**
     * Status.
     *
     * Default: `"APPROVED"`.
     */
    status?: ChargebackStatusWithLiterals;
    /**
     * External chargeback ID.
     * @format GUID
     * @readonly
     * @immutable
     */
    externalId?: string | null;
}
declare enum ChargebackStatus {
    UNSPECIFIED = "UNSPECIFIED",
    /** Chargeback was approved. */
    APPROVED = "APPROVED",
    /** Chargeback was reversed. */
    REVERSED = "REVERSED"
}
/** @enumType */
type ChargebackStatusWithLiterals = ChargebackStatus | 'UNSPECIFIED' | 'APPROVED' | 'REVERSED';
interface GiftCardPaymentDetails {
    /**
     * Gift card payment ID.
     * @minLength 1
     * @maxLength 100
     */
    giftCardPaymentId?: string;
    /**
     * ID of the app that created the gift card.
     * @format GUID
     */
    appId?: string;
    /**
     * Whether the gift card is voided.
     * @readonly
     */
    voided?: boolean;
}
interface Refund {
    /**
     * Refund ID.
     * @format GUID
     * @readonly
     */
    id?: string;
    /**
     * List of transactions.
     * @maxSize 50
     */
    transactions?: RefundTransaction[];
    /** Refund business details. */
    details?: RefundDetails;
    /**
     * Date and time the refund was created in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided.
     * @readonly
     * @immutable
     */
    createdDate?: Date | null;
    /**
     * Aggregated refund summary.
     * @readonly
     */
    summary?: AggregatedRefundSummary;
    /**
     * ID of the app that initiated this refund.
     * @format GUID
     * @readonly
     * @immutable
     */
    requestingServiceAppId?: string | null;
}
interface RefundTransaction {
    /**
     * ID of the payment associated with this refund.
     * @format GUID
     * @immutable
     */
    paymentId?: string;
    /**
     * Refund amount.
     * @immutable
     */
    amount?: Price;
    /** Refund status. */
    refundStatus?: RefundStatusWithLiterals;
    /** Optional details of current refund status. */
    refundStatusInfo?: RefundStatusInfo;
    /**
     * Payment gateway's refund ID.
     * This field is only returned when the value of `external_refund` is `false`.
     * @format GUID
     */
    gatewayRefundId?: string | null;
    /** ID of the refund in the payment provider's system. For example, at PayPal, Square, Stripe, etc. Not returned for external refunds. */
    providerRefundId?: string | null;
    /**
     * Whether refund was made externally and manually on the payment provider's side.
     * @immutable
     */
    externalRefund?: boolean;
}
/** Refund transaction status. */
declare enum RefundStatus {
    /** Refund was initiated on payment provider side. PENDING status was assigned by provider. */
    PENDING = "PENDING",
    /** Refund transaction succeeded. */
    SUCCEEDED = "SUCCEEDED",
    /** Refund transaction failed. */
    FAILED = "FAILED",
    /** Refund request acknowledged, and will be executed soon. */
    SCHEDULED = "SCHEDULED",
    /** Refund was initiated on payment provider side. */
    STARTED = "STARTED"
}
/** @enumType */
type RefundStatusWithLiterals = RefundStatus | 'PENDING' | 'SUCCEEDED' | 'FAILED' | 'SCHEDULED' | 'STARTED';
interface RefundStatusInfo {
    /**
     * Reason code for the refund's current status.
     *
     * Learn more about [reason codes](https://dev.wix.com/docs/rest/business-management/payments/service-plugins/payment-service-provider-service-plugin/reason-codes).
     * @minLength 1
     * @maxLength 10
     */
    paymentGatewayReasonCode?: string | null;
    /**
     * Free text explanation of current refund status.
     * @minLength 1
     * @maxLength 1000
     */
    description?: string | null;
}
/** Business model of a refund request */
interface RefundDetails {
    /**
     * Order line item IDs and quantities that were refunded.
     * @maxSize 300
     */
    items?: RefundItem[];
    /** Whether the shipping fee was also refunded. */
    shippingIncluded?: boolean;
    /**
     * Reason for the refund, provided by customer (optional).
     * @maxLength 200
     */
    reason?: string | null;
    /**
     * Line items that were refunded.
     * @maxSize 300
     */
    lineItems?: LineItemRefund[];
    /**
     * Additional fees that were refunded.
     * @maxSize 100
     */
    additionalFees?: AdditionalFeeRefund[];
    /** Shipping amount that was refunded. */
    shipping?: ShippingRefund;
}
interface RefundItem {
    /**
     * Line item ID the refunded line item.
     * @format GUID
     */
    lineItemId?: string;
    /**
     * Line item quantity refunded.
     * @min 1
     * @max 100000
     */
    quantity?: number;
}
interface LineItemRefund {
    /**
     * Line item ID.
     * @format GUID
     * @immutable
     */
    lineItemId?: string;
    /**
     * Refund quantity.
     * @min 1
     * @max 100000
     * @immutable
     */
    quantity?: number;
}
interface AdditionalFeeRefund {
    /**
     * Additional fee ID.
     * @format GUID
     * @immutable
     */
    additionalFeeId?: string;
    /**
     * Refund amount.
     * @immutable
     */
    amount?: Price;
}
interface ShippingRefund {
    /**
     * Refund amount.
     * @immutable
     */
    amount?: Price;
}
interface AggregatedRefundSummary {
    /** Total amount requested for refund. */
    requestedRefund?: Price;
    /** Pending refund amount - the portion of `requestedRefund` that is still pending. */
    pendingRefund?: Price;
    /** Refunded amount - the portion of `requestedRefund` that refunded successfully. */
    refunded?: Price;
    /** Failed refund amount - the portion of `requestedRefund` that failed. */
    failedRefundAmount?: Price;
    /** Whether at least one refund transaction is still in `"PENDING"` status. */
    pending?: boolean;
    /** Breakdown of refunded items. Available only after refund is complete. */
    breakdown?: RefundItemsBreakdown;
}
interface RefundItemsBreakdown {
    /**
     * Refunded line items and the amount refunded for each.
     * @maxSize 300
     */
    lineItems?: LineItemRefundSummary[];
}
interface LineItemRefundSummary {
    /**
     * ID of the refunded line item.
     * @format GUID
     */
    lineItemId?: string;
    /** Total refunded amount for the line item. */
    totalRefundedAmount?: Price;
}
interface FormSubmission {
    /**
     * Label of form field
     * @maxLength 500
     */
    label?: string | null;
    /**
     * Key of the submission, and target of a form field
     * @maxLength 200
     */
    key?: string;
    /**
     * Value of the submission, formatted as a string
     * @maxLength 500
     */
    value?: string;
    /**
     * Id of the form field
     * @format GUID
     */
    fieldId?: string;
}
interface QueryExtendedBookingRequest {
    /** Information about filters, paging, and sorting. */
    query: QueryV2;
    /** Whether information about which actions the customer can perform for the bookings is returned. */
    withBookingAllowedActions?: boolean;
    /** Whether information about the attendance for the bookings is returned. */
    withBookingAttendanceInfo?: boolean;
    /**
     * Filters the retrieved bookings by the booking ID that corresponds to the
     * specified `sessionId`.
     *
     * The `booking.id` is calculated by calling Get Session and saving the returned
     * values for `participants.Id`.
     *
     * These participant IDs are then used as `booking.id`.
     * This filter overrides the `booking.id` filter inside the query object.
     *
     * __Note__: Bookings for courses don't include a `sessionId`. For these
     * bookings, you must use this field to filter by session ID.
     */
    sessionId?: string | null;
    /**
     * Whether to return information about the order from ecom.
     *
     * You must have the `ECOM.READ_ORDERS` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withEcomOrder?: boolean;
    /**
     * Whether to return information about the invoices from ecom.
     *
     * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_INVOICES` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withEcomInvoices?: boolean;
    /**
     * Whether to return information about the transactions from ecom.
     *
     * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_TRANSACTIONS` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withEcomTransactions?: boolean;
    /**
     * Whether to return information about the form submission.
     *
     * You must have the `WIX_FORMS.SUBMISSION_READ_ANY` and `BOOKINGS.BOOKING_FORM_SCHEMA_READ` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withFormSubmissions?: boolean;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     */
    sort?: Sorting[];
    /** Array of projected fields. A list of specific field names to return. If `fieldsets` are also specified, the union of `fieldsets` and `fields` is returned. */
    fields?: string[];
    /** Array of named, predefined sets of projected fields. A array of predefined named sets of fields to be returned. Specifying multiple `fieldsets` will return the union of fields from all sets. If `fields` are also specified, the union of `fieldsets` and `fields` is returned. */
    fieldsets?: string[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryExtendedBookingResponse {
    /**
     * Retrieved bookings.
     *
     * May include information about the attendance or actions the customer can
     * perform, depending on your request.
     */
    extendedBookings?: ExtendedBooking[];
    /** Paging metadata. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
    /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */
    cursors?: Cursors;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface QueryExtendedBookingsRequest {
    /** Information about filters, paging, and sorting. */
    query: CommonQueryV2;
    /** Whether to return information about the actions the customer can perform for the bookings. */
    withBookingAllowedActions?: boolean;
    /** Whether to return information about the attendance for the bookings. */
    withBookingAttendanceInfo?: boolean;
    /** Whether to return information about the online conferencing details for the bookings. */
    withBookingConferencingDetails?: boolean;
    /**
     * Whether to retrieve information about booking policy settings.
     *
     * You must have the `BOOKINGS.BOOKING_POLICY_SNAPSHOT_READ` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withBookingPolicySettings?: boolean;
    /**
     * Whether to retrieve information about booking fee
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction))
     * statuses.
     *
     * Information about booking fees with a status of `PREVIEW` isn't returned,
     * and only fees that have already been applied are included in the response.
     * If there are hypothetical fees for a booking that may be canceled in the
     * future, these fees aren't returned. To retrieve such fees, call
     * List Booking Fees By Booking Ids
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/list-booking-fees-by-booking-ids) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/list-booking-fees-by-booking-ids))
     * instead.
     *
     * You must have the `BOOKINGS.BOOKING_FEES_READ` permission scope when passing
     * `true`.
     *
     * Default: `false`
     */
    withBookingFeeDetails?: boolean;
    /**
     * Whether to return information about the order from ecom.
     *
     * You must have the `ECOM.READ_ORDERS` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withEcomOrder?: boolean;
    /**
     * Whether to return information about the invoices from ecom.
     *
     * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_INVOICES` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withEcomInvoices?: boolean;
    /**
     * Whether to return information about the transactions from ecom.
     *
     * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_TRANSACTIONS` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withEcomTransactions?: boolean;
    /**
     * Whether to return information about the form submission.
     *
     * You must have the `WIX_FORMS.SUBMISSION_READ_ANY` and `BOOKINGS.BOOKING_FORM_SCHEMA_READ` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    withFormSubmissions?: boolean;
}
interface CommonQueryV2 extends CommonQueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: CommonPaging;
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     *
     * See [Supported Filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)
     * for a full list.
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     */
    sort?: Sorting[];
}
/** @oneof */
interface CommonQueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: CommonPaging;
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
}
interface CommonPaging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface QueryExtendedBookingsResponse {
    /**
     * Retrieved bookings.
     *
     * May include information about the attendance or actions the customer can
     * perform, depending on your request.
     */
    extendedBookings?: ExtendedBooking[];
    /** Paging metadata. */
    pagingMetadata?: PagingMetadataV2;
}
interface CountExtendedBookingsRequest {
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
}
interface CountExtendedBookingsResponse {
    /** The number of bookings matching the specified filter. */
    count?: number;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function query(): __PublicMethodMetaInfo<'POST', {}, QueryExtendedBookingRequest$1, QueryExtendedBookingRequest, QueryExtendedBookingResponse$1, QueryExtendedBookingResponse>;
declare function queryExtendedBookings(): __PublicMethodMetaInfo<'POST', {}, QueryExtendedBookingsRequest$1, QueryExtendedBookingsRequest, QueryExtendedBookingsResponse$1, QueryExtendedBookingsResponse>;
declare function countExtendedBookings(): __PublicMethodMetaInfo<'POST', {}, CountExtendedBookingsRequest$1, CountExtendedBookingsRequest, CountExtendedBookingsResponse$1, CountExtendedBookingsResponse>;

export { type __PublicMethodMetaInfo, countExtendedBookings, query, queryExtendedBookings };
