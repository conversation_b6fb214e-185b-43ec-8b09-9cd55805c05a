"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  Action: () => Action,
  AggregationType: () => AggregationType,
  BenefitType: () => BenefitType,
  CategoryNotificationEvent: () => CategoryNotificationEvent,
  CloneErrors: () => CloneErrors,
  CrudType: () => CrudType,
  DayOfWeek: () => DayOfWeek,
  Event: () => Event,
  Interval: () => Interval,
  InvalidSlugError: () => InvalidSlugError,
  LocationType: () => LocationType,
  MissingValues: () => MissingValues,
  Mode: () => Mode,
  NestedAggregationType: () => NestedAggregationType,
  PlacementType: () => PlacementType,
  RateType: () => RateType,
  RequestedFields: () => RequestedFields,
  ResolutionMethod: () => ResolutionMethod,
  ScalarType: () => ScalarType,
  ServiceType: () => ServiceType,
  SortDirection: () => SortDirection,
  SortOrder: () => SortOrder,
  SortType: () => SortType,
  Status: () => Status,
  V2RequestedFields: () => V2RequestedFields,
  WebhookIdentityType: () => WebhookIdentityType,
  bulkCreateServices: () => bulkCreateServices4,
  bulkDeleteServices: () => bulkDeleteServices4,
  bulkDeleteServicesByFilter: () => bulkDeleteServicesByFilter4,
  bulkUpdateServices: () => bulkUpdateServices4,
  bulkUpdateServicesByFilter: () => bulkUpdateServicesByFilter4,
  cloneService: () => cloneService4,
  countServices: () => countServices4,
  createAddOnGroup: () => createAddOnGroup4,
  createService: () => createService4,
  deleteAddOnGroup: () => deleteAddOnGroup4,
  deleteService: () => deleteService4,
  disablePricingPlansForService: () => disablePricingPlansForService4,
  enablePricingPlansForService: () => enablePricingPlansForService4,
  getService: () => getService4,
  listAddOnGroupsByServiceId: () => listAddOnGroupsByServiceId4,
  onServiceCreated: () => onServiceCreated2,
  onServiceDeleted: () => onServiceDeleted2,
  onServiceUpdated: () => onServiceUpdated2,
  queryBookingForms: () => queryBookingForms4,
  queryCategories: () => queryCategories4,
  queryLocations: () => queryLocations4,
  queryPolicies: () => queryPolicies4,
  queryServices: () => queryServices4,
  searchServices: () => searchServices4,
  setAddOnsForGroup: () => setAddOnsForGroup4,
  setCustomSlug: () => setCustomSlug4,
  setServiceLocations: () => setServiceLocations4,
  updateAddOnGroup: () => updateAddOnGroup4,
  updateService: () => updateService4,
  validateSlug: () => validateSlug4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-services-v2-service-services.public.ts
var import_rename_all_nested_keys2 = require("@wix/sdk-runtime/rename-all-nested-keys");
var import_address3 = require("@wix/sdk-runtime/transformations/address");
var import_image3 = require("@wix/sdk-runtime/transformations/image");
var import_page_url_v23 = require("@wix/sdk-runtime/transformations/page-url-v2");
var import_timestamp3 = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths3 = require("@wix/sdk-runtime/transformations/transform-paths");
var import_sdk_types = require("@wix/sdk-types");

// src/bookings-services-v2-service-services.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_query_builder = require("@wix/sdk-runtime/query-builder");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-services-v2-service-services.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_float = require("@wix/sdk-runtime/transformations/float");
var import_float2 = require("@wix/sdk-runtime/transformations/float");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsServicesV2ServicesServiceUrl(opts) {
  const domainToMappings = {
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/services-2",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      },
      {
        srcPath: "/bookings/services/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/services-2",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
function resolveWixBookingsServicesV2AddOnGroupsServiceUrl(opts) {
  const domainToMappings = {
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/services-2",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      },
      {
        srcPath: "/bookings/services/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/services-2",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_services";
function createAddOnGroup(payload) {
  function __createAddOnGroup({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.CreateAddOnGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/create",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __createAddOnGroup;
}
function deleteAddOnGroup(payload) {
  function __deleteAddOnGroup({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.DeleteAddOnGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/delete",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __deleteAddOnGroup;
}
function updateAddOnGroup(payload) {
  function __updateAddOnGroup({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.UpdateAddOnGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/update",
        data: serializedData,
        host
      }),
      data: serializedData
    };
    return metadata;
  }
  return __updateAddOnGroup;
}
function listAddOnGroupsByServiceId(payload) {
  function __listAddOnGroupsByServiceId({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.ListAddOnGroupsByServiceId",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/list-add-on-groups-by-service-id",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __listAddOnGroupsByServiceId;
}
function setAddOnsForGroup(payload) {
  function __setAddOnsForGroup({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.SetAddOnsForGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/set-add-ons-for-group",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __setAddOnsForGroup;
}
function createService(payload) {
  function __createService({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "service.createdDate" },
          { path: "service.updatedDate" },
          { path: "service.media.items.image.urlExpirationDate" },
          { path: "service.media.mainMedia.image.urlExpirationDate" },
          { path: "service.media.coverMedia.image.urlExpirationDate" },
          { path: "service.bookingPolicy.createdDate" },
          { path: "service.bookingPolicy.updatedDate" },
          { path: "service.schedule.firstSessionStart" },
          { path: "service.schedule.lastSessionEnd" },
          { path: "service.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "service.supportedSlugs.createdDate" },
          { path: "service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "service.locations.business.address.geocode.latitude" },
          { path: "service.locations.business.address.geocode.longitude" },
          { path: "service.locations.custom.address.geocode.latitude" },
          { path: "service.locations.custom.address.geocode.longitude" },
          { path: "service.locations.calculatedAddress.geocode.latitude" },
          { path: "service.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.CreateService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createService;
}
function bulkCreateServices(payload) {
  function __bulkCreateServices({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "services.createdDate" },
          { path: "services.updatedDate" },
          { path: "services.media.items.image.urlExpirationDate" },
          { path: "services.media.mainMedia.image.urlExpirationDate" },
          { path: "services.media.coverMedia.image.urlExpirationDate" },
          { path: "services.bookingPolicy.createdDate" },
          { path: "services.bookingPolicy.updatedDate" },
          { path: "services.schedule.firstSessionStart" },
          { path: "services.schedule.lastSessionEnd" },
          { path: "services.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "services.supportedSlugs.createdDate" },
          { path: "services.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "services.locations.business.address.geocode.latitude" },
          { path: "services.locations.business.address.geocode.longitude" },
          { path: "services.locations.custom.address.geocode.latitude" },
          { path: "services.locations.custom.address.geocode.longitude" },
          { path: "services.locations.calculatedAddress.geocode.latitude" },
          { path: "services.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkCreateServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/create",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.media.items.image.urlExpirationDate" },
            { path: "results.item.media.mainMedia.image.urlExpirationDate" },
            { path: "results.item.media.coverMedia.image.urlExpirationDate" },
            { path: "results.item.bookingPolicy.createdDate" },
            { path: "results.item.bookingPolicy.updatedDate" },
            { path: "results.item.schedule.firstSessionStart" },
            { path: "results.item.schedule.lastSessionEnd" },
            {
              path: "results.item.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "results.item.supportedSlugs.createdDate" },
            { path: "results.item.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.locations.business.address.geocode.latitude"
            },
            {
              path: "results.item.locations.business.address.geocode.longitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.latitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.longitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkCreateServices;
}
function getService(payload) {
  function __getService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "GET",
      methodFqn: "wix.bookings.services.v2.ServicesService.GetService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getService;
}
function updateService(payload) {
  function __updateService({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "service.createdDate" },
          { path: "service.updatedDate" },
          { path: "service.media.items.image.urlExpirationDate" },
          { path: "service.media.mainMedia.image.urlExpirationDate" },
          { path: "service.media.coverMedia.image.urlExpirationDate" },
          { path: "service.bookingPolicy.createdDate" },
          { path: "service.bookingPolicy.updatedDate" },
          { path: "service.schedule.firstSessionStart" },
          { path: "service.schedule.lastSessionEnd" },
          { path: "service.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "service.supportedSlugs.createdDate" },
          { path: "service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "service.locations.business.address.geocode.latitude" },
          { path: "service.locations.business.address.geocode.longitude" },
          { path: "service.locations.custom.address.geocode.latitude" },
          { path: "service.locations.custom.address.geocode.longitude" },
          { path: "service.locations.calculatedAddress.geocode.latitude" },
          { path: "service.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "PATCH",
      methodFqn: "wix.bookings.services.v2.ServicesService.UpdateService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{service.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateService;
}
function bulkUpdateServices(payload) {
  function __bulkUpdateServices({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "services.mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "services.service.createdDate" },
          { path: "services.service.updatedDate" },
          { path: "services.service.media.items.image.urlExpirationDate" },
          { path: "services.service.media.mainMedia.image.urlExpirationDate" },
          { path: "services.service.media.coverMedia.image.urlExpirationDate" },
          { path: "services.service.bookingPolicy.createdDate" },
          { path: "services.service.bookingPolicy.updatedDate" },
          { path: "services.service.schedule.firstSessionStart" },
          { path: "services.service.schedule.lastSessionEnd" },
          {
            path: "services.service.staffMembers.mainMedia.image.urlExpirationDate"
          },
          {
            path: "services.service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "services.service.supportedSlugs.createdDate" },
          { path: "services.service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          {
            path: "services.service.locations.business.address.geocode.latitude"
          },
          {
            path: "services.service.locations.business.address.geocode.longitude"
          },
          {
            path: "services.service.locations.custom.address.geocode.latitude"
          },
          {
            path: "services.service.locations.custom.address.geocode.longitude"
          },
          {
            path: "services.service.locations.calculatedAddress.geocode.latitude"
          },
          {
            path: "services.service.locations.calculatedAddress.geocode.longitude"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkUpdateServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/update",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.media.items.image.urlExpirationDate" },
            { path: "results.item.media.mainMedia.image.urlExpirationDate" },
            { path: "results.item.media.coverMedia.image.urlExpirationDate" },
            { path: "results.item.bookingPolicy.createdDate" },
            { path: "results.item.bookingPolicy.updatedDate" },
            { path: "results.item.schedule.firstSessionStart" },
            { path: "results.item.schedule.lastSessionEnd" },
            {
              path: "results.item.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "results.item.supportedSlugs.createdDate" },
            { path: "results.item.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.locations.business.address.geocode.latitude"
            },
            {
              path: "results.item.locations.business.address.geocode.longitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.latitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.longitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkUpdateServices;
}
function bulkUpdateServicesByFilter(payload) {
  function __bulkUpdateServicesByFilter({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "service.createdDate" },
          { path: "service.updatedDate" },
          { path: "service.media.items.image.urlExpirationDate" },
          { path: "service.media.mainMedia.image.urlExpirationDate" },
          { path: "service.media.coverMedia.image.urlExpirationDate" },
          { path: "service.bookingPolicy.createdDate" },
          { path: "service.bookingPolicy.updatedDate" },
          { path: "service.schedule.firstSessionStart" },
          { path: "service.schedule.lastSessionEnd" },
          { path: "service.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "service.supportedSlugs.createdDate" },
          { path: "service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "service.locations.business.address.geocode.latitude" },
          { path: "service.locations.business.address.geocode.longitude" },
          { path: "service.locations.custom.address.geocode.latitude" },
          { path: "service.locations.custom.address.geocode.longitude" },
          { path: "service.locations.calculatedAddress.geocode.latitude" },
          { path: "service.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkUpdateServicesByFilter",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/update-by-filter",
        data: serializedData,
        host
      }),
      data: serializedData
    };
    return metadata;
  }
  return __bulkUpdateServicesByFilter;
}
function deleteService(payload) {
  function __deleteService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "DELETE",
      methodFqn: "wix.bookings.services.v2.ServicesService.DeleteService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteService;
}
function bulkDeleteServices(payload) {
  function __bulkDeleteServices({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkDeleteServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/delete",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.media.items.image.urlExpirationDate" },
            { path: "results.item.media.mainMedia.image.urlExpirationDate" },
            { path: "results.item.media.coverMedia.image.urlExpirationDate" },
            { path: "results.item.bookingPolicy.createdDate" },
            { path: "results.item.bookingPolicy.updatedDate" },
            { path: "results.item.schedule.firstSessionStart" },
            { path: "results.item.schedule.lastSessionEnd" },
            {
              path: "results.item.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "results.item.supportedSlugs.createdDate" },
            { path: "results.item.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.locations.business.address.geocode.latitude"
            },
            {
              path: "results.item.locations.business.address.geocode.longitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.latitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.longitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkDeleteServices;
}
function bulkDeleteServicesByFilter(payload) {
  function __bulkDeleteServicesByFilter({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkDeleteServicesByFilter",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/delete-by-filter",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkDeleteServicesByFilter;
}
function queryServices(payload) {
  function __queryServices({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "services.createdDate" },
            { path: "services.updatedDate" },
            { path: "services.media.items.image.urlExpirationDate" },
            { path: "services.media.mainMedia.image.urlExpirationDate" },
            { path: "services.media.coverMedia.image.urlExpirationDate" },
            { path: "services.bookingPolicy.createdDate" },
            { path: "services.bookingPolicy.updatedDate" },
            { path: "services.schedule.firstSessionStart" },
            { path: "services.schedule.lastSessionEnd" },
            {
              path: "services.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "services.supportedSlugs.createdDate" },
            { path: "services.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "services.locations.business.address.geocode.latitude" },
            { path: "services.locations.business.address.geocode.longitude" },
            { path: "services.locations.custom.address.geocode.latitude" },
            { path: "services.locations.custom.address.geocode.longitude" },
            { path: "services.locations.calculatedAddress.geocode.latitude" },
            {
              path: "services.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryServices;
}
function searchServices(payload) {
  function __searchServices({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "search.aggregations.range.buckets.from" },
          { path: "search.aggregations.range.buckets.to" },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.from"
          },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.to"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.SearchServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/search",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "services.createdDate" },
            { path: "services.updatedDate" },
            { path: "services.media.items.image.urlExpirationDate" },
            { path: "services.media.mainMedia.image.urlExpirationDate" },
            { path: "services.media.coverMedia.image.urlExpirationDate" },
            { path: "services.bookingPolicy.createdDate" },
            { path: "services.bookingPolicy.updatedDate" },
            { path: "services.schedule.firstSessionStart" },
            { path: "services.schedule.lastSessionEnd" },
            {
              path: "services.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "services.supportedSlugs.createdDate" },
            { path: "services.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "services.locations.business.address.geocode.latitude" },
            { path: "services.locations.business.address.geocode.longitude" },
            { path: "services.locations.custom.address.geocode.latitude" },
            { path: "services.locations.custom.address.geocode.longitude" },
            { path: "services.locations.calculatedAddress.geocode.latitude" },
            {
              path: "services.locations.calculatedAddress.geocode.longitude"
            },
            { path: "aggregationData.results.ranges.results.from" },
            { path: "aggregationData.results.ranges.results.to" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from"
            },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.from"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.to"
            },
            { path: "aggregationData.results.scalar.value" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.scalar.value"
            },
            {
              path: "aggregationData.results.nested.results.results.*.scalar.value"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __searchServices;
}
function queryPolicies(payload) {
  function __queryPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/policies/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicies.bookingPolicy.createdDate" },
            { path: "bookingPolicies.bookingPolicy.updatedDate" },
            { path: "bookingPolicies.services.createdDate" },
            { path: "bookingPolicies.services.updatedDate" },
            {
              path: "bookingPolicies.services.media.items.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.services.media.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.services.media.coverMedia.image.urlExpirationDate"
            },
            { path: "bookingPolicies.services.bookingPolicy.createdDate" },
            { path: "bookingPolicies.services.bookingPolicy.updatedDate" },
            { path: "bookingPolicies.services.schedule.firstSessionStart" },
            { path: "bookingPolicies.services.schedule.lastSessionEnd" },
            {
              path: "bookingPolicies.services.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "bookingPolicies.services.supportedSlugs.createdDate" },
            { path: "bookingPolicies.services.mainSlug.createdDate" },
            { path: "bookingPolicies.connectedServices.createdDate" },
            { path: "bookingPolicies.connectedServices.updatedDate" },
            {
              path: "bookingPolicies.connectedServices.media.items.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.media.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.media.coverMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.bookingPolicy.createdDate"
            },
            {
              path: "bookingPolicies.connectedServices.bookingPolicy.updatedDate"
            },
            {
              path: "bookingPolicies.connectedServices.schedule.firstSessionStart"
            },
            {
              path: "bookingPolicies.connectedServices.schedule.lastSessionEnd"
            },
            {
              path: "bookingPolicies.connectedServices.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.supportedSlugs.createdDate"
            },
            {
              path: "bookingPolicies.connectedServices.mainSlug.createdDate"
            }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "bookingPolicies.services.locations.business.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.services.locations.business.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.services.locations.custom.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.services.locations.custom.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.services.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "bookingPolicies.services.locations.calculatedAddress.geocode.longitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.business.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.business.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.custom.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.custom.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryPolicies;
}
function queryBookingForms(payload) {
  function __queryBookingForms({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryBookingForms",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/booking-forms/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryBookingForms;
}
function countServices(payload) {
  function __countServices({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.CountServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countServices;
}
function queryLocations(payload) {
  function __queryLocations({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryLocations",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/locations/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "businessLocations.locations.business.address.geocode.latitude"
            },
            {
              path: "businessLocations.locations.business.address.geocode.longitude"
            },
            {
              path: "businessLocations.locations.custom.address.geocode.latitude"
            },
            {
              path: "businessLocations.locations.custom.address.geocode.longitude"
            },
            {
              path: "businessLocations.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "businessLocations.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryLocations;
}
function queryCategories(payload) {
  function __queryCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/categories/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryCategories;
}
function setServiceLocations(payload) {
  function __setServiceLocations({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "locations.business.address.geocode.latitude" },
          { path: "locations.business.address.geocode.longitude" },
          { path: "locations.custom.address.geocode.latitude" },
          { path: "locations.custom.address.geocode.longitude" },
          { path: "locations.calculatedAddress.geocode.latitude" },
          { path: "locations.calculatedAddress.geocode.longitude" },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.latitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.longitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.latitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.longitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.latitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.longitude"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.SetServiceLocations",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/locations",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setServiceLocations;
}
function enablePricingPlansForService(payload) {
  function __enablePricingPlansForService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.EnablePricingPlansForService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/pricing-plans/add",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __enablePricingPlansForService;
}
function disablePricingPlansForService(payload) {
  function __disablePricingPlansForService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.DisablePricingPlansForService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/pricing-plans/remove",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __disablePricingPlansForService;
}
function setCustomSlug(payload) {
  function __setCustomSlug({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.SetCustomSlug",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/slugs/custom",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "slug.createdDate" },
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setCustomSlug;
}
function validateSlug(payload) {
  function __validateSlug({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.ValidateSlug",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/slugs/validate",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __validateSlug;
}
function cloneService(payload) {
  function __cloneService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.CloneService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/clone",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __cloneService;
}

// src/bookings-services-v2-service-services.universal.ts
var import_address = require("@wix/sdk-runtime/transformations/address");
var import_address2 = require("@wix/sdk-runtime/transformations/address");
var import_image = require("@wix/sdk-runtime/transformations/image");
var import_image2 = require("@wix/sdk-runtime/transformations/image");
var import_page_url_v2 = require("@wix/sdk-runtime/transformations/page-url-v2");
var import_page_url_v22 = require("@wix/sdk-runtime/transformations/page-url-v2");
var import_transform_paths2 = require("@wix/sdk-runtime/transformations/transform-paths");
var ServiceType = /* @__PURE__ */ ((ServiceType2) => {
  ServiceType2["APPOINTMENT"] = "APPOINTMENT";
  ServiceType2["CLASS"] = "CLASS";
  ServiceType2["COURSE"] = "COURSE";
  return ServiceType2;
})(ServiceType || {});
var RateType = /* @__PURE__ */ ((RateType2) => {
  RateType2["UNKNOWN_RATE_TYPE"] = "UNKNOWN_RATE_TYPE";
  RateType2["FIXED"] = "FIXED";
  RateType2["CUSTOM"] = "CUSTOM";
  RateType2["VARIED"] = "VARIED";
  RateType2["NO_FEE"] = "NO_FEE";
  return RateType2;
})(RateType || {});
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNKNOWN_LOCATION_TYPE"] = "UNKNOWN_LOCATION_TYPE";
  LocationType2["CUSTOM"] = "CUSTOM";
  LocationType2["BUSINESS"] = "BUSINESS";
  LocationType2["CUSTOMER"] = "CUSTOMER";
  return LocationType2;
})(LocationType || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
var V2RequestedFields = /* @__PURE__ */ ((V2RequestedFields2) => {
  V2RequestedFields2["UNKNOWN_REQUESTED_FIELD"] = "UNKNOWN_REQUESTED_FIELD";
  V2RequestedFields2["STAFF_MEMBER_DETAILS"] = "STAFF_MEMBER_DETAILS";
  V2RequestedFields2["RESOURCE_TYPE_DETAILS"] = "RESOURCE_TYPE_DETAILS";
  return V2RequestedFields2;
})(V2RequestedFields || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var SortType = /* @__PURE__ */ ((SortType2) => {
  SortType2["COUNT"] = "COUNT";
  SortType2["VALUE"] = "VALUE";
  return SortType2;
})(SortType || {});
var SortDirection = /* @__PURE__ */ ((SortDirection2) => {
  SortDirection2["DESC"] = "DESC";
  SortDirection2["ASC"] = "ASC";
  return SortDirection2;
})(SortDirection || {});
var MissingValues = /* @__PURE__ */ ((MissingValues2) => {
  MissingValues2["EXCLUDE"] = "EXCLUDE";
  MissingValues2["INCLUDE"] = "INCLUDE";
  return MissingValues2;
})(MissingValues || {});
var ScalarType = /* @__PURE__ */ ((ScalarType2) => {
  ScalarType2["UNKNOWN_SCALAR_TYPE"] = "UNKNOWN_SCALAR_TYPE";
  ScalarType2["COUNT_DISTINCT"] = "COUNT_DISTINCT";
  ScalarType2["MIN"] = "MIN";
  ScalarType2["MAX"] = "MAX";
  return ScalarType2;
})(ScalarType || {});
var NestedAggregationType = /* @__PURE__ */ ((NestedAggregationType2) => {
  NestedAggregationType2["UNKNOWN_AGGREGATION_TYPE"] = "UNKNOWN_AGGREGATION_TYPE";
  NestedAggregationType2["VALUE"] = "VALUE";
  NestedAggregationType2["RANGE"] = "RANGE";
  NestedAggregationType2["SCALAR"] = "SCALAR";
  NestedAggregationType2["DATE_HISTOGRAM"] = "DATE_HISTOGRAM";
  return NestedAggregationType2;
})(NestedAggregationType || {});
var Interval = /* @__PURE__ */ ((Interval2) => {
  Interval2["UNKNOWN_INTERVAL"] = "UNKNOWN_INTERVAL";
  Interval2["YEAR"] = "YEAR";
  Interval2["MONTH"] = "MONTH";
  Interval2["WEEK"] = "WEEK";
  Interval2["DAY"] = "DAY";
  Interval2["HOUR"] = "HOUR";
  Interval2["MINUTE"] = "MINUTE";
  Interval2["SECOND"] = "SECOND";
  return Interval2;
})(Interval || {});
var AggregationType = /* @__PURE__ */ ((AggregationType2) => {
  AggregationType2["UNKNOWN_AGGREGATION_TYPE"] = "UNKNOWN_AGGREGATION_TYPE";
  AggregationType2["VALUE"] = "VALUE";
  AggregationType2["RANGE"] = "RANGE";
  AggregationType2["SCALAR"] = "SCALAR";
  AggregationType2["DATE_HISTOGRAM"] = "DATE_HISTOGRAM";
  AggregationType2["NESTED"] = "NESTED";
  return AggregationType2;
})(AggregationType || {});
var Mode = /* @__PURE__ */ ((Mode2) => {
  Mode2["OR"] = "OR";
  Mode2["AND"] = "AND";
  return Mode2;
})(Mode || {});
var RequestedFields = /* @__PURE__ */ ((RequestedFields2) => {
  RequestedFields2["UNKNOWN_REQUESTED_FIELD"] = "UNKNOWN_REQUESTED_FIELD";
  RequestedFields2["DEFAULT_BOOKING_FORM"] = "DEFAULT_BOOKING_FORM";
  return RequestedFields2;
})(RequestedFields || {});
var Action = /* @__PURE__ */ ((Action2) => {
  Action2["UNKNOWN_ACTION_TYPE"] = "UNKNOWN_ACTION_TYPE";
  Action2["KEEP_AT_CURRENT_LOCATION"] = "KEEP_AT_CURRENT_LOCATION";
  Action2["MOVE_TO_LOCATION"] = "MOVE_TO_LOCATION";
  Action2["DELETE"] = "DELETE";
  return Action2;
})(Action || {});
var InvalidSlugError = /* @__PURE__ */ ((InvalidSlugError2) => {
  InvalidSlugError2["UNKNOWN_SLUG_ERROR"] = "UNKNOWN_SLUG_ERROR";
  InvalidSlugError2["SLUG_CONTAINS_ILLEGAL_CHARACTERS"] = "SLUG_CONTAINS_ILLEGAL_CHARACTERS";
  InvalidSlugError2["SLUG_ALREADY_EXISTS"] = "SLUG_ALREADY_EXISTS";
  return InvalidSlugError2;
})(InvalidSlugError || {});
var CloneErrors = /* @__PURE__ */ ((CloneErrors2) => {
  CloneErrors2["OPTIONS_AND_VARIANTS"] = "OPTIONS_AND_VARIANTS";
  CloneErrors2["FORM"] = "FORM";
  return CloneErrors2;
})(CloneErrors || {});
var Status = /* @__PURE__ */ ((Status2) => {
  Status2["CREATED"] = "CREATED";
  Status2["DELETED"] = "DELETED";
  return Status2;
})(Status || {});
var CategoryNotificationEvent = /* @__PURE__ */ ((CategoryNotificationEvent2) => {
  CategoryNotificationEvent2["Updated"] = "Updated";
  CategoryNotificationEvent2["Deleted"] = "Deleted";
  CategoryNotificationEvent2["Created"] = "Created";
  return CategoryNotificationEvent2;
})(CategoryNotificationEvent || {});
var BenefitType = /* @__PURE__ */ ((BenefitType2) => {
  BenefitType2["UNDEFINED"] = "UNDEFINED";
  BenefitType2["LIMITED"] = "LIMITED";
  BenefitType2["UNLIMITED"] = "UNLIMITED";
  return BenefitType2;
})(BenefitType || {});
var Event = /* @__PURE__ */ ((Event2) => {
  Event2["Updated"] = "Updated";
  Event2["Deleted"] = "Deleted";
  Event2["Created"] = "Created";
  return Event2;
})(Event || {});
var CrudType = /* @__PURE__ */ ((CrudType2) => {
  CrudType2["INVALID_CRUD_TYPE"] = "INVALID_CRUD_TYPE";
  CrudType2["CREATE"] = "CREATE";
  CrudType2["UPDATE"] = "UPDATE";
  CrudType2["DELETE"] = "DELETE";
  CrudType2["CREATE_OR_UPDATE"] = "CREATE_OR_UPDATE";
  return CrudType2;
})(CrudType || {});
var PlacementType = /* @__PURE__ */ ((PlacementType2) => {
  PlacementType2["BEFORE"] = "BEFORE";
  PlacementType2["AFTER"] = "AFTER";
  PlacementType2["REPLACE"] = "REPLACE";
  return PlacementType2;
})(PlacementType || {});
var DayOfWeek = /* @__PURE__ */ ((DayOfWeek2) => {
  DayOfWeek2["MONDAY"] = "MONDAY";
  DayOfWeek2["TUESDAY"] = "TUESDAY";
  DayOfWeek2["WEDNESDAY"] = "WEDNESDAY";
  DayOfWeek2["THURSDAY"] = "THURSDAY";
  DayOfWeek2["FRIDAY"] = "FRIDAY";
  DayOfWeek2["SATURDAY"] = "SATURDAY";
  DayOfWeek2["SUNDAY"] = "SUNDAY";
  return DayOfWeek2;
})(DayOfWeek || {});
var ResolutionMethod = /* @__PURE__ */ ((ResolutionMethod2) => {
  ResolutionMethod2["QUERY_PARAM"] = "QUERY_PARAM";
  ResolutionMethod2["SUBDOMAIN"] = "SUBDOMAIN";
  ResolutionMethod2["SUBDIRECTORY"] = "SUBDIRECTORY";
  return ResolutionMethod2;
})(ResolutionMethod || {});
async function createAddOnGroup2(addOnGroup, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    addOnGroup,
    serviceId: options?.serviceId
  });
  const reqOpts = createAddOnGroup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          addOnGroup: "$[0]",
          serviceId: "$[1].serviceId"
        },
        singleArgumentUnchanged: false
      },
      ["addOnGroup", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteAddOnGroup2(addOnGroupId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    addOnGroupId,
    serviceId: options?.serviceId
  });
  const reqOpts = deleteAddOnGroup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          addOnGroupId: "$[0]",
          serviceId: "$[1].serviceId"
        },
        singleArgumentUnchanged: false
      },
      ["addOnGroupId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateAddOnGroup2(addOnGroup, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    addOnGroup,
    serviceId: options?.serviceId
  });
  const reqOpts = updateAddOnGroup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          addOnGroup: "$[0]",
          serviceId: "$[1].serviceId"
        },
        singleArgumentUnchanged: false
      },
      ["addOnGroup", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function listAddOnGroupsByServiceId2(serviceId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId,
    groupIds: options?.groupIds
  });
  const reqOpts = listAddOnGroupsByServiceId(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceId: "$[0]",
          groupIds: "$[1].groupIds"
        },
        singleArgumentUnchanged: false
      },
      ["serviceId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function setAddOnsForGroup2(serviceId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId,
    groupId: options?.groupId,
    addOnIds: options?.addOnIds
  });
  const reqOpts = setAddOnsForGroup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceId: "$[0]",
          groupId: "$[1].groupId",
          addOnIds: "$[1].addOnIds"
        },
        singleArgumentUnchanged: false
      },
      ["serviceId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function createService2(service) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_transform_paths2.transformPaths)(
    (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ service }),
    [
      {
        transformFn: import_image.transformSDKImageToRESTImage,
        paths: [
          { path: "service.media.items.image" },
          { path: "service.media.mainMedia.image" },
          { path: "service.media.coverMedia.image" },
          { path: "service.staffMembers.mainMedia.image" },
          { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
        ]
      },
      {
        transformFn: import_address.transformSDKAddressToRESTAddress,
        paths: [
          { path: "service.locations.calculatedAddress" },
          { path: "service.locations.business.address" },
          { path: "service.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v2.transformSDKPageURLV2ToRESTPageURLV2,
        paths: [
          { path: "service.urls.servicePage" },
          { path: "service.urls.bookingPage" },
          { path: "service.urls.calendarPage" }
        ]
      }
    ]
  );
  const reqOpts = createService(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    )?.service;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { service: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["service"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkCreateServices2(services, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_transform_paths2.transformPaths)(
    (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
      services,
      returnEntity: options?.returnEntity
    }),
    [
      {
        transformFn: import_image.transformSDKImageToRESTImage,
        paths: [
          { path: "services.media.items.image" },
          { path: "services.media.mainMedia.image" },
          { path: "services.media.coverMedia.image" },
          { path: "services.staffMembers.mainMedia.image" },
          { path: "services.staffMemberDetails.staffMembers.mainMedia.image" }
        ]
      },
      {
        transformFn: import_address.transformSDKAddressToRESTAddress,
        paths: [
          { path: "services.locations.calculatedAddress" },
          { path: "services.locations.business.address" },
          { path: "services.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v2.transformSDKPageURLV2ToRESTPageURLV2,
        paths: [
          { path: "services.urls.servicePage" },
          { path: "services.urls.bookingPage" },
          { path: "services.urls.calendarPage" }
        ]
      }
    ]
  );
  const reqOpts = bulkCreateServices(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "results.item.media.items.image" },
            { path: "results.item.media.mainMedia.image" },
            { path: "results.item.media.coverMedia.image" },
            { path: "results.item.staffMembers.mainMedia.image" },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image"
            }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "results.item.locations.calculatedAddress" },
            { path: "results.item.locations.business.address" },
            { path: "results.item.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "results.item.urls.servicePage" },
            { path: "results.item.urls.bookingPage" },
            { path: "results.item.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          services: "$[0]",
          returnEntity: "$[1].returnEntity"
        },
        singleArgumentUnchanged: false
      },
      ["services", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getService2(serviceId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId
  });
  const reqOpts = getService(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    )?.service;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { serviceId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["serviceId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateService2(_id, service) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_transform_paths2.transformPaths)(
    (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ service: { ...service, id: _id } }),
    [
      {
        transformFn: import_image.transformSDKImageToRESTImage,
        paths: [
          { path: "service.media.items.image" },
          { path: "service.media.mainMedia.image" },
          { path: "service.media.coverMedia.image" },
          { path: "service.staffMembers.mainMedia.image" },
          { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
        ]
      },
      {
        transformFn: import_address.transformSDKAddressToRESTAddress,
        paths: [
          { path: "service.locations.calculatedAddress" },
          { path: "service.locations.business.address" },
          { path: "service.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v2.transformSDKPageURLV2ToRESTPageURLV2,
        paths: [
          { path: "service.urls.servicePage" },
          { path: "service.urls.bookingPage" },
          { path: "service.urls.calendarPage" }
        ]
      }
    ]
  );
  const reqOpts = updateService(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    )?.service;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: { service: "$[1]" },
        explicitPathsToArguments: { "service.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "service"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkUpdateServices2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_transform_paths2.transformPaths)(
    (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
      services: options?.services,
      returnEntity: options?.returnEntity
    }),
    [
      {
        transformFn: import_image.transformSDKImageToRESTImage,
        paths: [
          { path: "services.service.media.items.image" },
          { path: "services.service.media.mainMedia.image" },
          { path: "services.service.media.coverMedia.image" },
          { path: "services.service.staffMembers.mainMedia.image" },
          {
            path: "services.service.staffMemberDetails.staffMembers.mainMedia.image"
          }
        ]
      },
      {
        transformFn: import_address.transformSDKAddressToRESTAddress,
        paths: [
          { path: "services.service.locations.calculatedAddress" },
          { path: "services.service.locations.business.address" },
          { path: "services.service.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v2.transformSDKPageURLV2ToRESTPageURLV2,
        paths: [
          { path: "services.service.urls.servicePage" },
          { path: "services.service.urls.bookingPage" },
          { path: "services.service.urls.calendarPage" }
        ]
      }
    ]
  );
  const reqOpts = bulkUpdateServices(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "results.item.media.items.image" },
            { path: "results.item.media.mainMedia.image" },
            { path: "results.item.media.coverMedia.image" },
            { path: "results.item.staffMembers.mainMedia.image" },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image"
            }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "results.item.locations.calculatedAddress" },
            { path: "results.item.locations.business.address" },
            { path: "results.item.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "results.item.urls.servicePage" },
            { path: "results.item.urls.bookingPage" },
            { path: "results.item.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          services: "$[0].services",
          returnEntity: "$[0].returnEntity"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkUpdateServicesByFilter2(filter, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_transform_paths2.transformPaths)(
    (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
      filter,
      service: options?.service
    }),
    [
      {
        transformFn: import_image.transformSDKImageToRESTImage,
        paths: [
          { path: "service.media.items.image" },
          { path: "service.media.mainMedia.image" },
          { path: "service.media.coverMedia.image" },
          { path: "service.staffMembers.mainMedia.image" },
          { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
        ]
      },
      {
        transformFn: import_address.transformSDKAddressToRESTAddress,
        paths: [
          { path: "service.locations.calculatedAddress" },
          { path: "service.locations.business.address" },
          { path: "service.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v2.transformSDKPageURLV2ToRESTPageURLV2,
        paths: [
          { path: "service.urls.servicePage" },
          { path: "service.urls.bookingPage" },
          { path: "service.urls.calendarPage" }
        ]
      }
    ]
  );
  const reqOpts = bulkUpdateServicesByFilter(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0]", service: "$[1].service" },
        singleArgumentUnchanged: false
      },
      ["filter", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteService2(serviceId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId,
    preserveFutureSessionsWithParticipants: options?.preserveFutureSessionsWithParticipants,
    participantNotification: options?.participantNotification
  });
  const reqOpts = deleteService(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceId: "$[0]",
          preserveFutureSessionsWithParticipants: "$[1].preserveFutureSessionsWithParticipants",
          participantNotification: "$[1].participantNotification"
        },
        singleArgumentUnchanged: false
      },
      ["serviceId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkDeleteServices2(ids, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    ids,
    preserveFutureSessionsWithParticipants: options?.preserveFutureSessionsWithParticipants,
    participantNotification: options?.participantNotification
  });
  const reqOpts = bulkDeleteServices(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "results.item.media.items.image" },
            { path: "results.item.media.mainMedia.image" },
            { path: "results.item.media.coverMedia.image" },
            { path: "results.item.staffMembers.mainMedia.image" },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image"
            }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "results.item.locations.calculatedAddress" },
            { path: "results.item.locations.business.address" },
            { path: "results.item.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "results.item.urls.servicePage" },
            { path: "results.item.urls.bookingPage" },
            { path: "results.item.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          ids: "$[0]",
          preserveFutureSessionsWithParticipants: "$[1].preserveFutureSessionsWithParticipants",
          participantNotification: "$[1].participantNotification"
        },
        singleArgumentUnchanged: false
      },
      ["ids", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkDeleteServicesByFilter2(filter, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter,
    preserveFutureSessionsWithParticipants: options?.preserveFutureSessionsWithParticipants,
    participantNotification: options?.participantNotification
  });
  const reqOpts = bulkDeleteServicesByFilter(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          filter: "$[0]",
          preserveFutureSessionsWithParticipants: "$[1].preserveFutureSessionsWithParticipants",
          participantNotification: "$[1].participantNotification"
        },
        singleArgumentUnchanged: false
      },
      ["filter", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryServices2() {
  const { httpClient, sideEffects } = arguments[0];
  return (0, import_query_builder.queryBuilder)({
    func: async (payload) => {
      const reqOpts = queryServices(payload);
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({ data }) => {
      const transformedData = (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
        (0, import_transform_paths2.transformPaths)(data, [
          {
            transformFn: import_image2.transformRESTImageToSDKImage,
            paths: [
              { path: "services.media.items.image" },
              { path: "services.media.mainMedia.image" },
              { path: "services.media.coverMedia.image" },
              { path: "services.staffMembers.mainMedia.image" },
              {
                path: "services.staffMemberDetails.staffMembers.mainMedia.image"
              }
            ]
          },
          {
            transformFn: import_address2.transformRESTAddressToSDKAddress,
            paths: [
              { path: "services.locations.calculatedAddress" },
              { path: "services.locations.business.address" },
              { path: "services.locations.custom.address" }
            ]
          },
          {
            transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
            paths: [
              { path: "services.urls.servicePage" },
              { path: "services.urls.bookingPage" },
              { path: "services.urls.calendarPage" }
            ]
          }
        ])
      );
      return {
        items: transformedData?.services,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = (0, import_transform_error.transformError)(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "OFFSET",
    transformationPaths: {}
  });
}
async function searchServices2(search) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ search });
  const reqOpts = searchServices(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "services.media.items.image" },
            { path: "services.media.mainMedia.image" },
            { path: "services.media.coverMedia.image" },
            { path: "services.staffMembers.mainMedia.image" },
            {
              path: "services.staffMemberDetails.staffMembers.mainMedia.image"
            }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "services.locations.calculatedAddress" },
            { path: "services.locations.business.address" },
            { path: "services.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "services.urls.servicePage" },
            { path: "services.urls.bookingPage" },
            { path: "services.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { search: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["search"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function queryPolicies2(query) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ query });
  const reqOpts = queryPolicies(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "bookingPolicies.services.media.items.image" },
            { path: "bookingPolicies.services.media.mainMedia.image" },
            { path: "bookingPolicies.services.media.coverMedia.image" },
            { path: "bookingPolicies.services.staffMembers.mainMedia.image" },
            {
              path: "bookingPolicies.services.staffMemberDetails.staffMembers.mainMedia.image"
            },
            { path: "bookingPolicies.connectedServices.media.items.image" },
            { path: "bookingPolicies.connectedServices.media.mainMedia.image" },
            {
              path: "bookingPolicies.connectedServices.media.coverMedia.image"
            },
            {
              path: "bookingPolicies.connectedServices.staffMembers.mainMedia.image"
            },
            {
              path: "bookingPolicies.connectedServices.staffMemberDetails.staffMembers.mainMedia.image"
            }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "bookingPolicies.services.locations.calculatedAddress" },
            { path: "bookingPolicies.services.locations.business.address" },
            { path: "bookingPolicies.services.locations.custom.address" },
            {
              path: "bookingPolicies.connectedServices.locations.calculatedAddress"
            },
            {
              path: "bookingPolicies.connectedServices.locations.business.address"
            },
            {
              path: "bookingPolicies.connectedServices.locations.custom.address"
            }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "bookingPolicies.services.urls.servicePage" },
            { path: "bookingPolicies.services.urls.bookingPage" },
            { path: "bookingPolicies.services.urls.calendarPage" },
            { path: "bookingPolicies.connectedServices.urls.servicePage" },
            { path: "bookingPolicies.connectedServices.urls.bookingPage" },
            { path: "bookingPolicies.connectedServices.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["query"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function queryBookingForms2(query, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    query,
    conditionalFields: options?.conditionalFields
  });
  const reqOpts = queryBookingForms(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          query: "$[0]",
          conditionalFields: "$[1].conditionalFields"
        },
        singleArgumentUnchanged: false
      },
      ["query", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function countServices2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = countServices(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function queryLocations2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = queryLocations(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "businessLocations.locations.calculatedAddress" },
            { path: "businessLocations.locations.business.address" },
            { path: "businessLocations.locations.custom.address" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function queryCategories2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = queryCategories(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function setServiceLocations2(serviceId, locations, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = (0, import_transform_paths2.transformPaths)(
    (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
      serviceId,
      locations,
      removedLocationSessionsAction: options?.removedLocationSessionsAction,
      participantNotification: options?.participantNotification
    }),
    [
      {
        transformFn: import_address.transformSDKAddressToRESTAddress,
        paths: [
          { path: "locations.calculatedAddress" },
          { path: "locations.business.address" },
          { path: "locations.custom.address" },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address"
          }
        ]
      }
    ]
  );
  const reqOpts = setServiceLocations(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceId: "$[0]",
          locations: "$[1]",
          removedLocationSessionsAction: "$[2].removedLocationSessionsAction",
          participantNotification: "$[2].participantNotification"
        },
        singleArgumentUnchanged: false
      },
      ["serviceId", "locations", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function enablePricingPlansForService2(serviceId, pricingPlanIds) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId,
    pricingPlanIds
  });
  const reqOpts = enablePricingPlansForService(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { serviceId: "$[0]", pricingPlanIds: "$[1]" },
        singleArgumentUnchanged: false
      },
      ["serviceId", "pricingPlanIds"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function disablePricingPlansForService2(serviceId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId,
    pricingPlanIds: options?.pricingPlanIds
  });
  const reqOpts = disablePricingPlansForService(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceId: "$[0]",
          pricingPlanIds: "$[1].pricingPlanIds"
        },
        singleArgumentUnchanged: false
      },
      ["serviceId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function setCustomSlug2(serviceId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId,
    slug: options?.slug
  });
  const reqOpts = setCustomSlug(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { serviceId: "$[0]", slug: "$[1].slug" },
        singleArgumentUnchanged: false
      },
      ["serviceId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function validateSlug2(serviceId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId,
    slug: options?.slug
  });
  const reqOpts = validateSlug(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { serviceId: "$[0]", slug: "$[1].slug" },
        singleArgumentUnchanged: false
      },
      ["serviceId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function cloneService2(sourceServiceId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    sourceServiceId
  });
  const reqOpts = cloneService(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
      (0, import_transform_paths2.transformPaths)(result.data, [
        {
          transformFn: import_image2.transformRESTImageToSDKImage,
          paths: [
            { path: "service.media.items.image" },
            { path: "service.media.mainMedia.image" },
            { path: "service.media.coverMedia.image" },
            { path: "service.staffMembers.mainMedia.image" },
            { path: "service.staffMemberDetails.staffMembers.mainMedia.image" }
          ]
        },
        {
          transformFn: import_address2.transformRESTAddressToSDKAddress,
          paths: [
            { path: "service.locations.calculatedAddress" },
            { path: "service.locations.business.address" },
            { path: "service.locations.custom.address" }
          ]
        },
        {
          transformFn: import_page_url_v22.transformRESTPageURLV2ToSDKPageURLV2,
          paths: [
            { path: "service.urls.servicePage" },
            { path: "service.urls.bookingPage" },
            { path: "service.urls.calendarPage" }
          ]
        }
      ])
    );
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { sourceServiceId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["sourceServiceId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-services-v2-service-services.public.ts
function createAddOnGroup3(httpClient) {
  return (addOnGroup, options) => createAddOnGroup2(
    addOnGroup,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function deleteAddOnGroup3(httpClient) {
  return (addOnGroupId, options) => deleteAddOnGroup2(
    addOnGroupId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function updateAddOnGroup3(httpClient) {
  return (addOnGroup, options) => updateAddOnGroup2(
    addOnGroup,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function listAddOnGroupsByServiceId3(httpClient) {
  return (serviceId, options) => listAddOnGroupsByServiceId2(
    serviceId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function setAddOnsForGroup3(httpClient) {
  return (serviceId, options) => setAddOnsForGroup2(
    serviceId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function createService3(httpClient) {
  return (service) => createService2(
    service,
    // @ts-ignore
    { httpClient }
  );
}
function bulkCreateServices3(httpClient) {
  return (services, options) => bulkCreateServices2(
    services,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function getService3(httpClient) {
  return (serviceId) => getService2(
    serviceId,
    // @ts-ignore
    { httpClient }
  );
}
function updateService3(httpClient) {
  return (_id, service) => updateService2(
    _id,
    service,
    // @ts-ignore
    { httpClient }
  );
}
function bulkUpdateServices3(httpClient) {
  return (options) => bulkUpdateServices2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkUpdateServicesByFilter3(httpClient) {
  return (filter, options) => bulkUpdateServicesByFilter2(
    filter,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function deleteService3(httpClient) {
  return (serviceId, options) => deleteService2(
    serviceId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkDeleteServices3(httpClient) {
  return (ids, options) => bulkDeleteServices2(
    ids,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkDeleteServicesByFilter3(httpClient) {
  return (filter, options) => bulkDeleteServicesByFilter2(
    filter,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function queryServices3(httpClient) {
  return () => queryServices2(
    // @ts-ignore
    { httpClient }
  );
}
function searchServices3(httpClient) {
  return (search) => searchServices2(
    search,
    // @ts-ignore
    { httpClient }
  );
}
function queryPolicies3(httpClient) {
  return (query) => queryPolicies2(
    query,
    // @ts-ignore
    { httpClient }
  );
}
function queryBookingForms3(httpClient) {
  return (query, options) => queryBookingForms2(
    query,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function countServices3(httpClient) {
  return (options) => countServices2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function queryLocations3(httpClient) {
  return (options) => queryLocations2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function queryCategories3(httpClient) {
  return (options) => queryCategories2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function setServiceLocations3(httpClient) {
  return (serviceId, locations, options) => setServiceLocations2(
    serviceId,
    locations,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function enablePricingPlansForService3(httpClient) {
  return (serviceId, pricingPlanIds) => enablePricingPlansForService2(
    serviceId,
    pricingPlanIds,
    // @ts-ignore
    { httpClient }
  );
}
function disablePricingPlansForService3(httpClient) {
  return (serviceId, options) => disablePricingPlansForService2(
    serviceId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function setCustomSlug3(httpClient) {
  return (serviceId, options) => setCustomSlug2(
    serviceId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function validateSlug3(httpClient) {
  return (serviceId, options) => validateSlug2(
    serviceId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function cloneService3(httpClient) {
  return (sourceServiceId) => cloneService2(
    sourceServiceId,
    // @ts-ignore
    { httpClient }
  );
}
var onServiceCreated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.services.v2.service_created",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "entity.bookingPolicy.createdDate" },
          { path: "entity.bookingPolicy.updatedDate" },
          { path: "entity.schedule.firstSessionStart" },
          { path: "entity.schedule.lastSessionEnd" },
          { path: "entity.supportedSlugs.createdDate" },
          { path: "entity.mainSlug.createdDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_image3.transformRESTImageToSDKImage,
        paths: [
          { path: "entity.media.items.image" },
          { path: "entity.media.mainMedia.image" },
          { path: "entity.media.coverMedia.image" },
          { path: "entity.staffMembers.mainMedia.image" },
          { path: "entity.staffMemberDetails.staffMembers.mainMedia.image" }
        ]
      },
      {
        transformFn: import_address3.transformRESTAddressToSDKAddress,
        paths: [
          { path: "entity.locations.calculatedAddress" },
          { path: "entity.locations.business.address" },
          { path: "entity.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v23.transformRESTPageURLV2ToSDKPageURLV2,
        paths: [
          { path: "entity.urls.servicePage" },
          { path: "entity.urls.bookingPage" },
          { path: "entity.urls.calendarPage" }
        ]
      }
    ])
  )
)();
var onServiceDeleted = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.services.v2.service_deleted",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "entity.bookingPolicy.createdDate" },
          { path: "entity.bookingPolicy.updatedDate" },
          { path: "entity.schedule.firstSessionStart" },
          { path: "entity.schedule.lastSessionEnd" },
          { path: "entity.supportedSlugs.createdDate" },
          { path: "entity.mainSlug.createdDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_image3.transformRESTImageToSDKImage,
        paths: [
          { path: "entity.media.items.image" },
          { path: "entity.media.mainMedia.image" },
          { path: "entity.media.coverMedia.image" },
          { path: "entity.staffMembers.mainMedia.image" },
          { path: "entity.staffMemberDetails.staffMembers.mainMedia.image" }
        ]
      },
      {
        transformFn: import_address3.transformRESTAddressToSDKAddress,
        paths: [
          { path: "entity.locations.calculatedAddress" },
          { path: "entity.locations.business.address" },
          { path: "entity.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v23.transformRESTPageURLV2ToSDKPageURLV2,
        paths: [
          { path: "entity.urls.servicePage" },
          { path: "entity.urls.bookingPage" },
          { path: "entity.urls.calendarPage" }
        ]
      }
    ])
  )
)();
var onServiceUpdated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.services.v2.service_updated",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "entity.bookingPolicy.createdDate" },
          { path: "entity.bookingPolicy.updatedDate" },
          { path: "entity.schedule.firstSessionStart" },
          { path: "entity.schedule.lastSessionEnd" },
          { path: "entity.supportedSlugs.createdDate" },
          { path: "entity.mainSlug.createdDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_image3.transformRESTImageToSDKImage,
        paths: [
          { path: "entity.media.items.image" },
          { path: "entity.media.mainMedia.image" },
          { path: "entity.media.coverMedia.image" },
          { path: "entity.staffMembers.mainMedia.image" },
          { path: "entity.staffMemberDetails.staffMembers.mainMedia.image" }
        ]
      },
      {
        transformFn: import_address3.transformRESTAddressToSDKAddress,
        paths: [
          { path: "entity.locations.calculatedAddress" },
          { path: "entity.locations.business.address" },
          { path: "entity.locations.custom.address" }
        ]
      },
      {
        transformFn: import_page_url_v23.transformRESTPageURLV2ToSDKPageURLV2,
        paths: [
          { path: "entity.urls.servicePage" },
          { path: "entity.urls.bookingPage" },
          { path: "entity.urls.calendarPage" }
        ]
      }
    ])
  )
)();

// src/bookings-services-v2-service-services.context.ts
var import_rest_modules3 = require("@wix/sdk-runtime/rest-modules");
var import_event_definition_modules = require("@wix/sdk-runtime/event-definition-modules");
var createAddOnGroup4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(createAddOnGroup3);
var deleteAddOnGroup4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(deleteAddOnGroup3);
var updateAddOnGroup4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(updateAddOnGroup3);
var listAddOnGroupsByServiceId4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(listAddOnGroupsByServiceId3);
var setAddOnsForGroup4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(setAddOnsForGroup3);
var createService4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(createService3);
var bulkCreateServices4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkCreateServices3);
var getService4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(getService3);
var updateService4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(updateService3);
var bulkUpdateServices4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkUpdateServices3);
var bulkUpdateServicesByFilter4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkUpdateServicesByFilter3);
var deleteService4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(deleteService3);
var bulkDeleteServices4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkDeleteServices3);
var bulkDeleteServicesByFilter4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkDeleteServicesByFilter3);
var queryServices4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryServices3);
var searchServices4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(searchServices3);
var queryPolicies4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryPolicies3);
var queryBookingForms4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryBookingForms3);
var countServices4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(countServices3);
var queryLocations4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryLocations3);
var queryCategories4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryCategories3);
var setServiceLocations4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(setServiceLocations3);
var enablePricingPlansForService4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(enablePricingPlansForService3);
var disablePricingPlansForService4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(disablePricingPlansForService3);
var setCustomSlug4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(setCustomSlug3);
var validateSlug4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(validateSlug3);
var cloneService4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(cloneService3);
var onServiceCreated2 = (0, import_event_definition_modules.createEventModule)(onServiceCreated);
var onServiceDeleted2 = (0, import_event_definition_modules.createEventModule)(onServiceDeleted);
var onServiceUpdated2 = (0, import_event_definition_modules.createEventModule)(onServiceUpdated);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Action,
  AggregationType,
  BenefitType,
  CategoryNotificationEvent,
  CloneErrors,
  CrudType,
  DayOfWeek,
  Event,
  Interval,
  InvalidSlugError,
  LocationType,
  MissingValues,
  Mode,
  NestedAggregationType,
  PlacementType,
  RateType,
  RequestedFields,
  ResolutionMethod,
  ScalarType,
  ServiceType,
  SortDirection,
  SortOrder,
  SortType,
  Status,
  V2RequestedFields,
  WebhookIdentityType,
  bulkCreateServices,
  bulkDeleteServices,
  bulkDeleteServicesByFilter,
  bulkUpdateServices,
  bulkUpdateServicesByFilter,
  cloneService,
  countServices,
  createAddOnGroup,
  createService,
  deleteAddOnGroup,
  deleteService,
  disablePricingPlansForService,
  enablePricingPlansForService,
  getService,
  listAddOnGroupsByServiceId,
  onServiceCreated,
  onServiceDeleted,
  onServiceUpdated,
  queryBookingForms,
  queryCategories,
  queryLocations,
  queryPolicies,
  queryServices,
  searchServices,
  setAddOnsForGroup,
  setCustomSlug,
  setServiceLocations,
  updateAddOnGroup,
  updateService,
  validateSlug
});
//# sourceMappingURL=index.js.map