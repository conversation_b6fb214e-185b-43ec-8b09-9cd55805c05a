// src/bookings-fees-v1-booking-fee-booking-fees.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-fees-v1-booking-fee-booking-fees.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsFeesV1BookingFeesUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-fees";
function listBookingFeesByBookingIds(payload) {
  function __listBookingFeesByBookingIds({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "GET",
      methodFqn: "wix.bookings.fees.v1.BookingFees.ListBookingFeesByBookingIds",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __listBookingFeesByBookingIds;
}
function applyBookingFeesToOrder(payload) {
  function __applyBookingFeesToOrder({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "POST",
      methodFqn: "wix.bookings.fees.v1.BookingFees.ApplyBookingFeesToOrder",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees/apply",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __applyBookingFeesToOrder;
}
function collectAppliedBookingFees(payload) {
  function __collectAppliedBookingFees({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "POST",
      methodFqn: "wix.bookings.fees.v1.BookingFees.CollectAppliedBookingFees",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees/collect",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __collectAppliedBookingFees;
}

// src/bookings-fees-v1-booking-fee-booking-fees.universal.ts
var BookingFeeStatus = /* @__PURE__ */ ((BookingFeeStatus2) => {
  BookingFeeStatus2["UNKNOWN_STATUS"] = "UNKNOWN_STATUS";
  BookingFeeStatus2["PREVIEW"] = "PREVIEW";
  BookingFeeStatus2["NOT_YET_APPLIED_TO_ORDER"] = "NOT_YET_APPLIED_TO_ORDER";
  BookingFeeStatus2["APPLIED_TO_ORDER"] = "APPLIED_TO_ORDER";
  return BookingFeeStatus2;
})(BookingFeeStatus || {});
var Trigger = /* @__PURE__ */ ((Trigger2) => {
  Trigger2["UNKNOWN_TRIGGER"] = "UNKNOWN_TRIGGER";
  Trigger2["NOT_ATTENDED"] = "NOT_ATTENDED";
  Trigger2["BOOKING_CANCELED"] = "BOOKING_CANCELED";
  return Trigger2;
})(Trigger || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function listBookingFeesByBookingIds2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingIds: options?.bookingIds,
    multiServiceBookingIds: options?.multiServiceBookingIds
  });
  const reqOpts = listBookingFeesByBookingIds(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingIds: "$[0].bookingIds",
          multiServiceBookingIds: "$[0].multiServiceBookingIds"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function applyBookingFeesToOrder2(bookingIds, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingIds,
    priceOverride: options?.priceOverride,
    businessNotification: options?.businessNotification
  });
  const reqOpts = applyBookingFeesToOrder(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingIds: "$[0]",
          priceOverride: "$[1].priceOverride",
          businessNotification: "$[1].businessNotification"
        },
        singleArgumentUnchanged: false
      },
      ["bookingIds", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function collectAppliedBookingFees2(orderId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    orderId,
    additionalFeeId: options?.additionalFeeId,
    businessNotification: options?.businessNotification
  });
  const reqOpts = collectAppliedBookingFees(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          orderId: "$[0]",
          additionalFeeId: "$[1].additionalFeeId",
          businessNotification: "$[1].businessNotification"
        },
        singleArgumentUnchanged: false
      },
      ["orderId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-fees-v1-booking-fee-booking-fees.public.ts
function listBookingFeesByBookingIds3(httpClient) {
  return (options) => listBookingFeesByBookingIds2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function applyBookingFeesToOrder3(httpClient) {
  return (bookingIds, options) => applyBookingFeesToOrder2(
    bookingIds,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function collectAppliedBookingFees3(httpClient) {
  return (orderId, options) => collectAppliedBookingFees2(
    orderId,
    options,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-fees-v1-booking-fee-booking-fees.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
var listBookingFeesByBookingIds4 = /* @__PURE__ */ createRESTModule(listBookingFeesByBookingIds3);
var applyBookingFeesToOrder4 = /* @__PURE__ */ createRESTModule(applyBookingFeesToOrder3);
var collectAppliedBookingFees4 = /* @__PURE__ */ createRESTModule(collectAppliedBookingFees3);
export {
  BookingFeeStatus,
  Trigger,
  WebhookIdentityType,
  applyBookingFeesToOrder4 as applyBookingFeesToOrder,
  collectAppliedBookingFees4 as collectAppliedBookingFees,
  listBookingFeesByBookingIds4 as listBookingFeesByBookingIds
};
//# sourceMappingURL=index.mjs.map