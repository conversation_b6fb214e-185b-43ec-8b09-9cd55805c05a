{"version": 3, "sources": ["../../src/bookings-v2-price-info-pricing.http.ts", "../../src/bookings-v2-price-info-pricing.meta.ts"], "sourcesContent": ["import { transformSDKFloatToRESTFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsPricingBookingsPricingServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-pricing',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings-pricing/v2/price',\n        destPath: '/v2/pricing',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings-pricing',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings-pricing/v2/price',\n        destPath: '/v2/pricing',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/pricing/v2/pricing',\n        destPath: '/v2/pricing',\n      },\n      {\n        srcPath: '/bookings/v2/pricing',\n        destPath: '/v2/pricing',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_pricing';\n\n/**\n * Previews the base price for a set of line items belonging to the same\n * service, before a booking is created. During the booking flow, additional\n * taxes and fees may be added to the base price.\n *\n *\n * ## Response\n *\n * The response includes each line item's individual preview price and the\n * total of all line item preview prices. Note that the final price upon\n * booking creation may differ from the preview price.\n *\n * ## Errors\n *\n * _Preview Price_ fails if:\n *\n * - You specify line items that belong to different *services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).\n * - The site is using the *Bookings Pricing Integration SPI*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)).\n *\n * ## When to call Calculate Price instead\n *\n * To retrieve the price of an existing booking, call *Calculate Price*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price)).\n */\nexport function previewPrice(payload: object): RequestOptionsFactory<any> {\n  function __previewPrice({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'bookingLineItems.pricePerParticipant' },\n          { path: 'bookingLineItems.serviceChoices.pricePerParticipant' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.price_info',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.pricing.BookingsPricingService.PreviewPrice',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({\n        protoPath: '/v2/pricing/preview',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'priceInfo.calculatedPrice' },\n              { path: 'priceInfo.deposit' },\n              { path: 'priceInfo.bookingLineItems.pricePerParticipant' },\n              {\n                path: 'priceInfo.bookingLineItems.serviceChoices.pricePerParticipant',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __previewPrice;\n}\n\n/**\n * Calculates the base price of a booking.\n *\n *\n * The returned price serves as the foundation for charging the customer. During the\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/introduction)),\n * additional taxes and fees may be added to this base price.\n *\n * ## Price calculation method\n *\n * By default, Wix Bookings calculates a booking's price based on the relevant\n * `serviceOptionsAndVariants.variants.values.price`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n * You must then specify either `booking.bookedEntity.slot.serviceId` or\n * `booking.bookedEntity.schedule.scheduleId`.\n *\n * If the business uses the *Wix Bookings Pricing Integration service plugin*\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)),\n * the returned `priceInfo` object reflects values received from the SPI implementor in\n * _Calculate Price_\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/calculate-price)).\n * In this case, it suffices to specify `booking.bookedEntity`.\n *\n * ## When to call Preview Price instead\n *\n * To estimate the price for multiple booking line items before the booking exists,\n * call *Preview Price*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/preview-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/preview-price)).\n */\nexport function calculatePrice(payload: object): RequestOptionsFactory<any> {\n  function __calculatePrice({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'booking.createdDate' },\n          { path: 'booking.startDate' },\n          { path: 'booking.endDate' },\n          { path: 'booking.updatedDate' },\n          { path: 'booking.canceledDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n          { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.price_info',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.pricing.BookingsPricingService.CalculatePrice',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({\n        protoPath: '/v2/pricing/calculate',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'priceInfo.calculatedPrice' },\n              { path: 'priceInfo.deposit' },\n              { path: 'priceInfo.bookingLineItems.pricePerParticipant' },\n              {\n                path: 'priceInfo.bookingLineItems.serviceChoices.pricePerParticipant',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __calculatePrice;\n}\n", "import * as ambassadorWixBookingsV2PriceInfo from './bookings-v2-price-info-pricing.http.js';\nimport * as ambassadorWixBookingsV2PriceInfoTypes from './bookings-v2-price-info-pricing.types.js';\nimport * as ambassadorWixBookingsV2PriceInfoUniversalTypes from './bookings-v2-price-info-pricing.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function previewPrice(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV2PriceInfoUniversalTypes.PreviewPriceRequest,\n  ambassadorWixBookingsV2PriceInfoTypes.PreviewPriceRequest,\n  ambassadorWixBookingsV2PriceInfoUniversalTypes.PreviewPriceResponse,\n  ambassadorWixBookingsV2PriceInfoTypes.PreviewPriceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV2PriceInfo.previewPrice(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/pricing/preview',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function calculatePrice(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV2PriceInfoUniversalTypes.CalculatePriceRequest,\n  ambassadorWixBookingsV2PriceInfoTypes.CalculatePriceRequest,\n  ambassadorWixBookingsV2PriceInfoUniversalTypes.CalculatePriceResponse,\n  ambassadorWixBookingsV2PriceInfoTypes.CalculatePriceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV2PriceInfo.calculatePrice(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/pricing/calculate',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,oCAAoC;AAC7C,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,2DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AA4Bd,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uCAAuC;AAAA,UAC/C,EAAE,MAAM,sDAAsD;AAAA,QAChE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,iDAAiD;AAAA,YACzD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgCO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,oBAAoB;AAAA,UAC5B,EAAE,MAAM,kBAAkB;AAAA,UAC1B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,QACjE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,iDAAiD;AAAA,YACzD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACrLO,SAASC,gBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC6B,aAAa,OAAO;AAEvD,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC6B,eAAe,OAAO;AAEzD,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "previewPrice", "calculatePrice"]}