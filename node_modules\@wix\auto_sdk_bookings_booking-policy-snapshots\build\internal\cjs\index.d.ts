import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { ListPolicySnapshotsByBookingIdsResponse, ListPolicySnapshotsByBookingIdsApplicationErrors } from './index.typings.js';
export { ActionEvent, BookingPolicy, BookingPolicySnapshot, CancellationFeePolicy, CancellationPolicy, CancellationWindow, CancellationWindowFeeOneOf, DomainEvent, DomainEventBodyOneOf, Empty, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, ListPolicySnapshotsByBookingIdsRequest, Money, PolicyDescription, ReschedulePolicy, RestoreInfo, SaveCreditCardPolicy } from './index.typings.js';

declare function listPolicySnapshotsByBookingIds$1(httpClient: HttpClient): ListPolicySnapshotsByBookingIdsSignature;
interface ListPolicySnapshotsByBookingIdsSignature {
    /**
     * Retrieves a list of booking policy snapshots by booking IDs
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).
     * @param - List of booking IDs to retrieve policy snapshots for.
     */
    (bookingIds: string[]): Promise<NonNullablePaths<ListPolicySnapshotsByBookingIdsResponse, `bookingPolicySnapshots` | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.enabled` | `bookingPolicySnapshots.${number}.policy.customPolicyDescription.description` | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.enabled` | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.limitLatestCancellation` | `bookingPolicySnapshots.${number}.policy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.enabled` | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.limitLatestReschedule` | `bookingPolicySnapshots.${number}.policy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicySnapshots.${number}.policy.cancellationFeePolicy.enabled` | `bookingPolicySnapshots.${number}.policy.saveCreditCardPolicy.enabled`, 6> & {
        __applicationErrorsType?: ListPolicySnapshotsByBookingIdsApplicationErrors;
    }>;
}

declare const listPolicySnapshotsByBookingIds: MaybeContext<BuildRESTFunction<typeof listPolicySnapshotsByBookingIds$1> & typeof listPolicySnapshotsByBookingIds$1>;

export { ListPolicySnapshotsByBookingIdsApplicationErrors, ListPolicySnapshotsByBookingIdsResponse, listPolicySnapshotsByBookingIds };
