{"version": 3, "sources": ["../../../meta.ts", "../../../src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.ts", "../../../src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.meta.ts"], "sourcesContent": ["export * from './src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.meta.js';\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/booking-policy-snapshots',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/booking-policy-snapshots',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/booking-policy-snapshots',\n        destPath: '',\n      },\n      {\n        srcPath: '/booking-policy-snapshots',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_booking-policy-snapshots';\n\n/**\n * Retrieves a list of booking policy snapshots by booking IDs\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).\n */\nexport function listPolicySnapshotsByBookingIds(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listPolicySnapshotsByBookingIds({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.policy_snapshots.v1.booking_policy_snapshot',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(\n        { protoPath: '/v1/policy-snapshots', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicySnapshots.createdDate' },\n              { path: 'bookingPolicySnapshots.policy.createdDate' },\n              { path: 'bookingPolicySnapshots.policy.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listPolicySnapshotsByBookingIds;\n}\n", "import * as ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshot from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.js';\nimport * as ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshotTypes from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.types.js';\nimport * as ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshotUniversalTypes from './bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function listPolicySnapshotsByBookingIds(): __PublicMethodMetaInfo<\n  'GET',\n  {},\n  ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshotUniversalTypes.ListPolicySnapshotsByBookingIdsRequest,\n  ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshotTypes.ListPolicySnapshotsByBookingIdsRequest,\n  ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshotUniversalTypes.ListPolicySnapshotsByBookingIdsResponse,\n  ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshotTypes.ListPolicySnapshotsByBookingIdsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsPolicySnapshotsV1BookingPolicySnapshot.listPolicySnapshotsByBookingIds(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v1/policy-snapshots',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,yCAAAA;AAAA;AAAA;;;ACAA,0BAAkC;AAClC,uBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,qEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAMd,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,wBAAwB,MAAM,SAAS,KAAK;AAAA,MAC3D;AAAA,MACA,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,4CAA4C;AAAA,UACtD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACrDO,SAASC,mCAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACwD;AAAA,IAC1D;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["listPolicySnapshotsByBookingIds", "import_rest_modules", "payload", "listPolicySnapshotsByBookingIds"]}