{"version": 3, "sources": ["../../src/bookings-calendar-v2-external-calendar-external-calendars.universal.ts", "../../src/bookings-calendar-v2-external-calendar-external-calendars.http.ts", "../../src/bookings-calendar-v2-external-calendar-external-calendars.public.ts", "../../src/bookings-calendar-v2-external-calendar-external-calendars.context.ts"], "sourcesContent": ["import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsCalendarV2ExternalCalendar from './bookings-calendar-v2-external-calendar-external-calendars.http.js';\n\n/**\n * You can use the *External Calendar API*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/introduction))\n * to connect external calendars to a Wix site. Supported external calendars\n * include Google, Microsoft, and Apple calendars. The `externalCalendar` object\n * doesn't have any fields.\n */\nexport interface ExternalCalendar {}\n\nexport interface ListProvidersRequest {}\n\nexport interface ListProvidersResponse {\n  /** List of external calendar providers supported on the site. */\n  providers?: Provider[];\n}\n\nexport interface Provider {\n  /**\n   * ID of the external calendar provider.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Name of the external calendar provider. */\n  name?: string | null;\n  /** External calendar provider type. */\n  calendarType?: CalendarTypeWithLiterals;\n  /**\n   * Information about how you can connect the external calendar to the Wix site,\n   * and which functionality it supports.\n   */\n  features?: ProviderFeatures;\n}\n\nexport enum CalendarType {\n  /** There is no information about the external calendar type. */\n  UNDEFINED = 'UNDEFINED',\n  /** [Google Calendar](https://developers.google.com/calendar/api/guides/overview). */\n  GOOGLE = 'GOOGLE',\n  /** Apple iCalendar. */\n  I_CAL = 'I_CAL',\n  /** __Deprecated__. Use `MICROSOFT` instead. */\n  OUTLOOK = 'OUTLOOK',\n  /** __Deprecated__. Use `MICROSOFT` instead. */\n  OFFICE_365 = 'OFFICE_365',\n  /** Microsoft Calendar. For example, Office 365 calendar or Outlook calendar. */\n  MICROSOFT = 'MICROSOFT',\n  /** A different type of external calendar, not listed here. */\n  OTHER = 'OTHER',\n}\n\n/** @enumType */\nexport type CalendarTypeWithLiterals =\n  | CalendarType\n  | 'UNDEFINED'\n  | 'GOOGLE'\n  | 'I_CAL'\n  | 'OUTLOOK'\n  | 'OFFICE_365'\n  | 'MICROSOFT'\n  | 'OTHER';\n\nexport interface ProviderFeatures {\n  /** List of supported connection methods. */\n  connectMethods?: ConnectMethodWithLiterals[];\n  /**\n   * Whether you can update `syncConfic` for all external calendar connections\n   * by calling _Update Sync Config_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config)).\n   */\n  updateSyncConfig?: boolean | null;\n  /**\n   * Information about which events you can import from the external calendar to\n   * the Wix site.\n   */\n  listEventFromCalendars?: ListEventFromCalendarsWithLiterals;\n  /**\n   * Whether you can export Wix calendar *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * to the external calendar.\n   */\n  syncToCalendar?: SyncToCalendarWithLiterals;\n}\n\nexport enum ConnectMethod {\n  /**\n   * There is no information about how you can connect the external calendar\n   * to the Wix site.\n   */\n  UNDEFINED = 'UNDEFINED',\n  /**\n   * You can connect the external calendar to the Wix site by calling *Connect By O Auth*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth)).\n   */\n  OAUTH = 'OAUTH',\n  /**\n   * You can connect the external calendar to the Wix site by calling *Connect By Credentials*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials)).\n   */\n  CREDENTIALS = 'CREDENTIALS',\n}\n\n/** @enumType */\nexport type ConnectMethodWithLiterals =\n  | ConnectMethod\n  | 'UNDEFINED'\n  | 'OAUTH'\n  | 'CREDENTIALS';\n\nexport enum ListEventFromCalendars {\n  /**\n   * There is no information about which external calendar events you\n   * can import to the Wix site.\n   */\n  UNDEFINED = 'UNDEFINED',\n  /** You can't import any events from the external calendar to the Wix site. */\n  NOT_SUPPORTED = 'NOT_SUPPORTED',\n  /**\n   * You can import events only for the external calendar that's\n   * designated as primary.\n   */\n  PRIMARY_CALENDAR_ONLY = 'PRIMARY_CALENDAR_ONLY',\n  /**\n   * You can import events only for a specific external calendar account.\n   * You can call _List Calendars_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-calendars) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-calendars))\n   * to retrieve a complete list of external calendar accounts.\n   */\n  SPECIFIC_CALENDARS = 'SPECIFIC_CALENDARS',\n}\n\n/** @enumType */\nexport type ListEventFromCalendarsWithLiterals =\n  | ListEventFromCalendars\n  | 'UNDEFINED'\n  | 'NOT_SUPPORTED'\n  | 'PRIMARY_CALENDAR_ONLY'\n  | 'SPECIFIC_CALENDARS';\n\nexport enum SyncToCalendar {\n  /**\n   * There is no information about which Wix calendar *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * you can export to the external calendar.\n   */\n  UNDEFINED = 'UNDEFINED',\n  /**\n   * You can't export any Wix calendar *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * to the external calendar.\n   */\n  NOT_SUPPORTED = 'NOT_SUPPORTED',\n  /**\n   * You can export Wix calendar *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * only to the external calendar that's designated as primary.\n   */\n  PRIMARY_CALENDAR_ONLY = 'PRIMARY_CALENDAR_ONLY',\n  /** Events can be exported to specific external calendars. Use [List Calendars](#list-calendars) to get a list of calendars for a connected external calendar account. */\n  SPECIFIC_CALENDAR = 'SPECIFIC_CALENDAR',\n  /**\n   * You can export Wix calendar *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * only to a dedicated external calendar account. You can\n   * call _List Calendars_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-calendars) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-calendars))\n   * to retrieve a complete list of external calendar accounts.\n   */\n  DEDICATED_CALENDAR = 'DEDICATED_CALENDAR',\n}\n\n/** @enumType */\nexport type SyncToCalendarWithLiterals =\n  | SyncToCalendar\n  | 'UNDEFINED'\n  | 'NOT_SUPPORTED'\n  | 'PRIMARY_CALENDAR_ONLY'\n  | 'SPECIFIC_CALENDAR'\n  | 'DEDICATED_CALENDAR';\n\nexport interface GetConnectionRequest {\n  /** ID of the connection to retrieve. */\n  connectionId: string | null;\n}\n\nexport interface GetConnectionResponse {\n  /** Retrieved external calendar connection. */\n  connection?: Connection;\n}\n\nexport interface Connection {\n  /** ID of the connection between the external calendar and the Wix site. */\n  _id?: string | null;\n  /**\n   * ID of the external calendar provider.\n   * @format GUID\n   */\n  providerId?: string | null;\n  /** External calendar type. */\n  calendarType?: CalendarTypeWithLiterals;\n  /**\n   * ID of the *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * that's connected to the external calendar.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n  /**\n   * ID of the *Wix user*\n   * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))\n   * to whom the external calendar connection belongs.\n   * @format GUID\n   */\n  userId?: string | null;\n  /**\n   * ID of the app which created the external calendar connection.\n   * @format GUID\n   */\n  appId?: string | null;\n  /**\n   * Email address associated with the external calendar account. Available only\n   * after completed successfully.\n   * @format EMAIL\n   */\n  externalAccountEmail?: string | null;\n  /** Connection status. */\n  status?: StatusWithLiterals;\n  /** Reason for the error. Available only if `status` is `ERROR`. */\n  errorReason?: ErrorReasonWithLiterals;\n  /** Settings about which sync operations are supported. */\n  syncConfig?: ConnectionSyncConfig;\n}\n\nexport enum Status {\n  /** There is no information about the connection status. */\n  UNDEFINED = 'UNDEFINED',\n  /**\n   * The external calendar is connected to the Wix *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),\n   * but the sync process hasn't started yet.\n   */\n  CONNECTED = 'CONNECTED',\n  /** Sync process is ongoing. */\n  SYNC_IN_PROGRESS = 'SYNC_IN_PROGRESS',\n  /** The Wix calendar and the external calendar are in sync. */\n  SYNCED = 'SYNCED',\n  /**\n   * The external calender has been disconnected from the Wix *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   */\n  DISCONNECTED = 'DISCONNECTED',\n  /** The calendars sync is in error state. */\n  ERROR = 'ERROR',\n}\n\n/** @enumType */\nexport type StatusWithLiterals =\n  | Status\n  | 'UNDEFINED'\n  | 'CONNECTED'\n  | 'SYNC_IN_PROGRESS'\n  | 'SYNCED'\n  | 'DISCONNECTED'\n  | 'ERROR';\n\nexport enum ErrorReason {\n  /** There is no information about the connection error. */\n  UNDEFINED = 'UNDEFINED',\n  /** The external calendar's access token has been revoked. */\n  TOKEN_REVOKED = 'TOKEN_REVOKED',\n  /** The external calendar couldn't be created. */\n  EXTERNAL_CALENDAR_CREATION_FAILED = 'EXTERNAL_CALENDAR_CREATION_FAILED',\n  /** The external calendar was deleted. */\n  EXTERNAL_CALENDAR_DELETED = 'EXTERNAL_CALENDAR_DELETED',\n}\n\n/** @enumType */\nexport type ErrorReasonWithLiterals =\n  | ErrorReason\n  | 'UNDEFINED'\n  | 'TOKEN_REVOKED'\n  | 'EXTERNAL_CALENDAR_CREATION_FAILED'\n  | 'EXTERNAL_CALENDAR_DELETED';\n\nexport interface ConnectionSyncConfig {\n  /**\n   * Configuration for importing events from the external calendar to the Wix\n   * site.\n   */\n  listEventFromCalendars?: ConnectionSyncConfigListEventFromCalendars;\n  /**\n   * Configuration for exporting events from the Wix site to the external\n   * calendar.\n   */\n  syncToCalendar?: ConnectionSyncConfigSyncToCalendar;\n}\n\nexport interface Calendar {\n  /**\n   * ID of the external calendar account.\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Display name of the external calendar account.\n   * For example, `Primary` or `Birthdays`.\n   */\n  name?: string | null;\n}\n\nexport interface PrimaryCalendar {}\n\nexport interface Calendars {\n  calendars?: Calendar[];\n}\n\nexport interface DedicatedCalendar {}\n\nexport interface ConnectionSyncConfigListEventFromCalendars\n  extends ConnectionSyncConfigListEventFromCalendarsListFromOneOf {\n  /**\n   * An empty `primaryCalendar` object indicates that you can import\n   * events only from the primary external calendar. Keep in mind that\n   * not all external calendar providers support primary calendars.\n   */\n  primaryCalendar?: PrimaryCalendar;\n  /**\n   * You can import events from the list of specified external calendar\n   * accounts. The list may include the primary calendar.\n   */\n  calendars?: Calendars;\n  /**\n   * Whether you can call *List Events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n   * to import events from the external calendar to the Wix site.\n   */\n  enabled?: boolean | null;\n}\n\n/** @oneof */\nexport interface ConnectionSyncConfigListEventFromCalendarsListFromOneOf {\n  /**\n   * An empty `primaryCalendar` object indicates that you can import\n   * events only from the primary external calendar. Keep in mind that\n   * not all external calendar providers support primary calendars.\n   */\n  primaryCalendar?: PrimaryCalendar;\n  /**\n   * You can import events from the list of specified external calendar\n   * accounts. The list may include the primary calendar.\n   */\n  calendars?: Calendars;\n}\n\nexport interface ConnectionSyncConfigSyncToCalendar\n  extends ConnectionSyncConfigSyncToCalendarSyncToOneOf {\n  /**\n   * An empty `primaryCalendar` object indicates that Wix events are\n   * exported only to the primary account of the external calendar.\n   */\n  primaryCalendar?: PrimaryCalendar;\n  /**\n   * An empty `dedicatedCalendar` object indicates that Wix events are\n   * exported only to the dedicated account of the external calendar.\n   */\n  dedicatedCalendar?: DedicatedCalendar;\n  /**\n   * Whether Wix *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * are exported to the external calendar.\n   */\n  enabled?: boolean | null;\n}\n\n/** @oneof */\nexport interface ConnectionSyncConfigSyncToCalendarSyncToOneOf {\n  /**\n   * An empty `primaryCalendar` object indicates that Wix events are\n   * exported only to the primary account of the external calendar.\n   */\n  primaryCalendar?: PrimaryCalendar;\n  /**\n   * An empty `dedicatedCalendar` object indicates that Wix events are\n   * exported only to the dedicated account of the external calendar.\n   */\n  dedicatedCalendar?: DedicatedCalendar;\n}\n\nexport enum SyncToErrorReason {\n  /** No sync error. */\n  UNDEFINED = 'UNDEFINED',\n  /** Could not create calendar to sync sessions to. */\n  CALENDAR_CREATION_FAILURE = 'CALENDAR_CREATION_FAILURE',\n  /** Calendar was deleted while sync was in progress. */\n  CALENDAR_DELETED = 'CALENDAR_DELETED',\n}\n\n/** @enumType */\nexport type SyncToErrorReasonWithLiterals =\n  | SyncToErrorReason\n  | 'UNDEFINED'\n  | 'CALENDAR_CREATION_FAILURE'\n  | 'CALENDAR_DELETED';\n\nexport interface ListConnectionsRequest {\n  /**\n   * _Schedule IDs_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to filter by.\n   *\n   * Default: Returns all connections.\n   * @format GUID\n   */\n  scheduleIds?: string[] | null;\n  /**\n   * Whether to return a partial list of connections if details can't be\n   * retrieved for all connections.\n   *\n   * Default: `false`\n   */\n  partialFailure?: boolean | null;\n}\n\nexport interface ListConnectionsResponse {\n  /** Retrieved external calendar connections. */\n  connections?: Connection[];\n  /**\n   * List of provider IDs for which connection retrieval failed. Returned only\n   * if you specify `{\"partialFailure\": true}`.\n   */\n  failedProviderIds?: string[] | null;\n}\n\nexport interface ConnectByOAuthRequest {\n  /**\n   * ID of the schedule to connect with the external calendar account.\n   * @format GUID\n   */\n  scheduleId: string | null;\n  /**\n   * ID of the external calendar provider. Find this with the [List Providers](#list-providers) endpoint.\n   * @format GUID\n   */\n  providerId: string | null;\n  /**\n   * URL to redirect the user to after they authorize access to the external calendar account.\n   *\n   * If the connection is successfully established, the user is redirected to this URL with a query parameter `connectionId` containing the new connection ID.\n   * If the attempt to connect fails, the user is redirected to this URL with a query parameter `error` containing the error type.\n   * @format WEB_URL\n   */\n  redirectUrl: string | null;\n}\n\nexport interface ConnectByOAuthResponse {\n  /**\n   * URL of the external calendar authorization page to redirect the user to.\n   * @format WEB_URL\n   */\n  oauthUrl?: string | null;\n}\n\nexport interface RawHttpRequest {\n  body?: Uint8Array;\n  pathParams?: PathParametersEntry[];\n  queryParams?: QueryParametersEntry[];\n  headers?: HeadersEntry[];\n  method?: string;\n  rawPath?: string;\n  rawQuery?: string;\n}\n\nexport interface PathParametersEntry {\n  key?: string;\n  value?: string;\n}\n\nexport interface QueryParametersEntry {\n  key?: string;\n  value?: string;\n}\n\nexport interface HeadersEntry {\n  key?: string;\n  value?: string;\n}\n\nexport interface RawHttpResponse {\n  body?: Uint8Array;\n  statusCode?: number | null;\n  headers?: HeadersEntry[];\n}\n\nexport interface ConnectByCredentialsRequest {\n  /**\n   * ID of the schedule to connect with the external calendar account.\n   * @format GUID\n   */\n  scheduleId: string | null;\n  /**\n   * ID of the external calendar provider. Find this with the [List Providers](#list-providers) endpoint.\n   * @format GUID\n   */\n  providerId: string | null;\n  /**\n   * Email address for the external calendar account.\n   * @format EMAIL\n   */\n  email: string | null;\n  /** Password for the external calendar account. */\n  password: string | null;\n}\n\nexport interface ConnectByCredentialsResponse {\n  /** Established connection details. */\n  connection?: Connection;\n}\n\nexport interface ListCalendarsRequest {\n  /** ID of the external calendar connection to list calendars for. */\n  connectionId: string | null;\n}\n\nexport interface ListCalendarsResponse {\n  /** List of calendars belonging to the external calendar account. */\n  calendars?: Calendar[];\n}\n\nexport interface UpdateSyncConfigRequest {\n  /** ID of the external calendar connection to update. */\n  connectionId: string | null;\n  /** Updated sync configuration details. */\n  syncConfig: ConnectionSyncConfig;\n}\n\nexport interface UpdateSyncConfigResponse {\n  /** Connection with updated sync configuration. */\n  connection?: Connection;\n}\n\nexport interface DisconnectRequest {\n  /** ID of the external calendar connection to disconnect. */\n  connectionId: string | null;\n}\n\nexport interface DisconnectResponse {\n  /** Updated connection details. */\n  connection?: Connection;\n}\n\nexport interface ListEventsRequest {\n  /**\n   * Date and time from which to retrieve events,\n   * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).\n   * Required, unless `cursorPaging.cursor` is provided.\n   *\n   * Events which start before the `from` time and end after it are included in the returned list.\n   */\n  from?: string | null;\n  /**\n   * Date and time until which to retrieve events,\n   * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).\n   * Required, unless `cursorPaging.cursor` is provided.\n   *\n   * Events which start before the `to` time and end after it are included in the returned list.\n   */\n  to?: string | null;\n  /**\n   * Schedule IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to the specified schedules.\n   * Maximum of 100 schedule IDs per request.\n   * @format GUID\n   * @maxSize 100\n   */\n  scheduleIds?: string[] | null;\n  /**\n   * Wix user IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to schedules belonging to the specified Wix users.\n   * Maximum of 100 Wix user IDs per request.\n   * @format GUID\n   * @maxSize 100\n   */\n  userIds?: string[] | null;\n  /**\n   * Whether to include only all-day events in the returned list.\n   * If `true`, only all-day events are returned.\n   * If `false`, only events with a specified time are returned.\n   *\n   * Default: All events are returned.\n   */\n  allDay?: boolean | null;\n  /**\n   * Predefined sets of fields to return.\n   * - `NO_PI`: Returns event objects without personal information.\n   * - `OWN_PI`: Returns complete event objects, including personal information.\n   *\n   * Default: `NO_PI`\n   */\n  fieldsets?: string[];\n  /** Pagination options. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Whether to return a partial list of events if details can't be retrieved for some connections.\n   *\n   * Default: `false`\n   */\n  partialFailure?: boolean | null;\n}\n\nexport interface CursorPaging {\n  /**\n   * Number of events to load.\n   * Max: `1000`\n   * @max 1000\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * You can get the relevant cursor token\n   * from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   */\n  cursor?: string | null;\n}\n\nexport interface ListEventsResponse {\n  /** List of external calendar events matching the filters. */\n  events?: Event[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n  /** List of provider IDs for connections for which retrieval of events failed. Returned only if `partialFailure` body parameter is `true` in the request. */\n  failedProviderIds?: string[] | null;\n}\n\n/** An external calendar event. */\nexport interface Event {\n  /**\n   * ID of the *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to which the event belongs.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n  /** External calendar type. */\n  calendarType?: CalendarTypeWithLiterals;\n  /**\n   * Display name of the external calendar.\n   * For example, `Primary` or `Birthdays`.\n   */\n  calendarName?: string | null;\n  /** Event title. */\n  title?: string | null;\n  /** Start date and time of the event (inclusive), formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt). */\n  start?: string | null;\n  /** End date and time of the event (exclusive), formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt). */\n  end?: string | null;\n  /**\n   * Whether the event is an all-day event.\n   *\n   * Default: `false`\n   */\n  allDay?: boolean | null;\n  /**\n   * ID of the *Wix user*\n   * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))\n   * to whom the schedule belongs.\n   * For Bookings *staff members*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n   * identical to their *resource ID*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).\n   */\n  scheduleOwnerId?: string | null;\n  /**\n   * Name of the *Wix user*\n   * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities#wix-user) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities#wix-user))\n   * to whom the schedule belongs.\n   * For example, the `name` of a Bookings *staff member*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)).\n   */\n  scheduleOwnerName?: string | null;\n}\n\nexport interface CursorPagingMetadata {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  cursors?: Cursors;\n  /**\n   * Indicates if there are more results after the current page.\n   * If `true`, another page of results can be retrieved.\n   * If `false`, this is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /** Cursor pointing to next page in the list of results. */\n  next?: string | null;\n}\n\nexport interface ScheduleNotification extends ScheduleNotificationEventOneOf {\n  scheduleCreated?: ScheduleCreated;\n  scheduleUpdated?: ScheduleUpdated;\n  scheduleCancelled?: ScheduleCancelled;\n  sessionCreated?: SessionCreated;\n  sessionUpdated?: SessionUpdated;\n  sessionCancelled?: SessionCancelled;\n  availabilityPolicyUpdated?: AvailabilityPolicyUpdated;\n  /** @deprecated */\n  intervalSplit?: IntervalSplit;\n  recurringSessionSplit?: RecurringSessionSplit;\n  /**\n   * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.\n   * @deprecated\n   */\n  scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;\n  preserveFutureSessionsWithParticipants?: boolean | null;\n  /**\n   * Whether to notify participants about changed sessions. deprecated, use participant_notification\n   * @deprecated\n   */\n  notifyParticipants?: boolean;\n  /** site properties. Optional. Given in create schedule notification. */\n  siteProperties?: SitePropertiesOnScheduleCreation;\n  instanceId?: string;\n}\n\n/** @oneof */\nexport interface ScheduleNotificationEventOneOf {\n  scheduleCreated?: ScheduleCreated;\n  scheduleUpdated?: ScheduleUpdated;\n  scheduleCancelled?: ScheduleCancelled;\n  sessionCreated?: SessionCreated;\n  sessionUpdated?: SessionUpdated;\n  sessionCancelled?: SessionCancelled;\n  availabilityPolicyUpdated?: AvailabilityPolicyUpdated;\n  /** @deprecated */\n  intervalSplit?: IntervalSplit;\n  recurringSessionSplit?: RecurringSessionSplit;\n  /**\n   * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.\n   * @deprecated\n   */\n  scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;\n}\n\nexport interface ScheduleCreated {\n  schedule?: Schedule;\n}\n\nexport interface Schedule {\n  /** Schedule ID. */\n  _id?: string;\n  /** ID of the schedule's owner entity. This may be a resource ID or a service ID. */\n  scheduleOwnerId?: string | null;\n  /**\n   * Schedule's time zone in [Area/Location](https://en.wikipedia.org/wiki/Tz_database) format. Read-only.\n   * Derived from the Wix Business time zone.\n   * @readonly\n   */\n  timeZone?: string | null;\n  /**\n   * Deprecated. Please use the [Sessions API](https://dev.wix.com/api/rest/wix-bookings/schedules-and-sessions/session) instead.\n   * @deprecated\n   */\n  intervals?: RecurringInterval[];\n  /**\n   * Default title for the schedule's sessions. Maximum length: 6000 characters.\n   * @maxLength 6000\n   */\n  title?: string | null;\n  /**\n   * __Deprecated.__\n   * Tags for grouping schedules. These tags are the default tags for the schedule's sessions.\n   * The Wix Bookings app uses the following predefined tags to set schedule type: `\"INDIVIDUAL\"`, `\"GROUP\"`, and `\"COURSE\"`. Once the schedule type is set using these tags, you cannot update it. In addition to the app's tags, you can create and update your own tags.\n   * @deprecated\n   */\n  tags?: string[] | null;\n  /** Default location for the schedule's sessions. */\n  location?: Location;\n  /**\n   * Maximum number of participants that can be added to the schedule's sessions.\n   * Must be at most `1` for schedule whose availability is affected by another schedule. E.g, appointment schedules of the Wix Bookings app.\n   * @min 1\n   * @max 1000\n   */\n  capacity?: number | null;\n  /**\n   * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.\n   * @deprecated\n   */\n  rate?: Rate;\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  availability?: Availability;\n  /**\n   * Number of participants registered to sessions in this schedule, calculated as the sum of the party sizes.\n   * @readonly\n   */\n  totalNumberOfParticipants?: number;\n  /**\n   * *Partial list** of participants which are registered to sessions in this schedule.\n   * Participants who are registered in the schedule are automatically registered to any session that is created for the schedule.\n   * To retrieve the full list of schedule participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).\n   * @readonly\n   */\n  participants?: Participant[];\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  externalCalendarOverrides?: ExternalCalendarOverrides;\n  /**\n   * Schedule status. Default: Created\n   * @readonly\n   */\n  status?: ScheduleStatusWithLiterals;\n  /**\n   * Schedule creation date.\n   * @readonly\n   */\n  created?: Date | null;\n  /**\n   * Schedule last update date.\n   * @readonly\n   */\n  updated?: Date | null;\n  /**\n   * Schedule version number, updated each time the schedule is updated.\n   * @readonly\n   */\n  version?: number;\n  /**\n   * Fields which were inherited from the Business Info page under Settings in the Dashboard.\n   * @readonly\n   */\n  inheritedFields?: string[];\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  conferenceProvider?: ConferenceProvider;\n  /**\n   * A conference created for the schedule. This is used when a participant is added to a schedule.\n   * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.\n   * @deprecated\n   */\n  calendarConference?: CalendarConference;\n}\n\nexport interface RecurringInterval {\n  /**\n   * The recurring interval identifier.\n   * @readonly\n   */\n  _id?: string;\n  /** The start time of the recurring interval. Required. */\n  start?: Date | null;\n  /** The end time of the recurring interval. Optional. Empty value indicates that there is no end time. */\n  end?: Date | null;\n  /** The interval rules. The day, hour and minutes the interval is recurring. */\n  interval?: Interval;\n  /** The frequency of the interval. Optional. The default is frequency with the default repetition. */\n  frequency?: Frequency;\n  /** Specifies the list of linked schedules and the way this link affects the corresponding schedules' availability. Can be calculated from the schedule or overridden on the recurring interval. */\n  affectedSchedules?: LinkedSchedule[];\n  /** The type of recurring interval. */\n  intervalType?: RecurringIntervalTypeWithLiterals;\n}\n\nexport interface Interval {\n  /** The day the interval occurs. Optional. The default is the day of the recurring interval's start time. */\n  daysOfWeek?: DayWithLiterals;\n  /**\n   * The hour of the day the interval occurs. Must be consistent with the interval start time. Optional. The default is 0. Minimum: 0, maximum: 23.\n   * @max 23\n   */\n  hourOfDay?: number | null;\n  /**\n   * The minutes of the hour the interval accrues. Must be consistent with the interval end time. Optional. The default is 0. Minimum: 0, maximum: 59.\n   * @max 59\n   */\n  minuteOfHour?: number | null;\n  /** The duration of the interval in minutes. Required. Part of the session end time calculation. */\n  duration?: number;\n}\n\nexport enum Day {\n  /** Undefined. */\n  UNDEFINED = 'UNDEFINED',\n  /** Monday. */\n  MON = 'MON',\n  /** Tuesday. */\n  TUE = 'TUE',\n  /** Wednesday. */\n  WED = 'WED',\n  /** Thursday. */\n  THU = 'THU',\n  /** Friday. */\n  FRI = 'FRI',\n  /** Saturday. */\n  SAT = 'SAT',\n  /** Sunday. */\n  SUN = 'SUN',\n}\n\n/** @enumType */\nexport type DayWithLiterals =\n  | Day\n  | 'UNDEFINED'\n  | 'MON'\n  | 'TUE'\n  | 'WED'\n  | 'THU'\n  | 'FRI'\n  | 'SAT'\n  | 'SUN';\n\nexport interface Frequency {\n  /**\n   * The frequency of the recurrence in weeks. i.e. when this value is 4, the interval occurs every 4 weeks. Optional. The default is 1. minimum: 1, maximum: 52.\n   * @min 1\n   * @max 52\n   */\n  repetition?: number | null;\n}\n\nexport interface LinkedSchedule {\n  /**\n   * Schedule ID.\n   * @format GUID\n   */\n  scheduleId?: string;\n  /** Sets this schedule's availability for the duration of the linked schedule's sessions. Default is `\"BUSY\"`. */\n  transparency?: TransparencyWithLiterals;\n  /**\n   * Owner ID, of the linked schedule.\n   * @readonly\n   */\n  scheduleOwnerId?: string;\n}\n\nexport enum Transparency {\n  UNDEFINED = 'UNDEFINED',\n  /** The schedule can have available slots during the linked schedule's sessions. */\n  FREE = 'FREE',\n  /** The schedule can't have available slots during the linked schedule's sessions. */\n  BUSY = 'BUSY',\n}\n\n/** @enumType */\nexport type TransparencyWithLiterals =\n  | Transparency\n  | 'UNDEFINED'\n  | 'FREE'\n  | 'BUSY';\n\nexport enum RecurringIntervalType {\n  /** The default value. Sessions for this interval will be of type EVENT. */\n  UNDEFINED = 'UNDEFINED',\n  /** A recurring interval of events. */\n  EVENT = 'EVENT',\n  /** Deprecated. */\n  TIME_AVAILABILITY = 'TIME_AVAILABILITY',\n  /** A recurring interval for availability. */\n  AVAILABILITY = 'AVAILABILITY',\n}\n\n/** @enumType */\nexport type RecurringIntervalTypeWithLiterals =\n  | RecurringIntervalType\n  | 'UNDEFINED'\n  | 'EVENT'\n  | 'TIME_AVAILABILITY'\n  | 'AVAILABILITY';\n\nexport interface Location {\n  /**\n   * Location type.\n   * One of:\n   * - `\"OWNER_BUSINESS\"` The business address as set in the site’s general settings.\n   * - `\"OWNER_CUSTOM\"` The address as set when creating the service.\n   * - `\"CUSTOM\"` The address set for the individual session.\n   */\n  locationType?: LocationTypeWithLiterals;\n  /**\n   * Free text address used when locationType is `OWNER_CUSTOM`.\n   * @deprecated\n   */\n  address?: string | null;\n  /** Custom address, used when locationType is `\"OWNER_CUSTOM\"`. Might be used when locationType is `\"CUSTOM\"` in case the owner sets a custom address for the session which is different from the default. */\n  customAddress?: Address;\n}\n\nexport enum LocationType {\n  UNDEFINED = 'UNDEFINED',\n  OWNER_BUSINESS = 'OWNER_BUSINESS',\n  OWNER_CUSTOM = 'OWNER_CUSTOM',\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNDEFINED'\n  | 'OWNER_BUSINESS'\n  | 'OWNER_CUSTOM'\n  | 'CUSTOM';\n\n/** Physical address */\nexport interface Address extends AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n  /**\n   * Country code.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  subdivision?: string | null;\n  /** City name. */\n  city?: string | null;\n  /** Zip/postal code. */\n  postalCode?: string | null;\n  /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */\n  addressLine2?: string | null;\n  /** A string containing the full address of this location. */\n  formattedAddress?: string | null;\n  /** Free text to help find the address. */\n  hint?: string | null;\n  /** Coordinates of the physical address. */\n  geocode?: AddressLocation;\n  /** Country full name. */\n  countryFullname?: string | null;\n  /** Multi-level subdivisions from top to bottom. */\n  subdivisions?: Subdivision[];\n}\n\n/** @oneof */\nexport interface AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n}\n\nexport interface StreetAddress {\n  /** Street number. */\n  number?: string;\n  /** Street name. */\n  name?: string;\n  /** Apartment number. */\n  apt?: string;\n}\n\nexport interface AddressLocation {\n  /** Address latitude. */\n  latitude?: number | null;\n  /** Address longitude. */\n  longitude?: number | null;\n}\n\nexport interface Subdivision {\n  /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  code?: string;\n  /** Subdivision full name. */\n  name?: string;\n}\n\nexport interface LocationsLocation {\n  /**\n   * Location ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Location name.\n   * @maxLength 150\n   */\n  name?: string;\n  /**\n   * Location description.\n   * @maxLength 500\n   */\n  description?: string | null;\n  /**\n   * Whether this is the default location. There can only be one default location per site. The default location can't be archived.\n   * @readonly\n   */\n  default?: boolean;\n  /**\n   * Location status. Defaults to `ACTIVE`.\n   * __Notes:__\n   * - [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)\n   * doesn't affect the location's status.\n   * - `INACTIVE` status is currently not supported.\n   */\n  status?: LocationStatusWithLiterals;\n  /**\n   * Location type.\n   *\n   * **Note:** Currently not supported.\n   * @deprecated\n   */\n  locationType?: LocationsLocationTypeWithLiterals;\n  /** Fax number. */\n  fax?: string | null;\n  /** Timezone in `America/New_York` format. */\n  timeZone?: string | null;\n  /** Email address. */\n  email?: string | null;\n  /** Phone number. */\n  phone?: string | null;\n  /** Address. */\n  address?: LocationsAddress;\n  /**\n   * Business schedule. Array of weekly recurring time periods when the location is open for business. Limited to 100 time periods.\n   *\n   * __Note:__ Not supported by Wix Bookings.\n   */\n  businessSchedule?: BusinessSchedule;\n  /**\n   * Revision number, which increments by 1 each time the location is updated.\n   * To prevent conflicting changes, the existing revision must be used when updating a location.\n   */\n  revision?: string | null;\n  /**\n   * Whether the location is archived. Archived locations can't be updated.\n   * __Note:__ [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)\n   * doesn't affect its `status`.\n   * @readonly\n   */\n  archived?: boolean;\n  /**\n   * Location types.\n   * @maxSize 10\n   */\n  locationTypes?: LocationsLocationTypeWithLiterals[];\n}\n\n/** For future use */\nexport enum LocationStatus {\n  ACTIVE = 'ACTIVE',\n  INACTIVE = 'INACTIVE',\n}\n\n/** @enumType */\nexport type LocationStatusWithLiterals = LocationStatus | 'ACTIVE' | 'INACTIVE';\n\n/** For future use */\nexport enum LocationsLocationType {\n  UNKNOWN = 'UNKNOWN',\n  BRANCH = 'BRANCH',\n  OFFICES = 'OFFICES',\n  RECEPTION = 'RECEPTION',\n  HEADQUARTERS = 'HEADQUARTERS',\n  INVENTORY = 'INVENTORY',\n}\n\n/** @enumType */\nexport type LocationsLocationTypeWithLiterals =\n  | LocationsLocationType\n  | 'UNKNOWN'\n  | 'BRANCH'\n  | 'OFFICES'\n  | 'RECEPTION'\n  | 'HEADQUARTERS'\n  | 'INVENTORY';\n\nexport interface LocationsAddress {\n  /**\n   * 2-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /** Code for a subdivision (such as state, prefecture, or province) in [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) format. */\n  subdivision?: string | null;\n  /** City name. */\n  city?: string | null;\n  /**\n   * Postal or zip code.\n   * @maxLength 20\n   */\n  postalCode?: string | null;\n  /** Street address. Includes street name, number, and apartment number in separate fields. */\n  streetAddress?: LocationsStreetAddress;\n  /** Full address of the location. */\n  formatted?: string | null;\n  /** Geographic coordinates of location. */\n  location?: LocationsAddressLocation;\n}\n\n/** Street address. Includes street name, number, and apartment number in separate fields. */\nexport interface LocationsStreetAddress {\n  /** Street number. */\n  number?: string;\n  /** Street name. */\n  name?: string;\n  /** Apartment number. */\n  apt?: string;\n}\n\n/** Address Geolocation */\nexport interface LocationsAddressLocation {\n  /** Latitude of the location. Must be between -90 and 90. */\n  latitude?: number | null;\n  /** Longitude of the location. Must be between -180 and 180. */\n  longitude?: number | null;\n}\n\n/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */\nexport interface BusinessSchedule {\n  /**\n   * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.\n   * @maxSize 100\n   */\n  periods?: TimePeriod[];\n  /**\n   * Exceptions to the business's regular hours. The business can be open or closed during the exception.\n   * @maxSize 100\n   */\n  specialHourPeriod?: SpecialHourPeriod[];\n}\n\n/** Weekly recurring time periods when the business is regularly open or the service is available. */\nexport interface TimePeriod {\n  /** Day of the week the period starts on. */\n  openDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   */\n  openTime?: string;\n  /** Day of the week the period ends on. */\n  closeDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   *\n   * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.\n   */\n  closeTime?: string;\n}\n\n/** Enumerates the days of the week. */\nexport enum DayOfWeek {\n  MONDAY = 'MONDAY',\n  TUESDAY = 'TUESDAY',\n  WEDNESDAY = 'WEDNESDAY',\n  THURSDAY = 'THURSDAY',\n  FRIDAY = 'FRIDAY',\n  SATURDAY = 'SATURDAY',\n  SUNDAY = 'SUNDAY',\n}\n\n/** @enumType */\nexport type DayOfWeekWithLiterals =\n  | DayOfWeek\n  | 'MONDAY'\n  | 'TUESDAY'\n  | 'WEDNESDAY'\n  | 'THURSDAY'\n  | 'FRIDAY'\n  | 'SATURDAY'\n  | 'SUNDAY';\n\n/** Exception to the business's regular hours. The business can be open or closed during the exception. */\nexport interface SpecialHourPeriod {\n  /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  startDate?: string;\n  /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  endDate?: string;\n  /**\n   * Whether the business is closed (or the service is not available) during the exception.\n   *\n   * Default: `true`.\n   */\n  isClosed?: boolean;\n  /** Additional info about the exception. For example, \"We close earlier on New Year's Eve.\" */\n  comment?: string;\n}\n\nexport interface Rate {\n  /**\n   * Mapping between a named price option, for example, adult or child prices, and the price, currency, and down payment amount.\n   * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.\n   */\n  labeledPriceOptions?: Record<string, Price>;\n  /**\n   * Textual price information used when **Price Per Session** is set to **Custom Price** in the app's service details page.\n   * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.\n   */\n  priceText?: string | null;\n}\n\nexport interface Price {\n  /**\n   * Required payment amount.\n   * @format DECIMAL_VALUE\n   */\n  amount?: string;\n  /**\n   * Currency in which the amount is quoted.\n   * @format CURRENCY\n   */\n  currency?: string;\n  /**\n   * Amount of a down payment or deposit as part of the transaction.\n   * @format DECIMAL_VALUE\n   */\n  downPayAmount?: string;\n}\n\n/**\n * <!-- Needs updating when recurrence has been tested\n * Schedule's availability calculation is executed by the schedule's available intervals and this additional information.\n * Schedule's available intervals are recurring intervals (defined in the schedule) minus sessions that has no more spots for bookings (including time between_slots), or schedule's sessions with open spots for bookings.-->\n */\nexport interface Availability {\n  /** Date and time the schedule starts to be available for booking. */\n  start?: Date | null;\n  /** Date and time the schedule stops being available for booking. No value indicates no end time. */\n  end?: Date | null;\n  /** Other schedules that impact the availability calculation. Relevant only when there are availability constraints. */\n  linkedSchedules?: LinkedSchedule[];\n  /** Constraints for calculating the schedule's availability. */\n  constraints?: AvailabilityConstraints;\n}\n\n/** Describes how to calculate the specific slots that are available for booking. */\nexport interface AvailabilityConstraints {\n  /**\n   * A list of duration options for slots, in minutes. Minimum value for a duration is 1.\n   * The availability calculation generates slots with these durations, where there is no conflict with existing sessions or other availability constraints.\n   * @min 1\n   */\n  slotDurations?: number[];\n  /**\n   * The number of minutes between the `end` of one slot, and the `start` of the next.\n   * Minimum value is 0, maximum value is 120.\n   * @max 720\n   */\n  timeBetweenSlots?: number;\n  /**\n   * Specify how to split the slots in intervals of minutes.\n   * This value indicates the time between available slots' start time. e.g., from 5 minute slots (3:00, 3:05, 3:15) and 1 hour slots (3:00, 4:00, 5:00).\n   * Optional. The default is the first duration in slot_durations field.\n   * Deprecated. Use the `split_slots_interval.value_in_minutes`.\n   * @deprecated\n   */\n  splitInterval?: number | null;\n  /**\n   * An object defining the time between available slots' start times.  For example, a slot with slots_split_interval=5 can start every 5 minutes. The default is the slot duration.\n   * @readonly\n   */\n  slotsSplitInterval?: SplitInterval;\n}\n\n/** The time between available slots' start times. For example, For 5 minute slots, 3:00, 3:05, 3:15 etc. For 1 hour slots, 3:00, 4:00, 5:00 etc. */\nexport interface SplitInterval {\n  /**\n   * Whether the slot duration is used as the split interval value.\n   * If `same_as_duration` is `true`, the `value_in_minutes` is the sum of the first duration in\n   * `schedule.availabilityConstraints.SlotDurations` field, and `schedule.availabilityConstraints.TimeBetweenSlots` field.\n   */\n  sameAsDuration?: boolean | null;\n  /** Number of minutes between available slots' start times when `same_as_duration` is `false`. */\n  valueInMinutes?: number | null;\n}\n\nexport interface Participant {\n  /**\n   * Participant ID. Currently represents the booking.id.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Contact ID.\n   * @format GUID\n   */\n  contactId?: string | null;\n  /** Participant's name. */\n  name?: string | null;\n  /** Participant's phone number. */\n  phone?: string | null;\n  /** Participant's email address. */\n  email?: string | null;\n  /** Group or party size. The number of people attending. Defaults to 0. Maximum is 250. */\n  partySize?: number;\n  /**\n   * Approval status for the participant.\n   * <!-- Commented out untill updateParticipant is exposed Generally the same status as the booking, unless updated using the `updateParticipant()` API. Defaults to `\"UNDEFINED\"`.-->\n   */\n  approvalStatus?: ApprovalStatusWithLiterals;\n  /**\n   * Whether the participant was inherited from the schedule, as opposed to being booked directly to the session.\n   * @readonly\n   */\n  inherited?: boolean;\n}\n\nexport enum ApprovalStatus {\n  /** Default. */\n  UNDEFINED = 'UNDEFINED',\n  /** Pending business approval. */\n  PENDING = 'PENDING',\n  /** Approved by the business. */\n  APPROVED = 'APPROVED',\n  /** Declined by the business. */\n  DECLINED = 'DECLINED',\n}\n\n/** @enumType */\nexport type ApprovalStatusWithLiterals =\n  | ApprovalStatus\n  | 'UNDEFINED'\n  | 'PENDING'\n  | 'APPROVED'\n  | 'DECLINED';\n\nexport interface ExternalCalendarOverrides {\n  /** Synced title of the external calendar event. */\n  title?: string | null;\n  /** Synced description of the external calendar event. */\n  description?: string | null;\n}\n\nexport enum ScheduleStatus {\n  /** Undefined schedule status. */\n  UNDEFINED = 'UNDEFINED',\n  /** The schedule was created. */\n  CREATED = 'CREATED',\n  /** The schedule was cancelled. */\n  CANCELLED = 'CANCELLED',\n}\n\n/** @enumType */\nexport type ScheduleStatusWithLiterals =\n  | ScheduleStatus\n  | 'UNDEFINED'\n  | 'CREATED'\n  | 'CANCELLED';\n\nexport interface Version {\n  /** Schedule version number, updated each time the schedule is updated. */\n  scheduleVersion?: number | null;\n  /** Participants version number, updated each time the schedule participants are updated. */\n  participantsVersion?: number | null;\n}\n\nexport interface ConferenceProvider {\n  /** Conferencing provider ID */\n  providerId?: string;\n}\n\nexport interface CalendarConference {\n  /** Wix Calendar conference ID. */\n  _id?: string;\n  /** Conference meeting ID in the provider's conferencing system. */\n  externalId?: string;\n  /** Conference provider ID. */\n  providerId?: string;\n  /** URL used by the host to start the conference. */\n  hostUrl?: string;\n  /** URL used by a guest to join the conference. */\n  guestUrl?: string;\n  /** Password to join the conference. */\n  password?: string | null;\n  /** Conference description. */\n  description?: string | null;\n  /** Conference type. */\n  conferenceType?: ConferenceTypeWithLiterals;\n  /** ID of the account owner in the video conferencing service. */\n  accountOwnerId?: string | null;\n}\n\nexport enum ConferenceType {\n  /** Undefined conference type. */\n  UNDEFINED = 'UNDEFINED',\n  /** API-generated online meeting. */\n  ONLINE_MEETING_PROVIDER = 'ONLINE_MEETING_PROVIDER',\n  /** User-defined meeting. */\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type ConferenceTypeWithLiterals =\n  | ConferenceType\n  | 'UNDEFINED'\n  | 'ONLINE_MEETING_PROVIDER'\n  | 'CUSTOM';\n\nexport interface ScheduleUpdated {\n  /** The old schedule before the update. */\n  oldSchedule?: Schedule;\n  /** The new schedule after the update. */\n  newSchedule?: Schedule;\n  /**\n   * Recurring sessions updated event. If this field is given, the reason for the schedule updated event was\n   * updating at least one of the given schedule's recurring sessions.\n   * This event is triggered by create/update/delete recurring session apis.\n   */\n  recurringSessions?: RecurringSessionsUpdated;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether this notification was created as a result of an anonymization request, such as GDPR.\n   * An anonymized participant will have the following details:\n   * name = \"deleted\"\n   * phone = \"deleted\"\n   * email = \"<EMAIL>\"\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n}\n\nexport interface RecurringSessionsUpdated {\n  /** Old schedule's recurring session list. */\n  oldRecurringSessions?: Session[];\n  /** New schedule's recurring session list. */\n  newRecurringSessions?: Session[];\n}\n\nexport interface Session {\n  /**\n   * Session ID.\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * ID of the schedule that the session belongs to.\n   * @immutable\n   */\n  scheduleId?: string;\n  /**\n   * ID of the resource or service that the session's schedule belongs to.\n   * @readonly\n   */\n  scheduleOwnerId?: string | null;\n  /** Original start date and time of the session in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. */\n  originalStart?: Date | null;\n  /** An object specifying the start date and time of the session. If the session is a recurring session, `start` must contain a `localDateTime`. */\n  start?: CalendarDateTime;\n  /**\n   * An object specifying the end date and time of the session. The `end` time must be after the `start` time and be same type as `start`.\n   * If the session is a recurring session, `end` must contain a `localDateTime`.\n   */\n  end?: CalendarDateTime;\n  /**\n   * An object specifying a list of schedules and the way each schedule's availability is affected by the session. For example, the schedule of an instructor is affected by sessions of the class that they instruct.\n   * The array is inherited from the schedule and can be overridden even if the session is a recurring session.\n   */\n  affectedSchedules?: LinkedSchedule[];\n  /**\n   * Session title.\n   * The value is inherited from the schedule and can be overridden unless the session is a recurring session.\n   * @maxLength 6000\n   */\n  title?: string | null;\n  /**\n   * __Deprecated.__\n   * Tags for the session.\n   * The value is inherited from the schedule and can be overridden unless the session is a recurring session.\n   * @maxLength 200\n   * @deprecated\n   */\n  tags?: string[] | null;\n  /**\n   * An object describing the location where the session takes place.\n   * Defaults to the schedule location.\n   * For single sessions, `session.location.businessLocation` can only be provided for locations that are defined in the schedule using `schedule.location` or `schedule.availability.locations`.\n   */\n  location?: Location;\n  /**\n   * Maximum number of participants that can be added to the session. Defaults to the schedule capacity.\n   * The value is inherited from the schedule and can be overridden unless the session is a recurring session.\n   * @max 1000\n   */\n  capacity?: number | null;\n  /**\n   * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.\n   * @deprecated\n   */\n  rate?: Rate;\n  /**\n   * Time reserved after the session end time, derived from the schedule availability constraints and the time between slots. Read-only.\n   * If the session is a recurring session, this field must be empty.\n   */\n  timeReservedAfter?: number | null;\n  /**\n   * Additional information about the session.\n   * Notes are not supported for recurring sessions.\n   * @maxLength 10000\n   */\n  notes?: string;\n  /**\n   * The number of participants booked for the session. Read-only.\n   * Calculated as the sum of the party sizes.\n   * @readonly\n   */\n  totalNumberOfParticipants?: number;\n  /**\n   * *Partial list** list of participants booked for the session.\n   * The list includes participants who have registered for this specific session, and participants who have registered for a schedule that includes this session.\n   * If the session is a recurring session, this field must be empty.\n   * To retrieve the full list of session participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).\n   */\n  participants?: Participant[];\n  /**\n   * A list of properties for which values were inherited from the schedule.\n   * This does not include participants that were inherited from the schedule.\n   * @readonly\n   */\n  inheritedFields?: string[];\n  /**\n   * __Deprecated.__\n   * @deprecated\n   */\n  externalCalendarOverrides?: ExternalCalendarOverrides;\n  /**\n   * Session status.\n   * @readonly\n   */\n  status?: SessionStatusWithLiterals;\n  /**\n   * Recurring interval ID. Defined when a session will be a recurring session. read-only. Optional.\n   * For example, when creating a class service  with recurring sessions, you add a recurrence rule to create recurring sessions.\n   * This field is omitted for single sessions or instances of recurring sessions.\n   * Specified when the session was originally generated from a schedule recurring interval.\n   * Deprecated. Use `recurringSessionId`.\n   * @readonly\n   * @deprecated\n   */\n  recurringIntervalId?: string | null;\n  /**\n   * The ID of the recurring session if this session is an instance of a recurrence. Use this ID to update the recurrence and all of the instances.\n   * @readonly\n   */\n  recurringSessionId?: string | null;\n  /** Session type. */\n  type?: SessionTypeWithLiterals;\n  /**\n   * A conference created for the session according to the details set in the schedule's conference provider information.\n   * If the session is a recurring session, this field is inherited from the schedule.\n   * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.\n   * @deprecated\n   */\n  calendarConference?: CalendarConference;\n  /**\n   * A string representing a recurrence rule (RRULE) for a recurring session, as defined in [iCalendar RFC 5545](https://icalendar.org/iCalendar-RFC-5545/3-3-10-recurrence-rule.html).\n   * If the session is an instance of a recurrence pattern, the `instanceOfRecurrence` property will be contain the recurrence rule and this property will be empty.\n   * The RRULE defines a rule for repeating a session.\n   * Supported parameters are:\n   *\n   * |Keyword|Description|Supported values|\n   * |--|--|---|\n   * |`FREQ`|The frequency at which the session is recurs. Required.|`WEEKLY`|\n   * |`INTERVAL`|How often, in terms of `FREQ`, the session recurs. Default is 1. Optional.|\n   * |`UNTIL`|The UTC end date and time of the recurrence. Optional.|\n   * |`BYDAY`|Day of the week when the event should recur. Required.|One of: `MO`, `TU`, `WE`, `TH`, `FR`, `SA`, `SU`|\n   *\n   *\n   * For example, a session that repeats every second week on a Monday until January 7, 2022 at 8 AM:\n   * `\"FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20220107T080000Z\"`\n   *\n   * <!--ORIGINAL COMMENTS:\n   * `FREQ` — The frequency with which the session should be repeated (such as DAILY or WEEKLY).\n   * Supported `WEEKLY` value is supported.\n   * INTERVAL — Works together with FREQ to specify how often the session should be repeated. For example, FREQ=WEEKLY;INTERVAL=2 means once every two weeks. Optional. Default value is 1.\n   * COUNT — The number of times this event should be repeated. Not yet supported.\n   * UNTIL — The UTC date & time until which the session should be repeated. This parameter is optional. When it is not specified, the event repeats forever.\n   * The format is a short ISO date, followed by 'T' and a short time with seconds and without milliseconds, terminated by the UTC designator 'Z'. For example, until Jan. 19th 2018 at 7:00 AM: 'UNTIL=20180119T070000Z'.\n   * BYDAY - The days of the week when the event should be repeated. Currently, only a single day is supported. This parameter is mandatory.\n   * Possible values are: MO, TU, WE, TH, FR, SA, SU\n   * Note that DTSTART and DTEND lines are not allowed in this field; session start and end times are specified in the start and end fields.\n   * **Example**: FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20200427T070000Z\n   * ORIGINAL COMMENTS-->\n   */\n  recurrence?: string | null;\n  /**\n   * A string representing a recurrence rule (RRULE) if the session is an instance of a recurrence pattern.\n   * Empty when the session is not an instance of a recurrence rule, or if the session defines a recurrence pattern, and `recurrence` is not empty.\n   * @readonly\n   */\n  instanceOfRecurrence?: string | null;\n  /**\n   * The session version.\n   * Composed by the schedule, session and participants versions.\n   * @readonly\n   */\n  version?: SessionVersion;\n}\n\nexport interface CalendarDateTime {\n  /**\n   * UTC date-time in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. If a time zone offset is specified, the time is converted to UTC. For example, if you specify  `new Date('2021-01-06T16:00:00.000-07:00')`, the stored value will be `\"2021-01-06T23:00:00.000Z\"`.\n   * Required if `localDateTime` is not specified.\n   * If `localDateTime` is specified, `timestamp` is calculated as `localDateTime`, using the business's time zone.\n   */\n  timestamp?: Date | null;\n  /** An object containing the local date and time for the business's time zone. */\n  localDateTime?: LocalDateTime;\n  /**\n   * The time zone. Optional. Derived from the schedule's time zone.\n   * In case this field is associated with recurring session, this field is empty.\n   * @readonly\n   */\n  timeZone?: string | null;\n}\n\nexport interface LocalDateTime {\n  /** Year. 4-digit format. */\n  year?: number | null;\n  /**\n   * Month number, from 1-12.\n   * @min 1\n   * @max 12\n   */\n  monthOfYear?: number | null;\n  /** Day of the month, from 1-31. */\n  dayOfMonth?: number | null;\n  /**\n   * Hour of the day in 24-hour format, from 0-23.\n   * @max 23\n   */\n  hourOfDay?: number | null;\n  /**\n   * Minute, from 0-59.\n   * @max 59\n   */\n  minutesOfHour?: number | null;\n}\n\nexport interface ExternalCalendarInfo {\n  /** The external calendar type (e.g. Google Calendar, iCal, etc). */\n  calendarType?: CalendarTypeWithLiterals;\n}\n\nexport enum SessionStatus {\n  /** Undefined status. */\n  UNDEFINED = 'UNDEFINED',\n  /** Session is confirmed. Default status. */\n  CONFIRMED = 'CONFIRMED',\n  /**\n   * Session is cancelled.\n   * A cancelled session can be the cancellation of a recurring session that should no longer be displayed or a deleted single session.\n   * The ListSessions returns cancelled sessions only if 'includeDelete' flag is set to true.\n   */\n  CANCELLED = 'CANCELLED',\n}\n\n/** @enumType */\nexport type SessionStatusWithLiterals =\n  | SessionStatus\n  | 'UNDEFINED'\n  | 'CONFIRMED'\n  | 'CANCELLED';\n\nexport enum SessionType {\n  UNDEFINED = 'UNDEFINED',\n  /**\n   * Creates an event on the calendar for the owner of the schedule that the session belongs to.\n   * Default type.\n   */\n  EVENT = 'EVENT',\n  /** Represents a resource's available working hours. */\n  WORKING_HOURS = 'WORKING_HOURS',\n  /** Deprecated. Please use WORKING_HOURS. */\n  TIME_AVAILABILITY = 'TIME_AVAILABILITY',\n  /** Deprecated. Represents a resource's available hours. Please use WORKING_HOURS. */\n  AVAILABILITY = 'AVAILABILITY',\n}\n\n/** @enumType */\nexport type SessionTypeWithLiterals =\n  | SessionType\n  | 'UNDEFINED'\n  | 'EVENT'\n  | 'WORKING_HOURS'\n  | 'TIME_AVAILABILITY'\n  | 'AVAILABILITY';\n\nexport interface SessionVersion {\n  /** Incremental version number, which is updated on each change to the session or on changes affecting the session. */\n  number?: string | null;\n}\n\nexport interface ParticipantNotification {\n  /**\n   * Whether to send a message about the changes to the customer.\n   *\n   * Default: `false`\n   */\n  notifyParticipants?: boolean;\n  /** Custom message to send to the participants about the changes to the booking. */\n  message?: string | null;\n}\n\nexport interface ScheduleCancelled {\n  schedule?: Schedule;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n  oldSchedule?: Schedule;\n}\n\nexport interface SessionCreated {\n  session?: Session;\n}\n\nexport interface SessionUpdated {\n  oldSession?: Session;\n  newSession?: Session;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether this notification was created as a result of an anonymization request, such as GDPR.\n   * An anonymized participant will have the following details:\n   * name = \"deleted\"\n   * phone = \"deleted\"\n   * email = \"<EMAIL>\"\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n}\n\nexport interface SessionCancelled {\n  session?: Session;\n  /** Whether to notify participants about the change and an optional custom message */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface AvailabilityPolicyUpdated {\n  availabilityPolicy?: AvailabilityPolicy;\n}\n\n/** Availability policy applied to all site schedules. */\nexport interface AvailabilityPolicy {\n  /** Specify how to split the schedule slots in intervals of minutes. */\n  splitInterval?: SplitInterval;\n}\n\nexport interface IntervalSplit {\n  scheduleId?: string;\n  intervals?: RecurringInterval[];\n  newScheduleVersion?: number | null;\n  oldScheduleVersion?: number | null;\n}\n\nexport interface RecurringSessionSplit {\n  scheduleId?: string;\n  recurringSessions?: Session[];\n  newScheduleVersion?: number | null;\n  oldScheduleVersion?: number | null;\n}\n\n/** Schedule unassigned from user. */\nexport interface ScheduleUnassignedFromUser {\n  /**\n   * The Wix user id.\n   * @format GUID\n   */\n  userId?: string | null;\n  /** The schedule that was unassigned from the user. */\n  schedule?: Schedule;\n}\n\nexport interface MultipleSessionsCreated {\n  schedulesWithSessions?: ScheduleWithSessions[];\n}\n\nexport interface ScheduleWithSessions {\n  schedule?: Schedule;\n  siteProperties?: SitePropertiesOnScheduleCreation;\n  sessions?: Session[];\n}\n\nexport interface SitePropertiesOnScheduleCreation {\n  /** The global time zone value. */\n  timeZone?: string | null;\n}\n\nexport interface MigrationEvent {\n  migrationData?: MigrationData;\n}\n\nexport interface MigrationData {\n  businessId?: string | null;\n  staffs?: StaffData[];\n}\n\nexport interface StaffData {\n  resourceId?: string;\n  syncRequestEmail?: string;\n  refreshToken?: string;\n}\n\nexport interface Empty {}\n\n/** @docsIgnore */\nexport type ConnectByOAuthApplicationErrors = {\n  code?: 'NOT_SUPPORTED_BY_PROVIDER';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type ConnectByCredentialsApplicationErrors =\n  | {\n      code?: 'BAD_CREDENTIALS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_SUPPORTED_BY_PROVIDER';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'PREMIUM_FEATURE_NOT_ENABLED';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type ListCalendarsApplicationErrors =\n  | {\n      code?: 'CONNECTION_NOT_CONNECTED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_SUPPORTED_BY_PROVIDER';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'PREMIUM_FEATURE_NOT_ENABLED';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type UpdateSyncConfigApplicationErrors = {\n  code?: 'NOT_SUPPORTED_BY_PROVIDER';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type DisconnectApplicationErrors = {\n  code?: 'CONNECTION_ALREADY_DISCONNECTED';\n  description?: string;\n  data?: Record<string, any>;\n};\n\n/**\n * Retrieves a list of external calendar providers supported on the Wix site.\n *\n *\n * The list of external calendar providers includes:\n *\n * + External calendar providers that are supported by default, such as Google, Apple, and Microsoft.\n * + External calenders for which the site owner has enabled integration by installing an app.\n *\n * For each provider, check `features.connectMethods` to find out whether to use\n * _Connect By Credentials_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n * or _Connect By OAuth_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n * to establish a connection.\n * @public\n * @permissionId CALENDAR.LIST_EXTERNAL_CALENDAR_PROVIDERS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListProviders\n */\nexport async function listProviders(): Promise<\n  NonNullablePaths<\n    ListProvidersResponse,\n    | `providers`\n    | `providers.${number}.calendarType`\n    | `providers.${number}.features.listEventFromCalendars`\n    | `providers.${number}.features.syncToCalendar`,\n    5\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({});\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listProviders(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {},\n        singleArgumentUnchanged: false,\n      },\n      []\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves an external calendar connection by ID.\n *\n *\n * The `syncConfig` property contains configuration details about event import\n * from and event export to the external calendar.\n * @param connectionId - ID of the connection to retrieve.\n * @public\n * @requiredField connectionId\n * @permissionId CALENDAR.READ_OWN_EXTERNAL_CALENDAR_CONNECTIONS\n * @permissionId CALENDAR.READ_ALL_EXTERNAL_CALENDAR_CONNECTIONS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.GetConnection\n */\nexport async function getConnection(\n  connectionId: string\n): Promise<\n  NonNullablePaths<\n    GetConnectionResponse,\n    | `connection.calendarType`\n    | `connection.status`\n    | `connection.errorReason`\n    | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    connectionId: connectionId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.getConnection(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { connectionId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['connectionId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a list of external calendar connections.\n *\n *\n * ## Filter\n *\n * You can filter by *schedule ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n *\n * ## Partial success\n *\n * By default, the call fails if details for at least 1 connection can't be\n * retrieved from the external provider. However, you can specify\n * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n * least 1 connection can be retrieved.\n * @public\n * @param options - Options to use when listing connections.\n * @permissionId CALENDAR.READ_OWN_EXTERNAL_CALENDAR_CONNECTIONS\n * @permissionId CALENDAR.READ_ALL_EXTERNAL_CALENDAR_CONNECTIONS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListConnections\n */\nexport async function listConnections(\n  options?: ListConnectionsOptions\n): Promise<\n  NonNullablePaths<\n    ListConnectionsResponse,\n    | `connections`\n    | `connections.${number}.calendarType`\n    | `connections.${number}.status`\n    | `connections.${number}.errorReason`\n    | `failedProviderIds`,\n    4\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    scheduleIds: options?.scheduleIds,\n    partialFailure: options?.partialFailure,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listConnections(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          scheduleIds: '$[0].scheduleIds',\n          partialFailure: '$[0].partialFailure',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListConnectionsOptions {\n  /**\n   * _Schedule IDs_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to filter by.\n   *\n   * Default: Returns all connections.\n   * @format GUID\n   */\n  scheduleIds?: string[] | null;\n  /**\n   * Whether to return a partial list of connections if details can't be\n   * retrieved for all connections.\n   *\n   * Default: `false`\n   */\n  partialFailure?: boolean | null;\n}\n\n/**\n * Connects a *Wix schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to an external calendar account following [OAuth authorization protocol](https://oauth.net/2/).\n *\n *\n * ## Authorization flow\n *\n * The full authorization flow looks like this:\n *\n * 1. Call *Connect By OAuth* with the appropriate parameters.\n * 2. Redirect the owner of the external calendar account to the returned\n * `oAUthUrl`.\n * 3. The account owner authorizes access.\n * 4. The account owner is automatically redirected to the URL you've provided\n * in `redirectUrl` of the *Connect By OAuth* call.\n * 5. Save the new `connectionId`. You find it as a query parameter in the URL\n * to which the account owner is redirected.\n *\n * See *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/sample-flows#connect-an-external-calendar-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/sample-flows#connect-an-external-calendar-by-oauth))\n * for more details.\n *\n * ## Failure consequences\n *\n * If the attempt to connect fails, the account owner is still redirected to\n * the URL you specify in `redirectUrl`, but it incudes an `error` query\n * parameter set to 1 of the following values:\n *\n * - `reject`: The external calendar owner has rejected the authorization request.\n * - `unsupported`: Connecting to the user's external account type is not supported by the provider.\n * - `internal`: An error unrelated to the client or the request that prevents the server from fulfilling the request.\n *\n * ## Next steps\n *\n * After connecting an external calendar account to a Wix schedule, you can do\n * the following.\n *\n * ### Check the 2-way-sync settings\n *\n * Call *List Connections*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n * and filter by the relevant schedule ID.\n *\n * If needed, call *Update Sync Config*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n * to adjust the syncing configuration.\n *\n * ### Retrieve external calendar events\n *\n * Once a connection is successfully created, you can call *List Events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to obtain an up-to-date list of events in the connected external calendars.\n *\n * ## When to call Connect By Credentials instead\n *\n * You can call *Connect By Credentials*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n * if:\n *\n * + The external calendar provider doesn't support OAuth.\n * + You don't want to redirect the account owner.\n *\n * Call *List Providers*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n * for a complete list of external calendar providers supported on the Wix site,\n * including which authorization methods they support.\n * @param scheduleId - ID of the schedule to connect with the external calendar account.\n * @param redirectUrl - URL to redirect the user to after they authorize access to the external calendar account.\n *\n * If the connection is successfully established, the user is redirected to this URL with a query parameter `connectionId` containing the new connection ID.\n * If the attempt to connect fails, the user is redirected to this URL with a query parameter `error` containing the error type.\n * @public\n * @requiredField providerId\n * @requiredField redirectUrl\n * @requiredField scheduleId\n * @param providerId - ID of the external calendar provider. Find this with the `listProviders()` function.\n * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS\n * @permissionId CALENDAR.MANAGE_ALL_EXTERNAL_CALENDAR_CONNECTIONS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByOAuth\n */\nexport async function connectByOAuth(\n  providerId: string,\n  scheduleId: string,\n  redirectUrl: string\n): Promise<\n  ConnectByOAuthResponse & {\n    __applicationErrorsType?: ConnectByOAuthApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    providerId: providerId,\n    scheduleId: scheduleId,\n    redirectUrl: redirectUrl,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.connectByOAuth(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          providerId: '$[0]',\n          scheduleId: '$[1]',\n          redirectUrl: '$[2]',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['providerId', 'scheduleId', 'redirectUrl']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Connects a *Wix schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to an external calendar account using credentials.\n *\n *\n * ## Next steps\n *\n * After connecting an external calendar account to a Wix schedule, you can do\n * the following.\n *\n * ### Check the 2-way-sync settings\n *\n * Call *List Connections*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n * and filter by the relevant schedule ID.\n *\n * If needed, call *Update Sync Config*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n * to adjust the syncing configuration.\n *\n * ### Retrieve external calendar events\n *\n * Once a connection is successfully created, you can call *List Events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to obtain an up-to-date list of events in the connected external calendars.\n *\n * ## When to call Connect By OAuth instead\n *\n * You could call *Connect By OAuth*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n * if:\n *\n * + The external calendar provider doesn't support authorization by credentials.\n * + You don't have access to the external calendar account credentials.\n * + You want the account owner to approve the connection.\n *\n * Call *List Providers*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n * for a complete list of external calendar providers supported on the Wix site,\n * including which authorization methods they support.\n * @param scheduleId - ID of the schedule to connect with the external calendar account.\n * @param email - Email address for the external calendar account.\n * @param password - Password for the external calendar account.\n * @public\n * @requiredField email\n * @requiredField password\n * @requiredField providerId\n * @requiredField scheduleId\n * @param providerId - ID of the external calendar provider. Find this with the `listProviders()` function.\n * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS\n * @permissionId CALENDAR.MANAGE_ALL_EXTERNAL_CALENDAR_CONNECTIONS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByCredentials\n */\nexport async function connectByCredentials(\n  providerId: string,\n  scheduleId: string,\n  email: string,\n  password: string\n): Promise<\n  NonNullablePaths<\n    ConnectByCredentialsResponse,\n    | `connection.calendarType`\n    | `connection.status`\n    | `connection.errorReason`\n    | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n    6\n  > & {\n    __applicationErrorsType?: ConnectByCredentialsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[4] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    providerId: providerId,\n    scheduleId: scheduleId,\n    email: email,\n    password: password,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.connectByCredentials(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          providerId: '$[0]',\n          scheduleId: '$[1]',\n          email: '$[2]',\n          password: '$[3]',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['providerId', 'scheduleId', 'email', 'password']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves details about the external calendar accounts associated with the specified connection.\n * @param connectionId - ID of the external calendar connection to list calendars for.\n * @public\n * @requiredField connectionId\n * @permissionId CALENDAR.READ_OWN_EXTERNAL_CALENDAR_CONNECTIONS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListCalendars\n */\nexport async function listCalendars(connectionId: string): Promise<\n  NonNullablePaths<ListCalendarsResponse, `calendars`, 2> & {\n    __applicationErrorsType?: ListCalendarsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    connectionId: connectionId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listCalendars(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { connectionId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['connectionId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates the import and export settings for an external calendar connection's\n * events.\n *\n *\n * A connection's `syncConfig` object determines:\n *\n * + Whether events from 1 or more accounts belonging to the external\n * calendar are imported to the connected *Wix schedule*.\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n * If importing events is enabled, you can call _List Events_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to retrieve external calendar events.\n * + Whether events from the Wix schedule are exported to 1 or more accounts\n * belonging to the external calendar.\n *\n * To check the current import and export settings, you can call\n * _Get Connection_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/get-connection) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/get-connection))\n * or _List Connections_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections)).\n * @param connectionId - ID of the external calendar connection to update.\n * @param syncConfig - Updated sync configuration details.\n * @public\n * @requiredField connectionId\n * @requiredField syncConfig\n * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.UpdateSyncConfig\n */\nexport async function updateSyncConfig(\n  connectionId: string,\n  syncConfig: ConnectionSyncConfig\n): Promise<\n  NonNullablePaths<\n    UpdateSyncConfigResponse,\n    | `connection.calendarType`\n    | `connection.status`\n    | `connection.errorReason`\n    | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n    6\n  > & {\n    __applicationErrorsType?: UpdateSyncConfigApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    connectionId: connectionId,\n    syncConfig: syncConfig,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.updateSyncConfig(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { connectionId: '$[0]', syncConfig: '$[1]' },\n        singleArgumentUnchanged: false,\n      },\n      ['connectionId', 'syncConfig']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Disconnects a Wix schedule from an external calendar and deletes all Wix\n * calendar *events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * from the external calendar.\n *\n *\n * When an external calendar is disconnected, the connection's `status`\n * changes to `DISCONNECTED`.\n *\n * After disconnecting, _List Events_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * no longer returns events from the disconnected external calendar.\n * @param connectionId - ID of the external calendar connection to disconnect.\n * @public\n * @requiredField connectionId\n * @permissionId CALENDAR.MANAGE_OWN_EXTERNAL_CALENDAR_CONNECTIONS\n * @permissionId CALENDAR.MANAGE_ALL_EXTERNAL_CALENDAR_CONNECTIONS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.Disconnect\n */\nexport async function disconnect(connectionId: string): Promise<\n  NonNullablePaths<\n    DisconnectResponse,\n    | `connection.calendarType`\n    | `connection.status`\n    | `connection.errorReason`\n    | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n    6\n  > & {\n    __applicationErrorsType?: DisconnectApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    connectionId: connectionId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.disconnect(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { connectionId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['connectionId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a list of events from all external calendar accounts, based on\n * the provided filtering and paging.\n *\n *\n * ## Filters\n *\n * You must filter by specifying both `from` and `to` dates, unless you specify\n * `cursorPaging.cursor`.\n *\n * Additionally, you can specify `scheduleIds` or `userIds` to further limit\n * which events are returned. By default, events related to all schedules and\n * Wix users are returned.\n *\n * ## Sorting\n *\n * Returned events are sorted by start date in ascending order. You can't\n * adjust the sorting.\n *\n * ## Personal data\n *\n * By default, the following `event` fields aren't returned:\n *\n * + `calendarName`.\n * + `title`.\n * + `scheduleOwnerName`.\n *\n * You can retrieve these fields, by specifying `{\"fieldsets\": \"OWN_PI\"}`.\n *\n * ## Partial success\n *\n * By default, the call fails if events for at least 1 connection can't be\n * retrieved from an external provider. However, you can specify\n * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n * least 1 connection can be retrieved.\n * @public\n * @param options - Options to use when listing events.\n * @permissionId CALENDAR.READ_EXTERNAL_CALENDAR_EVENTS\n * @applicableIdentity APP\n * @fqn com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListEvents\n */\nexport async function listEvents(\n  options?: ListEventsOptions\n): Promise<\n  NonNullablePaths<\n    ListEventsResponse,\n    `events` | `events.${number}.calendarType` | `failedProviderIds`,\n    4\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    from: options?.from,\n    to: options?.to,\n    scheduleIds: options?.scheduleIds,\n    userIds: options?.userIds,\n    allDay: options?.allDay,\n    fieldsets: options?.fieldsets,\n    cursorPaging: options?.cursorPaging,\n    partialFailure: options?.partialFailure,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCalendarV2ExternalCalendar.listEvents(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          from: '$[0].from',\n          to: '$[0].to',\n          scheduleIds: '$[0].scheduleIds',\n          userIds: '$[0].userIds',\n          allDay: '$[0].allDay',\n          fieldsets: '$[0].fieldsets',\n          cursorPaging: '$[0].cursorPaging',\n          partialFailure: '$[0].partialFailure',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListEventsOptions {\n  /**\n   * Date and time from which to retrieve events,\n   * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).\n   * Required, unless `cursorPaging.cursor` is provided.\n   *\n   * Events which start before the `from` time and end after it are included in the returned list.\n   */\n  from?: string | null;\n  /**\n   * Date and time until which to retrieve events,\n   * formatted according to [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt).\n   * Required, unless `cursorPaging.cursor` is provided.\n   *\n   * Events which start before the `to` time and end after it are included in the returned list.\n   */\n  to?: string | null;\n  /**\n   * Schedule IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to the specified schedules.\n   * Maximum of 100 schedule IDs per request.\n   * @format GUID\n   * @maxSize 100\n   */\n  scheduleIds?: string[] | null;\n  /**\n   * Wix user IDs to filter by. If provided, the returned list includes only events belonging to external calendars connected to schedules belonging to the specified Wix users.\n   * Maximum of 100 Wix user IDs per request.\n   * @format GUID\n   * @maxSize 100\n   */\n  userIds?: string[] | null;\n  /**\n   * Whether to include only all-day events in the returned list.\n   * If `true`, only all-day events are returned.\n   * If `false`, only events with a specified time are returned.\n   *\n   * Default: All events are returned.\n   */\n  allDay?: boolean | null;\n  /**\n   * Predefined sets of fields to return.\n   * - `NO_PI`: Returns event objects without personal information.\n   * - `OWN_PI`: Returns complete event objects, including personal information.\n   *\n   * Default: `NO_PI`\n   */\n  fieldsets?: string[];\n  /** Pagination options. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Whether to return a partial list of events if details can't be retrieved for some connections.\n   *\n   * Default: `false`\n   */\n  partialFailure?: boolean | null;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'api._api_base_domain_': [\n      {\n        srcPath: '/external-calendar-2',\n        destPath: '',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/external-calendar',\n        destPath: '/v2/external-calendar',\n      },\n      {\n        srcPath: '/_api/bookings/v2/external-calendars',\n        destPath: '/v2/external-calendars',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/external-calendars',\n        destPath: '/v2/external-calendars',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_external-calendars';\n\n/**\n * Retrieves a list of external calendar providers supported on the Wix site.\n *\n *\n * The list of external calendar providers includes:\n *\n * + External calendar providers that are supported by default, such as Google, Apple, and Microsoft.\n * + External calenders for which the site owner has enabled integration by installing an app.\n *\n * For each provider, check `features.connectMethods` to find out whether to use\n * _Connect By Credentials_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n * or _Connect By OAuth_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n * to establish a connection.\n */\nexport function listProviders(payload: object): RequestOptionsFactory<any> {\n  function __listProviders({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListProviders',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        { protoPath: '/v2/external-calendars/providers', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n            {\n              protoPath: '/v2/external-calendars/providers',\n              data: payload,\n              host,\n            }\n          ),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __listProviders;\n}\n\n/**\n * Retrieves an external calendar connection by ID.\n *\n *\n * The `syncConfig` property contains configuration details about event import\n * from and event export to the external calendar.\n */\nexport function getConnection(payload: object): RequestOptionsFactory<any> {\n  function __getConnection({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.GetConnection',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath: '/v2/external-calendars/connections/{connectionId}',\n          data: payload,\n          host,\n        }\n      ),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n            {\n              protoPath: '/v2/external-calendars/connections/{connectionId}',\n              data: payload,\n              host,\n            }\n          ),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __getConnection;\n}\n\n/**\n * Retrieves a list of external calendar connections.\n *\n *\n * ## Filter\n *\n * You can filter by *schedule ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n *\n * ## Partial success\n *\n * By default, the call fails if details for at least 1 connection can't be\n * retrieved from the external provider. However, you can specify\n * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n * least 1 connection can be retrieved.\n */\nexport function listConnections(payload: object): RequestOptionsFactory<any> {\n  function __listConnections({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListConnections',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        { protoPath: '/v2/external-calendars/connections', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __listConnections;\n}\n\n/**\n * Connects a *Wix schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to an external calendar account following [OAuth authorization protocol](https://oauth.net/2/).\n *\n *\n * ## Authorization flow\n *\n * The full authorization flow looks like this:\n *\n * 1. Call *Connect By OAuth* with the appropriate parameters.\n * 2. Redirect the owner of the external calendar account to the returned\n * `oAUthUrl`.\n * 3. The account owner authorizes access.\n * 4. The account owner is automatically redirected to the URL you've provided\n * in `redirectUrl` of the *Connect By OAuth* call.\n * 5. Save the new `connectionId`. You find it as a query parameter in the URL\n * to which the account owner is redirected.\n *\n * See *this sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/sample-flows#connect-an-external-calendar-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/sample-flows#connect-an-external-calendar-by-oauth))\n * for more details.\n *\n * ## Failure consequences\n *\n * If the attempt to connect fails, the account owner is still redirected to\n * the URL you specify in `redirectUrl`, but it incudes an `error` query\n * parameter set to 1 of the following values:\n *\n * - `reject`: The external calendar owner has rejected the authorization request.\n * - `unsupported`: Connecting to the user's external account type is not supported by the provider.\n * - `internal`: An error unrelated to the client or the request that prevents the server from fulfilling the request.\n *\n * ## Next steps\n *\n * After connecting an external calendar account to a Wix schedule, you can do\n * the following.\n *\n * ### Check the 2-way-sync settings\n *\n * Call *List Connections*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n * and filter by the relevant schedule ID.\n *\n * If needed, call *Update Sync Config*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n * to adjust the syncing configuration.\n *\n * ### Retrieve external calendar events\n *\n * Once a connection is successfully created, you can call *List Events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to obtain an up-to-date list of events in the connected external calendars.\n *\n * ## When to call Connect By Credentials instead\n *\n * You can call *Connect By Credentials*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n * if:\n *\n * + The external calendar provider doesn't support OAuth.\n * + You don't want to redirect the account owner.\n *\n * Call *List Providers*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n * for a complete list of external calendar providers supported on the Wix site,\n * including which authorization methods they support.\n */\nexport function connectByOAuth(payload: object): RequestOptionsFactory<any> {\n  function __connectByOAuth({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByOAuth',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath: '/v2/external-calendars/connections:connectByOAuth',\n          data: payload,\n          host,\n        }\n      ),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __connectByOAuth;\n}\n\n/**\n * Connects a *Wix schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * to an external calendar account using credentials.\n *\n *\n * ## Next steps\n *\n * After connecting an external calendar account to a Wix schedule, you can do\n * the following.\n *\n * ### Check the 2-way-sync settings\n *\n * Call *List Connections*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n * and filter by the relevant schedule ID.\n *\n * If needed, call *Update Sync Config*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n * to adjust the syncing configuration.\n *\n * ### Retrieve external calendar events\n *\n * Once a connection is successfully created, you can call *List Events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to obtain an up-to-date list of events in the connected external calendars.\n *\n * ## When to call Connect By OAuth instead\n *\n * You could call *Connect By OAuth*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n * if:\n *\n * + The external calendar provider doesn't support authorization by credentials.\n * + You don't have access to the external calendar account credentials.\n * + You want the account owner to approve the connection.\n *\n * Call *List Providers*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n * for a complete list of external calendar providers supported on the Wix site,\n * including which authorization methods they support.\n */\nexport function connectByCredentials(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __connectByCredentials({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByCredentials',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath: '/v2/external-calendars/connections:connectByCredentials',\n          data: payload,\n          host,\n        }\n      ),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __connectByCredentials;\n}\n\n/** Retrieves details about the external calendar accounts associated with the specified connection. */\nexport function listCalendars(payload: object): RequestOptionsFactory<any> {\n  function __listCalendars({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListCalendars',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath:\n            '/v2/external-calendars/connections/{connectionId}/calendars',\n          data: payload,\n          host,\n        }\n      ),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n            {\n              protoPath:\n                '/v2/external-calendars/connections/{connectionId}/calendars',\n              data: payload,\n              host,\n            }\n          ),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __listCalendars;\n}\n\n/**\n * Updates the import and export settings for an external calendar connection's\n * events.\n *\n *\n * A connection's `syncConfig` object determines:\n *\n * + Whether events from 1 or more accounts belonging to the external\n * calendar are imported to the connected *Wix schedule*.\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n * If importing events is enabled, you can call _List Events_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * to retrieve external calendar events.\n * + Whether events from the Wix schedule are exported to 1 or more accounts\n * belonging to the external calendar.\n *\n * To check the current import and export settings, you can call\n * _Get Connection_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/get-connection) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/get-connection))\n * or _List Connections_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections)).\n */\nexport function updateSyncConfig(payload: object): RequestOptionsFactory<any> {\n  function __updateSyncConfig({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'fieldMask' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'PATCH' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.UpdateSyncConfig',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath:\n            '/v2/external-calendars/connections/{connectionId}/sync-config',\n          data: serializedData,\n          host,\n        }\n      ),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __updateSyncConfig;\n}\n\n/**\n * Disconnects a Wix schedule from an external calendar and deletes all Wix\n * calendar *events*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * from the external calendar.\n *\n *\n * When an external calendar is disconnected, the connection's `status`\n * changes to `DISCONNECTED`.\n *\n * After disconnecting, _List Events_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n * no longer returns events from the disconnected external calendar.\n */\nexport function disconnect(payload: object): RequestOptionsFactory<any> {\n  function __disconnect({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.Disconnect',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        {\n          protoPath:\n            '/v2/external-calendars/connections/{connectionId}/disconnect',\n          data: payload,\n          host,\n        }\n      ),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __disconnect;\n}\n\n/**\n * Retrieves a list of events from all external calendar accounts, based on\n * the provided filtering and paging.\n *\n *\n * ## Filters\n *\n * You must filter by specifying both `from` and `to` dates, unless you specify\n * `cursorPaging.cursor`.\n *\n * Additionally, you can specify `scheduleIds` or `userIds` to further limit\n * which events are returned. By default, events related to all schedules and\n * Wix users are returned.\n *\n * ## Sorting\n *\n * Returned events are sorted by start date in ascending order. You can't\n * adjust the sorting.\n *\n * ## Personal data\n *\n * By default, the following `event` fields aren't returned:\n *\n * + `calendarName`.\n * + `title`.\n * + `scheduleOwnerName`.\n *\n * You can retrieve these fields, by specifying `{\"fieldsets\": \"OWN_PI\"}`.\n *\n * ## Partial success\n *\n * By default, the call fails if events for at least 1 connection can't be\n * retrieved from an external provider. However, you can specify\n * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n * least 1 connection can be retrieved.\n */\nexport function listEvents(payload: object): RequestOptionsFactory<any> {\n  function __listEvents({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.calendar.v2.external_calendar',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListEvents',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(\n        { protoPath: '/v2/external-calendars/events', data: payload, host }\n      ),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __listEvents;\n}\n", "import { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  ConnectByCredentialsApplicationErrors,\n  ConnectByCredentialsResponse,\n  ConnectByOAuthApplicationErrors,\n  ConnectByOAuthResponse,\n  ConnectionSyncConfig,\n  DisconnectApplicationErrors,\n  DisconnectResponse,\n  GetConnectionResponse,\n  ListCalendarsApplicationErrors,\n  ListCalendarsResponse,\n  ListConnectionsOptions,\n  ListConnectionsResponse,\n  ListEventsOptions,\n  ListEventsResponse,\n  ListProvidersResponse,\n  UpdateSyncConfigApplicationErrors,\n  UpdateSyncConfigResponse,\n  connectByCredentials as universalConnectByCredentials,\n  connectByOAuth as universalConnectByOAuth,\n  disconnect as universalDisconnect,\n  getConnection as universalGetConnection,\n  listCalendars as universalListCalendars,\n  listConnections as universalListConnections,\n  listEvents as universalListEvents,\n  listProviders as universalListProviders,\n  updateSyncConfig as universalUpdateSyncConfig,\n} from './bookings-calendar-v2-external-calendar-external-calendars.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function listProviders(httpClient: HttpClient): ListProvidersSignature {\n  return () =>\n    universalListProviders(\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ListProvidersSignature {\n  /**\n   * Retrieves a list of external calendar providers supported on the Wix site.\n   *\n   *\n   * The list of external calendar providers includes:\n   *\n   * + External calendar providers that are supported by default, such as Google, Apple, and Microsoft.\n   * + External calenders for which the site owner has enabled integration by installing an app.\n   *\n   * For each provider, check `features.connectMethods` to find out whether to use\n   * _Connect By Credentials_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n   * or _Connect By OAuth_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n   * to establish a connection.\n   */\n  (): Promise<\n    NonNullablePaths<\n      ListProvidersResponse,\n      | `providers`\n      | `providers.${number}.calendarType`\n      | `providers.${number}.features.listEventFromCalendars`\n      | `providers.${number}.features.syncToCalendar`,\n      5\n    >\n  >;\n}\n\nexport function getConnection(httpClient: HttpClient): GetConnectionSignature {\n  return (connectionId: string) =>\n    universalGetConnection(\n      connectionId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetConnectionSignature {\n  /**\n   * Retrieves an external calendar connection by ID.\n   *\n   *\n   * The `syncConfig` property contains configuration details about event import\n   * from and event export to the external calendar.\n   * @param - ID of the connection to retrieve.\n   */\n  (connectionId: string): Promise<\n    NonNullablePaths<\n      GetConnectionResponse,\n      | `connection.calendarType`\n      | `connection.status`\n      | `connection.errorReason`\n      | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n      6\n    >\n  >;\n}\n\nexport function listConnections(\n  httpClient: HttpClient\n): ListConnectionsSignature {\n  return (options?: ListConnectionsOptions) =>\n    universalListConnections(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ListConnectionsSignature {\n  /**\n   * Retrieves a list of external calendar connections.\n   *\n   *\n   * ## Filter\n   *\n   * You can filter by *schedule ID*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   *\n   * ## Partial success\n   *\n   * By default, the call fails if details for at least 1 connection can't be\n   * retrieved from the external provider. However, you can specify\n   * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n   * least 1 connection can be retrieved.\n   * @param - Options to use when listing connections.\n   */\n  (options?: ListConnectionsOptions): Promise<\n    NonNullablePaths<\n      ListConnectionsResponse,\n      | `connections`\n      | `connections.${number}.calendarType`\n      | `connections.${number}.status`\n      | `connections.${number}.errorReason`\n      | `failedProviderIds`,\n      4\n    >\n  >;\n}\n\nexport function connectByOAuth(\n  httpClient: HttpClient\n): ConnectByOAuthSignature {\n  return (providerId: string, scheduleId: string, redirectUrl: string) =>\n    universalConnectByOAuth(\n      providerId,\n      scheduleId,\n      redirectUrl,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ConnectByOAuthSignature {\n  /**\n   * Connects a *Wix schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to an external calendar account following [OAuth authorization protocol](https://oauth.net/2/).\n   *\n   *\n   * ## Authorization flow\n   *\n   * The full authorization flow looks like this:\n   *\n   * 1. Call *Connect By OAuth* with the appropriate parameters.\n   * 2. Redirect the owner of the external calendar account to the returned\n   * `oAUthUrl`.\n   * 3. The account owner authorizes access.\n   * 4. The account owner is automatically redirected to the URL you've provided\n   * in `redirectUrl` of the *Connect By OAuth* call.\n   * 5. Save the new `connectionId`. You find it as a query parameter in the URL\n   * to which the account owner is redirected.\n   *\n   * See *this sample flow*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/sample-flows#connect-an-external-calendar-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/sample-flows#connect-an-external-calendar-by-oauth))\n   * for more details.\n   *\n   * ## Failure consequences\n   *\n   * If the attempt to connect fails, the account owner is still redirected to\n   * the URL you specify in `redirectUrl`, but it incudes an `error` query\n   * parameter set to 1 of the following values:\n   *\n   * - `reject`: The external calendar owner has rejected the authorization request.\n   * - `unsupported`: Connecting to the user's external account type is not supported by the provider.\n   * - `internal`: An error unrelated to the client or the request that prevents the server from fulfilling the request.\n   *\n   * ## Next steps\n   *\n   * After connecting an external calendar account to a Wix schedule, you can do\n   * the following.\n   *\n   * ### Check the 2-way-sync settings\n   *\n   * Call *List Connections*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n   * and filter by the relevant schedule ID.\n   *\n   * If needed, call *Update Sync Config*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n   * to adjust the syncing configuration.\n   *\n   * ### Retrieve external calendar events\n   *\n   * Once a connection is successfully created, you can call *List Events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n   * to obtain an up-to-date list of events in the connected external calendars.\n   *\n   * ## When to call Connect By Credentials instead\n   *\n   * You can call *Connect By Credentials*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))\n   * if:\n   *\n   * + The external calendar provider doesn't support OAuth.\n   * + You don't want to redirect the account owner.\n   *\n   * Call *List Providers*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n   * for a complete list of external calendar providers supported on the Wix site,\n   * including which authorization methods they support.\n   * @param - ID of the schedule to connect with the external calendar account.\n   * @param - URL to redirect the user to after they authorize access to the external calendar account.\n   *\n   * If the connection is successfully established, the user is redirected to this URL with a query parameter `connectionId` containing the new connection ID.\n   * If the attempt to connect fails, the user is redirected to this URL with a query parameter `error` containing the error type.\n   * @param - ID of the external calendar provider. Find this with the `listProviders()` function.\n   */\n  (providerId: string, scheduleId: string, redirectUrl: string): Promise<\n    ConnectByOAuthResponse & {\n      __applicationErrorsType?: ConnectByOAuthApplicationErrors;\n    }\n  >;\n}\n\nexport function connectByCredentials(\n  httpClient: HttpClient\n): ConnectByCredentialsSignature {\n  return (\n    providerId: string,\n    scheduleId: string,\n    email: string,\n    password: string\n  ) =>\n    universalConnectByCredentials(\n      providerId,\n      scheduleId,\n      email,\n      password,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ConnectByCredentialsSignature {\n  /**\n   * Connects a *Wix schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to an external calendar account using credentials.\n   *\n   *\n   * ## Next steps\n   *\n   * After connecting an external calendar account to a Wix schedule, you can do\n   * the following.\n   *\n   * ### Check the 2-way-sync settings\n   *\n   * Call *List Connections*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))\n   * and filter by the relevant schedule ID.\n   *\n   * If needed, call *Update Sync Config*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))\n   * to adjust the syncing configuration.\n   *\n   * ### Retrieve external calendar events\n   *\n   * Once a connection is successfully created, you can call *List Events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n   * to obtain an up-to-date list of events in the connected external calendars.\n   *\n   * ## When to call Connect By OAuth instead\n   *\n   * You could call *Connect By OAuth*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))\n   * if:\n   *\n   * + The external calendar provider doesn't support authorization by credentials.\n   * + You don't have access to the external calendar account credentials.\n   * + You want the account owner to approve the connection.\n   *\n   * Call *List Providers*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))\n   * for a complete list of external calendar providers supported on the Wix site,\n   * including which authorization methods they support.\n   * @param - ID of the schedule to connect with the external calendar account.\n   * @param - Email address for the external calendar account.\n   * @param - Password for the external calendar account.\n   * @param - ID of the external calendar provider. Find this with the `listProviders()` function.\n   */\n  (\n    providerId: string,\n    scheduleId: string,\n    email: string,\n    password: string\n  ): Promise<\n    NonNullablePaths<\n      ConnectByCredentialsResponse,\n      | `connection.calendarType`\n      | `connection.status`\n      | `connection.errorReason`\n      | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n      6\n    > & {\n      __applicationErrorsType?: ConnectByCredentialsApplicationErrors;\n    }\n  >;\n}\n\nexport function listCalendars(httpClient: HttpClient): ListCalendarsSignature {\n  return (connectionId: string) =>\n    universalListCalendars(\n      connectionId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ListCalendarsSignature {\n  /**\n   * Retrieves details about the external calendar accounts associated with the specified connection.\n   * @param - ID of the external calendar connection to list calendars for.\n   */\n  (connectionId: string): Promise<\n    NonNullablePaths<ListCalendarsResponse, `calendars`, 2> & {\n      __applicationErrorsType?: ListCalendarsApplicationErrors;\n    }\n  >;\n}\n\nexport function updateSyncConfig(\n  httpClient: HttpClient\n): UpdateSyncConfigSignature {\n  return (connectionId: string, syncConfig: ConnectionSyncConfig) =>\n    universalUpdateSyncConfig(\n      connectionId,\n      syncConfig,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateSyncConfigSignature {\n  /**\n   * Updates the import and export settings for an external calendar connection's\n   * events.\n   *\n   *\n   * A connection's `syncConfig` object determines:\n   *\n   * + Whether events from 1 or more accounts belonging to the external\n   * calendar are imported to the connected *Wix schedule*.\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   * If importing events is enabled, you can call _List Events_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n   * to retrieve external calendar events.\n   * + Whether events from the Wix schedule are exported to 1 or more accounts\n   * belonging to the external calendar.\n   *\n   * To check the current import and export settings, you can call\n   * _Get Connection_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/get-connection) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/get-connection))\n   * or _List Connections_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections)).\n   * @param - ID of the external calendar connection to update.\n   * @param - Updated sync configuration details.\n   */\n  (connectionId: string, syncConfig: ConnectionSyncConfig): Promise<\n    NonNullablePaths<\n      UpdateSyncConfigResponse,\n      | `connection.calendarType`\n      | `connection.status`\n      | `connection.errorReason`\n      | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n      6\n    > & {\n      __applicationErrorsType?: UpdateSyncConfigApplicationErrors;\n    }\n  >;\n}\n\nexport function disconnect(httpClient: HttpClient): DisconnectSignature {\n  return (connectionId: string) =>\n    universalDisconnect(\n      connectionId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DisconnectSignature {\n  /**\n   * Disconnects a Wix schedule from an external calendar and deletes all Wix\n   * calendar *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * from the external calendar.\n   *\n   *\n   * When an external calendar is disconnected, the connection's `status`\n   * changes to `DISCONNECTED`.\n   *\n   * After disconnecting, _List Events_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))\n   * no longer returns events from the disconnected external calendar.\n   * @param - ID of the external calendar connection to disconnect.\n   */\n  (connectionId: string): Promise<\n    NonNullablePaths<\n      DisconnectResponse,\n      | `connection.calendarType`\n      | `connection.status`\n      | `connection.errorReason`\n      | `connection.syncConfig.listEventFromCalendars.calendars.calendars`,\n      6\n    > & {\n      __applicationErrorsType?: DisconnectApplicationErrors;\n    }\n  >;\n}\n\nexport function listEvents(httpClient: HttpClient): ListEventsSignature {\n  return (options?: ListEventsOptions) =>\n    universalListEvents(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ListEventsSignature {\n  /**\n   * Retrieves a list of events from all external calendar accounts, based on\n   * the provided filtering and paging.\n   *\n   *\n   * ## Filters\n   *\n   * You must filter by specifying both `from` and `to` dates, unless you specify\n   * `cursorPaging.cursor`.\n   *\n   * Additionally, you can specify `scheduleIds` or `userIds` to further limit\n   * which events are returned. By default, events related to all schedules and\n   * Wix users are returned.\n   *\n   * ## Sorting\n   *\n   * Returned events are sorted by start date in ascending order. You can't\n   * adjust the sorting.\n   *\n   * ## Personal data\n   *\n   * By default, the following `event` fields aren't returned:\n   *\n   * + `calendarName`.\n   * + `title`.\n   * + `scheduleOwnerName`.\n   *\n   * You can retrieve these fields, by specifying `{\"fieldsets\": \"OWN_PI\"}`.\n   *\n   * ## Partial success\n   *\n   * By default, the call fails if events for at least 1 connection can't be\n   * retrieved from an external provider. However, you can specify\n   * `{\"partialFailure\": true}` to allow the call to succeed, if details for at\n   * least 1 connection can be retrieved.\n   * @param - Options to use when listing events.\n   */\n  (options?: ListEventsOptions): Promise<\n    NonNullablePaths<\n      ListEventsResponse,\n      `events` | `events.${number}.calendarType` | `failedProviderIds`,\n      4\n    >\n  >;\n}\n\nexport {\n  Address,\n  AddressLocation,\n  AddressStreetOneOf,\n  ApprovalStatus,\n  Availability,\n  AvailabilityConstraints,\n  AvailabilityPolicy,\n  AvailabilityPolicyUpdated,\n  BusinessSchedule,\n  Calendar,\n  CalendarConference,\n  CalendarDateTime,\n  CalendarType,\n  Calendars,\n  ConferenceProvider,\n  ConferenceType,\n  ConnectByCredentialsRequest,\n  ConnectByCredentialsResponse,\n  ConnectByOAuthRequest,\n  ConnectByOAuthResponse,\n  ConnectMethod,\n  Connection,\n  ConnectionSyncConfig,\n  ConnectionSyncConfigListEventFromCalendars,\n  ConnectionSyncConfigListEventFromCalendarsListFromOneOf,\n  ConnectionSyncConfigSyncToCalendar,\n  ConnectionSyncConfigSyncToCalendarSyncToOneOf,\n  CursorPaging,\n  CursorPagingMetadata,\n  Cursors,\n  Day,\n  DayOfWeek,\n  DedicatedCalendar,\n  DisconnectRequest,\n  DisconnectResponse,\n  Empty,\n  ErrorReason,\n  Event,\n  ExternalCalendar,\n  ExternalCalendarInfo,\n  ExternalCalendarOverrides,\n  Frequency,\n  GetConnectionRequest,\n  GetConnectionResponse,\n  HeadersEntry,\n  Interval,\n  IntervalSplit,\n  LinkedSchedule,\n  ListCalendarsRequest,\n  ListCalendarsResponse,\n  ListConnectionsOptions,\n  ListConnectionsRequest,\n  ListConnectionsResponse,\n  ListEventFromCalendars,\n  ListEventsOptions,\n  ListEventsRequest,\n  ListEventsResponse,\n  ListProvidersRequest,\n  ListProvidersResponse,\n  LocalDateTime,\n  Location,\n  LocationStatus,\n  LocationType,\n  LocationsAddress,\n  LocationsAddressLocation,\n  LocationsLocation,\n  LocationsLocationType,\n  LocationsStreetAddress,\n  MigrationData,\n  MigrationEvent,\n  MultipleSessionsCreated,\n  Participant,\n  ParticipantNotification,\n  PathParametersEntry,\n  Price,\n  PrimaryCalendar,\n  Provider,\n  ProviderFeatures,\n  QueryParametersEntry,\n  Rate,\n  RawHttpRequest,\n  RawHttpResponse,\n  RecurringInterval,\n  RecurringIntervalType,\n  RecurringSessionSplit,\n  RecurringSessionsUpdated,\n  Schedule,\n  ScheduleCancelled,\n  ScheduleCreated,\n  ScheduleNotification,\n  ScheduleNotificationEventOneOf,\n  ScheduleStatus,\n  ScheduleUnassignedFromUser,\n  ScheduleUpdated,\n  ScheduleWithSessions,\n  Session,\n  SessionCancelled,\n  SessionCreated,\n  SessionStatus,\n  SessionType,\n  SessionUpdated,\n  SessionVersion,\n  SitePropertiesOnScheduleCreation,\n  SpecialHourPeriod,\n  SplitInterval,\n  StaffData,\n  Status,\n  StreetAddress,\n  Subdivision,\n  SyncToCalendar,\n  SyncToErrorReason,\n  TimePeriod,\n  Transparency,\n  UpdateSyncConfigRequest,\n  UpdateSyncConfigResponse,\n  Version,\n} from './bookings-calendar-v2-external-calendar-external-calendars.universal.js';\n", "import {\n  listProviders as publicListProviders,\n  getConnection as publicGetConnection,\n  listConnections as publicListConnections,\n  connectByOAuth as publicConnectByOAuth,\n  connectByCredentials as publicConnectByCredentials,\n  listCalendars as publicListCalendars,\n  updateSyncConfig as publicUpdateSyncConfig,\n  disconnect as publicDisconnect,\n  listEvents as publicListEvents,\n} from './bookings-calendar-v2-external-calendar-external-calendars.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { BuildRESTFunction, MaybeContext } from '@wix/sdk-types';\n\nexport const listProviders: MaybeContext<\n  BuildRESTFunction<typeof publicListProviders> & typeof publicListProviders\n> = /*#__PURE__*/ createRESTModule(publicListProviders);\nexport const getConnection: MaybeContext<\n  BuildRESTFunction<typeof publicGetConnection> & typeof publicGetConnection\n> = /*#__PURE__*/ createRESTModule(publicGetConnection);\nexport const listConnections: MaybeContext<\n  BuildRESTFunction<typeof publicListConnections> & typeof publicListConnections\n> = /*#__PURE__*/ createRESTModule(publicListConnections);\nexport const connectByOAuth: MaybeContext<\n  BuildRESTFunction<typeof publicConnectByOAuth> & typeof publicConnectByOAuth\n> = /*#__PURE__*/ createRESTModule(publicConnectByOAuth);\nexport const connectByCredentials: MaybeContext<\n  BuildRESTFunction<typeof publicConnectByCredentials> &\n    typeof publicConnectByCredentials\n> = /*#__PURE__*/ createRESTModule(publicConnectByCredentials);\nexport const listCalendars: MaybeContext<\n  BuildRESTFunction<typeof publicListCalendars> & typeof publicListCalendars\n> = /*#__PURE__*/ createRESTModule(publicListCalendars);\nexport const updateSyncConfig: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateSyncConfig> &\n    typeof publicUpdateSyncConfig\n> = /*#__PURE__*/ createRESTModule(publicUpdateSyncConfig);\nexport const disconnect: MaybeContext<\n  BuildRESTFunction<typeof publicDisconnect> & typeof publicDisconnect\n> = /*#__PURE__*/ createRESTModule(publicDisconnect);\nexport const listEvents: MaybeContext<\n  BuildRESTFunction<typeof publicListEvents> & typeof publicListEvents\n> = /*#__PURE__*/ createRESTModule(publicListEvents);\n\nexport {\n  CalendarType,\n  ConnectMethod,\n  ListEventFromCalendars,\n  SyncToCalendar,\n  Status,\n  ErrorReason,\n  SyncToErrorReason,\n  Day,\n  Transparency,\n  RecurringIntervalType,\n  LocationType,\n  LocationStatus,\n  LocationsLocationType,\n  DayOfWeek,\n  ApprovalStatus,\n  ScheduleStatus,\n  ConferenceType,\n  SessionStatus,\n  SessionType,\n} from './bookings-calendar-v2-external-calendar-external-calendars.universal.js';\nexport {\n  ExternalCalendar,\n  ListProvidersRequest,\n  ListProvidersResponse,\n  Provider,\n  ProviderFeatures,\n  GetConnectionRequest,\n  GetConnectionResponse,\n  Connection,\n  ConnectionSyncConfig,\n  Calendar,\n  PrimaryCalendar,\n  Calendars,\n  DedicatedCalendar,\n  ConnectionSyncConfigListEventFromCalendars,\n  ConnectionSyncConfigListEventFromCalendarsListFromOneOf,\n  ConnectionSyncConfigSyncToCalendar,\n  ConnectionSyncConfigSyncToCalendarSyncToOneOf,\n  ListConnectionsRequest,\n  ListConnectionsResponse,\n  ConnectByOAuthRequest,\n  ConnectByOAuthResponse,\n  RawHttpRequest,\n  PathParametersEntry,\n  QueryParametersEntry,\n  HeadersEntry,\n  RawHttpResponse,\n  ConnectByCredentialsRequest,\n  ConnectByCredentialsResponse,\n  ListCalendarsRequest,\n  ListCalendarsResponse,\n  UpdateSyncConfigRequest,\n  UpdateSyncConfigResponse,\n  DisconnectRequest,\n  DisconnectResponse,\n  ListEventsRequest,\n  CursorPaging,\n  ListEventsResponse,\n  Event,\n  CursorPagingMetadata,\n  Cursors,\n  ScheduleNotification,\n  ScheduleNotificationEventOneOf,\n  ScheduleCreated,\n  Schedule,\n  RecurringInterval,\n  Interval,\n  Frequency,\n  LinkedSchedule,\n  Location,\n  Address,\n  AddressStreetOneOf,\n  StreetAddress,\n  AddressLocation,\n  Subdivision,\n  LocationsLocation,\n  LocationsAddress,\n  LocationsStreetAddress,\n  LocationsAddressLocation,\n  BusinessSchedule,\n  TimePeriod,\n  SpecialHourPeriod,\n  Rate,\n  Price,\n  Availability,\n  AvailabilityConstraints,\n  SplitInterval,\n  Participant,\n  ExternalCalendarOverrides,\n  Version,\n  ConferenceProvider,\n  CalendarConference,\n  ScheduleUpdated,\n  RecurringSessionsUpdated,\n  Session,\n  CalendarDateTime,\n  LocalDateTime,\n  ExternalCalendarInfo,\n  SessionVersion,\n  ParticipantNotification,\n  ScheduleCancelled,\n  SessionCreated,\n  SessionUpdated,\n  SessionCancelled,\n  AvailabilityPolicyUpdated,\n  AvailabilityPolicy,\n  IntervalSplit,\n  RecurringSessionSplit,\n  ScheduleUnassignedFromUser,\n  MultipleSessionsCreated,\n  ScheduleWithSessions,\n  SitePropertiesOnScheduleCreation,\n  MigrationEvent,\n  MigrationData,\n  StaffData,\n  Empty,\n  ListConnectionsOptions,\n  ListEventsOptions,\n} from './bookings-calendar-v2-external-calendar-external-calendars.universal.js';\nexport {\n  CalendarTypeWithLiterals,\n  ConnectMethodWithLiterals,\n  ListEventFromCalendarsWithLiterals,\n  SyncToCalendarWithLiterals,\n  StatusWithLiterals,\n  ErrorReasonWithLiterals,\n  SyncToErrorReasonWithLiterals,\n  DayWithLiterals,\n  TransparencyWithLiterals,\n  RecurringIntervalTypeWithLiterals,\n  LocationTypeWithLiterals,\n  LocationStatusWithLiterals,\n  LocationsLocationTypeWithLiterals,\n  DayOfWeekWithLiterals,\n  ApprovalStatusWithLiterals,\n  ScheduleStatusWithLiterals,\n  ConferenceTypeWithLiterals,\n  SessionStatusWithLiterals,\n  SessionTypeWithLiterals,\n  ConnectByOAuthApplicationErrors,\n  ConnectByCredentialsApplicationErrors,\n  ListCalendarsApplicationErrors,\n  UpdateSyncConfigApplicationErrors,\n  DisconnectApplicationErrors,\n} from './bookings-calendar-v2-external-calendar-external-calendars.universal.js';\n"], "mappings": ";AAAA,SAAS,kBAAkB,yBAAyB;AACpD;AAAA,EACE;AAAA,EACA;AAAA,OACK;;;ACJP,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,qEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAkBd,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,oCAAoC,MAAM,SAAS,KAAK;AAAA,MACvE;AAAA,MACA,QAAQ,kBAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK;AAAA,YACH;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ,kBAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,QAAQ,kBAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK;AAAA,YACH;AAAA,cACE,WAAW;AAAA,cACX,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ,kBAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAkBO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,sCAAsC,MAAM,SAAS,KAAK;AAAA,MACzE;AAAA,MACA,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsEO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA4CO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WACE;AAAA,UACF,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,QAAQ,kBAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK;AAAA,YACH;AAAA,cACE,WACE;AAAA,cACF,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ,kBAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAwBO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,YAAY,CAAC;AAAA,MAC/B;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WACE;AAAA,UACF,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgBO,SAAS,WAAW,SAA6C;AACtE,WAAS,aAAa,EAAE,KAAK,GAAQ;AACnC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WACE;AAAA,UACF,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsCO,SAAS,WAAW,SAA6C;AACtE,WAAS,aAAa,EAAE,KAAK,GAAQ;AACnC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,iCAAiC,MAAM,SAAS,KAAK;AAAA,MACpE;AAAA,MACA,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AD7dO,IAAK,eAAL,kBAAKA,kBAAL;AAEL,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,YAAS;AAET,EAAAA,cAAA,WAAQ;AAER,EAAAA,cAAA,aAAU;AAEV,EAAAA,cAAA,gBAAa;AAEb,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,WAAQ;AAdE,SAAAA;AAAA,GAAA;AAkDL,IAAK,gBAAL,kBAAKC,mBAAL;AAKL,EAAAA,eAAA,eAAY;AAKZ,EAAAA,eAAA,WAAQ;AAKR,EAAAA,eAAA,iBAAc;AAfJ,SAAAA;AAAA,GAAA;AAyBL,IAAK,yBAAL,kBAAKC,4BAAL;AAKL,EAAAA,wBAAA,eAAY;AAEZ,EAAAA,wBAAA,mBAAgB;AAKhB,EAAAA,wBAAA,2BAAwB;AAOxB,EAAAA,wBAAA,wBAAqB;AAnBX,SAAAA;AAAA,GAAA;AA8BL,IAAK,iBAAL,kBAAKC,oBAAL;AAML,EAAAA,gBAAA,eAAY;AAMZ,EAAAA,gBAAA,mBAAgB;AAMhB,EAAAA,gBAAA,2BAAwB;AAExB,EAAAA,gBAAA,uBAAoB;AASpB,EAAAA,gBAAA,wBAAqB;AA7BX,SAAAA;AAAA,GAAA;AA8FL,IAAK,SAAL,kBAAKC,YAAL;AAEL,EAAAA,QAAA,eAAY;AAMZ,EAAAA,QAAA,eAAY;AAEZ,EAAAA,QAAA,sBAAmB;AAEnB,EAAAA,QAAA,YAAS;AAKT,EAAAA,QAAA,kBAAe;AAEf,EAAAA,QAAA,WAAQ;AAnBE,SAAAA;AAAA,GAAA;AAgCL,IAAK,cAAL,kBAAKC,iBAAL;AAEL,EAAAA,aAAA,eAAY;AAEZ,EAAAA,aAAA,mBAAgB;AAEhB,EAAAA,aAAA,uCAAoC;AAEpC,EAAAA,aAAA,+BAA4B;AARlB,SAAAA;AAAA,GAAA;AA2HL,IAAK,oBAAL,kBAAKC,uBAAL;AAEL,EAAAA,mBAAA,eAAY;AAEZ,EAAAA,mBAAA,+BAA4B;AAE5B,EAAAA,mBAAA,sBAAmB;AANT,SAAAA;AAAA,GAAA;AAofL,IAAK,MAAL,kBAAKC,SAAL;AAEL,EAAAA,KAAA,eAAY;AAEZ,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAEN,EAAAA,KAAA,SAAM;AAhBI,SAAAA;AAAA,GAAA;AAuDL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,UAAO;AAEP,EAAAA,cAAA,UAAO;AALG,SAAAA;AAAA,GAAA;AAeL,IAAK,wBAAL,kBAAKC,2BAAL;AAEL,EAAAA,uBAAA,eAAY;AAEZ,EAAAA,uBAAA,WAAQ;AAER,EAAAA,uBAAA,uBAAoB;AAEpB,EAAAA,uBAAA,kBAAe;AARL,SAAAA;AAAA,GAAA;AAqCL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,oBAAiB;AACjB,EAAAA,cAAA,kBAAe;AACf,EAAAA,cAAA,YAAS;AAJC,SAAAA;AAAA,GAAA;AAsJL,IAAK,iBAAL,kBAAKC,oBAAL;AACL,EAAAA,gBAAA,YAAS;AACT,EAAAA,gBAAA,cAAW;AAFD,SAAAA;AAAA,GAAA;AASL,IAAK,wBAAL,kBAAKC,2BAAL;AACL,EAAAA,uBAAA,aAAU;AACV,EAAAA,uBAAA,YAAS;AACT,EAAAA,uBAAA,aAAU;AACV,EAAAA,uBAAA,eAAY;AACZ,EAAAA,uBAAA,kBAAe;AACf,EAAAA,uBAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AA+FL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,aAAU;AACV,EAAAA,WAAA,eAAY;AACZ,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AA4JL,IAAK,iBAAL,kBAAKC,oBAAL;AAEL,EAAAA,gBAAA,eAAY;AAEZ,EAAAA,gBAAA,aAAU;AAEV,EAAAA,gBAAA,cAAW;AAEX,EAAAA,gBAAA,cAAW;AARD,SAAAA;AAAA,GAAA;AA0BL,IAAK,iBAAL,kBAAKC,oBAAL;AAEL,EAAAA,gBAAA,eAAY;AAEZ,EAAAA,gBAAA,aAAU;AAEV,EAAAA,gBAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AAiDL,IAAK,iBAAL,kBAAKC,oBAAL;AAEL,EAAAA,gBAAA,eAAY;AAEZ,EAAAA,gBAAA,6BAA0B;AAE1B,EAAAA,gBAAA,YAAS;AANC,SAAAA;AAAA,GAAA;AAqQL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,eAAY;AAEZ,EAAAA,eAAA,eAAY;AAMZ,EAAAA,eAAA,eAAY;AAVF,SAAAA;AAAA,GAAA;AAoBL,IAAK,cAAL,kBAAKC,iBAAL;AACL,EAAAA,aAAA,eAAY;AAKZ,EAAAA,aAAA,WAAQ;AAER,EAAAA,aAAA,mBAAgB;AAEhB,EAAAA,aAAA,uBAAoB;AAEpB,EAAAA,aAAA,kBAAe;AAZL,SAAAA;AAAA,GAAA;AAoNZ,eAAsBC,iBASpB;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC,CAAC,CAAC;AAExD,QAAM,UAC4C,cAAc,OAAO;AAEvE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,CAAC;AAAA,QAC3B,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC;AAAA,IACH;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAgBA,eAAsBC,eACpB,cAUA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAC4C,cAAc,OAAO;AAEvE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,cAAc,OAAO;AAAA,QACjD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc;AAAA,IACjB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAwBA,eAAsBC,iBACpB,SAWA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,aAAa,SAAS;AAAA,IACtB,gBAAgB,SAAS;AAAA,EAC3B,CAAC;AAED,QAAM,UAC4C,gBAAgB,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,aAAa;AAAA,UACb,gBAAgB;AAAA,QAClB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAuGA,eAAsBC,gBACpB,YACA,YACA,aAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UAC4C,eAAe,OAAO;AAExE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc,cAAc,aAAa;AAAA,IAC5C;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyDA,eAAsBC,sBACpB,YACA,YACA,OACA,UAYA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UAC4C;AAAA,IAC9C;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc,cAAc,SAAS,UAAU;AAAA,IAClD;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAWA,eAAsBC,eAAc,cAIlC;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAC4C,cAAc,OAAO;AAEvE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,cAAc,OAAO;AAAA,QACjD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc;AAAA,IACjB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAgCA,eAAsBC,kBACpB,cACA,YAYA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UAC4C,iBAAiB,OAAO;AAE1E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,cAAc,QAAQ,YAAY,OAAO;AAAA,QACrE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,gBAAgB,YAAY;AAAA,IAC/B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAuBA,eAAsBC,YAAW,cAW/B;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAC4C,WAAW,OAAO;AAEpE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,cAAc,OAAO;AAAA,QACjD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc;AAAA,IACjB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA2CA,eAAsBC,YACpB,SAOA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,MAAM,SAAS;AAAA,IACf,IAAI,SAAS;AAAA,IACb,aAAa,SAAS;AAAA,IACtB,SAAS,SAAS;AAAA,IAClB,QAAQ,SAAS;AAAA,IACjB,WAAW,SAAS;AAAA,IACpB,cAAc,SAAS;AAAA,IACvB,gBAAgB,SAAS;AAAA,EAC3B,CAAC;AAED,QAAM,UAC4C,WAAW,OAAO;AAEpE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,cAAc;AAAA,UACd,gBAAgB;AAAA,QAClB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;AEnoFO,SAASC,eAAc,YAAgD;AAC5E,SAAO,MACLA;AAAA;AAAA,IAEE,EAAE,WAAW;AAAA,EACf;AACJ;AA+BO,SAASC,eAAc,YAAgD;AAC5E,SAAO,CAAC,iBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAuBO,SAASC,iBACd,YAC0B;AAC1B,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiCO,SAASC,gBACd,YACyB;AACzB,SAAO,CAAC,YAAoB,YAAoB,gBAC9CA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAoFO,SAASC,sBACd,YAC+B;AAC/B,SAAO,CACL,YACA,YACA,OACA,aAEAA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAoEO,SAASC,eAAc,YAAgD;AAC5E,SAAO,CAAC,iBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAcO,SAASC,kBACd,YAC2B;AAC3B,SAAO,CAAC,cAAsB,eAC5BA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAyCO,SAASC,YAAW,YAA6C;AACtE,SAAO,CAAC,iBACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAgCO,SAASC,YAAW,YAA6C;AACtE,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;;;AC5aA,SAAS,wBAAwB;AAG1B,IAAMC,iBAEK,iCAAiBA,cAAmB;AAC/C,IAAMC,iBAEK,iCAAiBA,cAAmB;AAC/C,IAAMC,mBAEK,iCAAiBA,gBAAqB;AACjD,IAAMC,kBAEK,iCAAiBA,eAAoB;AAChD,IAAMC,wBAGK,iCAAiBA,qBAA0B;AACtD,IAAMC,iBAEK,iCAAiBA,cAAmB;AAC/C,IAAMC,oBAGK,iCAAiBA,iBAAsB;AAClD,IAAMC,cAEK,iCAAiBA,WAAgB;AAC5C,IAAMC,cAEK,iCAAiBA,WAAgB;", "names": ["CalendarType", "ConnectMethod", "ListEventFromCalendars", "SyncToCalendar", "Status", "ErrorReason", "SyncToErrorReason", "Day", "Transparency", "RecurringIntervalType", "LocationType", "LocationStatus", "LocationsLocationType", "DayOfWeek", "ApprovalStatus", "ScheduleStatus", "ConferenceType", "SessionStatus", "SessionType", "listProviders", "getConnection", "listConnections", "connectByOAuth", "connectByCredentials", "listCalendars", "updateSyncConfig", "disconnect", "listEvents", "listProviders", "getConnection", "listConnections", "connectByOAuth", "connectByCredentials", "listCalendars", "updateSyncConfig", "disconnect", "listEvents", "listProviders", "getConnection", "listConnections", "connectByOAuth", "connectByCredentials", "listCalendars", "updateSyncConfig", "disconnect", "listEvents"]}