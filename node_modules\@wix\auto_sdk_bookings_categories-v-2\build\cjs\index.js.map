{"version": 3, "sources": ["../../index.ts", "../../src/bookings-categories-v2-category-categories-v-2.public.ts", "../../src/bookings-categories-v2-category-categories-v-2.universal.ts", "../../src/bookings-categories-v2-category-categories-v-2.http.ts", "../../src/bookings-categories-v2-category-categories-v-2.context.ts"], "sourcesContent": ["export * from './src/bookings-categories-v2-category-categories-v-2.context.js';\n", "import { renameKeysFromRESTResponseToSDKResponse } from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { EventDefinition, HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  CategoriesQueryBuilder,\n  Category,\n  CategoryCreatedEnvelope,\n  CategoryDeletedEnvelope,\n  CategoryUpdatedEnvelope,\n  CountCategoriesOptions,\n  CountCategoriesResponse,\n  CreateCategoryApplicationErrors,\n  MoveCategoryOptions,\n  MoveCategoryResponse,\n  UpdateCategory,\n  countCategories as universalCountCategories,\n  createCategory as universalCreateCategory,\n  deleteCategory as universalDeleteCategory,\n  getCategory as universalGetCategory,\n  moveCategory as universalMoveCategory,\n  queryCategories as universalQueryCategories,\n  updateCategory as universalUpdateCategory,\n} from './bookings-categories-v2-category-categories-v-2.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function createCategory(\n  httpClient: HttpClient\n): CreateCategorySignature {\n  return (category: NonNullablePaths<Category, `name`, 2>) =>\n    universalCreateCategory(\n      category,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CreateCategorySignature {\n  /**\n   * Creates a category.\n   * @param - Category to create.\n   * @returns Created category.\n   */\n  (category: NonNullablePaths<Category, `name`, 2>): Promise<\n    Category & {\n      __applicationErrorsType?: CreateCategoryApplicationErrors;\n    }\n  >;\n}\n\nexport function getCategory(httpClient: HttpClient): GetCategorySignature {\n  return (categoryId: string) =>\n    universalGetCategory(\n      categoryId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetCategorySignature {\n  /**\n   * Retrieves a category.\n   * @param - ID of the category to retrieve.\n   * @returns Retrieved category.\n   */\n  (categoryId: string): Promise<Category>;\n}\n\nexport function updateCategory(\n  httpClient: HttpClient\n): UpdateCategorySignature {\n  return (\n    _id: string,\n    category: NonNullablePaths<UpdateCategory, `revision`, 2>\n  ) =>\n    universalUpdateCategory(\n      _id,\n      category,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateCategorySignature {\n  /**\n   * Updates a category.\n   *\n   *\n   * Each time the category is updated, `revision` increments by 1.\n   * You must specify the current `revision` to prevent unintended overwrites.\n   *\n   * You can't adjust a categories `sortOrder` with this method, call Move Category ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/move-category) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/move-category)) instead.\n   * @param - Category ID.\n   * @returns Updated category.\n   */\n  (\n    _id: string,\n    category: NonNullablePaths<UpdateCategory, `revision`, 2>\n  ): Promise<Category>;\n}\n\nexport function deleteCategory(\n  httpClient: HttpClient\n): DeleteCategorySignature {\n  return (categoryId: string) =>\n    universalDeleteCategory(\n      categoryId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DeleteCategorySignature {\n  /**\n   * Deletes a category.\n   *\n   *\n   * ## Impact on connected services\n   *\n   * When you delete a category, any services linked to it remain associated with the now-deleted category. Wix Bookings still displays these services to business owners in the dashboard, but they aren't visible to customers on the live site.\n   * Attempting to call Update Service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service)) for a service that's linked to a deleted category fails, unless you specify a different, existing category ID in `service.category.id`.\n   * @param - ID of the category to delete.\n   */\n  (categoryId: string): Promise<void>;\n}\n\nexport function queryCategories(\n  httpClient: HttpClient\n): QueryCategoriesSignature {\n  return () =>\n    universalQueryCategories(\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface QueryCategoriesSignature {\n  /**\n   * Creates a query to retrieve a list of `category` objects.\n   *\n   * The `queryCategories()` function builds a query to retrieve a list of `category` objects and returns a `categoriesQueryBuilder` object.\n   *\n   * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-builder/find) function.\n   *\n   * You can refine the query by chaining `CategoriesQueryBuilder` functions onto the query. `CategoriesQueryBuilder` functions enable you to sort, filter, and control the results that `queryCategories()` returns.\n   *\n   * `queryCategories()` runs with the following `CategoriesQueryBuilder` defaults that you can override:\n   *\n   * + `limit` is `100`.\n   * + Sorted by `createdDate` in ascending order.\n   *\n   * The functions that are chained to `queryCategories()` are applied in the order they are called. For example, if you apply `ascending(\"name\")` and then `ascending(\"id\")`, the results are sorted first by `name`, and then, if there are multiple results with the same `name`, the items are sorted by `id`.\n   *\n   * The following `CategoriesQueryBuilder` functions are supported for the `queryCategories()` function. For a full description of the `category` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-result/items) property in `CategoriesQueryResult`.\n   */\n  (): CategoriesQueryBuilder;\n}\n\nexport function countCategories(\n  httpClient: HttpClient\n): CountCategoriesSignature {\n  return (options?: CountCategoriesOptions) =>\n    universalCountCategories(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CountCategoriesSignature {\n  /**\n   * Counts categories, given the specified filtering.\n   *\n   *\n   * Refer to the Supported Filters article ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/filtering-and-sorting)) for a complete list of supported filters.\n   */\n  (options?: CountCategoriesOptions): Promise<\n    NonNullablePaths<CountCategoriesResponse, `count`, 2>\n  >;\n}\n\nexport function moveCategory(httpClient: HttpClient): MoveCategorySignature {\n  return (categoryId: string, options?: MoveCategoryOptions) =>\n    universalMoveCategory(\n      categoryId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface MoveCategorySignature {\n  /**\n   * Moves a category to the start, end, or immediately after a specified category by updating its `sortOrder` field.\n   *\n   * Wix Bookings assigns `sortOrder` values with large gaps between categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new values to restore larger gaps.\n   * @param - ID of the category to move.\n   */\n  (\n    categoryId: string,\n    options?: MoveCategoryOptions\n  ): Promise<MoveCategoryResponse>;\n}\n\nexport const onCategoryCreated = EventDefinition(\n  'wix.bookings.categories.v2.category_created',\n  true,\n  (event: CategoryCreatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<CategoryCreatedEnvelope>();\nexport const onCategoryDeleted = EventDefinition(\n  'wix.bookings.categories.v2.category_deleted',\n  true,\n  (event: CategoryDeletedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<CategoryDeletedEnvelope>();\nexport const onCategoryUpdated = EventDefinition(\n  'wix.bookings.categories.v2.category_updated',\n  true,\n  (event: CategoryUpdatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n      ])\n    )\n)<CategoryUpdatedEnvelope>();\n\nexport {\n  ActionEvent,\n  BaseEventMetadata,\n  CategoriesQueryBuilder,\n  CategoriesQueryResult,\n  Category,\n  CategoryCreatedEnvelope,\n  CategoryDeletedEnvelope,\n  CategoryUpdatedEnvelope,\n  CountCategoriesOptions,\n  CountCategoriesRequest,\n  CountCategoriesResponse,\n  CreateCategoryRequest,\n  CreateCategoryResponse,\n  CursorPaging,\n  CursorPagingMetadata,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  Cursors,\n  DeleteCategoryRequest,\n  DeleteCategoryResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  EntityDeletedEvent,\n  EntityUpdatedEvent,\n  EventMetadata,\n  ExtendedFields,\n  GetCategoryRequest,\n  GetCategoryResponse,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  ImportCategoriesRequest,\n  ImportCategoriesResponse,\n  MessageEnvelope,\n  MoveCategoryOptions,\n  MoveCategoryRequest,\n  MoveCategoryResponse,\n  Position,\n  PublishCategoriesUpdatedRequest,\n  PublishCategoriesUpdatedResponse,\n  QueryCategoriesRequest,\n  QueryCategoriesResponse,\n  RestoreInfo,\n  SortOrder,\n  Sorting,\n  UpdateCategory,\n  UpdateCategoryRequest,\n  UpdateCategoryResponse,\n  WebhookIdentityType,\n} from './bookings-categories-v2-category-categories-v-2.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameK<PERSON>s<PERSON>romRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, HttpResponse, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsCategoriesV2Category from './bookings-categories-v2-category-categories-v-2.http.js';\n// @ts-ignore\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/** Categories organize services ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) by controlling the order in which customers see services on the live site and business owners see them in the dashboard. */\nexport interface Category {\n  /**\n   * Category ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the category is updated.\n   * To prevent conflicting changes, you must specify the current revision when updating the category.\n   *\n   * Ignored when creating a category.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Date and time the category was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the Category was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Category name.\n   * @minLength 1\n   * @maxLength 100\n   */\n  name?: string | null;\n  /**\n   * Defines the category's position in the categories list relative to other categories.\n   * Wix Bookings assigns `sortOrder` values with large gaps between adjacent categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new `sortOrder` values to restore larger gaps.\n   * @readonly\n   */\n  sortOrder?: number | null;\n  /**\n   * Custom field data for the category object.\n   *\n   * [Extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-schema-plugin-extensions) must be configured in the app dashboard before they can be accessed with API calls.\n   */\n  extendedFields?: ExtendedFields;\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\nexport interface CreateCategoryRequest {\n  /** Category to create. */\n  category: Category;\n}\n\nexport interface CreateCategoryResponse {\n  /** Created category. */\n  category?: Category;\n}\n\nexport interface GetCategoryRequest {\n  /**\n   * ID of the category to retrieve.\n   * @format GUID\n   */\n  categoryId: string;\n}\n\nexport interface GetCategoryResponse {\n  /** Retrieved category. */\n  category?: Category;\n}\n\nexport interface UpdateCategoryRequest {\n  /** Category to update. */\n  category: Category;\n}\n\nexport interface UpdateCategoryResponse {\n  /** Updated category. */\n  category?: Category;\n}\n\nexport interface DeleteCategoryRequest {\n  /**\n   * ID of the category to delete.\n   * @format GUID\n   */\n  categoryId: string;\n}\n\nexport interface DeleteCategoryResponse {}\n\nexport interface QueryCategoriesRequest {\n  /** WQL expression. */\n  query?: CursorQuery;\n}\n\nexport interface CursorQuery extends CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   * @maxSize 5\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface CursorPaging {\n  /**\n   * Maximum number of items to return in the results.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface QueryCategoriesResponse {\n  /** Retrieved categories. */\n  categories?: Category[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Cursor strings that point to the next page, previous page, or both. */\n  cursors?: Cursors;\n  /**\n   * Whether there are more pages to retrieve following the current page.\n   *\n   * + `true`: Another page of results can be retrieved.\n   * + `false`: This is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor string pointing to the next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to the previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface CountCategoriesRequest {\n  /** Filter to base the count on. */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountCategoriesResponse {\n  /** Number of categories matching the filter. */\n  count?: number;\n}\n\nexport interface MoveCategoryRequest {\n  /**\n   * ID of the category to move.\n   * @format GUID\n   */\n  categoryId: string;\n  /** New position of the category. */\n  position?: PositionWithLiterals;\n  /**\n   * ID of the reference category.\n   * Specify only for `{\"position\": \"AFTER_CATEGORY\"}`.\n   * @format GUID\n   */\n  afterCategoryId?: string | null;\n}\n\nexport enum Position {\n  UNKNOWN_POSITION = 'UNKNOWN_POSITION',\n  /** Place the category at the end of the list. */\n  LAST = 'LAST',\n  /** Place the category at the beginning of the list. */\n  FIRST = 'FIRST',\n  /** Place the category immediately after a specific category. */\n  AFTER_CATEGORY = 'AFTER_CATEGORY',\n}\n\n/** @enumType */\nexport type PositionWithLiterals =\n  | Position\n  | 'UNKNOWN_POSITION'\n  | 'LAST'\n  | 'FIRST'\n  | 'AFTER_CATEGORY';\n\nexport interface MoveCategoryResponse {\n  /** Updated category. */\n  category?: Category;\n}\n\nexport interface ImportCategoriesRequest {\n  /**\n   * List of categories to import.\n   * @minSize 1\n   * @maxSize 100\n   */\n  categories?: Category[];\n}\n\nexport interface ImportCategoriesResponse {}\n\nexport interface PublishCategoriesUpdatedRequest {}\n\nexport interface PublishCategoriesUpdatedResponse {}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type CreateCategoryApplicationErrors = {\n  code?: 'CATEGORY_LIMIT_REACHED';\n  description?: string;\n  data?: Record<string, any>;\n};\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface CategoryCreatedEnvelope {\n  entity: Category;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a category is created.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.CATEGORY_READ\n * @webhook\n * @eventType wix.bookings.categories.v2.category_created\n * @slug created\n */\nexport declare function onCategoryCreated(\n  handler: (event: CategoryCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface CategoryDeletedEnvelope {\n  entity: Category;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a category is deleted.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.CATEGORY_READ\n * @webhook\n * @eventType wix.bookings.categories.v2.category_deleted\n * @slug deleted\n */\nexport declare function onCategoryDeleted(\n  handler: (event: CategoryDeletedEnvelope) => void | Promise<void>\n): void;\n\nexport interface CategoryUpdatedEnvelope {\n  entity: Category;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a category is updated.\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.CATEGORY_READ\n * @webhook\n * @eventType wix.bookings.categories.v2.category_updated\n * @slug updated\n */\nexport declare function onCategoryUpdated(\n  handler: (event: CategoryUpdatedEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Creates a category.\n * @param category - Category to create.\n * @public\n * @requiredField category\n * @requiredField category.name\n * @permissionId BOOKINGS.CATEGORY_CREATE\n * @applicableIdentity APP\n * @returns Created category.\n * @fqn wix.bookings.categories.v2.CategoriesService.CreateCategory\n */\nexport async function createCategory(\n  category: NonNullablePaths<Category, `name`, 2>\n): Promise<\n  Category & {\n    __applicationErrorsType?: CreateCategoryApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ category: category });\n\n  const reqOpts =\n    ambassadorWixBookingsCategoriesV2Category.createCategory(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { category: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['category']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a category.\n * @param categoryId - ID of the category to retrieve.\n * @public\n * @requiredField categoryId\n * @permissionId BOOKINGS.CATEGORY_READ\n * @applicableIdentity APP\n * @applicableIdentity VISITOR\n * @returns Retrieved category.\n * @fqn wix.bookings.categories.v2.CategoriesService.GetCategory\n */\nexport async function getCategory(categoryId: string): Promise<Category> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    categoryId: categoryId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCategoriesV2Category.getCategory(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { categoryId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['categoryId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates a category.\n *\n *\n * Each time the category is updated, `revision` increments by 1.\n * You must specify the current `revision` to prevent unintended overwrites.\n *\n * You can't adjust a categories `sortOrder` with this method, call Move Category ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/move-category) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/move-category)) instead.\n * @param _id - Category ID.\n * @public\n * @requiredField _id\n * @requiredField category\n * @requiredField category.revision\n * @permissionId BOOKINGS.CATEGORY_UPDATE\n * @applicableIdentity APP\n * @returns Updated category.\n * @fqn wix.bookings.categories.v2.CategoriesService.UpdateCategory\n */\nexport async function updateCategory(\n  _id: string,\n  category: NonNullablePaths<UpdateCategory, `revision`, 2>\n): Promise<Category> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    category: { ...category, id: _id },\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCategoriesV2Category.updateCategory(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { category: '$[1]' },\n        explicitPathsToArguments: { 'category.id': '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'category']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateCategory {\n  /**\n   * Category ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the category is updated.\n   * To prevent conflicting changes, you must specify the current revision when updating the category.\n   *\n   * Ignored when creating a category.\n   * @readonly\n   */\n  revision?: string | null;\n  /**\n   * Date and time the category was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the Category was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Category name.\n   * @minLength 1\n   * @maxLength 100\n   */\n  name?: string | null;\n  /**\n   * Defines the category's position in the categories list relative to other categories.\n   * Wix Bookings assigns `sortOrder` values with large gaps between adjacent categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new `sortOrder` values to restore larger gaps.\n   * @readonly\n   */\n  sortOrder?: number | null;\n  /**\n   * Custom field data for the category object.\n   *\n   * [Extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-schema-plugin-extensions) must be configured in the app dashboard before they can be accessed with API calls.\n   */\n  extendedFields?: ExtendedFields;\n}\n\n/**\n * Deletes a category.\n *\n *\n * ## Impact on connected services\n *\n * When you delete a category, any services linked to it remain associated with the now-deleted category. Wix Bookings still displays these services to business owners in the dashboard, but they aren't visible to customers on the live site.\n * Attempting to call Update Service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service)) for a service that's linked to a deleted category fails, unless you specify a different, existing category ID in `service.category.id`.\n * @param categoryId - ID of the category to delete.\n * @public\n * @requiredField categoryId\n * @permissionId BOOKINGS.CATEGORY_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.categories.v2.CategoriesService.DeleteCategory\n */\nexport async function deleteCategory(categoryId: string): Promise<void> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    categoryId: categoryId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCategoriesV2Category.deleteCategory(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { categoryId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['categoryId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Creates a query to retrieve a list of `category` objects.\n *\n * The `queryCategories()` function builds a query to retrieve a list of `category` objects and returns a `categoriesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-builder/find) function.\n *\n * You can refine the query by chaining `CategoriesQueryBuilder` functions onto the query. `CategoriesQueryBuilder` functions enable you to sort, filter, and control the results that `queryCategories()` returns.\n *\n * `queryCategories()` runs with the following `CategoriesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `100`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryCategories()` are applied in the order they are called. For example, if you apply `ascending(\"name\")` and then `ascending(\"id\")`, the results are sorted first by `name`, and then, if there are multiple results with the same `name`, the items are sorted by `id`.\n *\n * The following `CategoriesQueryBuilder` functions are supported for the `queryCategories()` function. For a full description of the `category` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-result/items) property in `CategoriesQueryResult`.\n * @public\n * @permissionId BOOKINGS.CATEGORY_READ\n * @applicableIdentity APP\n * @applicableIdentity VISITOR\n * @fqn wix.bookings.categories.v2.CategoriesService.QueryCategories\n */\nexport function queryCategories(): CategoriesQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    Category,\n    'CURSOR',\n    QueryCategoriesRequest,\n    QueryCategoriesResponse\n  >({\n    func: async (payload: QueryCategoriesRequest) => {\n      const reqOpts =\n        ambassadorWixBookingsCategoriesV2Category.queryCategories(payload);\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (query: QueryCategoriesRequest['query']) => {\n      const args = [query, {}] as [QueryCategoriesRequest['query'], {}];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({ data }: HttpResponse<QueryCategoriesResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [])\n      );\n\n      return {\n        items: transformedData?.categories,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'CURSOR',\n    transformationPaths: {},\n  });\n}\n\ninterface QueryCursorResult {\n  cursors: Cursors;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface CategoriesQueryResult extends QueryCursorResult {\n  items: Category[];\n  query: CategoriesQueryBuilder;\n  next: () => Promise<CategoriesQueryResult>;\n  prev: () => Promise<CategoriesQueryResult>;\n}\n\nexport interface CategoriesQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  eq: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any\n  ) => CategoriesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ne: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any\n  ) => CategoriesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ge: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any\n  ) => CategoriesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  gt: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any\n  ) => CategoriesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  le: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any\n  ) => CategoriesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  lt: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any\n  ) => CategoriesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `string`.\n   * @param string - String to compare against. Case-insensitive.\n   */\n  startsWith: (\n    propertyName: '_id' | 'name',\n    value: string\n  ) => CategoriesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   */\n  hasSome: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any[]\n  ) => CategoriesQueryBuilder;\n  in: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: any\n  ) => CategoriesQueryBuilder;\n  exists: (\n    propertyName: '_id' | 'name' | 'sortOrder',\n    value: boolean\n  ) => CategoriesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  ascending: (\n    ...propertyNames: Array<'_id' | 'name' | 'sortOrder'>\n  ) => CategoriesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  descending: (\n    ...propertyNames: Array<'_id' | 'name' | 'sortOrder'>\n  ) => CategoriesQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */\n  limit: (limit: number) => CategoriesQueryBuilder;\n  /** @param cursor - A pointer to specific record */\n  skipTo: (cursor: string) => CategoriesQueryBuilder;\n  find: () => Promise<CategoriesQueryResult>;\n}\n\n/**\n * Counts categories, given the specified filtering.\n *\n *\n * Refer to the Supported Filters article ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/filtering-and-sorting)) for a complete list of supported filters.\n * @public\n * @permissionId BOOKINGS.CATEGORY_READ\n * @applicableIdentity APP\n * @applicableIdentity VISITOR\n * @fqn wix.bookings.categories.v2.CategoriesService.CountCategories\n */\nexport async function countCategories(\n  options?: CountCategoriesOptions\n): Promise<NonNullablePaths<CountCategoriesResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCategoriesV2Category.countCategories(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountCategoriesOptions {\n  /** Filter to base the count on. */\n  filter?: Record<string, any> | null;\n}\n\n/**\n * Moves a category to the start, end, or immediately after a specified category by updating its `sortOrder` field.\n *\n * Wix Bookings assigns `sortOrder` values with large gaps between categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new values to restore larger gaps.\n * @param categoryId - ID of the category to move.\n * @public\n * @requiredField categoryId\n * @permissionId BOOKINGS.CATEGORY_MOVE\n * @applicableIdentity APP\n * @fqn wix.bookings.categories.v2.CategoriesService.MoveCategory\n */\nexport async function moveCategory(\n  categoryId: string,\n  options?: MoveCategoryOptions\n): Promise<MoveCategoryResponse> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    categoryId: categoryId,\n    position: options?.position,\n    afterCategoryId: options?.afterCategoryId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsCategoriesV2Category.moveCategory(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          categoryId: '$[0]',\n          position: '$[1].position',\n          afterCategoryId: '$[1].afterCategoryId',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['categoryId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface MoveCategoryOptions {\n  /** New position of the category. */\n  position?: PositionWithLiterals;\n  /**\n   * ID of the reference category.\n   * Specify only for `{\"position\": \"AFTER_CATEGORY\"}`.\n   * @format GUID\n   */\n  afterCategoryId?: string | null;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsCategoriesV2CategoriesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/categories',\n        destPath: '/v2/categories',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/categories',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/categories',\n        destPath: '/v2/categories',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/bookings/v2/categories',\n        destPath: '/v2/categories',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/categories',\n        destPath: '/v2/categories',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/categories',\n        destPath: '/v2/categories',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings/v2/categories',\n        destPath: '/v2/categories',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings/v2/categories',\n        destPath: '/v2/categories',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_categories-v-2';\n\n/** Creates a category. */\nexport function createCategory(payload: object): RequestOptionsFactory<any> {\n  function __createCategory({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'category.createdDate' },\n          { path: 'category.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.categories.v2.category',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.categories.v2.CategoriesService.CreateCategory',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n        protoPath: '/v2/categories',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'category.createdDate' },\n              { path: 'category.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createCategory;\n}\n\n/** Retrieves a category. */\nexport function getCategory(payload: object): RequestOptionsFactory<any> {\n  function __getCategory({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.categories.v2.category',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.categories.v2.CategoriesService.GetCategory',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n        protoPath: '/v2/categories/{categoryId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'category.createdDate' },\n              { path: 'category.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getCategory;\n}\n\n/**\n * Updates a category.\n *\n *\n * Each time the category is updated, `revision` increments by 1.\n * You must specify the current `revision` to prevent unintended overwrites.\n *\n * You can't adjust a categories `sortOrder` with this method, call Move Category ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/move-category) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/move-category)) instead.\n */\nexport function updateCategory(payload: object): RequestOptionsFactory<any> {\n  function __updateCategory({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'fieldMask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'category.createdDate' },\n          { path: 'category.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.categories.v2.category',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.categories.v2.CategoriesService.UpdateCategory',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n        protoPath: '/v2/categories/{category.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'category.createdDate' },\n              { path: 'category.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateCategory;\n}\n\n/**\n * Deletes a category.\n *\n *\n * ## Impact on connected services\n *\n * When you delete a category, any services linked to it remain associated with the now-deleted category. Wix Bookings still displays these services to business owners in the dashboard, but they aren't visible to customers on the live site.\n * Attempting to call Update Service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service)) for a service that's linked to a deleted category fails, unless you specify a different, existing category ID in `service.category.id`.\n */\nexport function deleteCategory(payload: object): RequestOptionsFactory<any> {\n  function __deleteCategory({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.categories.v2.category',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.categories.v2.CategoriesService.DeleteCategory',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n        protoPath: '/v2/categories/{categoryId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteCategory;\n}\n\n/**\n * Creates a query to retrieve a list of `category` objects.\n *\n * The `queryCategories()` function builds a query to retrieve a list of `category` objects and returns a `categoriesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-builder/find) function.\n *\n * You can refine the query by chaining `CategoriesQueryBuilder` functions onto the query. `CategoriesQueryBuilder` functions enable you to sort, filter, and control the results that `queryCategories()` returns.\n *\n * `queryCategories()` runs with the following `CategoriesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `100`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryCategories()` are applied in the order they are called. For example, if you apply `ascending(\"name\")` and then `ascending(\"id\")`, the results are sorted first by `name`, and then, if there are multiple results with the same `name`, the items are sorted by `id`.\n *\n * The following `CategoriesQueryBuilder` functions are supported for the `queryCategories()` function. For a full description of the `category` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-result/items) property in `CategoriesQueryResult`.\n */\nexport function queryCategories(payload: object): RequestOptionsFactory<any> {\n  function __queryCategories({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.categories.v2.category',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.categories.v2.CategoriesService.QueryCategories',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n        protoPath: '/v2/categories/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'categories.createdDate' },\n              { path: 'categories.updatedDate' },\n            ],\n          },\n        ]),\n      fallback: [\n        {\n          method: 'POST' as any,\n          url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n            protoPath: '/v2/categories/query',\n            data: payload,\n            host,\n          }),\n          data: payload,\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __queryCategories;\n}\n\n/**\n * Counts categories, given the specified filtering.\n *\n *\n * Refer to the Supported Filters article ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/filtering-and-sorting)) for a complete list of supported filters.\n */\nexport function countCategories(payload: object): RequestOptionsFactory<any> {\n  function __countCategories({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.categories.v2.category',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.categories.v2.CategoriesService.CountCategories',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n        protoPath: '/v2/categories/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countCategories;\n}\n\n/**\n * Moves a category to the start, end, or immediately after a specified category by updating its `sortOrder` field.\n *\n * Wix Bookings assigns `sortOrder` values with large gaps between categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new values to restore larger gaps.\n */\nexport function moveCategory(payload: object): RequestOptionsFactory<any> {\n  function __moveCategory({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.categories.v2.category',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.categories.v2.CategoriesService.MoveCategory',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({\n        protoPath: '/v2/categories/set-position/{categoryId}',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'category.createdDate' },\n              { path: 'category.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __moveCategory;\n}\n", "import {\n  createCategory as publicCreateCategory,\n  getCategory as publicGetCategory,\n  updateCategory as publicUpdateCategory,\n  deleteCategory as publicDeleteCategory,\n  queryCategories as publicQueryCategories,\n  countCategories as publicCountCategories,\n  moveCategory as publicMoveCategory,\n} from './bookings-categories-v2-category-categories-v-2.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { createEventModule } from '@wix/sdk-runtime/event-definition-modules';\nimport {\n  BuildRESTFunction,\n  MaybeContext,\n  BuildEventDefinition,\n} from '@wix/sdk-types';\nimport { onCategoryCreated as publicOnCategoryCreated } from './bookings-categories-v2-category-categories-v-2.public.js';\nimport { onCategoryDeleted as publicOnCategoryDeleted } from './bookings-categories-v2-category-categories-v-2.public.js';\nimport { onCategoryUpdated as publicOnCategoryUpdated } from './bookings-categories-v2-category-categories-v-2.public.js';\n\nexport const createCategory: MaybeContext<\n  BuildRESTFunction<typeof publicCreateCategory> & typeof publicCreateCategory\n> = /*#__PURE__*/ createRESTModule(publicCreateCategory);\nexport const getCategory: MaybeContext<\n  BuildRESTFunction<typeof publicGetCategory> & typeof publicGetCategory\n> = /*#__PURE__*/ createRESTModule(publicGetCategory);\nexport const updateCategory: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateCategory> & typeof publicUpdateCategory\n> = /*#__PURE__*/ createRESTModule(publicUpdateCategory);\nexport const deleteCategory: MaybeContext<\n  BuildRESTFunction<typeof publicDeleteCategory> & typeof publicDeleteCategory\n> = /*#__PURE__*/ createRESTModule(publicDeleteCategory);\nexport const queryCategories: MaybeContext<\n  BuildRESTFunction<typeof publicQueryCategories> & typeof publicQueryCategories\n> = /*#__PURE__*/ createRESTModule(publicQueryCategories);\nexport const countCategories: MaybeContext<\n  BuildRESTFunction<typeof publicCountCategories> & typeof publicCountCategories\n> = /*#__PURE__*/ createRESTModule(publicCountCategories);\nexport const moveCategory: MaybeContext<\n  BuildRESTFunction<typeof publicMoveCategory> & typeof publicMoveCategory\n> = /*#__PURE__*/ createRESTModule(publicMoveCategory);\n/**\n * Triggered when a category is created.\n */\nexport const onCategoryCreated: BuildEventDefinition<\n  typeof publicOnCategoryCreated\n> = createEventModule(publicOnCategoryCreated);\n/**\n * Triggered when a category is deleted.\n */\nexport const onCategoryDeleted: BuildEventDefinition<\n  typeof publicOnCategoryDeleted\n> = createEventModule(publicOnCategoryDeleted);\n/**\n * Triggered when a category is updated.\n */\nexport const onCategoryUpdated: BuildEventDefinition<\n  typeof publicOnCategoryUpdated\n> = createEventModule(publicOnCategoryUpdated);\n\nexport {\n  SortOrder,\n  Position,\n  WebhookIdentityType,\n} from './bookings-categories-v2-category-categories-v-2.universal.js';\nexport {\n  Category,\n  ExtendedFields,\n  CreateCategoryRequest,\n  CreateCategoryResponse,\n  GetCategoryRequest,\n  GetCategoryResponse,\n  UpdateCategoryRequest,\n  UpdateCategoryResponse,\n  DeleteCategoryRequest,\n  DeleteCategoryResponse,\n  QueryCategoriesRequest,\n  CursorQuery,\n  CursorQueryPagingMethodOneOf,\n  Sorting,\n  CursorPaging,\n  QueryCategoriesResponse,\n  CursorPagingMetadata,\n  Cursors,\n  CountCategoriesRequest,\n  CountCategoriesResponse,\n  MoveCategoryRequest,\n  MoveCategoryResponse,\n  ImportCategoriesRequest,\n  ImportCategoriesResponse,\n  PublishCategoriesUpdatedRequest,\n  PublishCategoriesUpdatedResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  RestoreInfo,\n  EntityUpdatedEvent,\n  EntityDeletedEvent,\n  ActionEvent,\n  MessageEnvelope,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  BaseEventMetadata,\n  EventMetadata,\n  CategoryCreatedEnvelope,\n  CategoryDeletedEnvelope,\n  CategoryUpdatedEnvelope,\n  UpdateCategory,\n  CategoriesQueryResult,\n  CategoriesQueryBuilder,\n  CountCategoriesOptions,\n  MoveCategoryOptions,\n} from './bookings-categories-v2-category-categories-v-2.universal.js';\nexport {\n  SortOrderWithLiterals,\n  PositionWithLiterals,\n  WebhookIdentityTypeWithLiterals,\n  CreateCategoryApplicationErrors,\n} from './bookings-categories-v2-category-categories-v-2.universal.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAAA;AAAA,EAAA,sBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,sBAAAC;AAAA;AAAA;;;ACAA,IAAAC,iCAAwD;AACxD,IAAAC,oBAAqD;AACrD,IAAAC,0BAA+B;AAC/B,uBAA8D;;;ACH9D,6BAAoD;AACpD,2BAA6B;AAC7B,oCAGO;;;ACLP,0BAAkC;AAClC,uBAAqD;AACrD,IAAAC,oBAAqD;AACrD,wBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,mDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAGd,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,mDAAmD;AAAA,QACtD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,YAAY,SAA6C;AACvE,WAAS,cAAc,EAAE,KAAK,GAAQ;AACpC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,mDAAmD;AAAA,QACtD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,YAAY,CAAC;AAAA,MAC/B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,mDAAmD;AAAA,QACtD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,mDAAmD;AAAA,QACtD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,mDAAmD;AAAA,QACtD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,yBAAyB;AAAA,YACjC,EAAE,MAAM,yBAAyB;AAAA,UACnC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACH,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK,mDAAmD;AAAA,YACtD,WAAW;AAAA,YACX,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,mDAAmD;AAAA,QACtD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAOO,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,mDAAmD;AAAA,QACtD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADhVA,IAAAC,0BAA+B;AAgJxB,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AAoFL,IAAK,WAAL,kBAAKC,cAAL;AACL,EAAAA,UAAA,sBAAmB;AAEnB,EAAAA,UAAA,UAAO;AAEP,EAAAA,UAAA,WAAQ;AAER,EAAAA,UAAA,oBAAiB;AAPP,SAAAA;AAAA,GAAA;AA6KL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAoKZ,eAAsBC,gBACpB,UAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC,EAAE,SAAmB,CAAC;AAE5E,QAAM,UACsC,eAAe,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,UAAU,OAAO;AAAA,QAC7C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU;AAAA,IACb;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAaA,eAAsBC,aAAY,YAAuC;AAEvE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACsC,YAAY,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,YAAY,OAAO;AAAA,QAC/C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY;AAAA,IACf;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoBA,eAAsBE,gBACpB,KACA,UACmB;AAEnB,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,UAAU,EAAE,GAAG,UAAU,IAAI,IAAI;AAAA,EACnC,CAAC;AAED,QAAM,UACsC,eAAe,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,UAAU,OAAO;AAAA,QAC3C,0BAA0B,EAAE,eAAe,OAAO;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,UAAU;AAAA,IACpB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA8DA,eAAsBG,gBAAe,YAAmC;AAEtE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACsC,eAAe,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAAA,EACjC,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAH;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,YAAY,OAAO;AAAA,QAC/C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY;AAAA,IACf;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyBO,SAASI,mBAA0C;AAExD,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,aAAO,mCAKL;AAAA,IACA,MAAM,OAAO,YAAoC;AAC/C,YAAM,UACsC,gBAAgB,OAAO;AAEnE,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,UAA2C;AAC9D,YAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AACvB,iBAAO,qEAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC,EAAE,KAAK,MAA6C;AACxE,YAAM,sBAAkB;AAAA,YACtB,wCAAe,MAAM,CAAC,CAAC;AAAA,MACzB;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,uBAAmB,uBAAAJ,gBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;AA4GA,eAAsBK,iBACpB,SACgE;AAEhE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACsC,gBAAgB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAL;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkBA,eAAsBM,cACpB,YACA,SAC+B;AAE/B,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,UAAU,SAAS;AAAA,IACnB,iBAAiB,SAAS;AAAA,EAC5B,CAAC;AAED,QAAM,UACsC,aAAa,OAAO;AAEhE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAN;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,iBAAiB;AAAA,QACnB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc,SAAS;AAAA,IAC1B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;AD7iCO,SAASO,gBACd,YACyB;AACzB,SAAO,CAAC,aACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAeO,SAASC,aAAY,YAA8C;AACxE,SAAO,CAAC,eACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAWO,SAASC,gBACd,YACyB;AACzB,SAAO,CACL,KACA,aAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAoBO,SAASC,gBACd,YACyB;AACzB,SAAO,CAAC,eACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAgBO,SAASC,iBACd,YAC0B;AAC1B,SAAO,MACLA;AAAA;AAAA,IAEE,EAAE,WAAW;AAAA,EACf;AACJ;AAwBO,SAASC,iBACd,YAC0B;AAC1B,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAcO,SAASC,cAAa,YAA+C;AAC1E,SAAO,CAAC,YAAoB,YAC1BA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAeO,IAAM,wBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;AACpB,IAAM,wBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;AACpB,IAAM,wBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;;;AGtP3B,IAAAC,uBAAiC;AACjC,sCAAkC;AAU3B,IAAMC,kBAEK,2DAAiBA,eAAoB;AAChD,IAAMC,eAEK,2DAAiBA,YAAiB;AAC7C,IAAMC,kBAEK,2DAAiBA,eAAoB;AAChD,IAAMC,kBAEK,2DAAiBA,eAAoB;AAChD,IAAMC,mBAEK,2DAAiBA,gBAAqB;AACjD,IAAMC,mBAEK,2DAAiBA,gBAAqB;AACjD,IAAMC,gBAEK,2DAAiBA,aAAkB;AAI9C,IAAMC,yBAET,mDAAkB,iBAAuB;AAItC,IAAMC,yBAET,mDAAkB,iBAAuB;AAItC,IAAMC,yBAET,mDAAkB,iBAAuB;", "names": ["countCategories", "createCategory", "deleteCategory", "getCategory", "moveCategory", "onCategoryCreated", "onCategoryDeleted", "onCategoryUpdated", "queryCategories", "updateCategory", "import_rename_all_nested_keys", "import_timestamp", "import_transform_paths", "import_timestamp", "import_rest_modules", "payload", "import_transform_paths", "SortOrder", "Position", "WebhookIdentityType", "createCategory", "sdkTransformError", "getCategory", "updateCategory", "deleteCategory", "queryCategories", "countCategories", "moveCategory", "createCategory", "getCategory", "updateCategory", "deleteCategory", "queryCategories", "countCategories", "moveCategory", "import_rest_modules", "createCategory", "getCategory", "updateCategory", "deleteCategory", "queryCategories", "countCategories", "moveCategory", "onCategoryCreated", "onCategoryDeleted", "onCategoryUpdated"]}