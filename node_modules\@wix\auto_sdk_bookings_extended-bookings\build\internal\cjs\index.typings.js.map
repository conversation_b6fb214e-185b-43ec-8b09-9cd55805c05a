{"version": 3, "sources": ["../../../index.typings.ts", "../../../src/bookings-reader-v2-extended-booking-extended-bookings.universal.ts", "../../../src/bookings-reader-v2-extended-booking-extended-bookings.http.ts"], "sourcesContent": ["export * from './src/bookings-reader-v2-extended-booking-extended-bookings.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsReaderV2ExtendedBooking from './bookings-reader-v2-extended-booking-extended-bookings.http.js';\n\n/** Extended bookings. */\nexport interface ExtendedBooking {\n  /** Booking. */\n  booking?: Booking;\n  /**\n   * Information about the actions the customer can perform for the\n   * booking. Available only when you've specified `withBookingAllowedActions` as\n   * `true`.\n   */\n  allowedActions?: AllowedActions;\n  /**\n   * Information about the attendance. Available only when passing\n   * `withBookingAttendanceInfo` as `true`.\n   */\n  attendance?: Attendance;\n  /**\n   * Information about the online conferencing details. Available only when passing\n   * `withBookingConferencingDetails` as `true`.\n   */\n  conferencingDetails?: ConferencingDetails;\n  /**\n   * Information about the booking's policy settings according to the relevant\n   * booking policy snapshot.\n   *\n   * Available only when you've specified `withBookingPolicySettings` as `true`.\n   */\n  bookingPolicySettings?: BookingPolicySettings;\n  /**\n   * Information about booking fee\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction))\n   * statuses. Available only if you've specified\n   * `withBookingFeeDetails` as `true`.\n   *\n   * Only details for fees that have already been applied to a booking are\n   * returned. No details are provided for the following scenarios:\n   * - Hypothetical fees related to a potential future cancellation by the customer.\n   * - Scenarios where the booking has been canceled before the start of the earliest cancellation window that includes a penalty.\n   *\n   * To retrieve hypothetical booking fees, call List Booking Fees By Booking Ids\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/list-booking-fees-by-booking-ids) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/list-booking-fees-by-booking-ids))\n   * instead.\n   */\n  bookingFeeDetails?: BookingFeeDetails;\n  /**\n   * Information about the booking order.\n   * Available only when specified `withEcomOrder` as `true`.\n   */\n  order?: Order;\n  /**\n   * Information about the booking waiver.\n   * Available only when specified `withWaiver` as `true`.\n   */\n  waiver?: Waiver;\n  /**\n   * Information about the booking order invoices.\n   * Available only when specified `withEcomInvoices` as `true`.\n   */\n  invoices?: OrderInvoices;\n  /**\n   * Information about the booking order transactions.\n   * Available only when specified `withEcomTransactions` as `true`.\n   */\n  transactions?: OrderTransactions;\n  /**\n   * Information about the booking form submission.\n   * Available only when specified `withFormSubmissions` as `true`.\n   * @maxSize 100\n   */\n  formSubmissions?: FormSubmission[];\n}\n\nexport enum AttendanceStatus {\n  /** There is no available attendance information. */\n  NOT_SET = 'NOT_SET',\n  /** At least a single participant attended the session. */\n  ATTENDED = 'ATTENDED',\n  /** No participants attended the session. */\n  NOT_ATTENDED = 'NOT_ATTENDED',\n}\n\n/** @enumType */\nexport type AttendanceStatusWithLiterals =\n  | AttendanceStatus\n  | 'NOT_SET'\n  | 'ATTENDED'\n  | 'NOT_ATTENDED';\n\n/** An entity representing a scheduled appointment, class session, or course. */\nexport interface Booking extends BookingParticipantsInfoOneOf {\n  /**\n   * Total number of participants.\n   * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the booked service choices and participant count for each choice.\n   * When creating a booking, use this field only if the booking includes multiple service variants\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   *\n   * For example, use this for a spa package booking that includes different service levels:\n   * - 2 participants chose \"Standard Package\".\n   * - 1 participant chose \"VIP Package\".\n   */\n  participantsChoices?: ParticipantChoices;\n  /**\n   * Booking ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * An object describing the bookable entity - either a specific time slot or a recurring schedule.\n   *\n   * The structure depends on the type of service being booked:\n   *\n   * *For appointment services:** Use `slot` to book a specific time slot with a\n   * service provider. Appointments are typically one-time sessions at a specific date and time.\n   *\n   * *For class services:** Use `slot` to book a specific class session. Classes\n   * are individual sessions that can have multiple participants.\n   *\n   * *For course services:** Use `schedule` to book an entire course consisting of\n   * multiple sessions over time. Courses are recurring, multi-session offerings.\n   *\n   * Choose the appropriate field based on your service type and booking requirements.\n   */\n  bookedEntity?: BookedEntity;\n  /**\n   * Contact details of the site visitor or member\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/members/members/introduction) | [REST](https://dev.wix.com/docs/rest/crm/members-contacts/members/members/introduction))\n   * making the booking.\n   */\n  contactDetails?: ContactDetails;\n  /** Additional custom fields submitted with the booking form. */\n  additionalFields?: CustomFormField[];\n  /**\n   * Booking status. A booking is automatically confirmed if the service allows it\n   * and an eCommerce order is created. It is automatically declined if there is a\n   * double booking and the customer hasn't paid or is eligible for an automatic\n   * refund. Currently, only payments with pricing plans are automatically refundable.\n   */\n  status?: BookingStatusWithLiterals;\n  /**\n   * The payment status of the booking. This field automatically syncs with the\n   * `paymentStatus` of the corresponding eCommerce order\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup)| [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))\n   * when customers use Wix eCommerce checkout.\n   *\n   * ## Integration patterns\n   *\n   * *When using Wix eCommerce checkout:** Wix Bookings automatically syncs the payment status based on the eCommerce order's payment status.\n   * Do not manually update this field.\n   *\n   * *When using custom payment flows:** You can manually update the payment status with Confirm Booking or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) to reflect the customer's payment state.\n   *\n   * *For membership/pricing plan payments:** Wix Bookings automatically manages the payment status when customers pay with an active pricing plan ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/introduction)) subscription.\n   *\n   * All payment statuses are supported for every booking `status`.\n   */\n  paymentStatus?: BookingPaymentStatusWithLiterals;\n  /**\n   * Payment option selected by the customer. If the customer hasn't completed their checkout, they may still change the payment method. Must be one of the payment options offered by the service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)), unless `skipSelectedPaymentOptionValidation` is `true`.\n   *\n   * When undefined during an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)), Wix Bookings uses the service's default payment option\n   */\n  selectedPaymentOption?: SelectedPaymentOptionWithLiterals;\n  /**\n   * Date and time the booking was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /** External user ID that you can provide. */\n  externalUserId?: string | null;\n  /**\n   * Revision number to be used when updating, rescheduling, or cancelling the booking.\n   * Increments by 1 each time the booking is updated, rescheduled, or canceled. To prevent conflicting changes, the current revision must be specified when updating the booking.\n   */\n  revision?: string | null;\n  /**\n   * ID of the creator of the booking.\n   * If `appId` and another ID are present, the other ID takes precedence.\n   * @readonly\n   */\n  createdBy?: IdentificationData;\n  /**\n   * The start date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * For a slot, this is the start date of the slot. For a schedule, this is the start date of the first session.\n   * @readonly\n   */\n  startDate?: Date | null;\n  /**\n   * The end date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * For a slot, this is the end date of the slot. For a schedule, this is the end date of the last session.\n   * @readonly\n   */\n  endDate?: Date | null;\n  /**\n   * Date and time the booking was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Custom field data for this object.\n   * Extended fields must be configured in the app dashboard before they can be accessed with API calls.\n   */\n  extendedFields?: ExtendedFields;\n  /**\n   * Whether this booking overlaps with another confirmed booking. Returned\n   * only if set to `true`.\n   * @readonly\n   */\n  doubleBooked?: boolean | null;\n}\n\n/** @oneof */\nexport interface BookingParticipantsInfoOneOf {\n  /**\n   * Total number of participants.\n   * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the booked service choices and participant count for each choice.\n   * When creating a booking, use this field only if the booking includes multiple service variants\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   *\n   * For example, use this for a spa package booking that includes different service levels:\n   * - 2 participants chose \"Standard Package\".\n   * - 1 participant chose \"VIP Package\".\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\n/**\n * A multi-service booking is considered available if all single-service bookings are available as returned from List Multi Service Availability Time Slots.\n * Currently, `SEPARATE_BOOKINGS` and `PARALLEL_BOOKINGS` are not supported.\n * Multi-service booking is available if each of its bookings is available separately.\n * For `SEQUENTIAL_BOOKINGS`, see `List Multi Service Availability Time Slots` documentation.\n */\nexport enum MultiServiceBookingType {\n  /** You must schedule single-service bookings back-to-back with each booking starting when the previous booking ends */\n  SEQUENTIAL_BOOKINGS = 'SEQUENTIAL_BOOKINGS',\n  /** Not currently supported. */\n  SEPARATE_BOOKINGS = 'SEPARATE_BOOKINGS',\n  /** Not currently supported. */\n  PARALLEL_BOOKINGS = 'PARALLEL_BOOKINGS',\n}\n\n/** @enumType */\nexport type MultiServiceBookingTypeWithLiterals =\n  | MultiServiceBookingType\n  | 'SEQUENTIAL_BOOKINGS'\n  | 'SEPARATE_BOOKINGS'\n  | 'PARALLEL_BOOKINGS';\n\nexport interface BookedEntity extends BookedEntityItemOneOf {\n  /**\n   * Booked slot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).\n   *\n   * Specify `slot` when creating bookings for:\n   * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).\n   * Wix Bookings creates a new session when the booking is confirmed.\n   * - **Class services:** Group sessions at specific times (fitness classes, workshops).\n   * Wix Bookings links the booking to an existing scheduled session.\n   *\n   * For course services, specify `schedule` instead of `slot`.\n   */\n  slot?: BookedSlot;\n  /**\n   * Booked schedule\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   *\n   * Specify `schedule` when creating bookings for:\n   * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).\n   * Wix Bookings enrolls participants in all sessions defined by the course schedule.\n   */\n  schedule?: BookedSchedule;\n  /**\n   * Session title at the time of booking. If there is no pre-existing session,\n   * for example for appointment-based services, Wix Bookings sets `title` to the service name.\n   * @readonly\n   * @maxLength 6000\n   */\n  title?: string | null;\n  /**\n   * List of tags for the booking.\n   *\n   * - \"INDIVIDUAL\": For bookings of appointment-based services. Including when the appointment is for a group of participants.\n   * - \"GROUP\": For bookings of individual class sessions.\n   * - \"COURSE\": For course bookings.\n   */\n  tags?: string[] | null;\n}\n\n/** @oneof */\nexport interface BookedEntityItemOneOf {\n  /**\n   * Booked slot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).\n   *\n   * Specify `slot` when creating bookings for:\n   * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).\n   * Wix Bookings creates a new session when the booking is confirmed.\n   * - **Class services:** Group sessions at specific times (fitness classes, workshops).\n   * Wix Bookings links the booking to an existing scheduled session.\n   *\n   * For course services, specify `schedule` instead of `slot`.\n   */\n  slot?: BookedSlot;\n  /**\n   * Booked schedule\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   *\n   * Specify `schedule` when creating bookings for:\n   * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).\n   * Wix Bookings enrolls participants in all sessions defined by the course schedule.\n   */\n  schedule?: BookedSchedule;\n}\n\nexport interface BookedSlot {\n  /** Session ID. */\n  sessionId?: string | null;\n  /** Service ID. */\n  serviceId?: string;\n  /** Schedule ID. */\n  scheduleId?: string;\n  /**\n   * ID of the corresponding event\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).\n   * Available for both appointment and class bookings, not available for course bookings.\n   * For appointment-based services, Wix Bookings automatically populates `eventId` when the booking `status` changes to `CONFIRMED`.\n   * For class bookings, Wix Bookings automatically populates `eventId` upon booking creation.\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n  /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  startDate?: string | null;\n  /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  endDate?: string | null;\n  /** The time zone according to which the slot was shown to the user when booking, and should be shown in the future. */\n  timezone?: string | null;\n  /**\n   * Primary resource\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) for the booking.\n   * For example, the staff member ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) providing the service.\n   */\n  resource?: BookedResource;\n  /** Location where the session takes place. */\n  location?: Location;\n}\n\nexport interface BookedResource {\n  /**\n   * ID of the booking's primary resource.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Resource's name at the time of booking.\n   * @maxLength 40\n   */\n  name?: string | null;\n  /**\n   * Resource's email at the time of booking.\n   * @maxLength 500\n   */\n  email?: string | null;\n  /**\n   * ID of the schedule belonging to the booking's primary resource.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n}\n\nexport interface Location {\n  /**\n   * Business location ID. Available only for locations that are business locations,\n   * meaning the `location_type` is `\"OWNER_BUSINESS\"`.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Location name. */\n  name?: string | null;\n  /** The full address of this location. */\n  formattedAddress?: string | null;\n  /**\n   * The full translated address of this location.\n   * @maxLength 512\n   */\n  formattedAddressTranslated?: string | null;\n  /**\n   * Location type.\n   *\n   * - `\"OWNER_BUSINESS\"`: The business address, as set in the site’s general settings.\n   * - `\"OWNER_CUSTOM\"`: The address as set when creating the service.\n   * - `\"CUSTOM\"`: The address as set for the individual session.\n   */\n  locationType?: LocationTypeWithLiterals;\n}\n\nexport enum LocationType {\n  UNDEFINED = 'UNDEFINED',\n  OWNER_BUSINESS = 'OWNER_BUSINESS',\n  OWNER_CUSTOM = 'OWNER_CUSTOM',\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNDEFINED'\n  | 'OWNER_BUSINESS'\n  | 'OWNER_CUSTOM'\n  | 'CUSTOM';\n\nexport interface BookedSchedule {\n  /** Schedule ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)). */\n  scheduleId?: string;\n  /**\n   * Booked service ID.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * Location ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)) where the schedule's sessions take place.\n   * @readonly\n   */\n  location?: Location;\n  /**\n   * Time zone in which the slot or session was shown to the customer when they booked.\n   * Also used whenever the customer reviews the booking's timing in the future.\n   */\n  timezone?: string | null;\n  /**\n   * Start time of the first session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.\n   * @readonly\n   */\n  firstSessionStart?: string | null;\n  /**\n   * End time of the last session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.\n   * @readonly\n   */\n  lastSessionEnd?: string | null;\n}\n\nexport interface ContactDetails {\n  /**\n   * Contact ID.\n   * @format GUID\n   */\n  contactId?: string | null;\n  /**\n   * Contact's first name. When populated from a standard booking form, this\n   * property corresponds to the `name` field.\n   */\n  firstName?: string | null;\n  /** Contact's last name. */\n  lastName?: string | null;\n  /**\n   * Contact's email. If no [contact](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/contacts/contact-v4/contact-object)\n   * with this email exist, a new contact is created.\n   * Used to validate coupon usage limitations per contact. If not specified,\n   * the coupon usage limitation will not be enforced. (Coupon usage limitation\n   * validation is not supported yet).\n   * @format EMAIL\n   */\n  email?: string | null;\n  /** Contact's phone number. */\n  phone?: string | null;\n  /** Contact's full address. */\n  fullAddress?: Address;\n  /**\n   * Contact's time zone.\n   * @deprecated\n   */\n  timeZone?: string | null;\n  /**\n   * Contact's country in [ISO 3166-1 alpha-2 code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)\n   * format.\n   * @format COUNTRY\n   */\n  countryCode?: string | null;\n}\n\n/** Physical address */\nexport interface Address extends AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n  /**\n   * Country code.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  subdivision?: string | null;\n  /** City name. */\n  city?: string | null;\n  /** Zip/postal code. */\n  postalCode?: string | null;\n  /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */\n  addressLine2?: string | null;\n  /** A string containing the full address of this location. */\n  formattedAddress?: string | null;\n  /** Free text to help find the address. */\n  hint?: string | null;\n  /** Coordinates of the physical address. */\n  geocode?: AddressLocation;\n  /** Country full name. */\n  countryFullname?: string | null;\n  /** Multi-level subdivisions from top to bottom. */\n  subdivisions?: Subdivision[];\n}\n\n/** @oneof */\nexport interface AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n}\n\nexport interface StreetAddress {\n  /** Street number. */\n  number?: string;\n  /** Street name. */\n  name?: string;\n  /** Apartment number. */\n  apt?: string;\n}\n\nexport interface AddressLocation {\n  /** Address latitude. */\n  latitude?: number | null;\n  /** Address longitude. */\n  longitude?: number | null;\n}\n\nexport interface Subdivision {\n  /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  code?: string;\n  /** Subdivision full name. */\n  name?: string;\n}\n\nexport interface CustomFormField {\n  /**\n   * ID of the form field as defined in the form.\n   * @format GUID\n   */\n  _id?: string;\n  /** Value that was submitted for this field. */\n  value?: string | null;\n  /**\n   * Form field's label at the time of submission.\n   * @readonly\n   */\n  label?: string | null;\n  valueType?: ValueTypeWithLiterals;\n}\n\nexport enum ValueType {\n  /** Short text. This is the default value type. */\n  SHORT_TEXT = 'SHORT_TEXT',\n  /** Long text. */\n  LONG_TEXT = 'LONG_TEXT',\n  /** A text that represents the check box value. If selected the value is `true`, otherwise `false`. */\n  CHECK_BOX = 'CHECK_BOX',\n}\n\n/** @enumType */\nexport type ValueTypeWithLiterals =\n  | ValueType\n  | 'SHORT_TEXT'\n  | 'LONG_TEXT'\n  | 'CHECK_BOX';\n\n/** Booking status. */\nexport enum BookingStatus {\n  /** The booking was created, but the customer hasn't completed the related eCommerce order yet. */\n  CREATED = 'CREATED',\n  /**\n   * The merchant has confirmed the booking and it appears in the business calendar. Merchants can set up their services\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) to automatically confirm all `PENDING` bookings.\n   */\n  CONFIRMED = 'CONFIRMED',\n  /**\n   * The customer has canceled the booking. Depending on the relevant service's policy snapshot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))\n   * they may have to pay a cancellation fee ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction)).\n   */\n  CANCELED = 'CANCELED',\n  /** The merchant must manually confirm the booking before it appears in the business calendar. */\n  PENDING = 'PENDING',\n  /** The merchant has declined the booking before the customer was charged. */\n  DECLINED = 'DECLINED',\n  /**\n   * The booking is on a waitlist.\n   * Currently, you can't call Register to Waitlist ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/waitlist/register-to-waitlist)) for course or appointment bookings, even though this is supported in live sites.\n   * You can call Register to Waitlist only for class session bookings.\n   */\n  WAITING_LIST = 'WAITING_LIST',\n}\n\n/** @enumType */\nexport type BookingStatusWithLiterals =\n  | BookingStatus\n  | 'CREATED'\n  | 'CONFIRMED'\n  | 'CANCELED'\n  | 'PENDING'\n  | 'DECLINED'\n  | 'WAITING_LIST';\n\n/**\n * Payment status.\n * Automatically updated when using eCommerce checkout APIs.\n */\nexport enum BookingPaymentStatus {\n  /** Undefined payment status. */\n  UNDEFINED = 'UNDEFINED',\n  /** The booking isn't paid. */\n  NOT_PAID = 'NOT_PAID',\n  /** The booking is fully paid. */\n  PAID = 'PAID',\n  /** The booking is partially paid. */\n  PARTIALLY_PAID = 'PARTIALLY_PAID',\n  /** The booking is refunded. */\n  REFUNDED = 'REFUNDED',\n  /** The booking is free of charge. */\n  EXEMPT = 'EXEMPT',\n}\n\n/** @enumType */\nexport type BookingPaymentStatusWithLiterals =\n  | BookingPaymentStatus\n  | 'UNDEFINED'\n  | 'NOT_PAID'\n  | 'PAID'\n  | 'PARTIALLY_PAID'\n  | 'REFUNDED'\n  | 'EXEMPT';\n\n/**\n * Selected payment option.\n *\n * One of the payment options offered by the service.\n * This field is set when the user selects an option during booking.\n * If left undefined, the payment option is resolved by the service configuration on checkout.\n */\nexport enum SelectedPaymentOption {\n  /** Undefined payment option. */\n  UNDEFINED = 'UNDEFINED',\n  /** Offline payment. */\n  OFFLINE = 'OFFLINE',\n  /** Online payment. */\n  ONLINE = 'ONLINE',\n  /** Payment using a Wix Pricing Plan. */\n  MEMBERSHIP = 'MEMBERSHIP',\n  /**\n   * Customers can pay only in person with a Wix Pricing Plan, while the Wix user\n   * must manually redeem the pricing plan in the dashboard.\n   */\n  MEMBERSHIP_OFFLINE = 'MEMBERSHIP_OFFLINE',\n}\n\n/** @enumType */\nexport type SelectedPaymentOptionWithLiterals =\n  | SelectedPaymentOption\n  | 'UNDEFINED'\n  | 'OFFLINE'\n  | 'ONLINE'\n  | 'MEMBERSHIP'\n  | 'MEMBERSHIP_OFFLINE';\n\nexport interface BookingSource {\n  /** Platform from which a booking was created. */\n  platform?: PlatformWithLiterals;\n  /** Actor that created this booking. */\n  actor?: ActorWithLiterals;\n  /**\n   * Wix site ID of the application that created the booking.\n   * @format GUID\n   * @readonly\n   */\n  appDefId?: string | null;\n  /**\n   * Name of the application that created the booking, as saved in Wix Developers Center at the time of booking.\n   * @readonly\n   */\n  appName?: string | null;\n}\n\nexport enum Platform {\n  UNDEFINED_PLATFORM = 'UNDEFINED_PLATFORM',\n  WEB = 'WEB',\n  MOBILE_APP = 'MOBILE_APP',\n}\n\n/** @enumType */\nexport type PlatformWithLiterals =\n  | Platform\n  | 'UNDEFINED_PLATFORM'\n  | 'WEB'\n  | 'MOBILE_APP';\n\nexport enum Actor {\n  UNDEFINED_ACTOR = 'UNDEFINED_ACTOR',\n  BUSINESS = 'BUSINESS',\n  CUSTOMER = 'CUSTOMER',\n}\n\n/** @enumType */\nexport type ActorWithLiterals =\n  | Actor\n  | 'UNDEFINED_ACTOR'\n  | 'BUSINESS'\n  | 'CUSTOMER';\n\nexport interface ParticipantNotification {\n  /**\n   * Whether to send a message about the changes to the customer.\n   *\n   * Default: `false`\n   */\n  notifyParticipants?: boolean;\n  /** Custom message to send to the participants about the changes to the booking. */\n  message?: string | null;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /**\n   * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.\n   * @format GUID\n   */\n  contactId?: string | null;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum IdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type IdentityTypeWithLiterals =\n  | IdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\n/**\n * Settings that control booking flow behavior and override default business rules.\n *\n * These settings allow administrators to bypass standard validation checks\n * and policies when creating, confirming, rescheduling, or canceling bookings.\n * Most settings require elevated permissions to use.\n *\n * Use flow control settings to handle special scenarios like:\n * - Emergency bookings outside normal business hours\n * - Admin-initiated bookings that bypass availability checks\n * - Custom payment flows that don't use standard eCommerce checkout\n * - Overriding cancellation or rescheduling policies in exceptional cases\n */\nexport interface FlowControlSettings {\n  /** Whether availability is checked when creating or confirming the booking. */\n  skipAvailabilityValidation?: boolean;\n  /**\n   * Whether the booking's `status` is automatically updated to `CONFIRMED` when\n   * the customer completes the eCommerce checkout\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),\n   * regardless of whether the relevant service requires manual business confirmation.\n   */\n  skipBusinessConfirmation?: boolean;\n  /**\n   * Whether the customer is allowed to pay with a payment method that isn't\n   * supported for the relevant service.\n   */\n  skipSelectedPaymentOptionValidation?: boolean;\n  /**\n   * Whether the customer receives an automatic refund if there's a double booking\n   * conflict. Only available if the customer has paid with a\n   * pricing plan.\n   */\n  withRefund?: boolean | null;\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\nexport interface ParticipantChoices {\n  /**\n   * Information about the booked service choices. Includes the number of participants.\n   * @minSize 1\n   * @maxSize 20\n   */\n  serviceChoices?: ServiceChoices[];\n}\n\nexport interface ServiceChoices {\n  /**\n   * Number of participants for this variant ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  numberOfParticipants?: number | null;\n  /**\n   * Service choices for these participants.\n   * @maxSize 5\n   */\n  choices?: ServiceChoice[];\n}\n\nexport interface ServiceChoice extends ServiceChoiceChoiceOneOf {\n  /**\n   * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.\n   * Choices are specific values for an option the customer can choose to book. For example,\n   * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.\n   * Each choice may have a different price.\n   */\n  custom?: string;\n  duration?: Duration;\n  /**\n   * ID of the corresponding option for the choice. For example, the choice `child`\n   * could correspond to the option `ageGroup`. In this case, `optionId` is the ID\n   * for the `ageGroup` option.\n   * @format GUID\n   */\n  optionId?: string;\n}\n\n/** @oneof */\nexport interface ServiceChoiceChoiceOneOf {\n  /**\n   * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.\n   * Choices are specific values for an option the customer can choose to book. For example,\n   * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.\n   * Each choice may have a different price.\n   */\n  custom?: string;\n  duration?: Duration;\n}\n\nexport interface Duration {\n  /**\n   * Duration of the service in minutes.\n   * Min: 1 minute, Max: 30 days, 23 hours, and 59 minutes\n   * @min 1\n   * @max 44639\n   */\n  minutes?: number;\n  /**\n   * Name of the duration option.\n   * Defaults to the formatted duration e.g. \"1 hour, 30 minutes\".\n   * @maxLength 255\n   */\n  name?: string | null;\n}\n\nexport interface MultiServiceBookingInfo {\n  /**\n   * Multi-service booking ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /** Type of the multi-service booking. */\n  type?: MultiServiceBookingTypeWithLiterals;\n}\n\nexport interface BookedAddOn {\n  /**\n   * The ID of the add-on.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * The ID of the add-on group.\n   * @format GUID\n   */\n  groupId?: string;\n  /**\n   * The add-on duration in minutes at the time of booking.\n   * @min 1\n   * @max 1440\n   * @readonly\n   */\n  durationInMinutes?: number | null;\n  /**\n   * The quantity of booked add-ons.\n   * @min 1\n   * @max 1000\n   */\n  quantity?: number | null;\n  /**\n   * Add-on `name` at the time of booking.\n   * @maxLength 100\n   * @readonly\n   */\n  name?: string | null;\n  /**\n   * Add-on name translated to the language the customer used during booking.\n   * @maxLength 100\n   * @readonly\n   */\n  nameTranslated?: string | null;\n}\n\n/** Possible allowed actions for a Booking */\nexport interface AllowedActions {\n  /** Whether the customer is allowed to cancel the booking. */\n  cancel?: boolean;\n  /** Whether the customer is allowed to reschedule the booking. */\n  reschedule?: boolean;\n  /**\n   * Deprecated.\n   * Whether the customer is entitled to a refund when canceling the booking.\n   * @deprecated\n   */\n  refund?: boolean | null;\n}\n\nexport interface Attendance {\n  /**\n   * ID of the attendance object.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /** General information about the booking's attendance. */\n  status?: AttendanceStatusWithLiterals;\n  /**\n   * Total number of participants who attended the session. Can\n   * be greater than `1` for bookings with multiple participants.\n   */\n  numberOfAttendees?: number;\n}\n\nexport interface ConferencingDetails {\n  /**\n   * URL used by a guest to join the conference.\n   * @minLength 1\n   * @maxLength 2000\n   */\n  guestUrl?: string | null;\n  /**\n   * Conference password.\n   * @minLength 1\n   * @maxLength 100\n   */\n  password?: string | null;\n}\n\nexport interface BookingPolicySettings {\n  /** Whether the booking has an active cancellation fee policy. */\n  cancellationFeeEnabled?: boolean | null;\n}\n\nexport interface BookingFeeDetails {\n  /** Cancellation fee status. */\n  cancellationFeeStatus?: BookingFeeStatusWithLiterals;\n}\n\nexport enum BookingFeeStatus {\n  /** There is no information about the booking fee status. */\n  UNKNOWN_STATUS = 'UNKNOWN_STATUS',\n  /** The booking fee hasn't been applied to the eCommerce order yet. */\n  NOT_YET_APPLIED_TO_ORDER = 'NOT_YET_APPLIED_TO_ORDER',\n  /** The booking fee has already been applied as an additional fee to the eCommerce order. */\n  APPLIED_TO_ORDER = 'APPLIED_TO_ORDER',\n}\n\n/** @enumType */\nexport type BookingFeeStatusWithLiterals =\n  | BookingFeeStatus\n  | 'UNKNOWN_STATUS'\n  | 'NOT_YET_APPLIED_TO_ORDER'\n  | 'APPLIED_TO_ORDER';\n\nexport interface Order {\n  /**\n   * Order ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Order number displayed in the site owner's dashboard (auto-generated).\n   * @readonly\n   */\n  number?: string;\n  /**\n   * Order price summary.\n   * @readonly\n   */\n  priceSummary?: PriceSummary;\n  /**\n   * Order line items.\n   * @minSize 1\n   * @maxSize 300\n   * @readonly\n   */\n  lineItems?: OrderLineItem[];\n  /** Order status. */\n  status?: OrderStatusWithLiterals;\n  /** Order payment status. */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /**\n   * Balance summary.\n   * @readonly\n   */\n  balanceSummary?: BalanceSummary;\n  /**\n   * Currency used for the pricing of this order in [ISO-4217](https://en.wikipedia.org/wiki/ISO_4217#List_of_ISO_4217_currency_codes) format.\n   * @format CURRENCY\n   */\n  currency?: string | null;\n}\n\nexport interface PriceSummary {\n  /** Subtotal of all the line items, before discounts and before tax. */\n  subtotal?: Price;\n  /** Total shipping price, before discounts and before tax. */\n  shipping?: Price;\n  /** Total tax on this order. */\n  tax?: Price;\n  /** Total calculated discount value. */\n  discount?: Price;\n  /** Order’s total price after discounts and tax. */\n  total?: Price;\n  /** Total price of additional fees before tax. */\n  totalAdditionalFees?: Price;\n}\n\nexport interface Price {\n  /**\n   * Amount.\n   * @decimalValue options { gte:0, lte:1000000000000000 }\n   */\n  amount?: string;\n  /**\n   * Amount formatted with currency symbol.\n   * @readonly\n   */\n  formattedAmount?: string;\n}\n\nexport interface OrderLineItem {\n  /**\n   * Line item ID.\n   * @immutable\n   */\n  _id?: string;\n  /**\n   * References to the line item's origin catalog.\n   * This field may be empty in the case of a custom line item.\n   */\n  catalogReference?: CatalogReference;\n  /** Line item price after line item discounts for display purposes. */\n  price?: Price;\n  /**\n   * Type of selected payment option for current item.\n   *\n   * Default: `FULL_PAYMENT_ONLINE`\n   */\n  paymentOption?: PaymentOptionTypeWithLiterals;\n  /** Additional description for the price. For example, when price is 0 but additional details about the actual price are needed - \"Starts at $67\". */\n  priceDescription?: PriceDescription;\n}\n\n/** Used for grouping line items. Sent when an item is added to a cart, checkout, or order. */\nexport interface CatalogReference {\n  /**\n   * ID of the item within the catalog it belongs to.\n   * @minLength 1\n   * @maxLength 36\n   */\n  catalogItemId?: string;\n  /**\n   * ID of the app providing the catalog.\n   *\n   * You can get your app's ID from its page in the [app dashboard](https://dev.wix.com/dc3/my-apps/).\n   *\n   * For items from Wix catalogs, the following values always apply:\n   * + Wix Stores: `\"215238eb-22a5-4c36-9e7b-e7c08025e04e\"`\n   * + Wix Bookings: `\"13d21c63-b5ec-5912-8397-c3a5ddb27a97\"`\n   * + Wix Restaurants: `\"9a5d83fd-8570-482e-81ab-cfa88942ee60\"`\n   * @minLength 1\n   */\n  appId?: string;\n  /**\n   * Additional item details in key:value pairs. Use this optional field to provide more specificity with item selection. The `options` field values differ depending on which catalog is providing the items.\n   *\n   * For products and variants from a Wix Stores catalog, learn more about eCommerce integration ([SDK](https://dev.wix.com/docs/sdk/backend-modules/stores/catalog-v3/e-commerce-integration) | [REST](https://dev.wix.com/docs/rest/business-solutions/stores/catalog/e-commerce-integration)).\n   */\n  options?: Record<string, any> | null;\n}\n\n/** Type of selected payment option for catalog item */\nexport enum PaymentOptionType {\n  /** The entire payment for this item happens as part of the checkout. */\n  FULL_PAYMENT_ONLINE = 'FULL_PAYMENT_ONLINE',\n  /** The entire payment for this item happens after checkout. For example, when using cash, check, or other offline payment methods. */\n  FULL_PAYMENT_OFFLINE = 'FULL_PAYMENT_OFFLINE',\n  /** Payment for this item is done by charging a membership. Any remaining amount not covered by the membership, such as item modifiers, is paid online. */\n  MEMBERSHIP = 'MEMBERSHIP',\n  /** Partial payment to be paid upfront during checkout. The initial amount to be paid for each line item is specified in `depositAmount`. */\n  DEPOSIT_ONLINE = 'DEPOSIT_ONLINE',\n  /** Payment for this item can only be done by charging a membership and must be manually redeemed in the dashboard by the site admin. When selected, `price` is `0`. */\n  MEMBERSHIP_OFFLINE = 'MEMBERSHIP_OFFLINE',\n  /**\n   * Item price is charged to online membership. Any remaining amount not covered by the membership, such as item modifiers, is paid offline.\n   * @documentationMaturity preview\n   */\n  MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER = 'MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER',\n}\n\n/** @enumType */\nexport type PaymentOptionTypeWithLiterals =\n  | PaymentOptionType\n  | 'FULL_PAYMENT_ONLINE'\n  | 'FULL_PAYMENT_OFFLINE'\n  | 'MEMBERSHIP'\n  | 'DEPOSIT_ONLINE'\n  | 'MEMBERSHIP_OFFLINE'\n  | 'MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER';\n\nexport interface PriceDescription {\n  /**\n   * __Required.__ Price description in the site's default language as defined in the [request envelope](https://dev.wix.com/docs/build-apps/develop-your-app/frameworks/self-hosting/supported-extensions/backend-extensions/add-self-hosted-service-plugin-extensions#request-envelope).\n   * @minLength 1\n   * @maxLength 100\n   */\n  original?: string;\n  /**\n   * Price description translated into the buyer's language.\n   *\n   * Default: Same as `original`.\n   * @minLength 1\n   * @maxLength 100\n   */\n  translated?: string | null;\n}\n\nexport enum OrderStatus {\n  /** Order created, but not yet approved or canceled. */\n  INITIALIZED = 'INITIALIZED',\n  /**\n   * Order approved.\n   *\n   * This happens when either an online payment is received **or** when `order.priceSummary.total = 0` (a zero-total order).\n   * Offline orders (cash payment) are automatically approved.\n   */\n  APPROVED = 'APPROVED',\n  /** Order canceled by the user. */\n  CANCELED = 'CANCELED',\n  /**\n   * Order pending.\n   * @documentationMaturity preview\n   */\n  PENDING = 'PENDING',\n  /**\n   * Order rejected.\n   *\n   * This happens when pending payments fail.\n   * @documentationMaturity preview\n   */\n  REJECTED = 'REJECTED',\n}\n\n/** @enumType */\nexport type OrderStatusWithLiterals =\n  | OrderStatus\n  | 'INITIALIZED'\n  | 'APPROVED'\n  | 'CANCELED'\n  | 'PENDING'\n  | 'REJECTED';\n\nexport enum PaymentStatus {\n  UNSPECIFIED = 'UNSPECIFIED',\n  /**\n   * `NOT_PAID` can apply to an order made online, but not yet paid. In such cases `order.status` will be `INITIALIZED`.\n   * This status also applies when an offline order needs to be manually marked as paid. In such cases `order.status` will be `APPROVED`.\n   */\n  NOT_PAID = 'NOT_PAID',\n  /** All required payments associated with this order are paid. */\n  PAID = 'PAID',\n  /** Order partially refunded, but the refunded amount is less than the order's total price. See `order.balanceSummary` for more details. */\n  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',\n  /** Order fully refunded. Refund amount equals total price. See `order.balanceSummary` for more details. */\n  FULLY_REFUNDED = 'FULLY_REFUNDED',\n  /**\n   * All payments pending.\n   *\n   * This can happen with two-step payments, when a payment requires manual review, or when a payment is in progress and will be concluded shortly.\n   * Learn more about [pending orders](https://support.wix.com/en/article/pending-orders).\n   */\n  PENDING = 'PENDING',\n  /** At least one payment received and approved, but it covers less than the order's total price. See `order.balanceSummary` for more details. */\n  PARTIALLY_PAID = 'PARTIALLY_PAID',\n  /**\n   * Payment received, but not yet confirmed by the payment provider.\n   *\n   * In most cases, when a payment provider is holding payment it's because setup hasn't been successfully completed by the merchant/site owner.\n   * To solve this, the merchant/site owner should log in to the payment provider's dashboard and make sure their account is set up correctly, or contact their support for further assistance.\n   * @documentationMaturity preview\n   */\n  PENDING_MERCHANT = 'PENDING_MERCHANT',\n  /**\n   * One or more payments canceled.\n   * @documentationMaturity preview\n   */\n  CANCELED = 'CANCELED',\n  /**\n   * One or more payments declined.\n   * @documentationMaturity preview\n   */\n  DECLINED = 'DECLINED',\n}\n\n/** @enumType */\nexport type PaymentStatusWithLiterals =\n  | PaymentStatus\n  | 'UNSPECIFIED'\n  | 'NOT_PAID'\n  | 'PAID'\n  | 'PARTIALLY_REFUNDED'\n  | 'FULLY_REFUNDED'\n  | 'PENDING'\n  | 'PARTIALLY_PAID'\n  | 'PENDING_MERCHANT'\n  | 'CANCELED'\n  | 'DECLINED';\n\nexport interface BalanceSummary {\n  /**\n   * Current amount left to pay.\n   * @readonly\n   */\n  balance?: Balance;\n  /**\n   * The value includes payments that have subsequently been fully or partially refunded.\n   * @readonly\n   */\n  paid?: Price;\n}\n\n/**\n * Order balance. Reflects amount left to be paid on order and is calculated dynamically. Can be negative per balance definition.\n * `amount` field depends on order payment status:\n * + UNSPECIFIED, NOT_PAID: price_summary.total_price\n * + PARTIALLY_PAID : price_summary.total_price - pay_now.total_price\n * + PENDING, REFUNDED, PARTIALLY_REFUNDED, PAID : 0\n */\nexport interface Balance {\n  /**\n   * Balance amount.\n   *\n   * A negative `amount` represents the amount to be refunded. This can happen due to overcharging or the order being modified after a payment has been made.\n   * @decimalValue options { }\n   * @readonly\n   */\n  amount?: string;\n  /**\n   * Amount formatted with currency symbol.\n   * @readonly\n   */\n  formattedAmount?: string;\n}\n\nexport interface Waiver {\n  /**\n   * The ID of the waiver. This is a unique identifier for the waiver.\n   * @format GUID\n   */\n  _id?: string | null;\n  /**\n   * The ID of the waiver template.\n   * @format GUID\n   */\n  waiverTemplateId?: string | null;\n  /**\n   * The ID of the contact associated with the waiver.\n   * @format GUID\n   */\n  contactId?: string | null;\n  /** The status of the waiver. */\n  status?: StatusWithLiterals;\n}\n\nexport enum Status {\n  UNDEFINED = 'UNDEFINED',\n  SIGNED = 'SIGNED',\n  NOT_SIGNED = 'NOT_SIGNED',\n}\n\n/** @enumType */\nexport type StatusWithLiterals = Status | 'UNDEFINED' | 'SIGNED' | 'NOT_SIGNED';\n\nexport interface OrderInvoices {\n  /** Order ID. */\n  orderId?: string;\n  /**\n   * Invoices info.\n   * @maxSize 300\n   */\n  invoicesInfo?: Invoice[];\n}\n\nexport interface Invoice {\n  /** Invoice ID. */\n  _id?: string;\n  /** ID of the app that set the invoice. */\n  appId?: string;\n}\n\nexport interface OrderTransactions {\n  /**\n   * Order ID.\n   * @format GUID\n   */\n  orderId?: string;\n  /**\n   * Record of payments made to the merchant.\n   * @maxSize 100\n   */\n  payments?: Payment[];\n  /**\n   * Record of refunds made to the buyer.\n   * @maxSize 300\n   */\n  refunds?: Refund[];\n}\n\nexport interface Payment\n  extends PaymentPaymentDetailsOneOf,\n    PaymentReceiptInfoOneOf {\n  /** Regular payment details. */\n  regularPaymentDetails?: RegularPaymentDetails;\n  /** Gift card payment details. */\n  giftcardPaymentDetails?: GiftCardPaymentDetails;\n  /**\n   * Receipt created by Wix\n   * @internal\n   */\n  wixReceipt?: WixReceiptInfo;\n  /**\n   * Receipt created by an external system.\n   * @internal\n   */\n  externalReceipt?: ExternalReceiptInfo;\n  /**\n   * Payment ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /** Date and time the payment was created in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided. */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the payment was last updated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /** Payment amount. */\n  amount?: Price;\n  /**\n   * Whether refunds for this payment are disabled.\n   * + `true`: This payment is not refundable.\n   * + `false`: This payment may be refunded. However, this ultimately depends on the payment provider.\n   */\n  refundDisabled?: boolean;\n  /**\n   * Whether receipt generation for this payment are allowed.\n   * + `true`: This payment supports receipt generation.\n   * + `false`: This payment not supports receipt generation, or receipt already generated\n   * @internal\n   * @readonly\n   */\n  supportReceiptGeneration?: boolean;\n}\n\n/** @oneof */\nexport interface PaymentPaymentDetailsOneOf {\n  /** Regular payment details. */\n  regularPaymentDetails?: RegularPaymentDetails;\n  /** Gift card payment details. */\n  giftcardPaymentDetails?: GiftCardPaymentDetails;\n}\n\n/** @oneof */\nexport interface PaymentReceiptInfoOneOf {\n  /**\n   * Receipt created by Wix\n   * @internal\n   */\n  wixReceipt?: WixReceiptInfo;\n  /**\n   * Receipt created by an external system.\n   * @internal\n   */\n  externalReceipt?: ExternalReceiptInfo;\n}\n\nexport interface RegularPaymentDetails\n  extends RegularPaymentDetailsPaymentMethodDetailsOneOf {\n  /** Whether regular card used */\n  creditCardDetails?: CreditCardPaymentMethodDetails;\n  /**\n   * Wix Payments order ID.\n   * @maxLength 100\n   */\n  paymentOrderId?: string | null;\n  /**\n   * Payment gateway's transaction ID.\n   * This field is only returned when the value of `offline_payment` is `false`.\n   * @maxLength 100\n   */\n  gatewayTransactionId?: string | null;\n  /**\n   * Payment method. Non-exhaustive list of supported values:\n   * + `CreditCard`, `Alipay`, `AstropayCash`, `AstropayDBT`, `AstropayMBT`, `Bitcoin`, `BitPay`, `Cash`, `ConvenienceStore`, `EPay`, `Fake`, `Giropay`, `IDeal`, `InPerson`, `Klarna`, `MercadoPago`, `Netpay`, `NordeaSolo`, `Offline`, `PagSeguro`, `PayEasy`, `PayPal`, `Paysafecard`, `Paysafecash`, `PointOfSale`, `Poli`, `Privat24`, `Przelewy24`, `RapidTransfer`, `Sepa`, `Skrill`, `Sofort`, `Trustly`, `Neteller`, `Unionpay`, `UniPay`, `Yandex`\n   * @maxLength 100\n   */\n  paymentMethod?: string | null;\n  /**\n   * Transaction ID in the payment provider's system. For example, at PayPal, Square, Stripe, etc. Not returned for offline payments.\n   * @maxLength 100\n   */\n  providerTransactionId?: string | null;\n  /** Whether the payment was made offline. For example, when using cash or when marked as paid in the Business Manager. */\n  offlinePayment?: boolean;\n  /** Payment status. */\n  status?: TransactionStatusWithLiterals;\n  /**\n   * Whether there is a payment agreement that allows for future charges.\n   * @immutable\n   */\n  savedPaymentMethod?: boolean;\n  /** Authorization details. */\n  authorizationDetails?: AuthorizationDetails;\n  /**\n   * Payment service provider display name. Non-exhaustive list of supported values: Wix Payments, PayPal, Square, Stripe, etc.\n   * @internal\n   * @maxLength 300\n   */\n  paymentProvider?: string | null;\n  /**\n   * Record of chargebacks made by the buyer.\n   * @maxSize 6\n   */\n  chargebacks?: Chargeback[];\n}\n\n/** @oneof */\nexport interface RegularPaymentDetailsPaymentMethodDetailsOneOf {\n  /** Whether regular card used */\n  creditCardDetails?: CreditCardPaymentMethodDetails;\n}\n\nexport enum TransactionStatus {\n  UNDEFINED = 'UNDEFINED',\n  APPROVED = 'APPROVED',\n  PENDING = 'PENDING',\n  PENDING_MERCHANT = 'PENDING_MERCHANT',\n  CANCELED = 'CANCELED',\n  DECLINED = 'DECLINED',\n  REFUNDED = 'REFUNDED',\n  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',\n  AUTHORIZED = 'AUTHORIZED',\n  VOIDED = 'VOIDED',\n}\n\n/** @enumType */\nexport type TransactionStatusWithLiterals =\n  | TransactionStatus\n  | 'UNDEFINED'\n  | 'APPROVED'\n  | 'PENDING'\n  | 'PENDING_MERCHANT'\n  | 'CANCELED'\n  | 'DECLINED'\n  | 'REFUNDED'\n  | 'PARTIALLY_REFUNDED'\n  | 'AUTHORIZED'\n  | 'VOIDED';\n\nexport interface CreditCardPaymentMethodDetails {\n  /**\n   * The last 4 digits of the card number.\n   * @maxLength 4\n   */\n  lastFourDigits?: string | null;\n  /**\n   * Card issuer's brand.\n   * @maxLength 100\n   */\n  brand?: string | null;\n}\n\nexport interface AuthorizationDetails {\n  /**\n   * Whether the authorized payment is of a delayed capture.\n   * @readonly\n   */\n  delayedCapture?: boolean;\n  /** Date and time the payment was authorized in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. */\n  authorizedDate?: Date | null;\n  /**\n   * List of captures associated with payment\n   * In case of failed it can be replaced with new one with PENDING or SUCCESS statuses\n   * @maxSize 1\n   */\n  captures?: AuthorizationCapture[];\n  /** Void associated with payment */\n  void?: AuthorizationVoid;\n  /** Scheduled action for this transaction */\n  scheduledAction?: ScheduledAction;\n}\n\nexport interface AuthorizationCapture {\n  /**\n   * Capture ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /** Status of this capture action */\n  status?: AuthorizationCaptureStatusWithLiterals;\n  /**\n   * Amount of this capture\n   * @immutable\n   */\n  amount?: Price;\n  /** Date and time the capture was initiated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. */\n  _createdDate?: Date | null;\n  /** In case of status is FAILED may contain failure details */\n  failureDetails?: AuthorizationActionFailureDetails;\n}\n\nexport enum AuthorizationCaptureStatus {\n  UNKNOWN_STATUS = 'UNKNOWN_STATUS',\n  /** Capture operation still in progress. */\n  PENDING = 'PENDING',\n  /** Capture operation succeeded. */\n  SUCCEEDED = 'SUCCEEDED',\n  /** Capture operation failed. */\n  FAILED = 'FAILED',\n}\n\n/** @enumType */\nexport type AuthorizationCaptureStatusWithLiterals =\n  | AuthorizationCaptureStatus\n  | 'UNKNOWN_STATUS'\n  | 'PENDING'\n  | 'SUCCEEDED'\n  | 'FAILED';\n\nexport interface AuthorizationActionFailureDetails {\n  /** @maxLength 100 */\n  failureCode?: string;\n}\n\nexport interface AuthorizationVoid {\n  /** Status of this void action */\n  status?: AuthorizationVoidStatusWithLiterals;\n  /** Date and time the void was initiated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. */\n  voidedDate?: Date | null;\n  /** In case of status is FAILED may contain failure details */\n  failureDetails?: AuthorizationActionFailureDetails;\n  /** Reason of void action */\n  reason?: ReasonWithLiterals;\n}\n\nexport enum AuthorizationVoidStatus {\n  UNKNOWN_STATUS = 'UNKNOWN_STATUS',\n  /** Void operation still in progress. */\n  PENDING = 'PENDING',\n  /** Void operation succeeded. */\n  SUCCEEDED = 'SUCCEEDED',\n  /** Void operation failed. */\n  FAILED = 'FAILED',\n}\n\n/** @enumType */\nexport type AuthorizationVoidStatusWithLiterals =\n  | AuthorizationVoidStatus\n  | 'UNKNOWN_STATUS'\n  | 'PENDING'\n  | 'SUCCEEDED'\n  | 'FAILED';\n\n/** Reason the authorization was voided. */\nexport enum Reason {\n  UNKNOWN_REASON = 'UNKNOWN_REASON',\n  /** Authorization was voided by user. */\n  MANUAL = 'MANUAL',\n  /** Authorization passed execution date. */\n  SCHEDULED = 'SCHEDULED',\n}\n\n/** @enumType */\nexport type ReasonWithLiterals =\n  | Reason\n  | 'UNKNOWN_REASON'\n  | 'MANUAL'\n  | 'SCHEDULED';\n\nexport interface ScheduledAction {\n  /** Type of the action. */\n  actionType?: ActionTypeWithLiterals;\n  /** The date and time of the action. */\n  executionDate?: Date | null;\n}\n\nexport enum ActionType {\n  UNKNOWN_ACTION_TYPE = 'UNKNOWN_ACTION_TYPE',\n  VOID = 'VOID',\n  CAPTURE = 'CAPTURE',\n}\n\n/** @enumType */\nexport type ActionTypeWithLiterals =\n  | ActionType\n  | 'UNKNOWN_ACTION_TYPE'\n  | 'VOID'\n  | 'CAPTURE';\n\nexport interface Chargeback {\n  /**\n   * Chargeback ID.\n   * @format GUID\n   * @readonly\n   * @immutable\n   */\n  _id?: string;\n  /**\n   * Date and time the chargeback was created in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided.\n   * @readonly\n   * @immutable\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the chargeback was updated in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Amount.\n   * @readonly\n   * @immutable\n   */\n  amount?: Price;\n  /**\n   * Reversal amount. Present only when status is REVERSED.\n   * @readonly\n   */\n  reversalAmount?: Price;\n  /**\n   * Status.\n   *\n   * Default: `\"APPROVED\"`.\n   */\n  status?: ChargebackStatusWithLiterals;\n  /**\n   * External chargeback ID.\n   * @format GUID\n   * @readonly\n   * @immutable\n   */\n  externalId?: string | null;\n}\n\nexport enum ChargebackStatus {\n  UNSPECIFIED = 'UNSPECIFIED',\n  /** Chargeback was approved. */\n  APPROVED = 'APPROVED',\n  /** Chargeback was reversed. */\n  REVERSED = 'REVERSED',\n}\n\n/** @enumType */\nexport type ChargebackStatusWithLiterals =\n  | ChargebackStatus\n  | 'UNSPECIFIED'\n  | 'APPROVED'\n  | 'REVERSED';\n\nexport interface GiftCardPaymentDetails {\n  /**\n   * Gift card payment ID.\n   * @minLength 1\n   * @maxLength 100\n   */\n  giftCardPaymentId?: string;\n  /**\n   * ID of the app that created the gift card.\n   * @format GUID\n   */\n  appId?: string;\n  /**\n   * Whether the gift card is voided.\n   * @readonly\n   */\n  voided?: boolean;\n  /**\n   * Gift card obfuscated code.\n   * @internal\n   * @maxLength 30\n   * @immutable\n   */\n  obfuscatedCode?: string | null;\n}\n\nexport interface MembershipPaymentDetails {\n  /**\n   * Membership ID.\n   * @minLength 1\n   * @maxLength 100\n   */\n  membershipId?: string;\n  /**\n   * ID of the line item this membership applies to.\n   * @minLength 1\n   * @maxLength 100\n   */\n  lineItemId?: string;\n  /** Payment status. */\n  status?: MembershipPaymentStatusWithLiterals;\n  /** Membership name. */\n  name?: MembershipName;\n  /**\n   * The transaction ID in the membership system. Can be used to void the transaction.\n   * @minLength 1\n   * @maxLength 100\n   */\n  externalTransactionId?: string | null;\n  /**\n   * Whether the membership is voided.\n   * @readonly\n   */\n  voided?: boolean;\n  /**\n   * ID of the application providing this payment option.\n   * @format GUID\n   */\n  providerAppId?: string;\n}\n\nexport enum MembershipPaymentStatus {\n  /** Payment was charged. */\n  CHARGED = 'CHARGED',\n  /** The attempt to charge the payment failed, for example, due to lack of credits. */\n  CHARGE_FAILED = 'CHARGE_FAILED',\n}\n\n/** @enumType */\nexport type MembershipPaymentStatusWithLiterals =\n  | MembershipPaymentStatus\n  | 'CHARGED'\n  | 'CHARGE_FAILED';\n\nexport interface MembershipName {\n  /**\n   * Membership name.\n   * @maxLength 100\n   */\n  original?: string;\n  /**\n   * Translated membership name. Defaults to `original` when not provided.\n   * @maxLength 100\n   */\n  translated?: string | null;\n}\n\nexport interface WixReceiptInfo {\n  /**\n   * Receipt ID\n   * @format GUID\n   */\n  receiptId?: string;\n  /**\n   * Display number of receipt\n   * @minLength 1\n   * @maxLength 40\n   */\n  displayNumber?: string | null;\n}\n\nexport interface ExternalReceiptInfo {\n  /**\n   * External receipt ID\n   * @maxLength 100\n   */\n  receiptId?: string | null;\n  /**\n   * ID of the app providing the receipt\n   * @format GUID\n   */\n  appId?: string | null;\n  /**\n   * Display number of receipt\n   * @minLength 1\n   * @maxLength 40\n   */\n  displayNumber?: string | null;\n}\n\nexport interface Refund {\n  /**\n   * Refund ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string;\n  /**\n   * List of transactions.\n   * @maxSize 50\n   */\n  transactions?: RefundTransaction[];\n  /** Refund business details. */\n  details?: RefundDetails;\n  /**\n   * Date and time the refund was created in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601#Combined_date_and_time_representations) format. Defaults to current time when not provided.\n   * @readonly\n   * @immutable\n   */\n  _createdDate?: Date | null;\n  /**\n   * Aggregated refund summary.\n   * @readonly\n   */\n  summary?: AggregatedRefundSummary;\n  /**\n   * ID of the app that initiated this refund.\n   * @format GUID\n   * @readonly\n   * @immutable\n   */\n  requestingServiceAppId?: string | null;\n}\n\nexport interface RefundTransaction {\n  /**\n   * ID of the payment associated with this refund.\n   * @format GUID\n   * @immutable\n   */\n  paymentId?: string;\n  /**\n   * Refund amount.\n   * @immutable\n   */\n  amount?: Price;\n  /** Refund status. */\n  refundStatus?: RefundStatusWithLiterals;\n  /** Optional details of current refund status. */\n  refundStatusInfo?: RefundStatusInfo;\n  /**\n   * Payment gateway's refund ID.\n   * This field is only returned when the value of `external_refund` is `false`.\n   * @format GUID\n   */\n  gatewayRefundId?: string | null;\n  /** ID of the refund in the payment provider's system. For example, at PayPal, Square, Stripe, etc. Not returned for external refunds. */\n  providerRefundId?: string | null;\n  /**\n   * Whether refund was made externally and manually on the payment provider's side.\n   * @immutable\n   */\n  externalRefund?: boolean;\n}\n\n/** Refund transaction status. */\nexport enum RefundStatus {\n  /** Refund was initiated on payment provider side. PENDING status was assigned by provider. */\n  PENDING = 'PENDING',\n  /** Refund transaction succeeded. */\n  SUCCEEDED = 'SUCCEEDED',\n  /** Refund transaction failed. */\n  FAILED = 'FAILED',\n  /** Refund request acknowledged, and will be executed soon. */\n  SCHEDULED = 'SCHEDULED',\n  /** Refund was initiated on payment provider side. */\n  STARTED = 'STARTED',\n}\n\n/** @enumType */\nexport type RefundStatusWithLiterals =\n  | RefundStatus\n  | 'PENDING'\n  | 'SUCCEEDED'\n  | 'FAILED'\n  | 'SCHEDULED'\n  | 'STARTED';\n\nexport interface RefundStatusInfo {\n  /**\n   * Reason code for the refund's current status.\n   *\n   * Learn more about [reason codes](https://dev.wix.com/docs/rest/business-management/payments/service-plugins/payment-service-provider-service-plugin/reason-codes).\n   * @minLength 1\n   * @maxLength 10\n   */\n  paymentGatewayReasonCode?: string | null;\n  /**\n   * Free text explanation of current refund status.\n   * @minLength 1\n   * @maxLength 1000\n   */\n  description?: string | null;\n}\n\n/** Business model of a refund request */\nexport interface RefundDetails {\n  /**\n   * Order line item IDs and quantities that were refunded.\n   * @maxSize 300\n   */\n  items?: RefundItem[];\n  /** Whether the shipping fee was also refunded. */\n  shippingIncluded?: boolean;\n  /**\n   * Reason for the refund, provided by customer (optional).\n   * @maxLength 200\n   */\n  reason?: string | null;\n  /**\n   * Line items that were refunded.\n   * @maxSize 300\n   */\n  lineItems?: LineItemRefund[];\n  /**\n   * Additional fees that were refunded.\n   * @maxSize 100\n   */\n  additionalFees?: AdditionalFeeRefund[];\n  /** Shipping amount that was refunded. */\n  shipping?: ShippingRefund;\n}\n\nexport interface RefundItem {\n  /**\n   * Line item ID the refunded line item.\n   * @format GUID\n   */\n  lineItemId?: string;\n  /**\n   * Line item quantity refunded.\n   * @min 1\n   * @max 100000\n   */\n  quantity?: number;\n}\n\nexport interface LineItemRefund {\n  /**\n   * Line item ID.\n   * @format GUID\n   * @immutable\n   */\n  lineItemId?: string;\n  /**\n   * Refund quantity.\n   * @min 1\n   * @max 100000\n   * @immutable\n   */\n  quantity?: number;\n}\n\nexport interface AdditionalFeeRefund {\n  /**\n   * Additional fee ID.\n   * @format GUID\n   * @immutable\n   */\n  additionalFeeId?: string;\n  /**\n   * Refund amount.\n   * @immutable\n   */\n  amount?: Price;\n}\n\nexport interface ShippingRefund {\n  /**\n   * Refund amount.\n   * @immutable\n   */\n  amount?: Price;\n}\n\nexport interface AggregatedRefundSummary {\n  /** Total amount requested for refund. */\n  requestedRefund?: Price;\n  /** Pending refund amount - the portion of `requestedRefund` that is still pending. */\n  pendingRefund?: Price;\n  /** Refunded amount - the portion of `requestedRefund` that refunded successfully. */\n  refunded?: Price;\n  /** Failed refund amount - the portion of `requestedRefund` that failed. */\n  failedRefundAmount?: Price;\n  /** Whether at least one refund transaction is still in `\"PENDING\"` status. */\n  pending?: boolean;\n  /** Breakdown of refunded items. Available only after refund is complete. */\n  breakdown?: RefundItemsBreakdown;\n}\n\nexport interface RefundItemsBreakdown {\n  /**\n   * Refunded line items and the amount refunded for each.\n   * @maxSize 300\n   */\n  lineItems?: LineItemRefundSummary[];\n}\n\nexport interface LineItemRefundSummary {\n  /**\n   * ID of the refunded line item.\n   * @format GUID\n   */\n  lineItemId?: string;\n  /** Total refunded amount for the line item. */\n  totalRefundedAmount?: Price;\n}\n\nexport interface FormSubmission {\n  /**\n   * Label of form field\n   * @maxLength 500\n   */\n  label?: string | null;\n  /**\n   * Key of the submission, and target of a form field\n   * @maxLength 200\n   */\n  key?: string;\n  /**\n   * Value of the submission, formatted as a string\n   * @maxLength 500\n   */\n  value?: string;\n  /**\n   * Id of the form field\n   * @format GUID\n   */\n  fieldId?: string;\n}\n\nexport interface QueryExtendedBookingRequest {\n  /** Information about filters, paging, and sorting. */\n  query: QueryV2;\n  /** Whether information about which actions the customer can perform for the bookings is returned. */\n  withBookingAllowedActions?: boolean;\n  /** Whether information about the attendance for the bookings is returned. */\n  withBookingAttendanceInfo?: boolean;\n  /**\n   * Filters the retrieved bookings by the booking ID that corresponds to the\n   * specified `sessionId`.\n   *\n   * The `booking.id` is calculated by calling Get Session and saving the returned\n   * values for `participants.Id`.\n   *\n   * These participant IDs are then used as `booking.id`.\n   * This filter overrides the `booking.id` filter inside the query object.\n   *\n   * __Note__: Bookings for courses don't include a `sessionId`. For these\n   * bookings, you must use this field to filter by session ID.\n   */\n  sessionId?: string | null;\n  /**\n   * Whether to return information about the order from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomOrder?: boolean;\n  /**\n   * Whether to return information about the invoices from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_INVOICES` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomInvoices?: boolean;\n  /**\n   * Whether to return information about the transactions from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_TRANSACTIONS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomTransactions?: boolean;\n  /**\n   * Whether to return information about the form submission.\n   *\n   * You must have the `WIX_FORMS.SUBMISSION_READ_ANY` and `BOOKINGS.BOOKING_FORM_SCHEMA_READ` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withFormSubmissions?: boolean;\n}\n\nexport interface QueryV2 extends QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   */\n  sort?: Sorting[];\n  /** Array of projected fields. A list of specific field names to return. If `fieldsets` are also specified, the union of `fieldsets` and `fields` is returned. */\n  fields?: string[];\n  /** Array of named, predefined sets of projected fields. A array of predefined named sets of fields to be returned. Specifying multiple `fieldsets` will return the union of fields from all sets. If `fields` are also specified, the union of `fieldsets` and `fields` is returned. */\n  fieldsets?: string[];\n}\n\n/** @oneof */\nexport interface QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface Paging {\n  /** Number of items to load. */\n  limit?: number | null;\n  /** Number of items to skip in the current sort order. */\n  offset?: number | null;\n}\n\nexport interface CursorPaging {\n  /**\n   * Maximum number of items to return in the results.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface QueryExtendedBookingResponse {\n  /**\n   * Retrieved bookings and additional information about attendance or actions\n   * the customer can perform\n   */\n  extendedBookings?: ExtendedBooking[];\n  /** Paging metadata. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface PagingMetadataV2 {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  offset?: number | null;\n  /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */\n  total?: number | null;\n  /** Flag that indicates the server failed to calculate the `total` field. */\n  tooManyToCount?: boolean | null;\n  /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */\n  cursors?: Cursors;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor string pointing to the next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to the previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface QueryExtendedBookingsRequest {\n  /** Information about filters, paging, and sorting. */\n  query: CommonQueryV2;\n  /** Whether to return information about the actions the customer can perform for the bookings. */\n  withBookingAllowedActions?: boolean;\n  /** Whether to return information about the attendance for the bookings. */\n  withBookingAttendanceInfo?: boolean;\n  /** Whether to return information about the online conferencing details for the bookings. */\n  withBookingConferencingDetails?: boolean;\n  /**\n   * Whether to retrieve information about booking policy settings.\n   *\n   * You must have the `BOOKINGS.BOOKING_POLICY_SNAPSHOT_READ` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withBookingPolicySettings?: boolean;\n  /**\n   * Whether to retrieve information about booking fee\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction))\n   * statuses.\n   *\n   * Information about booking fees with a status of `PREVIEW` isn't returned,\n   * and only fees that have already been applied are included in the response.\n   * If there are hypothetical fees for a booking that may be canceled in the\n   * future, these fees aren't returned. To retrieve such fees, call\n   * List Booking Fees By Booking Ids\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/list-booking-fees-by-booking-ids) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/list-booking-fees-by-booking-ids))\n   * instead.\n   *\n   * You must have the `BOOKINGS.BOOKING_FEES_READ` permission scope when passing\n   * `true`.\n   *\n   * Default: `false`\n   */\n  withBookingFeeDetails?: boolean;\n  /**\n   * Whether to return information about the order from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomOrder?: boolean;\n  /**\n   * Whether to return information about the invoices from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_INVOICES` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomInvoices?: boolean;\n  /**\n   * Whether to return information about the transactions from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_TRANSACTIONS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomTransactions?: boolean;\n  /**\n   * Whether to return information about the form submission.\n   *\n   * You must have the `WIX_FORMS.SUBMISSION_READ_ANY` and `BOOKINGS.BOOKING_FORM_SCHEMA_READ` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withFormSubmissions?: boolean;\n}\n\nexport interface CommonQueryV2 extends CommonQueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: CommonPaging;\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   *\n   *   See [Supported Filters](https://www.wix.com/velo/reference/wix-bookings-v2/extendedbookings/supported-filters)\n   * for a full list.\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface CommonQueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: CommonPaging;\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface CommonPaging {\n  /** Number of items to load. */\n  limit?: number | null;\n  /** Number of items to skip in the current sort order. */\n  offset?: number | null;\n}\n\nexport interface QueryExtendedBookingsResponse {\n  /**\n   * Retrieved bookings and additional information, such as information about about the attendance or actions\n   * the customer can perform.\n   */\n  extendedBookings?: ExtendedBooking[];\n  /** Paging metadata. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface CountExtendedBookingsRequest {\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountExtendedBookingsResponse {\n  /** The number of bookings matching the specified filter. */\n  count?: number;\n}\n\n/**\n * > **Deprecation Notice**\n * >\n * > **This endpoint has been replaced with [Query Extended Bookings](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings) and will be removed on May 31, 2025.**\n * > **If your app uses this endpoint, we recommend updating your code as soon as possible.**\n *\n *\n * Retrieves a list of bookings, given the provided paging, filtering, and sorting.\n *\n *\n * You can also retrieve information about which actions the customer can perform\n * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.\n *\n * Query Bookings runs with these defaults:\n *\n * - `createdDate` sorted in `DESC` order\n * - `cursorPaging.limit` is `50`\n *\n * You can check the overview about all\n * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)\n * for more information.\n *\n * `query.fields` and `query.fieldsets` aren't supported for this endpoint.\n *\n * When using filters for dates, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * Bookings belonging to a schedule don't have a `sessionId`. Therefore you\n * must use the `sessionId` filter that isn't part of the `query` object to\n * filter bookings for courses.\n *\n * To learn about working with _Query_ endpoints, see\n * [API Query Language](https://dev.wix.com/api/rest/getting-started/api-query-language),\n * [Sorting and Paging](https://dev.wix.com/api/rest/getting-started/sorting-and-paging),\n * and [Field Projection](https://dev.wix.com/api/rest/getting-started/field-projection).\n *\n * When calling Query Bookings, the retrieved data may not contain your most recent changes. See\n * [Wix-data and Eventual Consistency](https://dev.wix.com/docs/rest/business-solutions/cms/eventual-consistency)\n * for more information.\n * @public\n * @documentationMaturity preview\n * @requiredField query\n * @param query - Information about filters, paging, and sorting.\n * @param options - Additional options for performing the query.\n * @permissionId BOOKINGS.BOOKING_READ\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.reader.v2.BookingsReader.Query\n * @deprecated\n */\nexport async function query(\n  query: QueryV2,\n  options?: QueryOptions\n): Promise<\n  NonNullablePaths<\n    QueryExtendedBookingResponse,\n    | `extendedBookings`\n    | `extendedBookings.${number}.booking.totalParticipants`\n    | `extendedBookings.${number}.booking.status`\n    | `extendedBookings.${number}.booking.paymentStatus`\n    | `extendedBookings.${number}.booking.selectedPaymentOption`\n    | `extendedBookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `extendedBookings.${number}.booking.createdBy.memberId`\n    | `extendedBookings.${number}.booking.createdBy.wixUserId`\n    | `extendedBookings.${number}.booking.createdBy.appId`\n    | `extendedBookings.${number}.allowedActions.cancel`\n    | `extendedBookings.${number}.allowedActions.reschedule`\n    | `extendedBookings.${number}.attendance.status`\n    | `extendedBookings.${number}.attendance.numberOfAttendees`\n    | `extendedBookings.${number}.bookingFeeDetails.cancellationFeeStatus`\n    | `extendedBookings.${number}.order.number`\n    | `extendedBookings.${number}.order.status`\n    | `extendedBookings.${number}.order.paymentStatus`\n    | `extendedBookings.${number}.waiver.status`\n    | `extendedBookings.${number}.invoices.orderId`\n    | `extendedBookings.${number}.transactions.orderId`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    query: query,\n    withBookingAllowedActions: options?.withBookingAllowedActions,\n    withBookingAttendanceInfo: options?.withBookingAttendanceInfo,\n    sessionId: options?.sessionId,\n    withEcomOrder: options?.withEcomOrder,\n    withEcomInvoices: options?.withEcomInvoices,\n    withEcomTransactions: options?.withEcomTransactions,\n    withFormSubmissions: options?.withFormSubmissions,\n  });\n\n  const reqOpts = ambassadorWixBookingsReaderV2ExtendedBooking.query(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          query: '$[0]',\n          withBookingAllowedActions: '$[1].withBookingAllowedActions',\n          withBookingAttendanceInfo: '$[1].withBookingAttendanceInfo',\n          sessionId: '$[1].sessionId',\n          withEcomOrder: '$[1].withEcomOrder',\n          withEcomInvoices: '$[1].withEcomInvoices',\n          withEcomTransactions: '$[1].withEcomTransactions',\n          withFormSubmissions: '$[1].withFormSubmissions',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['query', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface QueryOptions {\n  /** Whether information about which actions the customer can perform for the bookings is returned. */\n  withBookingAllowedActions?: boolean;\n  /** Whether information about the attendance for the bookings is returned. */\n  withBookingAttendanceInfo?: boolean;\n  /**\n   * Filters the retrieved bookings by the booking ID that corresponds to the\n   * specified `sessionId`.\n   *\n   * The `booking.id` is calculated by calling Get Session and saving the returned\n   * values for `participants.Id`.\n   *\n   * These participant IDs are then used as `booking.id`.\n   * This filter overrides the `booking.id` filter inside the query object.\n   *\n   * __Note__: Bookings for courses don't include a `sessionId`. For these\n   * bookings, you must use this field to filter by session ID.\n   */\n  sessionId?: string | null;\n  /**\n   * Whether to return information about the order from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomOrder?: boolean;\n  /**\n   * Whether to return information about the invoices from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_INVOICES` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomInvoices?: boolean;\n  /**\n   * Whether to return information about the transactions from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_TRANSACTIONS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomTransactions?: boolean;\n  /**\n   * Whether to return information about the form submission.\n   *\n   * You must have the `WIX_FORMS.SUBMISSION_READ_ANY` and `BOOKINGS.BOOKING_FORM_SCHEMA_READ` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withFormSubmissions?: boolean;\n}\n\n/**\n * Retrieves a list of bookings, including additional extended information, given the provided paging, filtering, and sorting.\n *\n * `queryExtendedBookings()` doesn't use a query builder, instead it uses [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#with-the-api-query-language).\n *\n * Up to 100 extended bookings can be returned per request.\n *\n * `queryExtendedBookings()` runs with these defaults, which you can override:\n *\n * - `createdDate` sorted in `DESC` order\n * - `cursorPaging.limit` is `50`\n *\n *\n * You can retrieve information about which actions the customer can perform\n * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.\n *\n *\n * For field support, see\n * [supported filters](https://www.wix.com/velo/reference/wix-bookings-v2/extendedbookings/supported-filters)\n * for more information.\n *\n * You can specify a filter only once per query. If you specify a filter\n * more than once, only the first filter determines the extended bookings that are returned.\n *\n * When filtering by date, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n * @public\n * @requiredField query\n * @param query - Information about filters, paging, and sorting.\n * @param options - Additional options for performing the query.\n * @permissionId BOOKINGS.BOOKING_READ\n * @permissionId BOOKINGS.MANAGE_BOOKINGS\n * @permissionId BOOKINGS.BOOKING_READ_ANY\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.reader.v2.BookingsReader.QueryExtendedBookings\n */\nexport async function queryExtendedBookings(\n  query: CommonQueryV2,\n  options?: QueryExtendedBookingsOptions\n): Promise<\n  NonNullablePaths<\n    QueryExtendedBookingsResponse,\n    | `extendedBookings`\n    | `extendedBookings.${number}.booking.totalParticipants`\n    | `extendedBookings.${number}.booking.status`\n    | `extendedBookings.${number}.booking.paymentStatus`\n    | `extendedBookings.${number}.booking.selectedPaymentOption`\n    | `extendedBookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `extendedBookings.${number}.booking.createdBy.memberId`\n    | `extendedBookings.${number}.booking.createdBy.wixUserId`\n    | `extendedBookings.${number}.booking.createdBy.appId`\n    | `extendedBookings.${number}.allowedActions.cancel`\n    | `extendedBookings.${number}.allowedActions.reschedule`\n    | `extendedBookings.${number}.attendance.status`\n    | `extendedBookings.${number}.attendance.numberOfAttendees`\n    | `extendedBookings.${number}.bookingFeeDetails.cancellationFeeStatus`\n    | `extendedBookings.${number}.order.number`\n    | `extendedBookings.${number}.order.status`\n    | `extendedBookings.${number}.order.paymentStatus`\n    | `extendedBookings.${number}.waiver.status`\n    | `extendedBookings.${number}.invoices.orderId`\n    | `extendedBookings.${number}.transactions.orderId`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    query: query,\n    withBookingAllowedActions: options?.withBookingAllowedActions,\n    withBookingAttendanceInfo: options?.withBookingAttendanceInfo,\n    withBookingConferencingDetails: options?.withBookingConferencingDetails,\n    withBookingPolicySettings: options?.withBookingPolicySettings,\n    withBookingFeeDetails: options?.withBookingFeeDetails,\n    withEcomOrder: options?.withEcomOrder,\n    withEcomInvoices: options?.withEcomInvoices,\n    withEcomTransactions: options?.withEcomTransactions,\n    withFormSubmissions: options?.withFormSubmissions,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsReaderV2ExtendedBooking.queryExtendedBookings(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          query: '$[0]',\n          withBookingAllowedActions: '$[1].withBookingAllowedActions',\n          withBookingAttendanceInfo: '$[1].withBookingAttendanceInfo',\n          withBookingConferencingDetails: '$[1].withBookingConferencingDetails',\n          withBookingPolicySettings: '$[1].withBookingPolicySettings',\n          withBookingFeeDetails: '$[1].withBookingFeeDetails',\n          withEcomOrder: '$[1].withEcomOrder',\n          withEcomInvoices: '$[1].withEcomInvoices',\n          withEcomTransactions: '$[1].withEcomTransactions',\n          withFormSubmissions: '$[1].withFormSubmissions',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['query', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface QueryExtendedBookingsOptions {\n  /** Whether to return information about the actions the customer can perform for the bookings. */\n  withBookingAllowedActions?: boolean;\n  /** Whether to return information about the attendance for the bookings. */\n  withBookingAttendanceInfo?: boolean;\n  /** Whether to return information about the online conferencing details for the bookings. */\n  withBookingConferencingDetails?: boolean;\n  /**\n   * Whether to retrieve information about booking policy settings.\n   *\n   * You must have the `BOOKINGS.BOOKING_POLICY_SNAPSHOT_READ` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withBookingPolicySettings?: boolean;\n  /**\n   * Whether to retrieve information about booking fee\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction))\n   * statuses.\n   *\n   * Information about booking fees with a status of `PREVIEW` isn't returned,\n   * and only fees that have already been applied are included in the response.\n   * If there are hypothetical fees for a booking that may be canceled in the\n   * future, these fees aren't returned. To retrieve such fees, call\n   * List Booking Fees By Booking Ids\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/list-booking-fees-by-booking-ids) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/list-booking-fees-by-booking-ids))\n   * instead.\n   *\n   * You must have the `BOOKINGS.BOOKING_FEES_READ` permission scope when passing\n   * `true`.\n   *\n   * Default: `false`\n   */\n  withBookingFeeDetails?: boolean;\n  /**\n   * Whether to return information about the order from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomOrder?: boolean;\n  /**\n   * Whether to return information about the invoices from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_INVOICES` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomInvoices?: boolean;\n  /**\n   * Whether to return information about the transactions from ecom.\n   *\n   * You must have the `ECOM.READ_ORDERS` and `ECOM.READ_TRANSACTIONS` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withEcomTransactions?: boolean;\n  /**\n   * Whether to return information about the form submission.\n   *\n   * You must have the `WIX_FORMS.SUBMISSION_READ_ANY` and `BOOKINGS.BOOKING_FORM_SCHEMA_READ` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  withFormSubmissions?: boolean;\n}\n\n/**\n * Counts the number of bookings matching the specified filters.\n *\n *\n * For field support see\n * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)\n * for more information.\n * @public\n * @permissionId BOOKINGS.BOOKING_MANAGE\n * @permissionId BOOKINGS.MANAGE_BOOKINGS\n * @permissionId BOOKINGS.BOOKING_READ_ANY\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.reader.v2.BookingsReader.CountExtendedBookings\n */\nexport async function countExtendedBookings(\n  options?: CountExtendedBookingsOptions\n): Promise<NonNullablePaths<CountExtendedBookingsResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsReaderV2ExtendedBooking.countExtendedBookings(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountExtendedBookingsOptions {\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n}\n", "import { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsReaderV2BookingsReaderUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    _: [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'editor._base_domain': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n      {\n        srcPath: '/bookings/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_extended-bookings';\n\n/**\n * > **Deprecation Notice**\n * >\n * > **This endpoint has been replaced with [Query Extended Bookings](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings) and will be removed on May 31, 2025.**\n * > **If your app uses this endpoint, we recommend updating your code as soon as possible.**\n *\n *\n * Retrieves a list of bookings, given the provided paging, filtering, and sorting.\n *\n *\n * You can also retrieve information about which actions the customer can perform\n * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.\n *\n * Query Bookings runs with these defaults:\n *\n * - `createdDate` sorted in `DESC` order\n * - `cursorPaging.limit` is `50`\n *\n * You can check the overview about all\n * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)\n * for more information.\n *\n * `query.fields` and `query.fieldsets` aren't supported for this endpoint.\n *\n * When using filters for dates, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * Bookings belonging to a schedule don't have a `sessionId`. Therefore you\n * must use the `sessionId` filter that isn't part of the `query` object to\n * filter bookings for courses.\n *\n * To learn about working with _Query_ endpoints, see\n * [API Query Language](https://dev.wix.com/api/rest/getting-started/api-query-language),\n * [Sorting and Paging](https://dev.wix.com/api/rest/getting-started/sorting-and-paging),\n * and [Field Projection](https://dev.wix.com/api/rest/getting-started/field-projection).\n *\n * When calling Query Bookings, the retrieved data may not contain your most recent changes. See\n * [Wix-data and Eventual Consistency](https://dev.wix.com/docs/rest/business-solutions/cms/eventual-consistency)\n * for more information.\n * @deprecated\n */\nexport function query(payload: object): RequestOptionsFactory<any> {\n  function __query({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.reader.v2.extended_booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.reader.v2.BookingsReader.Query',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({\n        protoPath: '/v2/extended-booking/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'extendedBookings.booking.createdDate' },\n              { path: 'extendedBookings.booking.startDate' },\n              { path: 'extendedBookings.booking.endDate' },\n              { path: 'extendedBookings.booking.updatedDate' },\n              { path: 'extendedBookings.booking.canceledDate' },\n              { path: 'extendedBookings.transactions.payments.createdDate' },\n              { path: 'extendedBookings.transactions.payments.updatedDate' },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate',\n              },\n              { path: 'extendedBookings.transactions.refunds.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __query;\n}\n\n/**\n * Retrieves a list of bookings, including additional extended information, given the provided paging, filtering, and sorting.\n *\n * `queryExtendedBookings()` doesn't use a query builder, instead it uses [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#with-the-api-query-language).\n *\n * Up to 100 extended bookings can be returned per request.\n *\n * `queryExtendedBookings()` runs with these defaults, which you can override:\n *\n * - `createdDate` sorted in `DESC` order\n * - `cursorPaging.limit` is `50`\n *\n *\n * You can retrieve information about which actions the customer can perform\n * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.\n *\n *\n * For field support, see\n * [supported filters](https://www.wix.com/velo/reference/wix-bookings-v2/extendedbookings/supported-filters)\n * for more information.\n *\n * You can specify a filter only once per query. If you specify a filter\n * more than once, only the first filter determines the extended bookings that are returned.\n *\n * When filtering by date, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n */\nexport function queryExtendedBookings(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryExtendedBookings({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.reader.v2.extended_booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.reader.v2.BookingsReader.QueryExtendedBookings',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({\n        protoPath: '/v2/extended-bookings/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'extendedBookings.booking.createdDate' },\n              { path: 'extendedBookings.booking.startDate' },\n              { path: 'extendedBookings.booking.endDate' },\n              { path: 'extendedBookings.booking.updatedDate' },\n              { path: 'extendedBookings.booking.canceledDate' },\n              { path: 'extendedBookings.transactions.payments.createdDate' },\n              { path: 'extendedBookings.transactions.payments.updatedDate' },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate',\n              },\n              { path: 'extendedBookings.transactions.refunds.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryExtendedBookings;\n}\n\n/**\n * Counts the number of bookings matching the specified filters.\n *\n *\n * For field support see\n * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)\n * for more information.\n */\nexport function countExtendedBookings(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __countExtendedBookings({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.reader.v2.extended_booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.reader.v2.BookingsReader.CountExtendedBookings',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({\n        protoPath: '/v2/extended-bookings/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countExtendedBookings;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BAAAA;AAAA,EAAA,aAAAC;AAAA,EAAA,6BAAAC;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,oCAGO;;;ACJP,mBAA6C;AAC7C,uBAAqD;AACrD,6BAA+B;AAC/B,0BAA2B;AAI3B,SAAS,oDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,gCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AA0Cd,SAAS,MAAM,SAA6C;AACjE,WAAS,QAAQ,EAAE,KAAK,GAAQ;AAC9B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,YAC3C,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,wCAAwC;AAAA,YAChD,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,qDAAqD;AAAA,YAC7D;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA4BO,SAAS,sBACd,SAC4B;AAC5B,WAAS,wBAAwB,EAAE,KAAK,GAAQ;AAC9C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,YAC3C,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,wCAAwC;AAAA,YAChD,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,qDAAqD;AAAA,YAC7D;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,sBACd,SAC4B;AAC5B,WAAS,wBAAwB,EAAE,KAAK,GAAQ;AAC9C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADxNO,IAAK,mBAAL,kBAAKC,sBAAL;AAEL,EAAAA,kBAAA,aAAU;AAEV,EAAAA,kBAAA,cAAW;AAEX,EAAAA,kBAAA,kBAAe;AANL,SAAAA;AAAA,GAAA;AA0KL,IAAK,0BAAL,kBAAKC,6BAAL;AAEL,EAAAA,yBAAA,yBAAsB;AAEtB,EAAAA,yBAAA,uBAAoB;AAEpB,EAAAA,yBAAA,uBAAoB;AANV,SAAAA;AAAA,GAAA;AAoKL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,oBAAiB;AACjB,EAAAA,cAAA,kBAAe;AACf,EAAAA,cAAA,YAAS;AAJC,SAAAA;AAAA,GAAA;AAkKL,IAAK,YAAL,kBAAKC,eAAL;AAEL,EAAAA,WAAA,gBAAa;AAEb,EAAAA,WAAA,eAAY;AAEZ,EAAAA,WAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AAiBL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,aAAU;AAKV,EAAAA,eAAA,eAAY;AAMZ,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,aAAU;AAEV,EAAAA,eAAA,cAAW;AAMX,EAAAA,eAAA,kBAAe;AAvBL,SAAAA;AAAA,GAAA;AAwCL,IAAK,uBAAL,kBAAKC,0BAAL;AAEL,EAAAA,sBAAA,eAAY;AAEZ,EAAAA,sBAAA,cAAW;AAEX,EAAAA,sBAAA,UAAO;AAEP,EAAAA,sBAAA,oBAAiB;AAEjB,EAAAA,sBAAA,cAAW;AAEX,EAAAA,sBAAA,YAAS;AAZC,SAAAA;AAAA,GAAA;AAgCL,IAAK,wBAAL,kBAAKC,2BAAL;AAEL,EAAAA,uBAAA,eAAY;AAEZ,EAAAA,uBAAA,aAAU;AAEV,EAAAA,uBAAA,YAAS;AAET,EAAAA,uBAAA,gBAAa;AAKb,EAAAA,uBAAA,wBAAqB;AAbX,SAAAA;AAAA,GAAA;AA2CL,IAAK,WAAL,kBAAKC,cAAL;AACL,EAAAA,UAAA,wBAAqB;AACrB,EAAAA,UAAA,SAAM;AACN,EAAAA,UAAA,gBAAa;AAHH,SAAAA;AAAA,GAAA;AAaL,IAAK,QAAL,kBAAKC,WAAL;AACL,EAAAA,OAAA,qBAAkB;AAClB,EAAAA,OAAA,cAAW;AACX,EAAAA,OAAA,cAAW;AAHD,SAAAA;AAAA,GAAA;AA4EL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,uBAAoB;AACpB,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,cAAW;AACX,EAAAA,cAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AA6OL,IAAK,mBAAL,kBAAKC,sBAAL;AAEL,EAAAA,kBAAA,oBAAiB;AAEjB,EAAAA,kBAAA,8BAA2B;AAE3B,EAAAA,kBAAA,sBAAmB;AANT,SAAAA;AAAA,GAAA;AAwIL,IAAK,oBAAL,kBAAKC,uBAAL;AAEL,EAAAA,mBAAA,yBAAsB;AAEtB,EAAAA,mBAAA,0BAAuB;AAEvB,EAAAA,mBAAA,gBAAa;AAEb,EAAAA,mBAAA,oBAAiB;AAEjB,EAAAA,mBAAA,wBAAqB;AAKrB,EAAAA,mBAAA,8CAA2C;AAfjC,SAAAA;AAAA,GAAA;AA6CL,IAAK,cAAL,kBAAKC,iBAAL;AAEL,EAAAA,aAAA,iBAAc;AAOd,EAAAA,aAAA,cAAW;AAEX,EAAAA,aAAA,cAAW;AAKX,EAAAA,aAAA,aAAU;AAOV,EAAAA,aAAA,cAAW;AAvBD,SAAAA;AAAA,GAAA;AAmCL,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,iBAAc;AAKd,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,UAAO;AAEP,EAAAA,eAAA,wBAAqB;AAErB,EAAAA,eAAA,oBAAiB;AAOjB,EAAAA,eAAA,aAAU;AAEV,EAAAA,eAAA,oBAAiB;AAQjB,EAAAA,eAAA,sBAAmB;AAKnB,EAAAA,eAAA,cAAW;AAKX,EAAAA,eAAA,cAAW;AAvCD,SAAAA;AAAA,GAAA;AAgHL,IAAK,SAAL,kBAAKC,YAAL;AACL,EAAAA,QAAA,eAAY;AACZ,EAAAA,QAAA,YAAS;AACT,EAAAA,QAAA,gBAAa;AAHH,SAAAA;AAAA,GAAA;AA0KL,IAAK,oBAAL,kBAAKC,uBAAL;AACL,EAAAA,mBAAA,eAAY;AACZ,EAAAA,mBAAA,cAAW;AACX,EAAAA,mBAAA,aAAU;AACV,EAAAA,mBAAA,sBAAmB;AACnB,EAAAA,mBAAA,cAAW;AACX,EAAAA,mBAAA,cAAW;AACX,EAAAA,mBAAA,cAAW;AACX,EAAAA,mBAAA,wBAAqB;AACrB,EAAAA,mBAAA,gBAAa;AACb,EAAAA,mBAAA,YAAS;AAVC,SAAAA;AAAA,GAAA;AAgFL,IAAK,6BAAL,kBAAKC,gCAAL;AACL,EAAAA,4BAAA,oBAAiB;AAEjB,EAAAA,4BAAA,aAAU;AAEV,EAAAA,4BAAA,eAAY;AAEZ,EAAAA,4BAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AAkCL,IAAK,0BAAL,kBAAKC,6BAAL;AACL,EAAAA,yBAAA,oBAAiB;AAEjB,EAAAA,yBAAA,aAAU;AAEV,EAAAA,yBAAA,eAAY;AAEZ,EAAAA,yBAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AAmBL,IAAK,SAAL,kBAAKC,YAAL;AACL,EAAAA,QAAA,oBAAiB;AAEjB,EAAAA,QAAA,YAAS;AAET,EAAAA,QAAA,eAAY;AALF,SAAAA;AAAA,GAAA;AAsBL,IAAK,aAAL,kBAAKC,gBAAL;AACL,EAAAA,YAAA,yBAAsB;AACtB,EAAAA,YAAA,UAAO;AACP,EAAAA,YAAA,aAAU;AAHA,SAAAA;AAAA,GAAA;AA0DL,IAAK,mBAAL,kBAAKC,sBAAL;AACL,EAAAA,kBAAA,iBAAc;AAEd,EAAAA,kBAAA,cAAW;AAEX,EAAAA,kBAAA,cAAW;AALD,SAAAA;AAAA,GAAA;AA4EL,IAAK,0BAAL,kBAAKC,6BAAL;AAEL,EAAAA,yBAAA,aAAU;AAEV,EAAAA,yBAAA,mBAAgB;AAJN,SAAAA;AAAA,GAAA;AA6HL,IAAK,eAAL,kBAAKC,kBAAL;AAEL,EAAAA,cAAA,aAAU;AAEV,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,YAAS;AAET,EAAAA,cAAA,eAAY;AAEZ,EAAAA,cAAA,aAAU;AAVA,SAAAA;AAAA,GAAA;AA+RL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AA0QZ,eAAsBC,OACpBA,QACA,SA0BA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,OAAOA;AAAA,IACP,2BAA2B,SAAS;AAAA,IACpC,2BAA2B,SAAS;AAAA,IACpC,WAAW,SAAS;AAAA,IACpB,eAAe,SAAS;AAAA,IACxB,kBAAkB,SAAS;AAAA,IAC3B,sBAAsB,SAAS;AAAA,IAC/B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAAuD,MAAM,OAAO;AAE1E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,OAAO;AAAA,UACP,2BAA2B;AAAA,UAC3B,2BAA2B;AAAA,UAC3B,WAAW;AAAA,UACX,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB,sBAAsB;AAAA,UACtB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS,SAAS;AAAA,IACrB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA8FA,eAAsBC,uBACpBF,QACA,SA0BA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,OAAOA;AAAA,IACP,2BAA2B,SAAS;AAAA,IACpC,2BAA2B,SAAS;AAAA,IACpC,gCAAgC,SAAS;AAAA,IACzC,2BAA2B,SAAS;AAAA,IACpC,uBAAuB,SAAS;AAAA,IAChC,eAAe,SAAS;AAAA,IACxB,kBAAkB,SAAS;AAAA,IAC3B,sBAAsB,SAAS;AAAA,IAC/B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UACyC,sBAAsB,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,OAAO;AAAA,UACP,2BAA2B;AAAA,UAC3B,2BAA2B;AAAA,UAC3B,gCAAgC;AAAA,UAChC,2BAA2B;AAAA,UAC3B,uBAAuB;AAAA,UACvB,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB,sBAAsB;AAAA,UACtB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS,SAAS;AAAA,IACrB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyFA,eAAsBE,uBACpB,SACsE;AAEtE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACyC,sBAAsB,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;", "names": ["countExtendedBookings", "query", "queryExtendedBookings", "payload", "AttendanceStatus", "MultiServiceBookingType", "LocationType", "ValueType", "BookingStatus", "BookingPaymentStatus", "SelectedPaymentOption", "Platform", "Actor", "IdentityType", "BookingFeeStatus", "PaymentOptionType", "OrderStatus", "PaymentStatus", "Status", "TransactionStatus", "AuthorizationCaptureStatus", "AuthorizationVoidStatus", "Reason", "ActionType", "ChargebackStatus", "MembershipPaymentStatus", "RefundStatus", "SortOrder", "query", "sdkTransformError", "queryExtendedBookings", "countExtendedBookings"]}