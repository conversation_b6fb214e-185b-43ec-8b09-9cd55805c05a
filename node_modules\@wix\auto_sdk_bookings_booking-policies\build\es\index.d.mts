import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { BookingPolicy, CreateBookingPolicyValidationErrors, GetStrictestBookingPolicyResponse, UpdateBookingPolicy, UpdateBookingPolicyValidationErrors, SetDefaultBookingPolicyResponse, DeleteBookingPolicyApplicationErrors, BookingPoliciesQueryBuilder, CountBookingPoliciesOptions, CountBookingPoliciesResponse, BookingPolicyCreatedEnvelope, BookingPolicyDefaultBookingPolicySetEnvelope, BookingPolicyDeletedEnvelope, BookingPolicyUpdatedEnvelope } from './index.typings.mjs';
export { ActionEvent, Address, AddressHint, Asset, BaseEventMetadata, BookAfterStartPolicy, BookingPoliciesQueryResult, BusinessSchedule, CancellationFeePolicy, CancellationPolicy, CancellationWindow, CancellationWindowFeeOneOf, Categories, ChangeContext, ChangeContextPayloadOneOf, ConsentPolicy, CountBookingPoliciesRequest, CreateBookingPolicyRequest, CreateBookingPolicyResponse, CreateMissingDefaultPolicyRequest, CreateMissingDefaultPolicyResponse, CursorPaging, CursorPagingMetadata, CursorQuery, CursorQueryPagingMethodOneOf, Cursors, DayOfWeek, DayOfWeekWithLiterals, DefaultBookingPolicySet, DeleteBookingPolicyRequest, DeleteBookingPolicyResponse, DeleteContext, DeleteStatus, DeleteStatusWithLiterals, DomainEvent, DomainEventBodyOneOf, Empty, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, EventMetadata, ExtendedFields, GeoCoordinates, GetBookingPolicyRequest, GetBookingPolicyResponse, GetStrictestBookingPolicyRequest, IdentificationData, IdentificationDataIdOneOf, LimitEarlyBookingPolicy, LimitLateBookingPolicy, Locale, MessageEnvelope, MetaSiteSpecialEvent, MetaSiteSpecialEventPayloadOneOf, Money, Multilingual, Namespace, NamespaceChanged, NamespaceWithLiterals, OdeditorAssigned, OdeditorUnassigned, ParticipantsPolicy, PicassoAssigned, PicassoUnassigned, PlacementType, PlacementTypeWithLiterals, PolicyDescription, Properties, PropertiesChange, QueryBookingPoliciesRequest, QueryBookingPoliciesResponse, ReschedulePolicy, ResolutionMethod, ResolutionMethodWithLiterals, ResourcesPolicy, RestoreInfo, SaveCreditCardPolicy, ServiceProvisioned, ServiceRemoved, SetDefaultBookingPolicyRequest, SiteCloned, SiteCreated, SiteCreatedContext, SiteCreatedContextWithLiterals, SiteDeleted, SiteHardDeleted, SiteMarkedAsTemplate, SiteMarkedAsWixSite, SitePropertiesEvent, SitePropertiesNotification, SitePublished, SitePurgedExternally, SiteRenamed, SiteTransferred, SiteUndeleted, SiteUnpublished, SiteUrlChanged, SortOrder, SortOrderWithLiterals, Sorting, SpecialHourPeriod, State, StateWithLiterals, StudioAssigned, StudioUnassigned, SupportedLanguage, TimePeriod, Translation, UpdateAllPoliciesRequest, UpdateAllPoliciesResponse, UpdateBookingPolicyRequest, UpdateBookingPolicyResponse, V4SiteCreated, WaitlistPolicy, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.mjs';

declare function createBookingPolicy$1(httpClient: HttpClient): CreateBookingPolicySignature;
interface CreateBookingPolicySignature {
    /**
     * Creates a booking policy.
     * @param - Booking policy to create.
     * @returns Created booking policy.
     */
    (bookingPolicy: BookingPolicy): Promise<NonNullablePaths<BookingPolicy, `customPolicyDescription.enabled` | `customPolicyDescription.description` | `limitEarlyBookingPolicy.enabled` | `limitEarlyBookingPolicy.earliestBookingInMinutes` | `limitLateBookingPolicy.enabled` | `limitLateBookingPolicy.latestBookingInMinutes` | `bookAfterStartPolicy.enabled` | `cancellationPolicy.enabled` | `cancellationPolicy.limitLatestCancellation` | `cancellationPolicy.latestCancellationInMinutes` | `reschedulePolicy.enabled` | `reschedulePolicy.limitLatestReschedule` | `reschedulePolicy.latestRescheduleInMinutes` | `waitlistPolicy.enabled` | `waitlistPolicy.capacity` | `waitlistPolicy.reservationTimeInMinutes` | `participantsPolicy.maxParticipantsPerBooking` | `cancellationFeePolicy.enabled` | `cancellationFeePolicy.cancellationWindows` | `cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `cancellationFeePolicy.cancellationWindows.${number}.percentage` | `saveCreditCardPolicy.enabled`, 6> & {
        __validationErrorsType?: CreateBookingPolicyValidationErrors;
    }>;
}
declare function getBookingPolicy$1(httpClient: HttpClient): GetBookingPolicySignature;
interface GetBookingPolicySignature {
    /**
     * Retrieves a booking policy.
     * @param - ID of the booking policy to retrieve.
     * @returns Retrieved booking policy.
     */
    (bookingPolicyId: string): Promise<NonNullablePaths<BookingPolicy, `customPolicyDescription.enabled` | `customPolicyDescription.description` | `limitEarlyBookingPolicy.enabled` | `limitEarlyBookingPolicy.earliestBookingInMinutes` | `limitLateBookingPolicy.enabled` | `limitLateBookingPolicy.latestBookingInMinutes` | `bookAfterStartPolicy.enabled` | `cancellationPolicy.enabled` | `cancellationPolicy.limitLatestCancellation` | `cancellationPolicy.latestCancellationInMinutes` | `reschedulePolicy.enabled` | `reschedulePolicy.limitLatestReschedule` | `reschedulePolicy.latestRescheduleInMinutes` | `waitlistPolicy.enabled` | `waitlistPolicy.capacity` | `waitlistPolicy.reservationTimeInMinutes` | `participantsPolicy.maxParticipantsPerBooking` | `cancellationFeePolicy.enabled` | `cancellationFeePolicy.cancellationWindows` | `cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `cancellationFeePolicy.cancellationWindows.${number}.percentage` | `saveCreditCardPolicy.enabled`, 6>>;
}
declare function getStrictestBookingPolicy$1(httpClient: HttpClient): GetStrictestBookingPolicySignature;
interface GetStrictestBookingPolicySignature {
    /**
     * Retrieves the strictest version of each policy rule from a list of booking
     * policies.
     *
     *
     * Returns a hypothetical `bookingPolicy` object that combines the strictest
     * version of each rule. The `id` of the returned policy is `null` and no
     * corresponding `bookingPolicy` object is created. To create a new policy, you
     * can call *Create Booking Policy*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/create-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/create-booking-policy)).
     * @param - IDs of the booking policies for which to retrieve the strictest rules for.
     */
    (bookingPolicyIds: string[]): Promise<NonNullablePaths<GetStrictestBookingPolicyResponse, `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled`, 7>>;
}
declare function updateBookingPolicy$1(httpClient: HttpClient): UpdateBookingPolicySignature;
interface UpdateBookingPolicySignature {
    /**
     * Updates a booking policy.
     *
     *
     * Each time the booking policy is updated, `revision` increments by 1.
     * The current `revision` must be specified when updating the booking policy.
     * This ensures you're working with the latest booking policy and prevents unintended overwrites.
     * @param - ID of the booking policy.
     * @returns Updated booking policy.
     */
    (_id: string, bookingPolicy: NonNullablePaths<UpdateBookingPolicy, `revision`, 2>): Promise<NonNullablePaths<BookingPolicy, `customPolicyDescription.enabled` | `customPolicyDescription.description` | `limitEarlyBookingPolicy.enabled` | `limitEarlyBookingPolicy.earliestBookingInMinutes` | `limitLateBookingPolicy.enabled` | `limitLateBookingPolicy.latestBookingInMinutes` | `bookAfterStartPolicy.enabled` | `cancellationPolicy.enabled` | `cancellationPolicy.limitLatestCancellation` | `cancellationPolicy.latestCancellationInMinutes` | `reschedulePolicy.enabled` | `reschedulePolicy.limitLatestReschedule` | `reschedulePolicy.latestRescheduleInMinutes` | `waitlistPolicy.enabled` | `waitlistPolicy.capacity` | `waitlistPolicy.reservationTimeInMinutes` | `participantsPolicy.maxParticipantsPerBooking` | `cancellationFeePolicy.enabled` | `cancellationFeePolicy.cancellationWindows` | `cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `cancellationFeePolicy.cancellationWindows.${number}.percentage` | `saveCreditCardPolicy.enabled`, 6> & {
        __validationErrorsType?: UpdateBookingPolicyValidationErrors;
    }>;
}
declare function setDefaultBookingPolicy$1(httpClient: HttpClient): SetDefaultBookingPolicySignature;
interface SetDefaultBookingPolicySignature {
    /**
     * Sets a booking policy as the default.
     *
     *
     * Also updates the site's current default policy by setting its `default`
     * attribute to `false`. If the provided policy is already the site's
     * default, the call succeeds without changing any `bookingPolicy` object.
     * @param - ID of the booking policy that's set as default.
     */
    (bookingPolicyId: string): Promise<NonNullablePaths<SetDefaultBookingPolicyResponse, `currentDefaultBookingPolicy.customPolicyDescription.enabled` | `currentDefaultBookingPolicy.customPolicyDescription.description` | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.enabled` | `currentDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `currentDefaultBookingPolicy.limitLateBookingPolicy.enabled` | `currentDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `currentDefaultBookingPolicy.bookAfterStartPolicy.enabled` | `currentDefaultBookingPolicy.cancellationPolicy.enabled` | `currentDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation` | `currentDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `currentDefaultBookingPolicy.reschedulePolicy.enabled` | `currentDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule` | `currentDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `currentDefaultBookingPolicy.waitlistPolicy.enabled` | `currentDefaultBookingPolicy.waitlistPolicy.capacity` | `currentDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `currentDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `currentDefaultBookingPolicy.cancellationFeePolicy.enabled` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `currentDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `currentDefaultBookingPolicy.saveCreditCardPolicy.enabled` | `previousDefaultBookingPolicy.customPolicyDescription.enabled` | `previousDefaultBookingPolicy.customPolicyDescription.description` | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.enabled` | `previousDefaultBookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `previousDefaultBookingPolicy.limitLateBookingPolicy.enabled` | `previousDefaultBookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `previousDefaultBookingPolicy.bookAfterStartPolicy.enabled` | `previousDefaultBookingPolicy.cancellationPolicy.enabled` | `previousDefaultBookingPolicy.cancellationPolicy.limitLatestCancellation` | `previousDefaultBookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `previousDefaultBookingPolicy.reschedulePolicy.enabled` | `previousDefaultBookingPolicy.reschedulePolicy.limitLatestReschedule` | `previousDefaultBookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `previousDefaultBookingPolicy.waitlistPolicy.enabled` | `previousDefaultBookingPolicy.waitlistPolicy.capacity` | `previousDefaultBookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `previousDefaultBookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `previousDefaultBookingPolicy.cancellationFeePolicy.enabled` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.value` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.amount.currency` | `previousDefaultBookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `previousDefaultBookingPolicy.saveCreditCardPolicy.enabled`, 7>>;
}
declare function deleteBookingPolicy$1(httpClient: HttpClient): DeleteBookingPolicySignature;
interface DeleteBookingPolicySignature {
    /**
     * Deletes a booking policy.
     *
     *
     * You can't delete the default policy without first *setting a different policy as default*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/set-default-booking-policy) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/set-default-booking-policy)).
     * @param - ID of the booking policy to delete.
     */
    (bookingPolicyId: string): Promise<void & {
        __applicationErrorsType?: DeleteBookingPolicyApplicationErrors;
    }>;
}
declare function queryBookingPolicies$1(httpClient: HttpClient): QueryBookingPoliciesSignature;
interface QueryBookingPoliciesSignature {
    /**
     * Creates a query to retrieve a list of `bookingPolicy` objects.
     *
     * The `queryBookingPolicies()` function builds a query to retrieve a list of `bookingPolicy` objects and returns a `BookingPoliciesQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-builder/find) function.
     *
     * You can refine the query by chaining `BookingPoliciesQueryBuilder` functions onto the query. `BookingPoliciesQueryBuilder` functions enable you to sort, filter, and control the results that `queryBookingPolicies()` returns.
     *
     * `queryBookingPolicies()` runs with the following `BookingPoliciesQueryBuilder` defaults that you can override:
     *
     * + `limit` is `50`.
     * + Sorted by `createdDate` in ascending order.
     *
     * The functions that are chained to `queryBookingPolicies()` are applied in the order they are called. For example, if you apply `ascending("waitlistPolicy.capacity")` and then `ascending("name")`, the results are sorted first by `waitlistPolicy.capacity`, and then, if there are multiple results with the same `waitlistPolicy.capacity`, the items are sorted by `name`.
     *
     * The following `BookingPoliciesQueryBuilder` functions are supported for the `queryBookingPolicies()` function. For a full description of the `bookingPolicy` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/booking-policies-query-result/items) property in `BookingPoliciesQueryResult`.
     */
    (): BookingPoliciesQueryBuilder;
}
declare function countBookingPolicies$1(httpClient: HttpClient): CountBookingPoliciesSignature;
interface CountBookingPoliciesSignature {
    /**
     * Counts booking policies, given the provided filtering.
     *
     *
     * See *supported filters*
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
     * for a complete list of supported filters.
     */
    (options?: CountBookingPoliciesOptions): Promise<NonNullablePaths<CountBookingPoliciesResponse, `count`, 2>>;
}
declare const onBookingPolicyCreated$1: EventDefinition<BookingPolicyCreatedEnvelope, "wix.bookings.v1.booking_policy_created">;
declare const onBookingPolicyDefaultBookingPolicySet$1: EventDefinition<BookingPolicyDefaultBookingPolicySetEnvelope, "wix.bookings.v1.booking_policy_default_booking_policy_set">;
declare const onBookingPolicyDeleted$1: EventDefinition<BookingPolicyDeletedEnvelope, "wix.bookings.v1.booking_policy_deleted">;
declare const onBookingPolicyUpdated$1: EventDefinition<BookingPolicyUpdatedEnvelope, "wix.bookings.v1.booking_policy_updated">;

declare const createBookingPolicy: MaybeContext<BuildRESTFunction<typeof createBookingPolicy$1> & typeof createBookingPolicy$1>;
declare const getBookingPolicy: MaybeContext<BuildRESTFunction<typeof getBookingPolicy$1> & typeof getBookingPolicy$1>;
declare const getStrictestBookingPolicy: MaybeContext<BuildRESTFunction<typeof getStrictestBookingPolicy$1> & typeof getStrictestBookingPolicy$1>;
declare const updateBookingPolicy: MaybeContext<BuildRESTFunction<typeof updateBookingPolicy$1> & typeof updateBookingPolicy$1>;
declare const setDefaultBookingPolicy: MaybeContext<BuildRESTFunction<typeof setDefaultBookingPolicy$1> & typeof setDefaultBookingPolicy$1>;
declare const deleteBookingPolicy: MaybeContext<BuildRESTFunction<typeof deleteBookingPolicy$1> & typeof deleteBookingPolicy$1>;
declare const queryBookingPolicies: MaybeContext<BuildRESTFunction<typeof queryBookingPolicies$1> & typeof queryBookingPolicies$1>;
declare const countBookingPolicies: MaybeContext<BuildRESTFunction<typeof countBookingPolicies$1> & typeof countBookingPolicies$1>;
/**
 * Triggered when a booking policy is created.
 */
declare const onBookingPolicyCreated: BuildEventDefinition<typeof onBookingPolicyCreated$1>;
/**
 * Triggered when the site's default policy changes. Then,
 * _Booking Policy Updated_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/on-booking-policy-updated) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/booking-policy-updated))
 * is also triggered both for the new and the previous default policy.
 */
declare const onBookingPolicyDefaultBookingPolicySet: BuildEventDefinition<typeof onBookingPolicyDefaultBookingPolicySet$1>;
/**
 * Triggered when a booking policy is deleted.
 */
declare const onBookingPolicyDeleted: BuildEventDefinition<typeof onBookingPolicyDeleted$1>;
/**
 * Triggered when a booking policy is updated, including when a policy's
 * `default` attribute changes.
 */
declare const onBookingPolicyUpdated: BuildEventDefinition<typeof onBookingPolicyUpdated$1>;

export { BookingPoliciesQueryBuilder, BookingPolicy, BookingPolicyCreatedEnvelope, BookingPolicyDefaultBookingPolicySetEnvelope, BookingPolicyDeletedEnvelope, BookingPolicyUpdatedEnvelope, CountBookingPoliciesOptions, CountBookingPoliciesResponse, CreateBookingPolicyValidationErrors, DeleteBookingPolicyApplicationErrors, GetStrictestBookingPolicyResponse, SetDefaultBookingPolicyResponse, UpdateBookingPolicy, UpdateBookingPolicyValidationErrors, countBookingPolicies, createBookingPolicy, deleteBookingPolicy, getBookingPolicy, getStrictestBookingPolicy, onBookingPolicyCreated, onBookingPolicyDefaultBookingPolicySet, onBookingPolicyDeleted, onBookingPolicyUpdated, queryBookingPolicies, setDefaultBookingPolicy, updateBookingPolicy };
