import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { Attendance, GetAttendanceApplicationErrors, SetAttendanceOptions, SetAttendanceResponse, SetAttendanceApplicationErrors, BulkSetAttendanceOptions, BulkSetAttendanceResponse, BulkSetAttendanceApplicationErrors, AttendancesQueryBuilder, CountAttendancesOptions, CountAttendancesResponse } from './index.typings.mjs';
export { ActionEvent, ApplicationError, AttendanceDetails, AttendanceMarkedAsNotAttended, AttendanceStatus, AttendanceStatusWithLiterals, AttendancesQueryResult, BulkActionMetadata, BulkAttendanceResult, BulkSetAttendanceRequest, CountAttendancesRequest, CursorPaging, CursorPagingMetadata, Cursors, DomainEvent, DomainEventBodyOneOf, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, GetAttendanceRequest, GetAttendanceResponse, IdentificationData, IdentificationDataIdOneOf, ItemMetadata, MessageEnvelope, Paging, ParticipantNotification, QueryAttendanceRequest, QueryAttendanceResponse, QueryV2, QueryV2PagingMethodOneOf, RestoreInfo, SetAttendanceRequest, SortOrder, SortOrderWithLiterals, Sorting, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.mjs';

declare function getAttendance$1(httpClient: HttpClient): GetAttendanceSignature;
interface GetAttendanceSignature {
    /**
     * Retrieves attendance information.
     * @param - ID of the attendance object to retrieve.
     * @returns Retrieved attendance.
     */
    (attendanceId: string): Promise<NonNullablePaths<Attendance, `status` | `numberOfAttendees`, 2> & {
        __applicationErrorsType?: GetAttendanceApplicationErrors;
    }>;
}
declare function setAttendance$1(httpClient: HttpClient): SetAttendanceSignature;
interface SetAttendanceSignature {
    /**
     * Sets or updates attendance information for a booking session. This
     * information is stored in an `attendance` object.
     *
     * If an `attendance` object already exists for the session, it's updated.
     * Otherwise, a new object is created.
     *
     * By default, `numberOfAttendees` is set to `1`, but you can specify a higher
     * number if multiple participants attended. Do not set `numberOfAttendees` to
     * `0` to indicate no attendance, instead specify `{"status": "NOT_ATTENDED"}`.
     *
     * Validation guidelines:
     *
     * + The call succeeds for mismatches between `numberOfAttendees`
     * and `status`. For example, make sure that your code doesn't specify
     * `{"status": "NOT_ATTENDED"}` with `{"numberOfAttendees": 5}`.
     * + The API also allows `numberOfAttendees` to exceed the booking's
     * `numberOfParticipants`. Use higher values only when scenarios like
     * walk-ins justify the exception.
     * @param - Attendance to create or update.
     * @param - Options to use when setting an attendance.
     */
    (attendance: NonNullablePaths<Attendance, `bookingId` | `status`, 2>, options?: SetAttendanceOptions): Promise<NonNullablePaths<SetAttendanceResponse, `attendance.status` | `attendance.numberOfAttendees`, 3> & {
        __applicationErrorsType?: SetAttendanceApplicationErrors;
    }>;
}
declare function bulkSetAttendance$1(httpClient: HttpClient): BulkSetAttendanceSignature;
interface BulkSetAttendanceSignature {
    /**
     * Sets or updates attendance information for multiple booking sessions.
     *
     *
     * Refer to Set Attendance for detailed behavior of individual attendance
     * entries.
     *
     * The call fails entirely if any entry in `attendanceDetails` is missing a
     * required field.
     *
     * If attendance details are provided for a non-existent session, the call
     * succeeds for valid sessions while marking the unavailable session as a
     * failure in the response.
     * @param - Options to use when setting multiple attendances in bulk.
     */
    (options?: BulkSetAttendanceOptions): Promise<NonNullablePaths<BulkSetAttendanceResponse, `results` | `results.${number}.item.status` | `results.${number}.item.numberOfAttendees` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
        __applicationErrorsType?: BulkSetAttendanceApplicationErrors;
    }>;
}
declare function queryAttendance$1(httpClient: HttpClient): QueryAttendanceSignature;
interface QueryAttendanceSignature {
    /**
     * Creates a query to retrieve a list of attendances.
     *
     * The `queryAttendances()` function builds a query to retrieve a list of attendances and returns a `AttendancesQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to call the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-builder/find) function.
     *
     * You can refine the query by chaining `AttendancesQueryBuilder` functions onto the query. `AttendancesQueryBuilder` functions enable you to sort, filter, and control the results that `queryAttendances()` returns.
     *
     * `queryAttendances()` uses the following `AttendancesQueryBuilder` default values that you can override:
     *
     * + `limit` is `50`.
     * + Sorted by `id` in ascending order.
     *
     * The functions that are chained to `queryAttendances()` are applied in the order they are called. For example, if you apply `ascending("status")` and then `ascending("numberOfAttendees")`, the results are sorted first by the `"status"`, and then, if there are multiple results with the same `"status"`, the items are sorted by `"numberOfAttendees"`.
     *
     * The following `AttendancesQueryBuilder` functions are supported for the `queryAttendances()` function. For a full description of the tip settings object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-result/items) property in `AttendancesQueryResult`.
     */
    (): AttendancesQueryBuilder;
}
declare function countAttendances$1(httpClient: HttpClient): CountAttendancesSignature;
interface CountAttendancesSignature {
    /**
     * Counts attendance records by contact.
     *
     * Returns the total number of attendance records for the contact,
     * with optional filtering by attendance status and service IDs.
     *
     * Filtering options:
     * + **attendance_status**: Filter by attendance status (ATTENDED, NOT_ATTENDED)
     * + **service_id**: Filter by one or more service IDs from the bookings
     *
     * If no filters are provided, returns the total count of all attendance records
     * for the contact.
     */
    (options?: CountAttendancesOptions): Promise<NonNullablePaths<CountAttendancesResponse, `count`, 2>>;
}

declare const getAttendance: MaybeContext<BuildRESTFunction<typeof getAttendance$1> & typeof getAttendance$1>;
declare const setAttendance: MaybeContext<BuildRESTFunction<typeof setAttendance$1> & typeof setAttendance$1>;
declare const bulkSetAttendance: MaybeContext<BuildRESTFunction<typeof bulkSetAttendance$1> & typeof bulkSetAttendance$1>;
declare const queryAttendance: MaybeContext<BuildRESTFunction<typeof queryAttendance$1> & typeof queryAttendance$1>;
declare const countAttendances: MaybeContext<BuildRESTFunction<typeof countAttendances$1> & typeof countAttendances$1>;

export { Attendance, AttendancesQueryBuilder, BulkSetAttendanceApplicationErrors, BulkSetAttendanceOptions, BulkSetAttendanceResponse, CountAttendancesOptions, CountAttendancesResponse, GetAttendanceApplicationErrors, SetAttendanceApplicationErrors, SetAttendanceOptions, SetAttendanceResponse, bulkSetAttendance, countAttendances, getAttendance, queryAttendance, setAttendance };
