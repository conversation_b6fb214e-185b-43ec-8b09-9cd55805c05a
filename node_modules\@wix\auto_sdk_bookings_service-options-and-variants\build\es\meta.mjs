// src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_service-options-and-variants";
function createServiceOptionsAndVariants(payload) {
  function __createServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CreateServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __createServiceOptionsAndVariants;
}
function cloneServiceOptionsAndVariants(payload) {
  function __cloneServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CloneServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{cloneFromId}/clone",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __cloneServiceOptionsAndVariants;
}
function getServiceOptionsAndVariants(payload) {
  function __getServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "GET",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      fallback: [
        {
          method: "GET",
          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
            protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
            data: payload,
            host
          }),
          params: toURLSearchParams(payload)
        }
      ]
    };
    return metadata;
  }
  return __getServiceOptionsAndVariants;
}
function getServiceOptionsAndVariantsByServiceId(payload) {
  function __getServiceOptionsAndVariantsByServiceId({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "GET",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariantsByServiceId",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      fallback: [
        {
          method: "GET",
          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
            protoPath: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
            data: payload,
            host
          }),
          params: toURLSearchParams(payload)
        }
      ]
    };
    return metadata;
  }
  return __getServiceOptionsAndVariantsByServiceId;
}
function updateServiceOptionsAndVariants(payload) {
  function __updateServiceOptionsAndVariants({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "PATCH",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.UpdateServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}",
        data: serializedData,
        host
      }),
      data: serializedData
    };
    return metadata;
  }
  return __updateServiceOptionsAndVariants;
}
function deleteServiceOptionsAndVariants(payload) {
  function __deleteServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "DELETE",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.DeleteServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteServiceOptionsAndVariants;
}
function queryServiceOptionsAndVariants(payload) {
  function __queryServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.QueryServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryServiceOptionsAndVariants;
}

// src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.meta.ts
function createServiceOptionsAndVariants2() {
  const payload = {};
  const getRequestOptions = createServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/serviceOptionsAndVariants",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function cloneServiceOptionsAndVariants2() {
  const payload = { cloneFromId: ":cloneFromId" };
  const getRequestOptions = cloneServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/serviceOptionsAndVariants/{cloneFromId}/clone",
    pathParams: { cloneFromId: "cloneFromId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getServiceOptionsAndVariants2() {
  const payload = {
    serviceOptionsAndVariantsId: ":serviceOptionsAndVariantsId"
  };
  const getRequestOptions = getServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
    pathParams: { serviceOptionsAndVariantsId: "serviceOptionsAndVariantsId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getServiceOptionsAndVariantsByServiceId2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = getServiceOptionsAndVariantsByServiceId(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateServiceOptionsAndVariants2() {
  const payload = {
    serviceOptionsAndVariants: { id: ":serviceOptionsAndVariantsId" }
  };
  const getRequestOptions = updateServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}",
    pathParams: { serviceOptionsAndVariantsId: "serviceOptionsAndVariantsId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteServiceOptionsAndVariants2() {
  const payload = {
    serviceOptionsAndVariantsId: ":serviceOptionsAndVariantsId"
  };
  const getRequestOptions = deleteServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
    pathParams: { serviceOptionsAndVariantsId: "serviceOptionsAndVariantsId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryServiceOptionsAndVariants2() {
  const payload = {};
  const getRequestOptions = queryServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/serviceOptionsAndVariants/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  cloneServiceOptionsAndVariants2 as cloneServiceOptionsAndVariants,
  createServiceOptionsAndVariants2 as createServiceOptionsAndVariants,
  deleteServiceOptionsAndVariants2 as deleteServiceOptionsAndVariants,
  getServiceOptionsAndVariants2 as getServiceOptionsAndVariants,
  getServiceOptionsAndVariantsByServiceId2 as getServiceOptionsAndVariantsByServiceId,
  queryServiceOptionsAndVariants2 as queryServiceOptionsAndVariants,
  updateServiceOptionsAndVariants2 as updateServiceOptionsAndVariants
};
//# sourceMappingURL=meta.mjs.map