"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  BookingFeeStatus: () => BookingFeeStatus,
  Trigger: () => Trigger,
  WebhookIdentityType: () => WebhookIdentityType,
  applyBookingFeesToOrder: () => applyBookingFeesToOrder4,
  collectAppliedBookingFees: () => collectAppliedBookingFees4,
  listBookingFeesByBookingIds: () => listBookingFeesByBookingIds4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-fees-v1-booking-fee-booking-fees.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-fees-v1-booking-fee-booking-fees.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsFeesV1BookingFeesUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/booking-fees",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-fees";
function listBookingFeesByBookingIds(payload) {
  function __listBookingFeesByBookingIds({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "GET",
      methodFqn: "wix.bookings.fees.v1.BookingFees.ListBookingFeesByBookingIds",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __listBookingFeesByBookingIds;
}
function applyBookingFeesToOrder(payload) {
  function __applyBookingFeesToOrder({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "POST",
      methodFqn: "wix.bookings.fees.v1.BookingFees.ApplyBookingFeesToOrder",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees/apply",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __applyBookingFeesToOrder;
}
function collectAppliedBookingFees(payload) {
  function __collectAppliedBookingFees({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.fees.v1.booking_fee",
      method: "POST",
      methodFqn: "wix.bookings.fees.v1.BookingFees.CollectAppliedBookingFees",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsFeesV1BookingFeesUrl({
        protoPath: "/v1/booking-fees/collect",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __collectAppliedBookingFees;
}

// src/bookings-fees-v1-booking-fee-booking-fees.universal.ts
var BookingFeeStatus = /* @__PURE__ */ ((BookingFeeStatus2) => {
  BookingFeeStatus2["UNKNOWN_STATUS"] = "UNKNOWN_STATUS";
  BookingFeeStatus2["PREVIEW"] = "PREVIEW";
  BookingFeeStatus2["NOT_YET_APPLIED_TO_ORDER"] = "NOT_YET_APPLIED_TO_ORDER";
  BookingFeeStatus2["APPLIED_TO_ORDER"] = "APPLIED_TO_ORDER";
  return BookingFeeStatus2;
})(BookingFeeStatus || {});
var Trigger = /* @__PURE__ */ ((Trigger2) => {
  Trigger2["UNKNOWN_TRIGGER"] = "UNKNOWN_TRIGGER";
  Trigger2["NOT_ATTENDED"] = "NOT_ATTENDED";
  Trigger2["BOOKING_CANCELED"] = "BOOKING_CANCELED";
  return Trigger2;
})(Trigger || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function listBookingFeesByBookingIds2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingIds: options?.bookingIds,
    multiServiceBookingIds: options?.multiServiceBookingIds
  });
  const reqOpts = listBookingFeesByBookingIds(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingIds: "$[0].bookingIds",
          multiServiceBookingIds: "$[0].multiServiceBookingIds"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function applyBookingFeesToOrder2(bookingIds, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingIds,
    priceOverride: options?.priceOverride,
    businessNotification: options?.businessNotification
  });
  const reqOpts = applyBookingFeesToOrder(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingIds: "$[0]",
          priceOverride: "$[1].priceOverride",
          businessNotification: "$[1].businessNotification"
        },
        singleArgumentUnchanged: false
      },
      ["bookingIds", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function collectAppliedBookingFees2(orderId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    orderId,
    additionalFeeId: options?.additionalFeeId,
    businessNotification: options?.businessNotification
  });
  const reqOpts = collectAppliedBookingFees(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          orderId: "$[0]",
          additionalFeeId: "$[1].additionalFeeId",
          businessNotification: "$[1].businessNotification"
        },
        singleArgumentUnchanged: false
      },
      ["orderId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-fees-v1-booking-fee-booking-fees.public.ts
function listBookingFeesByBookingIds3(httpClient) {
  return (options) => listBookingFeesByBookingIds2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function applyBookingFeesToOrder3(httpClient) {
  return (bookingIds, options) => applyBookingFeesToOrder2(
    bookingIds,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function collectAppliedBookingFees3(httpClient) {
  return (orderId, options) => collectAppliedBookingFees2(
    orderId,
    options,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-fees-v1-booking-fee-booking-fees.context.ts
var import_rest_modules3 = require("@wix/sdk-runtime/rest-modules");
var listBookingFeesByBookingIds4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(listBookingFeesByBookingIds3);
var applyBookingFeesToOrder4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(applyBookingFeesToOrder3);
var collectAppliedBookingFees4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(collectAppliedBookingFees3);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  BookingFeeStatus,
  Trigger,
  WebhookIdentityType,
  applyBookingFeesToOrder,
  collectAppliedBookingFees,
  listBookingFeesByBookingIds
});
//# sourceMappingURL=index.js.map