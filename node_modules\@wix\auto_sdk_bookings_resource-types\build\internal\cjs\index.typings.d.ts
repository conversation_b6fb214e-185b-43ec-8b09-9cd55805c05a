import { NonNullablePaths } from '@wix/sdk-types';

/**
 * A resource type is a classification of resources. For example room, equipment,
 * or vehicle. Customers can only book *services*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
 * if at least 1 *resource*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
 * for every resource type connected to the service is available during the requested time.
 */
interface ResourceType {
    /**
     * Resource type ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the resource type is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the resource type.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was created.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was last updated.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the resource type. For example, `meeting room`. The name must be
     * unique per site.
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
    /** Extensions enabling users to save custom data related to the resource type. */
    extendedFields?: ExtendedFields;
}
interface ResourceCounts {
    /**
     * Total number of resources connected to the type.
     * @readonly
     */
    total?: number | null;
    /**
     * Whether at least 1 resource of the type is available in all business *locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     * @readonly
     */
    hasResourcesInAllLocations?: boolean | null;
    /**
     * Whether at least 1 resource of the type is available in customer locations.
     * @readonly
     */
    hasResourcesInCustomerLocations?: boolean | null;
    /**
     * Whether at least 1 resource of the type is available in custom business locations.
     * @readonly
     */
    hasResourcesInCustomLocations?: boolean | null;
    /**
     * Number of distinct business locations with resources connected to the type.
     * @readonly
     */
    distinctBusinessLocationsCount?: number | null;
    /**
     * IDs of all business locations with resources connected to the type. If there
     * are more than 50 location IDs, only the first 50 IDs are returned.
     * @readonly
     */
    distinctLocationIds?: DistinctLocationIds;
}
interface DistinctLocationIds {
    /**
     * IDs of the business locations with a resource connected to the type.
     * @format GUID
     * @maxSize 50
     */
    values?: string[];
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateResourceTypeRequest {
    /** Resource type to create. */
    resourceType: ResourceType;
}
interface LocationOptions {
    /**
     * Whether the resource is available in all *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in all business locations.
     * - `false`: The resource is available only in specific locations.
     *
     * Default: `false`
     */
    availableInAllLocations?: boolean | null;
    /** Details of resource availability in specific locations. */
    specificLocationOptions?: SpecificLocation;
}
interface SpecificLocation {
    /**
     * Whether the resource is available in *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in business locations.
     * - `false`: The resource isn't available in business locations.
     *
     * Default: `false`
     */
    availableInBusinessLocations?: boolean | null;
    /**
     * Information about the business locations where the resource is available.
     * Not returned, if the resource is available in either all business locations
     * or in no business location.
     * You can specify up to 100 business locations.
     * @maxSize 100
     */
    businessLocations?: BusinessLocation[];
}
interface BusinessLocation {
    /**
     * ID of the business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * @format GUID
     */
    locationId?: string | null;
}
declare enum RequestedFields {
    TOTAL_RESOURCE_COUNT = "TOTAL_RESOURCE_COUNT",
    SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS = "SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS",
    DISTINCT_RESOURCE_LOCATIONS = "DISTINCT_RESOURCE_LOCATIONS"
}
/** @enumType */
type RequestedFieldsWithLiterals = RequestedFields | 'TOTAL_RESOURCE_COUNT' | 'SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS' | 'DISTINCT_RESOURCE_LOCATIONS';
interface CreateResourceTypeResponse {
    /** Created resource type. */
    resourceType?: ResourceType;
}
declare enum CreateResourceTypeErrors {
    UNKNOWN_CREATE_RESOURCE_TYPE_ERROR = "UNKNOWN_CREATE_RESOURCE_TYPE_ERROR",
    /** Failed to create requested `quantity` of resources for the resource type */
    FAILED_TO_CREATE_RESOURCES = "FAILED_TO_CREATE_RESOURCES"
}
/** @enumType */
type CreateResourceTypeErrorsWithLiterals = CreateResourceTypeErrors | 'UNKNOWN_CREATE_RESOURCE_TYPE_ERROR' | 'FAILED_TO_CREATE_RESOURCES';
interface GetResourceTypeRequest {
    /**
     * ID of the resource type to retrieve.
     * @format GUID
     */
    resourceTypeId: string;
}
interface GetResourceTypeResponse {
    /** Retrieved resource type. */
    resourceType?: ResourceType;
}
interface UpdateResourceTypeRequest {
    /** Resource type to update. */
    resourceType: ResourceType;
}
interface UpdateResourceTypeResponse {
    /** Updated resource type. */
    resourceType?: ResourceType;
}
interface DeleteResourceTypeRequest {
    /**
     * ID of the resource type to delete.
     * @format GUID
     */
    resourceTypeId: string;
}
interface DeleteResourceTypeResponse {
}
interface QueryResourceTypesRequest {
    /**
     * Information about filtering and sorting.
     * Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters and sorting options.
     */
    query?: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 3
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 200
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryResourceTypesResponse {
    /** Retrieved resource types. */
    resourceTypes?: ResourceType[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor pointing to next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface CountResourceTypesRequest {
    /**
     * Filter to base the count on.
     * Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
}
interface CountResourceTypesResponse {
    /** Number of resource types matching the filter. */
    count?: number;
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type CreateResourceTypeApplicationErrors = {
    code?: 'RESOURCE_TYPE_ALREADY_EXISTS_FOR_NAME';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MAX_NUMBER_OF_RESOURCE_TYPES_REACHED';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type UpdateResourceTypeApplicationErrors = {
    code?: 'RESOURCE_TYPE_ALREADY_EXISTS_FOR_NAME';
    description?: string;
    data?: Record<string, any>;
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface ResourceTypeCreatedEnvelope {
    entity: ResourceType;
    metadata: EventMetadata;
}
/**
 * Triggered when a resource type is created.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.RESOURCE_TYPE_READ
 * @webhook
 * @eventType wix.bookings.resources.v2.resource_type_created
 * @slug created
 */
declare function onResourceTypeCreated(handler: (event: ResourceTypeCreatedEnvelope) => void | Promise<void>): void;
interface ResourceTypeDeletedEnvelope {
    entity: ResourceType;
    metadata: EventMetadata;
}
/**
 * Triggered when a resource type is deleted.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.RESOURCE_TYPE_READ
 * @webhook
 * @eventType wix.bookings.resources.v2.resource_type_deleted
 * @slug deleted
 */
declare function onResourceTypeDeleted(handler: (event: ResourceTypeDeletedEnvelope) => void | Promise<void>): void;
interface ResourceTypeUpdatedEnvelope {
    entity: ResourceType;
    metadata: EventMetadata;
}
/**
 * Triggered when a resource type is updated.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.RESOURCE_TYPE_READ
 * @webhook
 * @eventType wix.bookings.resources.v2.resource_type_updated
 * @slug updated
 */
declare function onResourceTypeUpdated(handler: (event: ResourceTypeUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Creates a new resource type.
 * @param resourceType - Resource type to create.
 * @public
 * @documentationMaturity preview
 * @requiredField resourceType
 * @requiredField resourceType.name
 * @permissionId BOOKINGS.RESOURCE_TYPE_CREATE
 * @applicableIdentity APP
 * @returns Created resource type.
 * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType
 */
declare function createResourceType(resourceType: NonNullablePaths<ResourceType, `name`, 2>): Promise<ResourceType & {
    __applicationErrorsType?: CreateResourceTypeApplicationErrors;
}>;
/**
 * Retrieves a resource type.
 * @param resourceTypeId - ID of the resource type to retrieve.
 * @public
 * @documentationMaturity preview
 * @requiredField resourceTypeId
 * @permissionId BOOKINGS.RESOURCE_TYPE_READ
 * @permissionId BOOKINGS.RESOURCES_READ
 * @applicableIdentity APP
 * @returns Retrieved resource type.
 * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType
 */
declare function getResourceType(resourceTypeId: string): Promise<ResourceType>;
/**
 * Updates a resource type.
 *
 *
 * Each time the resource type is updated, `revision` increments by 1. You must
 * include current revision of the resource type when updating it. This ensures
 * you're working with the latest service information and prevents unintended overwrites.
 * @param _id - Resource type ID.
 * @public
 * @documentationMaturity preview
 * @requiredField _id
 * @requiredField resourceType
 * @requiredField resourceType.revision
 * @permissionId BOOKINGS.RESOURCE_TYPE_UPDATE
 * @applicableIdentity APP
 * @returns Updated resource type.
 * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType
 */
declare function updateResourceType(_id: string, resourceType: NonNullablePaths<UpdateResourceType, `revision`, 2>): Promise<ResourceType & {
    __applicationErrorsType?: UpdateResourceTypeApplicationErrors;
}>;
interface UpdateResourceType {
    /**
     * Resource type ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the resource type is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the resource type.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was created.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource type was last updated.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the resource type. For example, `meeting room`. The name must be
     * unique per site.
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
    /** Extensions enabling users to save custom data related to the resource type. */
    extendedFields?: ExtendedFields;
}
/**
 * Deletes a resource type.
 *
 *
 * Deleting a resource type also automatically deletes all resources connected to it.
 * @param resourceTypeId - ID of the resource type to delete.
 * @public
 * @documentationMaturity preview
 * @requiredField resourceTypeId
 * @permissionId BOOKINGS.RESOURCE_TYPE_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType
 */
declare function deleteResourceType(resourceTypeId: string): Promise<void>;
/**
 * Creates a query to retrieve a list of resource types.
 *
 * The `queryResourceTypes()` function builds a query to retrieve a list of resource types and returns a `ResourceTypesQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-builder/find) function.
 *
 * You can refine the query by chaining `ResourceTypesQueryBuilder` functions onto the query. `ResourceTypesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResourceTypes()` returns.
 *
 * `queryResourceTypes()` runs with the following `ResourceTypesQueryBuilder` default that you can override:
 *
 * + `limit` is `50`.
 * + Sorted by `id` in ascending order.
 *
 * The functions that are chained to `queryResourceTypes()` are applied in the order they are called.
 *
 * The following `ResourceTypesQueryBuilder` functions are supported for the `queryResourceTypes()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-result/items) property in `ResourceTypesQueryResult`.
 * @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.RESOURCE_TYPE_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes
 */
declare function queryResourceTypes(): ResourceTypesQueryBuilder;
interface QueryCursorResult {
    cursors: Cursors;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface ResourceTypesQueryResult extends QueryCursorResult {
    items: ResourceType[];
    query: ResourceTypesQueryBuilder;
    next: () => Promise<ResourceTypesQueryResult>;
    prev: () => Promise<ResourceTypesQueryResult>;
}
interface ResourceTypesQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    eq: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    ne: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    ge: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    gt: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    le: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    lt: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `string`.
     * @param string - String to compare against. Case-insensitive.
     * @documentationMaturity preview
     */
    startsWith: (propertyName: 'name', value: string) => ResourceTypesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     * @documentationMaturity preview
     */
    hasSome: (propertyName: 'name', value: any[]) => ResourceTypesQueryBuilder;
    /** @documentationMaturity preview */
    in: (propertyName: 'name', value: any) => ResourceTypesQueryBuilder;
    /** @documentationMaturity preview */
    exists: (propertyName: 'name', value: boolean) => ResourceTypesQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object.
     * @documentationMaturity preview
     */
    limit: (limit: number) => ResourceTypesQueryBuilder;
    /** @param cursor - A pointer to specific record
     * @documentationMaturity preview
     */
    skipTo: (cursor: string) => ResourceTypesQueryBuilder;
    /** @documentationMaturity preview */
    find: () => Promise<ResourceTypesQueryResult>;
}
/**
 * Counts resource types, given the provided filtering.
 *
 *
 * Refer to the *supported filters article*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
 * for a complete list of supported filters.
 * @public
 * @documentationMaturity preview
 * @param options - Filter to base the count on. See
 * [queryResourceTypes()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/query-resource-types)
 * for supported filters.
 * @permissionId BOOKINGS.RESOURCE_TYPE_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes
 */
declare function countResourceTypes(options?: CountResourceTypesOptions): Promise<NonNullablePaths<CountResourceTypesResponse, `count`, 2>>;
interface CountResourceTypesOptions {
    /**
     * Filter to base the count on.
     * Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
}

export { type ActionEvent, type BaseEventMetadata, type BusinessLocation, type CountResourceTypesOptions, type CountResourceTypesRequest, type CountResourceTypesResponse, type CreateResourceTypeApplicationErrors, CreateResourceTypeErrors, type CreateResourceTypeErrorsWithLiterals, type CreateResourceTypeRequest, type CreateResourceTypeResponse, type CursorPaging, type CursorPagingMetadata, type CursorQuery, type CursorQueryPagingMethodOneOf, type Cursors, type DeleteResourceTypeRequest, type DeleteResourceTypeResponse, type DistinctLocationIds, type DomainEvent, type DomainEventBodyOneOf, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type EventMetadata, type ExtendedFields, type GetResourceTypeRequest, type GetResourceTypeResponse, type IdentificationData, type IdentificationDataIdOneOf, type LocationOptions, type MessageEnvelope, type QueryResourceTypesRequest, type QueryResourceTypesResponse, RequestedFields, type RequestedFieldsWithLiterals, type ResourceCounts, type ResourceType, type ResourceTypeCreatedEnvelope, type ResourceTypeDeletedEnvelope, type ResourceTypeUpdatedEnvelope, type ResourceTypesQueryBuilder, type ResourceTypesQueryResult, type RestoreInfo, SortOrder, type SortOrderWithLiterals, type Sorting, type SpecificLocation, type UpdateResourceType, type UpdateResourceTypeApplicationErrors, type UpdateResourceTypeRequest, type UpdateResourceTypeResponse, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, countResourceTypes, createResourceType, deleteResourceType, getResourceType, onResourceTypeCreated, onResourceTypeDeleted, onResourceTypeUpdated, queryResourceTypes, updateResourceType };
