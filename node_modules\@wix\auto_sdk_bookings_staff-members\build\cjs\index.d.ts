import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { S as StaffMember, C as CreateStaffMemberOptions, G as GetStaffMemberOptions, U as UpdateStaffMember, a as UpdateStaffMemberOptions, Q as QueryStaffMembersOptions, b as StaffMembersQueryBuilder, c as CountStaffMembersOptions, d as CountStaffMembersResponse, e as ConnectStaffMemberToUserOptions, f as ConnectStaffMemberToUserResponse, g as StaffMemberSearch, h as SearchStaffMembersOptions, i as SearchStaffMembersResponse, D as DisconnectStaffMemberFromUserOptions, j as DisconnectStaffMemberFromUserResponse, A as AssignWorkingHoursScheduleOptions, k as AssignWorkingHoursScheduleResponse, l as AssignCustomScheduleOptions, m as AssignCustomScheduleResponse, B as BulkUpdateStaffMemberTagsOptions, n as BulkUpdateStaffMemberTagsResponse, o as BulkUpdateStaffMemberTagsByFilterOptions, p as BulkUpdateStaffMemberTagsByFilterResponse, q as StaffMemberConnectedToUserEnvelope, r as StaffMemberCreatedEnvelope, s as StaffMemberDeletedEnvelope, t as StaffMemberDisconnectedFromUserEnvelope, u as StaffMemberFullyCreatedEnvelope, v as StaffMemberUpdatedEnvelope } from './bookings-staff-v1-staff-member-staff-members.universal-Tbhk-dK0.js';
export { bv as ActionEvent, bF as Address, bI as AddressLocation, bG as AddressStreetOneOf, aS as AggregationData, b9 as AggregationResults, ba as AggregationResultsResultOneOf, aZ as AggregationResultsScalarResult, E as AggregationType, cA as AggregationTypeWithLiterals, bg as ApplicationError, O as ApprovalStatus, cI as ApprovalStatusWithLiterals, bd as AssignCustomScheduleRequest, bc as AssignWorkingHoursScheduleRequest, ad as AssociatedConferencingAccount, ae as AssociatedConferencingAccountAccountOneOf, ac as AssociatedConferencingAccounts, ab as AssociatedConferencingProvider, aa as AssociatedConferencingProviders, a6 as AssociatedWixIdentity, w as AssociatedWixIdentityConnectionStatusEnumConnectionStatus, cu as AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals, bT as Availability, bU as AvailabilityConstraints, cc as AvailabilityPolicy, cb as AvailabilityPolicyUpdated, cp as BaseEventMetadata, bi as BulkActionMetadata, bj as BulkUpdateStaffMemberTagsByFilterRequest, be as BulkUpdateStaffMemberTagsRequest, bh as BulkUpdateStaffMemberTagsResult, a5 as BusinessLocation, bO as BusinessSchedule, b_ as CalendarConference, c2 as CalendarDateTime, W as CalendarType, cL as CalendarTypeWithLiterals, a7 as CommonIdentificationData, a8 as CommonIdentificationDataIdOneOf, cP as CommonSearchWithEntityContext, bZ as ConferenceProvider, V as ConferenceType, cK as ConferenceTypeWithLiterals, aN as ConnectStaffMemberToUserRequest, a9 as Connection, x as ConnectionStatus, cv as ConnectionStatusWithLiterals, aM as CountStaffMembersRequest, am as CreateStaffMemberRequest, an as CreateStaffMemberResponse, at as CursorPaging, av as CursorPagingMetadata, aG as CursorQuery, aH as CursorQueryPagingMethodOneOf, aP as CursorSearch, aQ as CursorSearchPagingMethodOneOf, aw as Cursors, af as CustomConferenceAccount, b5 as DateHistogramResult, b7 as DateHistogramResults, F as Day, N as DayOfWeek, cH as DayOfWeekWithLiterals, cB as DayWithLiterals, aD as DeleteStaffMemberRequest, aE as DeleteStaffMemberResponse, bb as DisconnectStaffMemberFromUserRequest, bp as DomainEvent, bq as DomainEventBodyOneOf, bm as Empty, br as EntityCreatedEvent, bu as EntityDeletedEvent, bt as EntityUpdatedEvent, cq as EventMetadata, a2 as EventSchedule, ag as ExtendedFields, c4 as ExternalCalendarInfo, bX as ExternalCalendarOverrides, bC as Frequency, aq as GetDeletedStaffMemberRequest, ar as GetDeletedStaffMemberResponse, ao as GetStaffMemberRequest, ap as GetStaffMemberResponse, b6 as GroupByValueResults, cn as IdentificationData, co as IdentificationDataIdOneOf, I as IdentityType, ct as IdentityTypeWithLiterals, bB as Interval, cd as IntervalSplit, bf as ItemMetadata, bD as LinkedSchedule, as as ListDeletedStaffMembersRequest, au as ListDeletedStaffMembersResponse, c3 as LocalDateTime, bE as Location, a3 as LocationOptions, J as LocationStatus, cF as LocationStatusWithLiterals, L as LocationType, cE as LocationTypeWithLiterals, bL as LocationsAddress, bN as LocationsAddressLocation, bK as LocationsLocation, K as LocationsLocationType, cG as LocationsLocationTypeWithLiterals, bM as LocationsStreetAddress, _ as MediaItem, $ as MediaItemMediaOneOf, cm as MessageEnvelope, ck as MigrationData, cj as MigrationEvent, M as Mode, cy as ModeWithLiterals, cg as MultipleSessionsCreated, aV as NestedAggregationResults, aW as NestedAggregationResultsResultOneOf, b2 as NestedResultValue, b3 as NestedResultValueResultOneOf, b8 as NestedResults, a_ as NestedValueAggregationResult, bW as Participant, c6 as ParticipantNotification, bn as PolicyRemovedFromContributor, bo as PolicyUpdatedForContributor, bS as Price, aK as QueryStaffMembersMultiLanguageRequest, aL as QueryStaffMembersMultiLanguageResponse, aF as QueryStaffMembersRequest, aJ as QueryStaffMembersResponse, aU as RangeAggregationResult, b0 as RangeResult, aY as RangeResults, bR as Rate, bA as RecurringInterval, H as RecurringIntervalType, cD as RecurringIntervalTypeWithLiterals, ce as RecurringSessionSplit, c0 as RecurringSessionsUpdated, ax as RemoveStaffMemberFromTrashBinRequest, ay as RemoveStaffMemberFromTrashBinResponse, R as RequestedFields, cw as RequestedFieldsWithLiterals, a0 as Resource, bs as RestoreInfo, az as RestoreStaffMemberFromTrashBinRequest, aA as RestoreStaffMemberFromTrashBinResponse, bk as RestoreStaffRequest, bl as RestoreStaffResponse, b4 as Results, b1 as ScalarResult, z as ScalarType, cz as ScalarTypeWithLiterals, bz as Schedule, c7 as ScheduleCancelled, by as ScheduleCreated, bw as ScheduleNotification, bx as ScheduleNotificationEventOneOf, P as ScheduleStatus, cJ as ScheduleStatusWithLiterals, cf as ScheduleUnassignedFromUser, b$ as ScheduleUpdated, ch as ScheduleWithSessions, aR as SearchDetails, aO as SearchStaffMembersRequest, c1 as Session, ca as SessionCancelled, c8 as SessionCreated, Y as SessionType, cN as SessionTypeWithLiterals, c9 as SessionUpdated, c5 as SessionVersion, ci as SitePropertiesOnScheduleCreation, y as SortOrder, cx as SortOrderWithLiterals, aI as Sorting, bQ as SpecialHourPeriod, a4 as SpecificLocation, bV as SplitInterval, cl as StaffData, ak as StaffMemberConnectedToUser, aj as StaffMemberDisconnectedFromUser, al as StaffMemberFullyCreated, cs as StaffMemberSearchSpec, cr as StaffMembersQueryResult, X as Status, cM as StatusWithLiterals, bH as StreetAddress, bJ as Subdivision, ai as TagList, ah as Tags, bP as TimePeriod, T as Transparency, cC as TransparencyWithLiterals, aB as UpdateStaffMemberRequest, aC as UpdateStaffMemberResponse, aT as ValueAggregationResult, a$ as ValueResult, aX as ValueResults, bY as Version, Z as WebhookIdentityType, cO as WebhookIdentityTypeWithLiterals, a1 as WorkingHoursSchedule } from './bookings-staff-v1-staff-member-staff-members.universal-Tbhk-dK0.js';

declare function createStaffMember$1(httpClient: HttpClient): CreateStaffMemberSignature;
interface CreateStaffMemberSignature {
    /**
     * Creates a staff member.
     *
     *
     * By default, the staff member works during the business working hours. You
     * could follow *this sample flow*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))
     * to set custom working hours.
     * @param - Staff member to create.
     * @param - Options for creating the staff member.
     * @returns Created staff member.
     */
    (staffMember: NonNullablePaths<StaffMember, `name`, 2>, options?: CreateStaffMemberOptions): Promise<NonNullablePaths<StaffMember, `resource.workingHoursSchedules` | `resource.workingHoursSchedules.${number}.shared` | `resource.usesDefaultWorkingHours` | `associatedWixIdentity.identificationData.anonymousVisitorId` | `associatedWixIdentity.identificationData.memberId` | `associatedWixIdentity.identificationData.wixUserId` | `associatedWixIdentity.identificationData.appId` | `associatedWixIdentity.connectionStatus` | `associatedWixIdentity.connection.status` | `tags.privateTags.tagIds`, 5>>;
}
declare function getStaffMember$1(httpClient: HttpClient): GetStaffMemberSignature;
interface GetStaffMemberSignature {
    /**
     * Retrieves a staff member.
     * @param - ID of the staff member to retrieve.
     * @param - Option for retrieving the staff member.
     * @returns Retrieved staff member.
     */
    (staffMemberId: string, options?: GetStaffMemberOptions): Promise<NonNullablePaths<StaffMember, `resource.workingHoursSchedules` | `resource.workingHoursSchedules.${number}.shared` | `resource.usesDefaultWorkingHours` | `associatedWixIdentity.identificationData.anonymousVisitorId` | `associatedWixIdentity.identificationData.memberId` | `associatedWixIdentity.identificationData.wixUserId` | `associatedWixIdentity.identificationData.appId` | `associatedWixIdentity.connectionStatus` | `associatedWixIdentity.connection.status` | `tags.privateTags.tagIds`, 5>>;
}
declare function updateStaffMember$1(httpClient: HttpClient): UpdateStaffMemberSignature;
interface UpdateStaffMemberSignature {
    /**
     * Updates a staff member.
     *
     *
     * Each time the staff member is updated, `revision` increments by 1. You must
     * include current revision of the staff member when updating it. This ensures
     * you're working with the latest service information and prevents unintended
     * overwrites.
     * @param - Staff member ID.
     * @param - The staff member to update
     * @param - Options for updating the staff member.
     * @returns Updated staff member.
     */
    (_id: string, staffMember: NonNullablePaths<UpdateStaffMember, `revision`, 2>, options?: UpdateStaffMemberOptions): Promise<NonNullablePaths<StaffMember, `resource.workingHoursSchedules` | `resource.workingHoursSchedules.${number}.shared` | `resource.usesDefaultWorkingHours` | `associatedWixIdentity.identificationData.anonymousVisitorId` | `associatedWixIdentity.identificationData.memberId` | `associatedWixIdentity.identificationData.wixUserId` | `associatedWixIdentity.identificationData.appId` | `associatedWixIdentity.connectionStatus` | `associatedWixIdentity.connection.status` | `tags.privateTags.tagIds`, 5>>;
}
declare function deleteStaffMember$1(httpClient: HttpClient): DeleteStaffMemberSignature;
interface DeleteStaffMemberSignature {
    /**
     * Deletes a staff member.
     *
     *
     * Also deletes the *resource*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
     * associated with the staff member.
     * @param - ID of the staff member to delete.
     */
    (staffMemberId: string): Promise<void>;
}
declare function queryStaffMembers$1(httpClient: HttpClient): QueryStaffMembersSignature;
interface QueryStaffMembersSignature {
    /**
     * Creates a query to retrieve a list of staff members.
     *
     * The `queryStaffMembers()` function builds a query to retrieve a list of staff members and returns a `StaffMembersQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-builder/find) function.
     *
     * You can refine the query by chaining `StaffMembersQueryBuilder` functions onto the query. `StaffMembersQueryBuilder` functions enable you to sort, filter, and control the results that `queryStaffMembers()` returns.
     *
     * `queryStaffMembers()` runs with the following `StaffMembersQueryBuilder` default that you can override:
     *
     * + `limit` is `50`.
     * + Sorted by `id` in ascending order.
     *
     * The functions that are chained to `staffMembersTypes()` are applied in the order they are called.
     *
     * The following `StaffMembersQueryBuilder` functions are supported for the `queryStaffMembers()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-result/items) property in `StaffMembersQueryResult`.
     */
    (options?: QueryStaffMembersOptions): StaffMembersQueryBuilder;
}
declare function countStaffMembers$1(httpClient: HttpClient): CountStaffMembersSignature;
interface CountStaffMembersSignature {
    /**
     * Counts how many staff members match the given filter.
     *
     *
     * ## Filter
     *
     * Refer to the _supported filters article_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters and sorting options.
     *
     * ## See also
     *
     * To learn about working with filters in general, see
     * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).
     */
    (options?: CountStaffMembersOptions): Promise<NonNullablePaths<CountStaffMembersResponse, `count`, 2>>;
}
declare function connectStaffMemberToUser$1(httpClient: HttpClient): ConnectStaffMemberToUserSignature;
interface ConnectStaffMemberToUserSignature {
    /**
     * Connects a Wix user to a staff member, enabling them to manage their own
     * working hour schedule in the dashboard.
     *
     *
     * By default, Wix Bookings uses the staff member's `email`. However, you can
     * specify an alternative email address. If no existing Wix user is associated
     * with that email, Wix sends them an invitation to become a Wix user. If an
     * existing user is found but not linked to the site, Wix Bookings sends an
     * invitation to join the site.
     *
     * To check the connection status, call *Get Staff Member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/get-staff-member) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/get-staff-member)),
     * and specify `ASSOCIATED_IDENTITY_STATUS` in the `fields` parameter.
     *
     * You must call *Disconnect Staff Member From User*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/disconnect-staff-member-from-user) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/disconnect-staff-member-from-user))
     * before connecting a different Wix user to the staff member.
     * @param - ID of the staff member to connect to the Wix user.
     * @param - Options for connecting the staff member to a Wix user.
     */
    (staffMemberId: string, options?: ConnectStaffMemberToUserOptions): Promise<NonNullablePaths<ConnectStaffMemberToUserResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
}
declare function searchStaffMembers$1(httpClient: HttpClient): SearchStaffMembersSignature;
interface SearchStaffMembersSignature {
    /**
     * Retrieves a list of up to 100 staff members, given the provided filtering, paging,
     * and sorting.
     *
     *
     * ## Defaults
     *
     * Search Staff Members has the following default settings, which you can override:
     * + Sorted by `createdDate` in ascending order.
     * + `cursorPaging.limit` set to `100`.
     *
     * ## Filter
     *
     * Refer to the _supported filters article_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters and sorting options.
     *
     * ## See also
     *
     * To learn about working with *Search* methods, see
     * _API Query Language_
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))
     * and _Sorting and Paging_
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).
     * @param - Search criteria including filter, sort, and paging options.
     *
     * See the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)) for complete filter and sorting options.
     */
    (search: StaffMemberSearch, options?: SearchStaffMembersOptions): Promise<NonNullablePaths<SearchStaffMembersResponse, `staffMembers` | `staffMembers.${number}.resource.usesDefaultWorkingHours` | `staffMembers.${number}.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMembers.${number}.associatedWixIdentity.identificationData.memberId` | `staffMembers.${number}.associatedWixIdentity.identificationData.wixUserId` | `staffMembers.${number}.associatedWixIdentity.identificationData.appId` | `staffMembers.${number}.associatedWixIdentity.connectionStatus` | `staffMembers.${number}.associatedWixIdentity.connection.status` | `aggregationData.results` | `aggregationData.results.${number}.scalar.type` | `aggregationData.results.${number}.scalar.value` | `aggregationData.results.${number}.name` | `aggregationData.results.${number}.type` | `aggregationData.results.${number}.fieldPath`, 6>>;
}
declare function disconnectStaffMemberFromUser$1(httpClient: HttpClient): DisconnectStaffMemberFromUserSignature;
interface DisconnectStaffMemberFromUserSignature {
    /**
     * Disconnects a staff member from a Wix user, clearing the `associatedWixIdentity`
     * field. Consequently, the user can no longer manage the staff member's working
     * hour schedule in the dashboard.
     *
     *
     * You must call *Disconnect Staff Member From User* before connecting a
     * different Wix user to the staff member.
     * @param - ID of the staff member to disconnect from its Wix user.
     * @param - Options to disconnect the staff member from a Wix user.
     */
    (staffMemberId: string, options?: DisconnectStaffMemberFromUserOptions): Promise<NonNullablePaths<DisconnectStaffMemberFromUserResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
}
declare function assignWorkingHoursSchedule$1(httpClient: HttpClient): AssignWorkingHoursScheduleSignature;
interface AssignWorkingHoursScheduleSignature {
    /**
     * Updates a staff member's working hours based on the specified *schedule ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     *
     * ## Default working hours
     *
     * By default, staff members work during the opening hours of the business's
     * _default location_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     *
     * ## Schedule ID
     *
     * You can specify either the ID of the business's working hour schedule or the
     * staff member's event schedule. The call fails if you specify a different
     * schedule ID.
     *
     * ### Staff event schedule
     *
     * To customize a staff member's working hours, specify their event schedule ID
     * as `scheduleId`. Refer to *this sample flow*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))
     * for more details.
     *
     * ### Business working hour schedule
     *
     * To reset a staff member's working hours to the default business hours,
     * specify the ID of the business working hour schedule as `scheduleId`.
     * @param - ID of the staff member to assign the schedule to.
     * @param - ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to assign to the staff member.
     *
     * Must be either the staff member's event schedule ID or the working hour
     * schedule ID for a business location.
     * @param - Options for setting the staff member's working hours schedule.
     */
    (staffMemberId: string, scheduleId: string, options?: AssignWorkingHoursScheduleOptions): Promise<NonNullablePaths<AssignWorkingHoursScheduleResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
}
declare function assignCustomSchedule$1(httpClient: HttpClient): AssignCustomScheduleSignature;
interface AssignCustomScheduleSignature {
    /**
     * Assigns a custom working hours schedule to the staff member
     *
     *
     * The working hours schedule is a schedule that defines the working hours of a staff member,
     * and dictate when the staff member is available for bookings.
     *
     * By default staff members use the shared business working hours schedule. By assigning a custom working hours schedule to a staff member,
     * you can define specific working hours for that staff member.
     *
     * To create and manage schedules and working hours sessions, use [Events API](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction).
     * @param - ID of the staff member for which to assign a working hour schedule.
     * @param - ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to assign to the staff member.
     * @deprecated
     */
    (staffMemberId: string, scheduleId: string, options?: AssignCustomScheduleOptions): Promise<NonNullablePaths<AssignCustomScheduleResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
}
declare function bulkUpdateStaffMemberTags$1(httpClient: HttpClient): BulkUpdateStaffMemberTagsSignature;
interface BulkUpdateStaffMemberTagsSignature {
    /**
     * Synchronously updates *tags*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).
     * for up to 100 staff members.
     *
     *
     * If you specify a tag both in `assignTags` and `unassignTags`, the call
     * succeeds and the tag is assigned.
     * @param - IDs of staff members to update tags for.
     */
    (ids: string[], options?: BulkUpdateStaffMemberTagsOptions): Promise<NonNullablePaths<BulkUpdateStaffMemberTagsResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
}
declare function bulkUpdateStaffMemberTagsByFilter$1(httpClient: HttpClient): BulkUpdateStaffMemberTagsByFilterSignature;
interface BulkUpdateStaffMemberTagsByFilterSignature {
    /**
     * Asynchronously updates *tags*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).
     * for staff members, given the provided filtering.
     *
     *
     * If you specify a tag both in `assignTags` and `unassignTags`, the call
     * succeeds and the tag is assigned.
     *
     * ## Filter
     *
     * Refer to the supported filters article
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters and sorting options.
     * @param - Filter to base the update upon. See the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    (filter: Record<string, any>, options?: BulkUpdateStaffMemberTagsByFilterOptions): Promise<NonNullablePaths<BulkUpdateStaffMemberTagsByFilterResponse, `jobId`, 2>>;
}
declare const onStaffMemberConnectedToUser$1: EventDefinition<StaffMemberConnectedToUserEnvelope, "wix.bookings.staff.v1.staff_member_connected_to_user">;
declare const onStaffMemberCreated$1: EventDefinition<StaffMemberCreatedEnvelope, "wix.bookings.staff.v1.staff_member_created">;
declare const onStaffMemberDeleted$1: EventDefinition<StaffMemberDeletedEnvelope, "wix.bookings.staff.v1.staff_member_deleted">;
declare const onStaffMemberDisconnectedFromUser$1: EventDefinition<StaffMemberDisconnectedFromUserEnvelope, "wix.bookings.staff.v1.staff_member_disconnected_from_user">;
declare const onStaffMemberFullyCreated$1: EventDefinition<StaffMemberFullyCreatedEnvelope, "wix.bookings.staff.v1.staff_member_fully_created">;
declare const onStaffMemberUpdated$1: EventDefinition<StaffMemberUpdatedEnvelope, "wix.bookings.staff.v1.staff_member_updated">;

declare const createStaffMember: MaybeContext<BuildRESTFunction<typeof createStaffMember$1> & typeof createStaffMember$1>;
declare const getStaffMember: MaybeContext<BuildRESTFunction<typeof getStaffMember$1> & typeof getStaffMember$1>;
declare const updateStaffMember: MaybeContext<BuildRESTFunction<typeof updateStaffMember$1> & typeof updateStaffMember$1>;
declare const deleteStaffMember: MaybeContext<BuildRESTFunction<typeof deleteStaffMember$1> & typeof deleteStaffMember$1>;
declare const queryStaffMembers: MaybeContext<BuildRESTFunction<typeof queryStaffMembers$1> & typeof queryStaffMembers$1>;
declare const countStaffMembers: MaybeContext<BuildRESTFunction<typeof countStaffMembers$1> & typeof countStaffMembers$1>;
declare const connectStaffMemberToUser: MaybeContext<BuildRESTFunction<typeof connectStaffMemberToUser$1> & typeof connectStaffMemberToUser$1>;
declare const searchStaffMembers: MaybeContext<BuildRESTFunction<typeof searchStaffMembers$1> & typeof searchStaffMembers$1>;
declare const disconnectStaffMemberFromUser: MaybeContext<BuildRESTFunction<typeof disconnectStaffMemberFromUser$1> & typeof disconnectStaffMemberFromUser$1>;
declare const assignWorkingHoursSchedule: MaybeContext<BuildRESTFunction<typeof assignWorkingHoursSchedule$1> & typeof assignWorkingHoursSchedule$1>;
declare const assignCustomSchedule: MaybeContext<BuildRESTFunction<typeof assignCustomSchedule$1> & typeof assignCustomSchedule$1>;
declare const bulkUpdateStaffMemberTags: MaybeContext<BuildRESTFunction<typeof bulkUpdateStaffMemberTags$1> & typeof bulkUpdateStaffMemberTags$1>;
declare const bulkUpdateStaffMemberTagsByFilter: MaybeContext<BuildRESTFunction<typeof bulkUpdateStaffMemberTagsByFilter$1> & typeof bulkUpdateStaffMemberTagsByFilter$1>;
/**
 * Triggered when a Wix user is connected to a staff member.
 *
 *
 * The event is triggered when the connection status changes to `CONNECTED`. It
 * isn't triggered when Wix Bookings sends an invitation to become a Wix user or
 * join a site.
 */
declare const onStaffMemberConnectedToUser: BuildEventDefinition<typeof onStaffMemberConnectedToUser$1>;
/**
 * Triggered when a staff member is created.
 */
declare const onStaffMemberCreated: BuildEventDefinition<typeof onStaffMemberCreated$1>;
/**
 * Triggered when a staff member is deleted.
 */
declare const onStaffMemberDeleted: BuildEventDefinition<typeof onStaffMemberDeleted$1>;
/**
 * Triggered when a Wix user is disconnected from a staff member.
 */
declare const onStaffMemberDisconnectedFromUser: BuildEventDefinition<typeof onStaffMemberDisconnectedFromUser$1>;
/**
 * Triggered when a staff member is created.
 */
declare const onStaffMemberFullyCreated: BuildEventDefinition<typeof onStaffMemberFullyCreated$1>;
/**
 * Triggered when a staff member is updated.
 */
declare const onStaffMemberUpdated: BuildEventDefinition<typeof onStaffMemberUpdated$1>;

export { AssignCustomScheduleOptions, AssignCustomScheduleResponse, AssignWorkingHoursScheduleOptions, AssignWorkingHoursScheduleResponse, BulkUpdateStaffMemberTagsByFilterOptions, BulkUpdateStaffMemberTagsByFilterResponse, BulkUpdateStaffMemberTagsOptions, BulkUpdateStaffMemberTagsResponse, ConnectStaffMemberToUserOptions, ConnectStaffMemberToUserResponse, CountStaffMembersOptions, CountStaffMembersResponse, CreateStaffMemberOptions, DisconnectStaffMemberFromUserOptions, DisconnectStaffMemberFromUserResponse, GetStaffMemberOptions, QueryStaffMembersOptions, SearchStaffMembersOptions, SearchStaffMembersResponse, StaffMember, StaffMemberConnectedToUserEnvelope, StaffMemberCreatedEnvelope, StaffMemberDeletedEnvelope, StaffMemberDisconnectedFromUserEnvelope, StaffMemberFullyCreatedEnvelope, StaffMemberSearch, StaffMemberUpdatedEnvelope, StaffMembersQueryBuilder, UpdateStaffMember, UpdateStaffMemberOptions, assignCustomSchedule, assignWorkingHoursSchedule, bulkUpdateStaffMemberTags, bulkUpdateStaffMemberTagsByFilter, connectStaffMemberToUser, countStaffMembers, createStaffMember, deleteStaffMember, disconnectStaffMemberFromUser, getStaffMember, onStaffMemberConnectedToUser, onStaffMemberCreated, onStaffMemberDeleted, onStaffMemberDisconnectedFromUser, onStaffMemberFullyCreated, onStaffMemberUpdated, queryStaffMembers, searchStaffMembers, updateStaffMember };
