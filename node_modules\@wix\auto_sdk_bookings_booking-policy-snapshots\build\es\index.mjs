// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      },
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policy-snapshots";
function listPolicySnapshotsByBookingIds(payload) {
  function __listPolicySnapshotsByBookingIds({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.policy_snapshots.v1.booking_policy_snapshot",
      method: "GET",
      methodFqn: "com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(
        { protoPath: "/v1/policy-snapshots", data: payload, host }
      ),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicySnapshots.createdDate" },
            { path: "bookingPolicySnapshots.policy.createdDate" },
            { path: "bookingPolicySnapshots.policy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listPolicySnapshotsByBookingIds;
}

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.ts
async function listPolicySnapshotsByBookingIds2(bookingIds) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    bookingIds
  });
  const reqOpts = listPolicySnapshotsByBookingIds(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingIds: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingIds"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.public.ts
function listPolicySnapshotsByBookingIds3(httpClient) {
  return (bookingIds) => listPolicySnapshotsByBookingIds2(
    bookingIds,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
var listPolicySnapshotsByBookingIds4 = /* @__PURE__ */ createRESTModule(listPolicySnapshotsByBookingIds3);
export {
  listPolicySnapshotsByBookingIds4 as listPolicySnapshotsByBookingIds
};
//# sourceMappingURL=index.mjs.map