{"version": 3, "sources": ["../../index.typings.ts", "../../src/data-v2-backup-backups.universal.ts", "../../src/data-v2-backup-backups.http.ts"], "sourcesContent": ["export * from './src/data-v2-backup-backups.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixDataV2Backup from './data-v2-backup-backups.http.js';\n\nexport interface Backup {\n  /**\n   * Backup ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string;\n  /**\n   * Backup status.\n   * @readonly\n   */\n  status?: StatusWithLiterals;\n  /**\n   * Type of backup, based on how it was triggered.\n   * @readonly\n   */\n  type?: TypeWithLiterals;\n  /**\n   * Date and time the backup was requested.\n   * @readonly\n   */\n  requestedDate?: Date | null;\n  /**\n   * Date and time the backup commenced. Value is `null` until the backup process begins in the background.\n   * @readonly\n   */\n  startedDate?: Date | null;\n  /**\n   * Date and time the backup process finished. Value is `null` until the backup process is completed in the background.\n   * @readonly\n   */\n  finishedDate?: Date | null;\n  /**\n   * Date and time the backup was deleted. Value is `null` if that backup hasn't been deleted.\n   * @readonly\n   */\n  deletedDate?: Date | null;\n  /**\n   * Backup size in bytes. Value is `null` until the backup process is completed.\n   * @readonly\n   */\n  sizeInBytes?: string | null;\n  /**\n   * IDs and display names of collections the backup contains.\n   * @readonly\n   * @maxSize 1000\n   */\n  collections?: Collection[];\n}\n\nexport enum Status {\n  /** Backup creation is in progress. */\n  PENDING = 'PENDING',\n  /** Backup has been created successfully and can be used for data restoration. */\n  READY = 'READY',\n  /** Backup creation has failed. */\n  FAILED = 'FAILED',\n  /** Backup has been deleted. */\n  DELETED = 'DELETED',\n  /** Backup has been canceled. */\n  CANCELED = 'CANCELED',\n}\n\n/** @enumType */\nexport type StatusWithLiterals =\n  | Status\n  | 'PENDING'\n  | 'READY'\n  | 'FAILED'\n  | 'DELETED'\n  | 'CANCELED';\n\nexport enum Type {\n  /** Backup taken on demand. */\n  ON_DEMAND = 'ON_DEMAND',\n  /** Backup taken automatically by the system on a regular schedule. */\n  AUTO = 'AUTO',\n}\n\n/** @enumType */\nexport type TypeWithLiterals = Type | 'ON_DEMAND' | 'AUTO';\n\nexport interface Collection {\n  /**\n   * Collection ID.\n   * @readonly\n   * @maxLength 255\n   */\n  _id?: string;\n  /**\n   * Collection display name.\n   * @readonly\n   * @maxLength 1000\n   */\n  displayName?: string | null;\n}\n\n/** event triggered when backup state is changed (i.e. it was created, started, completed or failed) */\nexport interface BackupStateChanged {\n  /**\n   * current state of backup\n   * @readonly\n   */\n  backup?: Backup;\n}\n\n/** event triggered when backup restoration state is changed (i.e. it was created, started or completed) */\nexport interface RestorationStateChanged {\n  /**\n   * current state of restoration\n   * @readonly\n   */\n  restoration?: Restoration;\n}\n\nexport interface Restoration {\n  /**\n   * Restoration ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string;\n  /**\n   * Details of the backup used for the restoration.\n   * @readonly\n   */\n  backup?: Backup;\n  /**\n   * Status of restoration.\n   * @readonly\n   */\n  status?: RestorationStatusWithLiterals;\n  /**\n   * Date and time the restoration was requested.\n   * @readonly\n   */\n  requestedDate?: Date | null;\n  /**\n   * Date and time the restoration commenced. Value is `null` until the restoration process begins in the background.\n   * @readonly\n   */\n  startedDate?: Date | null;\n  /**\n   * Date and time the restoration finished. Value is `null` until the restoration process is completed in the background.\n   * @readonly\n   */\n  finishedDate?: Date | null;\n  /**\n   * Restored collections.\n   * @readonly\n   * @maxSize 1000\n   */\n  restorationCollections?: RestorationCollection[];\n}\n\nexport enum RestorationStatus {\n  /** Restoration from a backup is in progress. */\n  PENDING = 'PENDING',\n  /** Restoration from a backup has been successful. */\n  COMPLETED = 'COMPLETED',\n  /** Restoration from a backup has failed. */\n  FAILED = 'FAILED',\n}\n\n/** @enumType */\nexport type RestorationStatusWithLiterals =\n  | RestorationStatus\n  | 'PENDING'\n  | 'COMPLETED'\n  | 'FAILED';\n\nexport interface RestorationCollection {\n  /**\n   * Collections to be restored.\n   *\n   * Note: If collections have a multi-reference relationship,\n   * the preexisting references will be restored if at least one of those collections are restored.\n   * @minLength 1\n   * @maxLength 1000\n   */\n  dataCollectionId?: string;\n  /**\n   * Destination where to restore the collection.\n   * When not specified destination is taken from backup.\n   */\n  restoreDestination?: RestoreDestination;\n}\n\nexport interface RestoreDestination {\n  /**\n   * Collection ID.\n   * @minLength 1\n   * @maxLength 255\n   */\n  dataCollectionId?: string;\n  /**\n   * Collection's display name as shown in the CMS. If empty, `displayName` is taken from `backup.collections`.\n   * @maxLength 1000\n   */\n  displayName?: string | null;\n}\n\nexport interface DisableInstanceRequest {\n  /** @format GUID */\n  instanceId?: string;\n}\n\nexport interface Empty {}\n\nexport interface EnableInstanceRequest {\n  /** @format GUID */\n  instanceId?: string;\n}\n\nexport interface DeleteAllRequest {\n  /** @format GUID */\n  instanceId?: string;\n}\n\nexport interface GetBackupMetadataRequest {\n  /** @format GUID */\n  backupId?: string;\n}\n\nexport interface GetBackupMetadataResponse {\n  metaData?: Record<string, any> | null;\n}\n\nexport interface RebuildRequest {\n  /** @format GUID */\n  backupId?: string;\n}\n\nexport interface MoveRequest {\n  /** @format GUID */\n  backupId?: string;\n}\n\nexport interface RemoveDeletedRequest {\n  limit?: number;\n}\n\nexport interface FailRestorationRequest {\n  /** @format GUID */\n  restorationId?: string;\n}\n\nexport interface TakeBackupRequest {\n  /** @format GUID */\n  instanceId?: string;\n  type?: TypeWithLiterals;\n}\n\nexport interface UpdateBackupMetadataRequest {\n  /** @format GUID */\n  backupId?: string;\n  s3Region?: string;\n  s3Bucket?: string;\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\nexport interface CreateBackupRequest {}\n\nexport interface CreateBackupResponse {\n  /** Details of the requested backup. */\n  backup?: Backup;\n}\n\nexport interface ListBackupsRequest {\n  /**\n   * Statuses to filter by.\n   * If provided, only backups with the specified statuses are listed.\n   * For example, to list only completed backups, use `?status=READY`.\n   * To list completed and pending backups, use `?status=READY&status=PENDING`.\n   *\n   * Default: No filtering\n   * @maxSize 10\n   */\n  status?: StatusWithLiterals[];\n  /**\n   * Type to filter by. If provided, only backups of the specified type are listed.\n   *\n   * Default: No filtering\n   * @maxSize 10\n   */\n  type?: TypeWithLiterals[];\n  /** Paging preferences. */\n  paging?: Paging;\n}\n\nexport interface Paging {\n  /** Number of items to load. */\n  limit?: number | null;\n  /** Number of items to skip in the current sort order. */\n  offset?: number | null;\n}\n\nexport interface ListBackupsResponse {\n  /**\n   * Retrieved backups.\n   * @maxSize 1000\n   */\n  backups?: Backup[];\n  /** Paging information. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface PagingMetadataV2 {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  offset?: number | null;\n  /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */\n  total?: number | null;\n  /** Flag that indicates the server failed to calculate the `total` field. */\n  tooManyToCount?: boolean | null;\n}\n\nexport interface RestoreBackupRequest {\n  /**\n   * ID of backup to be restored.\n   * @format GUID\n   */\n  backupId: string;\n}\n\nexport interface RestoreBackupResponse {\n  /** Details of data restoration from backup. */\n  restoration?: Restoration;\n}\n\nexport interface RestorePartialBackupRequest {\n  /**\n   * ID of backup to be restored.\n   * @format GUID\n   */\n  backupId: string;\n  /**\n   * **Required.** Collections to be restored.\n   *\n   * Note: If collections have a multi-reference relationship,\n   * the preexisting references will be restored if at least one of those collections are restored.\n   * @maxSize 1000\n   */\n  restorationCollections?: RestorationCollection[];\n}\n\nexport interface RestorePartialBackupResponse {\n  /** Details of data restoration from backup. */\n  restoration?: Restoration;\n}\n\nexport interface ListRestorationsRequest {\n  /**\n   * Statuses to filter by. If provided, only restorations with the specified statuses are listed.\n   * For example, to list only completed restorations, use `?status=COMPLETED`.\n   * To list completed and pending restorations, use `?status=COMPLETED&status=PENDING`.\n   *\n   * Default: No filtering\n   * @maxSize 10\n   */\n  status?: RestorationStatusWithLiterals[];\n  /** Offset and limit of items to retrieve. */\n  paging?: Paging;\n}\n\nexport interface ListRestorationsResponse {\n  /**\n   * Retrieved restorations.\n   * @maxSize 1000\n   */\n  restorations?: Restoration[];\n  /** Paging information. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface DeleteBackupRequest {\n  /**\n   * ID of the backup to be deleted.\n   * @format GUID\n   */\n  backupId: string;\n}\n\nexport interface DeleteBackupResponse {}\n\nexport interface CancelBackupRequest {\n  /**\n   * ID of the backup to be cancelled.\n   * @format GUID\n   */\n  backupId?: string;\n}\n\nexport interface CancelBackupResponse {}\n\nexport interface MigrateNamespaceRequest {\n  /** @maxLength 255 */\n  newNamespace?: string;\n  /** @maxLength 255 */\n  existingNamespace?: string;\n}\n\nexport interface MigrateNamespaceResponse {}\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface BackupStateChangedEnvelope {\n  data: BackupStateChanged;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a backup's status is changed. This includes when a backup is created and its status is set to `PENDING`.\n * @permissionScope Manage Data Backups\n * @permissionScopeId SCOPE.DC-DATA.MANAGE-BACKUPS\n * @permissionId WIX_DATA.LIST_BACKUPS\n * @webhook\n * @eventType wix.data.v2.backup_backup_state_changed\n * @slug backup_state_changed\n */\nexport declare function onBackupStateChanged(\n  handler: (event: BackupStateChangedEnvelope) => void | Promise<void>\n): void;\n\nexport interface BackupRestorationStateChangedEnvelope {\n  data: RestorationStateChanged;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a restoration's status is changed. This includes when a new restoration is initiated and its status is `PENDING`.\n * @permissionScope Manage Data Backups\n * @permissionScopeId SCOPE.DC-DATA.MANAGE-BACKUPS\n * @permissionId WIX_DATA.LIST_BACKUPS\n * @webhook\n * @eventType wix.data.v2.backup_restoration_state_changed\n * @slug restoration_state_changed\n */\nexport declare function onBackupRestorationStateChanged(\n  handler: (\n    event: BackupRestorationStateChangedEnvelope\n  ) => void | Promise<void>\n): void;\n\n/**\n * Creates an on-demand backup of live content in a site's collections.\n *\n *\n * By default, all of the site's collections are included in the backup. For a partial backup, specify which collections to include in the `backup.collections` parameter.\n *\n * The process of creating a backup takes time.\n * You can check whether a backup has completed successfully with List Backups.\n *\n * You can store up to 3 on-demand backups for each site.\n * If 3 on-demand backups already exist, the oldest existing on-demand backup for the site is deleted when a new one is created. Automated backups are not affected.\n * @public\n * @permissionId WIX_DATA.CREATE_BACKUP\n * @applicableIdentity APP\n * @fqn com.wixpress.dstore.api.backup.v2.BackupService.CreateBackup\n */\nexport async function createBackup(): Promise<\n  NonNullablePaths<\n    CreateBackupResponse,\n    | `backup._id`\n    | `backup.status`\n    | `backup.type`\n    | `backup.collections`\n    | `backup.collections.${number}._id`,\n    5\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({});\n\n  const reqOpts = ambassadorWixDataV2Backup.createBackup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {},\n        singleArgumentUnchanged: false,\n      },\n      []\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a list of all backups for a site.\n *\n * Results are sorted by requested date, with the newest first.\n *\n * You can use this method to check whether a backup initiated with Create Backup has been completed successfully.\n * @public\n * @permissionId WIX_DATA.LIST_BACKUPS\n * @applicableIdentity APP\n * @fqn com.wixpress.dstore.api.backup.v2.BackupService.ListBackups\n */\nexport async function listBackups(\n  options?: ListBackupsOptions\n): Promise<\n  NonNullablePaths<\n    ListBackupsResponse,\n    | `backups`\n    | `backups.${number}._id`\n    | `backups.${number}.status`\n    | `backups.${number}.type`,\n    4\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    status: options?.status,\n    type: options?.type,\n    paging: options?.paging,\n  });\n\n  const reqOpts = ambassadorWixDataV2Backup.listBackups(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          status: '$[0].status',\n          type: '$[0].type',\n          paging: '$[0].paging',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListBackupsOptions {\n  /**\n   * Statuses to filter by.\n   * If provided, only backups with the specified statuses are listed.\n   * For example, to list only completed backups, use `?status=READY`.\n   * To list completed and pending backups, use `?status=READY&status=PENDING`.\n   *\n   * Default: No filtering\n   * @maxSize 10\n   */\n  status?: StatusWithLiterals[];\n  /**\n   * Type to filter by. If provided, only backups of the specified type are listed.\n   *\n   * Default: No filtering\n   * @maxSize 10\n   */\n  type?: TypeWithLiterals[];\n  /** Paging preferences. */\n  paging?: Paging;\n}\n\n/**\n * Restores all data from a backup.\n *\n * The process of restoring data from a backup takes time.\n * You can check whether your restoration has completed successfully with List Restorations.\n * @param backupId - ID of backup to be restored.\n * @public\n * @requiredField backupId\n * @permissionId WIX_DATA.RESTORE_BACKUP\n * @applicableIdentity APP\n * @fqn com.wixpress.dstore.api.backup.v2.BackupService.RestoreBackup\n */\nexport async function restoreBackup(\n  backupId: string\n): Promise<\n  NonNullablePaths<\n    RestoreBackupResponse,\n    | `restoration._id`\n    | `restoration.backup._id`\n    | `restoration.backup.status`\n    | `restoration.backup.type`\n    | `restoration.backup.collections`\n    | `restoration.backup.collections.${number}._id`\n    | `restoration.status`\n    | `restoration.restorationCollections`\n    | `restoration.restorationCollections.${number}.dataCollectionId`\n    | `restoration.restorationCollections.${number}.restoreDestination.dataCollectionId`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ backupId: backupId });\n\n  const reqOpts = ambassadorWixDataV2Backup.restoreBackup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { backupId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['backupId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Restores specific collections from a backup.\n *\n * The process of restoring data from a backup takes time.\n * You can check whether your restoration has completed successfully with List Restorations.\n * @param backupId - ID of backup to be restored.\n * @param restorationCollections - **Required.** Collections to be restored.\n *\n * Note: If collections have a multi-reference relationship,\n * the preexisting references will be restored if at least one of those collections are restored.\n * @public\n * @requiredField backupId\n * @requiredField restorationCollections\n * @permissionId WIX_DATA.RESTORE_BACKUP\n * @applicableIdentity APP\n * @fqn com.wixpress.dstore.api.backup.v2.BackupService.RestorePartialBackup\n */\nexport async function restorePartialBackup(\n  backupId: string,\n  restorationCollections: RestorationCollection[]\n): Promise<\n  NonNullablePaths<\n    RestorePartialBackupResponse,\n    | `restoration._id`\n    | `restoration.backup._id`\n    | `restoration.backup.status`\n    | `restoration.backup.type`\n    | `restoration.backup.collections`\n    | `restoration.backup.collections.${number}._id`\n    | `restoration.status`\n    | `restoration.restorationCollections`\n    | `restoration.restorationCollections.${number}.dataCollectionId`\n    | `restoration.restorationCollections.${number}.restoreDestination.dataCollectionId`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    backupId: backupId,\n    restorationCollections: restorationCollections,\n  });\n\n  const reqOpts = ambassadorWixDataV2Backup.restorePartialBackup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          backupId: '$[0]',\n          restorationCollections: '$[1]',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['backupId', 'restorationCollections']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a list of all data restorations from backups.\n *\n * Results are sorted by requested date, with the newest first.\n *\n * You can use this method to check whether a restoration initiated with Restore Backup has been completed successfully.\n * @public\n * @permissionId WIX_DATA.LIST_RESTORATIONS\n * @applicableIdentity APP\n * @fqn com.wixpress.dstore.api.backup.v2.BackupService.ListRestorations\n */\nexport async function listRestorations(\n  options?: ListRestorationsOptions\n): Promise<\n  NonNullablePaths<\n    ListRestorationsResponse,\n    | `restorations`\n    | `restorations.${number}._id`\n    | `restorations.${number}.backup._id`\n    | `restorations.${number}.backup.status`\n    | `restorations.${number}.backup.type`\n    | `restorations.${number}.status`,\n    5\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    status: options?.status,\n    paging: options?.paging,\n  });\n\n  const reqOpts = ambassadorWixDataV2Backup.listRestorations(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          status: '$[0].status',\n          paging: '$[0].paging',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListRestorationsOptions {\n  /**\n   * Statuses to filter by. If provided, only restorations with the specified statuses are listed.\n   * For example, to list only completed restorations, use `?status=COMPLETED`.\n   * To list completed and pending restorations, use `?status=COMPLETED&status=PENDING`.\n   *\n   * Default: No filtering\n   * @maxSize 10\n   */\n  status?: RestorationStatusWithLiterals[];\n  /** Offset and limit of items to retrieve. */\n  paging?: Paging;\n}\n\n/**\n * Deletes a backup.\n *\n * The process of deleting a backup takes time.\n * You can check whether a backup has been deleted successfully with List Backups.\n * @param backupId - ID of the backup to be deleted.\n * @public\n * @requiredField backupId\n * @permissionId WIX_DATA.DELETE_BACKUP\n * @applicableIdentity APP\n * @fqn com.wixpress.dstore.api.backup.v2.BackupService.DeleteBackup\n */\nexport async function deleteBackup(backupId: string): Promise<void> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ backupId: backupId });\n\n  const reqOpts = ambassadorWixDataV2Backup.deleteBackup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { backupId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['backupId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressDstoreApiBackupV2BackupServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/data/backups',\n        destPath: '/api/v1/backups',\n      },\n      {\n        srcPath: '/_api/data/v2/backups',\n        destPath: '/v2/backups',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/data/backups',\n        destPath: '/api/v1/backups',\n      },\n      {\n        srcPath: '/_api/data/v2/backups',\n        destPath: '/v2/backups',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/data/backups',\n        destPath: '/api/v1/backups',\n      },\n      {\n        srcPath: '/_api/data/v2/backups',\n        destPath: '/v2/backups',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/data/backups',\n        destPath: '/api/v1/backups',\n      },\n      {\n        srcPath: '/_api/data/v2/backups',\n        destPath: '/v2/backups',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/data/backups',\n        destPath: '/api/v1/backups',\n      },\n      {\n        srcPath: '/_api/data/v2/backups',\n        destPath: '/v2/backups',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/wix-data/v1/backups',\n        destPath: '/api/v1/backups',\n      },\n      {\n        srcPath: '/wix-data/v2/backups',\n        destPath: '/v2/backups',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/data/v2/backups',\n        destPath: '/v2/backups',\n      },\n      {\n        srcPath: '/_api/data/backups',\n        destPath: '/api/v1/backups',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/data/v2/backups',\n        destPath: '/v2/backups',\n      },\n      {\n        srcPath: '/_api/data/backups',\n        destPath: '/api/v1/backups',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_data_backups';\n\n/**\n * Creates an on-demand backup of live content in a site's collections.\n *\n *\n * By default, all of the site's collections are included in the backup. For a partial backup, specify which collections to include in the `backup.collections` parameter.\n *\n * The process of creating a backup takes time.\n * You can check whether a backup has completed successfully with List Backups.\n *\n * You can store up to 3 on-demand backups for each site.\n * If 3 on-demand backups already exist, the oldest existing on-demand backup for the site is deleted when a new one is created. Automated backups are not affected.\n */\nexport function createBackup(payload: object): RequestOptionsFactory<any> {\n  function __createBackup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.data.v2.backup',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.dstore.api.backup.v2.BackupService.CreateBackup',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({\n        protoPath: '/v2/backups',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'backup.requestedDate' },\n              { path: 'backup.startedDate' },\n              { path: 'backup.finishedDate' },\n              { path: 'backup.deletedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createBackup;\n}\n\n/**\n * Retrieves a list of all backups for a site.\n *\n * Results are sorted by requested date, with the newest first.\n *\n * You can use this method to check whether a backup initiated with Create Backup has been completed successfully.\n */\nexport function listBackups(payload: object): RequestOptionsFactory<any> {\n  function __listBackups({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.data.v2.backup',\n      method: 'GET' as any,\n      methodFqn: 'com.wixpress.dstore.api.backup.v2.BackupService.ListBackups',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({\n        protoPath: '/v2/backups',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'backups.requestedDate' },\n              { path: 'backups.startedDate' },\n              { path: 'backups.finishedDate' },\n              { path: 'backups.deletedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listBackups;\n}\n\n/**\n * Restores all data from a backup.\n *\n * The process of restoring data from a backup takes time.\n * You can check whether your restoration has completed successfully with List Restorations.\n */\nexport function restoreBackup(payload: object): RequestOptionsFactory<any> {\n  function __restoreBackup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.data.v2.backup',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.dstore.api.backup.v2.BackupService.RestoreBackup',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({\n        protoPath: '/v2/backups/{backupId}/restore',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'restoration.requestedDate' },\n              { path: 'restoration.startedDate' },\n              { path: 'restoration.finishedDate' },\n              { path: 'restoration.backup.requestedDate' },\n              { path: 'restoration.backup.startedDate' },\n              { path: 'restoration.backup.finishedDate' },\n              { path: 'restoration.backup.deletedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __restoreBackup;\n}\n\n/**\n * Restores specific collections from a backup.\n *\n * The process of restoring data from a backup takes time.\n * You can check whether your restoration has completed successfully with List Restorations.\n */\nexport function restorePartialBackup(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __restorePartialBackup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.data.v2.backup',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.dstore.api.backup.v2.BackupService.RestorePartialBackup',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({\n        protoPath: '/v2/backups/{backupId}/partial-restore',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'restoration.requestedDate' },\n              { path: 'restoration.startedDate' },\n              { path: 'restoration.finishedDate' },\n              { path: 'restoration.backup.requestedDate' },\n              { path: 'restoration.backup.startedDate' },\n              { path: 'restoration.backup.finishedDate' },\n              { path: 'restoration.backup.deletedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __restorePartialBackup;\n}\n\n/**\n * Retrieves a list of all data restorations from backups.\n *\n * Results are sorted by requested date, with the newest first.\n *\n * You can use this method to check whether a restoration initiated with Restore Backup has been completed successfully.\n */\nexport function listRestorations(payload: object): RequestOptionsFactory<any> {\n  function __listRestorations({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.data.v2.backup',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.dstore.api.backup.v2.BackupService.ListRestorations',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({\n        protoPath: '/v2/backups/restorations',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'restorations.requestedDate' },\n              { path: 'restorations.startedDate' },\n              { path: 'restorations.finishedDate' },\n              { path: 'restorations.backup.requestedDate' },\n              { path: 'restorations.backup.startedDate' },\n              { path: 'restorations.backup.finishedDate' },\n              { path: 'restorations.backup.deletedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listRestorations;\n}\n\n/**\n * Deletes a backup.\n *\n * The process of deleting a backup takes time.\n * You can check whether a backup has been deleted successfully with List Backups.\n */\nexport function deleteBackup(payload: object): RequestOptionsFactory<any> {\n  function __deleteBackup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.data.v2.backup',\n      method: 'DELETE' as any,\n      methodFqn: 'com.wixpress.dstore.api.backup.v2.BackupService.DeleteBackup',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({\n        protoPath: '/v2/backups/{backupId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteBackup;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAAA;AAAA,EAAA,oBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,wBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,4BAAAC;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,oCAGO;;;ACJP,0BAAkC;AAClC,uBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,oDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAcd,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,qBAAqB;AAAA,YAC7B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,qBAAqB;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,YAAY,SAA6C;AACvE,WAAS,cAAc,EAAE,KAAK,GAAQ;AACpC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wBAAwB;AAAA,YAChC,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,sBAAsB;AAAA,UAChC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mCAAmC;AAAA,YAC3C,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,kCAAkC;AAAA,YAC1C,EAAE,MAAM,iCAAiC;AAAA,UAC3C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mCAAmC;AAAA,YAC3C,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,kCAAkC;AAAA,YAC1C,EAAE,MAAM,iCAAiC;AAAA,UAC3C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,6BAA6B;AAAA,YACrC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,kCAAkC;AAAA,YAC1C,EAAE,MAAM,mCAAmC;AAAA,YAC3C,EAAE,MAAM,kCAAkC;AAAA,UAC5C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AD1RO,IAAK,SAAL,kBAAKC,YAAL;AAEL,EAAAA,QAAA,aAAU;AAEV,EAAAA,QAAA,WAAQ;AAER,EAAAA,QAAA,YAAS;AAET,EAAAA,QAAA,aAAU;AAEV,EAAAA,QAAA,cAAW;AAVD,SAAAA;AAAA,GAAA;AAsBL,IAAK,OAAL,kBAAKC,UAAL;AAEL,EAAAA,MAAA,eAAY;AAEZ,EAAAA,MAAA,UAAO;AAJG,SAAAA;AAAA,GAAA;AAmFL,IAAK,oBAAL,kBAAKC,uBAAL;AAEL,EAAAA,mBAAA,aAAU;AAEV,EAAAA,mBAAA,eAAY;AAEZ,EAAAA,mBAAA,YAAS;AANC,SAAAA;AAAA,GAAA;AAgPL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAqQZ,eAAsBC,gBAUpB;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC,CAAC,CAAC;AAExD,QAAM,UAAoC,aAAa,OAAO;AAE9D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,CAAC;AAAA,QAC3B,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC;AAAA,IACH;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAaA,eAAsBC,aACpB,SAUA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,IACjB,MAAM,SAAS;AAAA,IACf,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UAAoC,YAAY,OAAO;AAE7D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoCA,eAAsBE,eACpB,UAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC,EAAE,SAAmB,CAAC;AAE5E,QAAM,UAAoC,cAAc,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,UAAU,OAAO;AAAA,QAC7C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU;AAAA,IACb;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmBA,eAAsBG,sBACpB,UACA,wBAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UAAoC,qBAAqB,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAH;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,UAAU;AAAA,UACV,wBAAwB;AAAA,QAC1B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY,wBAAwB;AAAA,IACvC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAaA,eAAsBI,kBACpB,SAYA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,IACjB,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UAAoC,iBAAiB,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAJ;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA4BA,eAAsBK,cAAa,UAAiC;AAElE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC,EAAE,SAAmB,CAAC;AAE5E,QAAM,UAAoC,aAAa,OAAO;AAE9D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAAA,EACjC,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAL;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,UAAU,OAAO;AAAA,QAC7C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU;AAAA,IACb;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;", "names": ["createBackup", "deleteBackup", "listBackups", "listRestorations", "restoreBackup", "restorePartialBackup", "import_rest_modules", "payload", "Status", "Type", "RestorationStatus", "WebhookIdentityType", "createBackup", "sdkTransformError", "listBackups", "restoreBackup", "restorePartialBackup", "listRestorations", "deleteBackup"]}