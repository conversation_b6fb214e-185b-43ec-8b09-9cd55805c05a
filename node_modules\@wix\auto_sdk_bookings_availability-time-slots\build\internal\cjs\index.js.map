{"version": 3, "sources": ["../../../index.ts", "../../../src/bookings-availability-v2-time-slot-availability-time-slots.universal.ts", "../../../src/bookings-availability-v2-time-slot-availability-time-slots.http.ts", "../../../src/bookings-availability-v2-time-slot-availability-time-slots.public.ts", "../../../src/bookings-availability-v2-time-slot-availability-time-slots.context.ts"], "sourcesContent": ["export * from './src/bookings-availability-v2-time-slot-availability-time-slots.context.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsAvailabilityV2TimeSlot from './bookings-availability-v2-time-slot-availability-time-slots.http.js';\n\n/**\n * A time slot represents a specific time period when a service is available for booking.\n * It provides all the information needed to display availability to customers, including whether customers can actually book it, the remaining capacity, and which staff members or resources are available.\n * Available time slots may not always be bookable due to service booking policies. When this occurs, the time slot includes information about the specific booking restrictions.\n *\n * For multi-service bookings, the top-level time slot acts as a container spanning the entire service sequence (from the start of the first service to the end of the last service). Individual service details are provided in nested time slots.\n */\nexport interface TimeSlot {\n  /**\n   * Service ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).\n   *\n   * Available only for single-service bookings. For multi-service bookings, this field is empty and individual service IDs are provided in `nestedTimeSlots`.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   *\n   * For multi-service bookings, this represents the start time of the first service in the sequence.\n   * @format LOCAL_DATE_TIME\n   */\n  localStartDate?: string | null;\n  /**\n   * Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T14:00:00`.\n   *\n   * For multi-service bookings, this represents the end time of the last service in the sequence.\n   * @format LOCAL_DATE_TIME\n   */\n  localEndDate?: string | null;\n  /**\n   * Whether customers can book the slot according to the service's booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).\n   *\n   * For multi-service bookings, this is `true` only when all services in the sequence comply with their respective booking policies.\n   */\n  bookable?: boolean | null;\n  /** Information about where the business provides the service to the customer. */\n  location?: Location;\n  /**\n   * Information about the event ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)) related to the slot.\n   * Available only for classes. Not available for appointment-based services and courses.\n   */\n  eventInfo?: EventInfo;\n  /**\n   * Total number of spots for the slot.\n   *\n   * For multi-service bookings, this is always `1` because customers book the entire service sequence as a single unit.\n   * @min 1\n   * @max 1000\n   */\n  totalCapacity?: number | null;\n  /**\n   * Remaining number of spots for the slot.\n   * - For appointment bookings: Either `1` (available) or `0` (unavailable).\n   * - For classes: Total capacity minus booked spots. Doesn't account for waitlist reservations. For classes with waitlists, use `bookableCapacity` to get the actual number of spots customers can book.\n   * - For courses: Total capacity minus booked spots. Courses don't currently support waitlists.\n   * @max 1000\n   */\n  remainingCapacity?: number | null;\n  /**\n   * Number of spots that customers can book for the slot.\n   * Calculated as the remaining capacity minus the spots reserved for the waitlist.\n   * If the service has no waitlist, identical to `remainingCapacity`.\n   *\n   * For multi-service bookings, this is either `1` (sequence can be booked) or `0` (sequence cannot be booked).\n   */\n  bookableCapacity?: number | null;\n  /**\n   * Information about booking policy violations for the slot.\n   *\n   * For multi-service bookings, this aggregates violations from all services in the sequence.\n   */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /**\n   * List of resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) available during the time slot.\n   *\n   * Available only for single-service bookings. For multi-service bookings, resource information is provided in `nestedTimeSlots`.\n   */\n  availableResources?: AvailableResources[];\n  /**\n   * Nested time slots for multi-service bookings.\n   * Each nested slot represents 1 service in the sequence, ordered according to the service sequence specified in the request.\n   *\n   * Available only for multi-service bookings. Empty for single-service bookings.\n   * @maxSize 8\n   */\n  nestedTimeSlots?: NestedTimeSlot[];\n  /** Information about why customers can't book the time slot. */\n  nonBookableReasons?: NonBookableReasons;\n  /**\n   * Schedule ID associated with this time slot.\n   * Same as the service's schedule ID.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n}\n\nexport interface Location {\n  /**\n   * Location ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   * Available only for business locations.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Location name. */\n  name?: string | null;\n  /** Formatted location address. */\n  formattedAddress?: string | null;\n  /** Location type. */\n  locationType?: LocationTypeWithLiterals;\n}\n\nexport enum LocationType {\n  UNKNOWN_LOCATION_TYPE = 'UNKNOWN_LOCATION_TYPE',\n  /** A business location, either the default business address, or locations defined for the business by the Business Info. */\n  BUSINESS = 'BUSINESS',\n  /** The location is unique to this service and isn't defined as 1 of the business locations. */\n  CUSTOM = 'CUSTOM',\n  /** The location can be determined by the customer and isn't set up beforehand. */\n  CUSTOMER = 'CUSTOMER',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNKNOWN_LOCATION_TYPE'\n  | 'BUSINESS'\n  | 'CUSTOM'\n  | 'CUSTOMER';\n\n/** Available for event based slots, and not for availability based slots */\nexport interface EventInfo {\n  /**\n   * Event ID.\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n  /** Information about the event's waitlist. Available only if the service has a waitlist. */\n  waitingList?: WaitingList;\n}\n\nexport interface WaitingList {\n  /**\n   * Total number of spots in the waitlist.\n   * @min 1\n   */\n  totalCapacity?: number | null;\n  /**\n   * Number of remaining spots in the waitlist.\n   * For example, an event with a waitlist for 10 people and 3 registrants, results in a remaining capacity of `7`.\n   */\n  remainingCapacity?: number | null;\n}\n\nexport interface BookingPolicyViolations {\n  /** Whether it's too early for customers to book the slot. */\n  tooEarlyToBook?: boolean | null;\n  /** Earliest time for booking the slot in `YYYY-MM-DDThh:mm:ss.sssZ` format. */\n  earliestBookingDate?: Date | null;\n  /** Whether it's too late for customers to book the slot. */\n  tooLateToBook?: boolean | null;\n  /** Whether customers can book the service online. */\n  bookOnlineDisabled?: boolean | null;\n}\n\nexport interface AvailableResources {\n  /**\n   * Resource type ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).\n   * @format GUID\n   */\n  resourceTypeId?: string | null;\n  /**\n   * Details about resources available during the time slot.\n   *\n   * Behavior varies by method:\n   *\n   * List methods (List Availability Time Slots and List Multi Service Availability Time Slots):\n   * - Empty by default.\n   * - Up to 10 resources when specifying `includeResourceTypeIds` or `resourceIds` in the request.\n   *\n   * Get methods (Get Availability Time Slots and Get Multi Service Availability Time Slots):\n   * - All resources by default.\n   * - Filtered resources when specifying `includeResourceTypeIds` or `resourceIds` in the request.\n   */\n  resources?: Resource[];\n  /**\n   * Whether there are more available resources for the slot than those listed in `resources`.\n   * @readonly\n   */\n  hasMoreAvailableResources?: boolean | null;\n}\n\nexport interface Resource {\n  /**\n   * Resource ID.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Resource name.\n   * @minLength 1\n   */\n  name?: string | null;\n}\n\nexport interface NestedTimeSlot {\n  /**\n   * Service ID of the nested time slot.\n   * @format GUID\n   */\n  serviceId?: string;\n  /**\n   * Local start date of the nested time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  localStartDate?: string;\n  /**\n   * Local end date of the nested time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  localEndDate?: string;\n  /**\n   * List of resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) available during the nested time slot.\n   * @maxSize 10\n   */\n  availableResources?: AvailableResources[];\n  /**\n   * The schedule ID associated with this nested time slot.\n   * Same as the service's schedule ID.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n}\n\nexport interface NonBookableReasons {\n  /** Whether the slot is fully booked with no remaining capacity. */\n  noRemainingCapacity?: boolean | null;\n  /** Whether booking the slot violates any of the service's booking policies. */\n  violatesBookingPolicy?: boolean | null;\n  /** Whether the slot is reserved for the waitlist. A new customer can't book the reserved slot. */\n  reservedForWaitingList?: boolean | null;\n}\n\nexport interface ListMultiServiceAvailabilityTimeSlotsRequest {\n  /**\n   * Services for which the multi-service time slots are returned.\n   * Each service can include its own resource filters.\n   *\n   * Required unless you specify `cursorPaging.cursor`.\n   *\n   * Min: 2 services\n   * Max: 8 services\n   * @maxSize 8\n   */\n  services?: Service[];\n  /**\n   * Lower boundary for `localStartDate` in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate`.\n   *\n   * Required unless you specify `cursorPaging.cursor`.\n   * @format LOCAL_DATE_TIME\n   */\n  fromLocalDate?: string | null;\n  /**\n   * Upper boundary for `localToDate` in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * Each returned time slot has a `localEndDate` between `fromLocalDate` and `toLocalDate`.\n   *\n   * Required unless you specify `cursorPaging.cursor`.\n   * @format LOCAL_DATE_TIME\n   */\n  toLocalDate?: string | null;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values. For example, `America/New_York` or `UTC`.\n   *\n   * Required unless you specify `cursorPaging.cursor`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * Location for which the multi-service time slots are returned. If you filter by `{\"type\": \"BUSINESS\"}`, you must also specify a location ID. Filters for `location.name` and `location.formattedAddress` are ignored.\n   *\n   * Required unless you specify `cursorPaging.cursor`.\n   */\n  location?: Location;\n  /**\n   * Whether the time slot is bookable according to all services' booking policies.\n   * If not specified, returns both bookable and non-bookable time slots.\n   */\n  bookable?: boolean | null;\n  /**\n   * Indicators for service's booking policy violations.\n   * Only relevant when `bookable` filter is false.\n   */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /**\n   * Maximum number of slots to return for each day in the specified time range.\n   * If `bookable` filter isn't specified, bookable slots are returned first.\n   */\n  timeSlotsPerDay?: number | null;\n  /**\n   * Cursor-based paging configuration.\n   * Enables fetching results in smaller chunks by setting a limit on the number of results.\n   * For consistent pagination behavior, use the same `limit` value throughout a pagination sequence.\n   * When specifying a new `limit` in follow-up requests, the API respects the new value.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Service {\n  /**\n   * Service ID.\n   * @format GUID\n   */\n  serviceId?: string;\n  /**\n   * ID of the resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) to include in response.\n   * @format GUID\n   * @maxSize 135\n   */\n  resourceIds?: string[];\n  /**\n   * Resource type IDs ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) to include in returned time slots.\n   * This is in addition to the specifically requested resources.\n   *\n   * <blockquote>\n   * Currently supported only for Staff Member resource type.\n   * Staff members belong to resource type ID `cd44cf8-756f-41c3-bd90-3e2ffcaf1155`.\n   * </blockquote>\n   * @format GUID\n   * @maxSize 100\n   */\n  includeResourceTypeIds?: string[];\n  /**\n   * Selected customer choices.\n   * If specified, the selected choices are used to calculate service configuration.\n   * If not specified, the service default configuration is used.\n   * Enforcing this field is the responsibility of the service plugin implementer, and not the Time Slots V2 API.\n   */\n  customerChoices?: V2CustomerChoices;\n}\n\n/**\n * Selected customer choices.\n * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.\n */\nexport interface V2CustomerChoices {\n  /**\n   * Selected customer duration in minutes.\n   * Min: `1` minute\n   * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)\n   * Default: `15` minutes\n   * @min 1\n   * @max 44639\n   */\n  durationInMinutes?: number | null;\n  /**\n   * Selected add-on IDs.\n   *\n   * Max: Calculated as the product of the maximum number of add-on groups multiplied by the maximum number of add-ons per group. Currently 21 (3 groups × 7 add-ons per group), but may change in the future.\n   * @format GUID\n   * @maxSize 21\n   */\n  addOnIds?: string[] | null;\n  /**\n   * Selected duration choice ID.\n   * @format GUID\n   */\n  durationChoiceId?: string | null;\n}\n\nexport interface CursorPaging {\n  /**\n   * Number of results to load.\n   *\n   * Default: `1000`.\n   * Max: `1000`.\n   * @max 1000\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * You can get the relevant cursor token\n   * from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   */\n  cursor?: string | null;\n}\n\nexport interface ListMultiServiceAvailabilityTimeSlotsResponse {\n  /**\n   * Retrieved time slots.\n   * Sorted by `localStartDate` in ascending order. When multiple slots have the same start time, no specific secondary sorting is guaranteed.\n   * @maxSize 1000\n   */\n  timeSlots?: TimeSlot[];\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * Paging metadata for the next page of results.\n   * Contains a cursor if there are more than 1000 results.\n   */\n  cursorPagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /** Offset that was requested. */\n  cursors?: Cursors;\n  /**\n   * Indicates if there are more results after the current page.\n   * If `true`, another page of results can be retrieved.\n   * If `false`, this is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface Cursors {\n  /** Cursor pointing to next page in the list of results. */\n  next?: string | null;\n  /** Cursor pointing to previous page in the list of results. */\n  prev?: string | null;\n}\n\nexport interface GetMultiServiceAvailabilityTimeSlotRequest {\n  /**\n   * Services for which the multi-service time slots are returned.\n   * You can specify resource filters for each service.\n   * @minSize 2\n   * @maxSize 8\n   */\n  services?: Service[];\n  /**\n   * Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  localStartDate?: string;\n  /**\n   * Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  localEndDate?: string;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * Location for which the multi-service time slots are returned. If you filter by `{\"type\": \"BUSINESS\"}`, you must also specify a location ID. A filter for `location.name` is ignored.\n   *\n   * Required unless you specify `cursorPaging.cursor`.\n   */\n  location?: Location;\n}\n\nexport interface GetMultiServiceAvailabilityTimeSlotResponse {\n  /** Retrieved time slot. */\n  timeSlot?: TimeSlot;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n}\n\nexport interface ListAvailabilityTimeSlotsRequest {\n  /**\n   * Service ID for which to retrieve time slots. You must specify the ID of an appointment-based service.\n   * Required, unless you specify `cursorPaging.cursor`.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * Lower boundary for `localStartDate` to include in response.\n   * Each returned time slot has a `localStartDate` within the provided `fromLocalDate` and `toLocalDate` exclusive.\n   * Required, unless you specify `cursorPaging.cursor`.\n   *\n   * Local start date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  fromLocalDate?: string | null;\n  /**\n   * Upper boundary for `localStartDate` to include in response.\n   * Each returned time slot has a `localStartDate` within the provided `fromLocalDate` and `toLocalDate` exclusive.\n   * Required, unless you specify `cursorPaging.cursor`.\n   *\n   * Local end date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  toLocalDate?: string | null;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * Locations to include in response.\n   * If not specified, returns time slots for all locations where the service is available.\n   * @maxSize 5\n   */\n  locations?: Location[];\n  /**\n   * Resources to include in response.\n   * If specified, returns up to 10 available resources out of the provided list.\n   * Otherwise, returns time slots with empty `availableResources`.\n   * @format GUID\n   * @maxSize 135\n   * @deprecated\n   */\n  resourceIds?: string[];\n  /**\n   * Resource type IDs to include in the response.\n   * If specified, returns up to 10 `availableResources` with matching `resourceTypeId`.\n   * This controls which resource details are included in the response but doesn't filter the time slots themselves.\n   * @format GUID\n   * @maxSize 100\n   */\n  includeResourceTypeIds?: string[];\n  /**\n   * Whether the time slot is bookable according to the service's booking policies.\n   * If not specified, returns both bookable and un-bookable time slots.\n   */\n  bookable?: boolean | null;\n  /**\n   * Indicators for service's booking policy violations.\n   * Only relevant when `bookable` filter is set to `false`. Allows filtering for time slots with specific violation types:\n   * - `tooEarlyToBook`: Returns slots that violate minimum advance booking time.\n   * - `tooLateToBook`: Returns slots that violate maximum advance booking time.\n   * - `bookOnlineDisabled`: Returns slots where online booking is disabled.\n   */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /**\n   * Maximum number of slots to return for each day in the specified time range.\n   * If `bookable` filter isn't specified, bookable slots are returned first.\n   */\n  timeSlotsPerDay?: number | null;\n  /**\n   * Cursor-based paging configuration.\n   * Enables fetching results in smaller chunks by setting a limit on the number of results.\n   * For consistent pagination behavior, use the same `limit` value throughout a pagination sequence.\n   * When specifying a new `limit` in follow-up requests, the API respects the new value.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Resource types to filter time slots.\n   * Only returns time slots that have these specific resource types available.\n   * This filters the time slots themselves, unlike `includeResourceTypeIds` which only controls response details.\n   * @maxSize 3\n   */\n  resourceTypes?: ResourceType[];\n}\n\n/**\n * Selected customer choices.\n * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.\n */\nexport interface CustomerChoices {\n  /**\n   * The selected customer duration in minutes.\n   * Min: `1 minute`\n   * Max: `44639 minutes` (30 days, 23 hours, and 59 minutes)\n   * @min 1\n   * @max 44639\n   */\n  durationInMinutes?: number | null;\n  /**\n   * The selected add-ons IDs.\n   * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.\n   * @format GUID\n   * @maxSize 21\n   */\n  addOnIds?: string[] | null;\n  /**\n   * The selected duration choice ID.\n   * @format GUID\n   */\n  durationChoiceId?: string | null;\n}\n\nexport interface ResourceType {\n  /**\n   * Resource type ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).\n   * @format GUID\n   */\n  resourceTypeId?: string | null;\n  /**\n   * Resource IDs.\n   * Available only if there is at least 1 resource available for the slot.\n   * @format GUID\n   * @minSize 1\n   * @maxSize 135\n   */\n  resourceIds?: string[] | null;\n}\n\nexport interface ListAvailabilityTimeSlotsResponse {\n  /**\n   * Retrieved time slots.\n   * Sorted by `localStartDate` in ascending order. When multiple slots have the same start time, no specific secondary sorting is guaranteed.\n   * @maxSize 1000\n   */\n  timeSlots?: TimeSlot[];\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * Paging metadata for the next page of results.\n   * Contains a cursor if there are more than 1000 results.\n   */\n  cursorPagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface GetAvailabilityTimeSlotRequest {\n  /**\n   * Service ID of the time slot.\n   * You must specify the ID of an appointment-based service.\n   * @format GUID\n   */\n  serviceId: string;\n  /**\n   * Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  localStartDate: string;\n  /**\n   * Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  localEndDate: string;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone: string | null;\n  /**\n   * Location to filter time slots by.\n   *\n   * For business locations, you must specify a location ID.\n   * When specifying a location ID, all other location field filters are ignored.\n   */\n  location: Location;\n  /**\n   * IDs of the resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) to check availability for.\n   * If omitted, time slots are returned if there's at least 1 available resource.\n   * @format GUID\n   * @maxSize 135\n   */\n  resourceIds?: string[];\n  /**\n   * IDs of the resource types ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) to check availability for.\n   * @format GUID\n   * @maxSize 100\n   */\n  includeResourceTypeIds?: string[];\n}\n\nexport interface GetAvailabilityTimeSlotRequestCustomerChoices {\n  /**\n   * Selected add-on IDs.\n   * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.\n   * @format GUID\n   * @maxSize 21\n   */\n  addOnIds?: string[] | null;\n  /**\n   * Selected duration choice ID.\n   * @format GUID\n   */\n  durationChoiceId?: string | null;\n}\n\nexport interface GetAvailabilityTimeSlotResponse {\n  /** Retrieved time slot. */\n  timeSlot?: TimeSlot;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n}\n\nexport interface ListEventTimeSlotsRequest {\n  /**\n   * Lower boundary for `localStartDate` to include in the response.\n   * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate.\n   * Required unless you specify `cursorPaging.cursor`.\n   *\n   * Local start date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  fromLocalDate?: string | null;\n  /**\n   * Upper boundary for `localStartDate` to include in the response.\n   * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate`.\n   * Required unless you specify `cursorPaging.cursor`.\n   *\n   * Local end date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  toLocalDate?: string | null;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @maxLength 100\n   */\n  timeZone?: string | null;\n  /**\n   * Optional service IDs to filter the response.\n   * If not provided, time slots for all services are returned.\n   * @format GUID\n   * @maxSize 100\n   */\n  serviceIds?: string[] | null;\n  /**\n   * Whether to include time slots that aren't bookable according to the service's booking policy or that are fully booked.\n   * - `true`: Both bookable and non-bookable time slots are returned.\n   * - `false`: Only bookable time slots are returned.\n   *\n   * Default: `true`\n   */\n  includeNonBookable?: boolean | null;\n  /**\n   * Minimum bookable capacity.\n   * Use to filter out sessions that can't accommodate the desired party size.\n   * @min 1\n   */\n  minBookableCapacity?: number | null;\n  /**\n   * Optional filter to apply to the events, for example resource or location.\n   * See the [Events API Supported Filters and Sorting article](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/supported-filters-and-sorting) for a complete list of filters.\n   */\n  eventFilter?: Record<string, any> | null;\n  /**\n   * Maximum number of slots to return for each day in the specified time range.\n   * If both bookable and non-bookable slots exist on the same day, bookable slots are returned first.\n   *\n   * When you specify `maxSlotsPerDay`, you must also specify `toLocalDate`, and it must be no more than 1 month after `fromLocalDate`.\n   * @min 1\n   * @max 1000\n   */\n  maxSlotsPerDay?: number | null;\n  /**\n   * Cursor-based paging configuration.\n   * Enables fetching results in smaller chunks by setting a limit on the number of returned items.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Indicators for service's booking policy violations.\n   * Allows filtering for time slots with specific violation types:\n   * - `tooEarlyToBook`: Returns slots that violate minimum advance booking time.\n   * - `tooLateToBook`: Returns slots that violate maximum advance booking time.\n   * - `bookOnlineDisabled`: Returns slots where online booking is disabled.\n   *\n   * When specified, only time slots matching the violation criteria are returned.\n   */\n  bookingPolicyViolations?: BookingPolicyViolations;\n}\n\nexport interface ListEventTimeSlotsResponse {\n  /** Retrieved time slots matching the specified filters. */\n  timeSlots?: TimeSlot[];\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * Paging metadata for the next page of results.\n   * Contains a cursor if more data is available.\n   */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface GetEventTimeSlotRequest {\n  /**\n   * Event ID.\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @maxLength 100\n   */\n  timeZone?: string | null;\n}\n\nexport interface GetEventTimeSlotResponse {\n  /** The time slot. */\n  timeSlot?: TimeSlot;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n}\n\n/** @docsIgnore */\nexport type ListAvailabilityTimeSlotsApplicationErrors =\n  | {\n      code?: 'NO_IMPLEMENTERS_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTIPLE_IMPLEMENTERS_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MISSING_ARGUMENTS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNAUTHORIZED_OPERATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SERVICE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type GetAvailabilityTimeSlotApplicationErrors =\n  | {\n      code?: 'SLOT_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NO_IMPLEMENTERS_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTIPLE_IMPLEMENTERS_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNAUTHORIZED_OPERATION';\n      description?: string;\n      data?: Record<string, any>;\n    };\n\n/**\n * Retrieves a list of appointment time slots that match the provided filters.\n *\n *\n * ## Defaults\n *\n * List Availability Time Slots uses the following defaults:\n *\n * - Sorts time slots by `localStartDate` in ascending order.\n * - `cursorPaging.limit` set to `1000`.\n * - Returns both bookable and un-bookable time slots.\n * - If `bookable` filter isn't specified, bookable slots are returned first.\n * - `fromLocalDate` is set to the current time.\n *\n * ## Filtering behavior\n *\n * The method automatically filters time slots:\n *\n * - **Past time slots**: Only time slots starting from the specified `fromLocalDate` are included. When you omit `fromLocalDate`, Wix Bookings uses the current time as the default.\n * - **Resource availability**: Only time slots with at least some available resources are returned.\n *\n * ## Service type limitations\n *\n * Only appointment-based services are supported when calling List Availability Time Slots.\n *\n * To retrieve class availability, you can call List Event Time Slots([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/event-time-slots/list-event-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-event-time-slots)).\n *\n * To retrieve course availability, you can follow the End-to-End Booking Flow for course([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flow#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n *\n * ## Available resources\n *\n * Time slots aren't returned when they have no available resources.\n *\n * Each time slot includes details for up to 10 available resources.\n * If there are more than 10 resources, you can call Get Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot)) and filter by the resource Type ID to get details for the remaining resources.\n *\n * ## Business hours exception\n *\n * Wix Bookings disregards business opening hours when all of the following conditions are met:\n *\n * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n * <-!-- 4. The service doesn't support add-ons ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/add-ons/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/add-ons/introduction)). -->\n *\n * In these cases, the working hours of the relevant staff member are used for availability calculation.\n * @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.AVAILABILITY_READ_TIME_SLOTS\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.ListAvailabilityTimeSlots\n */\nexport async function listAvailabilityTimeSlots(\n  options?: ListAvailabilityTimeSlotsOptions\n): Promise<\n  NonNullablePaths<\n    ListAvailabilityTimeSlotsResponse,\n    `timeSlots` | `timeSlots.${number}.location.locationType`,\n    5\n  > & {\n    __applicationErrorsType?: ListAvailabilityTimeSlotsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: options?.serviceId,\n    fromLocalDate: options?.fromLocalDate,\n    toLocalDate: options?.toLocalDate,\n    timeZone: options?.timeZone,\n    locations: options?.locations,\n    resourceIds: options?.resourceIds,\n    includeResourceTypeIds: options?.includeResourceTypeIds,\n    bookable: options?.bookable,\n    bookingPolicyViolations: options?.bookingPolicyViolations,\n    timeSlotsPerDay: options?.timeSlotsPerDay,\n    cursorPaging: options?.cursorPaging,\n    resourceTypes: options?.resourceTypes,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.listAvailabilityTimeSlots(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceId: '$[0].serviceId',\n          fromLocalDate: '$[0].fromLocalDate',\n          toLocalDate: '$[0].toLocalDate',\n          timeZone: '$[0].timeZone',\n          locations: '$[0].locations',\n          resourceIds: '$[0].resourceIds',\n          includeResourceTypeIds: '$[0].includeResourceTypeIds',\n          bookable: '$[0].bookable',\n          bookingPolicyViolations: '$[0].bookingPolicyViolations',\n          timeSlotsPerDay: '$[0].timeSlotsPerDay',\n          cursorPaging: '$[0].cursorPaging',\n          resourceTypes: '$[0].resourceTypes',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListAvailabilityTimeSlotsOptions {\n  /**\n   * Service ID for which to retrieve time slots. You must specify the ID of an appointment-based service.\n   * Required, unless you specify `cursorPaging.cursor`.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * Lower boundary for `localStartDate` to include in response.\n   * Each returned time slot has a `localStartDate` within the provided `fromLocalDate` and `toLocalDate` exclusive.\n   * Required, unless you specify `cursorPaging.cursor`.\n   *\n   * Local start date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  fromLocalDate?: string | null;\n  /**\n   * Upper boundary for `localStartDate` to include in response.\n   * Each returned time slot has a `localStartDate` within the provided `fromLocalDate` and `toLocalDate` exclusive.\n   * Required, unless you specify `cursorPaging.cursor`.\n   *\n   * Local end date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @format LOCAL_DATE_TIME\n   */\n  toLocalDate?: string | null;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * Locations to include in response.\n   * If not specified, returns time slots for all locations where the service is available.\n   * @maxSize 5\n   */\n  locations?: Location[];\n  /**\n   * Resources to include in response.\n   * If specified, returns up to 10 available resources out of the provided list.\n   * Otherwise, returns time slots with empty `availableResources`.\n   * @format GUID\n   * @maxSize 135\n   * @deprecated\n   */\n  resourceIds?: string[];\n  /**\n   * Resource type IDs to include in the response.\n   * If specified, returns up to 10 `availableResources` with matching `resourceTypeId`.\n   * This controls which resource details are included in the response but doesn't filter the time slots themselves.\n   * @format GUID\n   * @maxSize 100\n   */\n  includeResourceTypeIds?: string[];\n  /**\n   * Whether the time slot is bookable according to the service's booking policies.\n   * If not specified, returns both bookable and un-bookable time slots.\n   */\n  bookable?: boolean | null;\n  /**\n   * Indicators for service's booking policy violations.\n   * Only relevant when `bookable` filter is set to `false`. Allows filtering for time slots with specific violation types:\n   * - `tooEarlyToBook`: Returns slots that violate minimum advance booking time.\n   * - `tooLateToBook`: Returns slots that violate maximum advance booking time.\n   * - `bookOnlineDisabled`: Returns slots where online booking is disabled.\n   */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /**\n   * Maximum number of slots to return for each day in the specified time range.\n   * If `bookable` filter isn't specified, bookable slots are returned first.\n   */\n  timeSlotsPerDay?: number | null;\n  /**\n   * Cursor-based paging configuration.\n   * Enables fetching results in smaller chunks by setting a limit on the number of results.\n   * For consistent pagination behavior, use the same `limit` value throughout a pagination sequence.\n   * When specifying a new `limit` in follow-up requests, the API respects the new value.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Resource types to filter time slots.\n   * Only returns time slots that have these specific resource types available.\n   * This filters the time slots themselves, unlike `includeResourceTypeIds` which only controls response details.\n   * @maxSize 3\n   */\n  resourceTypes?: ResourceType[];\n}\n\n/**\n * Retrieves detailed information about a specific appointment time slot.\n *\n *\n * Call this method to get complete resource availability after finding a suitable slot with List Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).\n *\n * ## Defaults\n *\n * Get Availability Time Slot uses the following defaults:\n *\n * - Returns all available resources unless filtered by `resourceIds` or `includeResourceTypeIds`.\n * - Includes full booking status and capacity details\n *\n * ## Service type limitations\n *\n * Only appointment-based services are supported when calling Get Availability Time Slot.\n *\n * To retrieve class session availability, you can call Get Event Time Slot ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot)).\n *\n * To retrieve course availability, you can follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flows#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n *\n * ## Business hours exception\n *\n * Wix Bookings disregards business opening hours when all of the following conditions are met:\n *\n * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n *\n * In these cases, the working hours of the relevant staff member are used instead for availability calculation.\n * @param serviceId - Service ID of the time slot.\n * You must specify the ID of an appointment-based service.\n * @param localStartDate - Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n * For example, `2026-01-30T13:30:00`.\n * @param localEndDate - Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n * For example, `2026-01-30T13:30:00`.\n * @param timeZone - Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n * For example, `America/New_York` or `UTC`.\n *\n * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n * @param location - Location to filter time slots by.\n *\n * For business locations, you must specify a location ID.\n * When specifying a location ID, all other location field filters are ignored.\n * @public\n * @documentationMaturity preview\n * @requiredField localEndDate\n * @requiredField localStartDate\n * @requiredField location\n * @requiredField serviceId\n * @requiredField timeZone\n * @permissionId BOOKINGS.AVAILABILITY_READ_TIME_SLOTS\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.GetAvailabilityTimeSlot\n */\nexport async function getAvailabilityTimeSlot(\n  serviceId: string,\n  localStartDate: string,\n  localEndDate: string,\n  timeZone: string,\n  location: Location,\n  options?: GetAvailabilityTimeSlotOptions\n): Promise<\n  NonNullablePaths<\n    GetAvailabilityTimeSlotResponse,\n    | `timeSlot.location.locationType`\n    | `timeSlot.availableResources`\n    | `timeSlot.nestedTimeSlots`\n    | `timeSlot.nestedTimeSlots.${number}.serviceId`\n    | `timeSlot.nestedTimeSlots.${number}.localStartDate`\n    | `timeSlot.nestedTimeSlots.${number}.localEndDate`,\n    5\n  > & {\n    __applicationErrorsType?: GetAvailabilityTimeSlotApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[6] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    localStartDate: localStartDate,\n    localEndDate: localEndDate,\n    timeZone: timeZone,\n    location: location,\n    resourceIds: options?.resourceIds,\n    includeResourceTypeIds: options?.includeResourceTypeIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.getAvailabilityTimeSlot(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceId: '$[0]',\n          localStartDate: '$[1]',\n          localEndDate: '$[2]',\n          timeZone: '$[3]',\n          location: '$[4]',\n          resourceIds: '$[5].resourceIds',\n          includeResourceTypeIds: '$[5].includeResourceTypeIds',\n        },\n        singleArgumentUnchanged: false,\n      },\n      [\n        'serviceId',\n        'localStartDate',\n        'localEndDate',\n        'timeZone',\n        'location',\n        'options',\n      ]\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface GetAvailabilityTimeSlotOptions {\n  /**\n   * IDs of the resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) to check availability for.\n   * If omitted, time slots are returned if there's at least 1 available resource.\n   * @format GUID\n   * @maxSize 135\n   */\n  resourceIds?: string[];\n  /**\n   * IDs of the resource types ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) to check availability for.\n   * @format GUID\n   * @maxSize 100\n   */\n  includeResourceTypeIds?: string[];\n}\n", "import { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_availability-time-slots';\n\n/**\n * Retrieves a list of appointment time slots that match the provided filters.\n *\n *\n * ## Defaults\n *\n * List Availability Time Slots uses the following defaults:\n *\n * - Sorts time slots by `localStartDate` in ascending order.\n * - `cursorPaging.limit` set to `1000`.\n * - Returns both bookable and un-bookable time slots.\n * - If `bookable` filter isn't specified, bookable slots are returned first.\n * - `fromLocalDate` is set to the current time.\n *\n * ## Filtering behavior\n *\n * The method automatically filters time slots:\n *\n * - **Past time slots**: Only time slots starting from the specified `fromLocalDate` are included. When you omit `fromLocalDate`, Wix Bookings uses the current time as the default.\n * - **Resource availability**: Only time slots with at least some available resources are returned.\n *\n * ## Service type limitations\n *\n * Only appointment-based services are supported when calling List Availability Time Slots.\n *\n * To retrieve class availability, you can call List Event Time Slots([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/event-time-slots/list-event-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-event-time-slots)).\n *\n * To retrieve course availability, you can follow the End-to-End Booking Flow for course([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flow#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n *\n * ## Available resources\n *\n * Time slots aren't returned when they have no available resources.\n *\n * Each time slot includes details for up to 10 available resources.\n * If there are more than 10 resources, you can call Get Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot)) and filter by the resource Type ID to get details for the remaining resources.\n *\n * ## Business hours exception\n *\n * Wix Bookings disregards business opening hours when all of the following conditions are met:\n *\n * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n * <-!-- 4. The service doesn't support add-ons ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/add-ons/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/add-ons/introduction)). -->\n *\n * In these cases, the working hours of the relevant staff member are used for availability calculation.\n */\nexport function listAvailabilityTimeSlots(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listAvailabilityTimeSlots({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [{ path: 'bookingPolicyViolations.earliestBookingDate' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.ListAvailabilityTimeSlots',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl({\n        protoPath: '/v2/time-slots',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlots.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listAvailabilityTimeSlots;\n}\n\n/**\n * Retrieves detailed information about a specific appointment time slot.\n *\n *\n * Call this method to get complete resource availability after finding a suitable slot with List Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).\n *\n * ## Defaults\n *\n * Get Availability Time Slot uses the following defaults:\n *\n * - Returns all available resources unless filtered by `resourceIds` or `includeResourceTypeIds`.\n * - Includes full booking status and capacity details\n *\n * ## Service type limitations\n *\n * Only appointment-based services are supported when calling Get Availability Time Slot.\n *\n * To retrieve class session availability, you can call Get Event Time Slot ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot)).\n *\n * To retrieve course availability, you can follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flows#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n *\n * ## Business hours exception\n *\n * Wix Bookings disregards business opening hours when all of the following conditions are met:\n *\n * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n *\n * In these cases, the working hours of the relevant staff member are used instead for availability calculation.\n */\nexport function getAvailabilityTimeSlot(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getAvailabilityTimeSlot({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.GetAvailabilityTimeSlot',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl({\n        protoPath: '/v2/time-slots/get',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlot.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getAvailabilityTimeSlot;\n}\n", "import { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  GetAvailabilityTimeSlotApplicationErrors,\n  GetAvailabilityTimeSlotOptions,\n  GetAvailabilityTimeSlotResponse,\n  ListAvailabilityTimeSlotsApplicationErrors,\n  ListAvailabilityTimeSlotsOptions,\n  ListAvailabilityTimeSlotsResponse,\n  Location,\n  getAvailabilityTimeSlot as universalGetAvailabilityTimeSlot,\n  listAvailabilityTimeSlots as universalListAvailabilityTimeSlots,\n} from './bookings-availability-v2-time-slot-availability-time-slots.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function listAvailabilityTimeSlots(\n  httpClient: HttpClient\n): ListAvailabilityTimeSlotsSignature {\n  return (options?: ListAvailabilityTimeSlotsOptions) =>\n    universalListAvailabilityTimeSlots(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ListAvailabilityTimeSlotsSignature {\n  /**\n   * Retrieves a list of appointment time slots that match the provided filters.\n   *\n   *\n   * ## Defaults\n   *\n   * List Availability Time Slots uses the following defaults:\n   *\n   * - Sorts time slots by `localStartDate` in ascending order.\n   * - `cursorPaging.limit` set to `1000`.\n   * - Returns both bookable and un-bookable time slots.\n   * - If `bookable` filter isn't specified, bookable slots are returned first.\n   * - `fromLocalDate` is set to the current time.\n   *\n   * ## Filtering behavior\n   *\n   * The method automatically filters time slots:\n   *\n   * - **Past time slots**: Only time slots starting from the specified `fromLocalDate` are included. When you omit `fromLocalDate`, Wix Bookings uses the current time as the default.\n   * - **Resource availability**: Only time slots with at least some available resources are returned.\n   *\n   * ## Service type limitations\n   *\n   * Only appointment-based services are supported when calling List Availability Time Slots.\n   *\n   * To retrieve class availability, you can call List Event Time Slots([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/event-time-slots/list-event-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-event-time-slots)).\n   *\n   * To retrieve course availability, you can follow the End-to-End Booking Flow for course([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flow#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n   *\n   * ## Available resources\n   *\n   * Time slots aren't returned when they have no available resources.\n   *\n   * Each time slot includes details for up to 10 available resources.\n   * If there are more than 10 resources, you can call Get Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot)) and filter by the resource Type ID to get details for the remaining resources.\n   *\n   * ## Business hours exception\n   *\n   * Wix Bookings disregards business opening hours when all of the following conditions are met:\n   *\n   * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n   * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n   * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n   * <-!-- 4. The service doesn't support add-ons ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/add-ons/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/add-ons/introduction)). -->\n   *\n   * In these cases, the working hours of the relevant staff member are used for availability calculation.\n   */\n  (options?: ListAvailabilityTimeSlotsOptions): Promise<\n    NonNullablePaths<\n      ListAvailabilityTimeSlotsResponse,\n      `timeSlots` | `timeSlots.${number}.location.locationType`,\n      5\n    > & {\n      __applicationErrorsType?: ListAvailabilityTimeSlotsApplicationErrors;\n    }\n  >;\n}\n\nexport function getAvailabilityTimeSlot(\n  httpClient: HttpClient\n): GetAvailabilityTimeSlotSignature {\n  return (\n    serviceId: string,\n    localStartDate: string,\n    localEndDate: string,\n    timeZone: string,\n    location: Location,\n    options?: GetAvailabilityTimeSlotOptions\n  ) =>\n    universalGetAvailabilityTimeSlot(\n      serviceId,\n      localStartDate,\n      localEndDate,\n      timeZone,\n      location,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetAvailabilityTimeSlotSignature {\n  /**\n   * Retrieves detailed information about a specific appointment time slot.\n   *\n   *\n   * Call this method to get complete resource availability after finding a suitable slot with List Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).\n   *\n   * ## Defaults\n   *\n   * Get Availability Time Slot uses the following defaults:\n   *\n   * - Returns all available resources unless filtered by `resourceIds` or `includeResourceTypeIds`.\n   * - Includes full booking status and capacity details\n   *\n   * ## Service type limitations\n   *\n   * Only appointment-based services are supported when calling Get Availability Time Slot.\n   *\n   * To retrieve class session availability, you can call Get Event Time Slot ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot)).\n   *\n   * To retrieve course availability, you can follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flows#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n   *\n   * ## Business hours exception\n   *\n   * Wix Bookings disregards business opening hours when all of the following conditions are met:\n   *\n   * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n   * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n   * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n   *\n   * In these cases, the working hours of the relevant staff member are used instead for availability calculation.\n   * @param - Service ID of the time slot.\n   * You must specify the ID of an appointment-based service.\n   * @param - Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @param - Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * For example, `2026-01-30T13:30:00`.\n   * @param - Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate`.\n   * For example, `America/New_York` or `UTC`.\n   *\n   * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).\n   * @param - Location to filter time slots by.\n   *\n   * For business locations, you must specify a location ID.\n   * When specifying a location ID, all other location field filters are ignored.\n   */\n  (\n    serviceId: string,\n    localStartDate: string,\n    localEndDate: string,\n    timeZone: string,\n    location: Location,\n    options?: GetAvailabilityTimeSlotOptions\n  ): Promise<\n    NonNullablePaths<\n      GetAvailabilityTimeSlotResponse,\n      | `timeSlot.location.locationType`\n      | `timeSlot.availableResources`\n      | `timeSlot.nestedTimeSlots`\n      | `timeSlot.nestedTimeSlots.${number}.serviceId`\n      | `timeSlot.nestedTimeSlots.${number}.localStartDate`\n      | `timeSlot.nestedTimeSlots.${number}.localEndDate`,\n      5\n    > & {\n      __applicationErrorsType?: GetAvailabilityTimeSlotApplicationErrors;\n    }\n  >;\n}\n\nexport {\n  AvailableResources,\n  BookingPolicyViolations,\n  CursorPaging,\n  CursorPagingMetadata,\n  Cursors,\n  CustomerChoices,\n  EventInfo,\n  GetAvailabilityTimeSlotOptions,\n  GetAvailabilityTimeSlotRequest,\n  GetAvailabilityTimeSlotRequestCustomerChoices,\n  GetAvailabilityTimeSlotResponse,\n  GetEventTimeSlotRequest,\n  GetEventTimeSlotResponse,\n  GetMultiServiceAvailabilityTimeSlotRequest,\n  GetMultiServiceAvailabilityTimeSlotResponse,\n  ListAvailabilityTimeSlotsOptions,\n  ListAvailabilityTimeSlotsRequest,\n  ListAvailabilityTimeSlotsResponse,\n  ListEventTimeSlotsRequest,\n  ListEventTimeSlotsResponse,\n  ListMultiServiceAvailabilityTimeSlotsRequest,\n  ListMultiServiceAvailabilityTimeSlotsResponse,\n  Location,\n  LocationType,\n  NestedTimeSlot,\n  NonBookableReasons,\n  Resource,\n  ResourceType,\n  Service,\n  TimeSlot,\n  V2CustomerChoices,\n  WaitingList,\n} from './bookings-availability-v2-time-slot-availability-time-slots.universal.js';\n", "import {\n  listAvailabilityTimeSlots as publicListAvailabilityTimeSlots,\n  getAvailabilityTimeSlot as publicGetAvailabilityTimeSlot,\n} from './bookings-availability-v2-time-slot-availability-time-slots.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { BuildRESTFunction, MaybeContext } from '@wix/sdk-types';\n\nexport const listAvailabilityTimeSlots: MaybeContext<\n  BuildRESTFunction<typeof publicListAvailabilityTimeSlots> &\n    typeof publicListAvailabilityTimeSlots\n> = /*#__PURE__*/ createRESTModule(publicListAvailabilityTimeSlots);\nexport const getAvailabilityTimeSlot: MaybeContext<\n  BuildRESTFunction<typeof publicGetAvailabilityTimeSlot> &\n    typeof publicGetAvailabilityTimeSlot\n> = /*#__PURE__*/ createRESTModule(publicGetAvailabilityTimeSlot);\n\nexport { LocationType } from './bookings-availability-v2-time-slot-availability-time-slots.universal.js';\nexport {\n  TimeSlot,\n  Location,\n  EventInfo,\n  WaitingList,\n  BookingPolicyViolations,\n  AvailableResources,\n  Resource,\n  NestedTimeSlot,\n  NonBookableReasons,\n  ListMultiServiceAvailabilityTimeSlotsRequest,\n  Service,\n  V2CustomerChoices,\n  CursorPaging,\n  ListMultiServiceAvailabilityTimeSlotsResponse,\n  CursorPagingMetadata,\n  Cursors,\n  GetMultiServiceAvailabilityTimeSlotRequest,\n  GetMultiServiceAvailabilityTimeSlotResponse,\n  ListAvailabilityTimeSlotsRequest,\n  CustomerChoices,\n  ResourceType,\n  ListAvailabilityTimeSlotsResponse,\n  GetAvailabilityTimeSlotRequest,\n  GetAvailabilityTimeSlotRequestCustomerChoices,\n  GetAvailabilityTimeSlotResponse,\n  ListEventTimeSlotsRequest,\n  ListEventTimeSlotsResponse,\n  GetEventTimeSlotRequest,\n  GetEventTimeSlotResponse,\n  ListAvailabilityTimeSlotsOptions,\n  GetAvailabilityTimeSlotOptions,\n} from './bookings-availability-v2-time-slot-availability-time-slots.universal.js';\nexport {\n  LocationTypeWithLiterals,\n  ListAvailabilityTimeSlotsApplicationErrors,\n  GetAvailabilityTimeSlotApplicationErrors,\n} from './bookings-availability-v2-time-slot-availability-time-slots.universal.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,iCAAAA;AAAA,EAAA,iCAAAC;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,oCAGO;;;ACJP,uBAAqD;AACrD,IAAAC,oBAAqD;AACrD,6BAA+B;AAC/B,0BAA2B;AAI3B,SAAS,iEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,gCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAiDd,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,8CAA8C,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iEAAiE;AAAA,QACpE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiCO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iEAAiE;AAAA,QACpE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AD3KO,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,2BAAwB;AAExB,EAAAA,cAAA,cAAW;AAEX,EAAAA,cAAA,YAAS;AAET,EAAAA,cAAA,cAAW;AAPD,SAAAA;AAAA,GAAA;AA80BZ,eAAsBC,2BACpB,SASA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,WAAW,SAAS;AAAA,IACpB,eAAe,SAAS;AAAA,IACxB,aAAa,SAAS;AAAA,IACtB,UAAU,SAAS;AAAA,IACnB,WAAW,SAAS;AAAA,IACpB,aAAa,SAAS;AAAA,IACtB,wBAAwB,SAAS;AAAA,IACjC,UAAU,SAAS;AAAA,IACnB,yBAAyB,SAAS;AAAA,IAClC,iBAAiB,SAAS;AAAA,IAC1B,cAAc,SAAS;AAAA,IACvB,eAAe,SAAS;AAAA,EAC1B,CAAC;AAED,QAAM,UACwC;AAAA,IAC1C;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,eAAe;AAAA,UACf,aAAa;AAAA,UACb,UAAU;AAAA,UACV,WAAW;AAAA,UACX,aAAa;AAAA,UACb,wBAAwB;AAAA,UACxB,UAAU;AAAA,UACV,yBAAyB;AAAA,UACzB,iBAAiB;AAAA,UACjB,cAAc;AAAA,UACd,eAAe;AAAA,QACjB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsJA,eAAsBC,yBACpB,WACA,gBACA,cACA,UACA,UACA,SAcA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,SAAS;AAAA,IACtB,wBAAwB,SAAS;AAAA,EACnC,CAAC;AAED,QAAM,UACwC;AAAA,IAC1C;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,UAAU;AAAA,UACV,UAAU;AAAA,UACV,aAAa;AAAA,UACb,wBAAwB;AAAA,QAC1B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;AEjuCO,SAASE,2BACd,YACoC;AACpC,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA6DO,SAASC,yBACd,YACkC;AAClC,SAAO,CACL,WACA,gBACA,cACA,UACA,UACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;;;ACtGA,IAAAC,uBAAiC;AAG1B,IAAMC,6BAGK,2DAAiBA,0BAA+B;AAC3D,IAAMC,2BAGK,2DAAiBA,wBAA6B;", "names": ["getAvailabilityTimeSlot", "listAvailabilityTimeSlots", "import_timestamp", "payload", "LocationType", "listAvailabilityTimeSlots", "sdkTransformError", "getAvailabilityTimeSlot", "listAvailabilityTimeSlots", "getAvailabilityTimeSlot", "import_rest_modules", "listAvailabilityTimeSlots", "getAvailabilityTimeSlot"]}