"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  Event: () => Event,
  Status: () => Status,
  WebhookIdentityType: () => WebhookIdentityType,
  createCategory: () => createCategory3,
  deleteCategory: () => deleteCategory3,
  listCategories: () => listCategories3,
  onCategoryNotification: () => onCategoryNotification2,
  updateCategory: () => updateCategory3
});
module.exports = __toCommonJS(index_exports);

// src/bookings-v1-category-categories.public.ts
var import_rename_all_nested_keys2 = require("@wix/sdk-runtime/rename-all-nested-keys");
var import_sdk_types = require("@wix/sdk-types");

// src/bookings-v1-category-categories.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-v1-category-categories.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl(opts) {
  const domainToMappings = {
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/bookings/v1/batch/categories",
        destPath: "/v1/batch/categories"
      },
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/categories-proxy",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ],
    "bookings._base_domain_": [
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_categories";
function list(payload) {
  function __list({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "GET",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService.List",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __list;
}
function create(payload) {
  function __create({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "POST",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService.Create",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __create;
}
function update(payload) {
  function __update({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "PUT",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService.Update",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories/{category.id}",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __update;
}
function _delete(payload) {
  function ___delete({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "DELETE",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService._delete",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories/{id}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return ___delete;
}

// src/bookings-v1-category-categories.universal.ts
var Status = /* @__PURE__ */ ((Status2) => {
  Status2["CREATED"] = "CREATED";
  Status2["DELETED"] = "DELETED";
  return Status2;
})(Status || {});
var Event = /* @__PURE__ */ ((Event2) => {
  Event2["Updated"] = "Updated";
  Event2["Deleted"] = "Deleted";
  Event2["Created"] = "Created";
  return Event2;
})(Event || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function listCategories(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    categoryIds: options?.categoryIds,
    includeDeleted: options?.includeDeleted
  });
  const reqOpts = list(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          categoryIds: "$[0].categoryIds",
          includeDeleted: "$[0].includeDeleted"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function createCategory(category) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ category });
  const reqOpts = create(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.category;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { category: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateCategory(_id, category) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    category: { ...category, id: _id }
  });
  const reqOpts = update(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: { category: "$[1]" },
        explicitPathsToArguments: { "category.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteCategory(_id, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    id: _id,
    deleteServices: options?.deleteServices
  });
  const reqOpts = _delete(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          id: "$[0]",
          deleteServices: "$[1].deleteServices"
        },
        singleArgumentUnchanged: false
      },
      ["_id", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-v1-category-categories.public.ts
function listCategories2(httpClient) {
  return (options) => listCategories(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function createCategory2(httpClient) {
  return (category) => createCategory(
    category,
    // @ts-ignore
    { httpClient }
  );
}
function updateCategory2(httpClient) {
  return (_id, category) => updateCategory(
    _id,
    category,
    // @ts-ignore
    { httpClient }
  );
}
function deleteCategory2(httpClient) {
  return (_id, options) => deleteCategory(
    _id,
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onCategoryNotification = (0, import_sdk_types.EventDefinition)(
  "com.wixpress.bookings.services.api.v1.CategoryNotification",
  false,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(event)
)();

// src/bookings-v1-category-categories.context.ts
var import_rest_modules3 = require("@wix/sdk-runtime/rest-modules");
var import_event_definition_modules = require("@wix/sdk-runtime/event-definition-modules");
var listCategories3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(listCategories2);
var createCategory3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(createCategory2);
var updateCategory3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(updateCategory2);
var deleteCategory3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(deleteCategory2);
var onCategoryNotification2 = (0, import_event_definition_modules.createEventModule)(onCategoryNotification);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Event,
  Status,
  WebhookIdentityType,
  createCategory,
  deleteCategory,
  listCategories,
  onCategoryNotification,
  updateCategory
});
//# sourceMappingURL=index.js.map