// src/bookings-resources-v2-resource-type-resource-types.public.ts
import { renameKeysFromRESTResponseToSDKResponse as renameKeysFromRESTResponseToSDKResponse2 } from "@wix/sdk-runtime/rename-all-nested-keys";
import { transformRESTTimestampToSDKTimestamp as transformRESTTimestampToSDKTimestamp2 } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths as transformPaths3 } from "@wix/sdk-runtime/transformations/transform-paths";
import { EventDefinition } from "@wix/sdk-types";

// src/bookings-resources-v2-resource-type-resource-types.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import { queryBuilder } from "@wix/sdk-runtime/query-builder";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-resources-v2-resource-type-resource-types.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      },
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/resource-types",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_resource-types";
function createResourceType(payload) {
  function __createResourceType({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createResourceType;
}
function getResourceType(payload) {
  function __getResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "GET",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getResourceType;
}
function updateResourceType(payload) {
  function __updateResourceType({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "PATCH",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceType.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateResourceType;
}
function deleteResourceType(payload) {
  function __deleteResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "DELETE",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteResourceType;
}
function queryResourceTypes(payload) {
  function __queryResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceTypes.createdDate" },
            { path: "resourceTypes.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryResourceTypes;
}
function countResourceTypes(payload) {
  function __countResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countResourceTypes;
}

// src/bookings-resources-v2-resource-type-resource-types.universal.ts
import { transformPaths as transformPaths2 } from "@wix/sdk-runtime/transformations/transform-paths";
var RequestedFields = /* @__PURE__ */ ((RequestedFields2) => {
  RequestedFields2["TOTAL_RESOURCE_COUNT"] = "TOTAL_RESOURCE_COUNT";
  RequestedFields2["SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS"] = "SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS";
  RequestedFields2["DISTINCT_RESOURCE_LOCATIONS"] = "DISTINCT_RESOURCE_LOCATIONS";
  return RequestedFields2;
})(RequestedFields || {});
var CreateResourceTypeErrors = /* @__PURE__ */ ((CreateResourceTypeErrors2) => {
  CreateResourceTypeErrors2["UNKNOWN_CREATE_RESOURCE_TYPE_ERROR"] = "UNKNOWN_CREATE_RESOURCE_TYPE_ERROR";
  CreateResourceTypeErrors2["FAILED_TO_CREATE_RESOURCES"] = "FAILED_TO_CREATE_RESOURCES";
  return CreateResourceTypeErrors2;
})(CreateResourceTypeErrors || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createResourceType2(resourceType) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resourceType
  });
  const reqOpts = createResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resourceType;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceType: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceType"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getResourceType2(resourceTypeId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resourceTypeId
  });
  const reqOpts = getResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resourceType;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceTypeId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceTypeId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateResourceType2(_id, resourceType) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resourceType: { ...resourceType, id: _id }
  });
  const reqOpts = updateResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resourceType;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: { resourceType: "$[1]" },
        explicitPathsToArguments: { "resourceType.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "resourceType"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteResourceType2(resourceTypeId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resourceTypeId
  });
  const reqOpts = deleteResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceTypeId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceTypeId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryResourceTypes2() {
  const { httpClient, sideEffects } = arguments[0];
  return queryBuilder({
    func: async (payload) => {
      const reqOpts = queryResourceTypes(
        payload
      );
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return renameKeysFromSDKRequestToRESTRequest({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({
      data
    }) => {
      const transformedData = renameKeysFromRESTResponseToSDKResponse(
        transformPaths2(data, [])
      );
      return {
        items: transformedData?.resourceTypes,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = sdkTransformError(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countResourceTypes2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    filter: options?.filter
  });
  const reqOpts = countResourceTypes(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-resources-v2-resource-type-resource-types.public.ts
function createResourceType3(httpClient) {
  return (resourceType) => createResourceType2(
    resourceType,
    // @ts-ignore
    { httpClient }
  );
}
function getResourceType3(httpClient) {
  return (resourceTypeId) => getResourceType2(
    resourceTypeId,
    // @ts-ignore
    { httpClient }
  );
}
function updateResourceType3(httpClient) {
  return (_id, resourceType) => updateResourceType2(
    _id,
    resourceType,
    // @ts-ignore
    { httpClient }
  );
}
function deleteResourceType3(httpClient) {
  return (resourceTypeId) => deleteResourceType2(
    resourceTypeId,
    // @ts-ignore
    { httpClient }
  );
}
function queryResourceTypes3(httpClient) {
  return () => queryResourceTypes2(
    // @ts-ignore
    { httpClient }
  );
}
function countResourceTypes3(httpClient) {
  return (options) => countResourceTypes2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onResourceTypeCreated = EventDefinition(
  "wix.bookings.resources.v2.resource_type_created",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onResourceTypeDeleted = EventDefinition(
  "wix.bookings.resources.v2.resource_type_deleted",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onResourceTypeUpdated = EventDefinition(
  "wix.bookings.resources.v2.resource_type_updated",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();

// src/bookings-resources-v2-resource-type-resource-types.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
import { createEventModule } from "@wix/sdk-runtime/event-definition-modules";
var createResourceType4 = /* @__PURE__ */ createRESTModule(createResourceType3);
var getResourceType4 = /* @__PURE__ */ createRESTModule(getResourceType3);
var updateResourceType4 = /* @__PURE__ */ createRESTModule(updateResourceType3);
var deleteResourceType4 = /* @__PURE__ */ createRESTModule(deleteResourceType3);
var queryResourceTypes4 = /* @__PURE__ */ createRESTModule(queryResourceTypes3);
var countResourceTypes4 = /* @__PURE__ */ createRESTModule(countResourceTypes3);
var onResourceTypeCreated2 = createEventModule(onResourceTypeCreated);
var onResourceTypeDeleted2 = createEventModule(onResourceTypeDeleted);
var onResourceTypeUpdated2 = createEventModule(onResourceTypeUpdated);
export {
  CreateResourceTypeErrors,
  RequestedFields,
  SortOrder,
  WebhookIdentityType,
  countResourceTypes4 as countResourceTypes,
  createResourceType4 as createResourceType,
  deleteResourceType4 as deleteResourceType,
  getResourceType4 as getResourceType,
  onResourceTypeCreated2 as onResourceTypeCreated,
  onResourceTypeDeleted2 as onResourceTypeDeleted,
  onResourceTypeUpdated2 as onResourceTypeUpdated,
  queryResourceTypes4 as queryResourceTypes,
  updateResourceType4 as updateResourceType
};
//# sourceMappingURL=index.mjs.map