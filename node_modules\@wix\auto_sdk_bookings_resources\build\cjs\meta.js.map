{"version": 3, "sources": ["../../meta.ts", "../../src/bookings-resources-v2-resource-resources.http.ts", "../../src/bookings-resources-v2-resource-resources.meta.ts"], "sourcesContent": ["export * from './src/bookings-resources-v2-resource-resources.meta.js';\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFloatToRESTFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsResourcesV2ResourcesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/bookings/v2/resources',\n        destPath: '/v2/resources',\n      },\n      {\n        srcPath: '/_api/bookings/v2/resources',\n        destPath: '/v2/resources',\n      },\n      {\n        srcPath: '/v2/bulk/resources',\n        destPath: '/v2/bulk/resources',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/resources-2',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/resources',\n        destPath: '/v2/resources',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/resources',\n        destPath: '/v2/bulk/resources',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/resources-2',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_resources';\n\n/**\n * Creates a new resource.\n *\n *\n * ## Connected schedules\n *\n * A new event *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration))\n * is automatically created for the resource.\n *\n * ## Locations\n *\n * If you don't specify `locationOptions`, Wix Bookings automatically sets\n * `locationOptions.availableInAllLocations` to `true`.\n *\n * If you specify 1 business location ID in `locationOptions.specificLocationOptions.businessLocations`,\n * you must specify `locationOptions.specificLocationOptions.availableInBusinessLocations`\n * as `true`. Currently, you can specify only a single business location.\n */\nexport function createResource(payload: object): RequestOptionsFactory<any> {\n  function __createResource({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resource.createdDate' },\n          { path: 'resource.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.CreateResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resource.createdDate' },\n              { path: 'resource.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createResource;\n}\n\n/**\n * Creates up to 50 resources.\n *\n *\n * Refer to *Create Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/create-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/create-resource))\n * for more details.\n */\nexport function bulkCreateResources(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkCreateResources({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resources.createdDate' },\n          { path: 'resources.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.ResourcesService.BulkCreateResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/bulk/resources/create',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkCreateResources;\n}\n\n/** Retrieves a resource. */\nexport function getResource(payload: object): RequestOptionsFactory<any> {\n  function __getResource({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.GetResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/{resourceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resource.createdDate' },\n              { path: 'resource.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getResource;\n}\n\n/**\n * Updates a resource.\n *\n *\n * Each time the resource is updated, `revision` increments by 1. You must include the current revision of the resource when updating it.\n * This ensures you're working with the latest service information and prevents unintended overwrites.\n */\nexport function updateResource(payload: object): RequestOptionsFactory<any> {\n  function __updateResource({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resource.createdDate' },\n          { path: 'resource.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.UpdateResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/{resource.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resource.createdDate' },\n              { path: 'resource.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateResource;\n}\n\n/**\n * Updates multiple resources.\n *\n *\n * Refer to *Update Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/update-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/update-resource))\n * for more details.\n */\nexport function bulkUpdateResources(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateResources({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'resources.fieldMask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resources.resource.createdDate' },\n          { path: 'resources.resource.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.ResourcesService.BulkUpdateResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/bulk/resources/update',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateResources;\n}\n\n/**\n * Deletes a resource.\n *\n *\n * Deleting a resource cancels its event schedule and all its working hour\n * schedules that aren't shared with another resource. Learn more about\n * _how Bookings uses the Calendar APIs_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).\n */\nexport function deleteResource(payload: object): RequestOptionsFactory<any> {\n  function __deleteResource({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.DeleteResource',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/{resourceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteResource;\n}\n\n/**\n * Deletes multiple resources.\n *\n *\n * Refer to *Delete Resource*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/delete-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/delete-resource))\n * for more details.\n */\nexport function bulkDeleteResources(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkDeleteResources({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.ResourcesService.BulkDeleteResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/bulk/resources/delete',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkDeleteResources;\n}\n\n/**\n * Retrieves a list of resources matching the provided search criteria.\n *\n *\n * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.\n */\nexport function searchResources(payload: object): RequestOptionsFactory<any> {\n  function __searchResources({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'search.aggregations.range.buckets.from' },\n          { path: 'search.aggregations.range.buckets.to' },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.from',\n          },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.to',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.SearchResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/search',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resources.createdDate' },\n              { path: 'resources.updatedDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'aggregationData.results.ranges.results.from' },\n              { path: 'aggregationData.results.ranges.results.to' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from',\n              },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.from',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.to',\n              },\n              { path: 'aggregationData.results.scalar.value' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.scalar.value',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.scalar.value',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __searchResources;\n}\n\n/**\n * Creates a query to retrieve a list of resources.\n *\n * The `queryResources()` function builds a query to retrieve a list of resources and returns a `ResourcesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-builder/find) function.\n *\n * You can refine the query by chaining `ResourcesQueryBuilder` functions onto the query. `ResourcesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResources()` returns.\n *\n * `queryResources()` runs with the following `ResourcesQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryResources()` are applied in the order they are called. For example, if you apply `ascending(\"typeId\")` and then `ascending(\"name\")`, the results are sorted first by the `\"typeId\"`, and then, if there are multiple results with the same `\"typeId\"`, the items are sorted by `\"name\"`.\n *\n * The following `ResourcesQueryBuilder` functions are supported for the `queryResources()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-result/items) property in `ResourcesQueryResult`.\n */\nexport function queryResources(payload: object): RequestOptionsFactory<any> {\n  function __queryResources({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.QueryResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resources.createdDate' },\n              { path: 'resources.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryResources;\n}\n\n/**\n * Counts resources according to given criteria.\n *\n *\n * Refer to the *supported filters article*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n */\nexport function countResources(payload: object): RequestOptionsFactory<any> {\n  function __countResources({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.resources.v2.ResourcesService.CountResources',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({\n        protoPath: '/v2/resources/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countResources;\n}\n", "import * as ambassadorWixBookingsResourcesV2Resource from './bookings-resources-v2-resource-resources.http.js';\nimport * as ambassadorWixBookingsResourcesV2ResourceTypes from './bookings-resources-v2-resource-resources.types.js';\nimport * as ambassadorWixBookingsResourcesV2ResourceUniversalTypes from './bookings-resources-v2-resource-resources.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function createResource(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.CreateResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.CreateResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.CreateResourceResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.CreateResourceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.createResource(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/resources',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkCreateResources(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.BulkCreateResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.BulkCreateResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.BulkCreateResourcesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.BulkCreateResourcesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.bulkCreateResources(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/resources/create',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getResource(): __PublicMethodMetaInfo<\n  'GET',\n  { resourceId: string },\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.GetResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.GetResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.GetResourceResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.GetResourceResponse\n> {\n  const payload = { resourceId: ':resourceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.getResource(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/resources/{resourceId}',\n    pathParams: { resourceId: 'resourceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateResource(): __PublicMethodMetaInfo<\n  'PATCH',\n  { resourceId: string },\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.UpdateResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.UpdateResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.UpdateResourceResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.UpdateResourceResponse\n> {\n  const payload = { resource: { id: ':resourceId' } } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.updateResource(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PATCH',\n    path: '/v2/resources/{resource.id}',\n    pathParams: { resourceId: 'resourceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkUpdateResources(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.BulkUpdateResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.BulkUpdateResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.BulkUpdateResourcesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.BulkUpdateResourcesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.bulkUpdateResources(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/resources/update',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteResource(): __PublicMethodMetaInfo<\n  'DELETE',\n  { resourceId: string },\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.DeleteResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.DeleteResourceRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.DeleteResourceResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.DeleteResourceResponse\n> {\n  const payload = { resourceId: ':resourceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.deleteResource(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'DELETE',\n    path: '/v2/resources/{resourceId}',\n    pathParams: { resourceId: 'resourceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkDeleteResources(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.BulkDeleteResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.BulkDeleteResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.BulkDeleteResourcesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.BulkDeleteResourcesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.bulkDeleteResources(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/resources/delete',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function searchResources(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.SearchResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.SearchResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.SearchResourcesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.SearchResourcesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.searchResources(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/resources/search',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryResources(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.QueryResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.QueryResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.QueryResourcesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.QueryResourcesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.queryResources(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/resources/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function countResources(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.CountResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypes.CountResourcesRequest,\n  ambassadorWixBookingsResourcesV2ResourceUniversalTypes.CountResourcesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypes.CountResourcesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2Resource.countResources(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/resources/count',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,6BAAAA;AAAA,EAAA,2BAAAC;AAAA,EAAA,2BAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,sBAAAC;AAAA;AAAA;;;ACAA,0BAAkC;AAClC,mBAA6C;AAC7C,IAAAC,gBAA6C;AAC7C,uBAAqD;AACrD,IAAAC,oBAAqD;AACrD,wBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,iDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAqBd,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,wBAAwB;AAAA,UAChC,EAAE,MAAM,wBAAwB;AAAA,QAClC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,YAAY,SAA6C;AACvE,WAAS,cAAc,EAAE,KAAK,GAAQ;AACpC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,sBAAsB,CAAC;AAAA,MACzC;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,iCAAiC;AAAA,UACzC,EAAE,MAAM,iCAAiC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yCAAyC;AAAA,UACjD,EAAE,MAAM,uCAAuC;AAAA,UAC/C;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wBAAwB;AAAA,YAChC,EAAE,MAAM,wBAAwB;AAAA,UAClC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wBAAwB;AAAA,YAChC,EAAE,MAAM,wBAAwB;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,iDAAiD;AAAA,QACpD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC9fO,SAASC,kBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACqC,eAAe,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,uBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACqC,oBAAoB,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,eAOd;AACA,QAAM,UAAU,EAAE,YAAY,cAAc;AAE5C,QAAM,oBACqC,YAAY,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,YAAY,aAAa;AAAA,IACvC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,EAAE,UAAU,EAAE,IAAI,cAAc,EAAE;AAElD,QAAM,oBACqC,eAAe,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,YAAY,aAAa;AAAA,IACvC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,uBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACqC,oBAAoB,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,EAAE,YAAY,cAAc;AAE5C,QAAM,oBACqC,eAAe,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,YAAY,aAAa;AAAA,IACvC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,uBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACqC,oBAAoB,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,mBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACqC,gBAAgB,OAAO;AAElE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACqC,eAAe,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACqC,eAAe,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["bulkCreateResources", "bulkDeleteResources", "bulkUpdateResources", "countResources", "createResource", "deleteResource", "getResource", "queryResources", "searchResources", "updateResource", "import_float", "import_timestamp", "import_rest_modules", "payload", "createResource", "bulkCreateResources", "getResource", "updateResource", "bulkUpdateResources", "deleteResource", "bulkDeleteResources", "searchResources", "queryResources", "countResources"]}