// src/bookings-categories-v2-category-categories-v-2.public.ts
import { renameKeysFromRESTResponseToSDKResponse as renameKeysFromRESTResponseToSDKResponse2 } from "@wix/sdk-runtime/rename-all-nested-keys";
import { transformRESTTimestampToSDKTimestamp as transformRESTTimestampToSDKTimestamp2 } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths as transformPaths3 } from "@wix/sdk-runtime/transformations/transform-paths";
import { EventDefinition } from "@wix/sdk-types";

// src/bookings-categories-v2-category-categories-v-2.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import { queryBuilder } from "@wix/sdk-runtime/query-builder";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-categories-v2-category-categories-v-2.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsCategoriesV2CategoriesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/categories",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_categories-v-2";
function createCategory(payload) {
  function __createCategory({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "category.createdDate" },
          { path: "category.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.CreateCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createCategory;
}
function getCategory(payload) {
  function __getCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "GET",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.GetCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{categoryId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getCategory;
}
function updateCategory(payload) {
  function __updateCategory({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "category.createdDate" },
          { path: "category.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "PATCH",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.UpdateCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{category.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateCategory;
}
function deleteCategory(payload) {
  function __deleteCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "DELETE",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.DeleteCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{categoryId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteCategory;
}
function queryCategories(payload) {
  function __queryCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.QueryCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "categories.createdDate" },
            { path: "categories.updatedDate" }
          ]
        }
      ]),
      fallback: [
        {
          method: "POST",
          url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
            protoPath: "/v2/categories/query",
            data: payload,
            host
          }),
          data: payload
        }
      ]
    };
    return metadata;
  }
  return __queryCategories;
}
function countCategories(payload) {
  function __countCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.CountCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countCategories;
}
function moveCategory(payload) {
  function __moveCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.MoveCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/set-position/{categoryId}",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __moveCategory;
}

// src/bookings-categories-v2-category-categories-v-2.universal.ts
import { transformPaths as transformPaths2 } from "@wix/sdk-runtime/transformations/transform-paths";
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var Position = /* @__PURE__ */ ((Position2) => {
  Position2["UNKNOWN_POSITION"] = "UNKNOWN_POSITION";
  Position2["LAST"] = "LAST";
  Position2["FIRST"] = "FIRST";
  Position2["AFTER_CATEGORY"] = "AFTER_CATEGORY";
  return Position2;
})(Position || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createCategory2(category) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({ category });
  const reqOpts = createCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { category: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getCategory2(categoryId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    categoryId
  });
  const reqOpts = getCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { categoryId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["categoryId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateCategory2(_id, category) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    category: { ...category, id: _id }
  });
  const reqOpts = updateCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: { category: "$[1]" },
        explicitPathsToArguments: { "category.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteCategory2(categoryId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    categoryId
  });
  const reqOpts = deleteCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { categoryId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["categoryId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryCategories2() {
  const { httpClient, sideEffects } = arguments[0];
  return queryBuilder({
    func: async (payload) => {
      const reqOpts = queryCategories(payload);
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return renameKeysFromSDKRequestToRESTRequest({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({ data }) => {
      const transformedData = renameKeysFromRESTResponseToSDKResponse(
        transformPaths2(data, [])
      );
      return {
        items: transformedData?.categories,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = sdkTransformError(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countCategories2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    filter: options?.filter
  });
  const reqOpts = countCategories(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function moveCategory2(categoryId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    categoryId,
    position: options?.position,
    afterCategoryId: options?.afterCategoryId
  });
  const reqOpts = moveCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          categoryId: "$[0]",
          position: "$[1].position",
          afterCategoryId: "$[1].afterCategoryId"
        },
        singleArgumentUnchanged: false
      },
      ["categoryId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-categories-v2-category-categories-v-2.public.ts
function createCategory3(httpClient) {
  return (category) => createCategory2(
    category,
    // @ts-ignore
    { httpClient }
  );
}
function getCategory3(httpClient) {
  return (categoryId) => getCategory2(
    categoryId,
    // @ts-ignore
    { httpClient }
  );
}
function updateCategory3(httpClient) {
  return (_id, category) => updateCategory2(
    _id,
    category,
    // @ts-ignore
    { httpClient }
  );
}
function deleteCategory3(httpClient) {
  return (categoryId) => deleteCategory2(
    categoryId,
    // @ts-ignore
    { httpClient }
  );
}
function queryCategories3(httpClient) {
  return () => queryCategories2(
    // @ts-ignore
    { httpClient }
  );
}
function countCategories3(httpClient) {
  return (options) => countCategories2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function moveCategory3(httpClient) {
  return (categoryId, options) => moveCategory2(
    categoryId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onCategoryCreated = EventDefinition(
  "wix.bookings.categories.v2.category_created",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onCategoryDeleted = EventDefinition(
  "wix.bookings.categories.v2.category_deleted",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onCategoryUpdated = EventDefinition(
  "wix.bookings.categories.v2.category_updated",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();

// src/bookings-categories-v2-category-categories-v-2.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
import { createEventModule } from "@wix/sdk-runtime/event-definition-modules";
var createCategory4 = /* @__PURE__ */ createRESTModule(createCategory3);
var getCategory4 = /* @__PURE__ */ createRESTModule(getCategory3);
var updateCategory4 = /* @__PURE__ */ createRESTModule(updateCategory3);
var deleteCategory4 = /* @__PURE__ */ createRESTModule(deleteCategory3);
var queryCategories4 = /* @__PURE__ */ createRESTModule(queryCategories3);
var countCategories4 = /* @__PURE__ */ createRESTModule(countCategories3);
var moveCategory4 = /* @__PURE__ */ createRESTModule(moveCategory3);
var onCategoryCreated2 = createEventModule(onCategoryCreated);
var onCategoryDeleted2 = createEventModule(onCategoryDeleted);
var onCategoryUpdated2 = createEventModule(onCategoryUpdated);
export {
  Position,
  SortOrder,
  WebhookIdentityType,
  countCategories4 as countCategories,
  createCategory4 as createCategory,
  deleteCategory4 as deleteCategory,
  getCategory4 as getCategory,
  moveCategory4 as moveCategory,
  onCategoryCreated2 as onCategoryCreated,
  onCategoryDeleted2 as onCategoryDeleted,
  onCategoryUpdated2 as onCategoryUpdated,
  queryCategories4 as queryCategories,
  updateCategory4 as updateCategory
};
//# sourceMappingURL=index.mjs.map