"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  Actor: () => Actor,
  BookingStatus: () => BookingStatus,
  IdentificationDataIdentityType: () => IdentificationDataIdentityType,
  IdentityType: () => IdentityType,
  LocationLocationType: () => LocationLocationType,
  LocationType: () => LocationType,
  MultiServiceBookingType: () => MultiServiceBookingType,
  PaymentStatus: () => PaymentStatus,
  Platform: () => Platform,
  SelectedPaymentOption: () => SelectedPaymentOption,
  SortOrder: () => SortOrder,
  ValueType: () => ValueType,
  WebhookIdentityType: () => WebhookIdentityType,
  addBookingsToMultiServiceBooking: () => addBookingsToMultiServiceBooking4,
  bulkConfirmOrDeclineBooking: () => bulkConfirmOrDeclineBooking4,
  bulkCreateBooking: () => bulkCreateBooking4,
  bulkGetMultiServiceBookingAllowedActions: () => bulkGetMultiServiceBookingAllowedActions4,
  cancelBooking: () => cancelBooking3,
  cancelMultiServiceBooking: () => cancelMultiServiceBooking4,
  confirmBooking: () => confirmBooking3,
  confirmMultiServiceBooking: () => confirmMultiServiceBooking4,
  confirmOrDeclineBooking: () => confirmOrDeclineBooking4,
  createBooking: () => createBooking4,
  createMultiServiceBooking: () => createMultiServiceBooking4,
  declineBooking: () => declineBooking3,
  declineMultiServiceBooking: () => declineMultiServiceBooking4,
  getMultiServiceBooking: () => getMultiServiceBooking4,
  getMultiServiceBookingAvailability: () => getMultiServiceBookingAvailability4,
  markBookingAsPending: () => markBookingAsPending4,
  markMultiServiceBookingAsPending: () => markMultiServiceBookingAsPending4,
  onBookingCanceled: () => onBookingCanceled2,
  onBookingConfirmed: () => onBookingConfirmed2,
  onBookingCreated: () => onBookingCreated2,
  onBookingDeclined: () => onBookingDeclined2,
  onBookingNumberOfParticipantsUpdated: () => onBookingNumberOfParticipantsUpdated2,
  onBookingRescheduled: () => onBookingRescheduled2,
  onBookingUpdated: () => onBookingUpdated2,
  removeBookingsFromMultiServiceBooking: () => removeBookingsFromMultiServiceBooking4,
  rescheduleBooking: () => rescheduleBooking4,
  rescheduleMultiServiceBooking: () => rescheduleMultiServiceBooking4,
  setBookingSubmissionId: () => setBookingSubmissionId4,
  updateExtendedFields: () => updateExtendedFields4,
  updateNumberOfParticipants: () => updateNumberOfParticipants3
});
module.exports = __toCommonJS(index_exports);

// src/bookings-v2-booking-bookings.public.ts
var import_rename_all_nested_keys2 = require("@wix/sdk-runtime/rename-all-nested-keys");
var import_float3 = require("@wix/sdk-runtime/transformations/float");
var import_timestamp3 = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths2 = require("@wix/sdk-runtime/transformations/transform-paths");
var import_sdk_types = require("@wix/sdk-types");

// src/bookings-v2-booking-bookings.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-v2-booking-bookings.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_float = require("@wix/sdk-runtime/transformations/float");
var import_float2 = require("@wix/sdk-runtime/transformations/float");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings-service",
        destPath: "/v2/bookings"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
function resolveComWixpressBookingsBookingsV2BookingsUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bulk/multi_service_bookings",
        destPath: "/v2/bulk/multi_service_bookings"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bookings",
        destPath: "/v2/bookings"
      },
      {
        srcPath: "/bookings/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/multi_service_bookings",
        destPath: "/v2/multi_service_bookings"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings-service/v2/bulk/bookings",
        destPath: "/v2/bulk/bookings"
      },
      {
        srcPath: "/_api/bookings-service/v2/bookings",
        destPath: "/v2/bookings"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings-service",
        destPath: "/v2/bookings"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
function resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl(opts) {
  const domainToMappings = {
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-confirmator/v2/bookings/confirmation",
        destPath: "/v2/bookings/confirmation"
      },
      {
        srcPath: "/bookings/v2/confirmation",
        destPath: "/v2/confirmation"
      },
      {
        srcPath: "/bookings/v2/bulk/confirmation",
        destPath: "/v2/bulk/confirmation"
      },
      {
        srcPath: "/bookings/v2/bulk/bookings/confirmOrDecline",
        destPath: "/v2/bulk/bookings/confirmOrDecline"
      },
      {
        srcPath: "/_api/bookings-confirmator/v2/bulk/confirmation",
        destPath: "/v2/bulk/confirmation"
      },
      {
        srcPath: "/_api/bookings-confirmator/v2/bulk/bookings/confirmOrDecline",
        destPath: "/v2/bulk/bookings/confirmOrDecline"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_bookings";
function confirmOrDeclineBooking(payload) {
  function __confirmOrDeclineBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.confirmator.v2.Confirmator.ConfirmOrDeclineBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl({
        protoPath: "/v2/confirmation/{bookingId}:confirmOrDecline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __confirmOrDeclineBooking;
}
function bulkConfirmOrDeclineBooking(payload) {
  function __bulkConfirmOrDeclineBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.confirmator.v2.Confirmator.BulkConfirmOrDeclineBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl({
        protoPath: "/v2/bulk/bookings/confirmOrDecline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.startDate" },
            { path: "results.item.endDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "results.item.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkConfirmOrDeclineBooking;
}
function createBooking(payload) {
  function __createBooking({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "booking.createdDate" },
          { path: "booking.startDate" },
          { path: "booking.endDate" },
          { path: "booking.updatedDate" },
          { path: "booking.canceledDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "booking.contactDetails.fullAddress.geocode.latitude" },
          { path: "booking.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.CreateBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBooking;
}
function bulkCreateBooking(payload) {
  function __bulkCreateBooking({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "createBookingsInfo.booking.createdDate" },
          { path: "createBookingsInfo.booking.startDate" },
          { path: "createBookingsInfo.booking.endDate" },
          { path: "createBookingsInfo.booking.updatedDate" },
          { path: "createBookingsInfo.booking.canceledDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          {
            path: "createBookingsInfo.booking.contactDetails.fullAddress.geocode.latitude"
          },
          {
            path: "createBookingsInfo.booking.contactDetails.fullAddress.geocode.longitude"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.BulkCreateBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bulk/bookings/create",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.startDate" },
            { path: "results.item.endDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "results.item.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkCreateBooking;
}
function rescheduleBooking(payload) {
  function __rescheduleBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.RescheduleBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/reschedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __rescheduleBooking;
}
function bookingsConfirmBooking(payload) {
  function __bookingsConfirmBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.ConfirmBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/confirm",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsConfirmBooking;
}
function setBookingSubmissionId(payload) {
  function __setBookingSubmissionId({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.SetBookingSubmissionId",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/set-booking-submission-id",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setBookingSubmissionId;
}
function updateExtendedFields(payload) {
  function __updateExtendedFields({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.UpdateExtendedFields",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{id}/update_extended_fields",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __updateExtendedFields;
}
function bookingsDeclineBooking(payload) {
  function __bookingsDeclineBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.DeclineBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/decline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsDeclineBooking;
}
function bookingsCancelBooking(payload) {
  function __bookingsCancelBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.CancelBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/cancel",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsCancelBooking;
}
function bookingsUpdateNumberOfParticipants(payload) {
  function __bookingsUpdateNumberOfParticipants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.UpdateNumberOfParticipants",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/update_number_of_participants",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bookingsUpdateNumberOfParticipants;
}
function markBookingAsPending(payload) {
  function __markBookingAsPending({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.Bookings.MarkBookingAsPending",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2BookingsUrl({
        protoPath: "/v2/bookings/{bookingId}/mark_booking_as_pending",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "booking.createdDate" },
            { path: "booking.startDate" },
            { path: "booking.endDate" },
            { path: "booking.updatedDate" },
            { path: "booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            { path: "booking.contactDetails.fullAddress.geocode.latitude" },
            { path: "booking.contactDetails.fullAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __markBookingAsPending;
}
function createMultiServiceBooking(payload) {
  function __createMultiServiceBooking({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookings.createdDate" },
          { path: "bookings.startDate" },
          { path: "bookings.endDate" },
          { path: "bookings.updatedDate" },
          { path: "bookings.canceledDate" }
        ]
      },
      {
        transformFn: import_float.transformSDKFloatToRESTFloat,
        paths: [
          { path: "bookings.contactDetails.fullAddress.geocode.latitude" },
          { path: "bookings.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.CreateMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createMultiServiceBooking;
}
function rescheduleMultiServiceBooking(payload) {
  function __rescheduleMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.RescheduleMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/reschedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __rescheduleMultiServiceBooking;
}
function getMultiServiceBookingAvailability(payload) {
  function __getMultiServiceBookingAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBookingAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/get_availability",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __getMultiServiceBookingAvailability;
}
function cancelMultiServiceBooking(payload) {
  function __cancelMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.CancelMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/cancel",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __cancelMultiServiceBooking;
}
function markMultiServiceBookingAsPending(payload) {
  function __markMultiServiceBookingAsPending({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.MarkMultiServiceBookingAsPending",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/mark_as_pending",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __markMultiServiceBookingAsPending;
}
function confirmMultiServiceBooking(payload) {
  function __confirmMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.ConfirmMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/confirm",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __confirmMultiServiceBooking;
}
function declineMultiServiceBooking(payload) {
  function __declineMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.DeclineMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}/decline",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __declineMultiServiceBooking;
}
function bulkGetMultiServiceBookingAllowedActions(payload) {
  function __bulkGetMultiServiceBookingAllowedActions({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.BulkGetMultiServiceBookingAllowedActions",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/bulk/multi_service_bookings/get_allowed_actions",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkGetMultiServiceBookingAllowedActions;
}
function getMultiServiceBooking(payload) {
  function __getMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "GET",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/{multiServiceBookingId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "multiServiceBooking.bookings.booking.createdDate" },
            { path: "multiServiceBooking.bookings.booking.startDate" },
            { path: "multiServiceBooking.bookings.booking.endDate" },
            { path: "multiServiceBooking.bookings.booking.updatedDate" },
            { path: "multiServiceBooking.bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getMultiServiceBooking;
}
function addBookingsToMultiServiceBooking(payload) {
  function __addBookingsToMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.AddBookingsToMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/add_bookings_to_multi_service_booking",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookings.booking.createdDate" },
            { path: "bookings.booking.startDate" },
            { path: "bookings.booking.endDate" },
            { path: "bookings.booking.updatedDate" },
            { path: "bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __addBookingsToMultiServiceBooking;
}
function removeBookingsFromMultiServiceBooking(payload) {
  function __removeBookingsFromMultiServiceBooking({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v2.booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.bookings.v2.MultiServiceBookings.RemoveBookingsFromMultiServiceBooking",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({
        protoPath: "/v2/multi_service_bookings/remove_bookings_from_multi_service_booking",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookings.booking.createdDate" },
            { path: "bookings.booking.startDate" },
            { path: "bookings.booking.endDate" },
            { path: "bookings.booking.updatedDate" },
            { path: "bookings.booking.canceledDate" }
          ]
        },
        {
          transformFn: import_float2.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "bookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __removeBookingsFromMultiServiceBooking;
}

// src/bookings-v2-booking-bookings.universal.ts
var MultiServiceBookingType = /* @__PURE__ */ ((MultiServiceBookingType2) => {
  MultiServiceBookingType2["SEQUENTIAL_BOOKINGS"] = "SEQUENTIAL_BOOKINGS";
  MultiServiceBookingType2["SEPARATE_BOOKINGS"] = "SEPARATE_BOOKINGS";
  MultiServiceBookingType2["PARALLEL_BOOKINGS"] = "PARALLEL_BOOKINGS";
  return MultiServiceBookingType2;
})(MultiServiceBookingType || {});
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNDEFINED"] = "UNDEFINED";
  LocationType2["OWNER_BUSINESS"] = "OWNER_BUSINESS";
  LocationType2["OWNER_CUSTOM"] = "OWNER_CUSTOM";
  LocationType2["CUSTOM"] = "CUSTOM";
  return LocationType2;
})(LocationType || {});
var ValueType = /* @__PURE__ */ ((ValueType2) => {
  ValueType2["SHORT_TEXT"] = "SHORT_TEXT";
  ValueType2["LONG_TEXT"] = "LONG_TEXT";
  ValueType2["CHECK_BOX"] = "CHECK_BOX";
  return ValueType2;
})(ValueType || {});
var BookingStatus = /* @__PURE__ */ ((BookingStatus2) => {
  BookingStatus2["CREATED"] = "CREATED";
  BookingStatus2["CONFIRMED"] = "CONFIRMED";
  BookingStatus2["CANCELED"] = "CANCELED";
  BookingStatus2["PENDING"] = "PENDING";
  BookingStatus2["DECLINED"] = "DECLINED";
  BookingStatus2["WAITING_LIST"] = "WAITING_LIST";
  return BookingStatus2;
})(BookingStatus || {});
var PaymentStatus = /* @__PURE__ */ ((PaymentStatus2) => {
  PaymentStatus2["UNDEFINED"] = "UNDEFINED";
  PaymentStatus2["NOT_PAID"] = "NOT_PAID";
  PaymentStatus2["PAID"] = "PAID";
  PaymentStatus2["PARTIALLY_PAID"] = "PARTIALLY_PAID";
  PaymentStatus2["REFUNDED"] = "REFUNDED";
  PaymentStatus2["EXEMPT"] = "EXEMPT";
  return PaymentStatus2;
})(PaymentStatus || {});
var SelectedPaymentOption = /* @__PURE__ */ ((SelectedPaymentOption2) => {
  SelectedPaymentOption2["UNDEFINED"] = "UNDEFINED";
  SelectedPaymentOption2["OFFLINE"] = "OFFLINE";
  SelectedPaymentOption2["ONLINE"] = "ONLINE";
  SelectedPaymentOption2["MEMBERSHIP"] = "MEMBERSHIP";
  SelectedPaymentOption2["MEMBERSHIP_OFFLINE"] = "MEMBERSHIP_OFFLINE";
  return SelectedPaymentOption2;
})(SelectedPaymentOption || {});
var Platform = /* @__PURE__ */ ((Platform2) => {
  Platform2["UNDEFINED_PLATFORM"] = "UNDEFINED_PLATFORM";
  Platform2["WEB"] = "WEB";
  Platform2["MOBILE_APP"] = "MOBILE_APP";
  return Platform2;
})(Platform || {});
var Actor = /* @__PURE__ */ ((Actor2) => {
  Actor2["UNDEFINED_ACTOR"] = "UNDEFINED_ACTOR";
  Actor2["BUSINESS"] = "BUSINESS";
  Actor2["CUSTOMER"] = "CUSTOMER";
  return Actor2;
})(Actor || {});
var IdentificationDataIdentityType = /* @__PURE__ */ ((IdentificationDataIdentityType2) => {
  IdentificationDataIdentityType2["UNKNOWN"] = "UNKNOWN";
  IdentificationDataIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  IdentificationDataIdentityType2["MEMBER"] = "MEMBER";
  IdentificationDataIdentityType2["WIX_USER"] = "WIX_USER";
  IdentificationDataIdentityType2["APP"] = "APP";
  return IdentificationDataIdentityType2;
})(IdentificationDataIdentityType || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
var LocationLocationType = /* @__PURE__ */ ((LocationLocationType2) => {
  LocationLocationType2["UNDEFINED"] = "UNDEFINED";
  LocationLocationType2["OWNER_BUSINESS"] = "OWNER_BUSINESS";
  LocationLocationType2["OWNER_CUSTOM"] = "OWNER_CUSTOM";
  LocationLocationType2["CUSTOM"] = "CUSTOM";
  return LocationLocationType2;
})(LocationLocationType || {});
var IdentityType = /* @__PURE__ */ ((IdentityType2) => {
  IdentityType2["UNKNOWN"] = "UNKNOWN";
  IdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  IdentityType2["MEMBER"] = "MEMBER";
  IdentityType2["WIX_USER"] = "WIX_USER";
  IdentityType2["APP"] = "APP";
  return IdentityType2;
})(IdentityType || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
async function confirmOrDeclineBooking2(bookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    paymentStatus: options?.paymentStatus
  });
  const reqOpts = confirmOrDeclineBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingId: "$[0]",
          paymentStatus: "$[1].paymentStatus"
        },
        singleArgumentUnchanged: false
      },
      ["bookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkConfirmOrDeclineBooking2(details, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    details,
    returnEntity: options?.returnEntity
  });
  const reqOpts = bulkConfirmOrDeclineBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          details: "$[0]",
          returnEntity: "$[1].returnEntity"
        },
        singleArgumentUnchanged: false
      },
      ["details", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function createBooking2(booking, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    booking,
    participantNotification: options?.participantNotification,
    sendSmsReminder: options?.sendSmsReminder,
    flowControlSettings: options?.flowControlSettings
  });
  const reqOpts = createBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          booking: "$[0]",
          participantNotification: "$[1].participantNotification",
          sendSmsReminder: "$[1].sendSmsReminder",
          flowControlSettings: "$[1].flowControlSettings"
        },
        singleArgumentUnchanged: false
      },
      ["booking", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkCreateBooking2(createBookingsInfo, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    createBookingsInfo,
    returnFullEntity: options?.returnFullEntity
  });
  const reqOpts = bulkCreateBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          createBookingsInfo: "$[0]",
          returnFullEntity: "$[1].returnFullEntity"
        },
        singleArgumentUnchanged: false
      },
      ["createBookingsInfo", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function rescheduleBooking2(bookingId, slot, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    slot,
    revision: options?.revision,
    participantNotification: options?.participantNotification,
    flowControlSettings: options?.flowControlSettings,
    totalParticipants: options?.totalParticipants,
    participantsChoices: options?.participantsChoices
  });
  const reqOpts = rescheduleBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingId: "$[0]",
          slot: "$[1]",
          revision: "$[2].revision",
          participantNotification: "$[2].participantNotification",
          flowControlSettings: "$[2].flowControlSettings",
          totalParticipants: "$[2].totalParticipants",
          participantsChoices: "$[2].participantsChoices"
        },
        singleArgumentUnchanged: false
      },
      ["bookingId", "slot", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function confirmBooking(bookingId, revision, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    revision,
    participantNotification: options?.participantNotification,
    sendSmsReminder: options?.sendSmsReminder,
    paymentStatus: options?.paymentStatus,
    doubleBooked: options?.doubleBooked,
    flowControlSettings: options?.flowControlSettings
  });
  const reqOpts = bookingsConfirmBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingId: "$[0]",
          revision: "$[1]",
          participantNotification: "$[2].participantNotification",
          sendSmsReminder: "$[2].sendSmsReminder",
          paymentStatus: "$[2].paymentStatus",
          doubleBooked: "$[2].doubleBooked",
          flowControlSettings: "$[2].flowControlSettings"
        },
        singleArgumentUnchanged: false
      },
      ["bookingId", "revision", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function setBookingSubmissionId2(bookingId, submissionId) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    submissionId
  });
  const reqOpts = setBookingSubmissionId(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingId: "$[0]", submissionId: "$[1]" },
        singleArgumentUnchanged: false
      },
      ["bookingId", "submissionId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateExtendedFields2(_id, namespace, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    id: _id,
    namespace,
    namespaceData: options?.namespaceData
  });
  const reqOpts = updateExtendedFields(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          id: "$[0]",
          namespace: "$[1]",
          namespaceData: "$[2].namespaceData"
        },
        singleArgumentUnchanged: false
      },
      ["_id", "namespace", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function declineBooking(bookingId, revision, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    revision,
    participantNotification: options?.participantNotification,
    paymentStatus: options?.paymentStatus,
    doubleBooked: options?.doubleBooked,
    flowControlSettings: options?.flowControlSettings
  });
  const reqOpts = bookingsDeclineBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingId: "$[0]",
          revision: "$[1]",
          participantNotification: "$[2].participantNotification",
          paymentStatus: "$[2].paymentStatus",
          doubleBooked: "$[2].doubleBooked",
          flowControlSettings: "$[2].flowControlSettings"
        },
        singleArgumentUnchanged: false
      },
      ["bookingId", "revision", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function cancelBooking(bookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    participantNotification: options?.participantNotification,
    flowControlSettings: options?.flowControlSettings,
    revision: options?.revision
  });
  const reqOpts = bookingsCancelBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingId: "$[0]",
          participantNotification: "$[1].participantNotification",
          flowControlSettings: "$[1].flowControlSettings",
          revision: "$[1].revision"
        },
        singleArgumentUnchanged: false
      },
      ["bookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateNumberOfParticipants(bookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    revision: options?.revision,
    totalParticipants: options?.totalParticipants,
    participantsChoices: options?.participantsChoices
  });
  const reqOpts = bookingsUpdateNumberOfParticipants(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingId: "$[0]",
          revision: "$[1].revision",
          totalParticipants: "$[1].totalParticipants",
          participantsChoices: "$[1].participantsChoices"
        },
        singleArgumentUnchanged: false
      },
      ["bookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function markBookingAsPending2(bookingId, revision, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingId,
    revision,
    participantNotification: options?.participantNotification,
    sendSmsReminder: options?.sendSmsReminder,
    paymentStatus: options?.paymentStatus,
    doubleBooked: options?.doubleBooked,
    flowControlSettings: options?.flowControlSettings
  });
  const reqOpts = markBookingAsPending(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookingId: "$[0]",
          revision: "$[1]",
          participantNotification: "$[2].participantNotification",
          sendSmsReminder: "$[2].sendSmsReminder",
          paymentStatus: "$[2].paymentStatus",
          doubleBooked: "$[2].doubleBooked",
          flowControlSettings: "$[2].flowControlSettings"
        },
        singleArgumentUnchanged: false
      },
      ["bookingId", "revision", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function createMultiServiceBooking2(bookings, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookings,
    participantNotification: options?.participantNotification,
    sendSmsReminder: options?.sendSmsReminder,
    flowControlSettings: options?.flowControlSettings,
    returnFullEntity: options?.returnFullEntity,
    multiServiceBookingType: options?.multiServiceBookingType
  });
  const reqOpts = createMultiServiceBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          bookings: "$[0]",
          participantNotification: "$[1].participantNotification",
          sendSmsReminder: "$[1].sendSmsReminder",
          flowControlSettings: "$[1].flowControlSettings",
          returnFullEntity: "$[1].returnFullEntity",
          multiServiceBookingType: "$[1].multiServiceBookingType"
        },
        singleArgumentUnchanged: false
      },
      ["bookings", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function rescheduleMultiServiceBooking2(multiServiceBookingId, rescheduleBookingsInfo, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId,
    rescheduleBookingsInfo,
    participantNotification: options?.participantNotification,
    flowControlSettings: options?.flowControlSettings,
    returnFullEntity: options?.returnFullEntity
  });
  const reqOpts = rescheduleMultiServiceBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          multiServiceBookingId: "$[0]",
          rescheduleBookingsInfo: "$[1]",
          participantNotification: "$[2].participantNotification",
          flowControlSettings: "$[2].flowControlSettings",
          returnFullEntity: "$[2].returnFullEntity"
        },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId", "rescheduleBookingsInfo", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getMultiServiceBookingAvailability2(multiServiceBookingId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId
  });
  const reqOpts = getMultiServiceBookingAvailability(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { multiServiceBookingId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function cancelMultiServiceBooking2(multiServiceBookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId,
    participantNotification: options?.participantNotification,
    flowControlSettings: options?.flowControlSettings,
    returnFullEntity: options?.returnFullEntity
  });
  const reqOpts = cancelMultiServiceBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          multiServiceBookingId: "$[0]",
          participantNotification: "$[1].participantNotification",
          flowControlSettings: "$[1].flowControlSettings",
          returnFullEntity: "$[1].returnFullEntity"
        },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function markMultiServiceBookingAsPending2(multiServiceBookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId,
    markAsPendingBookingsInfo: options?.markAsPendingBookingsInfo,
    participantNotification: options?.participantNotification,
    sendSmsReminder: options?.sendSmsReminder,
    doubleBooked: options?.doubleBooked,
    returnFullEntity: options?.returnFullEntity,
    flowControlSettings: options?.flowControlSettings
  });
  const reqOpts = markMultiServiceBookingAsPending(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          multiServiceBookingId: "$[0]",
          markAsPendingBookingsInfo: "$[1].markAsPendingBookingsInfo",
          participantNotification: "$[1].participantNotification",
          sendSmsReminder: "$[1].sendSmsReminder",
          doubleBooked: "$[1].doubleBooked",
          returnFullEntity: "$[1].returnFullEntity",
          flowControlSettings: "$[1].flowControlSettings"
        },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function confirmMultiServiceBooking2(multiServiceBookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId,
    confirmBookingsInfo: options?.confirmBookingsInfo,
    participantNotification: options?.participantNotification,
    sendSmsReminder: options?.sendSmsReminder,
    doubleBooked: options?.doubleBooked,
    returnFullEntity: options?.returnFullEntity,
    flowControlSettings: options?.flowControlSettings
  });
  const reqOpts = confirmMultiServiceBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          multiServiceBookingId: "$[0]",
          confirmBookingsInfo: "$[1].confirmBookingsInfo",
          participantNotification: "$[1].participantNotification",
          sendSmsReminder: "$[1].sendSmsReminder",
          doubleBooked: "$[1].doubleBooked",
          returnFullEntity: "$[1].returnFullEntity",
          flowControlSettings: "$[1].flowControlSettings"
        },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function declineMultiServiceBooking2(multiServiceBookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId,
    declineBookingsInfo: options?.declineBookingsInfo,
    participantNotification: options?.participantNotification,
    doubleBooked: options?.doubleBooked,
    returnFullEntity: options?.returnFullEntity,
    flowControlSettings: options?.flowControlSettings
  });
  const reqOpts = declineMultiServiceBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          multiServiceBookingId: "$[0]",
          declineBookingsInfo: "$[1].declineBookingsInfo",
          participantNotification: "$[1].participantNotification",
          doubleBooked: "$[1].doubleBooked",
          returnFullEntity: "$[1].returnFullEntity",
          flowControlSettings: "$[1].flowControlSettings"
        },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkGetMultiServiceBookingAllowedActions2(multiServiceBookingIds) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingIds
  });
  const reqOpts = bulkGetMultiServiceBookingAllowedActions(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { multiServiceBookingIds: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingIds"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getMultiServiceBooking2(multiServiceBookingId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId
  });
  const reqOpts = getMultiServiceBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.multiServiceBooking;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { multiServiceBookingId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function addBookingsToMultiServiceBooking2(multiServiceBookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId,
    bookings: options?.bookings,
    returnFullEntity: options?.returnFullEntity
  });
  const reqOpts = addBookingsToMultiServiceBooking(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          multiServiceBookingId: "$[0]",
          bookings: "$[1].bookings",
          returnFullEntity: "$[1].returnFullEntity"
        },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function removeBookingsFromMultiServiceBooking2(multiServiceBookingId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    multiServiceBookingId,
    bookings: options?.bookings,
    returnFullEntity: options?.returnFullEntity
  });
  const reqOpts = removeBookingsFromMultiServiceBooking(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          multiServiceBookingId: "$[0]",
          bookings: "$[1].bookings",
          returnFullEntity: "$[1].returnFullEntity"
        },
        singleArgumentUnchanged: false
      },
      ["multiServiceBookingId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-v2-booking-bookings.public.ts
function confirmOrDeclineBooking3(httpClient) {
  return (bookingId, options) => confirmOrDeclineBooking2(
    bookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkConfirmOrDeclineBooking3(httpClient) {
  return (details, options) => bulkConfirmOrDeclineBooking2(
    details,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function createBooking3(httpClient) {
  return (booking, options) => createBooking2(
    booking,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkCreateBooking3(httpClient) {
  return (createBookingsInfo, options) => bulkCreateBooking2(
    createBookingsInfo,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function rescheduleBooking3(httpClient) {
  return (bookingId, slot, options) => rescheduleBooking2(
    bookingId,
    slot,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function confirmBooking2(httpClient) {
  return (bookingId, revision, options) => confirmBooking(
    bookingId,
    revision,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function setBookingSubmissionId3(httpClient) {
  return (bookingId, submissionId) => setBookingSubmissionId2(
    bookingId,
    submissionId,
    // @ts-ignore
    { httpClient }
  );
}
function updateExtendedFields3(httpClient) {
  return (_id, namespace, options) => updateExtendedFields2(
    _id,
    namespace,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function declineBooking2(httpClient) {
  return (bookingId, revision, options) => declineBooking(
    bookingId,
    revision,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function cancelBooking2(httpClient) {
  return (bookingId, options) => cancelBooking(
    bookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function updateNumberOfParticipants2(httpClient) {
  return (bookingId, options) => updateNumberOfParticipants(
    bookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function markBookingAsPending3(httpClient) {
  return (bookingId, revision, options) => markBookingAsPending2(
    bookingId,
    revision,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function createMultiServiceBooking3(httpClient) {
  return (bookings, options) => createMultiServiceBooking2(
    bookings,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function rescheduleMultiServiceBooking3(httpClient) {
  return (multiServiceBookingId, rescheduleBookingsInfo, options) => rescheduleMultiServiceBooking2(
    multiServiceBookingId,
    rescheduleBookingsInfo,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function getMultiServiceBookingAvailability3(httpClient) {
  return (multiServiceBookingId) => getMultiServiceBookingAvailability2(
    multiServiceBookingId,
    // @ts-ignore
    { httpClient }
  );
}
function cancelMultiServiceBooking3(httpClient) {
  return (multiServiceBookingId, options) => cancelMultiServiceBooking2(
    multiServiceBookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function markMultiServiceBookingAsPending3(httpClient) {
  return (multiServiceBookingId, options) => markMultiServiceBookingAsPending2(
    multiServiceBookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function confirmMultiServiceBooking3(httpClient) {
  return (multiServiceBookingId, options) => confirmMultiServiceBooking2(
    multiServiceBookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function declineMultiServiceBooking3(httpClient) {
  return (multiServiceBookingId, options) => declineMultiServiceBooking2(
    multiServiceBookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkGetMultiServiceBookingAllowedActions3(httpClient) {
  return (multiServiceBookingIds) => bulkGetMultiServiceBookingAllowedActions2(
    multiServiceBookingIds,
    // @ts-ignore
    { httpClient }
  );
}
function getMultiServiceBooking3(httpClient) {
  return (multiServiceBookingId) => getMultiServiceBooking2(
    multiServiceBookingId,
    // @ts-ignore
    { httpClient }
  );
}
function addBookingsToMultiServiceBooking3(httpClient) {
  return (multiServiceBookingId, options) => addBookingsToMultiServiceBooking2(
    multiServiceBookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function removeBookingsFromMultiServiceBooking3(httpClient) {
  return (multiServiceBookingId, options) => removeBookingsFromMultiServiceBooking2(
    multiServiceBookingId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onBookingCanceled = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v2.booking_canceled",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths2.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "data.booking.createdDate" },
          { path: "data.booking.startDate" },
          { path: "data.booking.endDate" },
          { path: "data.booking.updatedDate" },
          { path: "data.booking.canceledDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_float3.transformRESTFloatToSDKFloat,
        paths: [
          {
            path: "data.booking.contactDetails.fullAddress.geocode.latitude"
          },
          {
            path: "data.booking.contactDetails.fullAddress.geocode.longitude"
          }
        ]
      }
    ])
  )
)();
var onBookingConfirmed = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v2.booking_confirmed",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths2.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "data.booking.createdDate" },
          { path: "data.booking.startDate" },
          { path: "data.booking.endDate" },
          { path: "data.booking.updatedDate" },
          { path: "data.booking.canceledDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_float3.transformRESTFloatToSDKFloat,
        paths: [
          {
            path: "data.booking.contactDetails.fullAddress.geocode.latitude"
          },
          {
            path: "data.booking.contactDetails.fullAddress.geocode.longitude"
          }
        ]
      }
    ])
  )
)();
var onBookingCreated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v2.booking_created",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths2.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.startDate" },
          { path: "entity.endDate" },
          { path: "entity.updatedDate" },
          { path: "entity.canceledDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_float3.transformRESTFloatToSDKFloat,
        paths: [
          { path: "entity.contactDetails.fullAddress.geocode.latitude" },
          { path: "entity.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ])
  )
)();
var onBookingDeclined = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v2.booking_declined",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths2.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "data.booking.createdDate" },
          { path: "data.booking.startDate" },
          { path: "data.booking.endDate" },
          { path: "data.booking.updatedDate" },
          { path: "data.booking.canceledDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_float3.transformRESTFloatToSDKFloat,
        paths: [
          {
            path: "data.booking.contactDetails.fullAddress.geocode.latitude"
          },
          {
            path: "data.booking.contactDetails.fullAddress.geocode.longitude"
          }
        ]
      }
    ])
  )
)();
var onBookingNumberOfParticipantsUpdated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v2.booking_number_of_participants_updated",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths2.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "data.booking.createdDate" },
          { path: "data.booking.startDate" },
          { path: "data.booking.endDate" },
          { path: "data.booking.updatedDate" },
          { path: "data.booking.canceledDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_float3.transformRESTFloatToSDKFloat,
        paths: [
          {
            path: "data.booking.contactDetails.fullAddress.geocode.latitude"
          },
          {
            path: "data.booking.contactDetails.fullAddress.geocode.longitude"
          }
        ]
      }
    ])
  )
)();
var onBookingRescheduled = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v2.booking_rescheduled",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths2.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "data.previousStartDate" },
          { path: "data.previousEndDate" },
          { path: "data.booking.createdDate" },
          { path: "data.booking.startDate" },
          { path: "data.booking.endDate" },
          { path: "data.booking.updatedDate" },
          { path: "data.booking.canceledDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_float3.transformRESTFloatToSDKFloat,
        paths: [
          {
            path: "data.booking.contactDetails.fullAddress.geocode.latitude"
          },
          {
            path: "data.booking.contactDetails.fullAddress.geocode.longitude"
          }
        ]
      }
    ])
  )
)();
var onBookingUpdated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v2.booking_updated",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths2.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.startDate" },
          { path: "entity.endDate" },
          { path: "entity.updatedDate" },
          { path: "entity.canceledDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: import_float3.transformRESTFloatToSDKFloat,
        paths: [
          { path: "entity.contactDetails.fullAddress.geocode.latitude" },
          { path: "entity.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ])
  )
)();

// src/bookings-v2-booking-bookings.context.ts
var import_rest_modules3 = require("@wix/sdk-runtime/rest-modules");
var import_event_definition_modules = require("@wix/sdk-runtime/event-definition-modules");
var confirmOrDeclineBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(confirmOrDeclineBooking3);
var bulkConfirmOrDeclineBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkConfirmOrDeclineBooking3);
var createBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(createBooking3);
var bulkCreateBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(bulkCreateBooking3);
var rescheduleBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(rescheduleBooking3);
var confirmBooking3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(confirmBooking2);
var setBookingSubmissionId4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(setBookingSubmissionId3);
var updateExtendedFields4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(updateExtendedFields3);
var declineBooking3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(declineBooking2);
var cancelBooking3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(cancelBooking2);
var updateNumberOfParticipants3 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(updateNumberOfParticipants2);
var markBookingAsPending4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(markBookingAsPending3);
var createMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(createMultiServiceBooking3);
var rescheduleMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(rescheduleMultiServiceBooking3);
var getMultiServiceBookingAvailability4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(getMultiServiceBookingAvailability3);
var cancelMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(cancelMultiServiceBooking3);
var markMultiServiceBookingAsPending4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(markMultiServiceBookingAsPending3);
var confirmMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(confirmMultiServiceBooking3);
var declineMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(declineMultiServiceBooking3);
var bulkGetMultiServiceBookingAllowedActions4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(
  bulkGetMultiServiceBookingAllowedActions3
);
var getMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(getMultiServiceBooking3);
var addBookingsToMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(addBookingsToMultiServiceBooking3);
var removeBookingsFromMultiServiceBooking4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(removeBookingsFromMultiServiceBooking3);
var onBookingCanceled2 = (0, import_event_definition_modules.createEventModule)(onBookingCanceled);
var onBookingConfirmed2 = (0, import_event_definition_modules.createEventModule)(onBookingConfirmed);
var onBookingCreated2 = (0, import_event_definition_modules.createEventModule)(onBookingCreated);
var onBookingDeclined2 = (0, import_event_definition_modules.createEventModule)(onBookingDeclined);
var onBookingNumberOfParticipantsUpdated2 = (0, import_event_definition_modules.createEventModule)(onBookingNumberOfParticipantsUpdated);
var onBookingRescheduled2 = (0, import_event_definition_modules.createEventModule)(onBookingRescheduled);
var onBookingUpdated2 = (0, import_event_definition_modules.createEventModule)(onBookingUpdated);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Actor,
  BookingStatus,
  IdentificationDataIdentityType,
  IdentityType,
  LocationLocationType,
  LocationType,
  MultiServiceBookingType,
  PaymentStatus,
  Platform,
  SelectedPaymentOption,
  SortOrder,
  ValueType,
  WebhookIdentityType,
  addBookingsToMultiServiceBooking,
  bulkConfirmOrDeclineBooking,
  bulkCreateBooking,
  bulkGetMultiServiceBookingAllowedActions,
  cancelBooking,
  cancelMultiServiceBooking,
  confirmBooking,
  confirmMultiServiceBooking,
  confirmOrDeclineBooking,
  createBooking,
  createMultiServiceBooking,
  declineBooking,
  declineMultiServiceBooking,
  getMultiServiceBooking,
  getMultiServiceBookingAvailability,
  markBookingAsPending,
  markMultiServiceBookingAsPending,
  onBookingCanceled,
  onBookingConfirmed,
  onBookingCreated,
  onBookingDeclined,
  onBookingNumberOfParticipantsUpdated,
  onBookingRescheduled,
  onBookingUpdated,
  removeBookingsFromMultiServiceBooking,
  rescheduleBooking,
  rescheduleMultiServiceBooking,
  setBookingSubmissionId,
  updateExtendedFields,
  updateNumberOfParticipants
});
//# sourceMappingURL=index.js.map