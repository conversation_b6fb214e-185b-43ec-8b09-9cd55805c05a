import { am as CreateStaffMemberRequest$1, an as CreateStaffMemberResponse$1, ao as GetStaffMemberRequest$1, ap as GetStaffMemberResponse$1, aB as UpdateStaffMemberRequest$1, aC as UpdateStaffMemberResponse$1, aD as DeleteStaffMemberRequest$1, aE as DeleteStaffMemberResponse$1, aF as QueryStaffMembersRequest$1, aJ as QueryStaffMembersResponse$1, aM as CountStaffMembersRequest$1, d as CountStaffMembersResponse$1, aN as ConnectStaffMemberToUserRequest$1, f as ConnectStaffMemberToUserResponse$1, aO as SearchStaffMembersRequest$1, i as SearchStaffMembersResponse$1, bb as DisconnectStaffMemberFromUserRequest$1, j as DisconnectStaffMemberFromUserResponse$1, bc as AssignWorkingHoursScheduleRequest$1, k as AssignWorkingHoursScheduleResponse$1, bd as AssignCustomScheduleRequest$1, m as AssignCustomScheduleResponse$1, be as BulkUpdateStaffMemberTagsRequest$1, n as BulkUpdateStaffMemberTagsResponse$1, bj as BulkUpdateStaffMemberTagsByFilterRequest$1, p as BulkUpdateStaffMemberTagsByFilterResponse$1 } from './bookings-staff-v1-staff-member-staff-members.universal-Tbhk-dK0.mjs';
import '@wix/sdk-types';

/**
 * An individual providing services within Wix Bookings. Only staff members who
 * are also Wix users can manage their working hours in the dashboard.
 */
interface StaffMember {
    /**
     * Staff member ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Staff member name.
     * @minLength 1
     * @maxLength 40
     */
    name?: string | null;
    /**
     * Staff member's email address.
     * @maxLength 320
     * @format EMAIL
     */
    email?: string | null;
    /**
     * Staff member's phone number.
     * @maxLength 20
     * @format PHONE
     */
    phone?: string | null;
    /**
     * Description for the staff member. For example,
     * `Experienced nail technician specialized in gel and acrylic nails`.
     * @maxLength 500
     */
    description?: string | null;
    /** Staff media. */
    mainMedia?: MediaItem;
    /**
     * Staff member's *resource ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)),
     * identical to `resource.id`.
     * @readonly
     * @format GUID
     */
    resourceId?: string | null;
    /**
     * Details about the *resource object*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
     * associated with the staff member. Available only if you specify `RESOURCE_DETAILS`
     * in the `fields` array.
     * @readonly
     */
    resource?: Resource;
    /**
     * Identity of the Wix user associated with the staff member. Learn more about
     * _identities_
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).
     * @readonly
     */
    associatedWixIdentity?: AssociatedWixIdentity;
    /**
     * Revision number, which increments by 1 each time the staff member is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the staff member.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time the staff member was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Time the staff member was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    updatedDate?: Date | null;
    /** Extensions enabling users to save custom data related to the staff member. */
    extendedFields?: ExtendedFields;
    /**
     * Tags allowing you to classify staff members. Learn more about *tags*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).
     */
    tags?: Tags;
}
interface MediaItem extends MediaItemMediaOneOf {
    /** Staff member's main image. */
    image?: Image;
}
/** @oneof */
interface MediaItemMediaOneOf {
    /** Staff member's main image. */
    image?: Image;
}
interface Image {
    /**
     * WixMedia image ID. To manage media assets, use the [Media Manager API](https://dev.wix.com/docs/rest/assets/media/media-manager/introduction)
     * @maxLength 2048
     */
    id?: string;
    /**
     * Image URL.
     * @maxLength 2048
     */
    url?: string;
    /**
     * Original image height.
     * @readonly
     */
    height?: number;
    /**
     * Original image width.
     * @readonly
     */
    width?: number;
    /**
     * Image alt text.
     * @maxLength 2048
     */
    altText?: string | null;
    /**
     * Image filename.
     * @readonly
     * @maxLength 2048
     */
    filename?: string | null;
}
interface Resource {
    /**
     * ID of the *resource*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
     * associated with the staff member.
     * @format GUID
     */
    id?: string | null;
    /**
     * Working hour *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * of the staff member. By default, identical the opening hours of the
     * business's *default location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * If the staff has custom working hours, identical to `eventsSchedule`.
     * @maxSize 1
     */
    workingHoursSchedules?: WorkingHoursSchedule[];
    /**
     * Event *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * of the staff member.
     */
    eventsSchedule?: EventSchedule;
    /**
     * Whether the staff member works according to business's *default location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))
     * opening hours.
     *
     * `false`: The staff has custom working hours.
     *
     * Default: `true`
     */
    usesDefaultWorkingHours?: boolean;
}
interface WorkingHoursSchedule {
    /**
     * ID of the working hour *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * associated with the staff member. Currently, each staff member can't have more than a
     * single working hour schedule. Learn more about
     * _how Wix Bookings uses the Calendar APIs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).
     * @format GUID
     */
    id?: string | null;
    /**
     * Whether the working hour schedule is shared with the business. If this field
     * isn't available, the schedule isn't shared but specific for the staff member.
     */
    shared?: boolean;
}
interface EventSchedule {
    /**
     * ID of the event *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * associated with the staff member. Learn more about
     * _how Wix Bookings uses the Calendar APIs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).
     * @format GUID
     */
    id?: string | null;
}
/** A staff member resource can be associated with a Wix user via assignment of a permissions role in the business manager. */
interface AssociatedWixIdentity {
    /**
     * Information about the identity connected to the staff member. Available only
     * if the staff member is connected to a Wix user. Learn more about *identities*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).
     */
    identificationData?: CommonIdentificationData;
    /**
     * Connection status.
     * @readonly
     * @deprecated Connection status.
     * @replacedBy connection
     * @targetRemovalDate 2024-12-01
     */
    connectionStatus?: AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals;
    /**
     * Connection status. Available only if you specify `ASSOCIATED_IDENTITY_STATUS`
     * in the `fields` array.
     * @readonly
     */
    connection?: Connection;
}
interface CommonIdentificationData extends CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /**
     * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system. Retrievable via the [Contacts API](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/introduction)
     * @format GUID
     */
    contactId?: string | null;
}
/** @oneof */
interface CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum AssociatedWixIdentityConnectionStatusEnumConnectionStatus {
    /** There is no information about the connection status. */
    UNKNOWN = "UNKNOWN",
    /** The Wix user is connected to the staff member. */
    CONNECTED = "CONNECTED",
    /** The Wix user must accept the invitation to join **.wix.com.** or the site. */
    PENDING = "PENDING",
    /** The invitation to join **.wix.com.** or the site has expired. */
    EXPIRED = "EXPIRED",
    /** The Wix user was disconnected from the staff member. */
    DISCONNECTED = "DISCONNECTED"
}
/** @enumType */
type AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals = AssociatedWixIdentityConnectionStatusEnumConnectionStatus | 'UNKNOWN' | 'CONNECTED' | 'PENDING' | 'EXPIRED' | 'DISCONNECTED';
interface Connection {
    /**
     * Connection status of the Wix user and the staff member.
     * @readonly
     */
    status?: AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
/**
 * Common object for tags.
 * Should be use as in this example:
 * message Foo {
 * string id = 1;
 * ...
 * Tags tags = 5
 * }
 *
 * example of taggable entity
 * {
 * id: "123"
 * tags: {
 * tags: {
 * tag_ids:["11","22"]
 * },
 * private_tags: {
 * tag_ids: ["33", "44"]
 * }
 * }
 * }
 */
interface Tags {
    /** Tags that require an additional permission in order to access them, normally not given to site members or visitors. */
    privateTags?: TagList;
    /** Tags that are exposed to anyone who has access to the labeled entity itself, including site members and visitors. */
    tags?: TagList;
}
interface TagList {
    /**
     * List of tag IDs
     * @maxSize 100
     * @maxLength 5
     */
    tagIds?: string[];
}
interface CreateStaffMemberRequest {
    /** Staff member to create. */
    staffMember: StaffMember;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
declare enum RequestedFields {
    /** Includes `resource` in the response. */
    RESOURCE_DETAILS = "RESOURCE_DETAILS",
    /** Includes `associatedIdentityStatus` in the response. */
    ASSOCIATED_IDENTITY_STATUS = "ASSOCIATED_IDENTITY_STATUS"
}
/** @enumType */
type RequestedFieldsWithLiterals = RequestedFields | 'RESOURCE_DETAILS' | 'ASSOCIATED_IDENTITY_STATUS';
interface CreateStaffMemberResponse {
    /** Created staff member. */
    staffMember?: StaffMember;
}
interface GetStaffMemberRequest {
    /**
     * ID of the staff member to retrieve.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface GetStaffMemberResponse {
    /** Retrieved staff member. */
    staffMember?: StaffMember;
}
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface CursorPagingMetadata {
    /**
     * Number of items returned in the current response page.
     *
     * This count reflects the actual number of items in the current result set,
     * which may be less than the requested limit if fewer items are available.
     */
    count?: number | null;
    /**
     * Navigation cursors for moving between result pages.
     *
     * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor
     * to retrieve subsequent pages and `prev` cursor to go back to previous pages.
     * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).
     */
    cursors?: Cursors;
    /**
     * Indicates whether additional results are available beyond the current page.
     *
     * - `true`: More results exist and can be retrieved using the `next` cursor.
     * - `false`: This is the final page of results.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor token for retrieving the next page of results.
     *
     * Use this token in subsequent requests to continue pagination forward.
     * Value is `null` when on the last page of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor token for retrieving the previous page of results.
     *
     * Use this token to navigate backwards through result pages.
     * Value is `null` when on the first page of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface UpdateStaffMemberRequest {
    /** Staff member to update. */
    staffMember: StaffMember;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface UpdateStaffMemberResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface DeleteStaffMemberRequest {
    /**
     * ID of the staff member to delete.
     * @format GUID
     */
    staffMemberId: string;
}
interface DeleteStaffMemberResponse {
}
interface QueryStaffMembersRequest {
    /**
     * Information about filters, paging, and sorting. See the article about
     * booking policy filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for all supported filters and sorting options.
     */
    query?: CursorQuery;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 10
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface QueryStaffMembersResponse {
    /** Retrieved staff members. */
    staffMembers?: StaffMember[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CountStaffMembersRequest {
    /**
     * Filter to base the count upon. See the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
}
interface CountStaffMembersResponse {
    /** Total number of staff members matching the given filter. */
    count?: number;
}
interface ConnectStaffMemberToUserRequest {
    /**
     * ID of the staff member to connect to the Wix user.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * Email of the Wix user to connect to the staff member.
     *
     * Default: Email of the staff member.
     * @maxLength 320
     * @format EMAIL
     */
    email?: string | null;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface ConnectStaffMemberToUserResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface SearchStaffMembersRequest {
    /**
     * Search criteria including filter, sort, and paging options.
     *
     * See the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)) for complete filter and sorting options.
     */
    search: CursorSearch;
    /**
     * Conditional fields to return in the response.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface CursorSearch extends CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. Can't be used together with 'paging'.
     * `cursor_paging.cursor` can't be used together with `filter` or `sort`.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object for narrowing search results. For example, to return only staff members with specific email domains: `"filter": {"email": {"$contains": "@company.com"}}`.
     *
     * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).
     */
    filter?: Record<string, any> | null;
    /**
     * Array of sort objects specifying result order. For example, to sort by creation date in descending order: `"sort": [{"fieldName": "createdDate", "order": "DESC"}]`.
     *
     * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).
     * @maxSize 10
     */
    sort?: Sorting[];
    /** Free text to match in searchable fields. */
    search?: SearchDetails;
}
/** @oneof */
interface CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. Can't be used together with 'paging'.
     * `cursor_paging.cursor` can't be used together with `filter` or `sort`.
     */
    cursorPaging?: CursorPaging;
}
interface SearchDetails {
    /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */
    mode?: ModeWithLiterals;
    /**
     * Search term or expression.
     * @maxLength 100
     */
    expression?: string | null;
    /**
     * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `resource.workingHoursSchedules.shared`.
     * @maxLength 200
     * @maxSize 20
     */
    fields?: string[];
    /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */
    fuzzy?: boolean;
}
declare enum Mode {
    /** At least 1 of the search terms must be present. */
    OR = "OR",
    /** All search terms must be present. */
    AND = "AND"
}
/** @enumType */
type ModeWithLiterals = Mode | 'OR' | 'AND';
interface SearchStaffMembersResponse {
    /**
     * Retrieved staff members that match the search criteria specified in the request.
     *
     * Each staff member includes their profile information such as name, email, phone number,
     * working hours schedule, connected user details, and any conditional fields requested
     * (resource details, conferencing providers, or identity status).
     */
    staffMembers?: StaffMember[];
    /**
     * Cursor-based paging metadata for navigating through search results.
     *
     * Contains pagination details including current cursor position, availability of additional
     * results, and item counts. Use the `next` cursor to retrieve subsequent result pages.
     * Note: Staff members don't support aggregations, so only cursor and count information is provided.
     */
    pagingMetadata?: CursorPagingMetadata;
    /**
     * Aggregation data results (currently reserved for future use).
     *
     * Staff member searches don't currently support aggregations. This field is reserved
     * for potential future aggregation capabilities such as grouping by working hours,
     * location assignments, or user connection status.
     */
    aggregationData?: AggregationData;
}
interface AggregationData {
    /**
     * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.
     * @maxSize 10000
     */
    results?: AggregationResults[];
}
interface ValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 100
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number;
}
interface RangeAggregationResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number;
}
declare enum ScalarType {
    UNKNOWN_SCALAR_TYPE = "UNKNOWN_SCALAR_TYPE",
    /** Total number of distinct values. */
    COUNT_DISTINCT = "COUNT_DISTINCT",
    /** Minimum value. */
    MIN = "MIN",
    /** Maximum value. */
    MAX = "MAX"
}
/** @enumType */
type ScalarTypeWithLiterals = ScalarType | 'UNKNOWN_SCALAR_TYPE' | 'COUNT_DISTINCT' | 'MIN' | 'MAX';
interface NestedAggregationResults extends NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /**
     * User-defined name of aggregation, matches the one specified in request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that matches result. */
    type?: AggregationTypeWithLiterals;
    /**
     * Field to aggregate by, matches the one specified in request.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
}
declare enum AggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR"
}
/** @enumType */
type AggregationTypeWithLiterals = AggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR';
interface ValueResults {
    /**
     * Array of value aggregation results, each containing a field value and the count of entities with that value.
     * @maxSize 250
     */
    results?: ValueAggregationResult[];
}
interface RangeResults {
    /**
     * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.
     * @maxSize 50
     */
    results?: RangeAggregationResult[];
}
interface AggregationResultsScalarResult {
    /** Type of scalar aggregation. */
    type?: ScalarTypeWithLiterals;
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Nested aggregations result data. */
    nestedResults?: NestedAggregationResults;
}
interface ValueResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number | null;
}
interface RangeResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number | null;
}
interface ScalarResult {
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedResultValue extends NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
/** @oneof */
interface NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
interface Results {
    /** Map of nested aggregation results, keyed by aggregation name. */
    results?: Record<string, NestedResultValue>;
}
interface DateHistogramResult {
    /**
     * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * @maxLength 100
     */
    value?: string;
    /** Count of documents in the bucket. */
    count?: number;
}
interface GroupByValueResults {
    /**
     * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.
     * @maxSize 1000
     */
    results?: NestedValueAggregationResult[];
}
interface DateHistogramResults {
    /**
     * Array of date histogram aggregation results, each containing a date bucket and its count.
     * @maxSize 200
     */
    results?: DateHistogramResult[];
}
/**
 * Results of `NESTED` aggregation type in a flattened form.
 * Aggregations in resulting array are keyed by requested aggregation `name`.
 */
interface NestedResults {
    /**
     * Array of nested aggregation result groups, each containing multiple aggregation results.
     * @maxSize 1000
     */
    results?: Results[];
}
interface AggregationResults extends AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
    /**
     * User-defined name of aggregation as derived from search request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that must match specified kind as derived from search request. */
    type?: AggregationTypeWithLiterals;
    /**
     * Field to aggregate by as derived from search request.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
}
interface DisconnectStaffMemberFromUserRequest {
    /**
     * ID of the staff member to disconnect from its Wix user.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface DisconnectStaffMemberFromUserResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface AssignWorkingHoursScheduleRequest {
    /**
     * ID of the staff member to assign the schedule to.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to assign to the staff member.
     *
     * Must be either the staff member's event schedule ID or the working hour
     * schedule ID for a business location.
     * @format GUID
     */
    scheduleId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface AssignWorkingHoursScheduleResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface AssignCustomScheduleRequest {
    /**
     * ID of the staff member for which to assign a working hour schedule.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to assign to the staff member.
     * @format GUID
     */
    scheduleId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface AssignCustomScheduleResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface BulkUpdateStaffMemberTagsRequest {
    /**
     * IDs of staff members to update tags for.
     * @minSize 1
     * @maxSize 100
     * @format GUID
     */
    ids: string[];
    /** Tags to add to the staff members. */
    assignTags?: Tags;
    /** Tags to remove from the staff members. */
    unassignTags?: Tags;
}
interface BulkUpdateStaffMemberTagsResponse {
    /**
     * List of update results.
     * @minSize 1
     * @maxSize 100
     */
    results?: BulkUpdateStaffMemberTagsResult[];
    /** Bulk action metadata. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface ItemMetadata {
    /**
     * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).
     * @format GUID
     */
    id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkUpdateStaffMemberTagsResult {
    /** Metadata about an individual update operation. */
    itemMetadata?: ItemMetadata;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface BulkUpdateStaffMemberTagsByFilterRequest {
    /**
     * Filter to base the update upon. See the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter: Record<string, any> | null;
    /** Tags to add to the staff members. */
    assignTags?: Tags;
    /** Tags to remove from the staff members. */
    unassignTags?: Tags;
}
interface BulkUpdateStaffMemberTagsByFilterResponse {
    /**
     * Job ID for the bulk update operation.
     * @format GUID
     */
    jobId?: string;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createStaffMember(): __PublicMethodMetaInfo<'POST', {}, CreateStaffMemberRequest$1, CreateStaffMemberRequest, CreateStaffMemberResponse$1, CreateStaffMemberResponse>;
declare function getStaffMember(): __PublicMethodMetaInfo<'GET', {
    staffMemberId: string;
}, GetStaffMemberRequest$1, GetStaffMemberRequest, GetStaffMemberResponse$1, GetStaffMemberResponse>;
declare function updateStaffMember(): __PublicMethodMetaInfo<'PATCH', {
    staffMemberId: string;
}, UpdateStaffMemberRequest$1, UpdateStaffMemberRequest, UpdateStaffMemberResponse$1, UpdateStaffMemberResponse>;
declare function deleteStaffMember(): __PublicMethodMetaInfo<'DELETE', {
    staffMemberId: string;
}, DeleteStaffMemberRequest$1, DeleteStaffMemberRequest, DeleteStaffMemberResponse$1, DeleteStaffMemberResponse>;
declare function queryStaffMembers(): __PublicMethodMetaInfo<'POST', {}, QueryStaffMembersRequest$1, QueryStaffMembersRequest, QueryStaffMembersResponse$1, QueryStaffMembersResponse>;
declare function countStaffMembers(): __PublicMethodMetaInfo<'POST', {}, CountStaffMembersRequest$1, CountStaffMembersRequest, CountStaffMembersResponse$1, CountStaffMembersResponse>;
declare function connectStaffMemberToUser(): __PublicMethodMetaInfo<'POST', {
    staffMemberId: string;
}, ConnectStaffMemberToUserRequest$1, ConnectStaffMemberToUserRequest, ConnectStaffMemberToUserResponse$1, ConnectStaffMemberToUserResponse>;
declare function searchStaffMembers(): __PublicMethodMetaInfo<'POST', {}, SearchStaffMembersRequest$1, SearchStaffMembersRequest, SearchStaffMembersResponse$1, SearchStaffMembersResponse>;
declare function disconnectStaffMemberFromUser(): __PublicMethodMetaInfo<'POST', {
    staffMemberId: string;
}, DisconnectStaffMemberFromUserRequest$1, DisconnectStaffMemberFromUserRequest, DisconnectStaffMemberFromUserResponse$1, DisconnectStaffMemberFromUserResponse>;
declare function assignWorkingHoursSchedule(): __PublicMethodMetaInfo<'POST', {
    staffMemberId: string;
}, AssignWorkingHoursScheduleRequest$1, AssignWorkingHoursScheduleRequest, AssignWorkingHoursScheduleResponse$1, AssignWorkingHoursScheduleResponse>;
declare function assignCustomSchedule(): __PublicMethodMetaInfo<'POST', {
    staffMemberId: string;
}, AssignCustomScheduleRequest$1, AssignCustomScheduleRequest, AssignCustomScheduleResponse$1, AssignCustomScheduleResponse>;
declare function bulkUpdateStaffMemberTags(): __PublicMethodMetaInfo<'POST', {}, BulkUpdateStaffMemberTagsRequest$1, BulkUpdateStaffMemberTagsRequest, BulkUpdateStaffMemberTagsResponse$1, BulkUpdateStaffMemberTagsResponse>;
declare function bulkUpdateStaffMemberTagsByFilter(): __PublicMethodMetaInfo<'POST', {}, BulkUpdateStaffMemberTagsByFilterRequest$1, BulkUpdateStaffMemberTagsByFilterRequest, BulkUpdateStaffMemberTagsByFilterResponse$1, BulkUpdateStaffMemberTagsByFilterResponse>;

export { type __PublicMethodMetaInfo, assignCustomSchedule, assignWorkingHoursSchedule, bulkUpdateStaffMemberTags, bulkUpdateStaffMemberTagsByFilter, connectStaffMemberToUser, countStaffMembers, createStaffMember, deleteStaffMember, disconnectStaffMemberFromUser, getStaffMember, queryStaffMembers, searchStaffMembers, updateStaffMember };
