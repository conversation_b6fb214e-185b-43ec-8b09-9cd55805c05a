import { GetAttendanceRequest as GetAttendanceRequest$1, GetAttendanceResponse as GetAttendanceResponse$1, SetAttendanceRequest as SetAttendanceRequest$1, SetAttendanceResponse as SetAttendanceResponse$1, BulkSetAttendanceRequest as BulkSetAttendanceRequest$1, BulkSetAttendanceResponse as BulkSetAttendanceResponse$1, QueryAttendanceRequest as QueryAttendanceRequest$1, QueryAttendanceResponse as QueryAttendanceResponse$1, CountAttendancesRequest as CountAttendancesRequest$1, CountAttendancesResponse as CountAttendancesResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

/**
 * The `attendance` object represents the attendance information
 * for a booked session, such as:
 *
 * + Did anyone attend the session?
 * + How many people attended the session?
 *
 * The number of session `attendance` objects available depends on the booking type:
 * + Appointment bookings have 1 `attendance` object per appointment session.
 * + Class bookings have 1 `attendance` object for each session of the class. The number of sessions for a class is defined in Schedule and Sessions `schedule.capacity` property.
 * + Course bookings have an `attendance` object for each session of the course. For example, if there are 12 sessions in a course, there are 12 `attendance` objects. The number of sessions for a class is defined in Schedule and Sessions `schedule.capacity` property.
 */
interface Attendance {
    /**
     * ID of the `attendance` object.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Corresponding booking ID.
     * @format GUID
     */
    bookingId?: string | null;
    /** Corresponding session ID. */
    sessionId?: string | null;
    /** Status indicating if any participants attended the session. */
    status?: AttendanceStatusWithLiterals;
    /**
     * Total number of participants that attended the session. By default, the number
     * of attendees is set to `1`, but you can set a number to greater than `1` if multiple
     * participants attended.
     *
     * Do not set to `0` to indicate that no one attended the session. Instead, set the `status` field to `NOT_ATTENDED`.
     *
     * Default: 1
     */
    numberOfAttendees?: number;
}
declare enum AttendanceStatus {
    /** There is no available attendance information. */
    NOT_SET = "NOT_SET",
    /** At least a single participant attended the session. */
    ATTENDED = "ATTENDED",
    /** No participants attended the session. */
    NOT_ATTENDED = "NOT_ATTENDED"
}
/** @enumType */
type AttendanceStatusWithLiterals = AttendanceStatus | 'NOT_SET' | 'ATTENDED' | 'NOT_ATTENDED';
interface GetAttendanceRequest {
    /**
     * ID of the attendance object to retrieve.
     * @format GUID
     */
    attendanceId: string;
}
interface GetAttendanceResponse {
    /** Retrieved attendance. */
    attendance?: Attendance;
}
interface SetAttendanceRequest {
    /** Attendance to create or update. */
    attendance: Attendance;
    /** Information about whether to send a message to a customer after their attendance was set. */
    participantNotification?: ParticipantNotification;
}
interface ParticipantNotification {
    /**
     * Specify whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean | null;
    /**
     * Optional custom message to send to the participants about the changes to the booking.
     * @minLength 1
     * @maxLength 5000
     */
    message?: string | null;
}
interface SetAttendanceResponse {
    /** Created or updated attendance. */
    attendance?: Attendance;
}
interface BulkSetAttendanceRequest {
    returnFullEntity?: boolean;
    /**
     * List of attendance details for booking sessions to create or update.
     * @maxSize 8
     */
    attendanceDetails?: AttendanceDetails[];
}
interface AttendanceDetails {
    /** Created or updated attendance information for a booking session. */
    attendance?: Attendance;
    /** Information about whether to send a message to the customer after their attendance was set. */
    participantNotification?: ParticipantNotification;
}
interface BulkSetAttendanceResponse {
    /**
     * List of created or updated `attendance` objects.
     * @minSize 1
     * @maxSize 8
     */
    results?: BulkAttendanceResult[];
    /** Information about the total number of successes and failures for the Bulk Set Attendance call. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkAttendanceResult {
    /** Created or updated `attendance` object. */
    item?: Attendance;
    /** Metadata for the created or updated `attendance` object. */
    itemMetadata?: ItemMetadata;
}
interface ItemMetadata {
    /** Item ID. Should always be available, unless it's impossible (for example, when failing to create an item). */
    id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface QueryAttendanceRequest {
    /** Query options. */
    query: QueryV2;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object. See [API Query Language](https://dev.wix.com/api/rest/getting-started/api-query-language) for more information.
     *
     * For a detailed list of supported fields and operators, see [Supported Filters and Sorting](https://dev.wix.com/api/rest/wix-bookings/attendance/supported-filters).
     *
     * Max: 1 filter
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[ {"fieldName":"sortField1","order":"ASC"}, {"fieldName":"sortField2","order":"DESC"} ]`
     *
     * For details about sorting, see [Supported Filters and Sorting](https://dev.wix.com/api/rest/wix-bookings/attendance/supported-filters).
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /** Name of the field to sort by. */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
/**
 * Sort order. Use `ASC` for ascending order or `DESC` for descending order.
 *
 * Default: `ASC`.
 */
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Number of `Attendance` objects to return.
     *
     * Default: `50`
     * Maximum: `1000`
     * @max 1000
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     *
     * Not relevant for the first request.
     */
    cursor?: string | null;
}
/** List of objects that contain attendance information. */
interface QueryAttendanceResponse {
    /** List of `attendance` objects that contain attendance information for a booked session. */
    attendances?: Attendance[];
    /** Metadata for the paged set of results. */
    pagingMetadata?: CursorPagingMetadata;
}
/** This is the preferred message for cursor-paging enabled services */
interface CursorPagingMetadata {
    /** Use these cursors to paginate between results. [Read more](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging). */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /** Cursor pointing to next page in the list of results. */
    next?: string | null;
    /** Cursor pointing to previous page in the list of results. */
    prev?: string | null;
}
interface CountAttendancesRequest {
    /** Filter criteria for counting attendance records. If not provided, counts all attendance records for the contact. */
    filter?: Record<string, any> | null;
}
interface CountAttendancesResponse {
    /** Total number of attendance records matching the filters. */
    count?: number;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function getAttendance(): __PublicMethodMetaInfo<'GET', {
    attendanceId: string;
}, GetAttendanceRequest$1, GetAttendanceRequest, GetAttendanceResponse$1, GetAttendanceResponse>;
declare function setAttendance(): __PublicMethodMetaInfo<'POST', {}, SetAttendanceRequest$1, SetAttendanceRequest, SetAttendanceResponse$1, SetAttendanceResponse>;
declare function bulkSetAttendance(): __PublicMethodMetaInfo<'POST', {}, BulkSetAttendanceRequest$1, BulkSetAttendanceRequest, BulkSetAttendanceResponse$1, BulkSetAttendanceResponse>;
declare function queryAttendance(): __PublicMethodMetaInfo<'POST', {}, QueryAttendanceRequest$1, QueryAttendanceRequest, QueryAttendanceResponse$1, QueryAttendanceResponse>;
declare function countAttendances(): __PublicMethodMetaInfo<'POST', {}, CountAttendancesRequest$1, CountAttendancesRequest, CountAttendancesResponse$1, CountAttendancesResponse>;

export { type __PublicMethodMetaInfo, bulkSetAttendance, countAttendances, getAttendance, queryAttendance, setAttendance };
