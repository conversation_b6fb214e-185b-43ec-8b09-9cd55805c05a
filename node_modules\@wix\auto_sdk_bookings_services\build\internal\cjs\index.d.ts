import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { A as AddOnGroup, C as CreateAddOnGroupOptions, a as CreateAddOnGroupResponse, D as DeleteAddOnGroupOptions, b as DeleteAddOnGroupApplicationErrors, U as UpdateAddOnGroupOptions, c as UpdateAddOnGroupResponse, d as UpdateAddOnGroupApplicationErrors, L as ListAddOnGroupsByServiceIdOptions, e as ListAddOnGroupsByServiceIdResponse, S as SetAddOnsForGroupOptions, f as SetAddOnsForGroupResponse, g as SetAddOnsForGroupApplicationErrors, h as Service, i as CreateServiceValidationErrors, B as BulkCreateServicesOptions, j as BulkCreateServicesResponse, k as UpdateService, l as UpdateServiceValidationErrors, m as BulkUpdateServicesOptions, n as BulkUpdateServicesResponse, o as BulkUpdateServicesByFilterOptions, p as BulkUpdateServicesByFilterResponse, q as DeleteServiceOptions, r as BulkDeleteServicesOptions, s as BulkDeleteServicesResponse, t as BulkDeleteServicesByFilterOptions, u as BulkDeleteServicesByFilterResponse, v as ServicesQueryBuilder, w as ServiceSearch, x as SearchServicesResponse, y as CursorQuery, Q as QueryPoliciesResponse, z as QueryBookingFormsOptions, E as QueryBookingFormsResponse, F as QueryBookingFormsApplicationErrors, G as CountServicesOptions, H as CountServicesResponse, I as QueryLocationsOptions, J as QueryLocationsResponse, K as QueryCategoriesOptions, M as QueryCategoriesResponse, N as Location, O as SetServiceLocationsOptions, P as SetServiceLocationsResponse, R as EnablePricingPlansForServiceResponse, T as EnablePricingPlansForServiceApplicationErrors, V as DisablePricingPlansForServiceOptions, W as DisablePricingPlansForServiceResponse, X as DisablePricingPlansForServiceApplicationErrors, Y as SetCustomSlugOptions, Z as SetCustomSlugResponse, _ as SetCustomSlugApplicationErrors, $ as SetCustomSlugValidationErrors, a0 as ValidateSlugOptions, a1 as ValidateSlugResponse, a2 as CloneServiceResponse, a3 as ServiceCreatedEnvelope, a4 as ServiceDeletedEnvelope, a5 as ServiceUpdatedEnvelope } from './bookings-services-v2-service-services.universal-C8m36LwQ.js';
export { al as Action, bB as ActionEvent, ed as ActionWithLiterals, bK as AddOn, bL as AddOnAddOnInfoOneOf, bn as AddOnDetails, bM as AddOnGroupDetail, dH as Address, dI as AddressHint, aP as AddressLocation, co as Aggregation, cE as AggregationData, cp as AggregationKindOneOf, cX as AggregationResults, cY as AggregationResultsResultOneOf, cL as AggregationResultsScalarResult, ai as AggregationType, ea as AggregationTypeWithLiterals, bW as ApplicationError, b5 as AvailabilityConstraints, dW as BaseEventMetadata, dx as Behavior, dy as BehaviorBehaviorOneOf, dt as Benefit, ds as BenefitNotification, aq as BenefitType, ei as BenefitTypeWithLiterals, aW as BookAfterStartPolicy, d1 as BookingForm, aS as BookingPolicy, c$ as BookingPolicyWithServices, bX as BulkActionMetadata, bT as BulkCreateServicesRequest, cb as BulkDeleteServicesByFilterRequest, ca as BulkDeleteServicesRequest, bU as BulkServiceResult, c6 as BulkUpdateServicesByFilterRequest, c4 as BulkUpdateServicesRequest, aQ as BusinessLocationOptions, d7 as BusinessLocations, dK as BusinessSchedule, b0 as CancellationFeePolicy, aX as CancellationPolicy, b1 as CancellationWindow, b2 as CancellationWindowFeeOneOf, dF as Categories, dq as Category, dp as CategoryNotification, ap as CategoryNotificationEvent, eh as CategoryNotificationEventWithLiterals, dR as ChangeContext, dS as ChangeContextPayloadOneOf, an as CloneErrors, ef as CloneErrorsWithLiterals, dn as CloneServiceRequest, aM as CommonAddress, aN as CommonAddressStreetOneOf, eo as CommonSearchWithEntityContext, aK as Conferencing, d3 as ConnectedService, dP as ConsentPolicy, d4 as CountServicesRequest, bF as CreateAddOnGroupRequest, bO as CreateServiceRequest, bP as CreateServiceResponse, as as CrudType, ek as CrudTypeWithLiterals, ch as CursorPaging, cD as CursorPagingMetadata, c_ as CursorQueryPagingMethodOneOf, cm as CursorSearch, cn as CursorSearchPagingMethodOneOf, ck as Cursors, aR as CustomLocationOptions, d8 as CustomLocations, aG as CustomPayment, d9 as CustomerLocations, cw as DateHistogramAggregation, cT as DateHistogramResult, cV as DateHistogramResults, au as DayOfWeek, em as DayOfWeekWithLiterals, br as Delete, bG as DeleteAddOnGroupRequest, bH as DeleteAddOnGroupResponse, c7 as DeleteServiceRequest, c9 as DeleteServiceResponse, dk as DisablePricingPlansForServiceRequest, dv as Discount, dw as DiscountDiscountOneOf, bv as DomainEvent, bw as DomainEventBodyOneOf, b6 as Duration, dr as Empty, di as EnablePricingPlansForServiceRequest, bx as EntityCreatedEvent, bA as EntityDeletedEvent, bz as EntityUpdatedEvent, du as EntryPass, ar as Event, dX as EventMetadata, ej as EventWithLiterals, bi as ExtendedFields, bS as FieldViolation, aE as FixedPayment, aA as Form, d2 as FormDetails, aB as FormSettings, dJ as GeoCoordinates, b_ as GetServiceAvailabilityConstraintsRequest, b$ as GetServiceAvailabilityConstraintsResponse, bY as GetServiceRequest, bZ as GetServiceResponse, cA as GroupByAggregation, cB as GroupByAggregationKindOneOf, cU as GroupByValueResults, dA as HtmlSitePublished, bD as IdentificationData, bE as IdentificationDataIdOneOf, cr as IncludeMissingValuesOptions, ah as Interval, e9 as IntervalWithLiterals, dj as InvalidPricingPlan, am as InvalidSlugError, ee as InvalidSlugErrorWithLiterals, bV as ItemMetadata, bk as Keyword, aU as LimitEarlyBookingPolicy, aV as LimitLateBookingPolicy, bJ as ListAddOnGroupsByServiceIdRequest, dG as Locale, aL as LocationOptionsOneOf, a8 as LocationType, e0 as LocationTypeWithLiterals, c5 as MaskedService, aw as Media, ax as MediaItem, ay as MediaItemItemOneOf, bC as MessageEnvelope, ae as MissingValues, e6 as MissingValuesWithLiterals, aj as Mode, eb as ModeWithLiterals, aF as Money, dh as MoveToNewLocationsOptions, dN as Multilingual, cz as NestedAggregation, cx as NestedAggregationItem, cy as NestedAggregationItemKindOneOf, cH as NestedAggregationResults, cI as NestedAggregationResultsResultOneOf, ag as NestedAggregationType, e8 as NestedAggregationTypeWithLiterals, cQ as NestedResultValue, cR as NestedResultValueResultOneOf, cW as NestedResults, cM as NestedValueAggregationResult, aJ as OnlineBooking, dB as Page, cg as Paging, cj as PagingMetadataV2, c8 as ParticipantNotification, a_ as ParticipantsPolicy, aC as Payment, aI as PaymentOptions, aD as PaymentRateOneOf, at as PlacementType, el as PlacementTypeWithLiterals, aT as PolicyDescription, dE as Properties, dT as PropertiesChange, d0 as QueryBookingFormsRequest, db as QueryCategoriesFilter, da as QueryCategoriesRequest, d6 as QueryLocationsFilter, d5 as QueryLocationsRequest, cZ as QueryPoliciesRequest, dc as QueryServicesMultiLanguageRequest, dd as QueryServicesMultiLanguageResponse, cc as QueryServicesRequest, ci as QueryServicesResponse, cd as QueryV2, ce as QueryV2PagingMethodOneOf, cu as RangeAggregation, cG as RangeAggregationResult, cq as RangeBucket, cO as RangeResult, cK as RangeResults, a7 as RateType, d$ as RateTypeWithLiterals, bo as ReindexMessage, bp as ReindexMessageActionOneOf, df as RemovedLocationSessionsAction, dg as RemovedLocationSessionsActionActionOptionsOneOf, ak as RequestedFields, ec as RequestedFieldsWithLiterals, aY as ReschedulePolicy, av as ResolutionMethod, en as ResolutionMethodWithLiterals, bb as ResourceGroup, bc as ResourceIds, bf as ResourceType, a$ as ResourcesPolicy, by as RestoreInfo, cS as Results, b3 as SaveCreditCardPolicy, cv as ScalarAggregation, cP as ScalarResult, af as ScalarType, e7 as ScalarTypeWithLiterals, b4 as Schedule, bs as Schema, cC as SearchDetails, cl as SearchServicesRequest, bj as SeoSchema, c0 as ServiceAvailabilityConstraints, bd as ServiceResource, be as ServiceResourceSelectionOneOf, dZ as ServiceSearchSpec, a6 as ServiceType, d_ as ServiceTypeWithLiterals, dY as ServicesQueryResult, bu as ServicesUrlsChanged, bN as SetAddOnsForGroupRequest, bt as SetCustomSlugEvent, dl as SetCustomSlugRequest, de as SetServiceLocationsRequest, bm as Settings, dV as SiteCloned, dU as SiteCreated, dD as SitePropertiesEvent, dC as SitePropertiesNotification, bg as Slug, ad as SortDirection, e5 as SortDirectionWithLiterals, ab as SortOrder, e3 as SortOrderWithLiterals, ac as SortType, e4 as SortTypeWithLiterals, cf as Sorting, dM as SpecialHourPeriod, c1 as SplitInterval, b8 as StaffMediaItem, b9 as StaffMediaItemItemOneOf, b7 as StaffMember, ba as StaffMemberDetails, ao as Status, eg as StatusWithLiterals, aO as StreetAddress, dO as SupportedLanguage, bl as Tag, dL as TimePeriod, dQ as Translation, bh as URLs, bI as UpdateAddOnGroupRequest, c2 as UpdateServiceRequest, c3 as UpdateServiceResponse, bq as Upsert, dz as UserDomainInfoChangedEvent, az as V2Category, aa as V2RequestedFields, e2 as V2RequestedFieldsWithLiterals, bQ as ValidateServiceRequest, bR as ValidateServiceResponse, dm as ValidateSlugRequest, cs as ValueAggregation, ct as ValueAggregationOptionsOneOf, cF as ValueAggregationResult, cN as ValueResult, cJ as ValueResults, aH as VariedPayment, aZ as WaitlistPolicy, a9 as WebhookIdentityType, e1 as WebhookIdentityTypeWithLiterals } from './bookings-services-v2-service-services.universal-C8m36LwQ.js';

declare function createAddOnGroup$1(httpClient: HttpClient): CreateAddOnGroupSignature;
interface CreateAddOnGroupSignature {
    /**
     * Create a new AddOns group.
     * An AddOns group defines a collection of AddOns that can be linked to a Service,
     * with constraints such as minimum and maximum selections.
     * @param - AddOnGroup to create.
     */
    (addOnGroup: NonNullablePaths<AddOnGroup, `name`, 2>, options?: CreateAddOnGroupOptions): Promise<NonNullablePaths<CreateAddOnGroupResponse, `addOnGroup.addOnIds`, 3>>;
}
declare function deleteAddOnGroup$1(httpClient: HttpClient): DeleteAddOnGroupSignature;
interface DeleteAddOnGroupSignature {
    /**
     * Delete an existing AddOns group.
     * This will remove the group and unlink all associated AddOns.
     * @param - ID of the AddOnGroup to delete.
     */
    (addOnGroupId: string, options: NonNullablePaths<DeleteAddOnGroupOptions, `serviceId`, 2>): Promise<void & {
        __applicationErrorsType?: DeleteAddOnGroupApplicationErrors;
    }>;
}
declare function updateAddOnGroup$1(httpClient: HttpClient): UpdateAddOnGroupSignature;
interface UpdateAddOnGroupSignature {
    /**
     * Update an existing AddOns group.
     * This allows modifying group settings such as its name, prompt, constraints, or associated AddOns.
     * @param - AddOnGroup to update.
     */
    (addOnGroup: NonNullablePaths<AddOnGroup, `_id`, 2>, options: NonNullablePaths<UpdateAddOnGroupOptions, `serviceId`, 2>): Promise<NonNullablePaths<UpdateAddOnGroupResponse, `addOnGroup.addOnIds`, 3> & {
        __applicationErrorsType?: UpdateAddOnGroupApplicationErrors;
    }>;
}
declare function listAddOnGroupsByServiceId$1(httpClient: HttpClient): ListAddOnGroupsByServiceIdSignature;
interface ListAddOnGroupsByServiceIdSignature {
    /**
     * Retrieves a list of AddOnGroups including enriched AddOn details in the correct order.
     * If the group_id is specified, only the AddOns for the specified group will be returned,
     * otherwise all groups will be returned.
     * @param - ID of the service to retrieve AddOnGroups for.
     */
    (serviceId: string, options?: ListAddOnGroupsByServiceIdOptions): Promise<NonNullablePaths<ListAddOnGroupsByServiceIdResponse, `addOnGroupsDetails`, 2>>;
}
declare function setAddOnsForGroup$1(httpClient: HttpClient): SetAddOnsForGroupSignature;
interface SetAddOnsForGroupSignature {
    /**
     * Sets the AddOns for a specific group.
     * The order of the AddOns in the list will be used to determine their display order.
     * @param - The service ID to set AddOns for.
     */
    (serviceId: string, options: NonNullablePaths<SetAddOnsForGroupOptions, `addOnIds` | `groupId`, 2>): Promise<NonNullablePaths<SetAddOnsForGroupResponse, `addOnGroup.addOnIds`, 3> & {
        __applicationErrorsType?: SetAddOnsForGroupApplicationErrors;
    }>;
}
declare function createService$1(httpClient: HttpClient): CreateServiceSignature;
interface CreateServiceSignature {
    /**
     * Creates a service.
     *
     *
     * ## Required fields
     *
     * When creating a service you must specify the following fields:
     * - `type`
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)):
     * Whether it's an appointment-based service, class, or course.
     * - `name`: Service name that's displayed to customers.
     * - `onlineBooking`:
     * Settings determining whether customers can book online, whether the business
     * must manually confirm bookings, and whether customers can request to book an
     * appointment time slot that already has a booking request awaiting business
     * confirmation.
     * - `payment`
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)):
     * How customers can pay when signing up for the service.
     *
     * ### Session durations
     *
     * Depending on which type of service you're creating, you may also need to specify
     * supported session durations.
     *
     * __Classes and courses__
     *
     * Don't specify `schedule.availabilityConstraints.sessionDurations`.
     *
     * __Appointment-based services without varied pricing based on session length__
     *
     * Specify the single supported session duration in the
     * `schedule.availabilityConstraints.sessionDurations` array.
     *
     * __Appointment-based services with varied pricing based on session length__
     *
     * - Specify all supported session durations in `schedule.availabilityConstraints.sessionDurations`.
     * - Note that Wix Bookings doesn't display these values to customers and
     * ignores them in pricing and availability calculations. Instead session
     * durations are retrieved from the relevant service *variants*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * - It is mandatory to specify `schedule.availabilityConstraints.sessionDurations`,
     * even though these values are ignored.
     * @param - Service to create.
     * @returns Created service.
     */
    (service: Service): Promise<NonNullablePaths<Service, `type` | `media.items` | `category._id` | `form._id` | `payment.fixed.price.value` | `payment.fixed.price.currency` | `payment.rateType` | `payment.pricingPlanIds` | `locations` | `locations.${number}.business._id` | `locations.${number}.business.name` | `locations.${number}.custom._id` | `locations.${number}._id` | `locations.${number}.type` | `bookingPolicy._id` | `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.resourcesPolicy.enabled` | `bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled` | `schedule.availabilityConstraints.durations` | `schedule.availabilityConstraints.durations.${number}.minutes` | `schedule.availabilityConstraints.sessionDurations` | `schedule.availabilityConstraints.timeBetweenSessions` | `staffMemberIds` | `serviceResources` | `supportedSlugs` | `supportedSlugs.${number}.name` | `seoData.tags` | `seoData.tags.${number}.type` | `seoData.tags.${number}.children` | `seoData.tags.${number}.custom` | `seoData.tags.${number}.disabled` | `seoData.settings.preventAutoRedirect` | `seoData.settings.keywords` | `seoData.settings.keywords.${number}.term` | `seoData.settings.keywords.${number}.isMain`, 6> & {
        __validationErrorsType?: CreateServiceValidationErrors;
    }>;
}
declare function bulkCreateServices$1(httpClient: HttpClient): BulkCreateServicesSignature;
interface BulkCreateServicesSignature {
    /**
     * Creates multiple services.
     *
     *
     * See *Create Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service))
     * for more details.
     * @param - Services to create.
     */
    (services: Service[], options?: BulkCreateServicesOptions): Promise<NonNullablePaths<BulkCreateServicesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.type` | `results.${number}.item.category._id` | `results.${number}.item.form._id` | `results.${number}.item.payment.rateType` | `results.${number}.item.bookingPolicy._id` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
}
declare function getService$1(httpClient: HttpClient): GetServiceSignature;
interface GetServiceSignature {
    /**
     * Retrieves a service.
     * @param - ID of the service to retrieve.
     * @returns Retrieved service.
     */
    (serviceId: string): Promise<NonNullablePaths<Service, `type` | `media.items` | `category._id` | `form._id` | `payment.fixed.price.value` | `payment.fixed.price.currency` | `payment.rateType` | `payment.pricingPlanIds` | `locations` | `locations.${number}.business._id` | `locations.${number}.business.name` | `locations.${number}.custom._id` | `locations.${number}._id` | `locations.${number}.type` | `bookingPolicy._id` | `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.resourcesPolicy.enabled` | `bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled` | `schedule.availabilityConstraints.durations` | `schedule.availabilityConstraints.durations.${number}.minutes` | `schedule.availabilityConstraints.sessionDurations` | `schedule.availabilityConstraints.timeBetweenSessions` | `staffMemberIds` | `serviceResources` | `supportedSlugs` | `supportedSlugs.${number}.name` | `seoData.tags` | `seoData.tags.${number}.type` | `seoData.tags.${number}.children` | `seoData.tags.${number}.custom` | `seoData.tags.${number}.disabled` | `seoData.settings.preventAutoRedirect` | `seoData.settings.keywords` | `seoData.settings.keywords.${number}.term` | `seoData.settings.keywords.${number}.isMain`, 6>>;
}
declare function updateService$1(httpClient: HttpClient): UpdateServiceSignature;
interface UpdateServiceSignature {
    /**
     * Updates a service.
     *
     *
     * Each time the service is updated, `revision` increments by 1. You must
     * include the number of the existing revision when updating the service.
     * This ensures you're working with the latest service information and
     * prevents unintended overwrites.
     *
     * ## Session durations
     *
     * Specify `schedule.availabilityConstraints.sessionDurations`
     * only if you want to update it for appointment-based services without varied
     * pricing based on session length. Don't specify `schedule.availabilityConstraints.sessionDurations`
     * for all other appointment-based services, classes, or courses. See *Create Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service#session-durations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service#session-durations))
     * for more details.
     * @param - Service ID.
     * @returns Updated service.
     */
    (_id: string, service: NonNullablePaths<UpdateService, `revision`, 2>): Promise<NonNullablePaths<Service, `type` | `media.items` | `category._id` | `form._id` | `payment.fixed.price.value` | `payment.fixed.price.currency` | `payment.rateType` | `payment.pricingPlanIds` | `locations` | `locations.${number}.business._id` | `locations.${number}.business.name` | `locations.${number}.custom._id` | `locations.${number}._id` | `locations.${number}.type` | `bookingPolicy._id` | `bookingPolicy.customPolicyDescription.enabled` | `bookingPolicy.customPolicyDescription.description` | `bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicy.cancellationPolicy.enabled` | `bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicy.reschedulePolicy.enabled` | `bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicy.waitlistPolicy.enabled` | `bookingPolicy.waitlistPolicy.capacity` | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicy.resourcesPolicy.enabled` | `bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicy.cancellationFeePolicy.cancellationWindows` | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `bookingPolicy.saveCreditCardPolicy.enabled` | `schedule.availabilityConstraints.durations` | `schedule.availabilityConstraints.durations.${number}.minutes` | `schedule.availabilityConstraints.sessionDurations` | `schedule.availabilityConstraints.timeBetweenSessions` | `staffMemberIds` | `serviceResources` | `supportedSlugs` | `supportedSlugs.${number}.name` | `seoData.tags` | `seoData.tags.${number}.type` | `seoData.tags.${number}.children` | `seoData.tags.${number}.custom` | `seoData.tags.${number}.disabled` | `seoData.settings.preventAutoRedirect` | `seoData.settings.keywords` | `seoData.settings.keywords.${number}.term` | `seoData.settings.keywords.${number}.isMain`, 6> & {
        __validationErrorsType?: UpdateServiceValidationErrors;
    }>;
}
declare function bulkUpdateServices$1(httpClient: HttpClient): BulkUpdateServicesSignature;
interface BulkUpdateServicesSignature {
    /**
     * Updates up to 100 services.
     *
     *
     * See *Update Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))
     * for more details.
     *
     * The call succeeds even if one or more individual services can't be updated.
     * Information about failures is returned in `bulkActionMetadata`.
     */
    (options?: NonNullablePaths<BulkUpdateServicesOptions, `services.${number}.service` | `services.${number}.service._id` | `services.${number}.service.revision`, 5>): Promise<NonNullablePaths<BulkUpdateServicesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.type` | `results.${number}.item.category._id` | `results.${number}.item.form._id` | `results.${number}.item.payment.rateType` | `results.${number}.item.bookingPolicy._id` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
}
declare function bulkUpdateServicesByFilter$1(httpClient: HttpClient): BulkUpdateServicesByFilterSignature;
interface BulkUpdateServicesByFilterSignature {
    /**
     * Updates multiple services by filter.
     *
     *
     * Refer to the supported filters article
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     *
     * See *Update Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))
     * for more details about updating a service.
     *
     * The call succeeds even if one or more individual services can't be updated.
     * Information about failures is returned in `bulkActionMetadata`.
     * @param - Filter to identify the services to update.
     */
    (filter: Record<string, any>, options: NonNullablePaths<BulkUpdateServicesByFilterOptions, `service`, 2>): Promise<NonNullablePaths<BulkUpdateServicesByFilterResponse, `jobId`, 2>>;
}
declare function deleteService$1(httpClient: HttpClient): DeleteServiceSignature;
interface DeleteServiceSignature {
    /**
     * Deletes a service.
     *
     *
     * Specify `{"preserveFutureSessionsWithParticipants": true}` to retain all
     * future sessions for the service. By default, all future sessions are canceled.
     * @param - ID of the service to delete.
     * @param - Allows you to configure how to handle the deleted service's future sessions and how to notify the sessions participants.
     */
    (serviceId: string, options?: DeleteServiceOptions): Promise<void>;
}
declare function bulkDeleteServices$1(httpClient: HttpClient): BulkDeleteServicesSignature;
interface BulkDeleteServicesSignature {
    /**
     * Deletes multiple services.
     *
     *
     * See *Delete Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))
     * for more details about deleting a service.
     *
     * The call succeeds even if one or more individual services can't be deleted.
     * Information about failures is returned in `bulkActionMetadata`.
     * @param - IDs of the services to delete.
     */
    (ids: string[], options?: BulkDeleteServicesOptions): Promise<NonNullablePaths<BulkDeleteServicesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.type` | `results.${number}.item.category._id` | `results.${number}.item.form._id` | `results.${number}.item.payment.rateType` | `results.${number}.item.bookingPolicy._id` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
}
declare function bulkDeleteServicesByFilter$1(httpClient: HttpClient): BulkDeleteServicesByFilterSignature;
interface BulkDeleteServicesByFilterSignature {
    /**
     * Deletes multiple services by filter.
     *
     *
     * See *Delete Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))
     * for more details about deleting a service.
     *
     * The call succeeds even if one or more individual services can't be deleted.
     * Information about failures is returned in `bulkActionMetadata`.
     *
     * Refer to the supported filters article
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     *
     * To learn about working with filters in general, see
     * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).
     * @param - Filter to identify the services that need to be deleted.
     */
    (filter: Record<string, any>, options?: BulkDeleteServicesByFilterOptions): Promise<NonNullablePaths<BulkDeleteServicesByFilterResponse, `jobId`, 2>>;
}
declare function queryServices$1(httpClient: HttpClient): QueryServicesSignature;
interface QueryServicesSignature {
    /**
     * Creates a query to retrieve a list of `service` objects.
     *
     * The `queryServices()` function builds a query to retrieve a list of `service` objects and returns a `ServicesQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-builder/find) function.
     *
     * You can refine the query by chaining `ServicesQueryBuilder` functions onto the query. `ServicesQueryBuilder` functions enable you to sort, filter, and control the results that `queryServices()` returns.
     *
     * `queryServices()` runs with the following `ServicesQueryBuilder` defaults that you can override:
     *
     * + `limit` is `100`.
     * + Sorted by `createdDate` in ascending order.
     *
     * The functions that are chained to `queryServices()` are applied in the order they are called. For example, if you apply `ascending("category.name")` and then `ascending("name")`, the results are sorted first by `category.name`, and then, if there are multiple results with the same `category.name`, the items are sorted by `name`.
     *
     * The following `ServicesQueryBuilder` functions are supported for the `queryServices()` function. For a full description of the `service` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-result/items) property in `ServicesQueryResult`.
     */
    (): ServicesQueryBuilder;
}
declare function searchServices$1(httpClient: HttpClient): SearchServicesSignature;
interface SearchServicesSignature {
    /**
     * Retrieves a list of up to 100 services, given the provided filtering, paging,
     * and sorting.
     *
     *
     * ## Defaults
     *
     * Search Services has the following default settings, which you can override:
     * + Sorted by `createdDate` in ascending order.
     * + `paging.limit` set to `100`.
     * + `paging.offset` set to `0`.
     *
     * ## Filters
     *
     * When using filters for dates, you must use
     * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
     *
     * Refer to the supported filters article
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting))
     * for a complete list of supported filters and sorting options.
     *
     * ## See also
     *
     * To learn about working with *Search* methods, see
     * _API Query Language_
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))
     * and *Sorting and Paging*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).
     * @param - Search criteria including filter, sort, aggregations, and paging options.
     *
     * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)) for complete filter and sorting options.
     */
    (search: ServiceSearch): Promise<NonNullablePaths<SearchServicesResponse, `services` | `services.${number}.type` | `services.${number}.category._id` | `services.${number}.form._id` | `services.${number}.payment.rateType` | `services.${number}.bookingPolicy._id` | `services.${number}.bookingPolicy.customPolicyDescription.enabled` | `services.${number}.bookingPolicy.customPolicyDescription.description` | `services.${number}.bookingPolicy.limitEarlyBookingPolicy.enabled` | `services.${number}.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `services.${number}.bookingPolicy.limitLateBookingPolicy.enabled` | `services.${number}.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `services.${number}.bookingPolicy.bookAfterStartPolicy.enabled` | `services.${number}.bookingPolicy.cancellationPolicy.enabled` | `services.${number}.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `services.${number}.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `services.${number}.bookingPolicy.reschedulePolicy.enabled` | `services.${number}.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `services.${number}.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `services.${number}.bookingPolicy.waitlistPolicy.enabled` | `services.${number}.bookingPolicy.waitlistPolicy.capacity` | `services.${number}.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `services.${number}.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `services.${number}.bookingPolicy.resourcesPolicy.enabled` | `services.${number}.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `services.${number}.bookingPolicy.cancellationFeePolicy.enabled` | `services.${number}.bookingPolicy.saveCreditCardPolicy.enabled` | `services.${number}.schedule.availabilityConstraints.timeBetweenSessions` | `services.${number}.seoData.settings.preventAutoRedirect` | `aggregationData.results` | `aggregationData.results.${number}.scalar.type` | `aggregationData.results.${number}.scalar.value` | `aggregationData.results.${number}.name` | `aggregationData.results.${number}.type` | `aggregationData.results.${number}.fieldPath`, 6>>;
}
declare function queryPolicies$1(httpClient: HttpClient): QueryPoliciesSignature;
interface QueryPoliciesSignature {
    /**
     * Retrieves a list of up to 100 *booking policies*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)),
     * and information about the services that are connected to them,
     * given the provided filtering, paging, and sorting.
     *
     * ## Defaults
     *
     * Query Policies has the following default settings, which you can override:
     *
     * + Sorted by `id` in ascending order.
     * + `cursorPaging.limit` set to `100`.
     *
     * ## Filters
     *
     * For a complete list of supported filters, refer to **Booking Policies API: Supported Filters**
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters)).
     *
     * When using date filters, you must use
     * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
     *
     * ## Returned services
     *
     * If a booking policy is connected to more than 5 services, only a subset of
     * those services is returned. The `bookingPolicies.totalServiceCount` values
     * indicate the total number of services linked to each policy. You can call *Search Services*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))
     * and specify the relevant policy ID in the filter to retrieve all services
     * connected to the booking policy.
     *
     * ## See also
     *
     * To learn about working with *Query* endpoints, see
     * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
     * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
     * @param - Information about filters, paging, and sorting. See the article about
     * booking policy filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
     * for all supported filters and sorting options.
     */
    (query: CursorQuery): Promise<NonNullablePaths<QueryPoliciesResponse, `bookingPolicies` | `bookingPolicies.${number}.bookingPolicy._id` | `bookingPolicies.${number}.bookingPolicy.customPolicyDescription.enabled` | `bookingPolicies.${number}.bookingPolicy.customPolicyDescription.description` | `bookingPolicies.${number}.bookingPolicy.limitEarlyBookingPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `bookingPolicies.${number}.bookingPolicy.limitLateBookingPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `bookingPolicies.${number}.bookingPolicy.bookAfterStartPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.capacity` | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `bookingPolicies.${number}.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `bookingPolicies.${number}.bookingPolicy.resourcesPolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `bookingPolicies.${number}.bookingPolicy.cancellationFeePolicy.enabled` | `bookingPolicies.${number}.bookingPolicy.saveCreditCardPolicy.enabled` | `bookingPolicies.${number}.totalServiceCount`, 6>>;
}
declare function queryBookingForms$1(httpClient: HttpClient): QueryBookingFormsSignature;
interface QueryBookingFormsSignature {
    /**
     * Retrieves a list of up to 100 *booking forms*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/forms/introduction) | [REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)),
     * and information about the services that are connected to them,
     * given the provided filtering, paging, and sorting.
     *
     *
     * ## Defaults
     *
     * Query Booking Forms has the following default settings, which you can override:
     *
     * + Sorted by `id` in ascending order.
     * + `cursorPaging.limit` set to `100`.
     *
     * ## Filters
     *
     * For a complete list of supported filters, refer to **Forms API: Supported Filters**
     * ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)).
     *
     * When using date filters, you must use
     * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
     *
     * ## Returned services
     *
     * If a booking policy is connected to more than 5 services, only a subset of
     * these service IDs and names is returned. The `bookingForms.totalServiceCount`
     * values indicate the total number of services linked to each form. You can call *Query Services*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/query-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/query-services))
     * and specify the relevant form ID in the filter to retrieve all services
     * connected to the booking form.
     *
     * ## Default booking forms
     *
     * By default, all Wix Bookings services use a standard booking form. To retrieve
     * a site's default booking form with Query Booking Forms, specify
     * `{"conditionalFields": ["DEFAULT_BOOKING_FORM"]}`.
     *
     * ## See also
     *
     * To learn about working with *Query* endpoints, see
     * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
     * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
     * @param - Information about filters, paging, and sorting. See the article about
     * form filters ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object))
     * for all supported filters and sorting options.
     */
    (query: CursorQuery, options?: QueryBookingFormsOptions): Promise<NonNullablePaths<QueryBookingFormsResponse, `bookingForms` | `bookingForms.${number}.formDetails.formId` | `bookingForms.${number}.totalServiceCount` | `defaultBookingForm.formDetails.formId` | `defaultBookingForm.connectedServices` | `defaultBookingForm.totalServiceCount`, 5> & {
        __applicationErrorsType?: QueryBookingFormsApplicationErrors;
    }>;
}
declare function countServices$1(httpClient: HttpClient): CountServicesSignature;
interface CountServicesSignature {
    /**
     * Counts how many services match the given filter.
     *
     *
     * Refer to the supported filters article
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     *
     * To learn about working with filters in general, see
     * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters)
     */
    (options?: CountServicesOptions): Promise<NonNullablePaths<CountServicesResponse, `count`, 2>>;
}
declare function queryLocations$1(httpClient: HttpClient): QueryLocationsSignature;
interface QueryLocationsSignature {
    /**
     * Retrieves 3 separate lists of business, custom, and customer *locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)),
     * given the provided filtering, and whether each location is connected to at
     * least one of the site's services.
     *
     *
     * ## Defaults
     *
     * Query Locations has the following default setting, which you can't override:
     * Sorted by `id` in ascending order.
     *
     * ## Filters
     *
     * For a complete list of supported filters, refer to the `location` object
     * ([REST](https://dev.wix.com/docs/rest/business-management/locations/location-object)).
     *
     * When using date filters, you must use
     * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
     *
     * ## See also
     *
     * To learn about working with *Query* endpoints, see
     * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
     * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
     */
    (options?: QueryLocationsOptions): Promise<NonNullablePaths<QueryLocationsResponse, `businessLocations.exists` | `businessLocations.locations` | `businessLocations.locations.${number}.business._id` | `businessLocations.locations.${number}.business.name` | `businessLocations.locations.${number}.custom._id` | `businessLocations.locations.${number}._id` | `businessLocations.locations.${number}.type` | `customLocations.exists` | `customerLocations.exists`, 6>>;
}
declare function queryCategories$1(httpClient: HttpClient): QueryCategoriesSignature;
interface QueryCategoriesSignature {
    /**
     * Retrieves a list of service *categories*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/introduction)),
     * given the provided filtering.
     *
     *
     * ## Defaults
     *
     * Query Categories has the following default setting, which you can't override:
     * Sorted by `id` in ascending order.
     *
     * ## Filters
     *
     * For a complete list of supported filters, refer to the supported filters article
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering)).
     *
     * When using date filters, you must use
     * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
     *
     * ## See also
     *
     * To learn about working with *Query* endpoints, see
     * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)
     * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).
     */
    (options?: QueryCategoriesOptions): Promise<NonNullablePaths<QueryCategoriesResponse, `categories` | `categories.${number}._id`, 4>>;
}
declare function setServiceLocations$1(httpClient: HttpClient): SetServiceLocationsSignature;
interface SetServiceLocationsSignature {
    /**
     * Replaces the list of the *locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))
     * where the business offers the service.
     *
     *
     * ## Consequences for customers
     *
     * Removing a service location may impact existing sessions and their
     * participants. If you remove at least one service location, you must
     * specify `removedLocationSessionsAction` to indicate what happens to all
     * future sessions scheduled at this location.
     *
     * - **Keep existing location**: If you want to retain future sessions at their
     * originally scheduled location, specify
     * `{"removedLocationSessionsAction.action": "KEEP_AT_CURRENT_LOCATION"}`.
     * This ensures nothing changes for the customer, but the business must be
     * able to provide access to the removed location in the future.
     * - **Update location**: If you want to update the location for future sessions
     * scheduled at the removed location, specify
     * `{"removedLocationSessionsAction.action": "MOVE_TO_LOCATION"}` and
     * `moveToLocationOptions.newLocation`.
     *
     * You can't mix and match to keep some sessions at the previous location while
     * moving other sessions to an updated location.
     *
     * Past session details aren't changed, no matter which option you choose for
     * future sessions.
     *
     * Future sessions scheduled for a location defined by the customer are also not
     * updated.
     *
     * ## Specify location details
     *
     * Depending on whether the new or updated location is a business or custom location,
     * you need to specify different fields.
     *
     * - **Business location**: Specify the relevant *location ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))
     * in `locations.business.id`.
     * - **Custom location**: Specify the complete address object as
     * `locations.custom.address`.
     *
     * ## Participant notifications
     *
     * You can specify a `participantNotification.message` that's immediately send
     * to all customers who had booked at a changed location. Ensure
     * `participantNotification.notifyParticipants` is set to `true` to send the
     * message.
     * @param - ID of the service.
     * @param - List of locations replacing existing service locations. Omitting an existing location removes it from the service. Specify `removedLocationSessionsAction` to determine the handling of future sessions scheduled at that location.
     */
    (serviceId: string, locations: Location[], options?: SetServiceLocationsOptions): Promise<NonNullablePaths<SetServiceLocationsResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7>>;
}
declare function enablePricingPlansForService$1(httpClient: HttpClient): EnablePricingPlansForServiceSignature;
interface EnablePricingPlansForServiceSignature {
    /**
     * Adds a list of *pricing plan IDs*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * to a service's `payment.pricingPlanIds` array.
     *
     *
     * The call doesn't validate whether the service's `payment.options.pricingPlan`
     * is set to `true`. If it's set to `false`, customers aren't able to pay
     * for their bookings using pricing plans. You can call *Update Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))
     * to change a service's supported payment methods.
     * @param - ID of the service to update.
     * @param - IDs of the *pricing plans*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * to add to the service's `payment.pricingPlanIds` array.
     */
    (serviceId: string, pricingPlanIds: string[]): Promise<NonNullablePaths<EnablePricingPlansForServiceResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7> & {
        __applicationErrorsType?: EnablePricingPlansForServiceApplicationErrors;
    }>;
}
declare function disablePricingPlansForService$1(httpClient: HttpClient): DisablePricingPlansForServiceSignature;
interface DisablePricingPlansForServiceSignature {
    /**
     * Removes a list of *pricing plan IDs*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * from a service's `payment.pricingPlanIds` array.
     *
     *
     * If you remove all pricing plan IDs from `payment.pricingPlanIds` and the
     * service supports only payments by pricing plan, customers will no longer be
     * able to book the service, as they will have no payment options available.
     * @param - ID of the service to update.
     */
    (serviceId: string, options?: DisablePricingPlansForServiceOptions): Promise<NonNullablePaths<DisablePricingPlansForServiceResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7> & {
        __applicationErrorsType?: DisablePricingPlansForServiceApplicationErrors;
    }>;
}
declare function setCustomSlug$1(httpClient: HttpClient): SetCustomSlugSignature;
interface SetCustomSlugSignature {
    /**
     * Sets a new active slug for the service.
     *
     *
     * The call fails if at least one of these conditions is met:
     * - The slug doesn't adheres to the supported format.
     * - Another service is currently using the slug.
     * - Another service has used the slug in the past.
     * @param - ID of the service for which to update the active slug.
     */
    (serviceId: string, options?: SetCustomSlugOptions): Promise<NonNullablePaths<SetCustomSlugResponse, `slug.name` | `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain`, 7> & {
        __applicationErrorsType?: SetCustomSlugApplicationErrors;
        __validationErrorsType?: SetCustomSlugValidationErrors;
    }>;
}
declare function validateSlug$1(httpClient: HttpClient): ValidateSlugSignature;
interface ValidateSlugSignature {
    /**
     * Checks whether a custom slug is validate for the service.
     *
     *
     * The checks include:
     * - The slug adheres to the supported format.
     * - No other service is currently using the slug.
     * - No other service has used the slug in the past.
     *
     * The call fails if at least one of the checks fails.
     * @param - IO of the service to check custom slug validity for.
     */
    (serviceId: string, options?: ValidateSlugOptions): Promise<NonNullablePaths<ValidateSlugResponse, `valid` | `errors`, 2>>;
}
declare function cloneService$1(httpClient: HttpClient): CloneServiceSignature;
interface CloneServiceSignature {
    /**
     * Clones a service.
     *
     *
     * ## Connected entities
     *
     * By default, not all entities connected to the service are cloned.
     *
     * ### Schedule
     *
     * Wix Bookings automatically creates a new active *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * for the cloned service. If Wix Bookings can't create this schedule, the
     * Clone Service* call fails.
     *
     * - __For appointment-based services__: Future appointments aren't added to the
     * cloned service's schedule. Use the *Events API*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * to add them as needed.
     * - __For classes and courses__: Future one-time events aren't added to the
     * cloned service's schedule, while future recurring events are added
     * asynchronously. The *Clone Service* call always succeeds, whether or not
     * recurring events are added.
     * If the response includes `RECURRING_EVENTS` in the `errors` array, it means the
     * cloned service doesn't have future recurring events, even though the original
     * service does. You can either delete the cloned service and try again or use
     * the *Events API*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * to add missing events to the schedule.
     *
     * Wix Bookings never adds past events to the cloned service's schedule.
     *
     * ### Service options and variants
     *
     * If the original service has *variants*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),
     * they're cloned asynchronously. The *Clone Service* call always succeeds,
     * regardless of whether variants were cloned.
     *
     * If the response includes `OPTIONS_AND_VARIANTS` in the `errors` array, the cloned
     * service doesn't have variants, even though the original service does. You can
     * delete the cloned service and call *Clone Service* again, or use the
     * _Service Options And Variants API_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction))
     * to add variants.
     *
     * ### Booking form
     *
     * The original service's booking form isn't cloned, whether it's
     * the default or a custom booking form.
     *
     * ### Pricing plans
     *
     * If the original service's accepts payments via *pricing plans*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/plans/introduction)),
     * the cloned service's `payment.options.pricingPlan` is also set to `true`. To
     * accept specific pricing plans, call *Enable Pricing Plans For Service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/enable-pricing-plans-for-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/enable-pricing-plans-for-service)),
     * after cloning the service. If the original service accepts only
     * pricing plan payments and you don't call *Enable Pricing Plans For Service*
     * after cloning the service, customers will be unable to book the service.
     * @param - ID of the service to clone.
     */
    (sourceServiceId: string): Promise<NonNullablePaths<CloneServiceResponse, `service.type` | `service.media.items` | `service.category._id` | `service.form._id` | `service.payment.fixed.price.value` | `service.payment.fixed.price.currency` | `service.payment.rateType` | `service.payment.pricingPlanIds` | `service.locations` | `service.locations.${number}.business._id` | `service.locations.${number}.business.name` | `service.locations.${number}.custom._id` | `service.locations.${number}._id` | `service.locations.${number}.type` | `service.bookingPolicy._id` | `service.bookingPolicy.customPolicyDescription.enabled` | `service.bookingPolicy.customPolicyDescription.description` | `service.bookingPolicy.limitEarlyBookingPolicy.enabled` | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes` | `service.bookingPolicy.limitLateBookingPolicy.enabled` | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes` | `service.bookingPolicy.bookAfterStartPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.enabled` | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation` | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes` | `service.bookingPolicy.reschedulePolicy.enabled` | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule` | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes` | `service.bookingPolicy.waitlistPolicy.enabled` | `service.bookingPolicy.waitlistPolicy.capacity` | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes` | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking` | `service.bookingPolicy.resourcesPolicy.enabled` | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed` | `service.bookingPolicy.cancellationFeePolicy.enabled` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows` | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage` | `service.bookingPolicy.saveCreditCardPolicy.enabled` | `service.schedule.availabilityConstraints.durations` | `service.schedule.availabilityConstraints.durations.${number}.minutes` | `service.schedule.availabilityConstraints.sessionDurations` | `service.schedule.availabilityConstraints.timeBetweenSessions` | `service.staffMemberIds` | `service.serviceResources` | `service.supportedSlugs` | `service.supportedSlugs.${number}.name` | `service.seoData.tags` | `service.seoData.tags.${number}.type` | `service.seoData.tags.${number}.children` | `service.seoData.tags.${number}.custom` | `service.seoData.tags.${number}.disabled` | `service.seoData.settings.preventAutoRedirect` | `service.seoData.settings.keywords` | `service.seoData.settings.keywords.${number}.term` | `service.seoData.settings.keywords.${number}.isMain` | `errors`, 7>>;
}
declare const onServiceCreated$1: EventDefinition<ServiceCreatedEnvelope, "wix.bookings.services.v2.service_created">;
declare const onServiceDeleted$1: EventDefinition<ServiceDeletedEnvelope, "wix.bookings.services.v2.service_deleted">;
declare const onServiceUpdated$1: EventDefinition<ServiceUpdatedEnvelope, "wix.bookings.services.v2.service_updated">;

declare const createAddOnGroup: MaybeContext<BuildRESTFunction<typeof createAddOnGroup$1> & typeof createAddOnGroup$1>;
declare const deleteAddOnGroup: MaybeContext<BuildRESTFunction<typeof deleteAddOnGroup$1> & typeof deleteAddOnGroup$1>;
declare const updateAddOnGroup: MaybeContext<BuildRESTFunction<typeof updateAddOnGroup$1> & typeof updateAddOnGroup$1>;
declare const listAddOnGroupsByServiceId: MaybeContext<BuildRESTFunction<typeof listAddOnGroupsByServiceId$1> & typeof listAddOnGroupsByServiceId$1>;
declare const setAddOnsForGroup: MaybeContext<BuildRESTFunction<typeof setAddOnsForGroup$1> & typeof setAddOnsForGroup$1>;
declare const createService: MaybeContext<BuildRESTFunction<typeof createService$1> & typeof createService$1>;
declare const bulkCreateServices: MaybeContext<BuildRESTFunction<typeof bulkCreateServices$1> & typeof bulkCreateServices$1>;
declare const getService: MaybeContext<BuildRESTFunction<typeof getService$1> & typeof getService$1>;
declare const updateService: MaybeContext<BuildRESTFunction<typeof updateService$1> & typeof updateService$1>;
declare const bulkUpdateServices: MaybeContext<BuildRESTFunction<typeof bulkUpdateServices$1> & typeof bulkUpdateServices$1>;
declare const bulkUpdateServicesByFilter: MaybeContext<BuildRESTFunction<typeof bulkUpdateServicesByFilter$1> & typeof bulkUpdateServicesByFilter$1>;
declare const deleteService: MaybeContext<BuildRESTFunction<typeof deleteService$1> & typeof deleteService$1>;
declare const bulkDeleteServices: MaybeContext<BuildRESTFunction<typeof bulkDeleteServices$1> & typeof bulkDeleteServices$1>;
declare const bulkDeleteServicesByFilter: MaybeContext<BuildRESTFunction<typeof bulkDeleteServicesByFilter$1> & typeof bulkDeleteServicesByFilter$1>;
declare const queryServices: MaybeContext<BuildRESTFunction<typeof queryServices$1> & typeof queryServices$1>;
declare const searchServices: MaybeContext<BuildRESTFunction<typeof searchServices$1> & typeof searchServices$1>;
declare const queryPolicies: MaybeContext<BuildRESTFunction<typeof queryPolicies$1> & typeof queryPolicies$1>;
declare const queryBookingForms: MaybeContext<BuildRESTFunction<typeof queryBookingForms$1> & typeof queryBookingForms$1>;
declare const countServices: MaybeContext<BuildRESTFunction<typeof countServices$1> & typeof countServices$1>;
declare const queryLocations: MaybeContext<BuildRESTFunction<typeof queryLocations$1> & typeof queryLocations$1>;
declare const queryCategories: MaybeContext<BuildRESTFunction<typeof queryCategories$1> & typeof queryCategories$1>;
declare const setServiceLocations: MaybeContext<BuildRESTFunction<typeof setServiceLocations$1> & typeof setServiceLocations$1>;
declare const enablePricingPlansForService: MaybeContext<BuildRESTFunction<typeof enablePricingPlansForService$1> & typeof enablePricingPlansForService$1>;
declare const disablePricingPlansForService: MaybeContext<BuildRESTFunction<typeof disablePricingPlansForService$1> & typeof disablePricingPlansForService$1>;
declare const setCustomSlug: MaybeContext<BuildRESTFunction<typeof setCustomSlug$1> & typeof setCustomSlug$1>;
declare const validateSlug: MaybeContext<BuildRESTFunction<typeof validateSlug$1> & typeof validateSlug$1>;
declare const cloneService: MaybeContext<BuildRESTFunction<typeof cloneService$1> & typeof cloneService$1>;
/**
 * Triggered when a service is created.
 */
declare const onServiceCreated: BuildEventDefinition<typeof onServiceCreated$1>;
/**
 * Triggered when a service is deleted.
 */
declare const onServiceDeleted: BuildEventDefinition<typeof onServiceDeleted$1>;
/**
 * Triggered when a service is updated.
 */
declare const onServiceUpdated: BuildEventDefinition<typeof onServiceUpdated$1>;

export { AddOnGroup, BulkCreateServicesOptions, BulkCreateServicesResponse, BulkDeleteServicesByFilterOptions, BulkDeleteServicesByFilterResponse, BulkDeleteServicesOptions, BulkDeleteServicesResponse, BulkUpdateServicesByFilterOptions, BulkUpdateServicesByFilterResponse, BulkUpdateServicesOptions, BulkUpdateServicesResponse, CloneServiceResponse, CountServicesOptions, CountServicesResponse, CreateAddOnGroupOptions, CreateAddOnGroupResponse, CreateServiceValidationErrors, CursorQuery, DeleteAddOnGroupApplicationErrors, DeleteAddOnGroupOptions, DeleteServiceOptions, DisablePricingPlansForServiceApplicationErrors, DisablePricingPlansForServiceOptions, DisablePricingPlansForServiceResponse, EnablePricingPlansForServiceApplicationErrors, EnablePricingPlansForServiceResponse, ListAddOnGroupsByServiceIdOptions, ListAddOnGroupsByServiceIdResponse, Location, QueryBookingFormsApplicationErrors, QueryBookingFormsOptions, QueryBookingFormsResponse, QueryCategoriesOptions, QueryCategoriesResponse, QueryLocationsOptions, QueryLocationsResponse, QueryPoliciesResponse, SearchServicesResponse, Service, ServiceCreatedEnvelope, ServiceDeletedEnvelope, ServiceSearch, ServiceUpdatedEnvelope, ServicesQueryBuilder, SetAddOnsForGroupApplicationErrors, SetAddOnsForGroupOptions, SetAddOnsForGroupResponse, SetCustomSlugApplicationErrors, SetCustomSlugOptions, SetCustomSlugResponse, SetCustomSlugValidationErrors, SetServiceLocationsOptions, SetServiceLocationsResponse, UpdateAddOnGroupApplicationErrors, UpdateAddOnGroupOptions, UpdateAddOnGroupResponse, UpdateService, UpdateServiceValidationErrors, ValidateSlugOptions, ValidateSlugResponse, bulkCreateServices, bulkDeleteServices, bulkDeleteServicesByFilter, bulkUpdateServices, bulkUpdateServicesByFilter, cloneService, countServices, createAddOnGroup, createService, deleteAddOnGroup, deleteService, disablePricingPlansForService, enablePricingPlansForService, getService, listAddOnGroupsByServiceId, onServiceCreated, onServiceDeleted, onServiceUpdated, queryBookingForms, queryCategories, queryLocations, queryPolicies, queryServices, searchServices, setAddOnsForGroup, setCustomSlug, setServiceLocations, updateAddOnGroup, updateService, validateSlug };
