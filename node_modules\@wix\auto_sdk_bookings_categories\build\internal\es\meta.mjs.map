{"version": 3, "sources": ["../../../src/bookings-v1-category-categories.http.ts", "../../../src/bookings-v1-category-categories.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/bookings/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/categories-proxy',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n    'bookings._base_domain_': [\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_categories';\n\n/**\n * Retrieves all categories.\n * @deprecated\n */\nexport function list(payload: object): RequestOptionsFactory<any> {\n  function __list({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'GET' as any,\n      methodFqn: 'com.wixpress.bookings.services.api.v1.CategoriesService.List',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __list;\n}\n\n/**\n * Creates a category.\n * @deprecated\n */\nexport function create(payload: object): RequestOptionsFactory<any> {\n  function __create({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.services.api.v1.CategoriesService.Create',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __create;\n}\n\n/**\n * Updates a category.\n *\n *\n * Each time the category is updated, revision increments by 1. You must include\n * the number of the existing revision when updating the category. This ensures\n * you're working with the latest service information and prevents unintended\n * overwrites.\n * @deprecated\n */\nexport function update(payload: object): RequestOptionsFactory<any> {\n  function __update({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'PUT' as any,\n      methodFqn:\n        'com.wixpress.bookings.services.api.v1.CategoriesService.Update',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories/{category.id}',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __update;\n}\n\n/**\n * Deletes a category.\n *\n *\n * You can specify `{\"deleteServices\": true}` to also delete all associated\n * services. Learn more about *deleting a service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service)).\n * @deprecated\n */\nexport function _delete(payload: object): RequestOptionsFactory<any> {\n  function ___delete({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'DELETE' as any,\n      methodFqn:\n        'com.wixpress.bookings.services.api.v1.CategoriesService._delete',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories/{id}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return ___delete;\n}\n", "import * as ambassadorWixBookingsV1Category from './bookings-v1-category-categories.http.js';\nimport * as ambassadorWixBookingsV1CategoryTypes from './bookings-v1-category-categories.types.js';\nimport * as ambassadorWixBookingsV1CategoryUniversalTypes from './bookings-v1-category-categories.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function listCategories(): __PublicMethodMetaInfo<\n  'GET',\n  {},\n  ambassadorWixBookingsV1CategoryUniversalTypes.ListCategoryRequest,\n  ambassadorWixBookingsV1CategoryTypes.ListCategoryRequest,\n  ambassadorWixBookingsV1CategoryUniversalTypes.ListCategoryResponse,\n  ambassadorWixBookingsV1CategoryTypes.ListCategoryResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions = ambassadorWixBookingsV1Category.list(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v1/categories',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function createCategory(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV1CategoryUniversalTypes.CreateCategoryRequest,\n  ambassadorWixBookingsV1CategoryTypes.CreateCategoryRequest,\n  ambassadorWixBookingsV1CategoryUniversalTypes.CreateCategoryResponse,\n  ambassadorWixBookingsV1CategoryTypes.CreateCategoryResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions = ambassadorWixBookingsV1Category.create(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/categories',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateCategory(): __PublicMethodMetaInfo<\n  'PUT',\n  { categoryId: string },\n  ambassadorWixBookingsV1CategoryUniversalTypes.UpdateCategoryRequest,\n  ambassadorWixBookingsV1CategoryTypes.UpdateCategoryRequest,\n  ambassadorWixBookingsV1CategoryUniversalTypes.UpdateCategoryResponse,\n  ambassadorWixBookingsV1CategoryTypes.UpdateCategoryResponse\n> {\n  const payload = { category: { id: ':categoryId' } } as any;\n\n  const getRequestOptions = ambassadorWixBookingsV1Category.update(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PUT',\n    path: '/v1/categories/{category.id}',\n    pathParams: { categoryId: 'categoryId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteCategory(): __PublicMethodMetaInfo<\n  'DELETE',\n  { id: string },\n  ambassadorWixBookingsV1CategoryUniversalTypes.DeleteCategoryRequest,\n  ambassadorWixBookingsV1CategoryTypes.DeleteCategoryRequest,\n  ambassadorWixBookingsV1CategoryUniversalTypes.DeleteCategoryResponse,\n  ambassadorWixBookingsV1CategoryTypes.DeleteCategoryResponse\n> {\n  const payload = { id: ':id' } as any;\n\n  const getRequestOptions = ambassadorWixBookingsV1Category._delete(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'DELETE',\n    path: '/v1/categories/{id}',\n    pathParams: { id: 'id' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,kBAAkB;AAI3B,SAAS,4DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAMd,SAAS,KAAK,SAA6C;AAChE,WAAS,OAAO,EAAE,KAAK,GAAQ;AAC7B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,OAAO,SAA6C;AAClE,WAAS,SAAS,EAAE,KAAK,GAAQ;AAC/B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAYO,SAAS,OAAO,SAA6C;AAClE,WAAS,SAAS,EAAE,KAAK,GAAQ;AAC/B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,QAAQ,SAA6C;AACnE,WAAS,UAAU,EAAE,KAAK,GAAQ;AAChC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC5LO,SAAS,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAAoD,KAAK,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAAS,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAAoD,OAAO,OAAO;AAExE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAAS,iBAOd;AACA,QAAM,UAAU,EAAE,UAAU,EAAE,IAAI,cAAc,EAAE;AAElD,QAAM,oBAAoD,OAAO,OAAO;AAExE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,YAAY,aAAa;AAAA,IACvC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAAS,iBAOd;AACA,QAAM,UAAU,EAAE,IAAI,MAAM;AAE5B,QAAM,oBAAoD,QAAQ,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,IAAI,KAAK;AAAA,IACvB,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": []}