import { PreviewPriceRequest as PreviewPriceRequest$1, PreviewPriceResponse as PreviewPriceResponse$1, CalculatePriceRequest as CalculatePriceRequest$1, CalculatePriceResponse as CalculatePriceResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

interface PriceInfo extends PriceInfoTotalPriceOneOf {
    /**
     * Calculated total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `FIXED`, `VARIED`, or `NO_FEE`.
     */
    calculatedPrice?: number;
    /**
     * Description of the total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `CUSTOM`.
     * @deprecated Description of the total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `CUSTOM`.
     * @replacedBy price_description_info.original
     * @targetRemovalDate 2025-01-10
     */
    priceDescription?: string;
    /**
     * Description of the total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `CUSTOM`.
     */
    priceDescriptionInfo?: PriceDescriptionInfo;
    /**
     * List of line items, including the number of participants and the price per participant.
     * @maxSize 20
     */
    bookingLineItems?: BookingLineItem[];
    /**
     * Total deposit the customer must pay when booking the service. Available if
     * the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.options.deposit` is set to `true`.
     */
    deposit?: number | null;
}
/** @oneof */
interface PriceInfoTotalPriceOneOf {
    /**
     * Calculated total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `FIXED`, `VARIED`, or `NO_FEE`.
     */
    calculatedPrice?: number;
    /**
     * Description of the total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `CUSTOM`.
     * @deprecated Description of the total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `CUSTOM`.
     * @replacedBy price_description_info.original
     * @targetRemovalDate 2025-01-10
     */
    priceDescription?: string;
    /**
     * Description of the total price. Available if the _service's_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
     * `payment.rateType` is `CUSTOM`.
     */
    priceDescriptionInfo?: PriceDescriptionInfo;
}
interface BookingLineItem {
    /**
     * _Service ID_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).
     *
     * Required in *Calculate Price*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price))
     * if the site uses Wix Bookings' default pricing logic.
     *
     * Optional in *Calculate Price*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price))
     * if the site use pricing logic defined by a
     * Wix Bookings Pricing Integration service plugin*
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction))
     * integration.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * _Resource ID_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).
     * Required in *Calculate Price*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price))
     * and *Preview Price*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/preview-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/preview-price))
     * for appointment-based services and classes.
     * @format GUID
     */
    resourceId?: string | null;
    /**
     * Custom choices. Choices are specific values for an option the customer can choose to book.
     * For example, the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price. Refer to the *Service Options And Variants API*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction))
     * for more details.
     */
    choices?: ServiceChoice[];
    /**
     * Number of participants for the line item.
     * @min 1
     */
    numberOfParticipants?: number | null;
    /**
     * Price per participant for the line item.
     * @readonly
     */
    pricePerParticipant?: number | null;
}
interface ServiceChoice extends ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
    /**
     * ID of the corresponding option for the choice. For example, the choice `child`
     * could correspond to the option `ageGroup`. In this case, `optionId` is the ID
     * for the `ageGroup` option.
     * @format GUID
     */
    optionId?: string;
}
/** @oneof */
interface ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
}
interface Duration {
    /**
     * Duration of the service in minutes.
     * Min: 1 minute, Max: 30 days, 23 hours, and 59 minutes
     * @min 1
     * @max 44639
     */
    minutes?: number;
    /**
     * Name of the duration option.
     * Defaults to the formatted duration e.g. "1 hour, 30 minutes".
     * @maxLength 255
     */
    name?: string | null;
}
interface PriceDescriptionInfo {
    /**
     * Price description in the site's main language. Refer to the *Site Properties API*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/introduction))
     * for more details.
     * @maxLength 50
     */
    original?: string;
    /**
     * Translated price description. Available if the booking was made in a
     * secondary language.
     * @maxLength 50
     */
    translated?: string | null;
}
interface PreviewPriceRequest {
    /**
     * List of line items to preview the price for.
     * @minSize 1
     * @maxSize 20
     */
    bookingLineItems: BookingLineItem[];
}
interface PreviewPriceResponse {
    /** Information about each line item's price and the estimated total price based on the line items. */
    priceInfo?: PriceInfo;
}
interface CalculatePriceRequest {
    /**
     * _Booking_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/introduction))
     * to calculate the base price for.
     */
    booking: Booking;
}
/** An entity representing a scheduled appointment, class session, or course. */
interface Booking extends BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
    /**
     * Booking ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * An object describing the bookable entity - either a specific time slot or a recurring schedule.
     *
     * The structure depends on the type of service being booked:
     *
     * *For appointment services:** Use `slot` to book a specific time slot with a
     * service provider. Appointments are typically one-time sessions at a specific date and time.
     *
     * *For class services:** Use `slot` to book a specific class session. Classes
     * are individual sessions that can have multiple participants.
     *
     * *For course services:** Use `schedule` to book an entire course consisting of
     * multiple sessions over time. Courses are recurring, multi-session offerings.
     *
     * Choose the appropriate field based on your service type and booking requirements.
     */
    bookedEntity?: BookedEntity;
    /**
     * Contact details of the site visitor or member
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/members/members/introduction) | [REST](https://dev.wix.com/docs/rest/crm/members-contacts/members/members/introduction))
     * making the booking.
     */
    contactDetails?: ContactDetails;
    /** Additional custom fields submitted with the booking form. */
    additionalFields?: CustomFormField[];
    /**
     * Booking status. A booking is automatically confirmed if the service allows it
     * and an eCommerce order is created. It is automatically declined if there is a
     * double booking and the customer hasn't paid or is eligible for an automatic
     * refund. Currently, only payments with pricing plans are automatically refundable.
     */
    status?: BookingStatusWithLiterals;
    /**
     * The payment status of the booking. This field automatically syncs with the
     * `paymentStatus` of the corresponding eCommerce order
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup)| [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))
     * when customers use Wix eCommerce checkout.
     *
     * ## Integration patterns
     *
     * *When using Wix eCommerce checkout:** Wix Bookings automatically syncs the payment status based on the eCommerce order's payment status.
     * Do not manually update this field.
     *
     * *When using custom payment flows:** You can manually update the payment status with Confirm Booking or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) to reflect the customer's payment state.
     *
     * *For membership/pricing plan payments:** Wix Bookings automatically manages the payment status when customers pay with an active pricing plan ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/introduction)) subscription.
     *
     * All payment statuses are supported for every booking `status`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
    /**
     * Payment option selected by the customer. If the customer hasn't completed their checkout, they may still change the payment method. Must be one of the payment options offered by the service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)), unless `skipSelectedPaymentOptionValidation` is `true`.
     *
     * When undefined during an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)), Wix Bookings uses the service's default payment option
     */
    selectedPaymentOption?: SelectedPaymentOptionWithLiterals;
    /**
     * Date and time the booking was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
    /** External user ID that you can provide. */
    externalUserId?: string | null;
    /**
     * Revision number to be used when updating, rescheduling, or cancelling the booking.
     * Increments by 1 each time the booking is updated, rescheduled, or canceled. To prevent conflicting changes, the current revision must be specified when updating the booking.
     */
    revision?: string | null;
    /**
     * ID of the creator of the booking.
     * If `appId` and another ID are present, the other ID takes precedence.
     * @readonly
     */
    createdBy?: IdentificationData;
    /**
     * The start date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the start date of the slot. For a schedule, this is the start date of the first session.
     * @readonly
     */
    startDate?: Date | null;
    /**
     * The end date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the end date of the slot. For a schedule, this is the end date of the last session.
     * @readonly
     */
    endDate?: Date | null;
    /**
     * Date and time the booking was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Custom field data for this object.
     * Extended fields must be configured in the app dashboard before they can be accessed with API calls.
     */
    extendedFields?: ExtendedFields;
    /**
     * Whether this booking overlaps with another confirmed booking. Returned
     * only if set to `true`.
     * @readonly
     */
    doubleBooked?: boolean | null;
}
/** @oneof */
interface BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
}
interface BookedEntity extends BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
    /**
     * Session title at the time of booking. If there is no pre-existing session,
     * for example for appointment-based services, Wix Bookings sets `title` to the service name.
     * @readonly
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * List of tags for the booking.
     *
     * - "INDIVIDUAL": For bookings of appointment-based services. Including when the appointment is for a group of participants.
     * - "GROUP": For bookings of individual class sessions.
     * - "COURSE": For course bookings.
     */
    tags?: string[] | null;
}
/** @oneof */
interface BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
}
interface BookedSlot {
    /** Session ID. */
    sessionId?: string | null;
    /** Service ID. */
    serviceId?: string;
    /** Schedule ID. */
    scheduleId?: string;
    /**
     * ID of the corresponding event
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).
     * Available for both appointment and class bookings, not available for course bookings.
     * For appointment-based services, Wix Bookings automatically populates `eventId` when the booking `status` changes to `CONFIRMED`.
     * For class bookings, Wix Bookings automatically populates `eventId` upon booking creation.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
    /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    startDate?: string | null;
    /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    endDate?: string | null;
    /** The time zone according to which the slot was shown to the user when booking, and should be shown in the future. */
    timezone?: string | null;
    /**
     * Primary resource
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) for the booking.
     * For example, the staff member ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) providing the service.
     */
    resource?: BookedResource;
    /** Location where the session takes place. */
    location?: Location;
}
interface BookedResource {
    /**
     * ID of the booking's primary resource.
     * @format GUID
     */
    id?: string;
    /**
     * Resource's name at the time of booking.
     * @maxLength 40
     */
    name?: string | null;
    /**
     * Resource's email at the time of booking.
     * @maxLength 500
     */
    email?: string | null;
    /**
     * ID of the schedule belonging to the booking's primary resource.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface Location {
    /**
     * Business location ID. Available only for locations that are business locations,
     * meaning the `location_type` is `"OWNER_BUSINESS"`.
     * @format GUID
     */
    id?: string | null;
    /** Location name. */
    name?: string | null;
    /** The full address of this location. */
    formattedAddress?: string | null;
    /**
     * The full translated address of this location.
     * @maxLength 512
     */
    formattedAddressTranslated?: string | null;
    /**
     * Location type.
     *
     * - `"OWNER_BUSINESS"`: The business address, as set in the site’s general settings.
     * - `"OWNER_CUSTOM"`: The address as set when creating the service.
     * - `"CUSTOM"`: The address as set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface BookedSchedule {
    /** Schedule ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)). */
    scheduleId?: string;
    /**
     * Booked service ID.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Location ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)) where the schedule's sessions take place.
     * @readonly
     */
    location?: Location;
    /**
     * Time zone in which the slot or session was shown to the customer when they booked.
     * Also used whenever the customer reviews the booking's timing in the future.
     */
    timezone?: string | null;
    /**
     * Start time of the first session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    firstSessionStart?: string | null;
    /**
     * End time of the last session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    lastSessionEnd?: string | null;
}
interface ContactDetails {
    /**
     * Contact ID.
     * @format GUID
     */
    contactId?: string | null;
    /**
     * Contact's first name. When populated from a standard booking form, this
     * property corresponds to the `name` field.
     */
    firstName?: string | null;
    /** Contact's last name. */
    lastName?: string | null;
    /**
     * Contact's email. If no [contact](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/contacts/contact-v4/contact-object)
     * with this email exist, a new contact is created.
     * Used to validate coupon usage limitations per contact. If not specified,
     * the coupon usage limitation will not be enforced. (Coupon usage limitation
     * validation is not supported yet).
     * @format EMAIL
     */
    email?: string | null;
    /** Contact's phone number. */
    phone?: string | null;
    /** Contact's full address. */
    fullAddress?: Address;
    /**
     * Contact's time zone.
     * @deprecated
     */
    timeZone?: string | null;
    /**
     * Contact's country in [ISO 3166-1 alpha-2 code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
     * format.
     * @format COUNTRY
     */
    countryCode?: string | null;
}
/** Physical address */
interface Address extends AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
    /**
     * Country code.
     * @format COUNTRY
     */
    country?: string | null;
    /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /** Zip/postal code. */
    postalCode?: string | null;
    /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */
    addressLine2?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Free text to help find the address. */
    hint?: string | null;
    /** Coordinates of the physical address. */
    geocode?: AddressLocation;
    /** Country full name. */
    countryFullname?: string | null;
    /** Multi-level subdivisions from top to bottom. */
    subdivisions?: Subdivision[];
}
/** @oneof */
interface AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
}
interface StreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
interface AddressLocation {
    /** Address latitude. */
    latitude?: number | null;
    /** Address longitude. */
    longitude?: number | null;
}
interface Subdivision {
    /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    code?: string;
    /** Subdivision full name. */
    name?: string;
}
interface CustomFormField {
    /**
     * ID of the form field as defined in the form.
     * @format GUID
     */
    id?: string;
    /** Value that was submitted for this field. */
    value?: string | null;
    /**
     * Form field's label at the time of submission.
     * @readonly
     */
    label?: string | null;
    valueType?: ValueTypeWithLiterals;
}
declare enum ValueType {
    /** Short text. This is the default value type. */
    SHORT_TEXT = "SHORT_TEXT",
    /** Long text. */
    LONG_TEXT = "LONG_TEXT",
    /** A text that represents the check box value. If selected the value is `true`, otherwise `false`. */
    CHECK_BOX = "CHECK_BOX"
}
/** @enumType */
type ValueTypeWithLiterals = ValueType | 'SHORT_TEXT' | 'LONG_TEXT' | 'CHECK_BOX';
/** Booking status. */
declare enum BookingStatus {
    /** The booking was created, but the customer hasn't completed the related eCommerce order yet. */
    CREATED = "CREATED",
    /**
     * The merchant has confirmed the booking and it appears in the business calendar. Merchants can set up their services
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) to automatically confirm all `PENDING` bookings.
     */
    CONFIRMED = "CONFIRMED",
    /**
     * The customer has canceled the booking. Depending on the relevant service's policy snapshot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
     * they may have to pay a cancellation fee ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction)).
     */
    CANCELED = "CANCELED",
    /** The merchant must manually confirm the booking before it appears in the business calendar. */
    PENDING = "PENDING",
    /** The merchant has declined the booking before the customer was charged. */
    DECLINED = "DECLINED",
    /**
     * The booking is on a waitlist.
     * Currently, you can't call Register to Waitlist ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/waitlist/register-to-waitlist)) for course or appointment bookings, even though this is supported in live sites.
     * You can call Register to Waitlist only for class session bookings.
     */
    WAITING_LIST = "WAITING_LIST"
}
/** @enumType */
type BookingStatusWithLiterals = BookingStatus | 'CREATED' | 'CONFIRMED' | 'CANCELED' | 'PENDING' | 'DECLINED' | 'WAITING_LIST';
/**
 * Payment status.
 * Automatically updated when using eCommerce checkout APIs.
 */
declare enum PaymentStatus {
    /** Undefined payment status. */
    UNDEFINED = "UNDEFINED",
    /** The booking isn't paid. */
    NOT_PAID = "NOT_PAID",
    /** The booking is fully paid. */
    PAID = "PAID",
    /** The booking is partially paid. */
    PARTIALLY_PAID = "PARTIALLY_PAID",
    /** The booking is refunded. */
    REFUNDED = "REFUNDED",
    /** The booking is free of charge. */
    EXEMPT = "EXEMPT"
}
/** @enumType */
type PaymentStatusWithLiterals = PaymentStatus | 'UNDEFINED' | 'NOT_PAID' | 'PAID' | 'PARTIALLY_PAID' | 'REFUNDED' | 'EXEMPT';
/**
 * Selected payment option.
 *
 * One of the payment options offered by the service.
 * This field is set when the user selects an option during booking.
 * If left undefined, the payment option is resolved by the service configuration on checkout.
 */
declare enum SelectedPaymentOption {
    /** Undefined payment option. */
    UNDEFINED = "UNDEFINED",
    /** Offline payment. */
    OFFLINE = "OFFLINE",
    /** Online payment. */
    ONLINE = "ONLINE",
    /** Payment using a Wix Pricing Plan. */
    MEMBERSHIP = "MEMBERSHIP",
    /**
     * Customers can pay only in person with a Wix Pricing Plan, while the Wix user
     * must manually redeem the pricing plan in the dashboard.
     */
    MEMBERSHIP_OFFLINE = "MEMBERSHIP_OFFLINE"
}
/** @enumType */
type SelectedPaymentOptionWithLiterals = SelectedPaymentOption | 'UNDEFINED' | 'OFFLINE' | 'ONLINE' | 'MEMBERSHIP' | 'MEMBERSHIP_OFFLINE';
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /**
     * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.
     * @format GUID
     */
    contactId?: string | null;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface ParticipantChoices {
    /**
     * Information about the booked service choices. Includes the number of participants.
     * @minSize 1
     * @maxSize 20
     */
    serviceChoices?: ServiceChoices[];
}
interface ServiceChoices {
    /**
     * Number of participants for this variant ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    numberOfParticipants?: number | null;
    /**
     * Service choices for these participants.
     * @maxSize 5
     */
    choices?: ServiceChoice[];
}
interface CalculatePriceResponse {
    /** Information about each line item's base price and the total base price. */
    priceInfo?: PriceInfo;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function previewPrice(): __PublicMethodMetaInfo<'POST', {}, PreviewPriceRequest$1, PreviewPriceRequest, PreviewPriceResponse$1, PreviewPriceResponse>;
declare function calculatePrice(): __PublicMethodMetaInfo<'POST', {}, CalculatePriceRequest$1, CalculatePriceRequest, CalculatePriceResponse$1, CalculatePriceResponse>;

export { type __PublicMethodMetaInfo, calculatePrice, previewPrice };
