// src/bookings-resources-v2-resource-type-resource-types.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      },
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/resource-types",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_resource-types";
function createResourceType(payload) {
  function __createResourceType({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createResourceType;
}
function getResourceType(payload) {
  function __getResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "GET",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getResourceType;
}
function updateResourceType(payload) {
  function __updateResourceType({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "PATCH",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceType.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateResourceType;
}
function deleteResourceType(payload) {
  function __deleteResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "DELETE",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteResourceType;
}
function queryResourceTypes(payload) {
  function __queryResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceTypes.createdDate" },
            { path: "resourceTypes.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryResourceTypes;
}
function countResourceTypes(payload) {
  function __countResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countResourceTypes;
}

// src/bookings-resources-v2-resource-type-resource-types.meta.ts
function createResourceType2() {
  const payload = {};
  const getRequestOptions = createResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/resource-types",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getResourceType2() {
  const payload = { resourceTypeId: ":resourceTypeId" };
  const getRequestOptions = getResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/resources/resource-types/{resourceTypeId}",
    pathParams: { resourceTypeId: "resourceTypeId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateResourceType2() {
  const payload = { resourceType: { id: ":resourceTypeId" } };
  const getRequestOptions = updateResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v2/resources/resource-types/{resourceType.id}",
    pathParams: { resourceTypeId: "resourceTypeId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteResourceType2() {
  const payload = { resourceTypeId: ":resourceTypeId" };
  const getRequestOptions = deleteResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v2/resources/resource-types/{resourceTypeId}",
    pathParams: { resourceTypeId: "resourceTypeId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryResourceTypes2() {
  const payload = {};
  const getRequestOptions = queryResourceTypes(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/resource-types/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countResourceTypes2() {
  const payload = {};
  const getRequestOptions = countResourceTypes(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/resource-types/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  countResourceTypes2 as countResourceTypes,
  createResourceType2 as createResourceType,
  deleteResourceType2 as deleteResourceType,
  getResourceType2 as getResourceType,
  queryResourceTypes2 as queryResourceTypes,
  updateResourceType2 as updateResourceType
};
//# sourceMappingURL=meta.mjs.map