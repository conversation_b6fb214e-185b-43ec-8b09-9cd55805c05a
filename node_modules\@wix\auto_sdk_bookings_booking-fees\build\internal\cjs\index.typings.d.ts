import { NonNullablePaths } from '@wix/sdk-types';

/**
 * Fee for a specific booking that's calculated according to the associated booking policy snapshot ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction)). Currently, only cancellation fees, including no-show fees, are supported.
 *
 * Wix Bookings automatically applies the cancellation fee to the eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer cancels the booking themselves.
 * Cancellation fees aren't automatically applied to an eCommerce order if the business owner cancels the booking in their dashboard on behalf of the customer.
 * You can call Apply Booking Fee to Order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/apply-booking-fees-to-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/apply-booking-fees-to-order)) to manually apply booking fees to an eCommerce order.
 */
interface BookingFee {
    /**
     * Booking fee ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * ID of the booking associated with the fee.
     * @format GUID
     */
    bookingId?: string | null;
    /** Cancellation fee details. */
    cancellationFee?: CancellationFee;
    /**
     * Information about the *booking policy snapshot*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
     * according to which the fee was created.
     */
    policyDetails?: PolicyDetails;
}
interface CancellationFee {
    /** Price the customer must pay. */
    price?: Money;
    /** Status of the booking fee. */
    status?: BookingFeeStatusWithLiterals;
    /**
     * Information about what triggered the creation of the booking fee.
     * @readonly
     */
    trigger?: TriggerWithLiterals;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gte:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     * @readonly
     */
    formattedValue?: string | null;
}
declare enum BookingFeeStatus {
    /** There is no eCommerce order associated with the booking. */
    UNKNOWN_STATUS = "UNKNOWN_STATUS",
    /** The fee is informational only; the customer doesn't have to pay it. For example, it shows how much the customer would owe if they canceled the booking now. */
    PREVIEW = "PREVIEW",
    /** The booking fee hasn't been added to the eCommerce order yet. */
    NOT_YET_APPLIED_TO_ORDER = "NOT_YET_APPLIED_TO_ORDER",
    /** The booking fee has been added to the eCommerce order. The customer may not have paid it yet. */
    APPLIED_TO_ORDER = "APPLIED_TO_ORDER"
}
/** @enumType */
type BookingFeeStatusWithLiterals = BookingFeeStatus | 'UNKNOWN_STATUS' | 'PREVIEW' | 'NOT_YET_APPLIED_TO_ORDER' | 'APPLIED_TO_ORDER';
/** The domain event that triggered the booking fee calculation. */
declare enum Trigger {
    /** There is no information about what triggered the creation of the booking fee. */
    UNKNOWN_TRIGGER = "UNKNOWN_TRIGGER",
    /** The booking fee was created because the customer didn't show up to the booking or canceled after the expiration of the last cancellation window. */
    NOT_ATTENDED = "NOT_ATTENDED",
    /** The booking fee was created because the customer canceled the booking before the expiration of the last cancellation window. */
    BOOKING_CANCELED = "BOOKING_CANCELED"
}
/** @enumType */
type TriggerWithLiterals = Trigger | 'UNKNOWN_TRIGGER' | 'NOT_ATTENDED' | 'BOOKING_CANCELED';
interface PolicyDetails {
    /**
     * ID of the booking policy.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Description of the booking policy.
     * @maxLength 2500
     */
    description?: string | null;
    /**
     * Translated description of the booking policy according to the buyer language of the eCommerce order.
     * @maxLength 2500
     */
    descriptionTranslated?: string | null;
}
interface FailedToApplyBookingFeeToOrder {
    /** Booking fee which couldn't to be applied to the eCommerce order. */
    bookingFee?: BookingFee;
    /**
     * IDs of the booking fees that are related to the booking fee which couldn't be
     * applied to the eCommerce order.
     * @format GUID
     * @maxSize 4
     */
    relatedBookingFeeIds?: string[];
    /** Information about the affected eCommerce order. */
    ecomOrderInfo?: EcomOrderInfo;
    /**
     * Information about whether to notify the business about failing to apply the
     * booking fees to the eCommerce order and the message to send.
     */
    businessNotification?: BusinessNotification;
}
interface EcomOrderInfo {
    /**
     * Order ID.
     * @format GUID
     */
    orderId?: string | null;
    /** Additional fee price. */
    additionalFeePrice?: Price;
    /**
     * The eCommerce additional fee id that was created on the order.
     * @format GUID
     */
    additionalFeeId?: string | null;
}
interface Price {
    /**
     * Amount.
     * @decimalValue options { gte:0, lte:1000000000000000, maxScale:2 }
     */
    amount?: string;
    /**
     * Amount formatted with currency symbol.
     * @readonly
     * @maxLength 100
     */
    formattedAmount?: string;
}
interface BusinessNotification {
    /**
     * Whether to notify the business about changes made to the booking fees.
     * Default is false.
     */
    notifyBusiness?: boolean | null;
    /**
     * Optional custom message to send.
     * @minLength 1
     * @maxLength 5000
     */
    message?: string | null;
}
interface FailedToCollectAppliedBookingFees {
    /**
     * IDs of the bookings for which the booking fees couldn't be collected from the
     * customer.
     * @format GUID
     * @maxSize 4
     */
    bookingIds?: string[];
    /** Information about the affected eCommerce order. */
    ecomOrderInfo?: EcomOrderInfo;
    /**
     * Information about whether to notify the business about failing to collect the
     * booking fees from the customer and the message to send.
     */
    businessNotification?: BusinessNotification;
}
interface ListBookingFeesByBookingIdsRequest {
    /**
     * IDs of the bookings to retrieve booking fees for.
     * @format GUID
     * @maxSize 5
     */
    bookingIds?: string[];
    /**
     * IDs of the multi service bookings to retrieve booking fees for.
     * @format GUID
     * @maxSize 1
     */
    multiServiceBookingIds?: string[];
}
interface ListBookingFeesByBookingIdsResponse {
    /** List of retrieved booking fees. */
    bookingFees?: BookingFee[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Cursor strings that point to the next page, previous page, or both. */
    cursors?: Cursors;
    /**
     * Whether there are more pages to retrieve following the current page.
     *
     * + `true`: Another page of results can be retrieved.
     * + `false`: This is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface ApplyBookingFeesToOrderRequest {
    /**
     * IDs of the bookings for which to apply booking fees to an eCommerce order.
     * @format GUID
     * @minSize 1
     * @maxSize 5
     */
    bookingIds: string[] | null;
    /**
     * Custom price override for the additional fee that's added to the eCommerce
     * order. The override mustn't exceed the sum of all booking fees. You must have
     * the `OVERRIDE_BOOKING_FEE_PRICE` permission to use this property.
     */
    priceOverride?: Money;
    /**
     * Information about the message to the business and whether to send it if the
     * booking fee application to the eCommerce order fails.
     */
    businessNotification?: BusinessNotification;
}
interface ApplyBookingFeesToOrderResponse {
    /**
     * Booking fees that were applied as a single additional fee to the eCommerce
     * order.
     */
    bookingFees?: BookingFee[];
    /** Information about the eCommerce order to which the booking fees are applied. */
    ecomOrderInfo?: EcomOrderInfo;
}
interface ListNonPreviewBookingFeesByBookingIdsRequest {
    /**
     * IDs of the bookings to retrieve booking fees for.
     * @format GUID
     * @minSize 1
     * @maxSize 100
     */
    bookingIds?: string[];
}
interface ListNonPreviewBookingFeesByBookingIdsResponse {
    /** List of retrieved booking fees. */
    bookingFees?: BookingFee[];
}
interface CollectAppliedBookingFeesRequest {
    /**
     * ID of the eCommerce order that includes the booking fees as a single
     * `additionalFee`.
     * @format GUID
     */
    orderId: string | null;
    /**
     * ID of the additional fee that's related to all given booking fees.
     * @format GUID
     */
    additionalFeeId: string | null;
    /**
     * Information about whether to notify the business about failing to collect the
     * booking fees from the customer and the message to send.
     */
    businessNotification?: BusinessNotification;
}
interface CollectAppliedBookingFeesResponse {
    /** Collected amount. */
    collectedPrice?: Price;
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type ListBookingFeesByBookingIdsApplicationErrors = {
    code?: 'MISSING_BOOKING_IDS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type ApplyBookingFeesToOrderApplicationErrors = {
    code?: 'BOOKING_IDS_BELONG_TO_DIFFERENT_ORDERS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ORDER_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'PRICE_OVERRIDE_EXCEEDS_MAX';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_FEE_ALREADY_APPLIED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_FEE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ORDER_IS_ALREADY_EDITED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'PARTIALLY_OR_FULLY_REFUNDED_ORDER_CANNOT_BE_EDITED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ORDER_WITH_TAXABLE_EXISTING_ADDITIONAL_FEE_CANNOT_BE_EDITED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ORDER_CANNOT_BE_EDITED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_FEE_AND_ORDER_CURRENCIES_ARE_INCONSISTENT';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'PRICE_OVERRIDE_INSUFFICIENT_PERMISSIONS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_CALCULATING_BOOKING_FEE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_APPLYING_BOOKING_FEE_TO_ORDER';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UPDATING_ITEM_WITH_RELATED_DISCOUNT_IS_FORBIDDEN';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UPDATING_ITEM_WITH_RELATED_ADDITIONAL_FEE_IS_FORBIDDEN';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type CollectAppliedBookingFeesApplicationErrors = {
    code?: 'ORDER_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ADDITIONAL_FEE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FEE_IS_NOT_BOOKING_FEE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ORDER_BALANCE_NON_POSITIVE';
    description?: string;
    data?: Record<string, any>;
};
/**
 * Retrieves booking fees by booking IDs.
 *
 *
 * Instead of returning `bookingFee` objects with the `cancellationFee.price.value`
 * set to `0.00`, the method doesn't return a `bookingFee` object for the relevant
 * booking IDs. For example, no `bookingFee` object is returned if the canceled
 * booking was free or if the booking was canceled before the start of the earliest
 * cancellation window with an associated fee.
 *
 * If the service's booking policy has been updated since the booking was created,
 * booking fees are calculated according to the *booking policy snapshot*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
 * rather than the current version of the *policy*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).
 *
 * This method calculates the cancellation fee amount based on the time of the
 * call, you can't specify a time. Similarly, it calculates the cancellation fee
 * based on the number of participants who canceled, not a provided number.
 *
 * A `cancellationFee.status` of `PREVIEW` indicates that the booking fee is
 * informational only; the customer isn't required to pay it. When the `status` is
 * set to `UNKNOWN_STATUS` there is no eCommerce order associated with the booking. For
 * example, if a custom checkout was used for the booking instead of the
 * _eCommerce checkout_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
 *
 * If multiple events would trigger the calculation of a booking fee, for example
 * when a booking is first canceled and then marked as not attended, Wix calculates
 * the booking fee based on the first trigger. In this example, the booking
 * cancellation.
 * @public
 * @param options - Options to use when listing booking fees.
 * @permissionId BOOKINGS.BOOKING_FEES_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.fees.v1.BookingFees.ListBookingFeesByBookingIds
 */
declare function listBookingFeesByBookingIds(options?: ListBookingFeesByBookingIdsOptions): Promise<NonNullablePaths<ListBookingFeesByBookingIdsResponse, `bookingFees` | `bookingFees.${number}.cancellationFee.price.value` | `bookingFees.${number}.cancellationFee.price.currency` | `bookingFees.${number}.cancellationFee.status` | `bookingFees.${number}.cancellationFee.trigger`, 6> & {
    __applicationErrorsType?: ListBookingFeesByBookingIdsApplicationErrors;
}>;
interface ListBookingFeesByBookingIdsOptions {
    /**
     * IDs of the bookings to retrieve booking fees for.
     * @format GUID
     * @maxSize 5
     */
    bookingIds?: string[];
    /**
     * IDs of the multi service bookings to retrieve booking fees for.
     * @format GUID
     * @maxSize 1
     */
    multiServiceBookingIds?: string[];
}
/**
 * Applies booking fees to an _eCommerce order_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
 *
 *
 * The booking fees are added as a single additional fee to the eCommerce order.
 * The order's `additionalFee.lineItemIds` array is set to the list of corresponding
 * booking IDs. By default, the `additionalFee.price.amount` is the sum of all
 * booking fee prices. But you may provide a `priceOverride` instead. The override
 * price can't be higher than the sum of all booking fees.
 *
 * Apply Booking Fees to Order also updates the prices of all affected line items
 * in the relevant eCommerce order to zero. After a cancellation fee is applied
 * to an eCommerce order, the cancellation fee's `price.value` is updated to `0.00`
 * and its trigger is set to `UNKNOWN_TRIGGER`. You can retrieve the fee amount
 * from the corresponding `additionalFee` object of the eCommerce order with
 * _Search Orders_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/search-orders) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/search-orders)).
 *
 * If you apply multiple booking fees to an eCommerce order, they either all fail or
 * all succeed together. For example, the call fails if the booking fees are associated
 * with different eCommmerce orders.
 * @param bookingIds - IDs of the bookings for which to apply booking fees to an eCommerce order.
 * @public
 * @requiredField bookingIds
 * @requiredField options.priceOverride.currency
 * @requiredField options.priceOverride.value
 * @param options - Options to use when applying booking fees to an eCommerce order.
 * @permissionId BOOKINGS.BOOKING_FEE_APPLY_TO_ORDER
 * @fqn wix.bookings.fees.v1.BookingFees.ApplyBookingFeesToOrder
 */
declare function applyBookingFeesToOrder(bookingIds: string[], options?: NonNullablePaths<ApplyBookingFeesToOrderOptions, `priceOverride.currency` | `priceOverride.value`, 3>): Promise<NonNullablePaths<ApplyBookingFeesToOrderResponse, `bookingFees` | `bookingFees.${number}.cancellationFee.price.value` | `bookingFees.${number}.cancellationFee.price.currency` | `bookingFees.${number}.cancellationFee.status` | `bookingFees.${number}.cancellationFee.trigger` | `ecomOrderInfo.additionalFeePrice.amount` | `ecomOrderInfo.additionalFeePrice.formattedAmount`, 6> & {
    __applicationErrorsType?: ApplyBookingFeesToOrderApplicationErrors;
}>;
interface ApplyBookingFeesToOrderOptions {
    /**
     * Custom price override for the additional fee that's added to the eCommerce
     * order. The override mustn't exceed the sum of all booking fees. You must have
     * the `OVERRIDE_BOOKING_FEE_PRICE` permission to use this property.
     */
    priceOverride?: Money;
    /**
     * Information about the message to the business and whether to send it if the
     * booking fee application to the eCommerce order fails.
     */
    businessNotification?: BusinessNotification;
}
/**
 * Collects booking fees by charging the customer using the payment method that's
 * saved on the corresponding *eCommerce order*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))
 *
 *
 * <blockquote class="warning">
 *
 * __Warning:__
 * Currently, there is no validation that prevents idempotent requests.
 * This means that your code must make sure to not charge customers multiple
 * times for the same booking fee. You could use
 * _List Transactions For Single Order_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/order-transactions/list-transactions-for-single-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/order-transactions/list-transactions-for-single-order))
 * to check which charges were made previously for an eCommerce order.
 *
 * </blockquote>
 *
 * An HTTP status of `200` means that all booking fees were successfully collected.
 * Any other HTPP status means that collection failed.
 *
 * Collects the order's `additionalFees.price.amount` that's related to the
 * booking fees. If there are multiple additional fees on the eCommerce order,
 * the amount that's collected differs from `priceSummary.totalAdditionalFees.amount`.
 *
 * Possible failure reasons include:
 * + The order's `status` isn't `APPROVED`.
 * + There is no payment method saved on the order.
 * + The order's `balanceSummary.balance.amount` is lower than the
 * `additionalFees.price.amount` to collect.
 * + The order's `additionalFeeId` doesn't belong to a Wix Bookings cancellation fee.
 * @param orderId - ID of the eCommerce order that includes the booking fees as a single
 * `additionalFee`.
 * @public
 * @requiredField options.additionalFeeId
 * @requiredField orderId
 * @param options - Options to use when collecting booking fees that have been applied to an eCommerce order.
 * @permissionId BOOKINGS.BOOKING_FEE_COLLECT
 * @fqn wix.bookings.fees.v1.BookingFees.CollectAppliedBookingFees
 */
declare function collectAppliedBookingFees(orderId: string, options?: NonNullablePaths<CollectAppliedBookingFeesOptions, `additionalFeeId`, 2>): Promise<NonNullablePaths<CollectAppliedBookingFeesResponse, `collectedPrice.amount` | `collectedPrice.formattedAmount`, 3> & {
    __applicationErrorsType?: CollectAppliedBookingFeesApplicationErrors;
}>;
interface CollectAppliedBookingFeesOptions {
    /**
     * ID of the additional fee that's related to all given booking fees.
     * @format GUID
     */
    additionalFeeId: string | null;
    /**
     * Information about whether to notify the business about failing to collect the
     * booking fees from the customer and the message to send.
     */
    businessNotification?: BusinessNotification;
}

export { type ActionEvent, type ApplyBookingFeesToOrderApplicationErrors, type ApplyBookingFeesToOrderOptions, type ApplyBookingFeesToOrderRequest, type ApplyBookingFeesToOrderResponse, type BookingFee, BookingFeeStatus, type BookingFeeStatusWithLiterals, type BusinessNotification, type CancellationFee, type CollectAppliedBookingFeesApplicationErrors, type CollectAppliedBookingFeesOptions, type CollectAppliedBookingFeesRequest, type CollectAppliedBookingFeesResponse, type CursorPagingMetadata, type Cursors, type DomainEvent, type DomainEventBodyOneOf, type EcomOrderInfo, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type FailedToApplyBookingFeeToOrder, type FailedToCollectAppliedBookingFees, type IdentificationData, type IdentificationDataIdOneOf, type ListBookingFeesByBookingIdsApplicationErrors, type ListBookingFeesByBookingIdsOptions, type ListBookingFeesByBookingIdsRequest, type ListBookingFeesByBookingIdsResponse, type ListNonPreviewBookingFeesByBookingIdsRequest, type ListNonPreviewBookingFeesByBookingIdsResponse, type MessageEnvelope, type Money, type PolicyDetails, type Price, type RestoreInfo, Trigger, type TriggerWithLiterals, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, applyBookingFeesToOrder, collectAppliedBookingFees, listBookingFeesByBookingIds };
