# Installation
> `npm install --save @types/safe-json-stringify`

# Summary
This package contains type definitions for safe-json-stringify (https://github.com/debitoor/safe-json-stringify).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/safe-json-stringify.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/safe-json-stringify/index.d.ts)
````ts
declare namespace safeJsonStringify {
    function ensureProperties(obj: any): object;
}

// see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter
type ReplacerFn = (key: any, value: any) => any;

declare function safeJsonStringify(
    data: object,
    replacer?: ReplacerFn | any[] | null,
    space?: string | number,
): string;

export = safeJsonStringify;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 15:11:36 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/ericbyers).
