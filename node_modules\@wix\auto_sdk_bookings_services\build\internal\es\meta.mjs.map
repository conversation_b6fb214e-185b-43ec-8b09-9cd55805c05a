{"version": 3, "sources": ["../../../src/bookings-services-v2-service-services.http.ts", "../../../src/bookings-services-v2-service-services.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFloatToRESTFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsServicesV2ServicesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/services-2',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n      {\n        srcPath: '/bookings/services/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/services-2',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nfunction resolveWixBookingsServicesV2AddOnGroupsServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/services-2',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n      {\n        srcPath: '/bookings/services/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/services-2',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_services';\n\n/**\n * Create a new AddOns group.\n * An AddOns group defines a collection of AddOns that can be linked to a Service,\n * with constraints such as minimum and maximum selections.\n */\nexport function createAddOnGroup(payload: object): RequestOptionsFactory<any> {\n  function __createAddOnGroup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.AddOnGroupsService.CreateAddOnGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/create',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __createAddOnGroup;\n}\n\n/**\n * Delete an existing AddOns group.\n * This will remove the group and unlink all associated AddOns.\n */\nexport function deleteAddOnGroup(payload: object): RequestOptionsFactory<any> {\n  function __deleteAddOnGroup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.AddOnGroupsService.DeleteAddOnGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/delete',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __deleteAddOnGroup;\n}\n\n/**\n * Update an existing AddOns group.\n * This allows modifying group settings such as its name, prompt, constraints, or associated AddOns.\n */\nexport function updateAddOnGroup(payload: object): RequestOptionsFactory<any> {\n  function __updateAddOnGroup({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.AddOnGroupsService.UpdateAddOnGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/update',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __updateAddOnGroup;\n}\n\n/**\n * Retrieves a list of AddOnGroups including enriched AddOn details in the correct order.\n * If the group_id is specified, only the AddOns for the specified group will be returned,\n * otherwise all groups will be returned.\n */\nexport function listAddOnGroupsByServiceId(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listAddOnGroupsByServiceId({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.AddOnGroupsService.ListAddOnGroupsByServiceId',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath:\n          '/v2/services/add-on-groups/list-add-on-groups-by-service-id',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __listAddOnGroupsByServiceId;\n}\n\n/**\n * Sets the AddOns for a specific group.\n * The order of the AddOns in the list will be used to determine their display order.\n */\nexport function setAddOnsForGroup(payload: object): RequestOptionsFactory<any> {\n  function __setAddOnsForGroup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.AddOnGroupsService.SetAddOnsForGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/set-add-ons-for-group',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __setAddOnsForGroup;\n}\n\n/**\n * Creates a service.\n *\n *\n * ## Required fields\n *\n * When creating a service you must specify the following fields:\n * - `type`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)):\n * Whether it's an appointment-based service, class, or course.\n * - `name`: Service name that's displayed to customers.\n * - `onlineBooking`:\n * Settings determining whether customers can book online, whether the business\n * must manually confirm bookings, and whether customers can request to book an\n * appointment time slot that already has a booking request awaiting business\n * confirmation.\n * - `payment`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)):\n * How customers can pay when signing up for the service.\n *\n * ### Session durations\n *\n * Depending on which type of service you're creating, you may also need to specify\n * supported session durations.\n *\n * __Classes and courses__\n *\n * Don't specify `schedule.availabilityConstraints.sessionDurations`.\n *\n * __Appointment-based services without varied pricing based on session length__\n *\n * Specify the single supported session duration in the\n * `schedule.availabilityConstraints.sessionDurations` array.\n *\n * __Appointment-based services with varied pricing based on session length__\n *\n * - Specify all supported session durations in `schedule.availabilityConstraints.sessionDurations`.\n * - Note that Wix Bookings doesn't display these values to customers and\n * ignores them in pricing and availability calculations. Instead session\n * durations are retrieved from the relevant service *variants*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n * - It is mandatory to specify `schedule.availabilityConstraints.sessionDurations`,\n * even though these values are ignored.\n */\nexport function createService(payload: object): RequestOptionsFactory<any> {\n  function __createService({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'service.createdDate' },\n          { path: 'service.updatedDate' },\n          { path: 'service.media.items.image.urlExpirationDate' },\n          { path: 'service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'service.bookingPolicy.createdDate' },\n          { path: 'service.bookingPolicy.updatedDate' },\n          { path: 'service.schedule.firstSessionStart' },\n          { path: 'service.schedule.lastSessionEnd' },\n          { path: 'service.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'service.supportedSlugs.createdDate' },\n          { path: 'service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'service.locations.business.address.geocode.latitude' },\n          { path: 'service.locations.business.address.geocode.longitude' },\n          { path: 'service.locations.custom.address.geocode.latitude' },\n          { path: 'service.locations.custom.address.geocode.longitude' },\n          { path: 'service.locations.calculatedAddress.geocode.latitude' },\n          { path: 'service.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.CreateService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createService;\n}\n\n/**\n * Creates multiple services.\n *\n *\n * See *Create Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service))\n * for more details.\n */\nexport function bulkCreateServices(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkCreateServices({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'services.createdDate' },\n          { path: 'services.updatedDate' },\n          { path: 'services.media.items.image.urlExpirationDate' },\n          { path: 'services.media.mainMedia.image.urlExpirationDate' },\n          { path: 'services.media.coverMedia.image.urlExpirationDate' },\n          { path: 'services.bookingPolicy.createdDate' },\n          { path: 'services.bookingPolicy.updatedDate' },\n          { path: 'services.schedule.firstSessionStart' },\n          { path: 'services.schedule.lastSessionEnd' },\n          { path: 'services.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'services.supportedSlugs.createdDate' },\n          { path: 'services.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'services.locations.business.address.geocode.latitude' },\n          { path: 'services.locations.business.address.geocode.longitude' },\n          { path: 'services.locations.custom.address.geocode.latitude' },\n          { path: 'services.locations.custom.address.geocode.longitude' },\n          { path: 'services.locations.calculatedAddress.geocode.latitude' },\n          { path: 'services.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.BulkCreateServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/create',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.media.items.image.urlExpirationDate' },\n              { path: 'results.item.media.mainMedia.image.urlExpirationDate' },\n              { path: 'results.item.media.coverMedia.image.urlExpirationDate' },\n              { path: 'results.item.bookingPolicy.createdDate' },\n              { path: 'results.item.bookingPolicy.updatedDate' },\n              { path: 'results.item.schedule.firstSessionStart' },\n              { path: 'results.item.schedule.lastSessionEnd' },\n              {\n                path: 'results.item.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'results.item.supportedSlugs.createdDate' },\n              { path: 'results.item.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkCreateServices;\n}\n\n/** Retrieves a service. */\nexport function getService(payload: object): RequestOptionsFactory<any> {\n  function __getService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.GetService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getService;\n}\n\n/**\n * Updates a service.\n *\n *\n * Each time the service is updated, `revision` increments by 1. You must\n * include the number of the existing revision when updating the service.\n * This ensures you're working with the latest service information and\n * prevents unintended overwrites.\n *\n * ## Session durations\n *\n * Specify `schedule.availabilityConstraints.sessionDurations`\n * only if you want to update it for appointment-based services without varied\n * pricing based on session length. Don't specify `schedule.availabilityConstraints.sessionDurations`\n * for all other appointment-based services, classes, or courses. See *Create Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service#session-durations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service#session-durations))\n * for more details.\n */\nexport function updateService(payload: object): RequestOptionsFactory<any> {\n  function __updateService({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'service.createdDate' },\n          { path: 'service.updatedDate' },\n          { path: 'service.media.items.image.urlExpirationDate' },\n          { path: 'service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'service.bookingPolicy.createdDate' },\n          { path: 'service.bookingPolicy.updatedDate' },\n          { path: 'service.schedule.firstSessionStart' },\n          { path: 'service.schedule.lastSessionEnd' },\n          { path: 'service.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'service.supportedSlugs.createdDate' },\n          { path: 'service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'service.locations.business.address.geocode.latitude' },\n          { path: 'service.locations.business.address.geocode.longitude' },\n          { path: 'service.locations.custom.address.geocode.latitude' },\n          { path: 'service.locations.custom.address.geocode.longitude' },\n          { path: 'service.locations.calculatedAddress.geocode.latitude' },\n          { path: 'service.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.UpdateService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{service.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateService;\n}\n\n/**\n * Updates up to 100 services.\n *\n *\n * See *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * for more details.\n *\n * The call succeeds even if one or more individual services can't be updated.\n * Information about failures is returned in `bulkActionMetadata`.\n */\nexport function bulkUpdateServices(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateServices({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'services.mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'services.service.createdDate' },\n          { path: 'services.service.updatedDate' },\n          { path: 'services.service.media.items.image.urlExpirationDate' },\n          { path: 'services.service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'services.service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'services.service.bookingPolicy.createdDate' },\n          { path: 'services.service.bookingPolicy.updatedDate' },\n          { path: 'services.service.schedule.firstSessionStart' },\n          { path: 'services.service.schedule.lastSessionEnd' },\n          {\n            path: 'services.service.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          {\n            path: 'services.service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'services.service.supportedSlugs.createdDate' },\n          { path: 'services.service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          {\n            path: 'services.service.locations.business.address.geocode.latitude',\n          },\n          {\n            path: 'services.service.locations.business.address.geocode.longitude',\n          },\n          {\n            path: 'services.service.locations.custom.address.geocode.latitude',\n          },\n          {\n            path: 'services.service.locations.custom.address.geocode.longitude',\n          },\n          {\n            path: 'services.service.locations.calculatedAddress.geocode.latitude',\n          },\n          {\n            path: 'services.service.locations.calculatedAddress.geocode.longitude',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.BulkUpdateServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/update',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.media.items.image.urlExpirationDate' },\n              { path: 'results.item.media.mainMedia.image.urlExpirationDate' },\n              { path: 'results.item.media.coverMedia.image.urlExpirationDate' },\n              { path: 'results.item.bookingPolicy.createdDate' },\n              { path: 'results.item.bookingPolicy.updatedDate' },\n              { path: 'results.item.schedule.firstSessionStart' },\n              { path: 'results.item.schedule.lastSessionEnd' },\n              {\n                path: 'results.item.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'results.item.supportedSlugs.createdDate' },\n              { path: 'results.item.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateServices;\n}\n\n/**\n * Updates multiple services by filter.\n *\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * See *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * for more details about updating a service.\n *\n * The call succeeds even if one or more individual services can't be updated.\n * Information about failures is returned in `bulkActionMetadata`.\n */\nexport function bulkUpdateServicesByFilter(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateServicesByFilter({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'service.createdDate' },\n          { path: 'service.updatedDate' },\n          { path: 'service.media.items.image.urlExpirationDate' },\n          { path: 'service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'service.bookingPolicy.createdDate' },\n          { path: 'service.bookingPolicy.updatedDate' },\n          { path: 'service.schedule.firstSessionStart' },\n          { path: 'service.schedule.lastSessionEnd' },\n          { path: 'service.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'service.supportedSlugs.createdDate' },\n          { path: 'service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'service.locations.business.address.geocode.latitude' },\n          { path: 'service.locations.business.address.geocode.longitude' },\n          { path: 'service.locations.custom.address.geocode.latitude' },\n          { path: 'service.locations.custom.address.geocode.longitude' },\n          { path: 'service.locations.calculatedAddress.geocode.latitude' },\n          { path: 'service.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.BulkUpdateServicesByFilter',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/update-by-filter',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateServicesByFilter;\n}\n\n/**\n * Deletes a service.\n *\n *\n * Specify `{\"preserveFutureSessionsWithParticipants\": true}` to retain all\n * future sessions for the service. By default, all future sessions are canceled.\n */\nexport function deleteService(payload: object): RequestOptionsFactory<any> {\n  function __deleteService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.DeleteService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteService;\n}\n\n/**\n * Deletes multiple services.\n *\n *\n * See *Delete Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))\n * for more details about deleting a service.\n *\n * The call succeeds even if one or more individual services can't be deleted.\n * Information about failures is returned in `bulkActionMetadata`.\n */\nexport function bulkDeleteServices(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkDeleteServices({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.BulkDeleteServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/delete',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.media.items.image.urlExpirationDate' },\n              { path: 'results.item.media.mainMedia.image.urlExpirationDate' },\n              { path: 'results.item.media.coverMedia.image.urlExpirationDate' },\n              { path: 'results.item.bookingPolicy.createdDate' },\n              { path: 'results.item.bookingPolicy.updatedDate' },\n              { path: 'results.item.schedule.firstSessionStart' },\n              { path: 'results.item.schedule.lastSessionEnd' },\n              {\n                path: 'results.item.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'results.item.supportedSlugs.createdDate' },\n              { path: 'results.item.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkDeleteServices;\n}\n\n/**\n * Deletes multiple services by filter.\n *\n *\n * See *Delete Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))\n * for more details about deleting a service.\n *\n * The call succeeds even if one or more individual services can't be deleted.\n * Information about failures is returned in `bulkActionMetadata`.\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).\n */\nexport function bulkDeleteServicesByFilter(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkDeleteServicesByFilter({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.BulkDeleteServicesByFilter',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/delete-by-filter',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __bulkDeleteServicesByFilter;\n}\n\n/**\n * Creates a query to retrieve a list of `service` objects.\n *\n * The `queryServices()` function builds a query to retrieve a list of `service` objects and returns a `ServicesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-builder/find) function.\n *\n * You can refine the query by chaining `ServicesQueryBuilder` functions onto the query. `ServicesQueryBuilder` functions enable you to sort, filter, and control the results that `queryServices()` returns.\n *\n * `queryServices()` runs with the following `ServicesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `100`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryServices()` are applied in the order they are called. For example, if you apply `ascending(\"category.name\")` and then `ascending(\"name\")`, the results are sorted first by `category.name`, and then, if there are multiple results with the same `category.name`, the items are sorted by `name`.\n *\n * The following `ServicesQueryBuilder` functions are supported for the `queryServices()` function. For a full description of the `service` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-result/items) property in `ServicesQueryResult`.\n */\nexport function queryServices(payload: object): RequestOptionsFactory<any> {\n  function __queryServices({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'services.createdDate' },\n              { path: 'services.updatedDate' },\n              { path: 'services.media.items.image.urlExpirationDate' },\n              { path: 'services.media.mainMedia.image.urlExpirationDate' },\n              { path: 'services.media.coverMedia.image.urlExpirationDate' },\n              { path: 'services.bookingPolicy.createdDate' },\n              { path: 'services.bookingPolicy.updatedDate' },\n              { path: 'services.schedule.firstSessionStart' },\n              { path: 'services.schedule.lastSessionEnd' },\n              {\n                path: 'services.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'services.supportedSlugs.createdDate' },\n              { path: 'services.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'services.locations.business.address.geocode.latitude' },\n              { path: 'services.locations.business.address.geocode.longitude' },\n              { path: 'services.locations.custom.address.geocode.latitude' },\n              { path: 'services.locations.custom.address.geocode.longitude' },\n              { path: 'services.locations.calculatedAddress.geocode.latitude' },\n              {\n                path: 'services.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryServices;\n}\n\n/**\n * Retrieves a list of up to 100 services, given the provided filtering, paging,\n * and sorting.\n *\n *\n * ## Defaults\n *\n * Search Services has the following default settings, which you can override:\n * + Sorted by `createdDate` in ascending order.\n * + `paging.limit` set to `100`.\n * + `paging.offset` set to `0`.\n *\n * ## Filters\n *\n * When using filters for dates, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with *Search* methods, see\n * _API Query Language_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))\n * and *Sorting and Paging*\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).\n */\nexport function searchServices(payload: object): RequestOptionsFactory<any> {\n  function __searchServices({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'search.aggregations.range.buckets.from' },\n          { path: 'search.aggregations.range.buckets.to' },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.from',\n          },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.to',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.SearchServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/search',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'services.createdDate' },\n              { path: 'services.updatedDate' },\n              { path: 'services.media.items.image.urlExpirationDate' },\n              { path: 'services.media.mainMedia.image.urlExpirationDate' },\n              { path: 'services.media.coverMedia.image.urlExpirationDate' },\n              { path: 'services.bookingPolicy.createdDate' },\n              { path: 'services.bookingPolicy.updatedDate' },\n              { path: 'services.schedule.firstSessionStart' },\n              { path: 'services.schedule.lastSessionEnd' },\n              {\n                path: 'services.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'services.supportedSlugs.createdDate' },\n              { path: 'services.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'services.locations.business.address.geocode.latitude' },\n              { path: 'services.locations.business.address.geocode.longitude' },\n              { path: 'services.locations.custom.address.geocode.latitude' },\n              { path: 'services.locations.custom.address.geocode.longitude' },\n              { path: 'services.locations.calculatedAddress.geocode.latitude' },\n              {\n                path: 'services.locations.calculatedAddress.geocode.longitude',\n              },\n              { path: 'aggregationData.results.ranges.results.from' },\n              { path: 'aggregationData.results.ranges.results.to' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from',\n              },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.from',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.to',\n              },\n              { path: 'aggregationData.results.scalar.value' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.scalar.value',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.scalar.value',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __searchServices;\n}\n\n/**\n * Retrieves a list of up to 100 *booking policies*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)),\n * and information about the services that are connected to them,\n * given the provided filtering, paging, and sorting.\n *\n * ## Defaults\n *\n * Query Policies has the following default settings, which you can override:\n *\n * + Sorted by `id` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to **Booking Policies API: Supported Filters**\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## Returned services\n *\n * If a booking policy is connected to more than 5 services, only a subset of\n * those services is returned. The `bookingPolicies.totalServiceCount` values\n * indicate the total number of services linked to each policy. You can call *Search Services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))\n * and specify the relevant policy ID in the filter to retrieve all services\n * connected to the booking policy.\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryPolicies(payload: object): RequestOptionsFactory<any> {\n  function __queryPolicies({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryPolicies',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/policies/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicies.bookingPolicy.createdDate' },\n              { path: 'bookingPolicies.bookingPolicy.updatedDate' },\n              { path: 'bookingPolicies.services.createdDate' },\n              { path: 'bookingPolicies.services.updatedDate' },\n              {\n                path: 'bookingPolicies.services.media.items.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.services.media.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.services.media.coverMedia.image.urlExpirationDate',\n              },\n              { path: 'bookingPolicies.services.bookingPolicy.createdDate' },\n              { path: 'bookingPolicies.services.bookingPolicy.updatedDate' },\n              { path: 'bookingPolicies.services.schedule.firstSessionStart' },\n              { path: 'bookingPolicies.services.schedule.lastSessionEnd' },\n              {\n                path: 'bookingPolicies.services.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'bookingPolicies.services.supportedSlugs.createdDate' },\n              { path: 'bookingPolicies.services.mainSlug.createdDate' },\n              { path: 'bookingPolicies.connectedServices.createdDate' },\n              { path: 'bookingPolicies.connectedServices.updatedDate' },\n              {\n                path: 'bookingPolicies.connectedServices.media.items.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.media.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.media.coverMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.bookingPolicy.createdDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.bookingPolicy.updatedDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.schedule.firstSessionStart',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.schedule.lastSessionEnd',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.supportedSlugs.createdDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.mainSlug.createdDate',\n              },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'bookingPolicies.services.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.calculatedAddress.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryPolicies;\n}\n\n/**\n * Retrieves a list of up to 100 *booking forms*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/forms/introduction) | [REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)),\n * and information about the services that are connected to them,\n * given the provided filtering, paging, and sorting.\n *\n *\n * ## Defaults\n *\n * Query Booking Forms has the following default settings, which you can override:\n *\n * + Sorted by `id` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to **Forms API: Supported Filters**\n * ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## Returned services\n *\n * If a booking policy is connected to more than 5 services, only a subset of\n * these service IDs and names is returned. The `bookingForms.totalServiceCount`\n * values indicate the total number of services linked to each form. You can call *Query Services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/query-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/query-services))\n * and specify the relevant form ID in the filter to retrieve all services\n * connected to the booking form.\n *\n * ## Default booking forms\n *\n * By default, all Wix Bookings services use a standard booking form. To retrieve\n * a site's default booking form with Query Booking Forms, specify\n * `{\"conditionalFields\": [\"DEFAULT_BOOKING_FORM\"]}`.\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryBookingForms(payload: object): RequestOptionsFactory<any> {\n  function __queryBookingForms({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryBookingForms',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/booking-forms/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryBookingForms;\n}\n\n/**\n * Counts how many services match the given filter.\n *\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters)\n */\nexport function countServices(payload: object): RequestOptionsFactory<any> {\n  function __countServices({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.CountServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countServices;\n}\n\n/**\n * Retrieves 3 separate lists of business, custom, and customer *locations*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)),\n * given the provided filtering, and whether each location is connected to at\n * least one of the site's services.\n *\n *\n * ## Defaults\n *\n * Query Locations has the following default setting, which you can't override:\n * Sorted by `id` in ascending order.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to the `location` object\n * ([REST](https://dev.wix.com/docs/rest/business-management/locations/location-object)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryLocations(payload: object): RequestOptionsFactory<any> {\n  function __queryLocations({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryLocations',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/locations/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'businessLocations.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'businessLocations.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'businessLocations.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'businessLocations.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'businessLocations.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'businessLocations.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryLocations;\n}\n\n/**\n * Retrieves a list of service *categories*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/introduction)),\n * given the provided filtering.\n *\n *\n * ## Defaults\n *\n * Query Categories has the following default setting, which you can't override:\n * Sorted by `id` in ascending order.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryCategories(payload: object): RequestOptionsFactory<any> {\n  function __queryCategories({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryCategories',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/categories/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryCategories;\n}\n\n/**\n * Replaces the list of the *locations*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))\n * where the business offers the service.\n *\n *\n * ## Consequences for customers\n *\n * Removing a service location may impact existing sessions and their\n * participants. If you remove at least one service location, you must\n * specify `removedLocationSessionsAction` to indicate what happens to all\n * future sessions scheduled at this location.\n *\n * - **Keep existing location**: If you want to retain future sessions at their\n * originally scheduled location, specify\n * `{\"removedLocationSessionsAction.action\": \"KEEP_AT_CURRENT_LOCATION\"}`.\n * This ensures nothing changes for the customer, but the business must be\n * able to provide access to the removed location in the future.\n * - **Update location**: If you want to update the location for future sessions\n * scheduled at the removed location, specify\n * `{\"removedLocationSessionsAction.action\": \"MOVE_TO_LOCATION\"}` and\n * `moveToLocationOptions.newLocation`.\n *\n * You can't mix and match to keep some sessions at the previous location while\n * moving other sessions to an updated location.\n *\n * Past session details aren't changed, no matter which option you choose for\n * future sessions.\n *\n * Future sessions scheduled for a location defined by the customer are also not\n * updated.\n *\n * ## Specify location details\n *\n * Depending on whether the new or updated location is a business or custom location,\n * you need to specify different fields.\n *\n * - **Business location**: Specify the relevant *location ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))\n * in `locations.business.id`.\n * - **Custom location**: Specify the complete address object as\n * `locations.custom.address`.\n *\n * ## Participant notifications\n *\n * You can specify a `participantNotification.message` that's immediately send\n * to all customers who had booked at a changed location. Ensure\n * `participantNotification.notifyParticipants` is set to `true` to send the\n * message.\n */\nexport function setServiceLocations(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __setServiceLocations({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'locations.business.address.geocode.latitude' },\n          { path: 'locations.business.address.geocode.longitude' },\n          { path: 'locations.custom.address.geocode.latitude' },\n          { path: 'locations.custom.address.geocode.longitude' },\n          { path: 'locations.calculatedAddress.geocode.latitude' },\n          { path: 'locations.calculatedAddress.geocode.longitude' },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.latitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.longitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.latitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.longitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.latitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.longitude',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.SetServiceLocations',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/locations',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setServiceLocations;\n}\n\n/**\n * Adds a list of *pricing plan IDs*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n * to a service's `payment.pricingPlanIds` array.\n *\n *\n * The call doesn't validate whether the service's `payment.options.pricingPlan`\n * is set to `true`. If it's set to `false`, customers aren't able to pay\n * for their bookings using pricing plans. You can call *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * to change a service's supported payment methods.\n */\nexport function enablePricingPlansForService(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __enablePricingPlansForService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.EnablePricingPlansForService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/pricing-plans/add',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __enablePricingPlansForService;\n}\n\n/**\n * Removes a list of *pricing plan IDs*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n * from a service's `payment.pricingPlanIds` array.\n *\n *\n * If you remove all pricing plan IDs from `payment.pricingPlanIds` and the\n * service supports only payments by pricing plan, customers will no longer be\n * able to book the service, as they will have no payment options available.\n */\nexport function disablePricingPlansForService(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __disablePricingPlansForService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.DisablePricingPlansForService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/pricing-plans/remove',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __disablePricingPlansForService;\n}\n\n/**\n * Sets a new active slug for the service.\n *\n *\n * The call fails if at least one of these conditions is met:\n * - The slug doesn't adheres to the supported format.\n * - Another service is currently using the slug.\n * - Another service has used the slug in the past.\n */\nexport function setCustomSlug(payload: object): RequestOptionsFactory<any> {\n  function __setCustomSlug({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.SetCustomSlug',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/slugs/custom',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'slug.createdDate' },\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setCustomSlug;\n}\n\n/**\n * Checks whether a custom slug is validate for the service.\n *\n *\n * The checks include:\n * - The slug adheres to the supported format.\n * - No other service is currently using the slug.\n * - No other service has used the slug in the past.\n *\n * The call fails if at least one of the checks fails.\n */\nexport function validateSlug(payload: object): RequestOptionsFactory<any> {\n  function __validateSlug({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.ValidateSlug',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/slugs/validate',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __validateSlug;\n}\n\n/**\n * Clones a service.\n *\n *\n * ## Connected entities\n *\n * By default, not all entities connected to the service are cloned.\n *\n * ### Schedule\n *\n * Wix Bookings automatically creates a new active *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * for the cloned service. If Wix Bookings can't create this schedule, the\n * Clone Service* call fails.\n *\n * - __For appointment-based services__: Future appointments aren't added to the\n * cloned service's schedule. Use the *Events API*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * to add them as needed.\n * - __For classes and courses__: Future one-time events aren't added to the\n * cloned service's schedule, while future recurring events are added\n * asynchronously. The *Clone Service* call always succeeds, whether or not\n * recurring events are added.\n * If the response includes `RECURRING_EVENTS` in the `errors` array, it means the\n * cloned service doesn't have future recurring events, even though the original\n * service does. You can either delete the cloned service and try again or use\n * the *Events API*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * to add missing events to the schedule.\n *\n * Wix Bookings never adds past events to the cloned service's schedule.\n *\n * ### Service options and variants\n *\n * If the original service has *variants*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),\n * they're cloned asynchronously. The *Clone Service* call always succeeds,\n * regardless of whether variants were cloned.\n *\n * If the response includes `OPTIONS_AND_VARIANTS` in the `errors` array, the cloned\n * service doesn't have variants, even though the original service does. You can\n * delete the cloned service and call *Clone Service* again, or use the\n * _Service Options And Variants API_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction))\n * to add variants.\n *\n * ### Booking form\n *\n * The original service's booking form isn't cloned, whether it's\n * the default or a custom booking form.\n *\n * ### Pricing plans\n *\n * If the original service's accepts payments via *pricing plans*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/plans/introduction)),\n * the cloned service's `payment.options.pricingPlan` is also set to `true`. To\n * accept specific pricing plans, call *Enable Pricing Plans For Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/enable-pricing-plans-for-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/enable-pricing-plans-for-service)),\n * after cloning the service. If the original service accepts only\n * pricing plan payments and you don't call *Enable Pricing Plans For Service*\n * after cloning the service, customers will be unable to book the service.\n */\nexport function cloneService(payload: object): RequestOptionsFactory<any> {\n  function __cloneService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.CloneService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/clone',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __cloneService;\n}\n", "import * as ambassadorWixBookingsServicesV2Service from './bookings-services-v2-service-services.http.js';\nimport * as ambassadorWixBookingsServicesV2ServiceTypes from './bookings-services-v2-service-services.types.js';\nimport * as ambassadorWixBookingsServicesV2ServiceUniversalTypes from './bookings-services-v2-service-services.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function createAddOnGroup(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CreateAddOnGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.CreateAddOnGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CreateAddOnGroupResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.CreateAddOnGroupResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.createAddOnGroup(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/add-on-groups/create',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteAddOnGroup(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.DeleteAddOnGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.DeleteAddOnGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.DeleteAddOnGroupResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.DeleteAddOnGroupResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.deleteAddOnGroup(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/add-on-groups/delete',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateAddOnGroup(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.UpdateAddOnGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.UpdateAddOnGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.UpdateAddOnGroupResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.UpdateAddOnGroupResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.updateAddOnGroup(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/add-on-groups/update',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function listAddOnGroupsByServiceId(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.ListAddOnGroupsByServiceIdRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.ListAddOnGroupsByServiceIdRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.ListAddOnGroupsByServiceIdResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.ListAddOnGroupsByServiceIdResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.listAddOnGroupsByServiceId(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/add-on-groups/list-add-on-groups-by-service-id',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function setAddOnsForGroup(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SetAddOnsForGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.SetAddOnsForGroupRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SetAddOnsForGroupResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.SetAddOnsForGroupResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.setAddOnsForGroup(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/add-on-groups/set-add-ons-for-group',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function createService(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CreateServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.CreateServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CreateServiceResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.CreateServiceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.createService(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkCreateServices(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkCreateServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkCreateServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkCreateServicesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkCreateServicesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.bulkCreateServices(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/services/create',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getService(): __PublicMethodMetaInfo<\n  'GET',\n  { serviceId: string },\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.GetServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.GetServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.GetServiceResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.GetServiceResponse\n> {\n  const payload = { serviceId: ':serviceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.getService(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/services/{serviceId}',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateService(): __PublicMethodMetaInfo<\n  'PATCH',\n  { serviceId: string },\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.UpdateServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.UpdateServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.UpdateServiceResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.UpdateServiceResponse\n> {\n  const payload = { service: { id: ':serviceId' } } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.updateService(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PATCH',\n    path: '/v2/services/{service.id}',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkUpdateServices(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkUpdateServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkUpdateServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkUpdateServicesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkUpdateServicesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.bulkUpdateServices(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/services/update',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkUpdateServicesByFilter(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkUpdateServicesByFilterRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkUpdateServicesByFilterRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkUpdateServicesByFilterResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkUpdateServicesByFilterResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.bulkUpdateServicesByFilter(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/services/update-by-filter',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteService(): __PublicMethodMetaInfo<\n  'DELETE',\n  { serviceId: string },\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.DeleteServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.DeleteServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.DeleteServiceResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.DeleteServiceResponse\n> {\n  const payload = { serviceId: ':serviceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.deleteService(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'DELETE',\n    path: '/v2/services/{serviceId}',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkDeleteServices(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkDeleteServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkDeleteServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkDeleteServicesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkDeleteServicesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.bulkDeleteServices(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/services/delete',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkDeleteServicesByFilter(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkDeleteServicesByFilterRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkDeleteServicesByFilterRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.BulkDeleteServicesByFilterResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.BulkDeleteServicesByFilterResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.bulkDeleteServicesByFilter(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/services/delete-by-filter',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryServices(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryServicesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryServicesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.queryServices(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function searchServices(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SearchServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.SearchServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SearchServicesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.SearchServicesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.searchServices(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/search',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryPolicies(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryPoliciesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryPoliciesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryPoliciesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryPoliciesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.queryPolicies(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/policies/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryBookingForms(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryBookingFormsRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryBookingFormsRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryBookingFormsResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryBookingFormsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.queryBookingForms(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/booking-forms/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function countServices(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CountServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.CountServicesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CountServicesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.CountServicesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.countServices(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/count',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryLocations(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryLocationsRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryLocationsRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryLocationsResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryLocationsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.queryLocations(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/locations/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryCategories(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryCategoriesRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryCategoriesRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.QueryCategoriesResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.QueryCategoriesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.queryCategories(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/categories/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function setServiceLocations(): __PublicMethodMetaInfo<\n  'POST',\n  { serviceId: string },\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SetServiceLocationsRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.SetServiceLocationsRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SetServiceLocationsResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.SetServiceLocationsResponse\n> {\n  const payload = { serviceId: ':serviceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.setServiceLocations(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/{serviceId}/locations',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function enablePricingPlansForService(): __PublicMethodMetaInfo<\n  'POST',\n  { serviceId: string },\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.EnablePricingPlansForServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.EnablePricingPlansForServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.EnablePricingPlansForServiceResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.EnablePricingPlansForServiceResponse\n> {\n  const payload = { serviceId: ':serviceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.enablePricingPlansForService(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/{serviceId}/pricing-plans/add',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function disablePricingPlansForService(): __PublicMethodMetaInfo<\n  'POST',\n  { serviceId: string },\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.DisablePricingPlansForServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.DisablePricingPlansForServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.DisablePricingPlansForServiceResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.DisablePricingPlansForServiceResponse\n> {\n  const payload = { serviceId: ':serviceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.disablePricingPlansForService(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/{serviceId}/pricing-plans/remove',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function setCustomSlug(): __PublicMethodMetaInfo<\n  'POST',\n  { serviceId: string },\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SetCustomSlugRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.SetCustomSlugRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.SetCustomSlugResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.SetCustomSlugResponse\n> {\n  const payload = { serviceId: ':serviceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.setCustomSlug(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/{serviceId}/slugs/custom',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function validateSlug(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.ValidateSlugRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.ValidateSlugRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.ValidateSlugResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.ValidateSlugResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.validateSlug(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/slugs/validate',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function cloneService(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CloneServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceTypes.CloneServiceRequest,\n  ambassadorWixBookingsServicesV2ServiceUniversalTypes.CloneServiceResponse,\n  ambassadorWixBookingsServicesV2ServiceTypes.CloneServiceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsServicesV2Service.cloneService(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/services/clone',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,oCAAoC;AAC7C,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,+CACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,SAAS,kDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAOd,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAOO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA8CO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,kDAAkD;AAAA,UAC1D,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,kCAAkC;AAAA,UAC1C,EAAE,MAAM,yDAAyD;AAAA,UACjE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,mCAAmC;AAAA,UAC3C,EAAE,MAAM,0DAA0D;AAAA,UAClE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,gCAAgC;AAAA,QAC1C;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,UAChE,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,wDAAwD;AAAA,UAChE,EAAE,MAAM,yDAAyD;AAAA,QACnE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,oCAAoC;AAAA,UAC9C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,WAAW,SAA6C;AACtE,WAAS,aAAa,EAAE,KAAK,GAAQ;AACnC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,kDAAkD;AAAA,UAC1D,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,kCAAkC;AAAA,UAC1C,EAAE,MAAM,yDAAyD;AAAA,UACjE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,gBAAgB,CAAC;AAAA,MACnC;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,+BAA+B;AAAA,UACvC,EAAE,MAAM,+BAA+B;AAAA,UACvC,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,2DAA2D;AAAA,UACnE,EAAE,MAAM,4DAA4D;AAAA,UACpE,EAAE,MAAM,6CAA6C;AAAA,UACrD,EAAE,MAAM,6CAA6C;AAAA,UACrD,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,2CAA2C;AAAA,UACnD;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,wCAAwC;AAAA,QAClD;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,oCAAoC;AAAA,UAC9C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiBO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,kDAAkD;AAAA,UAC1D,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,kCAAkC;AAAA,UAC1C,EAAE,MAAM,yDAAyD;AAAA,UACjE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,oCAAoC;AAAA,UAC9C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,mCAAmC;AAAA,YAC3C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,wDAAwD;AAAA,YAChE;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA+BO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yCAAyC;AAAA,UACjD,EAAE,MAAM,uCAAuC;AAAA,UAC/C;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,mCAAmC;AAAA,YAC3C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,wDAAwD;AAAA,YAChE;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsCO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,mDAAmD;AAAA,YAC3D;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,gDAAgD;AAAA,YACxD,EAAE,MAAM,gDAAgD;AAAA,YACxD,EAAE,MAAM,gDAAgD;AAAA,YACxD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA6CO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA4BO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA2BO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoDO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,4CAA4C;AAAA,UACpD,EAAE,MAAM,6CAA6C;AAAA,UACrD,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,gDAAgD;AAAA,UACxD;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAcO,SAAS,6BACd,SAC4B;AAC5B,WAAS,+BAA+B,EAAE,KAAK,GAAQ;AACrD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAYO,SAAS,8BACd,SAC4B;AAC5B,WAAS,gCAAgC,EAAE,KAAK,GAAQ;AACtD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mBAAmB;AAAA,YAC3B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgEO,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AChrEO,SAASC,oBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,iBAAiB,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,oBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,iBAAiB,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,oBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,iBAAiB,OAAO;AAEjE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,8BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,2BAA2B,OAAO;AAE3E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,kBAAkB,OAAO;AAElE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,cAAc,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,mBAAmB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,cAOd;AACA,QAAM,UAAU,EAAE,WAAW,aAAa;AAE1C,QAAM,oBACmC,WAAW,OAAO;AAE3D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,EAAE,SAAS,EAAE,IAAI,aAAa,EAAE;AAEhD,QAAM,oBACmC,cAAc,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,mBAAmB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,8BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,2BAA2B,OAAO;AAE3E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,EAAE,WAAW,aAAa;AAE1C,QAAM,oBACmC,cAAc,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,mBAAmB,OAAO;AAEnE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,8BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,2BAA2B,OAAO;AAE3E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,cAAc,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,eAAe,OAAO;AAE/D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,cAAc,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,kBAAkB,OAAO;AAElE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,cAAc,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,eAAe,OAAO;AAE/D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,mBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,gBAAgB,OAAO;AAEhE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,uBAOd;AACA,QAAM,UAAU,EAAE,WAAW,aAAa;AAE1C,QAAM,oBACmC,oBAAoB,OAAO;AAEpE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,gCAOd;AACA,QAAM,UAAU,EAAE,WAAW,aAAa;AAE1C,QAAM,oBACmC;AAAA,IACrC;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iCAOd;AACA,QAAM,UAAU,EAAE,WAAW,aAAa;AAE1C,QAAM,oBACmC;AAAA,IACrC;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,EAAE,WAAW,aAAa;AAE1C,QAAM,oBACmC,cAAc,OAAO;AAE9D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,gBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,aAAa,OAAO;AAE7D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,gBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACmC,aAAa,OAAO;AAE7D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "createAddOnGroup", "deleteAddOnGroup", "updateAddOnGroup", "listAddOnGroupsByServiceId", "setAddOnsForGroup", "createService", "bulkCreateServices", "getService", "updateService", "bulkUpdateServices", "bulkUpdateServicesByFilter", "deleteService", "bulkDeleteServices", "bulkDeleteServicesByFilter", "queryServices", "searchServices", "queryPolicies", "queryBookingForms", "countServices", "queryLocations", "queryCategories", "setServiceLocations", "enablePricingPlansForService", "disablePricingPlansForService", "setCustomSlug", "validateSlug", "cloneService"]}