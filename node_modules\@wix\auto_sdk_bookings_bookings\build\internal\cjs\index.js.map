{"version": 3, "sources": ["../../../index.ts", "../../../src/bookings-v2-booking-bookings.public.ts", "../../../src/bookings-v2-booking-bookings.universal.ts", "../../../src/bookings-v2-booking-bookings.http.ts", "../../../src/bookings-v2-booking-bookings.context.ts"], "sourcesContent": ["export * from './src/bookings-v2-booking-bookings.context.js';\n", "import { renameKeysFromRESTResponseToSDKResponse } from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { EventDefinition, HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  AddBookingsToMultiServiceBookingApplicationErrors,\n  AddBookingsToMultiServiceBookingOptions,\n  AddBookingsToMultiServiceBookingResponse,\n  Booking,\n  BookingCanceledEnvelope,\n  BookingConfirmedEnvelope,\n  BookingCreatedEnvelope,\n  BookingDeclinedEnvelope,\n  BookingNumberOfParticipantsUpdatedEnvelope,\n  BookingRescheduledEnvelope,\n  BookingUpdatedEnvelope,\n  BulkConfirmOrDeclineBookingApplicationErrors,\n  BulkConfirmOrDeclineBookingOptions,\n  BulkConfirmOrDeclineBookingRequestBookingDetails,\n  BulkConfirmOrDeclineBookingResponse,\n  BulkCreateBookingApplicationErrors,\n  BulkCreateBookingOptions,\n  BulkCreateBookingResponse,\n  BulkGetMultiServiceBookingAllowedActionsResponse,\n  CancelBookingApplicationErrors,\n  CancelBookingOptions,\n  CancelBookingResponse,\n  CancelMultiServiceBookingApplicationErrors,\n  CancelMultiServiceBookingOptions,\n  CancelMultiServiceBookingResponse,\n  ConfirmBookingApplicationErrors,\n  ConfirmBookingOptions,\n  ConfirmBookingResponse,\n  ConfirmMultiServiceBookingApplicationErrors,\n  ConfirmMultiServiceBookingOptions,\n  ConfirmMultiServiceBookingResponse,\n  ConfirmOrDeclineBookingApplicationErrors,\n  ConfirmOrDeclineBookingOptions,\n  ConfirmOrDeclineBookingResponse,\n  CreateBookingApplicationErrors,\n  CreateBookingInfo,\n  CreateBookingOptions,\n  CreateBookingResponse,\n  CreateMultiServiceBookingApplicationErrors,\n  CreateMultiServiceBookingOptions,\n  CreateMultiServiceBookingResponse,\n  DeclineBookingApplicationErrors,\n  DeclineBookingOptions,\n  DeclineBookingResponse,\n  DeclineMultiServiceBookingApplicationErrors,\n  DeclineMultiServiceBookingOptions,\n  DeclineMultiServiceBookingResponse,\n  GetMultiServiceBookingAvailabilityApplicationErrors,\n  GetMultiServiceBookingAvailabilityResponse,\n  MarkBookingAsPendingApplicationErrors,\n  MarkBookingAsPendingOptions,\n  MarkBookingAsPendingResponse,\n  MarkMultiServiceBookingAsPendingApplicationErrors,\n  MarkMultiServiceBookingAsPendingOptions,\n  MarkMultiServiceBookingAsPendingResponse,\n  MultiServiceBooking,\n  RemoveBookingsFromMultiServiceBookingApplicationErrors,\n  RemoveBookingsFromMultiServiceBookingOptions,\n  RemoveBookingsFromMultiServiceBookingResponse,\n  RescheduleBookingApplicationErrors,\n  RescheduleBookingInfo,\n  RescheduleBookingOptions,\n  RescheduleBookingResponse,\n  RescheduleMultiServiceBookingApplicationErrors,\n  RescheduleMultiServiceBookingOptions,\n  RescheduleMultiServiceBookingResponse,\n  SetBookingSubmissionIdApplicationErrors,\n  SetBookingSubmissionIdResponse,\n  UpdateExtendedFieldsApplicationErrors,\n  UpdateExtendedFieldsOptions,\n  UpdateExtendedFieldsResponse,\n  UpdateNumberOfParticipantsApplicationErrors,\n  UpdateNumberOfParticipantsOptions,\n  UpdateNumberOfParticipantsResponse,\n  V2Slot,\n  addBookingsToMultiServiceBooking as universalAddBookingsToMultiServiceBooking,\n  bulkConfirmOrDeclineBooking as universalBulkConfirmOrDeclineBooking,\n  bulkCreateBooking as universalBulkCreateBooking,\n  bulkGetMultiServiceBookingAllowedActions as universalBulkGetMultiServiceBookingAllowedActions,\n  cancelBooking as universalCancelBooking,\n  cancelMultiServiceBooking as universalCancelMultiServiceBooking,\n  confirmBooking as universalConfirmBooking,\n  confirmMultiServiceBooking as universalConfirmMultiServiceBooking,\n  confirmOrDeclineBooking as universalConfirmOrDeclineBooking,\n  createBooking as universalCreateBooking,\n  createMultiServiceBooking as universalCreateMultiServiceBooking,\n  declineBooking as universalDeclineBooking,\n  declineMultiServiceBooking as universalDeclineMultiServiceBooking,\n  getMultiServiceBooking as universalGetMultiServiceBooking,\n  getMultiServiceBookingAvailability as universalGetMultiServiceBookingAvailability,\n  markBookingAsPending as universalMarkBookingAsPending,\n  markMultiServiceBookingAsPending as universalMarkMultiServiceBookingAsPending,\n  removeBookingsFromMultiServiceBooking as universalRemoveBookingsFromMultiServiceBooking,\n  rescheduleBooking as universalRescheduleBooking,\n  rescheduleMultiServiceBooking as universalRescheduleMultiServiceBooking,\n  setBookingSubmissionId as universalSetBookingSubmissionId,\n  updateExtendedFields as universalUpdateExtendedFields,\n  updateNumberOfParticipants as universalUpdateNumberOfParticipants,\n} from './bookings-v2-booking-bookings.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function confirmOrDeclineBooking(\n  httpClient: HttpClient\n): ConfirmOrDeclineBookingSignature {\n  return (bookingId: string, options?: ConfirmOrDeclineBookingOptions) =>\n    universalConfirmOrDeclineBooking(\n      bookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ConfirmOrDeclineBookingSignature {\n  /**\n   * Updates the booking `status` to `CONFIRMED`, `PENDING`, or `DECLINED` based\n   * on the `paymentStatus` you provide, double booking conflicts, and whether\n   * the service requires business approval.\n   *\n   * ## eCommerce checkout restriction\n   *\n   * Call this method only when using a custom checkout page. Don't\n   * call it when using a *Wix eCommerce checkout*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n   * In such cases, Wix automatically updates the booking status based on\n   * the `paymentStatus` of the corresponding *Wix eCommerce order*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n   *\n   * ## New booking status\n   *\n   * The booking `status` is set to `DECLINED` if both of the following conditions\n   * are met:\n   * + You provide `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT` as payment status.\n   * + There is a double booking conflict.\n   *\n   * If only one or none of these conditions is met, `status` is set to `PENDING`\n   * or `CONFIRMED` depending on whether the service requires business approval.\n   *\n   * ## Double bookings\n   *\n   * If there is a double booking conflict, but the booking has already been at least\n   * partially paid, the method still marks the booking as `PENDING` or `CONFIRMED`.\n   * Then, it also sets `doubleBooked` to `true`.\n   *\n   * ## Admin overwrites\n   *\n   * There are small but important differences in confirmation behavior if the\n   * booking was created with special `flowControlSettings`:\n   * + If the booking's `flowControlSettings.skipAvailabilityValidation` was set to\n   * `true`, the booking is never declined regardless of double booking conflicts.\n   * Instead, it's marked `CONFIRMED` or `PENDING`, depending on whether the\n   * service requires business approval.\n   * + If the booking's `flowControlSettings.skipBusinessConfirmation` was set to\n   * `true`, the booking skips `PENDING` status and is marked `CONFIRMED`\n   * immediately.\n   * @param - ID of the booking to confirm or decline.\n   */\n  (bookingId: string, options?: ConfirmOrDeclineBookingOptions): Promise<\n    NonNullablePaths<\n      ConfirmOrDeclineBookingResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: ConfirmOrDeclineBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function bulkConfirmOrDeclineBooking(\n  httpClient: HttpClient\n): BulkConfirmOrDeclineBookingSignature {\n  return (\n    details: NonNullablePaths<\n      BulkConfirmOrDeclineBookingRequestBookingDetails,\n      `bookingId`,\n      2\n    >[],\n    options?: BulkConfirmOrDeclineBookingOptions\n  ) =>\n    universalBulkConfirmOrDeclineBooking(\n      details,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface BulkConfirmOrDeclineBookingSignature {\n  /**\n   * Confirms or declines up to 300 bookings.\n   *\n   *\n   * See *Confirm Or Decline Booking*\n   * ([SDK](https://dev.wix.com/docs/velo/api-reference/wix-bookings-v2/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking))\n   * for details about when a booking is confirmed or declined.\n   * @param - Bookings to confirm or decline.\n   */\n  (\n    details: NonNullablePaths<\n      BulkConfirmOrDeclineBookingRequestBookingDetails,\n      `bookingId`,\n      2\n    >[],\n    options?: BulkConfirmOrDeclineBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      BulkConfirmOrDeclineBookingResponse,\n      | `results`\n      | `results.${number}.itemMetadata.originalIndex`\n      | `results.${number}.itemMetadata.success`\n      | `results.${number}.itemMetadata.error.code`\n      | `results.${number}.itemMetadata.error.description`\n      | `results.${number}.item.totalParticipants`\n      | `results.${number}.item.status`\n      | `results.${number}.item.paymentStatus`\n      | `results.${number}.item.selectedPaymentOption`\n      | `results.${number}.item.createdBy.anonymousVisitorId`\n      | `results.${number}.item.createdBy.memberId`\n      | `results.${number}.item.createdBy.wixUserId`\n      | `results.${number}.item.createdBy.appId`\n      | `bulkActionMetadata.totalSuccesses`\n      | `bulkActionMetadata.totalFailures`\n      | `bulkActionMetadata.undetailedFailures`,\n      6\n    > & {\n      __applicationErrorsType?: BulkConfirmOrDeclineBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function createBooking(httpClient: HttpClient): CreateBookingSignature {\n  return (\n    booking: NonNullablePaths<\n      Booking,\n      `additionalFields.${number}._id` | `bookedEntity`,\n      4\n    >,\n    options?: CreateBookingOptions\n  ) =>\n    universalCreateBooking(\n      booking,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CreateBookingSignature {\n  /**\n   * Creates a booking.\n   *\n   *\n   * ## Appointment booking\n   *\n   * For appointment-based services, specify the relevant `slot` in\n   * `bookedEntity.slot`. We recommend specifying the complete\n   * `availabilityEntries.slot` returned in Query Availability\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n   * in your call's request to avoid failed calls due to unavailability.\n   *\n   * ## Class session booking\n   *\n   * For class services, specify the relevant event ID\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n   * as `bookedEntity.slot.eventId`.\n   * We recommend retrieving the event ID from Query Availability's\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n   * `availabilityEntries.slot.eventId` to avoid failed calls due to unavailability.\n   * Specifying an event ID leads to automatic calculations of `slot.startDate`, `slot.endDate`,\n   * `slot.timezone`, `slot.resource`, and `slot.location`. All manually specified\n   * values are overridden.\n   *\n   * ## Course booking\n   *\n   * For course services, specify the course's schedule ID in `bookedEntity.schedule.scheduleId`.\n   * We recommend following [this sample flow](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course)\n   * to minimize failed calls due to unavailability.\n   *\n   * ## Related resources\n   *\n   * Specifying a `resource` triggers an availability check, resulting in a failed\n   * call if the resource is unavailable. Omitting a resource allows Wix Bookings\n   * to assign a resource belonging to the relevant type randomly when the merchant\n   * confirms the booking.\n   *\n   * ## Participant information\n   *\n   * You must specify either `participantsChoices` or `totalParticipants`.\n   * The call fails if the specified `participantsChoices` aren't among the supported\n   * _service options and variants_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   *\n   * ## Notify customers\n   *\n   * You can specify a `participantNotification.message` for the customer that's send\n   * immediately. Ensure `participantNotification.notifyParticipants` is set to `true`\n   * to send the message.\n   *\n   * If you specify `{\"sendSmsReminder\": true}`, the customer receives an SMS 24 hours\n   * before the session starts. The phone number is taken from `contactDetails.phone`.\n   *\n   * ## Booking status\n   *\n   * Bookings default to the `CREATED` status, not affecting the business calendar\n   * or resource availability. You can specify a different status when the calling\n   * [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities)\n   * has `Manage Bookings` permissions.\n   *\n   * ## Payment options\n   *\n   * The specified `selectedPaymentOption` indicates how the customer intends to\n   * pay, allowing for later changes to a different method supported by the service.\n   *\n   * ## Payment status\n   *\n   * A booking is initially created with `{\"paymentStatus\": \"UNDEFINED\"}` regardless\n   * of the payment status specified in Create Booking. If a customer uses an\n   * _eCommerce checkout_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),\n   * Wix Bookings automatically syncs the booking's payment status from\n   * the corresponding eCommerce order\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n   *\n   * If a booking doesn't have a corresponding eCommerce order, for example, since\n   * the customer didn't use the eCommerce checkout, you can update the booking's\n   * payment status with Confirm Or Decline Booking\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).\n   *\n   * ## Booking form data\n   *\n   * When customers sign up for a service, they must fill out the booking form.\n   * To create a booking with a completed booking form, specify the relevant data in\n   * `formSubmission`. Ensure the values of the corresponding fields in\n   * `booking.contactDetails` and `formSubmission` are identical. If these values\n   * don't match, Create Booking fails. Therefore, we recommend specifying\n   * only `booking.contactDetails.contactId` when providing `formSubmission`.\n   *\n   * ## Admin overwrites\n   *\n   * There are small but important differences when you specify special\n   * `flowControlSettings`:\n   *\n   * - `{\"skipAvailabilityValidation\": true}`: The call succeeds\n   * regardless of availability. If you don't specify any resource, the call\n   * succeeds even if no resource of the relevant type is available.\n   * - `{\"skipBusinessConfirmation\": true}`: Automatically confirms `PENDING`\n   * bookings that require manual confirmation.\n   * - `{\"skipSelectedPaymentOptionValidation\": true}`: Allows customers to pay\n   * with payment methods that aren't supported for the service.\n   *\n   * When using special `flowControlSettings`, ensure you have sufficient\n   * permissions. If you encounter failed calls due to insufficient permissions,\n   * consider the following options:\n   *\n   * - **App developers** can use a higher\n   * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n   * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n   * - **Site developers** can utilize\n   * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n   *\n   * Granting additional permissions and using elevation permits method calls that\n   * would typically fail due to authorization checks. Therefore, you should use\n   * them intentionally and securely.\n   * @param - The booking to create.\n   */\n  (\n    booking: NonNullablePaths<\n      Booking,\n      `additionalFields.${number}._id` | `bookedEntity`,\n      4\n    >,\n    options?: CreateBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      CreateBookingResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: CreateBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function bulkCreateBooking(\n  httpClient: HttpClient\n): BulkCreateBookingSignature {\n  return (\n    createBookingsInfo: NonNullablePaths<\n      CreateBookingInfo,\n      | `booking`\n      | `booking.additionalFields.${number}._id`\n      | `booking.bookedEntity`,\n      5\n    >[],\n    options?: BulkCreateBookingOptions\n  ) =>\n    universalBulkCreateBooking(\n      createBookingsInfo,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface BulkCreateBookingSignature {\n  /**\n   * Creates up to 8 bookings.\n   *\n   *\n   * See Create Booking\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking))\n   * for more information.\n   *\n   * If any of the specified bookings is missing a required field the entire call\n   * fails.\n   *\n   * If you specify 1 or more unavailable bookings, the call succeeds\n   * while the unavailable bookings aren't created. Instead, they're counted as\n   * failures in the returned `bulkActionMetadata`.\n   * @param - Bookings to create.\n   *\n   * Max: 8 bookings\n   */\n  (\n    createBookingsInfo: NonNullablePaths<\n      CreateBookingInfo,\n      | `booking`\n      | `booking.additionalFields.${number}._id`\n      | `booking.bookedEntity`,\n      5\n    >[],\n    options?: BulkCreateBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      BulkCreateBookingResponse,\n      | `results`\n      | `results.${number}.itemMetadata.originalIndex`\n      | `results.${number}.itemMetadata.success`\n      | `results.${number}.itemMetadata.error.code`\n      | `results.${number}.itemMetadata.error.description`\n      | `results.${number}.item.totalParticipants`\n      | `results.${number}.item.status`\n      | `results.${number}.item.paymentStatus`\n      | `results.${number}.item.selectedPaymentOption`\n      | `results.${number}.item.createdBy.anonymousVisitorId`\n      | `results.${number}.item.createdBy.memberId`\n      | `results.${number}.item.createdBy.wixUserId`\n      | `results.${number}.item.createdBy.appId`\n      | `bulkActionMetadata.totalSuccesses`\n      | `bulkActionMetadata.totalFailures`\n      | `bulkActionMetadata.undetailedFailures`,\n      6\n    > & {\n      __applicationErrorsType?: BulkCreateBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function rescheduleBooking(\n  httpClient: HttpClient\n): RescheduleBookingSignature {\n  return (\n    bookingId: string,\n    slot: V2Slot,\n    options?: NonNullablePaths<RescheduleBookingOptions, `revision`, 2>\n  ) =>\n    universalRescheduleBooking(\n      bookingId,\n      slot,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface RescheduleBookingSignature {\n  /**\n   * Reschedules an appointment booking to a different slot or a class booking to\n   * a different session.\n   *\n   *\n   * ## Course booking limitation\n   *\n   * You can't reschedule course bookings.\n   *\n   * ## Appointment sessions\n   *\n   * For appointments, the old session is removed from the business calendar\n   * while a new session is added. We recommend calling Query Availability\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n   * first and specifying the entire retrieved `slot`.\n   *\n   * ## Class sessions\n   *\n   * For classes, the new session must be an existing session belonging to the\n   * same class. We recommend retrieving `availabilityEntries.slot.eventId`\n   * from Query Availability\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n   * to avoid failed Reschedule Booking calls due to unavailability. Specify\n   * only `slot.eventId` instead of the entire `slot` object.\n   *\n   * ## Notify customers\n   *\n   * You can specify a `participantNotification.message` for the customer. To send\n   * the message, you must also specify `participantNotification.notifyParticipants`\n   * as `true`.\n   *\n   * ## Admin overwrites\n   *\n   * There are small but important differences when you specify special\n   * `flowControlSettings`:\n   *\n   * - `{\"ignoreReschedulePolicy\": true}`: The call succeeds even if the\n   * service's `reschedulePolicy` doesn't allow it.\n   * - `{\"skipAvailabilityValidation\": true}`: The call succeeds even if\n   * the specified session, slot, or resource isn't available. If you don't\n   * specify any resource, the call succeeds even if no resource of the relevant\n   * type is available.\n   * - `{\"skipBusinessConfirmation\": true}`: Any `PENDING` booking is\n   * automatically confirmed even if the services requires the merchants's\n   * manual confirmation.\n   *\n   * When using special `flowControlSettings`, ensure you have sufficient\n   * permissions. If you encounter failed calls due to insufficient permissions,\n   * consider the following options:\n   *\n   * - **App developers** can use a higher\n   * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n   * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n   * - **Site developers** can utilize\n   * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n   *\n   * Granting additional permissions and using elevation permits method calls that\n   * would typically fail due to authorization checks. Therefore, you should use\n   * them intentionally and securely.\n   * @param - ID of the booking to reschedule.\n   * @param - New slot of the booking.\n   * @param - An object representing the available options for rescheduling a booking.\n   */\n  (\n    bookingId: string,\n    slot: V2Slot,\n    options?: NonNullablePaths<RescheduleBookingOptions, `revision`, 2>\n  ): Promise<\n    NonNullablePaths<\n      RescheduleBookingResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: RescheduleBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function confirmBooking(\n  httpClient: HttpClient\n): ConfirmBookingSignature {\n  return (\n    bookingId: string,\n    revision: string,\n    options?: ConfirmBookingOptions\n  ) =>\n    universalConfirmBooking(\n      bookingId,\n      revision,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ConfirmBookingSignature {\n  /**\n   * Updates the booking status to `CONFIRMED` without checking whether the relevant slot or schedule is still available.\n   *\n   *\n   * ## eCommerce checkout restriction\n   *\n   * Call this method only when using a custom checkout page. Don't\n   * call it when using a Wix eCommerce checkout\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n   * In such cases, Wix automatically updates the booking status based on\n   * the `paymentStatus` of the corresponding Wix eCommerce order\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n   *\n   * ## When to call Confirm Or Decline Booking instead\n   *\n   * Confirm Booking doesn't check whether a slot or schedule is still available.\n   * For these checks, call Confirm or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) instead.\n   *\n   * ## Original status validation\n   *\n   * You can only confirm bookings with a status of `PENDING`, `CREATED`, or\n   * `WAITING_LIST`.\n   *\n   * ## Double bookings\n   *\n   * Confirm Booking doesn't check whether a slot or schedule is still available.\n   * You can specify\n   *\n   * ```json\n   * {\n   * \"flowControlSettings\": {\n   * \"checkAvailabilityValidation\": true\n   * },\n   * \"doubleBooked\": true\n   * }\n   * ```\n   * to forcefully set the booking's `doubleBooked` flag to `true`, regardless of\n   * a potential double booking conflict. You must call with `Manage Bookings`\n   * permissions to do so. For the default flow control settings\n   * `{\"checkAvailabilityValidation\": false}`, the specified `doubleBooked` value\n   * is ignored.\n   *\n   * ## Payment status\n   *\n   * Also updates the booking's `paymentStatus`, if you specify a new payment\n   * status.\n   *\n   * ## Notify customers\n   *\n   * You can specify a `participantNotification.message` for the customer. To send\n   * the message, you must also specify `participantNotification.notifyParticipants`\n   * as `true`.\n   * @param - ID of the booking to confirm.\n   * @param - Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be passed when\n   * updating the booking.\n   * @param - An object representing the available options for canceling a booking.\n   */\n  (\n    bookingId: string,\n    revision: string,\n    options?: ConfirmBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      ConfirmBookingResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: ConfirmBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function setBookingSubmissionId(\n  httpClient: HttpClient\n): SetBookingSubmissionIdSignature {\n  return (bookingId: string, submissionId: string) =>\n    universalSetBookingSubmissionId(\n      bookingId,\n      submissionId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface SetBookingSubmissionIdSignature {\n  /**\n   * Setting `submission_id` for a booking after the form submission is created.\n   * @param - ID of the booking to set `submissionId` for.\n   * @param - ID of the form submission to set on the booking.\n   */\n  (bookingId: string, submissionId: string): Promise<\n    NonNullablePaths<\n      SetBookingSubmissionIdResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: SetBookingSubmissionIdApplicationErrors;\n    }\n  >;\n}\n\nexport function updateExtendedFields(\n  httpClient: HttpClient\n): UpdateExtendedFieldsSignature {\n  return (\n    _id: string,\n    namespace: string,\n    options: NonNullablePaths<UpdateExtendedFieldsOptions, `namespaceData`, 2>\n  ) =>\n    universalUpdateExtendedFields(\n      _id,\n      namespace,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateExtendedFieldsSignature {\n  /**\n   * Updates the extended fields for a booking.\n   *\n   *\n   * If you specify an extended field `namespace` that doesn't exist yet, it's\n   * created.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/about-backend-extensions#schema-plugin-extensions).\n   * @param - ID of the booking for which to update extended fields.\n   * @param - [Namespace](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-reading-and-writing-schema-plugin-fields#namespaces) of the app for which to update extended fields.\n   * @param - Options for updating the booking's extended fields.\n   */\n  (\n    _id: string,\n    namespace: string,\n    options: NonNullablePaths<UpdateExtendedFieldsOptions, `namespaceData`, 2>\n  ): Promise<\n    NonNullablePaths<UpdateExtendedFieldsResponse, `namespace`, 2> & {\n      __applicationErrorsType?: UpdateExtendedFieldsApplicationErrors;\n    }\n  >;\n}\n\nexport function declineBooking(\n  httpClient: HttpClient\n): DeclineBookingSignature {\n  return (\n    bookingId: string,\n    revision: string,\n    options?: DeclineBookingOptions\n  ) =>\n    universalDeclineBooking(\n      bookingId,\n      revision,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DeclineBookingSignature {\n  /**\n   * Updates the booking status to `DECLINED` and updates the relevant session's\n   * `participants.approvalStatus` to `DECLINED` without checking whether the relevant\n   * slot or schedule is still available.\n   *\n   *\n   * ## eCommerce checkout restriction\n   *\n   * Call this method only when using a custom checkout page. Don't\n   * call it when using a Wix eCommerce checkout\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n   * In such cases, Wix automatically updates the booking status based on\n   * the `paymentStatus` of the corresponding Wix eCommerce order\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n   *\n   * ## When to call Confirm Or Decline Booking instead\n   *\n   * The method doesn't check whether a slot or schedule is still available. For\n   * these checks you can call Confirm or Decline Booking\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).\n   *\n   * ## Original status validation\n   *\n   * You can only decline bookings with a `status` of `PENDING`, `CREATED`, or\n   * `WAITING_LIST`.\n   *\n   * ## Payment status\n   *\n   * Also updates the booking's `paymentStatus`, if you specify a new payment\n   * status.\n   *\n   * ## Notify customers\n   *\n   * You can specify a `participantNotification.message` for the customer. To send\n   * the message, you must also specify `participantNotification.notifyParticipants`\n   * as `true`.\n   * @param - ID of the booking to decline.\n   * @param - Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified when\n   * declining the booking.\n   * @param - An object representing the available options for declining a booking.\n   */\n  (\n    bookingId: string,\n    revision: string,\n    options?: DeclineBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      DeclineBookingResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: DeclineBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function cancelBooking(httpClient: HttpClient): CancelBookingSignature {\n  return (\n    bookingId: string,\n    options?: NonNullablePaths<CancelBookingOptions, `revision`, 2>\n  ) =>\n    universalCancelBooking(\n      bookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CancelBookingSignature {\n  /**\n   * Updates the booking status to `CANCELED`.\n   *\n   *\n   * ## Appointments\n   *\n   * For appointments, the corresponding event is removed from the Bookings\n   * calendar.\n   *\n   * ## Class and course bookings\n   *\n   * For class or course bookings, the relevant participants are removed\n   * from the class session or the course. However, the class session or course\n   * remain on the business calendar.\n   *\n   * ## Notify customers\n   *\n   * You can specify a `participantNotification.message` for the customer. To send\n   * the message, you must also specify `participantNotification.notifyParticipants`\n   * as `true`.\n   *\n   * ## Admin overwrites\n   *\n   * There are small but important differences when you specify special\n   * `flowControlSettings`:\n   *\n   * - `{\"ignoreCancellationPolicy\": true}`: The call succeeds even if the\n   * service's `cancellationPolicy` doesn't allow it.\n   * - `{\"withRefund\": true}`: The customer is refunded even if the service's\n   * `refundPolicy` doesn't allow it.\n   * - `{\"waiveCancellationFee\": true}`: The customer doesn't have to pay\n   * the cancellation fee, even if the service's `cancellationPolicy` requires it.\n   *\n   * When using special `flowControlSettings`, ensure you have sufficient\n   * permissions. If you encounter failed calls due to insufficient permissions,\n   * consider the following options:\n   *\n   * - **App developers** can use a higher\n   * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n   * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n   * - **Site developers** can utilize\n   * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n   *\n   * Granting additional permissions and using elevation permits method calls that\n   * would typically fail due to authorization checks. Therefore, you should use\n   * them intentionally and securely.\n   * @param - ID of the booking to cancel.\n   * @param - An object representing the available options for canceling a booking.\n   */\n  (\n    bookingId: string,\n    options?: NonNullablePaths<CancelBookingOptions, `revision`, 2>\n  ): Promise<\n    NonNullablePaths<\n      CancelBookingResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: CancelBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function updateNumberOfParticipants(\n  httpClient: HttpClient\n): UpdateNumberOfParticipantsSignature {\n  return (\n    bookingId: string,\n    options?: NonNullablePaths<UpdateNumberOfParticipantsOptions, `revision`, 2>\n  ) =>\n    universalUpdateNumberOfParticipants(\n      bookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface UpdateNumberOfParticipantsSignature {\n  /**\n   * Updates the number of participants for a class or course booking and changes\n   * the `totalNumberOfParticipants` for the relevant sessions.\n   *\n   *\n   * ## Appointment limitation\n   *\n   * You can't update the number of participants for appointment bookings.\n   *\n   * ## Participant information\n   *\n   * You must specify either `participantsChoices` or `totalParticipants`.\n   * The call fails if the specified `participantsChoices` aren't among the\n   * supported service options and variants\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @param - ID of the booking to update the number of participants for.\n   */\n  (\n    bookingId: string,\n    options?: NonNullablePaths<UpdateNumberOfParticipantsOptions, `revision`, 2>\n  ): Promise<\n    NonNullablePaths<\n      UpdateNumberOfParticipantsResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: UpdateNumberOfParticipantsApplicationErrors;\n    }\n  >;\n}\n\nexport function markBookingAsPending(\n  httpClient: HttpClient\n): MarkBookingAsPendingSignature {\n  return (\n    bookingId: string,\n    revision: string,\n    options?: MarkBookingAsPendingOptions\n  ) =>\n    universalMarkBookingAsPending(\n      bookingId,\n      revision,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface MarkBookingAsPendingSignature {\n  /**\n   * @param - ID of the booking to mark as `PENDING`.\n   * @param - Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified when\n   * managing the booking.\n   */\n  (\n    bookingId: string,\n    revision: string,\n    options?: MarkBookingAsPendingOptions\n  ): Promise<\n    NonNullablePaths<\n      MarkBookingAsPendingResponse,\n      | `booking.totalParticipants`\n      | `booking.participantsChoices.serviceChoices`\n      | `booking.bookedEntity.slot.serviceId`\n      | `booking.bookedEntity.slot.scheduleId`\n      | `booking.bookedEntity.slot.resource._id`\n      | `booking.bookedEntity.slot.location.locationType`\n      | `booking.bookedEntity.schedule.scheduleId`\n      | `booking.bookedEntity.tags`\n      | `booking.contactDetails.fullAddress.streetAddress.number`\n      | `booking.contactDetails.fullAddress.streetAddress.name`\n      | `booking.contactDetails.fullAddress.streetAddress.apt`\n      | `booking.contactDetails.fullAddress.subdivisions`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n      | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n      | `booking.additionalFields`\n      | `booking.additionalFields.${number}._id`\n      | `booking.additionalFields.${number}.valueType`\n      | `booking.status`\n      | `booking.paymentStatus`\n      | `booking.selectedPaymentOption`\n      | `booking.createdBy.anonymousVisitorId`\n      | `booking.createdBy.memberId`\n      | `booking.createdBy.wixUserId`\n      | `booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: MarkBookingAsPendingApplicationErrors;\n    }\n  >;\n}\n\nexport function createMultiServiceBooking(\n  httpClient: HttpClient\n): CreateMultiServiceBookingSignature {\n  return (\n    bookings: NonNullablePaths<\n      Booking,\n      | `additionalFields.${number}._id`\n      | `bookedEntity`\n      | `bookedEntity.slot`\n      | `bookedEntity.slot.endDate`\n      | `bookedEntity.slot.location.locationType`\n      | `bookedEntity.slot.scheduleId`\n      | `bookedEntity.slot.startDate`,\n      5\n    >[],\n    options?: CreateMultiServiceBookingOptions\n  ) =>\n    universalCreateMultiServiceBooking(\n      bookings,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CreateMultiServiceBookingSignature {\n  /**\n   * Creates a multi-service booking and all included single-service bookings simultaneously.\n   *\n   *\n   * ## When to call this method\n   *\n   * Create sequential appointments where customers book related services together. For adding existing single-service bookings to an existing multi-service booking, call Add Bookings to Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking)) instead.\n   *\n   * ## Requirements and behavior\n   *\n   * __Package constraints__: Multi-service bookings support 2-8 appointment-based single-service bookings only (course and class bookings aren't supported). All single-service bookings must be at the same location with sequential scheduling and no gaps between appointments.\n   *\n   * __Timing specification__: You must provide complete `slot` details (`scheduleId`, `startDate`, `endDate`) for each single-service booking. Wix Bookings validates sequential timing but doesn't auto-calculate it.\n   *\n   * __Package pricing__: The total price equals the sum of individual services. Wix Bookings automatically syncs the payment status from the corresponding Wix eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecommerce/orders/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer paid via an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n   *\n   * __Package notifications__: Customers receive 1 unified notification for the entire multi-service booking. Wix Bookings doesn't send notifications for the package's individual single-service bookings.\n   *\n   * ## Related methods\n   *\n   * Verify availability first with List Multi Service Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)).\n   *\n   * See Create Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking)) for more details about constraints and effects of creating single-service bookings.\n   * @param - Between 2 to 8 new single-service booking specifications to create and combine in a multi-service booking.\n   *\n   * Each single-service booking specification must include `slot` details (`scheduleId`, `startDate`, and `endDate`).\n   * Specify contact details, number of participants, and any additional fields as needed.\n   *\n   * For sequential bookings, ensure the timing allows services to be scheduled back-to-back.\n   */\n  (\n    bookings: NonNullablePaths<\n      Booking,\n      | `additionalFields.${number}._id`\n      | `bookedEntity`\n      | `bookedEntity.slot`\n      | `bookedEntity.slot.endDate`\n      | `bookedEntity.slot.location.locationType`\n      | `bookedEntity.slot.scheduleId`\n      | `bookedEntity.slot.startDate`,\n      5\n    >[],\n    options?: CreateMultiServiceBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      CreateMultiServiceBookingResponse,\n      | `multiServiceBooking.bookings`\n      | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n      | `multiServiceBooking.bookings.${number}.booking.status`\n      | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n      | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: CreateMultiServiceBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function rescheduleMultiServiceBooking(\n  httpClient: HttpClient\n): RescheduleMultiServiceBookingSignature {\n  return (\n    multiServiceBookingId: string,\n    rescheduleBookingsInfo: NonNullablePaths<\n      RescheduleBookingInfo,\n      `bookingId` | `revision` | `slot`,\n      2\n    >[],\n    options?: RescheduleMultiServiceBookingOptions\n  ) =>\n    universalRescheduleMultiServiceBooking(\n      multiServiceBookingId,\n      rescheduleBookingsInfo,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface RescheduleMultiServiceBookingSignature {\n  /**\n   * Reschedules a multi-service booking by changing the timing for all or specific single-service bookings in the package.\n   *\n   *\n   * This method reschedules single-service bookings within the multi-service booking while maintaining sequential order. You must specify exact new timing for each service to ensure they remain back-to-back with no gaps or overlaps.\n   *\n   * This method fails if it can't reschedule at least 1 single-service booking. You must provide the current revision number for each single-service booking you're rescheduling to prevent conflicting changes.\n   *\n   * See Reschedule Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-booking)) for single-service booking rescheduling details.\n   * @param - ID of the multi-service booking to reschedule.\n   * @param - Information about the single-service bookings to reschedule.\n   */\n  (\n    multiServiceBookingId: string,\n    rescheduleBookingsInfo: NonNullablePaths<\n      RescheduleBookingInfo,\n      `bookingId` | `revision` | `slot`,\n      2\n    >[],\n    options?: RescheduleMultiServiceBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      RescheduleMultiServiceBookingResponse,\n      | `multiServiceBooking.bookings`\n      | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n      | `multiServiceBooking.bookings.${number}.booking.status`\n      | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n      | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: RescheduleMultiServiceBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function getMultiServiceBookingAvailability(\n  httpClient: HttpClient\n): GetMultiServiceBookingAvailabilitySignature {\n  return (multiServiceBookingId: string) =>\n    universalGetMultiServiceBookingAvailability(\n      multiServiceBookingId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetMultiServiceBookingAvailabilitySignature {\n  /**\n   * Checks if the business can still accommodate an existing multi-service booking and returns overall bookability status, capacity details, and policy violations.\n   *\n   *\n   * Wix Bookings considers:\n   * - The relevant services' booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).\n   * - The availability of all required resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).\n   *\n   * Call this method to check if an existing multi-service booking is still valid after business configuration changes.\n   * For example, staff changes, policy updates, or capacity modifications.\n   *\n   * For checking availability before creating new multi-service bookings, call List Multi Service Availability Time Slots\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)) instead.\n   * @param - ID of the multi-service booking to retrieve.\n   */\n  (multiServiceBookingId: string): Promise<\n    NonNullablePaths<\n      GetMultiServiceBookingAvailabilityResponse,\n      `bookable` | `multiServiceBookingInfo`,\n      2\n    > & {\n      __applicationErrorsType?: GetMultiServiceBookingAvailabilityApplicationErrors;\n    }\n  >;\n}\n\nexport function cancelMultiServiceBooking(\n  httpClient: HttpClient\n): CancelMultiServiceBookingSignature {\n  return (\n    multiServiceBookingId: string,\n    options?: CancelMultiServiceBookingOptions\n  ) =>\n    universalCancelMultiServiceBooking(\n      multiServiceBookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CancelMultiServiceBookingSignature {\n  /**\n   * Cancels a multi-service booking and all its associated single-service bookings.\n   *\n   *\n   * Cancels the entire multi-service booking, updating the status of all single-service bookings to `CANCELED`.\n   * The call fails if all single-service bookings are already canceled or declined.\n   *\n   * See Cancel Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/cancel-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/cancel-booking)) for single-service booking cancellation details.\n   * @param - ID of the multi-service booking to cancel.\n   */\n  (\n    multiServiceBookingId: string,\n    options?: CancelMultiServiceBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      CancelMultiServiceBookingResponse,\n      | `multiServiceBooking.bookings`\n      | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n      | `multiServiceBooking.bookings.${number}.booking.status`\n      | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n      | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: CancelMultiServiceBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function markMultiServiceBookingAsPending(\n  httpClient: HttpClient\n): MarkMultiServiceBookingAsPendingSignature {\n  return (\n    multiServiceBookingId: string,\n    options?: MarkMultiServiceBookingAsPendingOptions\n  ) =>\n    universalMarkMultiServiceBookingAsPending(\n      multiServiceBookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface MarkMultiServiceBookingAsPendingSignature {\n  /**\n   * Updates the status for all single-service bookings in a multi-service booking to `PENDING`.\n   *\n   *\n   * Call this method for multi-service bookings requiring manual business approval before confirmation.\n   *\n   * ## Status requirements\n   *\n   * - __Original status__: All single-service bookings must have `CREATED` status.\n   * - __Target status__: All bookings move to `PENDING` together (all-or-nothing operation).\n   *\n   * ## Checkout restrictions\n   *\n   * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n   * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n   *\n   * ## Additional updates\n   *\n   * - __Payment status__: Updates if you specify a new `markAsPendingBookingsInfo.paymentStatus`.\n   * - __Customer notifications__: Send messages using `participantNotification`.\n   * - __Revision control__: Requires current revision numbers for all single-service bookings.\n   *\n   * See Mark Booking as Pending ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending)) for more details about marking single-service bookings as pending.\n   * @param - ID of the multi-service booking to mark as `PENDING`.\n   */\n  (\n    multiServiceBookingId: string,\n    options?: MarkMultiServiceBookingAsPendingOptions\n  ): Promise<\n    NonNullablePaths<\n      MarkMultiServiceBookingAsPendingResponse,\n      | `multiServiceBooking.bookings`\n      | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n      | `multiServiceBooking.bookings.${number}.booking.status`\n      | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n      | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: MarkMultiServiceBookingAsPendingApplicationErrors;\n    }\n  >;\n}\n\nexport function confirmMultiServiceBooking(\n  httpClient: HttpClient\n): ConfirmMultiServiceBookingSignature {\n  return (\n    multiServiceBookingId: string,\n    options?: ConfirmMultiServiceBookingOptions\n  ) =>\n    universalConfirmMultiServiceBooking(\n      multiServiceBookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface ConfirmMultiServiceBookingSignature {\n  /**\n   * Updates the status for all single-service bookings in a multi-service booking to `CONFIRMED`.\n   *\n   *\n   * Call this method for multi-service bookings requiring manual business approval.\n   *\n   * ## Status requirements\n   *\n   * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.\n   * - __Target status__: All bookings move to `CONFIRMED` together (all-or-nothing operation).\n   *\n   * ## Checkout restrictions\n   *\n   * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n   * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n   *\n   * ## Additional updates\n   *\n   * - __Payment status__: Updates if you specify a new `confirmBookingsInfo.paymentStatus`.\n   * - __Customer notifications__: Send messages using `participantNotification`.\n   * - __Revision control__: Requires current revision numbers for all single-service bookings.\n   *\n   * See Confirm Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-booking)) for more details about confirming single-service bookings.\n   * @param - ID of the multi-service booking to confirm its related bookings.\n   */\n  (\n    multiServiceBookingId: string,\n    options?: ConfirmMultiServiceBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      ConfirmMultiServiceBookingResponse,\n      | `multiServiceBooking.bookings`\n      | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n      | `multiServiceBooking.bookings.${number}.booking.status`\n      | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n      | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: ConfirmMultiServiceBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function declineMultiServiceBooking(\n  httpClient: HttpClient\n): DeclineMultiServiceBookingSignature {\n  return (\n    multiServiceBookingId: string,\n    options?: DeclineMultiServiceBookingOptions\n  ) =>\n    universalDeclineMultiServiceBooking(\n      multiServiceBookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface DeclineMultiServiceBookingSignature {\n  /**\n   * Updates the status for all single-service bookings in a multi-service booking to `DECLINED`.\n   *\n   *\n   * Call this method to reject multi-service bookings that can't be accommodated or don't meet business requirements.\n   *\n   * ## Status requirements\n   *\n   * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.\n   * - __Target status__: All bookings move to `DECLINED` together (all-or-nothing operation).\n   *\n   * ## Checkout restrictions\n   *\n   * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n   * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n   *\n   * ## Additional updates\n   *\n   * - __Payment status__: Updates if you specify a new `declineBookingsInfo.paymentStatus`.\n   * - __Customer notifications__: Send messages using `participantNotification`.\n   * - __Revision control__: Requires current revision numbers for all single-service bookings.\n   *\n   * Refer to Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/decline-booking)) for more details about declining single-service bookings.\n   * @param - ID of the multi-service booking to decline.\n   */\n  (\n    multiServiceBookingId: string,\n    options?: DeclineMultiServiceBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      DeclineMultiServiceBookingResponse,\n      | `multiServiceBooking.bookings`\n      | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n      | `multiServiceBooking.bookings.${number}.booking.status`\n      | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n      | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n      | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n      7\n    > & {\n      __applicationErrorsType?: DeclineMultiServiceBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function bulkGetMultiServiceBookingAllowedActions(\n  httpClient: HttpClient\n): BulkGetMultiServiceBookingAllowedActionsSignature {\n  return (multiServiceBookingIds: string[]) =>\n    universalBulkGetMultiServiceBookingAllowedActions(\n      multiServiceBookingIds,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface BulkGetMultiServiceBookingAllowedActionsSignature {\n  /**\n   * Retrieves information about which actions the customer can perform for up to 50 multi-service bookings.\n   *\n   *\n   * For each multi-service booking, the response indicates which actions are currently allowed:\n   * - `cancel`: Whether the customer can cancel the multi-service booking.\n   * - `reschedule`: Whether the customer can adjust the multi-service booking's timing.\n   *\n   * Bear the following considerations in mind when calling this method:\n   *\n   * __Real-time validation__: Wix Bookings calculates allowed actions based on current multi-service booking status, booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)), and available resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) at the time of the call.\n   *\n   * __Permission context__: Depending on the permissions of the calling [identity](https://dev.wix.com/build-apps/develop-your-app/about-identities), you may see different allowed actions for the same multi-service booking. For example, if the identity has permissions to read only part of the multi-service booking, the response indicates which single-service bookings the identity can read.\n   *\n   * __Status dependencies__: Allowed actions change as bookings progress through their lifecycle (`CREATED` → `PENDING` → `CONFIRMED`/`DECLINED` → `CANCELED`).\n   * Bookings can skip `PENDING` and move directly from `CREATED` to `CONFIRMED`/`DECLINED` based on service configuration.\n   * @param - IDs of the multi-service bookings to retrieve allowed actions for.\n   */\n  (multiServiceBookingIds: string[]): Promise<\n    NonNullablePaths<\n      BulkGetMultiServiceBookingAllowedActionsResponse,\n      | `results`\n      | `results.${number}.itemMetadata.originalIndex`\n      | `results.${number}.itemMetadata.success`\n      | `results.${number}.itemMetadata.error.code`\n      | `results.${number}.itemMetadata.error.description`\n      | `results.${number}.item.cancel`\n      | `results.${number}.item.reschedule`\n      | `bulkActionMetadata.totalSuccesses`\n      | `bulkActionMetadata.totalFailures`\n      | `bulkActionMetadata.undetailedFailures`,\n      6\n    >\n  >;\n}\n\nexport function getMultiServiceBooking(\n  httpClient: HttpClient\n): GetMultiServiceBookingSignature {\n  return (multiServiceBookingId: string) =>\n    universalGetMultiServiceBooking(\n      multiServiceBookingId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetMultiServiceBookingSignature {\n  /**\n   * Retrieves a multi-service booking and all its associated single-service bookings.\n   *\n   *\n   * Returns the complete multi-service booking information including its ID, associated single-service bookings, and the total number of scheduled single-service bookings.\n   *\n   * If you call on behalf of an [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) with permissions to read only part of the multi-service booking, only the permitted single-service bookings are retrieved.\n   * The returned total number includes single-service bookings for which you don't have permissions.\n   *\n   * See Query Extended Bookings ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/extended-bookings/query-extended-bookings) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-reader-v2/query-extended-bookings)) for details about retrieving individual single-service bookings and additional metadata.\n   * @param - ID of the multi-service booking.\n   * @returns Retrieved multi-service booking.\n   */\n  (multiServiceBookingId: string): Promise<\n    NonNullablePaths<\n      MultiServiceBooking,\n      | `bookings`\n      | `bookings.${number}.booking.totalParticipants`\n      | `bookings.${number}.booking.status`\n      | `bookings.${number}.booking.paymentStatus`\n      | `bookings.${number}.booking.selectedPaymentOption`\n      | `bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `bookings.${number}.booking.createdBy.memberId`\n      | `bookings.${number}.booking.createdBy.wixUserId`\n      | `bookings.${number}.booking.createdBy.appId`,\n      6\n    >\n  >;\n}\n\nexport function addBookingsToMultiServiceBooking(\n  httpClient: HttpClient\n): AddBookingsToMultiServiceBookingSignature {\n  return (\n    multiServiceBookingId: string,\n    options?: NonNullablePaths<\n      AddBookingsToMultiServiceBookingOptions,\n      | `bookings`\n      | `bookings.${number}.bookingId`\n      | `bookings.${number}.revision`,\n      4\n    >\n  ) =>\n    universalAddBookingsToMultiServiceBooking(\n      multiServiceBookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface AddBookingsToMultiServiceBookingSignature {\n  /**\n   * Expands an existing multi-service booking by adding existing single-service bookings to the package.\n   *\n   *\n   * ## When to call this method\n   *\n   * Call this method to add 1 or more existing single-service bookings to an existing multi-service booking.\n   * For creating a new multi-service booking with new single-service bookings, call Create Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-multi-service-booking)) instead.\n   *\n   * ## Package integration\n   *\n   * The timing of the single-service bookings to add must be compatible with the multi-service booking's sequential schedule to ensure no gaps or overlaps between services in the updated package.\n   *\n   * ## Requirements and limitations\n   *\n   * - __Maximum capacity__: The total number of single-service bookings can't exceed 8.\n   * - __Booking eligibility__: You can add only independent single-service bookings that aren't part of another multi-service booking.\n   * - __Status compatibility__: Added bookings must have compatible status with the target multi-service booking.\n   * - __Revision control__: You must provide current revision numbers for all single-service bookings to add.\n   * @param - ID of the multi-service booking.\n   */\n  (\n    multiServiceBookingId: string,\n    options?: NonNullablePaths<\n      AddBookingsToMultiServiceBookingOptions,\n      | `bookings`\n      | `bookings.${number}.bookingId`\n      | `bookings.${number}.revision`,\n      4\n    >\n  ): Promise<\n    NonNullablePaths<\n      AddBookingsToMultiServiceBookingResponse,\n      | `bookings`\n      | `bookings.${number}.booking.totalParticipants`\n      | `bookings.${number}.booking.status`\n      | `bookings.${number}.booking.paymentStatus`\n      | `bookings.${number}.booking.selectedPaymentOption`\n      | `bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `bookings.${number}.booking.createdBy.memberId`\n      | `bookings.${number}.booking.createdBy.wixUserId`\n      | `bookings.${number}.booking.createdBy.appId`,\n      6\n    > & {\n      __applicationErrorsType?: AddBookingsToMultiServiceBookingApplicationErrors;\n    }\n  >;\n}\n\nexport function removeBookingsFromMultiServiceBooking(\n  httpClient: HttpClient\n): RemoveBookingsFromMultiServiceBookingSignature {\n  return (\n    multiServiceBookingId: string,\n    options?: RemoveBookingsFromMultiServiceBookingOptions\n  ) =>\n    universalRemoveBookingsFromMultiServiceBooking(\n      multiServiceBookingId,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface RemoveBookingsFromMultiServiceBookingSignature {\n  /**\n   * Removes single-service bookings from a multi-service booking and converts them to independent single-service bookings.\n   *\n   *\n   * ## Removal options\n   *\n   * __Remove all permitted bookings__: If you specify an empty `bookings` array, all single-service bookings for which the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has read permissions are removed from the multi-service booking.\n   *\n   * __Selective removal__: Specify single-service booking IDs and revisions to remove only specific single-service bookings from the package.\n   *\n   * __Sequential scheduling__: To maintain sequential scheduling, remove only first or last single-service bookings. For middle bookings, first reschedule all relevant single-service bookings to eliminate gaps. To do so, call Reschedule Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-multi-service-booking)) before removing the unwanted bookings.\n   *\n   * ## Removal behavior\n   *\n   * __Independent bookings__: Removed single-service bookings become independent bookings.\n   * You can manage them using single-service booking methods.\n   *\n   * __Automatic cleanup__: Multi-service bookings must contain at least 2 services.\n   * If removal results in only 1 remaining single-service booking for the multi-service booking, the entire multi-service booking is deleted and the remaining single-service booking becomes a standalone booking.\n   *\n   * __Revision control__: Specify current revision numbers to prevent conflicting modifications during the removal process.\n   * @param - ID of the multi-service booking.\n   */\n  (\n    multiServiceBookingId: string,\n    options?: RemoveBookingsFromMultiServiceBookingOptions\n  ): Promise<\n    NonNullablePaths<\n      RemoveBookingsFromMultiServiceBookingResponse,\n      | `bookings`\n      | `bookings.${number}.booking.totalParticipants`\n      | `bookings.${number}.booking.status`\n      | `bookings.${number}.booking.paymentStatus`\n      | `bookings.${number}.booking.selectedPaymentOption`\n      | `bookings.${number}.booking.createdBy.anonymousVisitorId`\n      | `bookings.${number}.booking.createdBy.memberId`\n      | `bookings.${number}.booking.createdBy.wixUserId`\n      | `bookings.${number}.booking.createdBy.appId`,\n      6\n    > & {\n      __applicationErrorsType?: RemoveBookingsFromMultiServiceBookingApplicationErrors;\n    }\n  >;\n}\n\nexport const onBookingCanceled = EventDefinition(\n  'wix.bookings.v2.booking_canceled',\n  true,\n  (event: BookingCanceledEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'data.booking.createdDate' },\n            { path: 'data.booking.startDate' },\n            { path: 'data.booking.endDate' },\n            { path: 'data.booking.updatedDate' },\n            { path: 'data.booking.canceledDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n        {\n          transformFn: transformRESTFloatToSDKFloat,\n          paths: [\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.latitude',\n            },\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.longitude',\n            },\n          ],\n        },\n      ])\n    )\n)<BookingCanceledEnvelope>();\nexport const onBookingConfirmed = EventDefinition(\n  'wix.bookings.v2.booking_confirmed',\n  true,\n  (event: BookingConfirmedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'data.booking.createdDate' },\n            { path: 'data.booking.startDate' },\n            { path: 'data.booking.endDate' },\n            { path: 'data.booking.updatedDate' },\n            { path: 'data.booking.canceledDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n        {\n          transformFn: transformRESTFloatToSDKFloat,\n          paths: [\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.latitude',\n            },\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.longitude',\n            },\n          ],\n        },\n      ])\n    )\n)<BookingConfirmedEnvelope>();\nexport const onBookingCreated = EventDefinition(\n  'wix.bookings.v2.booking_created',\n  true,\n  (event: BookingCreatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.startDate' },\n            { path: 'entity.endDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'entity.canceledDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n        {\n          transformFn: transformRESTFloatToSDKFloat,\n          paths: [\n            { path: 'entity.contactDetails.fullAddress.geocode.latitude' },\n            { path: 'entity.contactDetails.fullAddress.geocode.longitude' },\n          ],\n        },\n      ])\n    )\n)<BookingCreatedEnvelope>();\nexport const onBookingDeclined = EventDefinition(\n  'wix.bookings.v2.booking_declined',\n  true,\n  (event: BookingDeclinedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'data.booking.createdDate' },\n            { path: 'data.booking.startDate' },\n            { path: 'data.booking.endDate' },\n            { path: 'data.booking.updatedDate' },\n            { path: 'data.booking.canceledDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n        {\n          transformFn: transformRESTFloatToSDKFloat,\n          paths: [\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.latitude',\n            },\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.longitude',\n            },\n          ],\n        },\n      ])\n    )\n)<BookingDeclinedEnvelope>();\nexport const onBookingNumberOfParticipantsUpdated = EventDefinition(\n  'wix.bookings.v2.booking_number_of_participants_updated',\n  true,\n  (event: BookingNumberOfParticipantsUpdatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'data.booking.createdDate' },\n            { path: 'data.booking.startDate' },\n            { path: 'data.booking.endDate' },\n            { path: 'data.booking.updatedDate' },\n            { path: 'data.booking.canceledDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n        {\n          transformFn: transformRESTFloatToSDKFloat,\n          paths: [\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.latitude',\n            },\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.longitude',\n            },\n          ],\n        },\n      ])\n    )\n)<BookingNumberOfParticipantsUpdatedEnvelope>();\nexport const onBookingRescheduled = EventDefinition(\n  'wix.bookings.v2.booking_rescheduled',\n  true,\n  (event: BookingRescheduledEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'data.previousStartDate' },\n            { path: 'data.previousEndDate' },\n            { path: 'data.booking.createdDate' },\n            { path: 'data.booking.startDate' },\n            { path: 'data.booking.endDate' },\n            { path: 'data.booking.updatedDate' },\n            { path: 'data.booking.canceledDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n        {\n          transformFn: transformRESTFloatToSDKFloat,\n          paths: [\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.latitude',\n            },\n            {\n              path: 'data.booking.contactDetails.fullAddress.geocode.longitude',\n            },\n          ],\n        },\n      ])\n    )\n)<BookingRescheduledEnvelope>();\nexport const onBookingUpdated = EventDefinition(\n  'wix.bookings.v2.booking_updated',\n  true,\n  (event: BookingUpdatedEnvelope) =>\n    renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(event, [\n        {\n          transformFn: transformRESTTimestampToSDKTimestamp,\n          paths: [\n            { path: 'entity.createdDate' },\n            { path: 'entity.startDate' },\n            { path: 'entity.endDate' },\n            { path: 'entity.updatedDate' },\n            { path: 'entity.canceledDate' },\n            { path: 'metadata.eventTime' },\n          ],\n        },\n        {\n          transformFn: transformRESTFloatToSDKFloat,\n          paths: [\n            { path: 'entity.contactDetails.fullAddress.geocode.latitude' },\n            { path: 'entity.contactDetails.fullAddress.geocode.longitude' },\n          ],\n        },\n      ])\n    )\n)<BookingUpdatedEnvelope>();\n\nexport {\n  ActionEvent,\n  Actor,\n  AddBookingsToMultiServiceBookingOptions,\n  AddBookingsToMultiServiceBookingRequest,\n  AddBookingsToMultiServiceBookingResponse,\n  Address,\n  AddressLocation,\n  AddressStreetOneOf,\n  AllowedActions,\n  ApplicationError,\n  AvailableResources,\n  BaseEventMetadata,\n  BookedAddOn,\n  BookedEntity,\n  BookedEntityItemOneOf,\n  BookedResource,\n  BookedSchedule,\n  BookedSlot,\n  Booking,\n  BookingCanceled,\n  BookingCanceledEnvelope,\n  BookingChanged,\n  BookingConfirmed,\n  BookingConfirmedEnvelope,\n  BookingCreatedEnvelope,\n  BookingDeclined,\n  BookingDeclinedEnvelope,\n  BookingDetails,\n  BookingFormFilled,\n  BookingIdAndRevision,\n  BookingInfo,\n  BookingMarkedAsPending,\n  BookingNumberOfParticipantsUpdatedEnvelope,\n  BookingParticipantsInfoOneOf,\n  BookingPolicySettings,\n  BookingPolicyViolations,\n  BookingRescheduled,\n  BookingRescheduledEnvelope,\n  BookingRescheduledPreviousParticipantsInfoOneOf,\n  BookingResult,\n  BookingSource,\n  BookingStatus,\n  BookingUpdatedEnvelope,\n  BulkActionMetadata,\n  BulkBookingResult,\n  BulkCalculateAllowedActionsRequest,\n  BulkCalculateAllowedActionsResponse,\n  BulkCalculateAllowedActionsResult,\n  BulkConfirmOrDeclineBookingOptions,\n  BulkConfirmOrDeclineBookingRequest,\n  BulkConfirmOrDeclineBookingRequestBookingDetails,\n  BulkConfirmOrDeclineBookingResponse,\n  BulkCreateBookingOptions,\n  BulkCreateBookingRequest,\n  BulkCreateBookingResponse,\n  BulkGetMultiServiceBookingAllowedActionsRequest,\n  BulkGetMultiServiceBookingAllowedActionsResponse,\n  BulkRescheduleBookingRequest,\n  BulkRescheduleBookingRequestBooking,\n  BulkRescheduleBookingResponse,\n  BulkUpdateBookedScheduleRequest,\n  BulkUpdateBookedScheduleResponse,\n  BulkUpdateBookingRequest,\n  BulkUpdateBookingResponse,\n  CancelBookingFlowControlSettings,\n  CancelBookingOptions,\n  CancelBookingRequest,\n  CancelBookingRequestFlowControlSettings,\n  CancelBookingResponse,\n  CancelMultiServiceBookingOptions,\n  CancelMultiServiceBookingRequest,\n  CancelMultiServiceBookingResponse,\n  Clash,\n  CommonIdentificationData,\n  CommonIdentificationDataIdOneOf,\n  ConfirmBookingFlowControlSettings,\n  ConfirmBookingOptions,\n  ConfirmBookingRequest,\n  ConfirmBookingResponse,\n  ConfirmMultiServiceBookingOptions,\n  ConfirmMultiServiceBookingRequest,\n  ConfirmMultiServiceBookingResponse,\n  ConfirmOrDeclineBookingOptions,\n  ConfirmOrDeclineBookingRequest,\n  ConfirmOrDeclineBookingResponse,\n  ConfirmRequest,\n  ConfirmResponse,\n  ConsistentQueryBookingsRequest,\n  ConsistentQueryBookingsResponse,\n  ContactDetails,\n  CountBookingsRequest,\n  CountBookingsResponse,\n  CreateBookingFlowControlSettings,\n  CreateBookingInfo,\n  CreateBookingOptions,\n  CreateBookingRequest,\n  CreateBookingRequestFlowControlSettings,\n  CreateBookingResponse,\n  CreateMultiServiceBookingOptions,\n  CreateMultiServiceBookingRequest,\n  CreateMultiServiceBookingResponse,\n  CursorPaging,\n  Cursors,\n  CustomFormField,\n  DeclineBookingFlowControlSettings,\n  DeclineBookingOptions,\n  DeclineBookingRequest,\n  DeclineBookingResponse,\n  DeclineMultiServiceBookingOptions,\n  DeclineMultiServiceBookingRequest,\n  DeclineMultiServiceBookingResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  Duration,\n  EntityCreatedEvent,\n  EntityDeletedEvent,\n  EntityUpdatedEvent,\n  EventMetadata,\n  ExtendedFields,\n  FlowControlSettings,\n  GetMultiServiceBookingAvailabilityRequest,\n  GetMultiServiceBookingAvailabilityResponse,\n  GetMultiServiceBookingAvailabilityResponseBookingInfo,\n  GetMultiServiceBookingRequest,\n  GetMultiServiceBookingResponse,\n  GetScheduleAvailabilityRequest,\n  GetScheduleAvailabilityResponse,\n  GetSlotAvailabilityRequest,\n  GetSlotAvailabilityResponse,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  IdentificationDataIdentityType,\n  IdentityType,\n  ItemMetadata,\n  LegacyCreateBookingRequest,\n  LegacyCreateBookingResponse,\n  Location,\n  LocationLocationType,\n  LocationType,\n  MarkBookingAsPendingFlowControlSettings,\n  MarkBookingAsPendingOptions,\n  MarkBookingAsPendingRequest,\n  MarkBookingAsPendingResponse,\n  MarkMultiServiceBookingAsPendingOptions,\n  MarkMultiServiceBookingAsPendingRequest,\n  MarkMultiServiceBookingAsPendingResponse,\n  MaskedBooking,\n  MessageEnvelope,\n  MigrationCheckIfClashesWithBlockedTimeRequest,\n  MigrationCheckIfClashesWithBlockedTimeResponse,\n  MsidAndBookingId,\n  MultiServiceBooking,\n  MultiServiceBookingInfo,\n  MultiServiceBookingMetadata,\n  MultiServiceBookingType,\n  NumberOfParticipantsUpdated,\n  NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf,\n  Paging,\n  PagingMetadataV2,\n  ParticipantChoices,\n  ParticipantNotification,\n  PaymentStatus,\n  Platform,\n  QueryBookingsRequest,\n  QueryBookingsResponse,\n  QueryV2,\n  QueryV2PagingMethodOneOf,\n  RemoveBookingsFromMultiServiceBookingOptions,\n  RemoveBookingsFromMultiServiceBookingRequest,\n  RemoveBookingsFromMultiServiceBookingResponse,\n  RescheduleBookingFlowControlSettings,\n  RescheduleBookingInfo,\n  RescheduleBookingInfoParticipantsInfoOneOf,\n  RescheduleBookingOptions,\n  RescheduleBookingOptionsParticipantsInfoOneOf,\n  RescheduleBookingRequest,\n  RescheduleBookingRequestFlowControlSettings,\n  RescheduleBookingRequestParticipantsInfoOneOf,\n  RescheduleBookingResponse,\n  RescheduleMultiServiceBookingOptions,\n  RescheduleMultiServiceBookingRequest,\n  RescheduleMultiServiceBookingResponse,\n  RestoreInfo,\n  ScheduleAvailability,\n  SelectedPaymentOption,\n  ServiceChoice,\n  ServiceChoiceChoiceOneOf,\n  ServiceChoices,\n  SetBookingFormAndSubmissionIdRequest,\n  SetBookingFormAndSubmissionIdRequestCreatedByOneOf,\n  SetBookingFormAndSubmissionIdResponse,\n  SetBookingSessionIdRequest,\n  SetBookingSessionIdResponse,\n  SetBookingSubmissionIdRequest,\n  SetBookingSubmissionIdResponse,\n  Slot,\n  SlotAvailability,\n  SlotBookings,\n  SlotLocation,\n  SlotResource,\n  SlotSlotResource,\n  SortOrder,\n  Sorting,\n  StreetAddress,\n  Subdivision,\n  UpdateBookingRequest,\n  UpdateBookingResponse,\n  UpdateExtendedFieldsOptions,\n  UpdateExtendedFieldsRequest,\n  UpdateExtendedFieldsResponse,\n  UpdateNumberOfParticipantsOptions,\n  UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf,\n  UpdateNumberOfParticipantsRequest,\n  UpdateNumberOfParticipantsRequestParticipantsInfoOneOf,\n  UpdateNumberOfParticipantsResponse,\n  V2CancelBookingRequest,\n  V2CancelBookingResponse,\n  V2ConfirmBookingRequest,\n  V2ConfirmBookingResponse,\n  V2CreateBookingRequest,\n  V2CreateBookingRequestBookableItemOneOf,\n  V2CreateBookingRequestParticipantsInfoOneOf,\n  V2CreateBookingResponse,\n  V2DeclineBookingRequest,\n  V2DeclineBookingResponse,\n  V2RescheduleBookingRequest,\n  V2RescheduleBookingRequestParticipantsInfoOneOf,\n  V2RescheduleBookingResponse,\n  V2Slot,\n  V2UpdateNumberOfParticipantsRequest,\n  V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf,\n  V2UpdateNumberOfParticipantsResponse,\n  ValueType,\n  WaitingList,\n  WebhookIdentityType,\n  WebhooksIdentificationData,\n  WebhooksIdentificationDataIdOneOf,\n} from './bookings-v2-booking-bookings.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsV2Booking from './bookings-v2-booking-bookings.http.js';\n// @ts-ignore\n\n/** An entity representing a scheduled appointment, class session, or course. */\nexport interface Booking extends BookingParticipantsInfoOneOf {\n  /**\n   * Total number of participants.\n   * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the booked service choices and participant count for each choice.\n   * When creating a booking, use this field only if the booking includes multiple service variants\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   *\n   * For example, use this for a spa package booking that includes different service levels:\n   * - 2 participants chose \"Standard Package\".\n   * - 1 participant chose \"VIP Package\".\n   */\n  participantsChoices?: ParticipantChoices;\n  /**\n   * Booking ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * An object describing the bookable entity - either a specific time slot or a recurring schedule.\n   *\n   * The structure depends on the type of service being booked:\n   *\n   * *For appointment services:** Use `slot` to book a specific time slot with a\n   * service provider. Appointments are typically one-time sessions at a specific date and time.\n   *\n   * *For class services:** Use `slot` to book a specific class session. Classes\n   * are individual sessions that can have multiple participants.\n   *\n   * *For course services:** Use `schedule` to book an entire course consisting of\n   * multiple sessions over time. Courses are recurring, multi-session offerings.\n   *\n   * Choose the appropriate field based on your service type and booking requirements.\n   */\n  bookedEntity?: BookedEntity;\n  /**\n   * Contact details of the site visitor or member\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/members/members/introduction) | [REST](https://dev.wix.com/docs/rest/crm/members-contacts/members/members/introduction))\n   * making the booking.\n   */\n  contactDetails?: ContactDetails;\n  /** Additional custom fields submitted with the booking form. */\n  additionalFields?: CustomFormField[];\n  /**\n   * Booking status. A booking is automatically confirmed if the service allows it\n   * and an eCommerce order is created. It is automatically declined if there is a\n   * double booking and the customer hasn't paid or is eligible for an automatic\n   * refund. Currently, only payments with pricing plans are automatically refundable.\n   */\n  status?: BookingStatusWithLiterals;\n  /**\n   * The payment status of the booking. This field automatically syncs with the\n   * `paymentStatus` of the corresponding eCommerce order\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup)| [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))\n   * when customers use Wix eCommerce checkout.\n   *\n   * ## Integration patterns\n   *\n   * *When using Wix eCommerce checkout:** Wix Bookings automatically syncs the payment status based on the eCommerce order's payment status.\n   * Do not manually update this field.\n   *\n   * *When using custom payment flows:** You can manually update the payment status with Confirm Booking or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) to reflect the customer's payment state.\n   *\n   * *For membership/pricing plan payments:** Wix Bookings automatically manages the payment status when customers pay with an active pricing plan ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/introduction)) subscription.\n   *\n   * All payment statuses are supported for every booking `status`.\n   */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /**\n   * Payment option selected by the customer. If the customer hasn't completed their checkout, they may still change the payment method. Must be one of the payment options offered by the service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)), unless `skipSelectedPaymentOptionValidation` is `true`.\n   *\n   * When undefined during an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)), Wix Bookings uses the service's default payment option\n   */\n  selectedPaymentOption?: SelectedPaymentOptionWithLiterals;\n  /**\n   * Date and time the booking was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /** External user ID that you can provide. */\n  externalUserId?: string | null;\n  /**\n   * Revision number to be used when updating, rescheduling, or cancelling the booking.\n   * Increments by 1 each time the booking is updated, rescheduled, or canceled. To prevent conflicting changes, the current revision must be specified when updating the booking.\n   */\n  revision?: string | null;\n  /**\n   * ID of the creator of the booking.\n   * If `appId` and another ID are present, the other ID takes precedence.\n   * @readonly\n   */\n  createdBy?: CommonIdentificationData;\n  /**\n   * The start date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * For a slot, this is the start date of the slot. For a schedule, this is the start date of the first session.\n   * @readonly\n   */\n  startDate?: Date | null;\n  /**\n   * The end date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * For a slot, this is the end date of the slot. For a schedule, this is the end date of the last session.\n   * @readonly\n   */\n  endDate?: Date | null;\n  /**\n   * Date and time the booking was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Custom field data for this object.\n   * Extended fields must be configured in the app dashboard before they can be accessed with API calls.\n   */\n  extendedFields?: ExtendedFields;\n  /**\n   * Whether this booking overlaps with another confirmed booking. Returned\n   * only if set to `true`.\n   * @readonly\n   */\n  doubleBooked?: boolean | null;\n}\n\n/** @oneof */\nexport interface BookingParticipantsInfoOneOf {\n  /**\n   * Total number of participants.\n   * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the booked service choices and participant count for each choice.\n   * When creating a booking, use this field only if the booking includes multiple service variants\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   *\n   * For example, use this for a spa package booking that includes different service levels:\n   * - 2 participants chose \"Standard Package\".\n   * - 1 participant chose \"VIP Package\".\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\n/**\n * A multi-service booking is considered available if all single-service bookings are available as returned from List Multi Service Availability Time Slots.\n * Currently, `SEPARATE_BOOKINGS` and `PARALLEL_BOOKINGS` are not supported.\n * Multi-service booking is available if each of its bookings is available separately.\n * For `SEQUENTIAL_BOOKINGS`, see `List Multi Service Availability Time Slots` documentation.\n */\nexport enum MultiServiceBookingType {\n  /** You must schedule single-service bookings back-to-back with each booking starting when the previous booking ends */\n  SEQUENTIAL_BOOKINGS = 'SEQUENTIAL_BOOKINGS',\n  /** Not currently supported. */\n  SEPARATE_BOOKINGS = 'SEPARATE_BOOKINGS',\n  /** Not currently supported. */\n  PARALLEL_BOOKINGS = 'PARALLEL_BOOKINGS',\n}\n\n/** @enumType */\nexport type MultiServiceBookingTypeWithLiterals =\n  | MultiServiceBookingType\n  | 'SEQUENTIAL_BOOKINGS'\n  | 'SEPARATE_BOOKINGS'\n  | 'PARALLEL_BOOKINGS';\n\nexport interface BookedEntity extends BookedEntityItemOneOf {\n  /**\n   * Booked slot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).\n   *\n   * Specify `slot` when creating bookings for:\n   * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).\n   * Wix Bookings creates a new session when the booking is confirmed.\n   * - **Class services:** Group sessions at specific times (fitness classes, workshops).\n   * Wix Bookings links the booking to an existing scheduled session.\n   *\n   * For course services, specify `schedule` instead of `slot`.\n   */\n  slot?: BookedSlot;\n  /**\n   * Booked schedule\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   *\n   * Specify `schedule` when creating bookings for:\n   * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).\n   * Wix Bookings enrolls participants in all sessions defined by the course schedule.\n   */\n  schedule?: BookedSchedule;\n  /**\n   * Session title at the time of booking. If there is no pre-existing session,\n   * for example for appointment-based services, Wix Bookings sets `title` to the service name.\n   * @readonly\n   * @maxLength 6000\n   */\n  title?: string | null;\n  /**\n   * List of tags for the booking.\n   *\n   * - \"INDIVIDUAL\": For bookings of appointment-based services. Including when the appointment is for a group of participants.\n   * - \"GROUP\": For bookings of individual class sessions.\n   * - \"COURSE\": For course bookings.\n   */\n  tags?: string[] | null;\n}\n\n/** @oneof */\nexport interface BookedEntityItemOneOf {\n  /**\n   * Booked slot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).\n   *\n   * Specify `slot` when creating bookings for:\n   * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).\n   * Wix Bookings creates a new session when the booking is confirmed.\n   * - **Class services:** Group sessions at specific times (fitness classes, workshops).\n   * Wix Bookings links the booking to an existing scheduled session.\n   *\n   * For course services, specify `schedule` instead of `slot`.\n   */\n  slot?: BookedSlot;\n  /**\n   * Booked schedule\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).\n   *\n   * Specify `schedule` when creating bookings for:\n   * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).\n   * Wix Bookings enrolls participants in all sessions defined by the course schedule.\n   */\n  schedule?: BookedSchedule;\n}\n\nexport interface BookedSlot {\n  /** Session ID. */\n  sessionId?: string | null;\n  /** Service ID. */\n  serviceId?: string;\n  /** Schedule ID. */\n  scheduleId?: string;\n  /**\n   * ID of the corresponding event\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).\n   * Available for both appointment and class bookings, not available for course bookings.\n   * For appointment-based services, Wix Bookings automatically populates `eventId` when the booking `status` changes to `CONFIRMED`.\n   * For class bookings, Wix Bookings automatically populates `eventId` upon booking creation.\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n  /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  startDate?: string | null;\n  /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  endDate?: string | null;\n  /** The time zone according to which the slot was shown to the user when booking, and should be shown in the future. */\n  timezone?: string | null;\n  /**\n   * Primary resource\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) for the booking.\n   * For example, the staff member ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) providing the service.\n   */\n  resource?: BookedResource;\n  /** Location where the session takes place. */\n  location?: Location;\n}\n\nexport interface BookedResource {\n  /**\n   * ID of the booking's primary resource.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Resource's name at the time of booking.\n   * @maxLength 40\n   */\n  name?: string | null;\n  /**\n   * Resource's email at the time of booking.\n   * @maxLength 500\n   */\n  email?: string | null;\n  /**\n   * ID of the schedule belonging to the booking's primary resource.\n   * @format GUID\n   */\n  scheduleId?: string | null;\n}\n\nexport interface Location {\n  /**\n   * Business location ID. Available only for locations that are business locations,\n   * meaning the `location_type` is `\"OWNER_BUSINESS\"`.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Location name. */\n  name?: string | null;\n  /** The full address of this location. */\n  formattedAddress?: string | null;\n  /**\n   * The full translated address of this location.\n   * @maxLength 512\n   */\n  formattedAddressTranslated?: string | null;\n  /**\n   * Location type.\n   *\n   * - `\"OWNER_BUSINESS\"`: The business address, as set in the site’s general settings.\n   * - `\"OWNER_CUSTOM\"`: The address as set when creating the service.\n   * - `\"CUSTOM\"`: The address as set for the individual session.\n   */\n  locationType?: LocationTypeWithLiterals;\n}\n\nexport enum LocationType {\n  UNDEFINED = 'UNDEFINED',\n  OWNER_BUSINESS = 'OWNER_BUSINESS',\n  OWNER_CUSTOM = 'OWNER_CUSTOM',\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNDEFINED'\n  | 'OWNER_BUSINESS'\n  | 'OWNER_CUSTOM'\n  | 'CUSTOM';\n\nexport interface BookedSchedule {\n  /** Schedule ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)). */\n  scheduleId?: string;\n  /**\n   * Booked service ID.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * Location ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)) where the schedule's sessions take place.\n   * @readonly\n   */\n  location?: Location;\n  /**\n   * Time zone in which the slot or session was shown to the customer when they booked.\n   * Also used whenever the customer reviews the booking's timing in the future.\n   */\n  timezone?: string | null;\n  /**\n   * Start time of the first session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.\n   * @readonly\n   */\n  firstSessionStart?: string | null;\n  /**\n   * End time of the last session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.\n   * @readonly\n   */\n  lastSessionEnd?: string | null;\n}\n\nexport interface ContactDetails {\n  /**\n   * Contact ID.\n   * @format GUID\n   */\n  contactId?: string | null;\n  /**\n   * Contact's first name. When populated from a standard booking form, this\n   * property corresponds to the `name` field.\n   */\n  firstName?: string | null;\n  /** Contact's last name. */\n  lastName?: string | null;\n  /**\n   * Contact's email. If no [contact](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/contacts/contact-v4/contact-object)\n   * with this email exist, a new contact is created.\n   * Used to validate coupon usage limitations per contact. If not specified,\n   * the coupon usage limitation will not be enforced. (Coupon usage limitation\n   * validation is not supported yet).\n   * @format EMAIL\n   */\n  email?: string | null;\n  /** Contact's phone number. */\n  phone?: string | null;\n  /** Contact's full address. */\n  fullAddress?: Address;\n  /**\n   * Contact's time zone.\n   * @deprecated\n   */\n  timeZone?: string | null;\n  /**\n   * Contact's country in [ISO 3166-1 alpha-2 code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)\n   * format.\n   * @format COUNTRY\n   */\n  countryCode?: string | null;\n}\n\n/** Physical address */\nexport interface Address extends AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n  /**\n   * Country code.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  subdivision?: string | null;\n  /** City name. */\n  city?: string | null;\n  /** Zip/postal code. */\n  postalCode?: string | null;\n  /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */\n  addressLine2?: string | null;\n  /** A string containing the full address of this location. */\n  formattedAddress?: string | null;\n  /** Free text to help find the address. */\n  hint?: string | null;\n  /** Coordinates of the physical address. */\n  geocode?: AddressLocation;\n  /** Country full name. */\n  countryFullname?: string | null;\n  /** Multi-level subdivisions from top to bottom. */\n  subdivisions?: Subdivision[];\n}\n\n/** @oneof */\nexport interface AddressStreetOneOf {\n  /** Street name, number and apartment number. */\n  streetAddress?: StreetAddress;\n  /** Main address line, usually street and number, as free text. */\n  addressLine?: string | null;\n}\n\nexport interface StreetAddress {\n  /** Street number. */\n  number?: string;\n  /** Street name. */\n  name?: string;\n  /** Apartment number. */\n  apt?: string;\n}\n\nexport interface AddressLocation {\n  /** Address latitude. */\n  latitude?: number | null;\n  /** Address longitude. */\n  longitude?: number | null;\n}\n\nexport interface Subdivision {\n  /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */\n  code?: string;\n  /** Subdivision full name. */\n  name?: string;\n}\n\nexport interface CustomFormField {\n  /**\n   * ID of the form field as defined in the form.\n   * @format GUID\n   */\n  _id?: string;\n  /** Value that was submitted for this field. */\n  value?: string | null;\n  /**\n   * Form field's label at the time of submission.\n   * @readonly\n   */\n  label?: string | null;\n  valueType?: ValueTypeWithLiterals;\n}\n\nexport enum ValueType {\n  /** Short text. This is the default value type. */\n  SHORT_TEXT = 'SHORT_TEXT',\n  /** Long text. */\n  LONG_TEXT = 'LONG_TEXT',\n  /** A text that represents the check box value. If selected the value is `true`, otherwise `false`. */\n  CHECK_BOX = 'CHECK_BOX',\n}\n\n/** @enumType */\nexport type ValueTypeWithLiterals =\n  | ValueType\n  | 'SHORT_TEXT'\n  | 'LONG_TEXT'\n  | 'CHECK_BOX';\n\n/** Booking status. */\nexport enum BookingStatus {\n  /** The booking was created, but the customer hasn't completed the related eCommerce order yet. */\n  CREATED = 'CREATED',\n  /**\n   * The merchant has confirmed the booking and it appears in the business calendar. Merchants can set up their services\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) to automatically confirm all `PENDING` bookings.\n   */\n  CONFIRMED = 'CONFIRMED',\n  /**\n   * The customer has canceled the booking. Depending on the relevant service's policy snapshot\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))\n   * they may have to pay a cancellation fee ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction)).\n   */\n  CANCELED = 'CANCELED',\n  /** The merchant must manually confirm the booking before it appears in the business calendar. */\n  PENDING = 'PENDING',\n  /** The merchant has declined the booking before the customer was charged. */\n  DECLINED = 'DECLINED',\n  /**\n   * The booking is on a waitlist.\n   * Currently, you can't call Register to Waitlist ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/waitlist/register-to-waitlist)) for course or appointment bookings, even though this is supported in live sites.\n   * You can call Register to Waitlist only for class session bookings.\n   */\n  WAITING_LIST = 'WAITING_LIST',\n}\n\n/** @enumType */\nexport type BookingStatusWithLiterals =\n  | BookingStatus\n  | 'CREATED'\n  | 'CONFIRMED'\n  | 'CANCELED'\n  | 'PENDING'\n  | 'DECLINED'\n  | 'WAITING_LIST';\n\n/**\n * Payment status.\n * Automatically updated when using eCommerce checkout APIs.\n */\nexport enum PaymentStatus {\n  /** Undefined payment status. */\n  UNDEFINED = 'UNDEFINED',\n  /** The booking isn't paid. */\n  NOT_PAID = 'NOT_PAID',\n  /** The booking is fully paid. */\n  PAID = 'PAID',\n  /** The booking is partially paid. */\n  PARTIALLY_PAID = 'PARTIALLY_PAID',\n  /** The booking is refunded. */\n  REFUNDED = 'REFUNDED',\n  /** The booking is free of charge. */\n  EXEMPT = 'EXEMPT',\n}\n\n/** @enumType */\nexport type PaymentStatusWithLiterals =\n  | PaymentStatus\n  | 'UNDEFINED'\n  | 'NOT_PAID'\n  | 'PAID'\n  | 'PARTIALLY_PAID'\n  | 'REFUNDED'\n  | 'EXEMPT';\n\n/**\n * Selected payment option.\n *\n * One of the payment options offered by the service.\n * This field is set when the user selects an option during booking.\n * If left undefined, the payment option is resolved by the service configuration on checkout.\n */\nexport enum SelectedPaymentOption {\n  /** Undefined payment option. */\n  UNDEFINED = 'UNDEFINED',\n  /** Offline payment. */\n  OFFLINE = 'OFFLINE',\n  /** Online payment. */\n  ONLINE = 'ONLINE',\n  /** Payment using a Wix Pricing Plan. */\n  MEMBERSHIP = 'MEMBERSHIP',\n  /**\n   * Customers can pay only in person with a Wix Pricing Plan, while the Wix user\n   * must manually redeem the pricing plan in the dashboard.\n   */\n  MEMBERSHIP_OFFLINE = 'MEMBERSHIP_OFFLINE',\n}\n\n/** @enumType */\nexport type SelectedPaymentOptionWithLiterals =\n  | SelectedPaymentOption\n  | 'UNDEFINED'\n  | 'OFFLINE'\n  | 'ONLINE'\n  | 'MEMBERSHIP'\n  | 'MEMBERSHIP_OFFLINE';\n\nexport interface BookingSource {\n  /** Platform from which a booking was created. */\n  platform?: PlatformWithLiterals;\n  /** Actor that created this booking. */\n  actor?: ActorWithLiterals;\n  /**\n   * Wix site ID of the application that created the booking.\n   * @format GUID\n   * @readonly\n   */\n  appDefId?: string | null;\n  /**\n   * Name of the application that created the booking, as saved in Wix Developers Center at the time of booking.\n   * @readonly\n   */\n  appName?: string | null;\n}\n\nexport enum Platform {\n  UNDEFINED_PLATFORM = 'UNDEFINED_PLATFORM',\n  WEB = 'WEB',\n  MOBILE_APP = 'MOBILE_APP',\n}\n\n/** @enumType */\nexport type PlatformWithLiterals =\n  | Platform\n  | 'UNDEFINED_PLATFORM'\n  | 'WEB'\n  | 'MOBILE_APP';\n\nexport enum Actor {\n  UNDEFINED_ACTOR = 'UNDEFINED_ACTOR',\n  BUSINESS = 'BUSINESS',\n  CUSTOMER = 'CUSTOMER',\n}\n\n/** @enumType */\nexport type ActorWithLiterals =\n  | Actor\n  | 'UNDEFINED_ACTOR'\n  | 'BUSINESS'\n  | 'CUSTOMER';\n\nexport interface ParticipantNotification {\n  /**\n   * Whether to send a message about the changes to the customer.\n   *\n   * Default: `false`\n   */\n  notifyParticipants?: boolean;\n  /** Custom message to send to the participants about the changes to the booking. */\n  message?: string | null;\n}\n\nexport interface CommonIdentificationData\n  extends CommonIdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /**\n   * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.\n   * @format GUID\n   */\n  contactId?: string | null;\n}\n\n/** @oneof */\nexport interface CommonIdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum IdentificationDataIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type IdentificationDataIdentityTypeWithLiterals =\n  | IdentificationDataIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\n/**\n * Settings that control booking flow behavior and override default business rules.\n *\n * These settings allow administrators to bypass standard validation checks\n * and policies when creating, confirming, rescheduling, or canceling bookings.\n * Most settings require elevated permissions to use.\n *\n * Use flow control settings to handle special scenarios like:\n * - Emergency bookings outside normal business hours\n * - Admin-initiated bookings that bypass availability checks\n * - Custom payment flows that don't use standard eCommerce checkout\n * - Overriding cancellation or rescheduling policies in exceptional cases\n */\nexport interface FlowControlSettings {\n  /** Whether availability is checked when creating or confirming the booking. */\n  skipAvailabilityValidation?: boolean;\n  /**\n   * Whether the booking's `status` is automatically updated to `CONFIRMED` when\n   * the customer completes the eCommerce checkout\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),\n   * regardless of whether the relevant service requires manual business confirmation.\n   */\n  skipBusinessConfirmation?: boolean;\n  /**\n   * Whether the customer is allowed to pay with a payment method that isn't\n   * supported for the relevant service.\n   */\n  skipSelectedPaymentOptionValidation?: boolean;\n  /**\n   * Whether the customer receives an automatic refund if there's a double booking\n   * conflict. Only available if the customer has paid with a\n   * pricing plan.\n   */\n  withRefund?: boolean | null;\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\nexport interface ParticipantChoices {\n  /**\n   * Information about the booked service choices. Includes the number of participants.\n   * @minSize 1\n   * @maxSize 20\n   */\n  serviceChoices?: ServiceChoices[];\n}\n\nexport interface ServiceChoices {\n  /**\n   * Number of participants for this variant ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * @min 1\n   */\n  numberOfParticipants?: number | null;\n  /**\n   * Service choices for these participants.\n   * @maxSize 5\n   */\n  choices?: ServiceChoice[];\n}\n\nexport interface ServiceChoice extends ServiceChoiceChoiceOneOf {\n  /**\n   * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.\n   * Choices are specific values for an option the customer can choose to book. For example,\n   * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.\n   * Each choice may have a different price.\n   */\n  custom?: string;\n  duration?: Duration;\n  /**\n   * ID of the corresponding option for the choice. For example, the choice `child`\n   * could correspond to the option `ageGroup`. In this case, `optionId` is the ID\n   * for the `ageGroup` option.\n   * @format GUID\n   */\n  optionId?: string;\n}\n\n/** @oneof */\nexport interface ServiceChoiceChoiceOneOf {\n  /**\n   * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.\n   * Choices are specific values for an option the customer can choose to book. For example,\n   * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.\n   * Each choice may have a different price.\n   */\n  custom?: string;\n  duration?: Duration;\n}\n\nexport interface Duration {\n  /**\n   * Duration of the service in minutes.\n   * Min: 1 minute, Max: 30 days, 23 hours, and 59 minutes\n   * @min 1\n   * @max 44639\n   */\n  minutes?: number;\n  /**\n   * Name of the duration option.\n   * Defaults to the formatted duration e.g. \"1 hour, 30 minutes\".\n   * @maxLength 255\n   */\n  name?: string | null;\n}\n\nexport interface MultiServiceBookingInfo {\n  /**\n   * Multi-service booking ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /** Type of the multi-service booking. */\n  type?: MultiServiceBookingTypeWithLiterals;\n}\n\nexport interface BookedAddOn {\n  /**\n   * The ID of the add-on.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * The ID of the add-on group.\n   * @format GUID\n   */\n  groupId?: string;\n  /**\n   * The add-on duration in minutes at the time of booking.\n   * @min 1\n   * @max 1440\n   * @readonly\n   */\n  durationInMinutes?: number | null;\n  /**\n   * The quantity of booked add-ons.\n   * @min 1\n   * @max 1000\n   */\n  quantity?: number | null;\n  /**\n   * Add-on `name` at the time of booking.\n   * @maxLength 100\n   * @readonly\n   */\n  name?: string | null;\n  /**\n   * Add-on name translated to the language the customer used during booking.\n   * @maxLength 100\n   * @readonly\n   */\n  nameTranslated?: string | null;\n}\n\nexport interface BookingFormFilled {\n  /** The booking object that form was filled for. */\n  booking?: Booking;\n  /**\n   * The submitted form data, where key is the form field and value is the data submitted for the given field.\n   * See the [form submission object](https://dev.wix.com/docs/rest/crm/forms/form-submissions/submission-object)\n   * for more details.\n   */\n  formSubmission?: Record<string, any> | null;\n  /**\n   * ID of the [form](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)\n   * that was filled.\n   * @format GUID\n   */\n  formId?: string | null;\n}\n\nexport interface SetBookingFormAndSubmissionIdRequest\n  extends SetBookingFormAndSubmissionIdRequestCreatedByOneOf {\n  /**\n   * The visitor who created the booking.\n   * @format GUID\n   */\n  visitorId?: string | null;\n  /**\n   * The member that created the booking.\n   * @format GUID\n   */\n  memberId?: string | null;\n  /**\n   * The app that created the booking.\n   * @format GUID\n   */\n  appId?: string | null;\n  /**\n   * ID of the booking to set `formId` and `submissionId` for.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /**\n   * ID of the form to set on the booking.\n   * @format GUID\n   */\n  formId?: string | null;\n  /**\n   * ID of the form submission to set on the booking.\n   * @format GUID\n   */\n  submissionId?: string | null;\n  /**\n   * MetaSite ID\n   * @format GUID\n   */\n  msid?: string | null;\n  /**\n   * Instance ID\n   * @format GUID\n   */\n  instanceId?: string | null;\n}\n\n/** @oneof */\nexport interface SetBookingFormAndSubmissionIdRequestCreatedByOneOf {\n  /**\n   * The visitor who created the booking.\n   * @format GUID\n   */\n  visitorId?: string | null;\n  /**\n   * The member that created the booking.\n   * @format GUID\n   */\n  memberId?: string | null;\n  /**\n   * The app that created the booking.\n   * @format GUID\n   */\n  appId?: string | null;\n}\n\nexport interface SetBookingFormAndSubmissionIdResponse {\n  /** Updated booking. */\n  booking?: Booking;\n}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: WebhooksIdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface WebhooksIdentificationData\n  extends WebhooksIdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface WebhooksIdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\nexport interface V2CreateBookingRequest\n  extends V2CreateBookingRequestBookableItemOneOf,\n    V2CreateBookingRequestParticipantsInfoOneOf {\n  /**\n   * Information about the slot to create a booking for.\n   * If you set `slot.location.locationType` to `CUSTOM`, the created slot's\n   * location is set to `slot.location.formattedAddress` when provided.\n   * Otherwise it's set to `contactDetails.fullAddress.formattedAddress`.\n   */\n  slot?: Slot;\n  /** Information about the schedule to create a booking for. */\n  schedule?: BookedSchedule;\n  /** Contact details of the customer booking the service. */\n  contactDetails?: ContactDetails;\n  /**\n   * Booking status.\n   * One of:\n   * - `\"CREATED\"` - The booking was created.\n   * - `\"UPDATED\"` - The booking was updated.\n   * - `\"CONFIRMED\"` - The booking has been confirmed and appears on the bookings calendar.\n   * Booking can be manually confirmed using the Set As Confirmed endpoint.\n   * Booking can be automatically confirmed when the following requirements are met:\n   * + The service is configured as automatically confirmed.\n   * + Invoking eCommerce checkout API and an order has been created.\n   * - `\"CANCELED\"` - The booking has been canceled and synced to bookings calendar.\n   * The booking can be canceled using cancel API.\n   * - `\"PENDING\"` - The booking waiting to be confirmed or declined buy the owner and is synced to bookings calendar.\n   * Bookings can be manually set as pending using setAsPending API, requires manage booking status permissions.\n   * Booking can be automatically set as pending when the following requirements are met:\n   * + The Service is configured as manually confirmed.\n   * + Invoking the eCommerce checkout API and an order has been created.\n   * - `\"WAITING_LIST\"` - The booking is pending on a waiting list.\n   * Booking can be created with this status when invoking waiting list join API.\n   * - `\"DECLINED\"` - The booking was declined by the owner and synced to bookings calendar.\n   * Booking can be manually declined using decline API and requires manage booking permissions.\n   * Booking can be automatically declined when the following requirements are met:\n   * + Invoking eCommerce checkout API and the order declined event has been sent.\n   * + Invoking eCommerce checkout API and order approved event has been sent, but the booking is offline and the booking causes a double booking.\n   */\n  status?: BookingStatusWithLiterals;\n  /**\n   * Additional custom fields of the booking form. The customer must provide\n   * information for each field when booking the service. For example, that they\n   * bring their own towels or whether they use a wheelchair.\n   *\n   * Max: 100 fields\n   * @maxSize 100\n   */\n  additionalFields?: CustomFormField[];\n  /**\n   * Total number of participants. Available only when the service doesn't have\n   * [variants](https://dev.wix.com/api/rest/wix-bookings/service-options-and-variants/introduction).\n   *\n   * Max: `20`\n   */\n  numberOfParticipants?: number | null;\n  /**\n   * Internal business note. Not visible to the customer.\n   *\n   * Max: 200 characters\n   */\n  internalBusinessNote?: string | null;\n  /**\n   * Payment option the customer intends to use.\n   * Must be one of the payment options defined for the service, unless\n   * you pass `flowControlSettings.skipSelectedPaymentOptionValidation` as `true`.\n   */\n  selectedPaymentOption?: SelectedPaymentOptionWithLiterals;\n  /**\n   * Identifies the source (platform, actor and app) that created this booking.\n   * This property of the booking cannot be changed.\n   * The app_def_id and app_name will be resolved automatically.\n   * TODO GAP See if we need this - might be able to get this data from the headers?\n   */\n  bookingSource?: BookingSource;\n  /**\n   * A user identifier of an external application user that initiated the book request.\n   * Allows an external application to later identify its own bookings and correlate to its own internal users\n   */\n  externalUserId?: string | null;\n  /** Information about a message to send to the customer. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   *\n   * Default: `true`.\n   */\n  sendSmsReminder?: boolean | null;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings\n   * creation flow are changed. For example, whether the availability is\n   * checked before creating the booking or if additional payment options are\n   * accepted.\n   */\n  flowControlSettings?: CreateBookingRequestFlowControlSettings;\n}\n\n/** @oneof */\nexport interface V2CreateBookingRequestBookableItemOneOf {\n  /**\n   * Information about the slot to create a booking for.\n   * If you set `slot.location.locationType` to `CUSTOM`, the created slot's\n   * location is set to `slot.location.formattedAddress` when provided.\n   * Otherwise it's set to `contactDetails.fullAddress.formattedAddress`.\n   */\n  slot?: Slot;\n  /** Information about the schedule to create a booking for. */\n  schedule?: BookedSchedule;\n}\n\n/** @oneof */\nexport interface V2CreateBookingRequestParticipantsInfoOneOf {}\n\nexport interface Slot {\n  /**\n   * ID for the slot's corresponding session, when the session is either a single session\n   * or a specific session generated from a recurring session.\n   *\n   * Deprecated. Please use `eventId` instead.\n   * @deprecated ID for the slot's corresponding session, when the session is either a single session\n   * or a specific session generated from a recurring session.\n   *\n   * Deprecated. Please use `eventId` instead.\n   * @replacedBy event_id\n   * @targetRemovalDate 2025-09-30\n   */\n  sessionId?: string | null;\n  /** Service ID. */\n  serviceId?: string;\n  /** Schedule ID. */\n  scheduleId?: string;\n  /**\n   * The start time of this slot in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339)\n   * format.\n   *\n   * If `timezone` is specified,\n   * dates are based on the local date/time. This means that the timezone offset\n   * in the `start_date` is ignored.\n   */\n  startDate?: string | null;\n  /**\n   * The end time of this slot in\n   * [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339) format.\n   *\n   * If `timezone` is specified,\n   * dates are based on the local date/time. This means that the timezone offset\n   * in the `end_date` is ignored.\n   */\n  endDate?: string | null;\n  /**\n   * The timezone for which slot availability is to be calculated.\n   *\n   * Learn more about [handling Daylight Savings Time (DST) for local time zones](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_handling-daylight-savings-time-dst-for-local-time-zones)\n   * when calculating availability.\n   */\n  timezone?: string | null;\n  /**\n   * The resource required for this slot. Currently, the only supported resource\n   * is the relevant staff member for the slot.\n   */\n  resource?: SlotResource;\n  /** Geographic location of the slot. */\n  location?: Location;\n  /**\n   * ID for the slot's corresponding event, when the event is either a single event\n   * or a specific event generated from a recurring event.\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n}\n\nexport interface SlotResource {\n  /**\n   * Resource ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Resource name. Read only.\n   * @maxLength 1200\n   */\n  name?: string | null;\n}\n\nexport interface CreateBookingRequestFlowControlSettings {\n  /**\n   * Whether the availability is checked before creating the booking. When\n   * passing `false` a booking is only created when the slot or schedule is\n   * available. Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY`\n   * permission scope when passing `true`.\n   *\n   * Default: `false`.\n   */\n  skipAvailabilityValidation?: boolean;\n  /**\n   * Whether `PENDING` bookings are automatically set to `CONFIRMED` for\n   * services that normally require the owner's manual confirmation. Your\n   * app must have the `BOOKINGS.OVERRIDE_AVAILABILITY` permission scope\n   * when passing `true`.\n   *\n   * Default: `false`.\n   */\n  skipBusinessConfirmation?: boolean;\n  /**\n   * Whether customers can pay using a payment method that isn't supported\n   * for the service, but that's supported for other services. Your app\n   * must have the `BOOKINGS.MANAGE_PAYMENTS` permission scope when passing\n   * `true`.\n   *\n   * Default: `false`.\n   */\n  skipSelectedPaymentOptionValidation?: boolean;\n}\n\nexport interface V2CreateBookingResponse {\n  /** Created booking. */\n  booking?: Booking;\n}\n\nexport interface V2CancelBookingRequest {\n  /**\n   * ID of the booking to cancel.\n   * @format GUID\n   */\n  bookingId?: string;\n  /**\n   * Information about whether to notify the customer about the cancellation and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when managing the booking.\n   */\n  revision?: string | null;\n}\n\nexport interface CancelBookingRequestFlowControlSettings {\n  /**\n   * Whether the cancellation policy applies when canceling the booking. When\n   * passing `false` you can only cancel a booking if the cancellation policy\n   * allows it. Your app must have the `BOOKINGS.IGNORE_BOOKING_POLICY `\n   * permission scope when passing `true`.\n   *\n   * Default: `false`.\n   */\n  ignoreCancellationPolicy?: boolean;\n  /**\n   * Whether to issue a refund when canceling the booking.\n   * The refund will be issued only if the booking is refundable.\n   * Currently, booking is considered refundable when it was paid by membership.\n   * If passing `true`, the booking flow control settings will be set with refund,\n   * otherwise, either if `false` is passed or the field remains empty,\n   * the booking flow control settings will be set with no refund.\n   *\n   * Default: `false`.\n   */\n  withRefund?: boolean | null;\n}\n\nexport interface V2CancelBookingResponse {\n  /** Canceled booking. */\n  booking?: Booking;\n}\n\nexport interface V2RescheduleBookingRequest\n  extends V2RescheduleBookingRequestParticipantsInfoOneOf {\n  /**\n   * Id of the booking to reschedule.\n   * @format GUID\n   */\n  bookingId?: string;\n  /** Information about the new slot. */\n  slot?: Slot;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be passed when\n   * managing the booking.\n   */\n  revision?: string | null;\n  /**\n   * Information about whether to notify the customer about the rescheduling and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\n/** @oneof */\nexport interface V2RescheduleBookingRequestParticipantsInfoOneOf {}\n\nexport interface RescheduleBookingRequestFlowControlSettings {\n  /**\n   * Whether the rescheduling policy applies when rescheduling the booking.\n   * When passing `false` you can only cancel a booking if the rescheduling\n   * policy allows it. Your app must have the `BOOKINGS.IGNORE_BOOKING_POLICY `\n   * permission scope when passing `true`.\n   *\n   * Default: `false`.\n   */\n  ignoreReschedulePolicy?: boolean;\n  /**\n   * Whether the availability is checked before rescheduling the booking.\n   * When passing `false` a booking is only created when the slot or\n   * schedule is available. Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY`\n   * permission scope when passing `true`.\n   *\n   * Default: `false`.\n   */\n  skipAvailabilityValidation?: boolean;\n  /**\n   * Whether the rescheduled booking's status is automatically set to\n   * `CONFIRMED` for services that normally require the owner's manual\n   * confirmation. Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY`\n   * permission scope when passing `true`.\n   *\n   * Default: `false`.\n   */\n  skipBusinessConfirmation?: boolean;\n}\n\nexport interface V2RescheduleBookingResponse {\n  /** Rescheduled booking. */\n  booking?: Booking;\n}\n\nexport interface V2ConfirmBookingRequest {\n  /**\n   * ID of the booking to confirm.\n   * @format GUID\n   */\n  bookingId?: string;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be passed when\n   * managing the booking.\n   */\n  revision?: string | null;\n  /**\n   * Information about whether to notify the customer about the confirmation and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface V2ConfirmBookingResponse {\n  booking?: Booking;\n}\n\nexport interface V2DeclineBookingRequest {\n  /**\n   * ID of the booking to decline.\n   * @format GUID\n   */\n  bookingId?: string;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be passed when\n   * managing the booking.\n   */\n  revision?: string | null;\n  /**\n   * Information about whether to notify the customer about the decline and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface V2DeclineBookingResponse {\n  /** Declined booking. */\n  booking?: Booking;\n}\n\nexport interface V2UpdateNumberOfParticipantsRequest\n  extends V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {\n  /**\n   * ID of the booking to update the number of participants for.\n   * @format GUID\n   */\n  bookingId?: string;\n  /** Updated number of participants. */\n  numberOfParticipants?: number | null;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be passed when\n   * managing the booking.\n   */\n  revision?: string | null;\n}\n\n/** @oneof */\nexport interface V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {}\n\nexport interface V2UpdateNumberOfParticipantsResponse {\n  /** Booking with updated number of participants. */\n  booking?: Booking;\n}\n\nexport interface ConfirmOrDeclineBookingRequest {\n  /**\n   * ID of the booking to confirm or decline.\n   * @format GUID\n   */\n  bookingId: string;\n  /**\n   * Current payment status of the booking when using a custom checkout page and\n   * not the *eCommerce checkout*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n   *\n   * The booking is declined if there is a double booking conflict and you provide\n   * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.\n   */\n  paymentStatus?: PaymentStatusWithLiterals;\n}\n\nexport interface ConfirmOrDeclineBookingResponse {\n  /** Updated booking. */\n  booking?: Booking;\n}\n\nexport interface BulkConfirmOrDeclineBookingRequest {\n  /**\n   * Bookings to confirm or decline.\n   * @minSize 1\n   * @maxSize 300\n   */\n  details: BulkConfirmOrDeclineBookingRequestBookingDetails[];\n  /** Whether to return the confirmed or declined booking objects. */\n  returnEntity?: boolean;\n}\n\nexport interface BulkConfirmOrDeclineBookingRequestBookingDetails {\n  /**\n   * ID of the booking to confirm or decline.\n   * @format GUID\n   */\n  bookingId?: string;\n  /**\n   * Current payment status of the booking when using a custom checkout page and\n   * not the *eCommerce checkout*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n   *\n   * The booking is declined if there is a double booking conflict and you provide\n   * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.\n   */\n  paymentStatus?: PaymentStatusWithLiterals;\n}\n\nexport interface BulkConfirmOrDeclineBookingResponse {\n  /** List of confirmed or declined bookings, including metadata. */\n  results?: BulkBookingResult[];\n  /** Total successes and failures of the Bulk Confirm Or Decline call. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkBookingResult {\n  /**\n   * Information about the booking that was created or updated.\n   * Including its ID, index in the bulk request and whether it was\n   * successfully created or updated.\n   */\n  itemMetadata?: ItemMetadata;\n  /**\n   * Created or updated booking. Available only if you requested\n   * to return the booking entity.\n   */\n  item?: Booking;\n}\n\nexport interface ItemMetadata {\n  /** Item ID. Should always be available, unless it's impossible (for example, when failing to create an item). */\n  _id?: string | null;\n  /** Index of the item within the request array. Allows for correlation between request and response items. */\n  originalIndex?: number;\n  /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */\n  success?: boolean;\n  /** Details about the error in case of failure. */\n  error?: ApplicationError;\n}\n\nexport interface ApplicationError {\n  /** Error code. */\n  code?: string;\n  /** Description of the error. */\n  description?: string;\n  /** Data related to the error. */\n  data?: Record<string, any> | null;\n}\n\nexport interface BulkActionMetadata {\n  /** Number of items that were successfully processed. */\n  totalSuccesses?: number;\n  /** Number of items that couldn't be processed. */\n  totalFailures?: number;\n  /** Number of failures without details because detailed failure threshold was exceeded. */\n  undetailedFailures?: number;\n}\n\nexport interface BookingChanged {\n  /** The booking before the changes. */\n  previousBooking?: Booking;\n  /** The booking after the changes. */\n  currentBooking?: Booking;\n}\n\nexport interface CreateBookingRequest {\n  /** The booking to create. */\n  booking: Booking;\n  /**\n   * Information about whether to notify the customer and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   * Default: `true`.\n   */\n  sendSmsReminder?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability when creating a booking.\n   */\n  flowControlSettings?: CreateBookingFlowControlSettings;\n}\n\nexport interface CreateBookingFlowControlSettings {\n  /**\n   * Whether the availability is checked before creating the booking.\n   *\n   * - `false`: A booking is only created when the slot or schedule is available.\n   * - `true`: The booking is created regardless of availability conflicts. Make sure the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has the required permissions.\n   *\n   * *Use cases for `true`:**\n   * - Emergency or priority bookings that must be accommodated.\n   * - Administrative bookings that override normal availability rules.\n   * - Testing or demonstration purposes.\n   *\n   * Default: `false`.\n   */\n  skipAvailabilityValidation?: boolean;\n  /**\n   * Whether `PENDING` bookings are automatically set to `CONFIRMED` for\n   * services that normally require the owner's manual confirmation.\n   *\n   * Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY` permission\n   * when passing `true`.\n   * Default: `false`.\n   */\n  skipBusinessConfirmation?: boolean;\n  /**\n   * Whether customers can pay using a payment method that isn't supported\n   * for the service, but that's supported for other services.\n   *\n   * Your app must have the `BOOKINGS.MANAGE_PAYMENTS` permission when passing\n   * `true`.\n   * Default: `false`.\n   */\n  skipSelectedPaymentOptionValidation?: boolean;\n}\n\nexport interface CreateBookingResponse {\n  /** Created booking. */\n  booking?: Booking;\n}\n\n/**\n * The `fieldMask` should not include both the `numberOfParticipants` and `participantsInfo` paths. Including both results\n * in an error. `participantsInfo` is preferred over `numberOfParticipants`.\n */\nexport interface UpdateBookingRequest {\n  booking?: Booking;\n}\n\nexport interface UpdateBookingResponse {\n  booking?: Booking;\n}\n\nexport interface LegacyCreateBookingRequest {\n  booking?: Booking;\n}\n\nexport interface LegacyCreateBookingResponse {\n  booking?: Booking;\n}\n\n/**\n * The `fieldMask` for each booking should not include both the `numberOfParticipants` and `participantsInfo` paths. Including both results\n * in an error. `participantsInfo` is preferred over `numberOfParticipants`.\n */\nexport interface BulkUpdateBookingRequest {\n  bookings?: MaskedBooking[];\n}\n\nexport interface MaskedBooking {\n  booking?: Booking;\n  mask?: string[];\n}\n\nexport interface BulkUpdateBookingResponse {\n  /**\n   * Information about the booking that was updated.\n   * Including its ID, index in the bulk request and whether it was\n   * successfully updated.\n   */\n  results?: ItemMetadata[];\n  /** Total number of successes and failures for Bulk Update Bookings. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkCreateBookingRequest {\n  /**\n   * Bookings to create.\n   *\n   * Max: 8 bookings\n   * @minSize 1\n   * @maxSize 8\n   */\n  createBookingsInfo: CreateBookingInfo[];\n  /** Whether to return the created bookings. */\n  returnFullEntity?: boolean;\n}\n\nexport interface CreateBookingInfo {\n  /** Booking to create. */\n  booking?: Booking;\n  /**\n   * Information about whether to notify the customer and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   * Default: `true`.\n   */\n  sendSmsReminder?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability when creating a booking.\n   */\n  flowControlSettings?: CreateBookingFlowControlSettings;\n}\n\nexport interface BulkCreateBookingResponse {\n  /** List of individual Bulk Create Bookings results. */\n  results?: BulkBookingResult[];\n  /** Total number of successes and failures for Bulk Create Bookings. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface RescheduleBookingRequest\n  extends RescheduleBookingRequestParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices to book. Specify when not all\n   * participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n  /**\n   * ID of the booking to reschedule.\n   * @format GUID\n   */\n  bookingId: string;\n  /** New slot of the booking. */\n  slot: V2Slot;\n  /**\n   * Revision number, which increments by 1 each time the booking is rescheduled.\n   * To prevent conflicting changes, the current revision must be passed when\n   * rescheduling the booking.\n   */\n  revision: string | null;\n  /**\n   * Information about whether to notify the customer about the rescheduling and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability when rescheduling a booking.\n   */\n  flowControlSettings?: RescheduleBookingFlowControlSettings;\n}\n\n/** @oneof */\nexport interface RescheduleBookingRequestParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices to book. Specify when not all\n   * participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\nexport interface V2Slot {\n  /** Identifier for the underlying session when the session is a single session or generated from a recurring session. */\n  sessionId?: string | null;\n  /** Service identifier. Required. */\n  serviceId?: string;\n  /** Schedule identifier. Required. */\n  scheduleId?: string;\n  /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  startDate?: string | null;\n  /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */\n  endDate?: string | null;\n  /** The timezone according to which the slot is calculated and presented. */\n  timezone?: string | null;\n  /**\n   * The resource required for this slot.\n   * When populated, the specified resource will be assigned to the slot upon confirmation according to its availability.\n   * When empty, if `skip_availability_validation` is `false`, a random available resource will be assigned to the slot upon confirmation.\n   * Otherwise, one of the service resources will be assigned to the slot randomly upon confirmation.\n   */\n  resource?: SlotSlotResource;\n  /** Geographic location of the slot. */\n  location?: SlotLocation;\n  /**\n   * Calendar event ID - not supported.\n   * If not empty, on all write flows (create/update), it takes priority over `sessionId`.\n   * So if both `sessionId` and `eventId` are provided, the `sessionId` will be based on the `eventId`.\n   * Otherwise, if `eventId` is empty on write flow,\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n}\n\nexport enum LocationLocationType {\n  /** Undefined location type. */\n  UNDEFINED = 'UNDEFINED',\n  /** The business address as set in the site’s general settings. */\n  OWNER_BUSINESS = 'OWNER_BUSINESS',\n  /** The address set when creating the service. */\n  OWNER_CUSTOM = 'OWNER_CUSTOM',\n  /** The address set for the individual session. */\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type LocationLocationTypeWithLiterals =\n  | LocationLocationType\n  | 'UNDEFINED'\n  | 'OWNER_BUSINESS'\n  | 'OWNER_CUSTOM'\n  | 'CUSTOM';\n\nexport interface SlotSlotResource {\n  /**\n   * Resource ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Resource name.\n   * @maxLength 1200\n   */\n  name?: string | null;\n}\n\nexport interface SlotLocation {\n  /**\n   * Business Location ID. Present if the location is a business location.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Location name. */\n  name?: string | null;\n  /** A string containing the full address of this location. */\n  formattedAddress?: string | null;\n  /** Location type. */\n  locationType?: LocationLocationTypeWithLiterals;\n}\n\nexport interface RescheduleBookingFlowControlSettings {\n  /**\n   * Whether the rescheduling policy applies when rescheduling the booking.\n   *\n   * When passing `false`, you can only cancel a booking if the rescheduling\n   * policy allows it.\n   * Default: `false`.\n   */\n  ignoreReschedulePolicy?: boolean;\n  /**\n   * Whether the availability is checked before rescheduling the booking.\n   *\n   * When passing `false`, a booking is only created when the slot or\n   * schedule is available.\n   * Default: `false`.\n   */\n  skipAvailabilityValidation?: boolean;\n  /**\n   * Whether the rescheduled booking's status is automatically set to\n   * `CONFIRMED` for services that normally require the owner's manual\n   * confirmation.\n   * Default: `false`.\n   */\n  skipBusinessConfirmation?: boolean;\n}\n\nexport interface RescheduleBookingResponse {\n  /** Rescheduled booking. */\n  booking?: Booking;\n}\n\nexport interface BookingRescheduled\n  extends BookingRescheduledPreviousParticipantsInfoOneOf {\n  /**\n   * The previous total number of participants. Available only when the booking includes\n   * a single service variant.\n   */\n  previousTotalParticipants?: number;\n  /**\n   * Information about the previous booked service choices and participants.\n   * Available only when the booking includes multiple service variants.\n   */\n  previousParticipantsChoices?: ParticipantChoices;\n  /** The rescheduled booking object. */\n  booking?: Booking;\n  /** Information about whether to notify the customer about the rescheduling and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings\n   * rescheduling flow are changed. For example, whether the availability of\n   * the new slot is checked before rescheduling the booking or if you can\n   * reschedule the booking even though the rescheduling policy doesn't allow it.\n   */\n  flowControlSettings?: RescheduleBookingFlowControlSettings;\n  /** ID of the rescheduling initiator. */\n  initiatedBy?: IdentificationData;\n  /** The previous status of the booking. */\n  previousStatus?: BookingStatusWithLiterals;\n  /** An object describing the previous slot or schedule of the booking. */\n  previousBookedEntity?: BookedEntity;\n  /**\n   * The previous start date of the booking.\n   * For a slot, this is the start date of the slot.\n   * For a schedule, this is the start date of the first session.\n   */\n  previousStartDate?: Date | null;\n  /**\n   * The previous end date of the booking.\n   * For a slot, this is the end date of the slot.\n   * For a schedule, this is the end date of the last session.\n   */\n  previousEndDate?: Date | null;\n}\n\n/** @oneof */\nexport interface BookingRescheduledPreviousParticipantsInfoOneOf {\n  /**\n   * The previous total number of participants. Available only when the booking includes\n   * a single service variant.\n   */\n  previousTotalParticipants?: number;\n  /**\n   * Information about the previous booked service choices and participants.\n   * Available only when the booking includes multiple service variants.\n   */\n  previousParticipantsChoices?: ParticipantChoices;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /**\n   * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.\n   * @format GUID\n   * @readonly\n   */\n  contactId?: string | null;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum IdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type IdentityTypeWithLiterals =\n  | IdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\nexport interface BulkRescheduleBookingRequest {\n  /**\n   * Reschedule multiple bookings to multiple slots.\n   * @minSize 1\n   */\n  slotsBookings?: SlotBookings[];\n  /**\n   * Information about whether to notify the customer about the rescheduling and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface BulkRescheduleBookingRequestBooking {\n  /**\n   * ID of the booking to reschedule.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Revision number, which increments by 1 each time the booking is rescheduled.\n   * To prevent conflicting changes,\n   * the current revision must be specified when rescheduling the booking.\n   */\n  revision?: string | null;\n}\n\n/** Bookings to be rescheduled to the given slot. */\nexport interface SlotBookings {\n  /**\n   * The bookings details.\n   * @minSize 1\n   */\n  bookings?: BulkRescheduleBookingRequestBooking[];\n  /**\n   * The slot to which the bookings were rescheduled.\n   * These bookings are automatically assigned to the session, if given. Otherwise, a new session is created.\n   */\n  slot?: BookedSlot;\n}\n\nexport interface BulkRescheduleBookingResponse {\n  /**\n   * Information about the booking that was rescheduled.\n   * Including its ID, index in the bulk request and whether it was\n   * successfully rescheduled.\n   */\n  results?: ItemMetadata[];\n  /** Total number of successes and failures for Bulk Reschedule Bookings. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\n/** Update the booked schedule of multiple bookings to the given schedule. */\nexport interface BulkUpdateBookedScheduleRequest {\n  /**\n   * The bookings whose booked schedule is to be updated to the given schedule.\n   * @minSize 1\n   */\n  bookings?: BookingDetails[];\n  /** ID of the schedule to update. */\n  scheduleId?: string;\n  /**\n   * Information about whether to notify the customer about the rescheduling and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface BookingDetails {\n  /**\n   * ID of the bookings to be updated.\n   * @format GUID\n   */\n  _id?: string;\n  revision?: string | null;\n}\n\nexport interface BulkUpdateBookedScheduleResponse {\n  /**\n   * Information about the schedule that was updated.\n   * Including its ID, index in the bulk request and whether it was\n   * succesfully updated.\n   */\n  results?: ItemMetadata[];\n  /** Total number of successes and failures for Bulk Updated Booked Schedules. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface QueryBookingsRequest {\n  /** Information about filters, paging, and sorting. */\n  query?: QueryV2;\n}\n\nexport interface QueryV2 extends QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   */\n  sort?: Sorting[];\n  /** Array of projected fields. A list of specific field names to return. If `fieldsets` are also specified, the union of `fieldsets` and `fields` is returned. */\n  fields?: string[];\n  /** Array of named, predefined sets of projected fields. A array of predefined named sets of fields to be returned. Specifying multiple `fieldsets` will return the union of fields from all sets. If `fields` are also specified, the union of `fieldsets` and `fields` is returned. */\n  fieldsets?: string[];\n}\n\n/** @oneof */\nexport interface QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n  /**\n   * Cursor token pointing to a page of results. In the first request,\n   * specify `cursorPaging.limit`. For following requests, specify the\n   * retrieved `cursorPaging.cursor` token and not `query.filter` or\n   * `query.sort`.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface Paging {\n  /** Number of items to load. */\n  limit?: number | null;\n  /** Number of items to skip in the current sort order. */\n  offset?: number | null;\n}\n\nexport interface CursorPaging {\n  /**\n   * Maximum number of items to return in the results.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface QueryBookingsResponse {\n  /** Retrieved bookings. */\n  bookings?: Booking[];\n  /** Paging metadata. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface PagingMetadataV2 {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  offset?: number | null;\n  /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */\n  total?: number | null;\n  /** Flag that indicates the server failed to calculate the `total` field. */\n  tooManyToCount?: boolean | null;\n  /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */\n  cursors?: Cursors;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor string pointing to the next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to the previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface ConfirmRequest {\n  /**\n   * ID of the booking to confirm.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Information about whether to notify the customer about the confirmation and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface ConfirmResponse {\n  /** Confirmed booking. */\n  booking?: Booking;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n}\n\nexport interface ConfirmBookingRequest {\n  /**\n   * ID of the booking to confirm.\n   * @format GUID\n   */\n  bookingId: string;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be passed when\n   * updating the booking.\n   */\n  revision: string | null;\n  /**\n   * Information about whether to notify the customer about the confirmation and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   */\n  sendSmsReminder?: boolean | null;\n  /** Payment status to set for the booking. */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability when confirming a booking.\n   */\n  flowControlSettings?: ConfirmBookingFlowControlSettings;\n}\n\nexport interface ConfirmBookingFlowControlSettings {\n  /**\n   * Whether the availability is checked before confirming the booking.\n   *\n   * When specifying `false`, a booking is only updated with status `CONFIRMED`.\n   * Default: `false`.\n   */\n  checkAvailabilityValidation?: boolean;\n}\n\nexport interface ConfirmBookingResponse {\n  /** Confirmed booking. */\n  booking?: Booking;\n}\n\nexport interface BookingConfirmed {\n  /** The confirmed booking object. */\n  booking?: Booking;\n  /** Information about whether to notify the customer about the confirmation and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * The phone number is taken from `contactDetails.phone`.\n   */\n  sendSmsReminder?: boolean | null;\n  /** Whether this booking overlaps with another existing confirmed booking. */\n  doubleBooked?: boolean | null;\n  /** ID of the confirmation initiator. */\n  initiatedBy?: IdentificationData;\n  /** The previous status of the booking. */\n  previousStatus?: BookingStatusWithLiterals;\n  /** The previous payment status of the booking. */\n  previousPaymentStatus?: PaymentStatusWithLiterals;\n}\n\nexport interface ConsistentQueryBookingsRequest {\n  /** Information about filters, paging, and sorting. */\n  query?: QueryV2;\n}\n\nexport interface ConsistentQueryBookingsResponse {\n  /** Retrieved bookings. */\n  bookings?: Booking[];\n  /** Paging metadata. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface SetBookingSessionIdRequest {\n  /**\n   * ID of the booking to set `sessionId` for.\n   * @format GUID\n   */\n  _id?: string;\n  /** ID of the session to set on the booking. */\n  sessionId?: string;\n}\n\nexport interface SetBookingSessionIdResponse {\n  /** Updated booking. */\n  booking?: Booking;\n}\n\nexport interface SetBookingSubmissionIdRequest {\n  /**\n   * ID of the booking to set `submissionId` for.\n   * @format GUID\n   */\n  bookingId: string;\n  /**\n   * ID of the form submission to set on the booking.\n   * @format GUID\n   */\n  submissionId: string;\n}\n\nexport interface SetBookingSubmissionIdResponse {\n  /** Updated booking. */\n  booking?: Booking;\n}\n\nexport interface UpdateExtendedFieldsRequest {\n  /** ID of the entity to update. */\n  _id: string;\n  /** Identifier for the app whose extended fields are being updated. */\n  namespace: string;\n  /** Data to update. Structured according to the [schema](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields#json-schema-for-extended-fields) defined when the extended fields were configured. */\n  namespaceData: Record<string, any> | null;\n}\n\nexport interface UpdateExtendedFieldsResponse {\n  /**\n   * Updated namespace.\n   * @maxLength 164\n   */\n  namespace?: string;\n  /** Updated data. */\n  namespaceData?: Record<string, any> | null;\n}\n\nexport interface DeclineBookingRequest {\n  /**\n   * ID of the booking to decline.\n   * @format GUID\n   */\n  bookingId: string;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified when\n   * declining the booking.\n   * @min 1\n   */\n  revision: string | null;\n  /**\n   * Information about whether to notify the customer and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /** Payment status to set for the booking. */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to refund a declined booking.\n   */\n  flowControlSettings?: DeclineBookingFlowControlSettings;\n}\n\nexport interface DeclineBookingFlowControlSettings {\n  /**\n   * Whether to issue a refund when declining the booking.\n   *\n   * The refund will be issued only if the booking is refundable.\n   * Currently, a booking is considered refundable when it was paid by membership.\n   * If specifying `true`, the booking flow control settings will be set with a refund.\n   * If you specify `false` or an empty field,\n   * the booking flow control settings are set without refund.\n   *\n   * Default: `false`.\n   */\n  withRefund?: boolean | null;\n}\n\nexport interface DeclineBookingResponse {\n  /** Declined booking. */\n  booking?: Booking;\n}\n\nexport interface BookingDeclined {\n  /** The declined booking object. */\n  booking?: Booking;\n  /** Information about whether to notify the customer about the decline and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /** Whether this booking overlaps with another existing confirmed booking. */\n  doubleBooked?: boolean | null;\n  /** ID of the decline initiator. */\n  initiatedBy?: IdentificationData;\n  /** The previous status of the booking. */\n  previousStatus?: BookingStatusWithLiterals;\n  /** The previous payment status of the booking. */\n  previousPaymentStatus?: PaymentStatusWithLiterals;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings\n   * declining flow are changed. For example, whether to issue a refund.\n   */\n  flowControlSettings?: DeclineBookingFlowControlSettings;\n}\n\nexport interface CancelBookingRequest {\n  /**\n   * ID of the booking to cancel.\n   * @format GUID\n   */\n  bookingId: string;\n  /**\n   * Information about whether to notify the customer about the cancellation and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to allow a cancellation even though the service's\n   * policy doesn't allow it.\n   */\n  flowControlSettings?: CancelBookingFlowControlSettings;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified when\n   * managing the booking.\n   */\n  revision: string | null;\n}\n\nexport interface CancelBookingFlowControlSettings {\n  /**\n   * Whether the cancellation policy applies when canceling the booking.\n   *\n   * When passing `false`, you can only cancel a booking if the cancellation policy allows it.\n   * Default: `false`.\n   */\n  ignoreCancellationPolicy?: boolean | null;\n  /**\n   * Whether to issue a refund when canceling the booking.\n   *\n   * The refund will be issued only if the booking is refundable.\n   * Currently, a booking is considered refundable when it was paid by membership.\n   * If you specify `true`, the booking flow control settings is set to include a refund.\n   * If `false` is specified or the field remains empty,\n   * the booking flow control settings are set without refund.\n   *\n   * Default: `false`.\n   */\n  withRefund?: boolean | null;\n}\n\nexport interface CancelBookingResponse {\n  /** Canceled booking. */\n  booking?: Booking;\n}\n\nexport interface BookingCanceled {\n  /** The canceled booking object. */\n  booking?: Booking;\n  /** Information about whether to notify the customer about the cancellation and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings\n   * cancellation flow are changed. For example, whether you can cancel\n   * a booking even though the cancellation policy doesn't allow it or whether\n   * to issue a refund.\n   */\n  flowControlSettings?: CancelBookingFlowControlSettings;\n  /** ID of the cancellation initiator. */\n  initiatedBy?: IdentificationData;\n  /** The previous status of the booking. */\n  previousStatus?: BookingStatusWithLiterals;\n}\n\nexport interface UpdateNumberOfParticipantsRequest\n  extends UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices the participants have booked. Specify\n   * when not all participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n  /**\n   * ID of the booking to update the number of participants for.\n   * @format GUID\n   */\n  bookingId: string;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified\n   * when updating the booking.\n   */\n  revision: string | null;\n}\n\n/** @oneof */\nexport interface UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices the participants have booked. Specify\n   * when not all participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\nexport interface UpdateNumberOfParticipantsResponse {\n  /** Updated booking. */\n  booking?: Booking;\n}\n\nexport interface NumberOfParticipantsUpdated\n  extends NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf {\n  /**\n   * The previous total number of participants. Available only when the booking includes\n   * a single service variant.\n   */\n  previousTotalParticipants?: number;\n  /**\n   * Information about the previous booked service choices and participants.\n   * Available only when the booking includes multiple service variants.\n   */\n  previousParticipantsChoices?: ParticipantChoices;\n  /** The updated booking object. */\n  booking?: Booking;\n  /** ID of the participant number update initiator. */\n  initiatedBy?: IdentificationData;\n}\n\n/** @oneof */\nexport interface NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf {\n  /**\n   * The previous total number of participants. Available only when the booking includes\n   * a single service variant.\n   */\n  previousTotalParticipants?: number;\n  /**\n   * Information about the previous booked service choices and participants.\n   * Available only when the booking includes multiple service variants.\n   */\n  previousParticipantsChoices?: ParticipantChoices;\n}\n\nexport interface BulkCalculateAllowedActionsRequest {\n  /**\n   * The booking IDs for which we want to calculate the allowed actions.\n   * @minSize 1\n   */\n  bookingIds?: string[] | null;\n}\n\nexport interface BulkCalculateAllowedActionsResponse {\n  results?: BulkCalculateAllowedActionsResult[];\n  /** Total number of successes and failures for Bulk Calculate Allowed Actions. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkCalculateAllowedActionsResult {\n  /** Metadata for the booking. Including ID, index in the provided sequence, success status, and error. */\n  itemMetadata?: ItemMetadata;\n  /** Booking entity. */\n  item?: AllowedActions;\n}\n\n/** Possible actions allowed for the booking. */\nexport interface AllowedActions {\n  /** Whether canceling the booking is allowed. */\n  cancel?: boolean;\n  /** Whether rescheduling the booking is allowed. */\n  reschedule?: boolean;\n}\n\nexport interface GetSlotAvailabilityRequest {\n  /** The slot for which the availability is checked. */\n  slot?: V2Slot;\n  /** The timezone for which availability is to be calculated. */\n  timezone?: string | null;\n}\n\nexport interface GetSlotAvailabilityResponse {\n  availability?: SlotAvailability;\n  bookingPolicySettings?: BookingPolicySettings;\n}\n\nexport interface SlotAvailability {\n  /** Identifier for the underlying session when the session is a single session or generated from a recurring session. Required. */\n  slot?: V2Slot;\n  /** Whether this available slot is bookable. */\n  bookable?: boolean;\n  /**\n   * Total number of spots for this availability.\n   * For example, for a class of 10 spots with 3 spots booked, `totalSpots` is 10 and `openSpots` is 7.\n   */\n  totalSpots?: number | null;\n  /**\n   * Number of open spots for this availability.\n   * For appointments, if there are existing bookings with overlapping time, service & resource, `openSpots` is 0. Otherwise, `openSpots` is 1.\n   */\n  openSpots?: number | null;\n  /** An object describing the slot's waiting list and its occupancy. */\n  waitingList?: WaitingList;\n  /** Indicators for booking policy violations for the slot. */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /** Indicates whether this slot is locked. */\n  locked?: boolean | null;\n}\n\nexport interface WaitingList {\n  /**\n   * Total number of spots and open spots for this waiting list.\n   * For example, a Yoga class of 10 waiting list spots with 3 registered on the waiting list has `total_spots`: 10 and `open_spots`: 7.\n   */\n  totalSpots?: number | null;\n  openSpots?: number | null;\n}\n\nexport interface BookingPolicyViolations {\n  /** Booking policy violation: Too early to book this slot. */\n  tooEarlyToBook?: boolean | null;\n  /** Booking policy violation: Too late to book this slot. */\n  tooLateToBook?: boolean | null;\n  /** Booking policy violation: Online booking is disabled for this slot. */\n  bookOnlineDisabled?: boolean | null;\n}\n\nexport interface BookingPolicySettings {\n  /** Booking policy settings for a given slot or schedule. */\n  maxParticipantsPerBooking?: number | null;\n}\n\nexport interface AvailableResources {\n  /**\n   * Resource type ID.\n   * @format GUID\n   */\n  resourceTypeId?: string | null;\n  /**\n   * Available resources for the slot.\n   * `maxSize` is defined by 135 staff members + 3 resource types and 50 resources per type.\n   * `Availability-2` currently has no `maxSize` defined.\n   * @format GUID\n   * @maxSize 275\n   */\n  resourceIds?: string[];\n}\n\nexport interface GetScheduleAvailabilityRequest {\n  /**\n   * ID of the schedule for which to check availability.\n   * @format GUID\n   */\n  scheduleId?: string;\n}\n\nexport interface GetScheduleAvailabilityResponse {\n  availability?: ScheduleAvailability;\n  bookingPolicySettings?: BookingPolicySettings;\n}\n\nexport interface ScheduleAvailability {\n  /** Total number of spots. */\n  totalSpots?: number | null;\n  /** Number of remaining open spots. */\n  openSpots?: number | null;\n  /** Indicators of booking policy violations for the specified schedule. */\n  bookingPolicyViolations?: BookingPolicyViolations;\n}\n\nexport interface MarkBookingAsPendingRequest {\n  /**\n   * ID of the booking to mark as `PENDING`.\n   * @format GUID\n   */\n  bookingId: string;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified when\n   * managing the booking.\n   */\n  revision: string | null;\n  /**\n   * Information about whether to notify the customer and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   */\n  sendSmsReminder?: boolean | null;\n  /** Payment status to set for the booking. */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability before updating the status.\n   */\n  flowControlSettings?: MarkBookingAsPendingFlowControlSettings;\n}\n\nexport interface MarkBookingAsPendingFlowControlSettings {\n  /**\n   * Whether to check for double bookings before updating the booking as pending.\n   *\n   * When passing `false`, a booking is only updated with status `PENDING`.\n   * Default: `false`.\n   */\n  checkAvailabilityValidation?: boolean;\n  /**\n   * Whether to validate that the booking to be marked as pending has a `booking.slot.serviceId`\n   * of a pending approval service.\n   *\n   * Default: `false`.\n   */\n  skipPendingApprovalServiceValidation?: boolean;\n}\n\nexport interface MarkBookingAsPendingResponse {\n  /** Updated booking. */\n  booking?: Booking;\n}\n\nexport interface BookingMarkedAsPending {\n  /** The booking object that was marked as pending. */\n  booking?: Booking;\n  /** Information about whether to notify the customer upon manual confirmation of the pending booking and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * The phone number is taken from `contactDetails.phone`.\n   */\n  sendSmsReminder?: boolean | null;\n  /** Whether this booking overlaps with another existing confirmed booking. */\n  doubleBooked?: boolean | null;\n  /** ID of the mark as pending initiator. */\n  initiatedBy?: IdentificationData;\n  /** The previous status of the booking. */\n  previousStatus?: BookingStatusWithLiterals;\n  /** The previous payment status of the booking. */\n  previousPaymentStatus?: PaymentStatusWithLiterals;\n}\n\nexport interface MigrationCheckIfClashesWithBlockedTimeRequest {\n  msidAndBookingIds?: MsidAndBookingId[];\n}\n\nexport interface MsidAndBookingId {\n  msid?: string;\n  bookingId?: string;\n}\n\nexport interface MigrationCheckIfClashesWithBlockedTimeResponse {\n  clashes?: Clash[];\n}\n\nexport interface Clash {\n  msid?: string;\n  bookingId?: string;\n  sessionId?: string;\n  resourceName?: string;\n  contactName?: string;\n}\n\nexport interface CountBookingsRequest {\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`\n   */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountBookingsResponse {\n  /** Number of bookings matching the specified filter. */\n  count?: number;\n}\n\nexport interface CreateMultiServiceBookingRequest {\n  /**\n   * Between 2 to 8 new single-service booking specifications to create and combine in a multi-service booking.\n   *\n   * Each single-service booking specification must include `slot` details (`scheduleId`, `startDate`, and `endDate`).\n   * Specify contact details, number of participants, and any additional fields as needed.\n   *\n   * For sequential bookings, ensure the timing allows services to be scheduled back-to-back.\n   * @minSize 2\n   * @maxSize 8\n   */\n  bookings: Booking[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * Wix Bookings takes the phone number from `contactDetails.phone`.\n   *\n   * Default: `true`\n   */\n  sendSmsReminder?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to skip checking availability before updating the status.\n   */\n  flowControlSettings?: CreateBookingFlowControlSettings;\n  /** Whether to return the created single-service bookings. */\n  returnFullEntity?: boolean;\n  /**\n   * Multi-service booking type.\n   *\n   * Currently only `SEQUENTIAL_BOOKINGS` is supported.\n   */\n  multiServiceBookingType?: MultiServiceBookingTypeWithLiterals;\n}\n\nexport interface CreateMultiServiceBookingResponse {\n  /**\n   * Created multi-service booking.\n   * Contains the single-service bookings in the same order as specified in the request.\n   */\n  multiServiceBooking?: MultiServiceBooking;\n}\n\n/**\n * A multi-service booking combines multiple single-service bookings into a package.\n *\n * Currently, multi-service bookings support only sequential scheduling where services are scheduled back-to-back at the same location, with each single-service booking starting when the previous 1 ends.\n */\nexport interface MultiServiceBooking {\n  /**\n   * Multi-service booking ID.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** The single-service bookings that make up the multi-service booking package. */\n  bookings?: BookingResult[];\n}\n\nexport interface BookingResult {\n  /**\n   * Booking ID.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /** Booking entity. */\n  booking?: Booking;\n}\n\nexport interface RescheduleMultiServiceBookingRequest {\n  /**\n   * ID of the multi-service booking to reschedule.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n  /** Information about the single-service bookings to reschedule. */\n  rescheduleBookingsInfo: RescheduleBookingInfo[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings rescheduling flow are changed.\n   * For example, whether the availability of the new slot is checked before rescheduling the booking or if the customer can reschedule the booking even though the service's rescheduling policy doesn't allow it.\n   */\n  flowControlSettings?: RescheduleBookingFlowControlSettings;\n  /** Whether to return the rescheduled bookings entities. */\n  returnFullEntity?: boolean;\n}\n\nexport interface RescheduleBookingInfo\n  extends RescheduleBookingInfoParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * Specify when all participants book the same variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices to book. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * Specify when not all participants book the same variant.\n   */\n  participantsChoices?: ParticipantChoices;\n  /**\n   * ID of the booking to reschedule.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /** Information about the new slot. */\n  slot?: V2Slot;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be specified when managing the booking.\n   */\n  revision?: string | null;\n}\n\n/** @oneof */\nexport interface RescheduleBookingInfoParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * Specify when all participants book the same variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices to book. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * Specify when not all participants book the same variant.\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\nexport interface RescheduleMultiServiceBookingResponse {\n  /** Rescheduled multi-service booking. */\n  multiServiceBooking?: MultiServiceBooking;\n}\n\nexport interface GetMultiServiceBookingAvailabilityRequest {\n  /**\n   * ID of the multi-service booking to retrieve.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n}\n\nexport interface GetMultiServiceBookingAvailabilityResponse {\n  /** Whether all contained single-service bookings are bookable. */\n  bookable?: boolean;\n  /** Total number of spots. */\n  totalCapacity?: number | null;\n  /** Number of open spots. */\n  remainingCapacity?: number | null;\n  /** Indicators for policy violations of the multi-service booking. */\n  policyViolations?: BookingPolicyViolations;\n  /** Multi-service booking policy settings. */\n  policySettings?: BookingPolicySettings;\n  /** Details of the multi-service booking. */\n  multiServiceBookingInfo?: GetMultiServiceBookingAvailabilityResponseBookingInfo[];\n}\n\nexport interface GetMultiServiceBookingAvailabilityResponseBookingInfo {\n  /**\n   * Booking ID.\n   * @format GUID\n   */\n  bookingId?: string | null;\n}\n\nexport interface CancelMultiServiceBookingRequest {\n  /**\n   * ID of the multi-service booking to cancel.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings cancellation flow are changed.\n   * For example, whether the customer can cancel the booking even though the service's cancellation policy doesn't allow it or whether to issue a refund upon cancellation.\n   */\n  flowControlSettings?: CancelBookingFlowControlSettings;\n  /** Whether to return the canceled single-service bookings. */\n  returnFullEntity?: boolean;\n}\n\nexport interface CancelMultiServiceBookingResponse {\n  /** Canceled multi-service booking. */\n  multiServiceBooking?: MultiServiceBooking;\n}\n\nexport interface MarkMultiServiceBookingAsPendingRequest {\n  /**\n   * ID of the multi-service booking to mark as `PENDING`.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n  /** Information about the single-service bookings to mark as `PENDING`. */\n  markAsPendingBookingsInfo?: BookingInfo[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * Wix Bookings takes the phone number from `contactDetails.phone`.\n   *\n   * Default: `true`\n   */\n  sendSmsReminder?: boolean | null;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to return the single-service bookings that were marked as `PENDING`.\n   *\n   * Default: `false`\n   */\n  returnFullEntity?: boolean;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings creation flow are changed.\n   * For example, whether Wix Bookings checks availability before updating the booking.\n   */\n  flowControlSettings?: MarkBookingAsPendingFlowControlSettings;\n}\n\nexport interface BookingInfo {\n  /**\n   * ID of the single-service booking.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be specified when managing the booking.\n   */\n  revision?: string | null;\n  /** Payment status to set for the single-service booking. */\n  paymentStatus?: PaymentStatusWithLiterals;\n}\n\nexport interface MarkMultiServiceBookingAsPendingResponse {\n  /** Updated multi-service booking. */\n  multiServiceBooking?: MultiServiceBooking;\n}\n\nexport interface ConfirmMultiServiceBookingRequest {\n  /**\n   * ID of the multi-service booking to confirm its related bookings.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n  /** Information about the single-service bookings to confirm. */\n  confirmBookingsInfo?: BookingInfo[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * Wix Bookings takes the phone number from `contactDetails.phone`.\n   *\n   * Default: `true`\n   */\n  sendSmsReminder?: boolean | null;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /** Whether to return the confirmed single-service bookings. */\n  returnFullEntity?: boolean;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings confirmation flow are changed.\n   * For example, whether Wix Bookings checks availability before confirming the booking.\n   */\n  flowControlSettings?: ConfirmBookingFlowControlSettings;\n}\n\nexport interface ConfirmMultiServiceBookingResponse {\n  /** Confirmed multi-service booking. */\n  multiServiceBooking?: MultiServiceBooking;\n}\n\nexport interface DeclineMultiServiceBookingRequest {\n  /**\n   * ID of the multi-service booking to decline.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n  /** Information about the single-service bookings to decline. */\n  declineBookingsInfo?: BookingInfo[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /** Whether to return the declined single-service bookings. */\n  returnFullEntity?: boolean;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings declining flow are changed.\n   * For example, whether to issue a refund upon cancellation.\n   */\n  flowControlSettings?: DeclineBookingFlowControlSettings;\n}\n\nexport interface DeclineMultiServiceBookingResponse {\n  /** Declined multi-service booking. */\n  multiServiceBooking?: MultiServiceBooking;\n}\n\nexport interface BulkGetMultiServiceBookingAllowedActionsRequest {\n  /**\n   * IDs of the multi-service bookings to retrieve allowed actions for.\n   * @format GUID\n   * @minSize 1\n   * @maxSize 50\n   */\n  multiServiceBookingIds: string[] | null;\n}\n\nexport interface BulkGetMultiServiceBookingAllowedActionsResponse {\n  /**\n   * Information about the multi-service bookings that were retrieved.\n   * Includes their ID, index in the bulk request and whether they were successfully processed.\n   */\n  results?: BulkCalculateAllowedActionsResult[];\n  /** Total number of successes and failures for Bulk Get Multi Service Booking Allowed Actions. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface GetMultiServiceBookingRequest {\n  /**\n   * ID of the multi-service booking.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n}\n\nexport interface GetMultiServiceBookingResponse {\n  /** Retrieved multi-service booking. */\n  multiServiceBooking?: MultiServiceBooking;\n  /** Details about how many single-service bookings belong to the multi-service booking. */\n  metadata?: MultiServiceBookingMetadata;\n}\n\nexport interface MultiServiceBookingMetadata {\n  /**\n   * Total number of `CONFIRMED` and `PENDING` single-service bookings belonging to the multi-service booking.\n   * The total includes the number of single-service bookings which couldn't be retrieved due to lack of permissions.\n   */\n  totalNumberOfScheduledBookings?: number | null;\n}\n\nexport interface AddBookingsToMultiServiceBookingRequest {\n  /**\n   * ID of the multi-service booking.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n  /**\n   * List of single-service booking IDs and their revision.\n   * @maxSize 8\n   */\n  bookings: BookingIdAndRevision[];\n  /**\n   * Whether to return the single-service bookings that were added to the multi-service booking.\n   *\n   * Default: `false`\n   */\n  returnFullEntity?: boolean;\n}\n\nexport interface BookingIdAndRevision {\n  /**\n   * ID of the single-service booking.\n   * @format GUID\n   */\n  bookingId?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   * To prevent conflicting changes, the current revision must be specified when managing the booking.\n   */\n  revision?: string | null;\n}\n\nexport interface AddBookingsToMultiServiceBookingResponse {\n  /** Single-service bookings that were added to the multi-service booking. */\n  bookings?: BookingResult[];\n}\n\nexport interface RemoveBookingsFromMultiServiceBookingRequest {\n  /**\n   * ID of the multi-service booking.\n   * @format GUID\n   */\n  multiServiceBookingId: string | null;\n  /**\n   * List of single-service booking IDs and their revision.\n   * @maxSize 8\n   */\n  bookings?: BookingIdAndRevision[];\n  /**\n   * Whether to return the single-service bookings.\n   *\n   * Default: `false`\n   */\n  returnFullEntity?: boolean;\n}\n\nexport interface RemoveBookingsFromMultiServiceBookingResponse {\n  /** Single-service bookings that were removed from the multi-service booking. */\n  bookings?: BookingResult[];\n}\n\n/** @docsIgnore */\nexport type ConfirmOrDeclineBookingApplicationErrors =\n  | {\n      code?: 'INVALID_BOOKING_STATUS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type BulkConfirmOrDeclineBookingApplicationErrors = {\n  code?: 'DUPLICATED_BOOKINGS';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type CreateBookingApplicationErrors =\n  | {\n      code?: 'SESSION_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SCHEDULE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_CUSTOM_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'VALIDATION_FAILURE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SESSION_CAPACITY_EXCEEDED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SCHEDULE_CAPACITY_EXCEEDED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SLOT_NOT_AVAILABLE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_VALIDATING_AVAILABILITY';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_POLICY_VIOLATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNAUTHORIZED_OPERATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_SERVICE_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_FLOW_SELECTED_RESOURCES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'CAN_NOT_CREATE_BOOKING_WITH_MULTI_SERVICE_BOOKING_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'CAN_NOT_SKIP_AVAILABILITY_VALIDATION_IF_RESOURCE_NOT_PROVIDED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_DATE_FORMAT';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_TIME_ZONE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'CONTACT_DETAILS_CONFLICT';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type BulkCreateBookingApplicationErrors =\n  | {\n      code?: 'SESSION_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SCHEDULE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'RESOURCE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_CUSTOM_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'VALIDATION_FAILURE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SLOT_NOT_AVAILABLE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'EMPTY_FORM_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNAUTHORIZED_OPERATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'CAN_NOT_SKIP_AVAILABILITY_VALIDATION_IF_RESOURCE_NOT_PROVIDED';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type RescheduleBookingApplicationErrors =\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_POLICY_VIOLATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNAUTHORIZED_OPERATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SESSION_CAPACITY_EXCEEDED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SCHEDULE_CAPACITY_EXCEEDED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SLOT_NOT_AVAILABLE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'VALIDATION_FAILURE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'EMPTY_FORM_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SESSION_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SCHEDULE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'RESOURCE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_VALIDATING_AVAILABILITY';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_SESSION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_SCHEDULE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_SERVICE_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_ALLOWED_TO_UPDATE_BOOKING_IN_MULTI_SERVICE_BOOKING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ADD_ON_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ADD_ON_CHANGES_NOT_ALLOWED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ADD_ON_GROUP_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'PRICE_CHANGE_NOT_ALLOWED';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type ConfirmBookingApplicationErrors =\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_CONFIRMING_NON_PENDING_BOOKING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_BOOKING_STATUS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NO_OPEN_SPOTS';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type SetBookingSubmissionIdApplicationErrors = {\n  code?: 'BOOKING_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type UpdateExtendedFieldsApplicationErrors = {\n  code?: 'BOOKING_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type DeclineBookingApplicationErrors =\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_DECLINE_NON_PENDING_BOOKING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_BOOKING_STATUS';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type CancelBookingApplicationErrors =\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_POLICY_VIOLATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'VALIDATION_FAILURE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FEATURE_LIMIT_EXCEEDED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'REFUND_NOT_ALLOWED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_ALLOWED_TO_UPDATE_BOOKING_IN_MULTI_SERVICE_BOOKING';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type UpdateNumberOfParticipantsApplicationErrors = {\n  code?: 'BOOKING_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type MarkBookingAsPendingApplicationErrors =\n  | {\n      code?: 'BOOKING_MARK_BOOKING_AS_PENDING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NO_OPEN_SPOTS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_PENDING_APPROVAL_SERVICE';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type CreateMultiServiceBookingApplicationErrors =\n  | {\n      code?: 'VALIDATION_FAILURE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SCHEDULE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'RESOURCE_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'EMPTY_FORM_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_CUSTOM_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_CHOICES';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SLOT_NOT_AVAILABLE';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'UNAUTHORIZED_OPERATION';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'CAN_NOT_SKIP_AVAILABILITY_VALIDATION_IF_RESOURCE_NOT_PROVIDED';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type RescheduleMultiServiceBookingApplicationErrors =\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'REVISION_MISSING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_ALL_BOOKINGS_WERE_SENT';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'DUPLICATE_BOOKING_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SOME_BOOKINGS_UPDATES_FAILED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_REVISION_MISMATCH';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NO_OPEN_SPOTS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type GetMultiServiceBookingAvailabilityApplicationErrors = {\n  code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type CancelMultiServiceBookingApplicationErrors =\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ALL_BOOKINGS_ARE_ALREADY_DECLINED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ALL_BOOKINGS_ARE_ALREADY_CANCELED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type MarkMultiServiceBookingAsPendingApplicationErrors =\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'REVISION_MISSING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_ALL_BOOKINGS_WERE_SENT';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'DUPLICATE_BOOKING_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SOME_BOOKINGS_UPDATES_FAILED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_REVISION_MISMATCH';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NO_OPEN_SPOTS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type ConfirmMultiServiceBookingApplicationErrors =\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'REVISION_MISSING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_BOOKING_STATUS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ALL_BOOKINGS_ARE_ALREADY_CONFIRMED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_ALL_BOOKINGS_WERE_SENT';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'DUPLICATE_BOOKING_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SOME_BOOKINGS_UPDATES_FAILED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_REVISION_MISMATCH';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NO_OPEN_SPOTS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_ALL_BOOKINGS_HAVE_START_AND_END_DATE';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type DeclineMultiServiceBookingApplicationErrors =\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'REVISION_MISSING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'INVALID_BOOKING_STATUS';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'ALL_BOOKINGS_ARE_ALREADY_DECLINED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'NOT_ALL_BOOKINGS_WERE_SENT';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'DUPLICATE_BOOKING_INFO';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'SOME_BOOKINGS_UPDATES_FAILED';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_REVISION_MISMATCH';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type AddBookingsToMultiServiceBookingApplicationErrors =\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_REVISION_MISMATCH';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_ALREADY_PART_OF_ANOTHER_MULTI_SERVICE_BOOKING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_ALREADY_INCLUDES_BOOKING';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_TO_ADD_STATUS_NOT_VALID';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type RemoveBookingsFromMultiServiceBookingApplicationErrors =\n  | {\n      code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_NOT_FOUND';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'BOOKING_REVISION_MISMATCH';\n      description?: string;\n      data?: Record<string, any>;\n    };\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: WebhooksIdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface BookingCanceledEnvelope {\n  data: BookingCanceled;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking is canceled.\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Read bookings calendar - including participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS\n * @permissionId BOOKINGS.BOOKING_READ\n * @webhook\n * @eventType wix.bookings.v2.booking_canceled\n * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings\n * @slug canceled\n */\nexport declare function onBookingCanceled(\n  handler: (event: BookingCanceledEnvelope) => void | Promise<void>\n): void;\n\nexport interface BookingConfirmedEnvelope {\n  data: BookingConfirmed;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking is confirmed.\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Read bookings calendar - including participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS\n * @permissionId BOOKINGS.BOOKING_READ\n * @webhook\n * @eventType wix.bookings.v2.booking_confirmed\n * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings\n * @slug confirmed\n */\nexport declare function onBookingConfirmed(\n  handler: (event: BookingConfirmedEnvelope) => void | Promise<void>\n): void;\n\nexport interface BookingCreatedEnvelope {\n  entity: Booking;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking is created.\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Read bookings calendar - including participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS\n * @permissionId BOOKINGS.READ_BOOKINGS\n * @permissionId BOOKINGS.BOOKING_READ\n * @webhook\n * @eventType wix.bookings.v2.booking_created\n * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings\n * @slug created\n */\nexport declare function onBookingCreated(\n  handler: (event: BookingCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface BookingDeclinedEnvelope {\n  data: BookingDeclined;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking is declined.\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Read bookings calendar - including participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS\n * @permissionId BOOKINGS.BOOKING_READ\n * @webhook\n * @eventType wix.bookings.v2.booking_declined\n * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings\n * @slug declined\n */\nexport declare function onBookingDeclined(\n  handler: (event: BookingDeclinedEnvelope) => void | Promise<void>\n): void;\n\nexport interface BookingNumberOfParticipantsUpdatedEnvelope {\n  data: NumberOfParticipantsUpdated;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when the number of participants is updated.\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Read bookings calendar - including participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS\n * @permissionId BOOKINGS.BOOKING_READ\n * @webhook\n * @eventType wix.bookings.v2.booking_number_of_participants_updated\n * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings\n * @slug number_of_participants_updated\n */\nexport declare function onBookingNumberOfParticipantsUpdated(\n  handler: (\n    event: BookingNumberOfParticipantsUpdatedEnvelope\n  ) => void | Promise<void>\n): void;\n\nexport interface BookingRescheduledEnvelope {\n  data: BookingRescheduled;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booking is rescheduled.\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Read bookings calendar - including participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS\n * @permissionId BOOKINGS.BOOKING_READ\n * @webhook\n * @eventType wix.bookings.v2.booking_rescheduled\n * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings\n * @slug rescheduled\n */\nexport declare function onBookingRescheduled(\n  handler: (event: BookingRescheduledEnvelope) => void | Promise<void>\n): void;\n\nexport interface BookingUpdatedEnvelope {\n  entity: Booking;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a booked schedule is updated.\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionScope Read bookings calendar - including participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS\n * @permissionId BOOKINGS.READ_BOOKINGS\n * @permissionId BOOKINGS.BOOKING_READ\n * @webhook\n * @eventType wix.bookings.v2.booking_updated\n * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings\n * @slug updated\n */\nexport declare function onBookingUpdated(\n  handler: (event: BookingUpdatedEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Updates the booking `status` to `CONFIRMED`, `PENDING`, or `DECLINED` based\n * on the `paymentStatus` you provide, double booking conflicts, and whether\n * the service requires business approval.\n *\n * ## eCommerce checkout restriction\n *\n * Call this method only when using a custom checkout page. Don't\n * call it when using a *Wix eCommerce checkout*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n * In such cases, Wix automatically updates the booking status based on\n * the `paymentStatus` of the corresponding *Wix eCommerce order*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * ## New booking status\n *\n * The booking `status` is set to `DECLINED` if both of the following conditions\n * are met:\n * + You provide `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT` as payment status.\n * + There is a double booking conflict.\n *\n * If only one or none of these conditions is met, `status` is set to `PENDING`\n * or `CONFIRMED` depending on whether the service requires business approval.\n *\n * ## Double bookings\n *\n * If there is a double booking conflict, but the booking has already been at least\n * partially paid, the method still marks the booking as `PENDING` or `CONFIRMED`.\n * Then, it also sets `doubleBooked` to `true`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences in confirmation behavior if the\n * booking was created with special `flowControlSettings`:\n * + If the booking's `flowControlSettings.skipAvailabilityValidation` was set to\n * `true`, the booking is never declined regardless of double booking conflicts.\n * Instead, it's marked `CONFIRMED` or `PENDING`, depending on whether the\n * service requires business approval.\n * + If the booking's `flowControlSettings.skipBusinessConfirmation` was set to\n * `true`, the booking skips `PENDING` status and is marked `CONFIRMED`\n * immediately.\n * @param bookingId - ID of the booking to confirm or decline.\n * @public\n * @requiredField bookingId\n * @permissionId BOOKINGS.BOOKING_CONFIRM_OR_DECLINE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.confirmator.v2.Confirmator.ConfirmOrDeclineBooking\n */\nexport async function confirmOrDeclineBooking(\n  bookingId: string,\n  options?: ConfirmOrDeclineBookingOptions\n): Promise<\n  NonNullablePaths<\n    ConfirmOrDeclineBookingResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: ConfirmOrDeclineBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    paymentStatus: options?.paymentStatus,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.confirmOrDeclineBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingId: '$[0]',\n          paymentStatus: '$[1].paymentStatus',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ConfirmOrDeclineBookingOptions {\n  /**\n   * Current payment status of the booking when using a custom checkout page and\n   * not the *eCommerce checkout*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n   *\n   * The booking is declined if there is a double booking conflict and you provide\n   * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.\n   */\n  paymentStatus?: PaymentStatusWithLiterals;\n}\n\n/**\n * Confirms or declines up to 300 bookings.\n *\n *\n * See *Confirm Or Decline Booking*\n * ([SDK](https://dev.wix.com/docs/velo/api-reference/wix-bookings-v2/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking))\n * for details about when a booking is confirmed or declined.\n * @param details - Bookings to confirm or decline.\n * @public\n * @requiredField details\n * @requiredField details.bookingId\n * @permissionId BOOKINGS.BOOKING_CONFIRM_OR_DECLINE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.confirmator.v2.Confirmator.BulkConfirmOrDeclineBooking\n */\nexport async function bulkConfirmOrDeclineBooking(\n  details: NonNullablePaths<\n    BulkConfirmOrDeclineBookingRequestBookingDetails,\n    `bookingId`,\n    2\n  >[],\n  options?: BulkConfirmOrDeclineBookingOptions\n): Promise<\n  NonNullablePaths<\n    BulkConfirmOrDeclineBookingResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `results.${number}.item.totalParticipants`\n    | `results.${number}.item.status`\n    | `results.${number}.item.paymentStatus`\n    | `results.${number}.item.selectedPaymentOption`\n    | `results.${number}.item.createdBy.anonymousVisitorId`\n    | `results.${number}.item.createdBy.memberId`\n    | `results.${number}.item.createdBy.wixUserId`\n    | `results.${number}.item.createdBy.appId`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  > & {\n    __applicationErrorsType?: BulkConfirmOrDeclineBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    details: details,\n    returnEntity: options?.returnEntity,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.bulkConfirmOrDeclineBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          details: '$[0]',\n          returnEntity: '$[1].returnEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['details', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkConfirmOrDeclineBookingOptions {\n  /** Whether to return the confirmed or declined booking objects. */\n  returnEntity?: boolean;\n}\n\n/**\n * Creates a booking.\n *\n *\n * ## Appointment booking\n *\n * For appointment-based services, specify the relevant `slot` in\n * `bookedEntity.slot`. We recommend specifying the complete\n * `availabilityEntries.slot` returned in Query Availability\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * in your call's request to avoid failed calls due to unavailability.\n *\n * ## Class session booking\n *\n * For class services, specify the relevant event ID\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * as `bookedEntity.slot.eventId`.\n * We recommend retrieving the event ID from Query Availability's\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * `availabilityEntries.slot.eventId` to avoid failed calls due to unavailability.\n * Specifying an event ID leads to automatic calculations of `slot.startDate`, `slot.endDate`,\n * `slot.timezone`, `slot.resource`, and `slot.location`. All manually specified\n * values are overridden.\n *\n * ## Course booking\n *\n * For course services, specify the course's schedule ID in `bookedEntity.schedule.scheduleId`.\n * We recommend following [this sample flow](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course)\n * to minimize failed calls due to unavailability.\n *\n * ## Related resources\n *\n * Specifying a `resource` triggers an availability check, resulting in a failed\n * call if the resource is unavailable. Omitting a resource allows Wix Bookings\n * to assign a resource belonging to the relevant type randomly when the merchant\n * confirms the booking.\n *\n * ## Participant information\n *\n * You must specify either `participantsChoices` or `totalParticipants`.\n * The call fails if the specified `participantsChoices` aren't among the supported\n * _service options and variants_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer that's send\n * immediately. Ensure `participantNotification.notifyParticipants` is set to `true`\n * to send the message.\n *\n * If you specify `{\"sendSmsReminder\": true}`, the customer receives an SMS 24 hours\n * before the session starts. The phone number is taken from `contactDetails.phone`.\n *\n * ## Booking status\n *\n * Bookings default to the `CREATED` status, not affecting the business calendar\n * or resource availability. You can specify a different status when the calling\n * [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities)\n * has `Manage Bookings` permissions.\n *\n * ## Payment options\n *\n * The specified `selectedPaymentOption` indicates how the customer intends to\n * pay, allowing for later changes to a different method supported by the service.\n *\n * ## Payment status\n *\n * A booking is initially created with `{\"paymentStatus\": \"UNDEFINED\"}` regardless\n * of the payment status specified in Create Booking. If a customer uses an\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),\n * Wix Bookings automatically syncs the booking's payment status from\n * the corresponding eCommerce order\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * If a booking doesn't have a corresponding eCommerce order, for example, since\n * the customer didn't use the eCommerce checkout, you can update the booking's\n * payment status with Confirm Or Decline Booking\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).\n *\n * ## Booking form data\n *\n * When customers sign up for a service, they must fill out the booking form.\n * To create a booking with a completed booking form, specify the relevant data in\n * `formSubmission`. Ensure the values of the corresponding fields in\n * `booking.contactDetails` and `formSubmission` are identical. If these values\n * don't match, Create Booking fails. Therefore, we recommend specifying\n * only `booking.contactDetails.contactId` when providing `formSubmission`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences when you specify special\n * `flowControlSettings`:\n *\n * - `{\"skipAvailabilityValidation\": true}`: The call succeeds\n * regardless of availability. If you don't specify any resource, the call\n * succeeds even if no resource of the relevant type is available.\n * - `{\"skipBusinessConfirmation\": true}`: Automatically confirms `PENDING`\n * bookings that require manual confirmation.\n * - `{\"skipSelectedPaymentOptionValidation\": true}`: Allows customers to pay\n * with payment methods that aren't supported for the service.\n *\n * When using special `flowControlSettings`, ensure you have sufficient\n * permissions. If you encounter failed calls due to insufficient permissions,\n * consider the following options:\n *\n * - **App developers** can use a higher\n * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n * - **Site developers** can utilize\n * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n *\n * Granting additional permissions and using elevation permits method calls that\n * would typically fail due to authorization checks. Therefore, you should use\n * them intentionally and securely.\n * @param booking - The booking to create.\n * @public\n * @requiredField booking\n * @requiredField booking.additionalFields._id\n * @requiredField booking.bookedEntity\n * @permissionId BOOKINGS.BOOKING_CREATE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.CreateBooking\n */\nexport async function createBooking(\n  booking: NonNullablePaths<\n    Booking,\n    `additionalFields.${number}._id` | `bookedEntity`,\n    4\n  >,\n  options?: CreateBookingOptions\n): Promise<\n  NonNullablePaths<\n    CreateBookingResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: CreateBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    booking: booking,\n    participantNotification: options?.participantNotification,\n    sendSmsReminder: options?.sendSmsReminder,\n    flowControlSettings: options?.flowControlSettings,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Booking.createBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          booking: '$[0]',\n          participantNotification: '$[1].participantNotification',\n          sendSmsReminder: '$[1].sendSmsReminder',\n          flowControlSettings: '$[1].flowControlSettings',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['booking', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CreateBookingOptions {\n  /**\n   * Information about whether to notify the customer and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   * Default: `true`.\n   */\n  sendSmsReminder?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability when creating a booking.\n   */\n  flowControlSettings?: CreateBookingFlowControlSettings;\n}\n\n/**\n * Creates up to 8 bookings.\n *\n *\n * See Create Booking\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking))\n * for more information.\n *\n * If any of the specified bookings is missing a required field the entire call\n * fails.\n *\n * If you specify 1 or more unavailable bookings, the call succeeds\n * while the unavailable bookings aren't created. Instead, they're counted as\n * failures in the returned `bulkActionMetadata`.\n * @param createBookingsInfo - Bookings to create.\n *\n * Max: 8 bookings\n * @public\n * @requiredField createBookingsInfo\n * @requiredField createBookingsInfo.booking\n * @requiredField createBookingsInfo.booking.additionalFields._id\n * @requiredField createBookingsInfo.booking.bookedEntity\n * @permissionId BOOKINGS.BOOKING_CREATE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.BulkCreateBooking\n */\nexport async function bulkCreateBooking(\n  createBookingsInfo: NonNullablePaths<\n    CreateBookingInfo,\n    | `booking`\n    | `booking.additionalFields.${number}._id`\n    | `booking.bookedEntity`,\n    5\n  >[],\n  options?: BulkCreateBookingOptions\n): Promise<\n  NonNullablePaths<\n    BulkCreateBookingResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `results.${number}.item.totalParticipants`\n    | `results.${number}.item.status`\n    | `results.${number}.item.paymentStatus`\n    | `results.${number}.item.selectedPaymentOption`\n    | `results.${number}.item.createdBy.anonymousVisitorId`\n    | `results.${number}.item.createdBy.memberId`\n    | `results.${number}.item.createdBy.wixUserId`\n    | `results.${number}.item.createdBy.appId`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  > & {\n    __applicationErrorsType?: BulkCreateBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    createBookingsInfo: createBookingsInfo,\n    returnFullEntity: options?.returnFullEntity,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Booking.bulkCreateBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          createBookingsInfo: '$[0]',\n          returnFullEntity: '$[1].returnFullEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['createBookingsInfo', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkCreateBookingOptions {\n  /** Whether to return the created bookings. */\n  returnFullEntity?: boolean;\n}\n\n/**\n * Reschedules an appointment booking to a different slot or a class booking to\n * a different session.\n *\n *\n * ## Course booking limitation\n *\n * You can't reschedule course bookings.\n *\n * ## Appointment sessions\n *\n * For appointments, the old session is removed from the business calendar\n * while a new session is added. We recommend calling Query Availability\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * first and specifying the entire retrieved `slot`.\n *\n * ## Class sessions\n *\n * For classes, the new session must be an existing session belonging to the\n * same class. We recommend retrieving `availabilityEntries.slot.eventId`\n * from Query Availability\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * to avoid failed Reschedule Booking calls due to unavailability. Specify\n * only `slot.eventId` instead of the entire `slot` object.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences when you specify special\n * `flowControlSettings`:\n *\n * - `{\"ignoreReschedulePolicy\": true}`: The call succeeds even if the\n * service's `reschedulePolicy` doesn't allow it.\n * - `{\"skipAvailabilityValidation\": true}`: The call succeeds even if\n * the specified session, slot, or resource isn't available. If you don't\n * specify any resource, the call succeeds even if no resource of the relevant\n * type is available.\n * - `{\"skipBusinessConfirmation\": true}`: Any `PENDING` booking is\n * automatically confirmed even if the services requires the merchants's\n * manual confirmation.\n *\n * When using special `flowControlSettings`, ensure you have sufficient\n * permissions. If you encounter failed calls due to insufficient permissions,\n * consider the following options:\n *\n * - **App developers** can use a higher\n * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n * - **Site developers** can utilize\n * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n *\n * Granting additional permissions and using elevation permits method calls that\n * would typically fail due to authorization checks. Therefore, you should use\n * them intentionally and securely.\n * @param bookingId - ID of the booking to reschedule.\n * @param slot - New slot of the booking.\n * @public\n * @requiredField bookingId\n * @requiredField options.revision\n * @requiredField slot\n * @param options - An object representing the available options for rescheduling a booking.\n * @permissionId BOOKINGS.BOOKING_RESCHEDULE\n * @applicableIdentity APP\n * @applicableIdentity MEMBER\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.RescheduleBooking\n */\nexport async function rescheduleBooking(\n  bookingId: string,\n  slot: V2Slot,\n  options?: NonNullablePaths<RescheduleBookingOptions, `revision`, 2>\n): Promise<\n  NonNullablePaths<\n    RescheduleBookingResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: RescheduleBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    slot: slot,\n    revision: options?.revision,\n    participantNotification: options?.participantNotification,\n    flowControlSettings: options?.flowControlSettings,\n    totalParticipants: options?.totalParticipants,\n    participantsChoices: options?.participantsChoices,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Booking.rescheduleBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingId: '$[0]',\n          slot: '$[1]',\n          revision: '$[2].revision',\n          participantNotification: '$[2].participantNotification',\n          flowControlSettings: '$[2].flowControlSettings',\n          totalParticipants: '$[2].totalParticipants',\n          participantsChoices: '$[2].participantsChoices',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'slot', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface RescheduleBookingOptions\n  extends RescheduleBookingOptionsParticipantsInfoOneOf {\n  /**\n   * Revision number, which increments by 1 each time the booking is rescheduled.\n   * To prevent conflicting changes, the current revision must be passed when\n   * rescheduling the booking.\n   */\n  revision: string | null;\n  /**\n   * Information about whether to notify the customer about the rescheduling and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability when rescheduling a booking.\n   */\n  flowControlSettings?: RescheduleBookingFlowControlSettings;\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices to book. Specify when not all\n   * participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\n/** @oneof */\nexport interface RescheduleBookingOptionsParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices to book. Specify when not all\n   * participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\n/**\n * Updates the booking status to `CONFIRMED` without checking whether the relevant slot or schedule is still available.\n *\n *\n * ## eCommerce checkout restriction\n *\n * Call this method only when using a custom checkout page. Don't\n * call it when using a Wix eCommerce checkout\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n * In such cases, Wix automatically updates the booking status based on\n * the `paymentStatus` of the corresponding Wix eCommerce order\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * ## When to call Confirm Or Decline Booking instead\n *\n * Confirm Booking doesn't check whether a slot or schedule is still available.\n * For these checks, call Confirm or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) instead.\n *\n * ## Original status validation\n *\n * You can only confirm bookings with a status of `PENDING`, `CREATED`, or\n * `WAITING_LIST`.\n *\n * ## Double bookings\n *\n * Confirm Booking doesn't check whether a slot or schedule is still available.\n * You can specify\n *\n * ```json\n * {\n * \"flowControlSettings\": {\n * \"checkAvailabilityValidation\": true\n * },\n * \"doubleBooked\": true\n * }\n * ```\n * to forcefully set the booking's `doubleBooked` flag to `true`, regardless of\n * a potential double booking conflict. You must call with `Manage Bookings`\n * permissions to do so. For the default flow control settings\n * `{\"checkAvailabilityValidation\": false}`, the specified `doubleBooked` value\n * is ignored.\n *\n * ## Payment status\n *\n * Also updates the booking's `paymentStatus`, if you specify a new payment\n * status.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n * @param bookingId - ID of the booking to confirm.\n * @param revision - Revision number, which increments by 1 each time the booking is updated.\n * To prevent conflicting changes, the current revision must be passed when\n * updating the booking.\n * @public\n * @requiredField bookingId\n * @requiredField revision\n * @param options - An object representing the available options for canceling a booking.\n * @permissionId BOOKINGS.BOOKING_CONFIRM\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.ConfirmBooking\n */\nexport async function confirmBooking(\n  bookingId: string,\n  revision: string,\n  options?: ConfirmBookingOptions\n): Promise<\n  NonNullablePaths<\n    ConfirmBookingResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: ConfirmBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    revision: revision,\n    participantNotification: options?.participantNotification,\n    sendSmsReminder: options?.sendSmsReminder,\n    paymentStatus: options?.paymentStatus,\n    doubleBooked: options?.doubleBooked,\n    flowControlSettings: options?.flowControlSettings,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.bookingsConfirmBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingId: '$[0]',\n          revision: '$[1]',\n          participantNotification: '$[2].participantNotification',\n          sendSmsReminder: '$[2].sendSmsReminder',\n          paymentStatus: '$[2].paymentStatus',\n          doubleBooked: '$[2].doubleBooked',\n          flowControlSettings: '$[2].flowControlSettings',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'revision', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ConfirmBookingOptions {\n  /**\n   * Information about whether to notify the customer about the confirmation and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   */\n  sendSmsReminder?: boolean | null;\n  /** Payment status to set for the booking. */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability when confirming a booking.\n   */\n  flowControlSettings?: ConfirmBookingFlowControlSettings;\n}\n\n/**\n * Setting `submission_id` for a booking after the form submission is created.\n * @param bookingId - ID of the booking to set `submissionId` for.\n * @param submissionId - ID of the form submission to set on the booking.\n * @public\n * @documentationMaturity preview\n * @requiredField bookingId\n * @requiredField submissionId\n * @permissionId BOOKINGS.BOOKING_SET_SUBMISSION_ID\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.SetBookingSubmissionId\n */\nexport async function setBookingSubmissionId(\n  bookingId: string,\n  submissionId: string\n): Promise<\n  NonNullablePaths<\n    SetBookingSubmissionIdResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: SetBookingSubmissionIdApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    submissionId: submissionId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.setBookingSubmissionId(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { bookingId: '$[0]', submissionId: '$[1]' },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'submissionId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates the extended fields for a booking.\n *\n *\n * If you specify an extended field `namespace` that doesn't exist yet, it's\n * created.\n *\n * Learn more about [extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/about-backend-extensions#schema-plugin-extensions).\n * @public\n * @requiredField _id\n * @requiredField namespace\n * @requiredField options\n * @requiredField options.namespaceData\n * @param _id - ID of the booking for which to update extended fields.\n * @param namespace - [Namespace](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-reading-and-writing-schema-plugin-fields#namespaces) of the app for which to update extended fields.\n * @param options - Options for updating the booking's extended fields.\n * @permissionId BOOKINGS.BOOKING_UPDATE_EXTENDED_FIELDS\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.UpdateExtendedFields\n */\nexport async function updateExtendedFields(\n  _id: string,\n  namespace: string,\n  options: NonNullablePaths<UpdateExtendedFieldsOptions, `namespaceData`, 2>\n): Promise<\n  NonNullablePaths<UpdateExtendedFieldsResponse, `namespace`, 2> & {\n    __applicationErrorsType?: UpdateExtendedFieldsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    id: _id,\n    namespace: namespace,\n    namespaceData: options?.namespaceData,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Booking.updateExtendedFields(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          id: '$[0]',\n          namespace: '$[1]',\n          namespaceData: '$[2].namespaceData',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'namespace', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateExtendedFieldsOptions {\n  /** Data to update. Structured according to the [schema](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields#json-schema-for-extended-fields) defined when the extended fields were configured. */\n  namespaceData: Record<string, any> | null;\n}\n\n/**\n * Updates the booking status to `DECLINED` and updates the relevant session's\n * `participants.approvalStatus` to `DECLINED` without checking whether the relevant\n * slot or schedule is still available.\n *\n *\n * ## eCommerce checkout restriction\n *\n * Call this method only when using a custom checkout page. Don't\n * call it when using a Wix eCommerce checkout\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n * In such cases, Wix automatically updates the booking status based on\n * the `paymentStatus` of the corresponding Wix eCommerce order\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * ## When to call Confirm Or Decline Booking instead\n *\n * The method doesn't check whether a slot or schedule is still available. For\n * these checks you can call Confirm or Decline Booking\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).\n *\n * ## Original status validation\n *\n * You can only decline bookings with a `status` of `PENDING`, `CREATED`, or\n * `WAITING_LIST`.\n *\n * ## Payment status\n *\n * Also updates the booking's `paymentStatus`, if you specify a new payment\n * status.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n * @param bookingId - ID of the booking to decline.\n * @param revision - Revision number, which increments by 1 each time the booking is updated.\n *\n * To prevent conflicting changes, the current revision must be specified when\n * declining the booking.\n * @public\n * @requiredField bookingId\n * @requiredField revision\n * @param options - An object representing the available options for declining a booking.\n * @permissionId BOOKINGS.BOOKING_DECLINE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.DeclineBooking\n */\nexport async function declineBooking(\n  bookingId: string,\n  revision: string,\n  options?: DeclineBookingOptions\n): Promise<\n  NonNullablePaths<\n    DeclineBookingResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: DeclineBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    revision: revision,\n    participantNotification: options?.participantNotification,\n    paymentStatus: options?.paymentStatus,\n    doubleBooked: options?.doubleBooked,\n    flowControlSettings: options?.flowControlSettings,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.bookingsDeclineBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingId: '$[0]',\n          revision: '$[1]',\n          participantNotification: '$[2].participantNotification',\n          paymentStatus: '$[2].paymentStatus',\n          doubleBooked: '$[2].doubleBooked',\n          flowControlSettings: '$[2].flowControlSettings',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'revision', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DeclineBookingOptions {\n  /**\n   * Information about whether to notify the customer and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /** Payment status to set for the booking. */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to refund a declined booking.\n   */\n  flowControlSettings?: DeclineBookingFlowControlSettings;\n}\n\n/**\n * Updates the booking status to `CANCELED`.\n *\n *\n * ## Appointments\n *\n * For appointments, the corresponding event is removed from the Bookings\n * calendar.\n *\n * ## Class and course bookings\n *\n * For class or course bookings, the relevant participants are removed\n * from the class session or the course. However, the class session or course\n * remain on the business calendar.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences when you specify special\n * `flowControlSettings`:\n *\n * - `{\"ignoreCancellationPolicy\": true}`: The call succeeds even if the\n * service's `cancellationPolicy` doesn't allow it.\n * - `{\"withRefund\": true}`: The customer is refunded even if the service's\n * `refundPolicy` doesn't allow it.\n * - `{\"waiveCancellationFee\": true}`: The customer doesn't have to pay\n * the cancellation fee, even if the service's `cancellationPolicy` requires it.\n *\n * When using special `flowControlSettings`, ensure you have sufficient\n * permissions. If you encounter failed calls due to insufficient permissions,\n * consider the following options:\n *\n * - **App developers** can use a higher\n * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n * - **Site developers** can utilize\n * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n *\n * Granting additional permissions and using elevation permits method calls that\n * would typically fail due to authorization checks. Therefore, you should use\n * them intentionally and securely.\n * @param bookingId - ID of the booking to cancel.\n * @public\n * @requiredField bookingId\n * @requiredField options.revision\n * @param options - An object representing the available options for canceling a booking.\n * @permissionId BOOKINGS.BOOKING_CANCEL\n * @applicableIdentity APP\n * @applicableIdentity MEMBER\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.CancelBooking\n */\nexport async function cancelBooking(\n  bookingId: string,\n  options?: NonNullablePaths<CancelBookingOptions, `revision`, 2>\n): Promise<\n  NonNullablePaths<\n    CancelBookingResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: CancelBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    participantNotification: options?.participantNotification,\n    flowControlSettings: options?.flowControlSettings,\n    revision: options?.revision,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Booking.bookingsCancelBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingId: '$[0]',\n          participantNotification: '$[1].participantNotification',\n          flowControlSettings: '$[1].flowControlSettings',\n          revision: '$[1].revision',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CancelBookingOptions {\n  /**\n   * Information about whether to notify the customer about the cancellation and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to allow a cancellation even though the service's\n   * policy doesn't allow it.\n   */\n  flowControlSettings?: CancelBookingFlowControlSettings;\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified when\n   * managing the booking.\n   */\n  revision: string | null;\n}\n\n/**\n * Updates the number of participants for a class or course booking and changes\n * the `totalNumberOfParticipants` for the relevant sessions.\n *\n *\n * ## Appointment limitation\n *\n * You can't update the number of participants for appointment bookings.\n *\n * ## Participant information\n *\n * You must specify either `participantsChoices` or `totalParticipants`.\n * The call fails if the specified `participantsChoices` aren't among the\n * supported service options and variants\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n * @param bookingId - ID of the booking to update the number of participants for.\n * @public\n * @requiredField bookingId\n * @requiredField options.revision\n * @permissionId BOOKINGS.NUMBER_OF_PARTICIPANTS_UPDATE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.UpdateNumberOfParticipants\n */\nexport async function updateNumberOfParticipants(\n  bookingId: string,\n  options?: NonNullablePaths<UpdateNumberOfParticipantsOptions, `revision`, 2>\n): Promise<\n  NonNullablePaths<\n    UpdateNumberOfParticipantsResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: UpdateNumberOfParticipantsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    revision: options?.revision,\n    totalParticipants: options?.totalParticipants,\n    participantsChoices: options?.participantsChoices,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.bookingsUpdateNumberOfParticipants(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingId: '$[0]',\n          revision: '$[1].revision',\n          totalParticipants: '$[1].totalParticipants',\n          participantsChoices: '$[1].participantsChoices',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateNumberOfParticipantsOptions\n  extends UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf {\n  /**\n   * Revision number, which increments by 1 each time the booking is updated.\n   *\n   * To prevent conflicting changes, the current revision must be specified\n   * when updating the booking.\n   */\n  revision: string | null;\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices the participants have booked. Specify\n   * when not all participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\n/** @oneof */\nexport interface UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf {\n  /**\n   * Total number of participants. Specify when all participants have booked the\n   * same service variant.\n   */\n  totalParticipants?: number;\n  /**\n   * Information about the service choices the participants have booked. Specify\n   * when not all participants have booked the same service variant.\n   */\n  participantsChoices?: ParticipantChoices;\n}\n\n/** @param bookingId - ID of the booking to mark as `PENDING`.\n * @param revision - Revision number, which increments by 1 each time the booking is updated.\n *\n * To prevent conflicting changes, the current revision must be specified when\n * managing the booking.\n * @public\n * @documentationMaturity preview\n * @requiredField bookingId\n * @requiredField revision\n * @permissionId BOOKINGS.BOOKING_MARK_BOOKING_AS_PENDING\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.Bookings.MarkBookingAsPending\n */\nexport async function markBookingAsPending(\n  bookingId: string,\n  revision: string,\n  options?: MarkBookingAsPendingOptions\n): Promise<\n  NonNullablePaths<\n    MarkBookingAsPendingResponse,\n    | `booking.totalParticipants`\n    | `booking.participantsChoices.serviceChoices`\n    | `booking.bookedEntity.slot.serviceId`\n    | `booking.bookedEntity.slot.scheduleId`\n    | `booking.bookedEntity.slot.resource._id`\n    | `booking.bookedEntity.slot.location.locationType`\n    | `booking.bookedEntity.schedule.scheduleId`\n    | `booking.bookedEntity.tags`\n    | `booking.contactDetails.fullAddress.streetAddress.number`\n    | `booking.contactDetails.fullAddress.streetAddress.name`\n    | `booking.contactDetails.fullAddress.streetAddress.apt`\n    | `booking.contactDetails.fullAddress.subdivisions`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.code`\n    | `booking.contactDetails.fullAddress.subdivisions.${number}.name`\n    | `booking.additionalFields`\n    | `booking.additionalFields.${number}._id`\n    | `booking.additionalFields.${number}.valueType`\n    | `booking.status`\n    | `booking.paymentStatus`\n    | `booking.selectedPaymentOption`\n    | `booking.createdBy.anonymousVisitorId`\n    | `booking.createdBy.memberId`\n    | `booking.createdBy.wixUserId`\n    | `booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: MarkBookingAsPendingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookingId: bookingId,\n    revision: revision,\n    participantNotification: options?.participantNotification,\n    sendSmsReminder: options?.sendSmsReminder,\n    paymentStatus: options?.paymentStatus,\n    doubleBooked: options?.doubleBooked,\n    flowControlSettings: options?.flowControlSettings,\n  });\n\n  const reqOpts = ambassadorWixBookingsV2Booking.markBookingAsPending(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookingId: '$[0]',\n          revision: '$[1]',\n          participantNotification: '$[2].participantNotification',\n          sendSmsReminder: '$[2].sendSmsReminder',\n          paymentStatus: '$[2].paymentStatus',\n          doubleBooked: '$[2].doubleBooked',\n          flowControlSettings: '$[2].flowControlSettings',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookingId', 'revision', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface MarkBookingAsPendingOptions {\n  /**\n   * Information about whether to notify the customer and\n   * the message to send.\n   */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the\n   * session starts. The phone number is taken from `contactDetails.phone`.\n   */\n  sendSmsReminder?: boolean | null;\n  /** Payment status to set for the booking. */\n  paymentStatus?: PaymentStatusWithLiterals;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to check availability before updating the status.\n   */\n  flowControlSettings?: MarkBookingAsPendingFlowControlSettings;\n}\n\n/**\n * Creates a multi-service booking and all included single-service bookings simultaneously.\n *\n *\n * ## When to call this method\n *\n * Create sequential appointments where customers book related services together. For adding existing single-service bookings to an existing multi-service booking, call Add Bookings to Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking)) instead.\n *\n * ## Requirements and behavior\n *\n * __Package constraints__: Multi-service bookings support 2-8 appointment-based single-service bookings only (course and class bookings aren't supported). All single-service bookings must be at the same location with sequential scheduling and no gaps between appointments.\n *\n * __Timing specification__: You must provide complete `slot` details (`scheduleId`, `startDate`, `endDate`) for each single-service booking. Wix Bookings validates sequential timing but doesn't auto-calculate it.\n *\n * __Package pricing__: The total price equals the sum of individual services. Wix Bookings automatically syncs the payment status from the corresponding Wix eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecommerce/orders/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer paid via an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n *\n * __Package notifications__: Customers receive 1 unified notification for the entire multi-service booking. Wix Bookings doesn't send notifications for the package's individual single-service bookings.\n *\n * ## Related methods\n *\n * Verify availability first with List Multi Service Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)).\n *\n * See Create Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking)) for more details about constraints and effects of creating single-service bookings.\n * @param bookings - Between 2 to 8 new single-service booking specifications to create and combine in a multi-service booking.\n *\n * Each single-service booking specification must include `slot` details (`scheduleId`, `startDate`, and `endDate`).\n * Specify contact details, number of participants, and any additional fields as needed.\n *\n * For sequential bookings, ensure the timing allows services to be scheduled back-to-back.\n * @public\n * @documentationMaturity preview\n * @requiredField bookings\n * @requiredField bookings.additionalFields._id\n * @requiredField bookings.bookedEntity\n * @requiredField bookings.bookedEntity.item\n * @requiredField bookings.bookedEntity.item.slot\n * @requiredField bookings.bookedEntity.item.slot.endDate\n * @requiredField bookings.bookedEntity.item.slot.location.locationType\n * @requiredField bookings.bookedEntity.item.slot.scheduleId\n * @requiredField bookings.bookedEntity.item.slot.startDate\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_CREATE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.CreateMultiServiceBooking\n */\nexport async function createMultiServiceBooking(\n  bookings: NonNullablePaths<\n    Booking,\n    | `additionalFields.${number}._id`\n    | `bookedEntity`\n    | `bookedEntity.slot`\n    | `bookedEntity.slot.endDate`\n    | `bookedEntity.slot.location.locationType`\n    | `bookedEntity.slot.scheduleId`\n    | `bookedEntity.slot.startDate`,\n    5\n  >[],\n  options?: CreateMultiServiceBookingOptions\n): Promise<\n  NonNullablePaths<\n    CreateMultiServiceBookingResponse,\n    | `multiServiceBooking.bookings`\n    | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n    | `multiServiceBooking.bookings.${number}.booking.status`\n    | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n    | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: CreateMultiServiceBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    bookings: bookings,\n    participantNotification: options?.participantNotification,\n    sendSmsReminder: options?.sendSmsReminder,\n    flowControlSettings: options?.flowControlSettings,\n    returnFullEntity: options?.returnFullEntity,\n    multiServiceBookingType: options?.multiServiceBookingType,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.createMultiServiceBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          bookings: '$[0]',\n          participantNotification: '$[1].participantNotification',\n          sendSmsReminder: '$[1].sendSmsReminder',\n          flowControlSettings: '$[1].flowControlSettings',\n          returnFullEntity: '$[1].returnFullEntity',\n          multiServiceBookingType: '$[1].multiServiceBookingType',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['bookings', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CreateMultiServiceBookingOptions {\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * Wix Bookings takes the phone number from `contactDetails.phone`.\n   *\n   * Default: `true`\n   */\n  sendSmsReminder?: boolean | null;\n  /**\n   * Whether to ignore specific standard procedures of the Wix Bookings flow.\n   * For example, whether to skip checking availability before updating the status.\n   */\n  flowControlSettings?: CreateBookingFlowControlSettings;\n  /** Whether to return the created single-service bookings. */\n  returnFullEntity?: boolean;\n  /**\n   * Multi-service booking type.\n   *\n   * Currently only `SEQUENTIAL_BOOKINGS` is supported.\n   */\n  multiServiceBookingType?: MultiServiceBookingTypeWithLiterals;\n}\n\n/**\n * Reschedules a multi-service booking by changing the timing for all or specific single-service bookings in the package.\n *\n *\n * This method reschedules single-service bookings within the multi-service booking while maintaining sequential order. You must specify exact new timing for each service to ensure they remain back-to-back with no gaps or overlaps.\n *\n * This method fails if it can't reschedule at least 1 single-service booking. You must provide the current revision number for each single-service booking you're rescheduling to prevent conflicting changes.\n *\n * See Reschedule Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-booking)) for single-service booking rescheduling details.\n * @param multiServiceBookingId - ID of the multi-service booking to reschedule.\n * @param rescheduleBookingsInfo - Information about the single-service bookings to reschedule.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @requiredField rescheduleBookingsInfo\n * @requiredField rescheduleBookingsInfo.bookingId\n * @requiredField rescheduleBookingsInfo.revision\n * @requiredField rescheduleBookingsInfo.slot\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_RESCHEDULE\n * @applicableIdentity APP\n * @applicableIdentity MEMBER\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.RescheduleMultiServiceBooking\n */\nexport async function rescheduleMultiServiceBooking(\n  multiServiceBookingId: string,\n  rescheduleBookingsInfo: NonNullablePaths<\n    RescheduleBookingInfo,\n    `bookingId` | `revision` | `slot`,\n    2\n  >[],\n  options?: RescheduleMultiServiceBookingOptions\n): Promise<\n  NonNullablePaths<\n    RescheduleMultiServiceBookingResponse,\n    | `multiServiceBooking.bookings`\n    | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n    | `multiServiceBooking.bookings.${number}.booking.status`\n    | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n    | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: RescheduleMultiServiceBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n    rescheduleBookingsInfo: rescheduleBookingsInfo,\n    participantNotification: options?.participantNotification,\n    flowControlSettings: options?.flowControlSettings,\n    returnFullEntity: options?.returnFullEntity,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.rescheduleMultiServiceBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          multiServiceBookingId: '$[0]',\n          rescheduleBookingsInfo: '$[1]',\n          participantNotification: '$[2].participantNotification',\n          flowControlSettings: '$[2].flowControlSettings',\n          returnFullEntity: '$[2].returnFullEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId', 'rescheduleBookingsInfo', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface RescheduleMultiServiceBookingOptions {\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings rescheduling flow are changed.\n   * For example, whether the availability of the new slot is checked before rescheduling the booking or if the customer can reschedule the booking even though the service's rescheduling policy doesn't allow it.\n   */\n  flowControlSettings?: RescheduleBookingFlowControlSettings;\n  /** Whether to return the rescheduled bookings entities. */\n  returnFullEntity?: boolean;\n}\n\n/**\n * Checks if the business can still accommodate an existing multi-service booking and returns overall bookability status, capacity details, and policy violations.\n *\n *\n * Wix Bookings considers:\n * - The relevant services' booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).\n * - The availability of all required resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).\n *\n * Call this method to check if an existing multi-service booking is still valid after business configuration changes.\n * For example, staff changes, policy updates, or capacity modifications.\n *\n * For checking availability before creating new multi-service bookings, call List Multi Service Availability Time Slots\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)) instead.\n * @param multiServiceBookingId - ID of the multi-service booking to retrieve.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_GET_AVAILABILITY\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBookingAvailability\n */\nexport async function getMultiServiceBookingAvailability(\n  multiServiceBookingId: string\n): Promise<\n  NonNullablePaths<\n    GetMultiServiceBookingAvailabilityResponse,\n    `bookable` | `multiServiceBookingInfo`,\n    2\n  > & {\n    __applicationErrorsType?: GetMultiServiceBookingAvailabilityApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.getMultiServiceBookingAvailability(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { multiServiceBookingId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Cancels a multi-service booking and all its associated single-service bookings.\n *\n *\n * Cancels the entire multi-service booking, updating the status of all single-service bookings to `CANCELED`.\n * The call fails if all single-service bookings are already canceled or declined.\n *\n * See Cancel Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/cancel-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/cancel-booking)) for single-service booking cancellation details.\n * @param multiServiceBookingId - ID of the multi-service booking to cancel.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_CANCEL\n * @applicableIdentity APP\n * @applicableIdentity MEMBER\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.CancelMultiServiceBooking\n */\nexport async function cancelMultiServiceBooking(\n  multiServiceBookingId: string,\n  options?: CancelMultiServiceBookingOptions\n): Promise<\n  NonNullablePaths<\n    CancelMultiServiceBookingResponse,\n    | `multiServiceBooking.bookings`\n    | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n    | `multiServiceBooking.bookings.${number}.booking.status`\n    | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n    | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: CancelMultiServiceBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n    participantNotification: options?.participantNotification,\n    flowControlSettings: options?.flowControlSettings,\n    returnFullEntity: options?.returnFullEntity,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.cancelMultiServiceBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          multiServiceBookingId: '$[0]',\n          participantNotification: '$[1].participantNotification',\n          flowControlSettings: '$[1].flowControlSettings',\n          returnFullEntity: '$[1].returnFullEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CancelMultiServiceBookingOptions {\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings cancellation flow are changed.\n   * For example, whether the customer can cancel the booking even though the service's cancellation policy doesn't allow it or whether to issue a refund upon cancellation.\n   */\n  flowControlSettings?: CancelBookingFlowControlSettings;\n  /** Whether to return the canceled single-service bookings. */\n  returnFullEntity?: boolean;\n}\n\n/**\n * Updates the status for all single-service bookings in a multi-service booking to `PENDING`.\n *\n *\n * Call this method for multi-service bookings requiring manual business approval before confirmation.\n *\n * ## Status requirements\n *\n * - __Original status__: All single-service bookings must have `CREATED` status.\n * - __Target status__: All bookings move to `PENDING` together (all-or-nothing operation).\n *\n * ## Checkout restrictions\n *\n * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n *\n * ## Additional updates\n *\n * - __Payment status__: Updates if you specify a new `markAsPendingBookingsInfo.paymentStatus`.\n * - __Customer notifications__: Send messages using `participantNotification`.\n * - __Revision control__: Requires current revision numbers for all single-service bookings.\n *\n * See Mark Booking as Pending ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending)) for more details about marking single-service bookings as pending.\n * @param multiServiceBookingId - ID of the multi-service booking to mark as `PENDING`.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_MARK_AS_PENDING\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.MarkMultiServiceBookingAsPending\n */\nexport async function markMultiServiceBookingAsPending(\n  multiServiceBookingId: string,\n  options?: MarkMultiServiceBookingAsPendingOptions\n): Promise<\n  NonNullablePaths<\n    MarkMultiServiceBookingAsPendingResponse,\n    | `multiServiceBooking.bookings`\n    | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n    | `multiServiceBooking.bookings.${number}.booking.status`\n    | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n    | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: MarkMultiServiceBookingAsPendingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n    markAsPendingBookingsInfo: options?.markAsPendingBookingsInfo,\n    participantNotification: options?.participantNotification,\n    sendSmsReminder: options?.sendSmsReminder,\n    doubleBooked: options?.doubleBooked,\n    returnFullEntity: options?.returnFullEntity,\n    flowControlSettings: options?.flowControlSettings,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.markMultiServiceBookingAsPending(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          multiServiceBookingId: '$[0]',\n          markAsPendingBookingsInfo: '$[1].markAsPendingBookingsInfo',\n          participantNotification: '$[1].participantNotification',\n          sendSmsReminder: '$[1].sendSmsReminder',\n          doubleBooked: '$[1].doubleBooked',\n          returnFullEntity: '$[1].returnFullEntity',\n          flowControlSettings: '$[1].flowControlSettings',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface MarkMultiServiceBookingAsPendingOptions {\n  /** Information about the single-service bookings to mark as `PENDING`. */\n  markAsPendingBookingsInfo?: BookingInfo[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * Wix Bookings takes the phone number from `contactDetails.phone`.\n   *\n   * Default: `true`\n   */\n  sendSmsReminder?: boolean | null;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /**\n   * Whether to return the single-service bookings that were marked as `PENDING`.\n   *\n   * Default: `false`\n   */\n  returnFullEntity?: boolean;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings creation flow are changed.\n   * For example, whether Wix Bookings checks availability before updating the booking.\n   */\n  flowControlSettings?: MarkBookingAsPendingFlowControlSettings;\n}\n\n/**\n * Updates the status for all single-service bookings in a multi-service booking to `CONFIRMED`.\n *\n *\n * Call this method for multi-service bookings requiring manual business approval.\n *\n * ## Status requirements\n *\n * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.\n * - __Target status__: All bookings move to `CONFIRMED` together (all-or-nothing operation).\n *\n * ## Checkout restrictions\n *\n * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n *\n * ## Additional updates\n *\n * - __Payment status__: Updates if you specify a new `confirmBookingsInfo.paymentStatus`.\n * - __Customer notifications__: Send messages using `participantNotification`.\n * - __Revision control__: Requires current revision numbers for all single-service bookings.\n *\n * See Confirm Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-booking)) for more details about confirming single-service bookings.\n * @param multiServiceBookingId - ID of the multi-service booking to confirm its related bookings.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_CONFIRM\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.ConfirmMultiServiceBooking\n */\nexport async function confirmMultiServiceBooking(\n  multiServiceBookingId: string,\n  options?: ConfirmMultiServiceBookingOptions\n): Promise<\n  NonNullablePaths<\n    ConfirmMultiServiceBookingResponse,\n    | `multiServiceBooking.bookings`\n    | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n    | `multiServiceBooking.bookings.${number}.booking.status`\n    | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n    | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: ConfirmMultiServiceBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n    confirmBookingsInfo: options?.confirmBookingsInfo,\n    participantNotification: options?.participantNotification,\n    sendSmsReminder: options?.sendSmsReminder,\n    doubleBooked: options?.doubleBooked,\n    returnFullEntity: options?.returnFullEntity,\n    flowControlSettings: options?.flowControlSettings,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.confirmMultiServiceBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          multiServiceBookingId: '$[0]',\n          confirmBookingsInfo: '$[1].confirmBookingsInfo',\n          participantNotification: '$[1].participantNotification',\n          sendSmsReminder: '$[1].sendSmsReminder',\n          doubleBooked: '$[1].doubleBooked',\n          returnFullEntity: '$[1].returnFullEntity',\n          flowControlSettings: '$[1].flowControlSettings',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ConfirmMultiServiceBookingOptions {\n  /** Information about the single-service bookings to confirm. */\n  confirmBookingsInfo?: BookingInfo[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /**\n   * Whether to send an SMS reminder to the customer 24 hours before the session starts.\n   * Wix Bookings takes the phone number from `contactDetails.phone`.\n   *\n   * Default: `true`\n   */\n  sendSmsReminder?: boolean | null;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /** Whether to return the confirmed single-service bookings. */\n  returnFullEntity?: boolean;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings confirmation flow are changed.\n   * For example, whether Wix Bookings checks availability before confirming the booking.\n   */\n  flowControlSettings?: ConfirmBookingFlowControlSettings;\n}\n\n/**\n * Updates the status for all single-service bookings in a multi-service booking to `DECLINED`.\n *\n *\n * Call this method to reject multi-service bookings that can't be accommodated or don't meet business requirements.\n *\n * ## Status requirements\n *\n * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.\n * - __Target status__: All bookings move to `DECLINED` together (all-or-nothing operation).\n *\n * ## Checkout restrictions\n *\n * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n *\n * ## Additional updates\n *\n * - __Payment status__: Updates if you specify a new `declineBookingsInfo.paymentStatus`.\n * - __Customer notifications__: Send messages using `participantNotification`.\n * - __Revision control__: Requires current revision numbers for all single-service bookings.\n *\n * Refer to Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/decline-booking)) for more details about declining single-service bookings.\n * @param multiServiceBookingId - ID of the multi-service booking to decline.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_DECLINE\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.DeclineMultiServiceBooking\n */\nexport async function declineMultiServiceBooking(\n  multiServiceBookingId: string,\n  options?: DeclineMultiServiceBookingOptions\n): Promise<\n  NonNullablePaths<\n    DeclineMultiServiceBookingResponse,\n    | `multiServiceBooking.bookings`\n    | `multiServiceBooking.bookings.${number}.booking.totalParticipants`\n    | `multiServiceBooking.bookings.${number}.booking.status`\n    | `multiServiceBooking.bookings.${number}.booking.paymentStatus`\n    | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId`\n    | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`,\n    7\n  > & {\n    __applicationErrorsType?: DeclineMultiServiceBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n    declineBookingsInfo: options?.declineBookingsInfo,\n    participantNotification: options?.participantNotification,\n    doubleBooked: options?.doubleBooked,\n    returnFullEntity: options?.returnFullEntity,\n    flowControlSettings: options?.flowControlSettings,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.declineMultiServiceBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          multiServiceBookingId: '$[0]',\n          declineBookingsInfo: '$[1].declineBookingsInfo',\n          participantNotification: '$[1].participantNotification',\n          doubleBooked: '$[1].doubleBooked',\n          returnFullEntity: '$[1].returnFullEntity',\n          flowControlSettings: '$[1].flowControlSettings',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DeclineMultiServiceBookingOptions {\n  /** Information about the single-service bookings to decline. */\n  declineBookingsInfo?: BookingInfo[];\n  /** Information about whether to notify the customer and the message to send. */\n  participantNotification?: ParticipantNotification;\n  /** Whether this booking has a conflict with at least 1 other confirmed booking. */\n  doubleBooked?: boolean | null;\n  /** Whether to return the declined single-service bookings. */\n  returnFullEntity?: boolean;\n  /**\n   * Information about whether specific procedures of the standard Wix Bookings declining flow are changed.\n   * For example, whether to issue a refund upon cancellation.\n   */\n  flowControlSettings?: DeclineBookingFlowControlSettings;\n}\n\n/**\n * Retrieves information about which actions the customer can perform for up to 50 multi-service bookings.\n *\n *\n * For each multi-service booking, the response indicates which actions are currently allowed:\n * - `cancel`: Whether the customer can cancel the multi-service booking.\n * - `reschedule`: Whether the customer can adjust the multi-service booking's timing.\n *\n * Bear the following considerations in mind when calling this method:\n *\n * __Real-time validation__: Wix Bookings calculates allowed actions based on current multi-service booking status, booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)), and available resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) at the time of the call.\n *\n * __Permission context__: Depending on the permissions of the calling [identity](https://dev.wix.com/build-apps/develop-your-app/about-identities), you may see different allowed actions for the same multi-service booking. For example, if the identity has permissions to read only part of the multi-service booking, the response indicates which single-service bookings the identity can read.\n *\n * __Status dependencies__: Allowed actions change as bookings progress through their lifecycle (`CREATED` → `PENDING` → `CONFIRMED`/`DECLINED` → `CANCELED`).\n * Bookings can skip `PENDING` and move directly from `CREATED` to `CONFIRMED`/`DECLINED` based on service configuration.\n * @param multiServiceBookingIds - IDs of the multi-service bookings to retrieve allowed actions for.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingIds\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_GET_ALLOWED_ACTIONS\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.BulkGetMultiServiceBookingAllowedActions\n */\nexport async function bulkGetMultiServiceBookingAllowedActions(\n  multiServiceBookingIds: string[]\n): Promise<\n  NonNullablePaths<\n    BulkGetMultiServiceBookingAllowedActionsResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `results.${number}.item.cancel`\n    | `results.${number}.item.reschedule`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingIds: multiServiceBookingIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.bulkGetMultiServiceBookingAllowedActions(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { multiServiceBookingIds: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingIds']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a multi-service booking and all its associated single-service bookings.\n *\n *\n * Returns the complete multi-service booking information including its ID, associated single-service bookings, and the total number of scheduled single-service bookings.\n *\n * If you call on behalf of an [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) with permissions to read only part of the multi-service booking, only the permitted single-service bookings are retrieved.\n * The returned total number includes single-service bookings for which you don't have permissions.\n *\n * See Query Extended Bookings ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/extended-bookings/query-extended-bookings) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-reader-v2/query-extended-bookings)) for details about retrieving individual single-service bookings and additional metadata.\n * @param multiServiceBookingId - ID of the multi-service booking.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_READ\n * @applicableIdentity APP\n * @returns Retrieved multi-service booking.\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBooking\n */\nexport async function getMultiServiceBooking(\n  multiServiceBookingId: string\n): Promise<\n  NonNullablePaths<\n    MultiServiceBooking,\n    | `bookings`\n    | `bookings.${number}.booking.totalParticipants`\n    | `bookings.${number}.booking.status`\n    | `bookings.${number}.booking.paymentStatus`\n    | `bookings.${number}.booking.selectedPaymentOption`\n    | `bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `bookings.${number}.booking.createdBy.memberId`\n    | `bookings.${number}.booking.createdBy.wixUserId`\n    | `bookings.${number}.booking.createdBy.appId`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.getMultiServiceBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)\n      ?.multiServiceBooking!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { multiServiceBookingId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Expands an existing multi-service booking by adding existing single-service bookings to the package.\n *\n *\n * ## When to call this method\n *\n * Call this method to add 1 or more existing single-service bookings to an existing multi-service booking.\n * For creating a new multi-service booking with new single-service bookings, call Create Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-multi-service-booking)) instead.\n *\n * ## Package integration\n *\n * The timing of the single-service bookings to add must be compatible with the multi-service booking's sequential schedule to ensure no gaps or overlaps between services in the updated package.\n *\n * ## Requirements and limitations\n *\n * - __Maximum capacity__: The total number of single-service bookings can't exceed 8.\n * - __Booking eligibility__: You can add only independent single-service bookings that aren't part of another multi-service booking.\n * - __Status compatibility__: Added bookings must have compatible status with the target multi-service booking.\n * - __Revision control__: You must provide current revision numbers for all single-service bookings to add.\n * @param multiServiceBookingId - ID of the multi-service booking.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @requiredField options.bookings\n * @requiredField options.bookings.bookingId\n * @requiredField options.bookings.revision\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_ADD_BOOKINGS\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.AddBookingsToMultiServiceBooking\n */\nexport async function addBookingsToMultiServiceBooking(\n  multiServiceBookingId: string,\n  options?: NonNullablePaths<\n    AddBookingsToMultiServiceBookingOptions,\n    `bookings` | `bookings.${number}.bookingId` | `bookings.${number}.revision`,\n    4\n  >\n): Promise<\n  NonNullablePaths<\n    AddBookingsToMultiServiceBookingResponse,\n    | `bookings`\n    | `bookings.${number}.booking.totalParticipants`\n    | `bookings.${number}.booking.status`\n    | `bookings.${number}.booking.paymentStatus`\n    | `bookings.${number}.booking.selectedPaymentOption`\n    | `bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `bookings.${number}.booking.createdBy.memberId`\n    | `bookings.${number}.booking.createdBy.wixUserId`\n    | `bookings.${number}.booking.createdBy.appId`,\n    6\n  > & {\n    __applicationErrorsType?: AddBookingsToMultiServiceBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n    bookings: options?.bookings,\n    returnFullEntity: options?.returnFullEntity,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.addBookingsToMultiServiceBooking(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          multiServiceBookingId: '$[0]',\n          bookings: '$[1].bookings',\n          returnFullEntity: '$[1].returnFullEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface AddBookingsToMultiServiceBookingOptions {\n  /**\n   * List of single-service booking IDs and their revision.\n   * @maxSize 8\n   */\n  bookings: BookingIdAndRevision[];\n  /**\n   * Whether to return the single-service bookings that were added to the multi-service booking.\n   *\n   * Default: `false`\n   */\n  returnFullEntity?: boolean;\n}\n\n/**\n * Removes single-service bookings from a multi-service booking and converts them to independent single-service bookings.\n *\n *\n * ## Removal options\n *\n * __Remove all permitted bookings__: If you specify an empty `bookings` array, all single-service bookings for which the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has read permissions are removed from the multi-service booking.\n *\n * __Selective removal__: Specify single-service booking IDs and revisions to remove only specific single-service bookings from the package.\n *\n * __Sequential scheduling__: To maintain sequential scheduling, remove only first or last single-service bookings. For middle bookings, first reschedule all relevant single-service bookings to eliminate gaps. To do so, call Reschedule Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-multi-service-booking)) before removing the unwanted bookings.\n *\n * ## Removal behavior\n *\n * __Independent bookings__: Removed single-service bookings become independent bookings.\n * You can manage them using single-service booking methods.\n *\n * __Automatic cleanup__: Multi-service bookings must contain at least 2 services.\n * If removal results in only 1 remaining single-service booking for the multi-service booking, the entire multi-service booking is deleted and the remaining single-service booking becomes a standalone booking.\n *\n * __Revision control__: Specify current revision numbers to prevent conflicting modifications during the removal process.\n * @param multiServiceBookingId - ID of the multi-service booking.\n * @public\n * @documentationMaturity preview\n * @requiredField multiServiceBookingId\n * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_REMOVE_BOOKINGS\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.RemoveBookingsFromMultiServiceBooking\n */\nexport async function removeBookingsFromMultiServiceBooking(\n  multiServiceBookingId: string,\n  options?: RemoveBookingsFromMultiServiceBookingOptions\n): Promise<\n  NonNullablePaths<\n    RemoveBookingsFromMultiServiceBookingResponse,\n    | `bookings`\n    | `bookings.${number}.booking.totalParticipants`\n    | `bookings.${number}.booking.status`\n    | `bookings.${number}.booking.paymentStatus`\n    | `bookings.${number}.booking.selectedPaymentOption`\n    | `bookings.${number}.booking.createdBy.anonymousVisitorId`\n    | `bookings.${number}.booking.createdBy.memberId`\n    | `bookings.${number}.booking.createdBy.wixUserId`\n    | `bookings.${number}.booking.createdBy.appId`,\n    6\n  > & {\n    __applicationErrorsType?: RemoveBookingsFromMultiServiceBookingApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    multiServiceBookingId: multiServiceBookingId,\n    bookings: options?.bookings,\n    returnFullEntity: options?.returnFullEntity,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsV2Booking.removeBookingsFromMultiServiceBooking(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          multiServiceBookingId: '$[0]',\n          bookings: '$[1].bookings',\n          returnFullEntity: '$[1].returnFullEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['multiServiceBookingId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface RemoveBookingsFromMultiServiceBookingOptions {\n  /**\n   * List of single-service booking IDs and their revision.\n   * @maxSize 8\n   */\n  bookings?: BookingIdAndRevision[];\n  /**\n   * Whether to return the single-service bookings.\n   *\n   * Default: `false`\n   */\n  returnFullEntity?: boolean;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFloatToRESTFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    _: [\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/multi_service_bookings',\n        destPath: '/v2/multi_service_bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/multi_service_bookings',\n        destPath: '/v2/bulk/multi_service_bookings',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/multi_service_bookings',\n        destPath: '/v2/multi_service_bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/multi_service_bookings',\n        destPath: '/v2/bulk/multi_service_bookings',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/bookings/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/multi_service_bookings',\n        destPath: '/v2/multi_service_bookings',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/bookings-service',\n        destPath: '/v2/bookings',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nfunction resolveComWixpressBookingsBookingsV2BookingsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    _: [\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/multi_service_bookings',\n        destPath: '/v2/multi_service_bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/multi_service_bookings',\n        destPath: '/v2/bulk/multi_service_bookings',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/multi_service_bookings',\n        destPath: '/v2/multi_service_bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/multi_service_bookings',\n        destPath: '/v2/bulk/multi_service_bookings',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/bookings/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/multi_service_bookings',\n        destPath: '/v2/multi_service_bookings',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/bookings-service/v2/bulk/bookings',\n        destPath: '/v2/bulk/bookings',\n      },\n      {\n        srcPath: '/_api/bookings-service/v2/bookings',\n        destPath: '/v2/bookings',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/bookings-service',\n        destPath: '/v2/bookings',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nfunction resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings-confirmator/v2/bookings/confirmation',\n        destPath: '/v2/bookings/confirmation',\n      },\n      {\n        srcPath: '/bookings/v2/confirmation',\n        destPath: '/v2/confirmation',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/confirmation',\n        destPath: '/v2/bulk/confirmation',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/bookings/confirmOrDecline',\n        destPath: '/v2/bulk/bookings/confirmOrDecline',\n      },\n      {\n        srcPath: '/_api/bookings-confirmator/v2/bulk/confirmation',\n        destPath: '/v2/bulk/confirmation',\n      },\n      {\n        srcPath: '/_api/bookings-confirmator/v2/bulk/bookings/confirmOrDecline',\n        destPath: '/v2/bulk/bookings/confirmOrDecline',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_bookings';\n\n/**\n * Updates the booking `status` to `CONFIRMED`, `PENDING`, or `DECLINED` based\n * on the `paymentStatus` you provide, double booking conflicts, and whether\n * the service requires business approval.\n *\n * ## eCommerce checkout restriction\n *\n * Call this method only when using a custom checkout page. Don't\n * call it when using a *Wix eCommerce checkout*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n * In such cases, Wix automatically updates the booking status based on\n * the `paymentStatus` of the corresponding *Wix eCommerce order*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * ## New booking status\n *\n * The booking `status` is set to `DECLINED` if both of the following conditions\n * are met:\n * + You provide `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT` as payment status.\n * + There is a double booking conflict.\n *\n * If only one or none of these conditions is met, `status` is set to `PENDING`\n * or `CONFIRMED` depending on whether the service requires business approval.\n *\n * ## Double bookings\n *\n * If there is a double booking conflict, but the booking has already been at least\n * partially paid, the method still marks the booking as `PENDING` or `CONFIRMED`.\n * Then, it also sets `doubleBooked` to `true`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences in confirmation behavior if the\n * booking was created with special `flowControlSettings`:\n * + If the booking's `flowControlSettings.skipAvailabilityValidation` was set to\n * `true`, the booking is never declined regardless of double booking conflicts.\n * Instead, it's marked `CONFIRMED` or `PENDING`, depending on whether the\n * service requires business approval.\n * + If the booking's `flowControlSettings.skipBusinessConfirmation` was set to\n * `true`, the booking skips `PENDING` status and is marked `CONFIRMED`\n * immediately.\n */\nexport function confirmOrDeclineBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __confirmOrDeclineBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.confirmator.v2.Confirmator.ConfirmOrDeclineBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl({\n        protoPath: '/v2/confirmation/{bookingId}:confirmOrDecline',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __confirmOrDeclineBooking;\n}\n\n/**\n * Confirms or declines up to 300 bookings.\n *\n *\n * See *Confirm Or Decline Booking*\n * ([SDK](https://dev.wix.com/docs/velo/api-reference/wix-bookings-v2/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking))\n * for details about when a booking is confirmed or declined.\n */\nexport function bulkConfirmOrDeclineBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkConfirmOrDeclineBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.confirmator.v2.Confirmator.BulkConfirmOrDeclineBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsConfirmatorV2ConfirmatorUrl({\n        protoPath: '/v2/bulk/bookings/confirmOrDecline',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.startDate' },\n              { path: 'results.item.endDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkConfirmOrDeclineBooking;\n}\n\n/**\n * Creates a booking.\n *\n *\n * ## Appointment booking\n *\n * For appointment-based services, specify the relevant `slot` in\n * `bookedEntity.slot`. We recommend specifying the complete\n * `availabilityEntries.slot` returned in Query Availability\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * in your call's request to avoid failed calls due to unavailability.\n *\n * ## Class session booking\n *\n * For class services, specify the relevant event ID\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * as `bookedEntity.slot.eventId`.\n * We recommend retrieving the event ID from Query Availability's\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * `availabilityEntries.slot.eventId` to avoid failed calls due to unavailability.\n * Specifying an event ID leads to automatic calculations of `slot.startDate`, `slot.endDate`,\n * `slot.timezone`, `slot.resource`, and `slot.location`. All manually specified\n * values are overridden.\n *\n * ## Course booking\n *\n * For course services, specify the course's schedule ID in `bookedEntity.schedule.scheduleId`.\n * We recommend following [this sample flow](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course)\n * to minimize failed calls due to unavailability.\n *\n * ## Related resources\n *\n * Specifying a `resource` triggers an availability check, resulting in a failed\n * call if the resource is unavailable. Omitting a resource allows Wix Bookings\n * to assign a resource belonging to the relevant type randomly when the merchant\n * confirms the booking.\n *\n * ## Participant information\n *\n * You must specify either `participantsChoices` or `totalParticipants`.\n * The call fails if the specified `participantsChoices` aren't among the supported\n * _service options and variants_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer that's send\n * immediately. Ensure `participantNotification.notifyParticipants` is set to `true`\n * to send the message.\n *\n * If you specify `{\"sendSmsReminder\": true}`, the customer receives an SMS 24 hours\n * before the session starts. The phone number is taken from `contactDetails.phone`.\n *\n * ## Booking status\n *\n * Bookings default to the `CREATED` status, not affecting the business calendar\n * or resource availability. You can specify a different status when the calling\n * [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities)\n * has `Manage Bookings` permissions.\n *\n * ## Payment options\n *\n * The specified `selectedPaymentOption` indicates how the customer intends to\n * pay, allowing for later changes to a different method supported by the service.\n *\n * ## Payment status\n *\n * A booking is initially created with `{\"paymentStatus\": \"UNDEFINED\"}` regardless\n * of the payment status specified in Create Booking. If a customer uses an\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),\n * Wix Bookings automatically syncs the booking's payment status from\n * the corresponding eCommerce order\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * If a booking doesn't have a corresponding eCommerce order, for example, since\n * the customer didn't use the eCommerce checkout, you can update the booking's\n * payment status with Confirm Or Decline Booking\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).\n *\n * ## Booking form data\n *\n * When customers sign up for a service, they must fill out the booking form.\n * To create a booking with a completed booking form, specify the relevant data in\n * `formSubmission`. Ensure the values of the corresponding fields in\n * `booking.contactDetails` and `formSubmission` are identical. If these values\n * don't match, Create Booking fails. Therefore, we recommend specifying\n * only `booking.contactDetails.contactId` when providing `formSubmission`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences when you specify special\n * `flowControlSettings`:\n *\n * - `{\"skipAvailabilityValidation\": true}`: The call succeeds\n * regardless of availability. If you don't specify any resource, the call\n * succeeds even if no resource of the relevant type is available.\n * - `{\"skipBusinessConfirmation\": true}`: Automatically confirms `PENDING`\n * bookings that require manual confirmation.\n * - `{\"skipSelectedPaymentOptionValidation\": true}`: Allows customers to pay\n * with payment methods that aren't supported for the service.\n *\n * When using special `flowControlSettings`, ensure you have sufficient\n * permissions. If you encounter failed calls due to insufficient permissions,\n * consider the following options:\n *\n * - **App developers** can use a higher\n * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n * - **Site developers** can utilize\n * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n *\n * Granting additional permissions and using elevation permits method calls that\n * would typically fail due to authorization checks. Therefore, you should use\n * them intentionally and securely.\n */\nexport function createBooking(payload: object): RequestOptionsFactory<any> {\n  function __createBooking({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'booking.createdDate' },\n          { path: 'booking.startDate' },\n          { path: 'booking.endDate' },\n          { path: 'booking.updatedDate' },\n          { path: 'booking.canceledDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n          { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.bookings.v2.Bookings.CreateBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createBooking;\n}\n\n/**\n * Creates up to 8 bookings.\n *\n *\n * See Create Booking\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking))\n * for more information.\n *\n * If any of the specified bookings is missing a required field the entire call\n * fails.\n *\n * If you specify 1 or more unavailable bookings, the call succeeds\n * while the unavailable bookings aren't created. Instead, they're counted as\n * failures in the returned `bulkActionMetadata`.\n */\nexport function bulkCreateBooking(payload: object): RequestOptionsFactory<any> {\n  function __bulkCreateBooking({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'createBookingsInfo.booking.createdDate' },\n          { path: 'createBookingsInfo.booking.startDate' },\n          { path: 'createBookingsInfo.booking.endDate' },\n          { path: 'createBookingsInfo.booking.updatedDate' },\n          { path: 'createBookingsInfo.booking.canceledDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          {\n            path: 'createBookingsInfo.booking.contactDetails.fullAddress.geocode.latitude',\n          },\n          {\n            path: 'createBookingsInfo.booking.contactDetails.fullAddress.geocode.longitude',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.bookings.v2.Bookings.BulkCreateBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bulk/bookings/create',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.startDate' },\n              { path: 'results.item.endDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkCreateBooking;\n}\n\n/**\n * Reschedules an appointment booking to a different slot or a class booking to\n * a different session.\n *\n *\n * ## Course booking limitation\n *\n * You can't reschedule course bookings.\n *\n * ## Appointment sessions\n *\n * For appointments, the old session is removed from the business calendar\n * while a new session is added. We recommend calling Query Availability\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * first and specifying the entire retrieved `slot`.\n *\n * ## Class sessions\n *\n * For classes, the new session must be an existing session belonging to the\n * same class. We recommend retrieving `availabilityEntries.slot.eventId`\n * from Query Availability\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))\n * to avoid failed Reschedule Booking calls due to unavailability. Specify\n * only `slot.eventId` instead of the entire `slot` object.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences when you specify special\n * `flowControlSettings`:\n *\n * - `{\"ignoreReschedulePolicy\": true}`: The call succeeds even if the\n * service's `reschedulePolicy` doesn't allow it.\n * - `{\"skipAvailabilityValidation\": true}`: The call succeeds even if\n * the specified session, slot, or resource isn't available. If you don't\n * specify any resource, the call succeeds even if no resource of the relevant\n * type is available.\n * - `{\"skipBusinessConfirmation\": true}`: Any `PENDING` booking is\n * automatically confirmed even if the services requires the merchants's\n * manual confirmation.\n *\n * When using special `flowControlSettings`, ensure you have sufficient\n * permissions. If you encounter failed calls due to insufficient permissions,\n * consider the following options:\n *\n * - **App developers** can use a higher\n * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n * - **Site developers** can utilize\n * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n *\n * Granting additional permissions and using elevation permits method calls that\n * would typically fail due to authorization checks. Therefore, you should use\n * them intentionally and securely.\n */\nexport function rescheduleBooking(payload: object): RequestOptionsFactory<any> {\n  function __rescheduleBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.bookings.v2.Bookings.RescheduleBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{bookingId}/reschedule',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __rescheduleBooking;\n}\n\n/**\n * Updates the booking status to `CONFIRMED` without checking whether the relevant slot or schedule is still available.\n *\n *\n * ## eCommerce checkout restriction\n *\n * Call this method only when using a custom checkout page. Don't\n * call it when using a Wix eCommerce checkout\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n * In such cases, Wix automatically updates the booking status based on\n * the `paymentStatus` of the corresponding Wix eCommerce order\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * ## When to call Confirm Or Decline Booking instead\n *\n * Confirm Booking doesn't check whether a slot or schedule is still available.\n * For these checks, call Confirm or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) instead.\n *\n * ## Original status validation\n *\n * You can only confirm bookings with a status of `PENDING`, `CREATED`, or\n * `WAITING_LIST`.\n *\n * ## Double bookings\n *\n * Confirm Booking doesn't check whether a slot or schedule is still available.\n * You can specify\n *\n * ```json\n * {\n * \"flowControlSettings\": {\n * \"checkAvailabilityValidation\": true\n * },\n * \"doubleBooked\": true\n * }\n * ```\n * to forcefully set the booking's `doubleBooked` flag to `true`, regardless of\n * a potential double booking conflict. You must call with `Manage Bookings`\n * permissions to do so. For the default flow control settings\n * `{\"checkAvailabilityValidation\": false}`, the specified `doubleBooked` value\n * is ignored.\n *\n * ## Payment status\n *\n * Also updates the booking's `paymentStatus`, if you specify a new payment\n * status.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n */\nexport function bookingsConfirmBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bookingsConfirmBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.bookings.v2.Bookings.ConfirmBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{bookingId}/confirm',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bookingsConfirmBooking;\n}\n\n/** Setting `submission_id` for a booking after the form submission is created. */\nexport function setBookingSubmissionId(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __setBookingSubmissionId({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.Bookings.SetBookingSubmissionId',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{bookingId}/set-booking-submission-id',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setBookingSubmissionId;\n}\n\n/**\n * Updates the extended fields for a booking.\n *\n *\n * If you specify an extended field `namespace` that doesn't exist yet, it's\n * created.\n *\n * Learn more about [extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/about-backend-extensions#schema-plugin-extensions).\n */\nexport function updateExtendedFields(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __updateExtendedFields({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.Bookings.UpdateExtendedFields',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{id}/update_extended_fields',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __updateExtendedFields;\n}\n\n/**\n * Updates the booking status to `DECLINED` and updates the relevant session's\n * `participants.approvalStatus` to `DECLINED` without checking whether the relevant\n * slot or schedule is still available.\n *\n *\n * ## eCommerce checkout restriction\n *\n * Call this method only when using a custom checkout page. Don't\n * call it when using a Wix eCommerce checkout\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n * In such cases, Wix automatically updates the booking status based on\n * the `paymentStatus` of the corresponding Wix eCommerce order\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n * ## When to call Confirm Or Decline Booking instead\n *\n * The method doesn't check whether a slot or schedule is still available. For\n * these checks you can call Confirm or Decline Booking\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).\n *\n * ## Original status validation\n *\n * You can only decline bookings with a `status` of `PENDING`, `CREATED`, or\n * `WAITING_LIST`.\n *\n * ## Payment status\n *\n * Also updates the booking's `paymentStatus`, if you specify a new payment\n * status.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n */\nexport function bookingsDeclineBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bookingsDeclineBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.bookings.v2.Bookings.DeclineBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{bookingId}/decline',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bookingsDeclineBooking;\n}\n\n/**\n * Updates the booking status to `CANCELED`.\n *\n *\n * ## Appointments\n *\n * For appointments, the corresponding event is removed from the Bookings\n * calendar.\n *\n * ## Class and course bookings\n *\n * For class or course bookings, the relevant participants are removed\n * from the class session or the course. However, the class session or course\n * remain on the business calendar.\n *\n * ## Notify customers\n *\n * You can specify a `participantNotification.message` for the customer. To send\n * the message, you must also specify `participantNotification.notifyParticipants`\n * as `true`.\n *\n * ## Admin overwrites\n *\n * There are small but important differences when you specify special\n * `flowControlSettings`:\n *\n * - `{\"ignoreCancellationPolicy\": true}`: The call succeeds even if the\n * service's `cancellationPolicy` doesn't allow it.\n * - `{\"withRefund\": true}`: The customer is refunded even if the service's\n * `refundPolicy` doesn't allow it.\n * - `{\"waiveCancellationFee\": true}`: The customer doesn't have to pay\n * the cancellation fee, even if the service's `cancellationPolicy` requires it.\n *\n * When using special `flowControlSettings`, ensure you have sufficient\n * permissions. If you encounter failed calls due to insufficient permissions,\n * consider the following options:\n *\n * - **App developers** can use a higher\n * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),\n * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.\n * - **Site developers** can utilize\n * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).\n *\n * Granting additional permissions and using elevation permits method calls that\n * would typically fail due to authorization checks. Therefore, you should use\n * them intentionally and securely.\n */\nexport function bookingsCancelBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bookingsCancelBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.bookings.v2.Bookings.CancelBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{bookingId}/cancel',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bookingsCancelBooking;\n}\n\n/**\n * Updates the number of participants for a class or course booking and changes\n * the `totalNumberOfParticipants` for the relevant sessions.\n *\n *\n * ## Appointment limitation\n *\n * You can't update the number of participants for appointment bookings.\n *\n * ## Participant information\n *\n * You must specify either `participantsChoices` or `totalParticipants`.\n * The call fails if the specified `participantsChoices` aren't among the\n * supported service options and variants\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n */\nexport function bookingsUpdateNumberOfParticipants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bookingsUpdateNumberOfParticipants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.Bookings.UpdateNumberOfParticipants',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{bookingId}/update_number_of_participants',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bookingsUpdateNumberOfParticipants;\n}\n\nexport function markBookingAsPending(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __markBookingAsPending({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.Bookings.MarkBookingAsPending',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2BookingsUrl({\n        protoPath: '/v2/bookings/{bookingId}/mark_booking_as_pending',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'booking.createdDate' },\n              { path: 'booking.startDate' },\n              { path: 'booking.endDate' },\n              { path: 'booking.updatedDate' },\n              { path: 'booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'booking.contactDetails.fullAddress.geocode.latitude' },\n              { path: 'booking.contactDetails.fullAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __markBookingAsPending;\n}\n\n/**\n * Creates a multi-service booking and all included single-service bookings simultaneously.\n *\n *\n * ## When to call this method\n *\n * Create sequential appointments where customers book related services together. For adding existing single-service bookings to an existing multi-service booking, call Add Bookings to Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking)) instead.\n *\n * ## Requirements and behavior\n *\n * __Package constraints__: Multi-service bookings support 2-8 appointment-based single-service bookings only (course and class bookings aren't supported). All single-service bookings must be at the same location with sequential scheduling and no gaps between appointments.\n *\n * __Timing specification__: You must provide complete `slot` details (`scheduleId`, `startDate`, `endDate`) for each single-service booking. Wix Bookings validates sequential timing but doesn't auto-calculate it.\n *\n * __Package pricing__: The total price equals the sum of individual services. Wix Bookings automatically syncs the payment status from the corresponding Wix eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecommerce/orders/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer paid via an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n *\n * __Package notifications__: Customers receive 1 unified notification for the entire multi-service booking. Wix Bookings doesn't send notifications for the package's individual single-service bookings.\n *\n * ## Related methods\n *\n * Verify availability first with List Multi Service Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)).\n *\n * See Create Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking)) for more details about constraints and effects of creating single-service bookings.\n */\nexport function createMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __createMultiServiceBooking({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'bookings.createdDate' },\n          { path: 'bookings.startDate' },\n          { path: 'bookings.endDate' },\n          { path: 'bookings.updatedDate' },\n          { path: 'bookings.canceledDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'bookings.contactDetails.fullAddress.geocode.latitude' },\n          { path: 'bookings.contactDetails.fullAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.CreateMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath: '/v2/multi_service_bookings',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'multiServiceBooking.bookings.booking.createdDate' },\n              { path: 'multiServiceBooking.bookings.booking.startDate' },\n              { path: 'multiServiceBooking.bookings.booking.endDate' },\n              { path: 'multiServiceBooking.bookings.booking.updatedDate' },\n              { path: 'multiServiceBooking.bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createMultiServiceBooking;\n}\n\n/**\n * Reschedules a multi-service booking by changing the timing for all or specific single-service bookings in the package.\n *\n *\n * This method reschedules single-service bookings within the multi-service booking while maintaining sequential order. You must specify exact new timing for each service to ensure they remain back-to-back with no gaps or overlaps.\n *\n * This method fails if it can't reschedule at least 1 single-service booking. You must provide the current revision number for each single-service booking you're rescheduling to prevent conflicting changes.\n *\n * See Reschedule Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-booking)) for single-service booking rescheduling details.\n */\nexport function rescheduleMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __rescheduleMultiServiceBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.RescheduleMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath:\n          '/v2/multi_service_bookings/{multiServiceBookingId}/reschedule',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'multiServiceBooking.bookings.booking.createdDate' },\n              { path: 'multiServiceBooking.bookings.booking.startDate' },\n              { path: 'multiServiceBooking.bookings.booking.endDate' },\n              { path: 'multiServiceBooking.bookings.booking.updatedDate' },\n              { path: 'multiServiceBooking.bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __rescheduleMultiServiceBooking;\n}\n\n/**\n * Checks if the business can still accommodate an existing multi-service booking and returns overall bookability status, capacity details, and policy violations.\n *\n *\n * Wix Bookings considers:\n * - The relevant services' booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).\n * - The availability of all required resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).\n *\n * Call this method to check if an existing multi-service booking is still valid after business configuration changes.\n * For example, staff changes, policy updates, or capacity modifications.\n *\n * For checking availability before creating new multi-service bookings, call List Multi Service Availability Time Slots\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)) instead.\n */\nexport function getMultiServiceBookingAvailability(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getMultiServiceBookingAvailability({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBookingAvailability',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath:\n          '/v2/multi_service_bookings/{multiServiceBookingId}/get_availability',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __getMultiServiceBookingAvailability;\n}\n\n/**\n * Cancels a multi-service booking and all its associated single-service bookings.\n *\n *\n * Cancels the entire multi-service booking, updating the status of all single-service bookings to `CANCELED`.\n * The call fails if all single-service bookings are already canceled or declined.\n *\n * See Cancel Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/cancel-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/cancel-booking)) for single-service booking cancellation details.\n */\nexport function cancelMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __cancelMultiServiceBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.CancelMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath: '/v2/multi_service_bookings/{multiServiceBookingId}/cancel',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'multiServiceBooking.bookings.booking.createdDate' },\n              { path: 'multiServiceBooking.bookings.booking.startDate' },\n              { path: 'multiServiceBooking.bookings.booking.endDate' },\n              { path: 'multiServiceBooking.bookings.booking.updatedDate' },\n              { path: 'multiServiceBooking.bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __cancelMultiServiceBooking;\n}\n\n/**\n * Updates the status for all single-service bookings in a multi-service booking to `PENDING`.\n *\n *\n * Call this method for multi-service bookings requiring manual business approval before confirmation.\n *\n * ## Status requirements\n *\n * - __Original status__: All single-service bookings must have `CREATED` status.\n * - __Target status__: All bookings move to `PENDING` together (all-or-nothing operation).\n *\n * ## Checkout restrictions\n *\n * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n *\n * ## Additional updates\n *\n * - __Payment status__: Updates if you specify a new `markAsPendingBookingsInfo.paymentStatus`.\n * - __Customer notifications__: Send messages using `participantNotification`.\n * - __Revision control__: Requires current revision numbers for all single-service bookings.\n *\n * See Mark Booking as Pending ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending)) for more details about marking single-service bookings as pending.\n */\nexport function markMultiServiceBookingAsPending(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __markMultiServiceBookingAsPending({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.MarkMultiServiceBookingAsPending',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath:\n          '/v2/multi_service_bookings/{multiServiceBookingId}/mark_as_pending',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'multiServiceBooking.bookings.booking.createdDate' },\n              { path: 'multiServiceBooking.bookings.booking.startDate' },\n              { path: 'multiServiceBooking.bookings.booking.endDate' },\n              { path: 'multiServiceBooking.bookings.booking.updatedDate' },\n              { path: 'multiServiceBooking.bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __markMultiServiceBookingAsPending;\n}\n\n/**\n * Updates the status for all single-service bookings in a multi-service booking to `CONFIRMED`.\n *\n *\n * Call this method for multi-service bookings requiring manual business approval.\n *\n * ## Status requirements\n *\n * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.\n * - __Target status__: All bookings move to `CONFIRMED` together (all-or-nothing operation).\n *\n * ## Checkout restrictions\n *\n * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n *\n * ## Additional updates\n *\n * - __Payment status__: Updates if you specify a new `confirmBookingsInfo.paymentStatus`.\n * - __Customer notifications__: Send messages using `participantNotification`.\n * - __Revision control__: Requires current revision numbers for all single-service bookings.\n *\n * See Confirm Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-booking)) for more details about confirming single-service bookings.\n */\nexport function confirmMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __confirmMultiServiceBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.ConfirmMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath: '/v2/multi_service_bookings/{multiServiceBookingId}/confirm',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'multiServiceBooking.bookings.booking.createdDate' },\n              { path: 'multiServiceBooking.bookings.booking.startDate' },\n              { path: 'multiServiceBooking.bookings.booking.endDate' },\n              { path: 'multiServiceBooking.bookings.booking.updatedDate' },\n              { path: 'multiServiceBooking.bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __confirmMultiServiceBooking;\n}\n\n/**\n * Updates the status for all single-service bookings in a multi-service booking to `DECLINED`.\n *\n *\n * Call this method to reject multi-service bookings that can't be accommodated or don't meet business requirements.\n *\n * ## Status requirements\n *\n * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.\n * - __Target status__: All bookings move to `DECLINED` together (all-or-nothing operation).\n *\n * ## Checkout restrictions\n *\n * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,\n * Wix Bookings automatically manages the bookings' statuses based on payment processing.\n *\n * ## Additional updates\n *\n * - __Payment status__: Updates if you specify a new `declineBookingsInfo.paymentStatus`.\n * - __Customer notifications__: Send messages using `participantNotification`.\n * - __Revision control__: Requires current revision numbers for all single-service bookings.\n *\n * Refer to Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/decline-booking)) for more details about declining single-service bookings.\n */\nexport function declineMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __declineMultiServiceBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.DeclineMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath: '/v2/multi_service_bookings/{multiServiceBookingId}/decline',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'multiServiceBooking.bookings.booking.createdDate' },\n              { path: 'multiServiceBooking.bookings.booking.startDate' },\n              { path: 'multiServiceBooking.bookings.booking.endDate' },\n              { path: 'multiServiceBooking.bookings.booking.updatedDate' },\n              { path: 'multiServiceBooking.bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __declineMultiServiceBooking;\n}\n\n/**\n * Retrieves information about which actions the customer can perform for up to 50 multi-service bookings.\n *\n *\n * For each multi-service booking, the response indicates which actions are currently allowed:\n * - `cancel`: Whether the customer can cancel the multi-service booking.\n * - `reschedule`: Whether the customer can adjust the multi-service booking's timing.\n *\n * Bear the following considerations in mind when calling this method:\n *\n * __Real-time validation__: Wix Bookings calculates allowed actions based on current multi-service booking status, booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)), and available resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) at the time of the call.\n *\n * __Permission context__: Depending on the permissions of the calling [identity](https://dev.wix.com/build-apps/develop-your-app/about-identities), you may see different allowed actions for the same multi-service booking. For example, if the identity has permissions to read only part of the multi-service booking, the response indicates which single-service bookings the identity can read.\n *\n * __Status dependencies__: Allowed actions change as bookings progress through their lifecycle (`CREATED` → `PENDING` → `CONFIRMED`/`DECLINED` → `CANCELED`).\n * Bookings can skip `PENDING` and move directly from `CREATED` to `CONFIRMED`/`DECLINED` based on service configuration.\n */\nexport function bulkGetMultiServiceBookingAllowedActions(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkGetMultiServiceBookingAllowedActions({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.BulkGetMultiServiceBookingAllowedActions',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath: '/v2/bulk/multi_service_bookings/get_allowed_actions',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __bulkGetMultiServiceBookingAllowedActions;\n}\n\n/**\n * Retrieves a multi-service booking and all its associated single-service bookings.\n *\n *\n * Returns the complete multi-service booking information including its ID, associated single-service bookings, and the total number of scheduled single-service bookings.\n *\n * If you call on behalf of an [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) with permissions to read only part of the multi-service booking, only the permitted single-service bookings are retrieved.\n * The returned total number includes single-service bookings for which you don't have permissions.\n *\n * See Query Extended Bookings ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/extended-bookings/query-extended-bookings) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-reader-v2/query-extended-bookings)) for details about retrieving individual single-service bookings and additional metadata.\n */\nexport function getMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getMultiServiceBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath: '/v2/multi_service_bookings/{multiServiceBookingId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'multiServiceBooking.bookings.booking.createdDate' },\n              { path: 'multiServiceBooking.bookings.booking.startDate' },\n              { path: 'multiServiceBooking.bookings.booking.endDate' },\n              { path: 'multiServiceBooking.bookings.booking.updatedDate' },\n              { path: 'multiServiceBooking.bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'multiServiceBooking.bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getMultiServiceBooking;\n}\n\n/**\n * Expands an existing multi-service booking by adding existing single-service bookings to the package.\n *\n *\n * ## When to call this method\n *\n * Call this method to add 1 or more existing single-service bookings to an existing multi-service booking.\n * For creating a new multi-service booking with new single-service bookings, call Create Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-multi-service-booking)) instead.\n *\n * ## Package integration\n *\n * The timing of the single-service bookings to add must be compatible with the multi-service booking's sequential schedule to ensure no gaps or overlaps between services in the updated package.\n *\n * ## Requirements and limitations\n *\n * - __Maximum capacity__: The total number of single-service bookings can't exceed 8.\n * - __Booking eligibility__: You can add only independent single-service bookings that aren't part of another multi-service booking.\n * - __Status compatibility__: Added bookings must have compatible status with the target multi-service booking.\n * - __Revision control__: You must provide current revision numbers for all single-service bookings to add.\n */\nexport function addBookingsToMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __addBookingsToMultiServiceBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.AddBookingsToMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath:\n          '/v2/multi_service_bookings/add_bookings_to_multi_service_booking',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookings.booking.createdDate' },\n              { path: 'bookings.booking.startDate' },\n              { path: 'bookings.booking.endDate' },\n              { path: 'bookings.booking.updatedDate' },\n              { path: 'bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __addBookingsToMultiServiceBooking;\n}\n\n/**\n * Removes single-service bookings from a multi-service booking and converts them to independent single-service bookings.\n *\n *\n * ## Removal options\n *\n * __Remove all permitted bookings__: If you specify an empty `bookings` array, all single-service bookings for which the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has read permissions are removed from the multi-service booking.\n *\n * __Selective removal__: Specify single-service booking IDs and revisions to remove only specific single-service bookings from the package.\n *\n * __Sequential scheduling__: To maintain sequential scheduling, remove only first or last single-service bookings. For middle bookings, first reschedule all relevant single-service bookings to eliminate gaps. To do so, call Reschedule Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-multi-service-booking)) before removing the unwanted bookings.\n *\n * ## Removal behavior\n *\n * __Independent bookings__: Removed single-service bookings become independent bookings.\n * You can manage them using single-service booking methods.\n *\n * __Automatic cleanup__: Multi-service bookings must contain at least 2 services.\n * If removal results in only 1 remaining single-service booking for the multi-service booking, the entire multi-service booking is deleted and the remaining single-service booking becomes a standalone booking.\n *\n * __Revision control__: Specify current revision numbers to prevent conflicting modifications during the removal process.\n */\nexport function removeBookingsFromMultiServiceBooking(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __removeBookingsFromMultiServiceBooking({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.bookings.v2.MultiServiceBookings.RemoveBookingsFromMultiServiceBooking',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsBookingsV2MultiServiceBookingsUrl({\n        protoPath:\n          '/v2/multi_service_bookings/remove_bookings_from_multi_service_booking',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookings.booking.createdDate' },\n              { path: 'bookings.booking.startDate' },\n              { path: 'bookings.booking.endDate' },\n              { path: 'bookings.booking.updatedDate' },\n              { path: 'bookings.booking.canceledDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'bookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'bookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __removeBookingsFromMultiServiceBooking;\n}\n", "import {\n  confirmOrDeclineBooking as publicConfirmOrDeclineBooking,\n  bulkConfirmOrDeclineBooking as publicBulkConfirmOrDeclineBooking,\n  createBooking as publicCreateBooking,\n  bulkCreateBooking as publicBulkCreateBooking,\n  rescheduleBooking as publicRescheduleBooking,\n  confirmBooking as publicConfirmBooking,\n  setBookingSubmissionId as publicSetBookingSubmissionId,\n  updateExtendedFields as publicUpdateExtendedFields,\n  declineBooking as publicDeclineBooking,\n  cancelBooking as publicCancelBooking,\n  updateNumberOfParticipants as publicUpdateNumberOfParticipants,\n  markBookingAsPending as publicMarkBookingAsPending,\n  createMultiServiceBooking as publicCreateMultiServiceBooking,\n  rescheduleMultiServiceBooking as publicRescheduleMultiServiceBooking,\n  getMultiServiceBookingAvailability as publicGetMultiServiceBookingAvailability,\n  cancelMultiServiceBooking as publicCancelMultiServiceBooking,\n  markMultiServiceBookingAsPending as publicMarkMultiServiceBookingAsPending,\n  confirmMultiServiceBooking as publicConfirmMultiServiceBooking,\n  declineMultiServiceBooking as publicDeclineMultiServiceBooking,\n  bulkGetMultiServiceBookingAllowedActions as publicBulkGetMultiServiceBookingAllowedActions,\n  getMultiServiceBooking as publicGetMultiServiceBooking,\n  addBookingsToMultiServiceBooking as publicAddBookingsToMultiServiceBooking,\n  removeBookingsFromMultiServiceBooking as publicRemoveBookingsFromMultiServiceBooking,\n} from './bookings-v2-booking-bookings.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { createEventModule } from '@wix/sdk-runtime/event-definition-modules';\nimport {\n  BuildRESTFunction,\n  MaybeContext,\n  BuildEventDefinition,\n} from '@wix/sdk-types';\nimport { onBookingCanceled as publicOnBookingCanceled } from './bookings-v2-booking-bookings.public.js';\nimport { onBookingConfirmed as publicOnBookingConfirmed } from './bookings-v2-booking-bookings.public.js';\nimport { onBookingCreated as publicOnBookingCreated } from './bookings-v2-booking-bookings.public.js';\nimport { onBookingDeclined as publicOnBookingDeclined } from './bookings-v2-booking-bookings.public.js';\nimport { onBookingNumberOfParticipantsUpdated as publicOnBookingNumberOfParticipantsUpdated } from './bookings-v2-booking-bookings.public.js';\nimport { onBookingRescheduled as publicOnBookingRescheduled } from './bookings-v2-booking-bookings.public.js';\nimport { onBookingUpdated as publicOnBookingUpdated } from './bookings-v2-booking-bookings.public.js';\n\nexport const confirmOrDeclineBooking: MaybeContext<\n  BuildRESTFunction<typeof publicConfirmOrDeclineBooking> &\n    typeof publicConfirmOrDeclineBooking\n> = /*#__PURE__*/ createRESTModule(publicConfirmOrDeclineBooking);\nexport const bulkConfirmOrDeclineBooking: MaybeContext<\n  BuildRESTFunction<typeof publicBulkConfirmOrDeclineBooking> &\n    typeof publicBulkConfirmOrDeclineBooking\n> = /*#__PURE__*/ createRESTModule(publicBulkConfirmOrDeclineBooking);\nexport const createBooking: MaybeContext<\n  BuildRESTFunction<typeof publicCreateBooking> & typeof publicCreateBooking\n> = /*#__PURE__*/ createRESTModule(publicCreateBooking);\nexport const bulkCreateBooking: MaybeContext<\n  BuildRESTFunction<typeof publicBulkCreateBooking> &\n    typeof publicBulkCreateBooking\n> = /*#__PURE__*/ createRESTModule(publicBulkCreateBooking);\nexport const rescheduleBooking: MaybeContext<\n  BuildRESTFunction<typeof publicRescheduleBooking> &\n    typeof publicRescheduleBooking\n> = /*#__PURE__*/ createRESTModule(publicRescheduleBooking);\nexport const confirmBooking: MaybeContext<\n  BuildRESTFunction<typeof publicConfirmBooking> & typeof publicConfirmBooking\n> = /*#__PURE__*/ createRESTModule(publicConfirmBooking);\nexport const setBookingSubmissionId: MaybeContext<\n  BuildRESTFunction<typeof publicSetBookingSubmissionId> &\n    typeof publicSetBookingSubmissionId\n> = /*#__PURE__*/ createRESTModule(publicSetBookingSubmissionId);\nexport const updateExtendedFields: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateExtendedFields> &\n    typeof publicUpdateExtendedFields\n> = /*#__PURE__*/ createRESTModule(publicUpdateExtendedFields);\nexport const declineBooking: MaybeContext<\n  BuildRESTFunction<typeof publicDeclineBooking> & typeof publicDeclineBooking\n> = /*#__PURE__*/ createRESTModule(publicDeclineBooking);\nexport const cancelBooking: MaybeContext<\n  BuildRESTFunction<typeof publicCancelBooking> & typeof publicCancelBooking\n> = /*#__PURE__*/ createRESTModule(publicCancelBooking);\nexport const updateNumberOfParticipants: MaybeContext<\n  BuildRESTFunction<typeof publicUpdateNumberOfParticipants> &\n    typeof publicUpdateNumberOfParticipants\n> = /*#__PURE__*/ createRESTModule(publicUpdateNumberOfParticipants);\nexport const markBookingAsPending: MaybeContext<\n  BuildRESTFunction<typeof publicMarkBookingAsPending> &\n    typeof publicMarkBookingAsPending\n> = /*#__PURE__*/ createRESTModule(publicMarkBookingAsPending);\nexport const createMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicCreateMultiServiceBooking> &\n    typeof publicCreateMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicCreateMultiServiceBooking);\nexport const rescheduleMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicRescheduleMultiServiceBooking> &\n    typeof publicRescheduleMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicRescheduleMultiServiceBooking);\nexport const getMultiServiceBookingAvailability: MaybeContext<\n  BuildRESTFunction<typeof publicGetMultiServiceBookingAvailability> &\n    typeof publicGetMultiServiceBookingAvailability\n> = /*#__PURE__*/ createRESTModule(publicGetMultiServiceBookingAvailability);\nexport const cancelMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicCancelMultiServiceBooking> &\n    typeof publicCancelMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicCancelMultiServiceBooking);\nexport const markMultiServiceBookingAsPending: MaybeContext<\n  BuildRESTFunction<typeof publicMarkMultiServiceBookingAsPending> &\n    typeof publicMarkMultiServiceBookingAsPending\n> = /*#__PURE__*/ createRESTModule(publicMarkMultiServiceBookingAsPending);\nexport const confirmMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicConfirmMultiServiceBooking> &\n    typeof publicConfirmMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicConfirmMultiServiceBooking);\nexport const declineMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicDeclineMultiServiceBooking> &\n    typeof publicDeclineMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicDeclineMultiServiceBooking);\nexport const bulkGetMultiServiceBookingAllowedActions: MaybeContext<\n  BuildRESTFunction<typeof publicBulkGetMultiServiceBookingAllowedActions> &\n    typeof publicBulkGetMultiServiceBookingAllowedActions\n> = /*#__PURE__*/ createRESTModule(\n  publicBulkGetMultiServiceBookingAllowedActions\n);\nexport const getMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicGetMultiServiceBooking> &\n    typeof publicGetMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicGetMultiServiceBooking);\nexport const addBookingsToMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicAddBookingsToMultiServiceBooking> &\n    typeof publicAddBookingsToMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicAddBookingsToMultiServiceBooking);\nexport const removeBookingsFromMultiServiceBooking: MaybeContext<\n  BuildRESTFunction<typeof publicRemoveBookingsFromMultiServiceBooking> &\n    typeof publicRemoveBookingsFromMultiServiceBooking\n> = /*#__PURE__*/ createRESTModule(publicRemoveBookingsFromMultiServiceBooking);\n/**\n * Triggered when a booking is canceled.\n */\nexport const onBookingCanceled: BuildEventDefinition<\n  typeof publicOnBookingCanceled\n> = createEventModule(publicOnBookingCanceled);\n/**\n * Triggered when a booking is confirmed.\n */\nexport const onBookingConfirmed: BuildEventDefinition<\n  typeof publicOnBookingConfirmed\n> = createEventModule(publicOnBookingConfirmed);\n/**\n * Triggered when a booking is created.\n */\nexport const onBookingCreated: BuildEventDefinition<\n  typeof publicOnBookingCreated\n> = createEventModule(publicOnBookingCreated);\n/**\n * Triggered when a booking is declined.\n */\nexport const onBookingDeclined: BuildEventDefinition<\n  typeof publicOnBookingDeclined\n> = createEventModule(publicOnBookingDeclined);\n/**\n * Triggered when the number of participants is updated.\n */\nexport const onBookingNumberOfParticipantsUpdated: BuildEventDefinition<\n  typeof publicOnBookingNumberOfParticipantsUpdated\n> = createEventModule(publicOnBookingNumberOfParticipantsUpdated);\n/**\n * Triggered when a booking is rescheduled.\n */\nexport const onBookingRescheduled: BuildEventDefinition<\n  typeof publicOnBookingRescheduled\n> = createEventModule(publicOnBookingRescheduled);\n/**\n * Triggered when a booked schedule is updated.\n */\nexport const onBookingUpdated: BuildEventDefinition<\n  typeof publicOnBookingUpdated\n> = createEventModule(publicOnBookingUpdated);\n\nexport {\n  MultiServiceBookingType,\n  LocationType,\n  ValueType,\n  BookingStatus,\n  PaymentStatus,\n  SelectedPaymentOption,\n  Platform,\n  Actor,\n  IdentificationDataIdentityType,\n  WebhookIdentityType,\n  LocationLocationType,\n  IdentityType,\n  SortOrder,\n} from './bookings-v2-booking-bookings.universal.js';\nexport {\n  Booking,\n  BookingParticipantsInfoOneOf,\n  BookedEntity,\n  BookedEntityItemOneOf,\n  BookedSlot,\n  BookedResource,\n  Location,\n  BookedSchedule,\n  ContactDetails,\n  Address,\n  AddressStreetOneOf,\n  StreetAddress,\n  AddressLocation,\n  Subdivision,\n  CustomFormField,\n  BookingSource,\n  ParticipantNotification,\n  CommonIdentificationData,\n  CommonIdentificationDataIdOneOf,\n  FlowControlSettings,\n  ExtendedFields,\n  ParticipantChoices,\n  ServiceChoices,\n  ServiceChoice,\n  ServiceChoiceChoiceOneOf,\n  Duration,\n  MultiServiceBookingInfo,\n  BookedAddOn,\n  BookingFormFilled,\n  SetBookingFormAndSubmissionIdRequest,\n  SetBookingFormAndSubmissionIdRequestCreatedByOneOf,\n  SetBookingFormAndSubmissionIdResponse,\n  DomainEvent,\n  DomainEventBodyOneOf,\n  EntityCreatedEvent,\n  RestoreInfo,\n  EntityUpdatedEvent,\n  EntityDeletedEvent,\n  ActionEvent,\n  MessageEnvelope,\n  WebhooksIdentificationData,\n  WebhooksIdentificationDataIdOneOf,\n  V2CreateBookingRequest,\n  V2CreateBookingRequestBookableItemOneOf,\n  V2CreateBookingRequestParticipantsInfoOneOf,\n  Slot,\n  SlotResource,\n  CreateBookingRequestFlowControlSettings,\n  V2CreateBookingResponse,\n  V2CancelBookingRequest,\n  CancelBookingRequestFlowControlSettings,\n  V2CancelBookingResponse,\n  V2RescheduleBookingRequest,\n  V2RescheduleBookingRequestParticipantsInfoOneOf,\n  RescheduleBookingRequestFlowControlSettings,\n  V2RescheduleBookingResponse,\n  V2ConfirmBookingRequest,\n  V2ConfirmBookingResponse,\n  V2DeclineBookingRequest,\n  V2DeclineBookingResponse,\n  V2UpdateNumberOfParticipantsRequest,\n  V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf,\n  V2UpdateNumberOfParticipantsResponse,\n  ConfirmOrDeclineBookingRequest,\n  ConfirmOrDeclineBookingResponse,\n  BulkConfirmOrDeclineBookingRequest,\n  BulkConfirmOrDeclineBookingRequestBookingDetails,\n  BulkConfirmOrDeclineBookingResponse,\n  BulkBookingResult,\n  ItemMetadata,\n  ApplicationError,\n  BulkActionMetadata,\n  BookingChanged,\n  CreateBookingRequest,\n  CreateBookingFlowControlSettings,\n  CreateBookingResponse,\n  UpdateBookingRequest,\n  UpdateBookingResponse,\n  LegacyCreateBookingRequest,\n  LegacyCreateBookingResponse,\n  BulkUpdateBookingRequest,\n  MaskedBooking,\n  BulkUpdateBookingResponse,\n  BulkCreateBookingRequest,\n  CreateBookingInfo,\n  BulkCreateBookingResponse,\n  RescheduleBookingRequest,\n  RescheduleBookingRequestParticipantsInfoOneOf,\n  V2Slot,\n  SlotSlotResource,\n  SlotLocation,\n  RescheduleBookingFlowControlSettings,\n  RescheduleBookingResponse,\n  BookingRescheduled,\n  BookingRescheduledPreviousParticipantsInfoOneOf,\n  IdentificationData,\n  IdentificationDataIdOneOf,\n  BulkRescheduleBookingRequest,\n  BulkRescheduleBookingRequestBooking,\n  SlotBookings,\n  BulkRescheduleBookingResponse,\n  BulkUpdateBookedScheduleRequest,\n  BookingDetails,\n  BulkUpdateBookedScheduleResponse,\n  QueryBookingsRequest,\n  QueryV2,\n  QueryV2PagingMethodOneOf,\n  Sorting,\n  Paging,\n  CursorPaging,\n  QueryBookingsResponse,\n  PagingMetadataV2,\n  Cursors,\n  ConfirmRequest,\n  ConfirmResponse,\n  ConfirmBookingRequest,\n  ConfirmBookingFlowControlSettings,\n  ConfirmBookingResponse,\n  BookingConfirmed,\n  ConsistentQueryBookingsRequest,\n  ConsistentQueryBookingsResponse,\n  SetBookingSessionIdRequest,\n  SetBookingSessionIdResponse,\n  SetBookingSubmissionIdRequest,\n  SetBookingSubmissionIdResponse,\n  UpdateExtendedFieldsRequest,\n  UpdateExtendedFieldsResponse,\n  DeclineBookingRequest,\n  DeclineBookingFlowControlSettings,\n  DeclineBookingResponse,\n  BookingDeclined,\n  CancelBookingRequest,\n  CancelBookingFlowControlSettings,\n  CancelBookingResponse,\n  BookingCanceled,\n  UpdateNumberOfParticipantsRequest,\n  UpdateNumberOfParticipantsRequestParticipantsInfoOneOf,\n  UpdateNumberOfParticipantsResponse,\n  NumberOfParticipantsUpdated,\n  NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf,\n  BulkCalculateAllowedActionsRequest,\n  BulkCalculateAllowedActionsResponse,\n  BulkCalculateAllowedActionsResult,\n  AllowedActions,\n  GetSlotAvailabilityRequest,\n  GetSlotAvailabilityResponse,\n  SlotAvailability,\n  WaitingList,\n  BookingPolicyViolations,\n  BookingPolicySettings,\n  AvailableResources,\n  GetScheduleAvailabilityRequest,\n  GetScheduleAvailabilityResponse,\n  ScheduleAvailability,\n  MarkBookingAsPendingRequest,\n  MarkBookingAsPendingFlowControlSettings,\n  MarkBookingAsPendingResponse,\n  BookingMarkedAsPending,\n  MigrationCheckIfClashesWithBlockedTimeRequest,\n  MsidAndBookingId,\n  MigrationCheckIfClashesWithBlockedTimeResponse,\n  Clash,\n  CountBookingsRequest,\n  CountBookingsResponse,\n  CreateMultiServiceBookingRequest,\n  CreateMultiServiceBookingResponse,\n  MultiServiceBooking,\n  BookingResult,\n  RescheduleMultiServiceBookingRequest,\n  RescheduleBookingInfo,\n  RescheduleBookingInfoParticipantsInfoOneOf,\n  RescheduleMultiServiceBookingResponse,\n  GetMultiServiceBookingAvailabilityRequest,\n  GetMultiServiceBookingAvailabilityResponse,\n  GetMultiServiceBookingAvailabilityResponseBookingInfo,\n  CancelMultiServiceBookingRequest,\n  CancelMultiServiceBookingResponse,\n  MarkMultiServiceBookingAsPendingRequest,\n  BookingInfo,\n  MarkMultiServiceBookingAsPendingResponse,\n  ConfirmMultiServiceBookingRequest,\n  ConfirmMultiServiceBookingResponse,\n  DeclineMultiServiceBookingRequest,\n  DeclineMultiServiceBookingResponse,\n  BulkGetMultiServiceBookingAllowedActionsRequest,\n  BulkGetMultiServiceBookingAllowedActionsResponse,\n  GetMultiServiceBookingRequest,\n  GetMultiServiceBookingResponse,\n  MultiServiceBookingMetadata,\n  AddBookingsToMultiServiceBookingRequest,\n  BookingIdAndRevision,\n  AddBookingsToMultiServiceBookingResponse,\n  RemoveBookingsFromMultiServiceBookingRequest,\n  RemoveBookingsFromMultiServiceBookingResponse,\n  BaseEventMetadata,\n  EventMetadata,\n  BookingCanceledEnvelope,\n  BookingConfirmedEnvelope,\n  BookingCreatedEnvelope,\n  BookingDeclinedEnvelope,\n  BookingNumberOfParticipantsUpdatedEnvelope,\n  BookingRescheduledEnvelope,\n  BookingUpdatedEnvelope,\n  ConfirmOrDeclineBookingOptions,\n  BulkConfirmOrDeclineBookingOptions,\n  CreateBookingOptions,\n  BulkCreateBookingOptions,\n  RescheduleBookingOptions,\n  RescheduleBookingOptionsParticipantsInfoOneOf,\n  ConfirmBookingOptions,\n  UpdateExtendedFieldsOptions,\n  DeclineBookingOptions,\n  CancelBookingOptions,\n  UpdateNumberOfParticipantsOptions,\n  UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf,\n  MarkBookingAsPendingOptions,\n  CreateMultiServiceBookingOptions,\n  RescheduleMultiServiceBookingOptions,\n  CancelMultiServiceBookingOptions,\n  MarkMultiServiceBookingAsPendingOptions,\n  ConfirmMultiServiceBookingOptions,\n  DeclineMultiServiceBookingOptions,\n  AddBookingsToMultiServiceBookingOptions,\n  RemoveBookingsFromMultiServiceBookingOptions,\n} from './bookings-v2-booking-bookings.universal.js';\nexport {\n  MultiServiceBookingTypeWithLiterals,\n  LocationTypeWithLiterals,\n  ValueTypeWithLiterals,\n  BookingStatusWithLiterals,\n  PaymentStatusWithLiterals,\n  SelectedPaymentOptionWithLiterals,\n  PlatformWithLiterals,\n  ActorWithLiterals,\n  IdentificationDataIdentityTypeWithLiterals,\n  WebhookIdentityTypeWithLiterals,\n  LocationLocationTypeWithLiterals,\n  IdentityTypeWithLiterals,\n  SortOrderWithLiterals,\n  ConfirmOrDeclineBookingApplicationErrors,\n  BulkConfirmOrDeclineBookingApplicationErrors,\n  CreateBookingApplicationErrors,\n  BulkCreateBookingApplicationErrors,\n  RescheduleBookingApplicationErrors,\n  ConfirmBookingApplicationErrors,\n  SetBookingSubmissionIdApplicationErrors,\n  UpdateExtendedFieldsApplicationErrors,\n  DeclineBookingApplicationErrors,\n  CancelBookingApplicationErrors,\n  UpdateNumberOfParticipantsApplicationErrors,\n  MarkBookingAsPendingApplicationErrors,\n  CreateMultiServiceBookingApplicationErrors,\n  RescheduleMultiServiceBookingApplicationErrors,\n  GetMultiServiceBookingAvailabilityApplicationErrors,\n  CancelMultiServiceBookingApplicationErrors,\n  MarkMultiServiceBookingAsPendingApplicationErrors,\n  ConfirmMultiServiceBookingApplicationErrors,\n  DeclineMultiServiceBookingApplicationErrors,\n  AddBookingsToMultiServiceBookingApplicationErrors,\n  RemoveBookingsFromMultiServiceBookingApplicationErrors,\n} from './bookings-v2-booking-bookings.universal.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAAA;AAAA,EAAA,mCAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,gDAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,iCAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,kCAAAC;AAAA,EAAA,+BAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,iCAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,kCAAAC;AAAA,EAAA,8BAAAC;AAAA,EAAA,0CAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA,wCAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,0BAAAC;AAAA,EAAA,wBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,4CAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA,wBAAAC;AAAA,EAAA,6CAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,qCAAAC;AAAA,EAAA,8BAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA,kCAAAC;AAAA;AAAA;;;ACAA,IAAAC,iCAAwD;AACxD,IAAAC,gBAA6C;AAC7C,IAAAC,oBAAqD;AACrD,IAAAC,0BAA+B;AAC/B,uBAA8D;;;ACJ9D,6BAAoD;AACpD,oCAGO;;;ACJP,0BAAkC;AAClC,mBAA6C;AAC7C,IAAAC,gBAA6C;AAC7C,uBAAqD;AACrD,IAAAC,oBAAqD;AACrD,6BAA+B;AAC/B,IAAAC,uBAA2B;AAI3B,SAAS,4DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,SAAS,gDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,SAAS,sDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AA4Cd,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,sDAAsD;AAAA,QACzD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACC,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,4BACd,SAC4B;AAC5B,WAAS,8BAA8B,EAAE,KAAK,GAAQ;AACpD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,sDAAsD;AAAA,QACzD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,yBAAyB;AAAA,YACjC,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsHO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,oBAAoB;AAAA,UAC5B,EAAE,MAAM,kBAAkB;AAAA,UAC1B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,uBAAuB;AAAA,QACjC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,QACjE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiBO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yCAAyC;AAAA,UACjD,EAAE,MAAM,uCAAuC;AAAA,UAC/C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,yCAAyC;AAAA,UACjD,EAAE,MAAM,0CAA0C;AAAA,QACpD;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,yBAAyB;AAAA,YACjC,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA8DO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAuDO,SAAS,uBACd,SAC4B;AAC5B,WAAS,yBAAyB,EAAE,KAAK,GAAQ;AAC/C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,uBACd,SAC4B;AAC5B,WAAS,yBAAyB,EAAE,KAAK,GAAQ;AAC/C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAuCO,SAAS,uBACd,SAC4B;AAC5B,WAAS,yBAAyB,EAAE,KAAK,GAAQ;AAC/C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiDO,SAAS,sBACd,SAC4B;AAC5B,WAAS,wBAAwB,EAAE,KAAK,GAAQ;AAC9C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAkBO,SAAS,mCACd,SAC4B;AAC5B,WAAS,qCAAqC,EAAE,KAAK,GAAQ;AAC3D,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,SAAS,qBACd,SAC4B;AAC5B,WAAS,uBAAuB,EAAE,KAAK,GAAQ;AAC7C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,gDAAgD;AAAA,QACnD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,oBAAoB;AAAA,YAC5B,EAAE,MAAM,kBAAkB;AAAA,YAC1B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,uBAAuB;AAAA,UACjC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA0BO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,qBAAiB,uCAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,mBAAmB;AAAA,UAC3B,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,wBAAwB;AAAA,QAClC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAYO,SAAS,8BACd,SAC4B;AAC5B,WAAS,gCAAgC,EAAE,KAAK,GAAQ;AACtD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgBO,SAAS,mCACd,SAC4B;AAC5B,WAAS,qCAAqC,EAAE,KAAK,GAAQ;AAC3D,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA0BO,SAAS,iCACd,SAC4B;AAC5B,WAAS,mCAAmC,EAAE,KAAK,GAAQ;AACzD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA0BO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA0BO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAmBO,SAAS,yCACd,SAC4B;AAC5B,WAAS,2CAA2C,EAAE,KAAK,GAAQ;AACjE,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,uBACd,SAC4B;AAC5B,WAAS,yBAAyB,EAAE,KAAK,GAAQ;AAC/C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsBO,SAAS,iCACd,SAC4B;AAC5B,WAAS,mCAAmC,EAAE,KAAK,GAAQ;AACzD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,+BAA+B;AAAA,YACvC,EAAE,MAAM,6BAA6B;AAAA,YACrC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,+BAA+B;AAAA,YACvC,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAwBO,SAAS,sCACd,SAC4B;AAC5B,WAAS,wCAAwC,EAAE,KAAK,GAAQ;AAC9D,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,iBAClB,uCAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,+BAA+B;AAAA,YACvC,EAAE,MAAM,6BAA6B;AAAA,YACrC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,+BAA+B;AAAA,YACvC,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADztDO,IAAK,0BAAL,kBAAKC,6BAAL;AAEL,EAAAA,yBAAA,yBAAsB;AAEtB,EAAAA,yBAAA,uBAAoB;AAEpB,EAAAA,yBAAA,uBAAoB;AANV,SAAAA;AAAA,GAAA;AAoKL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,oBAAiB;AACjB,EAAAA,cAAA,kBAAe;AACf,EAAAA,cAAA,YAAS;AAJC,SAAAA;AAAA,GAAA;AAkKL,IAAK,YAAL,kBAAKC,eAAL;AAEL,EAAAA,WAAA,gBAAa;AAEb,EAAAA,WAAA,eAAY;AAEZ,EAAAA,WAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AAiBL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,aAAU;AAKV,EAAAA,eAAA,eAAY;AAMZ,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,aAAU;AAEV,EAAAA,eAAA,cAAW;AAMX,EAAAA,eAAA,kBAAe;AAvBL,SAAAA;AAAA,GAAA;AAwCL,IAAK,gBAAL,kBAAKC,mBAAL;AAEL,EAAAA,eAAA,eAAY;AAEZ,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,UAAO;AAEP,EAAAA,eAAA,oBAAiB;AAEjB,EAAAA,eAAA,cAAW;AAEX,EAAAA,eAAA,YAAS;AAZC,SAAAA;AAAA,GAAA;AAgCL,IAAK,wBAAL,kBAAKC,2BAAL;AAEL,EAAAA,uBAAA,eAAY;AAEZ,EAAAA,uBAAA,aAAU;AAEV,EAAAA,uBAAA,YAAS;AAET,EAAAA,uBAAA,gBAAa;AAKb,EAAAA,uBAAA,wBAAqB;AAbX,SAAAA;AAAA,GAAA;AA2CL,IAAK,WAAL,kBAAKC,cAAL;AACL,EAAAA,UAAA,wBAAqB;AACrB,EAAAA,UAAA,SAAM;AACN,EAAAA,UAAA,gBAAa;AAHH,SAAAA;AAAA,GAAA;AAaL,IAAK,QAAL,kBAAKC,WAAL;AACL,EAAAA,OAAA,qBAAkB;AAClB,EAAAA,OAAA,cAAW;AACX,EAAAA,OAAA,cAAW;AAHD,SAAAA;AAAA,GAAA;AA6EL,IAAK,iCAAL,kBAAKC,oCAAL;AACL,EAAAA,gCAAA,aAAU;AACV,EAAAA,gCAAA,uBAAoB;AACpB,EAAAA,gCAAA,YAAS;AACT,EAAAA,gCAAA,cAAW;AACX,EAAAA,gCAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAmZL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAmvBL,IAAK,uBAAL,kBAAKC,0BAAL;AAEL,EAAAA,sBAAA,eAAY;AAEZ,EAAAA,sBAAA,oBAAiB;AAEjB,EAAAA,sBAAA,kBAAe;AAEf,EAAAA,sBAAA,YAAS;AARC,SAAAA;AAAA,GAAA;AA4LL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,uBAAoB;AACpB,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,cAAW;AACX,EAAAA,cAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAmKL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AAqiEZ,eAAsBC,yBACpB,WACA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,eAAe,SAAS;AAAA,EAC1B,CAAC;AAED,QAAM,UAC2B,wBAAwB,OAAO;AAEhE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,eAAe;AAAA,QACjB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA6BA,eAAsBC,6BACpB,SAKA,SAwBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,cAAc,SAAS;AAAA,EACzB,CAAC;AAED,QAAM,UAC2B,4BAA4B,OAAO;AAEpE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,SAAS;AAAA,UACT,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,WAAW,SAAS;AAAA,IACvB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmIA,eAAsBE,eACpB,SAKA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,iBAAiB,SAAS;AAAA,IAC1B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAAyC,cAAc,OAAO;AAEpE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,SAAS;AAAA,UACT,yBAAyB;AAAA,UACzB,iBAAiB;AAAA,UACjB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,WAAW,SAAS;AAAA,IACvB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA+CA,eAAsBG,mBACpB,oBAOA,SAwBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,kBAAkB,SAAS;AAAA,EAC7B,CAAC;AAED,QAAM,UAAyC,kBAAkB,OAAO;AAExE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAH;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,oBAAoB;AAAA,UACpB,kBAAkB;AAAA,QACpB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,sBAAsB,SAAS;AAAA,IAClC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA8EA,eAAsBI,mBACpB,WACA,MACA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA,UAAU,SAAS;AAAA,IACnB,yBAAyB,SAAS;AAAA,IAClC,qBAAqB,SAAS;AAAA,IAC9B,mBAAmB,SAAS;AAAA,IAC5B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAAyC,kBAAkB,OAAO;AAExE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAJ;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,MAAM;AAAA,UACN,UAAU;AAAA,UACV,yBAAyB;AAAA,UACzB,qBAAqB;AAAA,UACrB,mBAAmB;AAAA,UACnB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,QAAQ,SAAS;AAAA,IACjC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA8GA,eAAsB,eACpB,WACA,UACA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,iBAAiB,SAAS;AAAA,IAC1B,eAAe,SAAS;AAAA,IACxB,cAAc,SAAS;AAAA,IACvB,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAC2B,uBAAuB,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,yBAAyB;AAAA,UACzB,iBAAiB;AAAA,UACjB,eAAe;AAAA,UACf,cAAc;AAAA,UACd,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,YAAY,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoCA,eAAsBK,wBACpB,WACA,cAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UAC2B,uBAAuB,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAL;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,WAAW,QAAQ,cAAc,OAAO;AAAA,QACpE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,cAAc;AAAA,IAC9B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsBA,eAAsBM,sBACpB,KACA,WACA,SAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,IAAI;AAAA,IACJ;AAAA,IACA,eAAe,SAAS;AAAA,EAC1B,CAAC;AAED,QAAM,UAAyC,qBAAqB,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAN;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,eAAe;AAAA,QACjB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,aAAa,SAAS;AAAA,IAChC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAwDA,eAAsB,eACpB,WACA,UACA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,eAAe,SAAS;AAAA,IACxB,cAAc,SAAS;AAAA,IACvB,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAC2B,uBAAuB,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,yBAAyB;AAAA,UACzB,eAAe;AAAA,UACf,cAAc;AAAA,UACd,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,YAAY,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA2EA,eAAsB,cACpB,WACA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,qBAAqB,SAAS;AAAA,IAC9B,UAAU,SAAS;AAAA,EACrB,CAAC;AAED,QAAM,UAAyC,sBAAsB,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,yBAAyB;AAAA,UACzB,qBAAqB;AAAA,UACrB,UAAU;AAAA,QACZ;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA8CA,eAAsB,2BACpB,WACA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,UAAU,SAAS;AAAA,IACnB,mBAAmB,SAAS;AAAA,IAC5B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAC2B,mCAAmC,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkDA,eAAsBO,sBACpB,WACA,UACA,SAgCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,iBAAiB,SAAS;AAAA,IAC1B,eAAe,SAAS;AAAA,IACxB,cAAc,SAAS;AAAA,IACvB,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAAyC,qBAAqB,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAP;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,yBAAyB;AAAA,UACzB,iBAAiB;AAAA,UACjB,eAAe;AAAA,UACf,cAAc;AAAA,UACd,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,YAAY,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoEA,eAAsBQ,2BACpB,UAWA,SAiBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,iBAAiB,SAAS;AAAA,IAC1B,qBAAqB,SAAS;AAAA,IAC9B,kBAAkB,SAAS;AAAA,IAC3B,yBAAyB,SAAS;AAAA,EACpC,CAAC;AAED,QAAM,UAC2B,0BAA0B,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAR;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,UAAU;AAAA,UACV,yBAAyB;AAAA,UACzB,iBAAiB;AAAA,UACjB,qBAAqB;AAAA,UACrB,kBAAkB;AAAA,UAClB,yBAAyB;AAAA,QAC3B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY,SAAS;AAAA,IACxB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkDA,eAAsBS,+BACpB,uBACA,wBAKA,SAiBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,qBAAqB,SAAS;AAAA,IAC9B,kBAAkB,SAAS;AAAA,EAC7B,CAAC;AAED,QAAM,UAC2B,8BAA8B,OAAO;AAEtE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAT;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,uBAAuB;AAAA,UACvB,wBAAwB;AAAA,UACxB,yBAAyB;AAAA,UACzB,qBAAqB;AAAA,UACrB,kBAAkB;AAAA,QACpB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,yBAAyB,0BAA0B,SAAS;AAAA,IAC/D;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmCA,eAAsBU,oCACpB,uBASA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAC2B,mCAAmC,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAV;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,uBAAuB,OAAO;AAAA,QAC1D,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,uBAAuB;AAAA,IAC1B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmBA,eAAsBW,2BACpB,uBACA,SAiBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,yBAAyB,SAAS;AAAA,IAClC,qBAAqB,SAAS;AAAA,IAC9B,kBAAkB,SAAS;AAAA,EAC7B,CAAC;AAED,QAAM,UAC2B,0BAA0B,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAX;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,uBAAuB;AAAA,UACvB,yBAAyB;AAAA,UACzB,qBAAqB;AAAA,UACrB,kBAAkB;AAAA,QACpB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,yBAAyB,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA6CA,eAAsBY,kCACpB,uBACA,SAiBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,2BAA2B,SAAS;AAAA,IACpC,yBAAyB,SAAS;AAAA,IAClC,iBAAiB,SAAS;AAAA,IAC1B,cAAc,SAAS;AAAA,IACvB,kBAAkB,SAAS;AAAA,IAC3B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAC2B,iCAAiC,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAZ;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,uBAAuB;AAAA,UACvB,2BAA2B;AAAA,UAC3B,yBAAyB;AAAA,UACzB,iBAAiB;AAAA,UACjB,cAAc;AAAA,UACd,kBAAkB;AAAA,UAClB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,yBAAyB,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA4DA,eAAsBa,4BACpB,uBACA,SAiBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,qBAAqB,SAAS;AAAA,IAC9B,yBAAyB,SAAS;AAAA,IAClC,iBAAiB,SAAS;AAAA,IAC1B,cAAc,SAAS;AAAA,IACvB,kBAAkB,SAAS;AAAA,IAC3B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAC2B,2BAA2B,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAb;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,uBAAuB;AAAA,UACvB,qBAAqB;AAAA,UACrB,yBAAyB;AAAA,UACzB,iBAAiB;AAAA,UACjB,cAAc;AAAA,UACd,kBAAkB;AAAA,UAClB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,yBAAyB,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAwDA,eAAsBc,4BACpB,uBACA,SAiBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,qBAAqB,SAAS;AAAA,IAC9B,yBAAyB,SAAS;AAAA,IAClC,cAAc,SAAS;AAAA,IACvB,kBAAkB,SAAS;AAAA,IAC3B,qBAAqB,SAAS;AAAA,EAChC,CAAC;AAED,QAAM,UAC2B,2BAA2B,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAd;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,uBAAuB;AAAA,UACvB,qBAAqB;AAAA,UACrB,yBAAyB;AAAA,UACzB,cAAc;AAAA,UACd,kBAAkB;AAAA,UAClB,qBAAqB;AAAA,QACvB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,yBAAyB,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA0CA,eAAsBe,0CACpB,wBAgBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAC2B;AAAA,IAC7B;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAf;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,wBAAwB,OAAO;AAAA,QAC3D,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,wBAAwB;AAAA,IAC3B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAqBA,eAAsBgB,wBACpB,uBAeA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAC2B,uBAAuB,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GACtD;AAAA,EACN,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAhB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,uBAAuB,OAAO;AAAA,QAC1D,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,uBAAuB;AAAA,IAC1B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA+BA,eAAsBiB,kCACpB,uBACA,SAqBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,UAAU,SAAS;AAAA,IACnB,kBAAkB,SAAS;AAAA,EAC7B,CAAC;AAED,QAAM,UAC2B,iCAAiC,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAjB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,uBAAuB;AAAA,UACvB,UAAU;AAAA,UACV,kBAAkB;AAAA,QACpB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,yBAAyB,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA6CA,eAAsBkB,uCACpB,uBACA,SAiBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,UAAU,SAAS;AAAA,IACnB,kBAAkB,SAAS;AAAA,EAC7B,CAAC;AAED,QAAM,UAC2B;AAAA,IAC7B;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAlB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,uBAAuB;AAAA,UACvB,UAAU;AAAA,UACV,kBAAkB;AAAA,QACpB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,yBAAyB,SAAS;AAAA,IACrC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;AD3vNO,SAASmB,yBACd,YACkC;AAClC,SAAO,CAAC,WAAmB,YACzBA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAgFO,SAASC,6BACd,YACsC;AACtC,SAAO,CACL,SAKA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA6CO,SAASC,eAAc,YAAgD;AAC5E,SAAO,CACL,SAKA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiKO,SAASC,mBACd,YAC4B;AAC5B,SAAO,CACL,oBAOA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAwDO,SAASC,mBACd,YAC4B;AAC5B,SAAO,CACL,WACA,MACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAwGO,SAASC,gBACd,YACyB;AACzB,SAAO,CACL,WACA,UACA,YAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAmGO,SAASC,wBACd,YACiC;AACjC,SAAO,CAAC,WAAmB,iBACzBA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA0CO,SAASC,sBACd,YAC+B;AAC/B,SAAO,CACL,KACA,WACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA0BO,SAASC,gBACd,YACyB;AACzB,SAAO,CACL,WACA,UACA,YAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAoFO,SAASC,eAAc,YAAgD;AAC5E,SAAO,CACL,WACA,YAEA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAyFO,SAASC,4BACd,YACqC;AACrC,SAAO,CACL,WACA,YAEA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAyDO,SAASC,sBACd,YAC+B;AAC/B,SAAO,CACL,WACA,UACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAgDO,SAASC,2BACd,YACoC;AACpC,SAAO,CACL,UAWA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiEO,SAASC,+BACd,YACwC;AACxC,SAAO,CACL,uBACA,wBAKA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA0CO,SAASC,oCACd,YAC6C;AAC7C,SAAO,CAAC,0BACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA6BO,SAASC,2BACd,YACoC;AACpC,SAAO,CACL,uBACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAmCO,SAASC,kCACd,YAC2C;AAC3C,SAAO,CACL,uBACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAkDO,SAASC,4BACd,YACqC;AACrC,SAAO,CACL,uBACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAkDO,SAASC,4BACd,YACqC;AACrC,SAAO,CACL,uBACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAkDO,SAASC,0CACd,YACmD;AACnD,SAAO,CAAC,2BACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAuCO,SAASC,wBACd,YACiC;AACjC,SAAO,CAAC,0BACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAiCO,SAASC,kCACd,YAC2C;AAC3C,SAAO,CACL,uBACA,YAQAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAoDO,SAASC,uCACd,YACgD;AAChD,SAAO,CACL,uBACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAgDO,IAAM,wBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;AACpB,IAAM,yBAAqB;AAAA,EAChC;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA4B;AACrB,IAAM,uBAAmB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,mBAAmB;AAAA,UAC3B,EAAE,MAAM,iBAAiB;AAAA,UACzB,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,sDAAsD;AAAA,QAChE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA0B;AACnB,IAAM,wBAAoB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA2B;AACpB,IAAM,2CAAuC;AAAA,EAClD;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA8C;AACvC,IAAM,2BAAuB;AAAA,EAClC;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA8B;AACvB,IAAM,uBAAmB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,CAAC,cACC;AAAA,QACE,wCAAe,OAAO;AAAA,MACpB;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,mBAAmB;AAAA,UAC3B,EAAE,MAAM,iBAAiB;AAAA,UACzB,EAAE,MAAM,qBAAqB;AAAA,UAC7B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,qBAAqB;AAAA,QAC/B;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,sDAAsD;AAAA,QAChE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACJ,EAA0B;;;AGh9D1B,IAAAC,uBAAiC;AACjC,sCAAkC;AAc3B,IAAMC,2BAGK,2DAAiBA,wBAA6B;AACzD,IAAMC,+BAGK,2DAAiBA,4BAAiC;AAC7D,IAAMC,iBAEK,2DAAiBA,cAAmB;AAC/C,IAAMC,qBAGK,2DAAiBA,kBAAuB;AACnD,IAAMC,qBAGK,2DAAiBA,kBAAuB;AACnD,IAAMC,kBAEK,2DAAiBA,eAAoB;AAChD,IAAMC,0BAGK,2DAAiBA,uBAA4B;AACxD,IAAMC,wBAGK,2DAAiBA,qBAA0B;AACtD,IAAMC,kBAEK,2DAAiBA,eAAoB;AAChD,IAAMC,iBAEK,2DAAiBA,cAAmB;AAC/C,IAAMC,8BAGK,2DAAiBA,2BAAgC;AAC5D,IAAMC,wBAGK,2DAAiBA,qBAA0B;AACtD,IAAMC,6BAGK,2DAAiBA,0BAA+B;AAC3D,IAAMC,iCAGK,2DAAiBA,8BAAmC;AAC/D,IAAMC,sCAGK,2DAAiBA,mCAAwC;AACpE,IAAMC,6BAGK,2DAAiBA,0BAA+B;AAC3D,IAAMC,oCAGK,2DAAiBA,iCAAsC;AAClE,IAAMC,8BAGK,2DAAiBA,2BAAgC;AAC5D,IAAMC,8BAGK,2DAAiBA,2BAAgC;AAC5D,IAAMC,4CAGK;AAAA,EAChBA;AACF;AACO,IAAMC,0BAGK,2DAAiBA,uBAA4B;AACxD,IAAMC,oCAGK,2DAAiBA,iCAAsC;AAClE,IAAMC,yCAGK,2DAAiBA,sCAA2C;AAIvE,IAAMC,yBAET,mDAAkB,iBAAuB;AAItC,IAAMC,0BAET,mDAAkB,kBAAwB;AAIvC,IAAMC,wBAET,mDAAkB,gBAAsB;AAIrC,IAAMC,yBAET,mDAAkB,iBAAuB;AAItC,IAAMC,4CAET,mDAAkB,oCAA0C;AAIzD,IAAMC,4BAET,mDAAkB,oBAA0B;AAIzC,IAAMC,wBAET,mDAAkB,gBAAsB;", "names": ["addBookingsToMultiServiceBooking", "bulkConfirmOrDeclineBooking", "bulkCreateBooking", "bulkGetMultiServiceBookingAllowedActions", "cancelBooking", "cancelMultiServiceBooking", "confirmBooking", "confirmMultiServiceBooking", "confirmOrDeclineBooking", "createBooking", "createMultiServiceBooking", "declineBooking", "declineMultiServiceBooking", "getMultiServiceBooking", "getMultiServiceBookingAvailability", "markBookingAsPending", "markMultiServiceBookingAsPending", "onBookingCanceled", "onBookingConfirmed", "onBookingCreated", "onBookingDeclined", "onBookingNumberOfParticipantsUpdated", "onBookingRescheduled", "onBookingUpdated", "removeBookingsFromMultiServiceBooking", "rescheduleBooking", "rescheduleMultiServiceBooking", "setBookingSubmissionId", "updateExtendedFields", "updateNumberOfParticipants", "import_rename_all_nested_keys", "import_float", "import_timestamp", "import_transform_paths", "import_float", "import_timestamp", "import_rest_modules", "payload", "MultiServiceBookingType", "LocationType", "ValueType", "BookingStatus", "PaymentStatus", "SelectedPaymentOption", "Platform", "Actor", "IdentificationDataIdentityType", "WebhookIdentityType", "LocationLocationType", "IdentityType", "SortOrder", "confirmOrDeclineBooking", "sdkTransformError", "bulkConfirmOrDeclineBooking", "createBooking", "bulkCreateBooking", "rescheduleBooking", "setBookingSubmissionId", "updateExtendedFields", "markBookingAsPending", "createMultiServiceBooking", "rescheduleMultiServiceBooking", "getMultiServiceBookingAvailability", "cancelMultiServiceBooking", "markMultiServiceBookingAsPending", "confirmMultiServiceBooking", "declineMultiServiceBooking", "bulkGetMultiServiceBookingAllowedActions", "getMultiServiceBooking", "addBookingsToMultiServiceBooking", "removeBookingsFromMultiServiceBooking", "confirmOrDeclineBooking", "bulkConfirmOrDeclineBooking", "createBooking", "bulkCreateBooking", "rescheduleBooking", "confirmBooking", "setBookingSubmissionId", "updateExtendedFields", "declineBooking", "cancelBooking", "updateNumberOfParticipants", "markBookingAsPending", "createMultiServiceBooking", "rescheduleMultiServiceBooking", "getMultiServiceBookingAvailability", "cancelMultiServiceBooking", "markMultiServiceBookingAsPending", "confirmMultiServiceBooking", "declineMultiServiceBooking", "bulkGetMultiServiceBookingAllowedActions", "getMultiServiceBooking", "addBookingsToMultiServiceBooking", "removeBookingsFromMultiServiceBooking", "import_rest_modules", "confirmOrDeclineBooking", "bulkConfirmOrDeclineBooking", "createBooking", "bulkCreateBooking", "rescheduleBooking", "confirmBooking", "setBookingSubmissionId", "updateExtendedFields", "declineBooking", "cancelBooking", "updateNumberOfParticipants", "markBookingAsPending", "createMultiServiceBooking", "rescheduleMultiServiceBooking", "getMultiServiceBookingAvailability", "cancelMultiServiceBooking", "markMultiServiceBookingAsPending", "confirmMultiServiceBooking", "declineMultiServiceBooking", "bulkGetMultiServiceBookingAllowedActions", "getMultiServiceBooking", "addBookingsToMultiServiceBooking", "removeBookingsFromMultiServiceBooking", "onBookingCanceled", "onBookingConfirmed", "onBookingCreated", "onBookingDeclined", "onBookingNumberOfParticipantsUpdated", "onBookingRescheduled", "onBookingUpdated"]}