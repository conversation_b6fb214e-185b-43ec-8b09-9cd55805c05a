import { ListEventTimeSlotsRequest as ListEventTimeSlotsRequest$1, ListEventTimeSlotsResponse as ListEventTimeSlotsResponse$1, GetEventTimeSlotRequest as GetEventTimeSlotRequest$1, GetEventTimeSlotResponse as GetEventTimeSlotResponse$1 } from './index.typings.mjs';
import '@wix/sdk-types';

/**
 * A time slot represents a specific time period when a service is available for booking.
 * It provides all the information needed to display availability to customers, including whether customers can actually book it, the remaining capacity, and which staff members or resources are available.
 * Available time slots may not always be bookable due to service booking policies. When this occurs, the time slot includes information about the specific booking restrictions.
 *
 * For multi-service bookings, the top-level time slot acts as a container spanning the entire service sequence (from the start of the first service to the end of the last service). Individual service details are provided in nested time slots.
 */
interface TimeSlot {
    /**
     * Service ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).
     *
     * Available only for single-service bookings. For multi-service bookings, this field is empty and individual service IDs are provided in `nestedTimeSlots`.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     *
     * For multi-service bookings, this represents the start time of the first service in the sequence.
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string | null;
    /**
     * Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T14:00:00`.
     *
     * For multi-service bookings, this represents the end time of the last service in the sequence.
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string | null;
    /**
     * Whether customers can book the slot according to the service's booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).
     *
     * For multi-service bookings, this is `true` only when all services in the sequence comply with their respective booking policies.
     */
    bookable?: boolean | null;
    /** Information about where the business provides the service to the customer. */
    location?: Location;
    /**
     * Information about the event ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)) related to the slot.
     * Available only for classes. Not available for appointment-based services and courses.
     */
    eventInfo?: EventInfo;
    /**
     * Total number of spots for the slot.
     *
     * For multi-service bookings, this is always `1` because customers book the entire service sequence as a single unit.
     * @min 1
     * @max 1000
     */
    totalCapacity?: number | null;
    /**
     * Remaining number of spots for the slot.
     * - For appointment bookings: Either `1` (available) or `0` (unavailable).
     * - For classes: Total capacity minus booked spots. Doesn't account for waitlist reservations. For classes with waitlists, use `bookableCapacity` to get the actual number of spots customers can book.
     * - For courses: Total capacity minus booked spots. Courses don't currently support waitlists.
     * @max 1000
     */
    remainingCapacity?: number | null;
    /**
     * Number of spots that customers can book for the slot.
     * Calculated as the remaining capacity minus the spots reserved for the waitlist.
     * If the service has no waitlist, identical to `remainingCapacity`.
     *
     * For multi-service bookings, this is either `1` (sequence can be booked) or `0` (sequence cannot be booked).
     */
    bookableCapacity?: number | null;
    /**
     * Information about booking policy violations for the slot.
     *
     * For multi-service bookings, this aggregates violations from all services in the sequence.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
    /**
     * List of resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) available during the time slot.
     *
     * Available only for single-service bookings. For multi-service bookings, resource information is provided in `nestedTimeSlots`.
     */
    availableResources?: AvailableResources[];
    /**
     * Nested time slots for multi-service bookings.
     * Each nested slot represents 1 service in the sequence, ordered according to the service sequence specified in the request.
     *
     * Available only for multi-service bookings. Empty for single-service bookings.
     * @maxSize 8
     */
    nestedTimeSlots?: NestedTimeSlot[];
    /** Information about why customers can't book the time slot. */
    nonBookableReasons?: NonBookableReasons;
    /**
     * Schedule ID associated with this time slot.
     * Same as the service's schedule ID.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface Location {
    /**
     * Location ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * Available only for business locations.
     * @format GUID
     */
    id?: string | null;
    /** Location name. */
    name?: string | null;
    /** Formatted location address. */
    formattedAddress?: string | null;
    /** Location type. */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNKNOWN_LOCATION_TYPE = "UNKNOWN_LOCATION_TYPE",
    /** A business location, either the default business address, or locations defined for the business by the Business Info. */
    BUSINESS = "BUSINESS",
    /** The location is unique to this service and isn't defined as 1 of the business locations. */
    CUSTOM = "CUSTOM",
    /** The location can be determined by the customer and isn't set up beforehand. */
    CUSTOMER = "CUSTOMER"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNKNOWN_LOCATION_TYPE' | 'BUSINESS' | 'CUSTOM' | 'CUSTOMER';
/** Available for event based slots, and not for availability based slots */
interface EventInfo {
    /**
     * Event ID.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
    /** Information about the event's waitlist. Available only if the service has a waitlist. */
    waitingList?: WaitingList;
}
interface WaitingList {
    /**
     * Total number of spots in the waitlist.
     * @min 1
     */
    totalCapacity?: number | null;
    /**
     * Number of remaining spots in the waitlist.
     * For example, an event with a waitlist for 10 people and 3 registrants, results in a remaining capacity of `7`.
     */
    remainingCapacity?: number | null;
}
interface BookingPolicyViolations {
    /** Whether it's too early for customers to book the slot. */
    tooEarlyToBook?: boolean | null;
    /** Earliest time for booking the slot in `YYYY-MM-DDThh:mm:ss.sssZ` format. */
    earliestBookingDate?: Date | null;
    /** Whether it's too late for customers to book the slot. */
    tooLateToBook?: boolean | null;
    /** Whether customers can book the service online. */
    bookOnlineDisabled?: boolean | null;
}
interface AvailableResources {
    /**
     * Resource type ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     * @format GUID
     */
    resourceTypeId?: string | null;
    /**
     * Details about resources available during the time slot.
     *
     * Behavior varies by method:
     *
     * List methods (List Availability Time Slots and List Multi Service Availability Time Slots):
     * - Empty by default.
     * - Up to 10 resources when specifying `includeResourceTypeIds` or `resourceIds` in the request.
     *
     * Get methods (Get Availability Time Slots and Get Multi Service Availability Time Slots):
     * - All resources by default.
     * - Filtered resources when specifying `includeResourceTypeIds` or `resourceIds` in the request.
     */
    resources?: Resource[];
    /**
     * Whether there are more available resources for the slot than those listed in `resources`.
     * @readonly
     */
    hasMoreAvailableResources?: boolean | null;
}
interface Resource {
    /**
     * Resource ID.
     * @format GUID
     */
    id?: string;
    /**
     * Resource name.
     * @minLength 1
     */
    name?: string | null;
}
interface NestedTimeSlot {
    /**
     * Service ID of the nested time slot.
     * @format GUID
     */
    serviceId?: string;
    /**
     * Local start date of the nested time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localStartDate?: string;
    /**
     * Local end date of the nested time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    localEndDate?: string;
    /**
     * List of resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) available during the nested time slot.
     * @maxSize 10
     */
    availableResources?: AvailableResources[];
    /**
     * The schedule ID associated with this nested time slot.
     * Same as the service's schedule ID.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface NonBookableReasons {
    /** Whether the slot is fully booked with no remaining capacity. */
    noRemainingCapacity?: boolean | null;
    /** Whether booking the slot violates any of the service's booking policies. */
    violatesBookingPolicy?: boolean | null;
    /** Whether the slot is reserved for the waitlist. A new customer can't book the reserved slot. */
    reservedForWaitingList?: boolean | null;
}
interface CursorPaging {
    /**
     * Number of results to load.
     *
     * Default: `1000`.
     * Max: `1000`.
     * @max 1000
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     */
    cursor?: string | null;
}
interface CursorPagingMetadata {
    /** Offset that was requested. */
    cursors?: Cursors;
    /**
     * Indicates if there are more results after the current page.
     * If `true`, another page of results can be retrieved.
     * If `false`, this is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /** Cursor pointing to next page in the list of results. */
    next?: string | null;
    /** Cursor pointing to previous page in the list of results. */
    prev?: string | null;
}
interface ListEventTimeSlotsRequest {
    /**
     * Lower boundary for `localStartDate` to include in the response.
     * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate.
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Local start date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    fromLocalDate?: string | null;
    /**
     * Upper boundary for `localStartDate` to include in the response.
     * Each returned time slot has a `localStartDate` between `fromLocalDate` and `toLocalDate`.
     * Required unless you specify `cursorPaging.cursor`.
     *
     * Local end date in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @format LOCAL_DATE_TIME
     */
    toLocalDate?: string | null;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @maxLength 100
     */
    timeZone?: string | null;
    /**
     * Optional service IDs to filter the response.
     * If not provided, time slots for all services are returned.
     * @format GUID
     * @maxSize 100
     */
    serviceIds?: string[] | null;
    /**
     * Whether to include time slots that aren't bookable according to the service's booking policy or that are fully booked.
     * - `true`: Both bookable and non-bookable time slots are returned.
     * - `false`: Only bookable time slots are returned.
     *
     * Default: `true`
     */
    includeNonBookable?: boolean | null;
    /**
     * Minimum bookable capacity.
     * Use to filter out sessions that can't accommodate the desired party size.
     * @min 1
     */
    minBookableCapacity?: number | null;
    /**
     * Optional filter to apply to the events, for example resource or location.
     * See the [Events API Supported Filters and Sorting article](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/supported-filters-and-sorting) for a complete list of filters.
     */
    eventFilter?: Record<string, any> | null;
    /**
     * Maximum number of slots to return for each day in the specified time range.
     * If both bookable and non-bookable slots exist on the same day, bookable slots are returned first.
     *
     * When you specify `maxSlotsPerDay`, you must also specify `toLocalDate`, and it must be no more than 1 month after `fromLocalDate`.
     * @min 1
     * @max 1000
     */
    maxSlotsPerDay?: number | null;
    /**
     * Cursor-based paging configuration.
     * Enables fetching results in smaller chunks by setting a limit on the number of returned items.
     */
    cursorPaging?: CursorPaging;
    /**
     * Indicators for service's booking policy violations.
     * Allows filtering for time slots with specific violation types:
     * - `tooEarlyToBook`: Returns slots that violate minimum advance booking time.
     * - `tooLateToBook`: Returns slots that violate maximum advance booking time.
     * - `bookOnlineDisabled`: Returns slots where online booking is disabled.
     *
     * When specified, only time slots matching the violation criteria are returned.
     */
    bookingPolicyViolations?: BookingPolicyViolations;
}
interface ListEventTimeSlotsResponse {
    /** Retrieved time slots matching the specified filters. */
    timeSlots?: TimeSlot[];
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
    /**
     * Paging metadata for the next page of results.
     * Contains a cursor if more data is available.
     */
    pagingMetadata?: CursorPagingMetadata;
}
interface GetEventTimeSlotRequest {
    /**
     * Event ID.
     * @minLength 36
     * @maxLength 250
     */
    eventId: string | null;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @maxLength 100
     */
    timeZone?: string | null;
}
interface GetEventTimeSlotResponse {
    /** The time slot. */
    timeSlot?: TimeSlot;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @minLength 1
     * @maxLength 150
     */
    timeZone?: string | null;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function listEventTimeSlots(): __PublicMethodMetaInfo<'POST', {}, ListEventTimeSlotsRequest$1, ListEventTimeSlotsRequest, ListEventTimeSlotsResponse$1, ListEventTimeSlotsResponse>;
declare function getEventTimeSlot(): __PublicMethodMetaInfo<'GET', {
    eventId: string;
}, GetEventTimeSlotRequest$1, GetEventTimeSlotRequest, GetEventTimeSlotResponse$1, GetEventTimeSlotResponse>;

export { type __PublicMethodMetaInfo, getEventTimeSlot, listEventTimeSlots };
