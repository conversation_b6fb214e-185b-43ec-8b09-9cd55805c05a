"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  cloneServiceOptionsAndVariants: () => cloneServiceOptionsAndVariants2,
  createServiceOptionsAndVariants: () => createServiceOptionsAndVariants2,
  deleteServiceOptionsAndVariants: () => deleteServiceOptionsAndVariants2,
  getServiceOptionsAndVariants: () => getServiceOptionsAndVariants2,
  getServiceOptionsAndVariantsByServiceId: () => getServiceOptionsAndVariantsByServiceId2,
  queryServiceOptionsAndVariants: () => queryServiceOptionsAndVariants2,
  updateServiceOptionsAndVariants: () => updateServiceOptionsAndVariants2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_service-options-and-variants";
function createServiceOptionsAndVariants(payload) {
  function __createServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CreateServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __createServiceOptionsAndVariants;
}
function cloneServiceOptionsAndVariants(payload) {
  function __cloneServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CloneServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{cloneFromId}/clone",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __cloneServiceOptionsAndVariants;
}
function getServiceOptionsAndVariants(payload) {
  function __getServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "GET",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      fallback: [
        {
          method: "GET",
          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
            protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
            data: payload,
            host
          }),
          params: (0, import_rest_modules.toURLSearchParams)(payload)
        }
      ]
    };
    return metadata;
  }
  return __getServiceOptionsAndVariants;
}
function getServiceOptionsAndVariantsByServiceId(payload) {
  function __getServiceOptionsAndVariantsByServiceId({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "GET",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariantsByServiceId",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      fallback: [
        {
          method: "GET",
          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
            protoPath: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
            data: payload,
            host
          }),
          params: (0, import_rest_modules.toURLSearchParams)(payload)
        }
      ]
    };
    return metadata;
  }
  return __getServiceOptionsAndVariantsByServiceId;
}
function updateServiceOptionsAndVariants(payload) {
  function __updateServiceOptionsAndVariants({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "PATCH",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.UpdateServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}",
        data: serializedData,
        host
      }),
      data: serializedData
    };
    return metadata;
  }
  return __updateServiceOptionsAndVariants;
}
function deleteServiceOptionsAndVariants(payload) {
  function __deleteServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "DELETE",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.DeleteServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteServiceOptionsAndVariants;
}
function queryServiceOptionsAndVariants(payload) {
  function __queryServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.QueryServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryServiceOptionsAndVariants;
}

// src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.meta.ts
function createServiceOptionsAndVariants2() {
  const payload = {};
  const getRequestOptions = createServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/serviceOptionsAndVariants",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function cloneServiceOptionsAndVariants2() {
  const payload = { cloneFromId: ":cloneFromId" };
  const getRequestOptions = cloneServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/serviceOptionsAndVariants/{cloneFromId}/clone",
    pathParams: { cloneFromId: "cloneFromId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getServiceOptionsAndVariants2() {
  const payload = {
    serviceOptionsAndVariantsId: ":serviceOptionsAndVariantsId"
  };
  const getRequestOptions = getServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
    pathParams: { serviceOptionsAndVariantsId: "serviceOptionsAndVariantsId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getServiceOptionsAndVariantsByServiceId2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = getServiceOptionsAndVariantsByServiceId(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateServiceOptionsAndVariants2() {
  const payload = {
    serviceOptionsAndVariants: { id: ":serviceOptionsAndVariantsId" }
  };
  const getRequestOptions = updateServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}",
    pathParams: { serviceOptionsAndVariantsId: "serviceOptionsAndVariantsId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteServiceOptionsAndVariants2() {
  const payload = {
    serviceOptionsAndVariantsId: ":serviceOptionsAndVariantsId"
  };
  const getRequestOptions = deleteServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
    pathParams: { serviceOptionsAndVariantsId: "serviceOptionsAndVariantsId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryServiceOptionsAndVariants2() {
  const payload = {};
  const getRequestOptions = queryServiceOptionsAndVariants(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/serviceOptionsAndVariants/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  cloneServiceOptionsAndVariants,
  createServiceOptionsAndVariants,
  deleteServiceOptionsAndVariants,
  getServiceOptionsAndVariants,
  getServiceOptionsAndVariantsByServiceId,
  queryServiceOptionsAndVariants,
  updateServiceOptionsAndVariants
});
//# sourceMappingURL=meta.js.map