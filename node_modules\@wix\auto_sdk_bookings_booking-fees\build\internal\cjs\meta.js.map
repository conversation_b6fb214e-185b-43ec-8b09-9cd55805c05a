{"version": 3, "sources": ["../../../meta.ts", "../../../src/bookings-fees-v1-booking-fee-booking-fees.http.ts", "../../../src/bookings-fees-v1-booking-fee-booking-fees.meta.ts"], "sourcesContent": ["export * from './src/bookings-fees-v1-booking-fee-booking-fees.meta.js';\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsFeesV1BookingFeesUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/booking-fees',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_booking-fees';\n\n/**\n * Retrieves booking fees by booking IDs.\n *\n *\n * Instead of returning `bookingFee` objects with the `cancellationFee.price.value`\n * set to `0.00`, the method doesn't return a `bookingFee` object for the relevant\n * booking IDs. For example, no `bookingFee` object is returned if the canceled\n * booking was free or if the booking was canceled before the start of the earliest\n * cancellation window with an associated fee.\n *\n * If the service's booking policy has been updated since the booking was created,\n * booking fees are calculated according to the *booking policy snapshot*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))\n * rather than the current version of the *policy*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).\n *\n * This method calculates the cancellation fee amount based on the time of the\n * call, you can't specify a time. Similarly, it calculates the cancellation fee\n * based on the number of participants who canceled, not a provided number.\n *\n * A `cancellationFee.status` of `PREVIEW` indicates that the booking fee is\n * informational only; the customer isn't required to pay it. When the `status` is\n * set to `UNKNOWN_STATUS` there is no eCommerce order associated with the booking. For\n * example, if a custom checkout was used for the booking instead of the\n * _eCommerce checkout_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).\n *\n * If multiple events would trigger the calculation of a booking fee, for example\n * when a booking is first canceled and then marked as not attended, Wix calculates\n * the booking fee based on the first trigger. In this example, the booking\n * cancellation.\n */\nexport function listBookingFeesByBookingIds(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listBookingFeesByBookingIds({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.fees.v1.booking_fee',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.fees.v1.BookingFees.ListBookingFeesByBookingIds',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsFeesV1BookingFeesUrl({\n        protoPath: '/v1/booking-fees',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __listBookingFeesByBookingIds;\n}\n\n/**\n * Applies booking fees to an _eCommerce order_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).\n *\n *\n * The booking fees are added as a single additional fee to the eCommerce order.\n * The order's `additionalFee.lineItemIds` array is set to the list of corresponding\n * booking IDs. By default, the `additionalFee.price.amount` is the sum of all\n * booking fee prices. But you may provide a `priceOverride` instead. The override\n * price can't be higher than the sum of all booking fees.\n *\n * Apply Booking Fees to Order also updates the prices of all affected line items\n * in the relevant eCommerce order to zero. After a cancellation fee is applied\n * to an eCommerce order, the cancellation fee's `price.value` is updated to `0.00`\n * and its trigger is set to `UNKNOWN_TRIGGER`. You can retrieve the fee amount\n * from the corresponding `additionalFee` object of the eCommerce order with\n * _Search Orders_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/search-orders) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/search-orders)).\n *\n * If you apply multiple booking fees to an eCommerce order, they either all fail or\n * all succeed together. For example, the call fails if the booking fees are associated\n * with different eCommmerce orders.\n */\nexport function applyBookingFeesToOrder(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __applyBookingFeesToOrder({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.fees.v1.booking_fee',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.fees.v1.BookingFees.ApplyBookingFeesToOrder',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsFeesV1BookingFeesUrl({\n        protoPath: '/v1/booking-fees/apply',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __applyBookingFeesToOrder;\n}\n\n/**\n * Collects booking fees by charging the customer using the payment method that's\n * saved on the corresponding *eCommerce order*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))\n *\n *\n * <blockquote class=\"warning\">\n *\n * __Warning:__\n * Currently, there is no validation that prevents idempotent requests.\n * This means that your code must make sure to not charge customers multiple\n * times for the same booking fee. You could use\n * _List Transactions For Single Order_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/order-transactions/list-transactions-for-single-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/order-transactions/list-transactions-for-single-order))\n * to check which charges were made previously for an eCommerce order.\n *\n * </blockquote>\n *\n * An HTTP status of `200` means that all booking fees were successfully collected.\n * Any other HTPP status means that collection failed.\n *\n * Collects the order's `additionalFees.price.amount` that's related to the\n * booking fees. If there are multiple additional fees on the eCommerce order,\n * the amount that's collected differs from `priceSummary.totalAdditionalFees.amount`.\n *\n * Possible failure reasons include:\n * + The order's `status` isn't `APPROVED`.\n * + There is no payment method saved on the order.\n * + The order's `balanceSummary.balance.amount` is lower than the\n * `additionalFees.price.amount` to collect.\n * + The order's `additionalFeeId` doesn't belong to a Wix Bookings cancellation fee.\n */\nexport function collectAppliedBookingFees(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __collectAppliedBookingFees({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.fees.v1.booking_fee',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.fees.v1.BookingFees.CollectAppliedBookingFees',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsFeesV1BookingFeesUrl({\n        protoPath: '/v1/booking-fees/collect',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __collectAppliedBookingFees;\n}\n", "import * as ambassadorWixBookingsFeesV1BookingFee from './bookings-fees-v1-booking-fee-booking-fees.http.js';\nimport * as ambassadorWixBookingsFeesV1BookingFeeTypes from './bookings-fees-v1-booking-fee-booking-fees.types.js';\nimport * as ambassadorWixBookingsFeesV1BookingFeeUniversalTypes from './bookings-fees-v1-booking-fee-booking-fees.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function listBookingFeesByBookingIds(): __PublicMethodMetaInfo<\n  'GET',\n  {},\n  ambassadorWixBookingsFeesV1BookingFeeUniversalTypes.ListBookingFeesByBookingIdsRequest,\n  ambassadorWixBookingsFeesV1BookingFeeTypes.ListBookingFeesByBookingIdsRequest,\n  ambassadorWixBookingsFeesV1BookingFeeUniversalTypes.ListBookingFeesByBookingIdsResponse,\n  ambassadorWixBookingsFeesV1BookingFeeTypes.ListBookingFeesByBookingIdsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsFeesV1BookingFee.listBookingFeesByBookingIds(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v1/booking-fees',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function applyBookingFeesToOrder(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsFeesV1BookingFeeUniversalTypes.ApplyBookingFeesToOrderRequest,\n  ambassadorWixBookingsFeesV1BookingFeeTypes.ApplyBookingFeesToOrderRequest,\n  ambassadorWixBookingsFeesV1BookingFeeUniversalTypes.ApplyBookingFeesToOrderResponse,\n  ambassadorWixBookingsFeesV1BookingFeeTypes.ApplyBookingFeesToOrderResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsFeesV1BookingFee.applyBookingFeesToOrder(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/booking-fees/apply',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function collectAppliedBookingFees(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsFeesV1BookingFeeUniversalTypes.CollectAppliedBookingFeesRequest,\n  ambassadorWixBookingsFeesV1BookingFeeTypes.CollectAppliedBookingFeesRequest,\n  ambassadorWixBookingsFeesV1BookingFeeUniversalTypes.CollectAppliedBookingFeesResponse,\n  ambassadorWixBookingsFeesV1BookingFeeTypes.CollectAppliedBookingFeesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsFeesV1BookingFee.collectAppliedBookingFees(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/booking-fees/collect',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,iCAAAA;AAAA,EAAA,iCAAAC;AAAA,EAAA,mCAAAC;AAAA;AAAA;;;ACAA,0BAAkC;AAClC,IAAAC,uBAA2B;AAI3B,SAAS,uCACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAkCd,SAAS,4BACd,SAC4B;AAC5B,WAAS,8BAA8B,EAAE,KAAK,GAAQ;AACpD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,uCAAuC;AAAA,QAC1C,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAyBO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,uCAAuC;AAAA,QAC1C,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAkCO,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,uCAAuC;AAAA,QAC1C,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC5KO,SAASC,+BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACkC,4BAA4B,OAAO;AAE3E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,2BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACkC,wBAAwB,OAAO;AAEvE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,6BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACkC,0BAA0B,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["applyBookingFeesToOrder", "collectAppliedBookingFees", "listBookingFeesByBookingIds", "import_rest_modules", "listBookingFeesByBookingIds", "applyBookingFeesToOrder", "collectAppliedBookingFees"]}