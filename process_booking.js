import { createClient, OAuthStrategy, ApiKeyStrategy } from '@wix/sdk';
import { bookings, extendedBookings } from '@wix/bookings';
// =====================
// Configurable Variables
// =====================

/**
 * Wix CLI Token Authentication (no OAuth required)
 * Reads token from ~/.wix/config.json (created by running `wix login`)
 */
import fs from 'fs';
import os from 'os';
import path from 'path';
const WIX_API_ROOT = 'https://www.wixapis.com';

function getWixCliToken() {
    try {
        // Try different possible locations for Wix CLI auth data
        const wixDir = path.join(os.homedir(), '.wix');
        const authDir = path.join(wixDir, 'auth');

        const possiblePaths = [
            path.join(wixDir, 'config.json'),
            path.join(wixDir, 'auth.json'),
            path.join(authDir, 'config.json'),
            path.join(authDir, 'auth.json')
        ];

        // Also check for any JSON files in the auth directory
        if (fs.existsSync(authDir)) {
            const authFiles = fs.readdirSync(authDir)
                .filter(file => file.endsWith('.json'))
                .map(file => path.join(authDir, file));
            possiblePaths.push(...authFiles);
        }

        let config = null;
        let usedPath = null;

        for (const configPath of possiblePaths) {
            try {
                if (fs.existsSync(configPath)) {
                    config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                    usedPath = configPath;
                    break;
                }
            } catch (e) {
                // Continue to next path
                continue;
            }
        }

        if (!config) {
            // List contents of .wix directory and auth subdirectory for debugging
            let debugInfo = '';
            if (fs.existsSync(wixDir)) {
                const contents = fs.readdirSync(wixDir, { withFileTypes: true });
                const fileList = contents.map(item =>
                    item.isDirectory() ? `${item.name}/` : item.name
                ).join(', ');
                debugInfo = `Contents of ~/.wix/: ${fileList}`;

                if (fs.existsSync(authDir)) {
                    const authContents = fs.readdirSync(authDir);
                    debugInfo += `. Contents of ~/.wix/auth/: ${authContents.join(', ')}`;
                }
            } else {
                debugInfo = '~/.wix/ directory not found. Please run "wix login".';
            }
            throw new Error(`No Wix CLI config found. ${debugInfo}`);
        }

        // Try different possible token structures
        if (config.accessToken) {
            return config.accessToken;
        }
        if (config.access_token) {
            return config.access_token;
        }
        if (config.tokens && Array.isArray(config.tokens) && config.tokens.length > 0) {
            return config.tokens[0].accessToken || config.tokens[0].access_token;
        }
        if (config.auth && config.auth.access_token) {
            return config.auth.access_token;
        }

        throw new Error(`No access token found in ${usedPath}. Config structure: ${JSON.stringify(Object.keys(config))}`);
    } catch (err) {
        throw new Error('Failed to read Wix CLI token: ' + err.message);
    }
}

// Booking Filter Criteria
const BOOKING_STATUS_FILTER = ['CONFIRMED']; // Only confirmed bookings
const SERVICE_TYPES = []; // Empty = all services, or specify service IDs

// =====================
// End of Configurable Variables
// =====================

import fetch from 'node-fetch';
import readline from 'readline';

// Import PIN creation functions from create_pin_job.js
import {
    randomPin,
    validatePin,
    parseTime,
    toGmt8Iso,
    addMinutesGmt8,
    buildAccessName,
    getAccessToken,
    createCustomPinJob
} from './create_pin_job.js';

// ========== Utility Functions ==========

function prompt(question) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    return new Promise(resolve => rl.question(question, ans => {
        rl.close();
        resolve(ans);
    }));
}

function formatBookingDate(isoString) {
    const d = new Date(isoString);
    return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
}

function formatBookingTime(isoString) {
    const d = new Date(isoString);
    return `${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`;
}

function calculateDuration(startIso, endIso) {
    const start = new Date(startIso);
    const end = new Date(endIso);
    const durationMinutes = Math.round((end - start) / 60000); // minutes

    // Always round to nearest hour for sessions (50-55 minutes becomes 1 hour)
    return Math.round(durationMinutes / 60); // hours
}

function logOperation(operation, data, success = true) {
    const timestamp = new Date().toISOString();
    const logLevel = success ? 'INFO' : 'ERROR';
    // Sanitize sensitive data
    const safeData = { ...data };
    if (safeData.pin) safeData.pin = '****';
    if (safeData.email) safeData.email = safeData.email.replace(/(.{3}).*@/, '$1***@');
    console.log(`[${timestamp}] ${logLevel}: ${operation}`, safeData);
}

// ========== Authentication Functions ==========

async function getWixAccessToken() {
    // Read token from Wix CLI config
    const token = getWixCliToken();
    if (!token) {
        throw new Error('Wix CLI token not found. Please run `wix login`.');
    }
    return token;
}

// ========== Wix SDK Client Initialization ==========
let wixClient = null;

async function getWixClient() {
    if (wixClient) return wixClient;

    const siteToken = 'IST.eyJraWQiOiJQb3pIX2FDMiIsImFsZyI6IlJTMjU2In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.Nw9U1ckk6g4OhyfBI8NhBrrMFgii_emyRzSD_6yX0WN9E03OqnVmMR50BRvYoK8c93RCOuWanQ_djLZuUjeGhr2dC4GF4Rms-f3fBXgg1IYEMYwmH_IIhC_km_Ckga8I72G8eNI9TUvKuX9DK0kzgrcc3nBsk3ZoBo3uW6I4AE1LGziPblBnpo0w_UwuvO4REI_N8oPvI6x5sXrUGaRpsZa9ZDttClzwmWuVggfotHQKQtinJJ_9hjxRvD2kQ_l5a69daeQZaFJpCaRc3Vm9x777cypiRTjD3HzTRqYaz0rCcBSnimdD2ActhvPc-x7qZnH4ilhni0ffP364YkUn7w';

    console.log('Creating Wix client with site token');

    try {
        wixClient = createClient({
            modules: { extendedBookings },  // Include both bookings and extendedBookings
            auth: ApiKeyStrategy({
                apiKey: siteToken,
                siteId: '11c5a478-34d0-483c-a16c-8b8813664a15'
            })
        });

        console.log('Created wixClient:', !!wixClient);
        console.log('wixClient keys:', Object.keys(wixClient));
        console.log('wixClient.bookings available:', !!wixClient.bookings);

        return wixClient;
    } catch (error) {
        console.error('Error creating wixClient:', error.message);
        console.error('Full error:', error);
        throw error;
    }
}

async function copyBookingNotificationToClipboard(bookingData) {
    try {
        // Validate required booking data
        if (!bookingData.customerEmail || !bookingData.customerName || !bookingData.pin) {
            throw new Error('Customer email, name, and PIN are required');
        }

        // Email subject - include date and time to distinguish multiple bookings
        const subject = `Your ViFit Studio PIN - ${bookingData.bookingDate} ${bookingData.startTime} to ${bookingData.endTime}`;

        // Email message - simple and clean matching the provided format
        const emailMessage = `Dear ${bookingData.customerName},

I hope this message finds you well. Your PIN for your booking at ${bookingData.location || 'ViFit Studio @ International Plaza'} from ${bookingData.bookingDate} ${bookingData.startTime} to ${bookingData.endTime} is ${bookingData.pin}.

Best regards,
ViFit Team`;

        // Full email content with subject and recipient
        const fullEmailContent = `To: ${bookingData.customerEmail}
Subject: ${subject}

${emailMessage}`;

        // Copy to clipboard using Node.js child_process to execute clip command (Windows)
        const { spawn } = await import('child_process');

        // Use Windows clip command to copy to clipboard - more reliable method
        const clipProcess = spawn('clip', [], { stdio: ['pipe', 'pipe', 'pipe'] });

        clipProcess.stdin.write(emailMessage);
        clipProcess.stdin.end();

        // Wait for the process to complete
        await new Promise((resolve, reject) => {
            clipProcess.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`clip command failed with code ${code}`));
                }
            });
            clipProcess.on('error', reject);
        });

        console.log('\n📧 EMAIL MESSAGE COPIED TO CLIPBOARD:');
        console.log('═'.repeat(60));
        console.log(`To: ${bookingData.customerEmail}`);
        console.log(`Subject: ${subject}`);
        console.log('─'.repeat(60));
        console.log(emailMessage);
        console.log('═'.repeat(60));
        console.log('✅ Message copied to clipboard - you can now paste it into your email client');

        logOperation('Booking notification copied to clipboard', {
            recipient: bookingData.customerEmail,
            customerName: bookingData.customerName,
            pin: '****', // Hide PIN in logs
            bookingDate: bookingData.bookingDate,
            timeSlot: `${bookingData.startTime} to ${bookingData.endTime}`
        });

        return {
            success: true,
            recipient: bookingData.customerEmail,
            subject: subject,
            message: 'Email message copied to clipboard successfully',
            emailContent: emailMessage
        };

    } catch (error) {
        logOperation('Clipboard copy failed', {
            recipient: bookingData.customerEmail || 'unknown',
            error: error.message
        }, false);

        // If clipboard copy fails, at least display the message
        console.log('\n📧 EMAIL MESSAGE (clipboard copy failed):');
        console.log('═'.repeat(60));
        if (bookingData.customerEmail) {
            console.log(`To: ${bookingData.customerEmail}`);
            const subject = `Your ViFit Studio PIN - ${bookingData.bookingDate} ${bookingData.startTime} to ${bookingData.endTime}`;
            console.log(`Subject: ${subject}`);
            console.log('─'.repeat(60));
            const emailMessage = `Dear ${bookingData.customerName},

I hope this message finds you well. Your PIN for your booking at ${bookingData.location || 'ViFit Studio @ International Plaza'} from ${bookingData.bookingDate} ${bookingData.startTime} to ${bookingData.endTime} is ${bookingData.pin}.

Best regards,
ViFit Team`;
            console.log(emailMessage);
        }
        console.log('═'.repeat(60));

        return {
            success: false,
            error: error.message,
            recipient: bookingData.customerEmail || 'unknown'
        };
    }
}

/**
 * Helper function to format booking data for notification email from a Wix booking object
 * @param {Object} wixBooking - Wix booking object
 * @param {string} pin - Generated PIN for the booking
 * @returns {Object} Formatted booking data for notification email
 */
function formatBookingForNotificationEmail(wixBooking, pin) {
    const contact = wixBooking.contactDetails || {};
    const customerName = `${contact.firstName || ''} ${contact.lastName || ''}`.trim();

    // Format date as "30 August 2025" (not the existing YYYY-MM-DD format)
    const startDate = new Date(wixBooking.startDate);
    const bookingDate = startDate.toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });

    // Format times as "11am", "1pm" (not the existing HH:mm format)
    const startTime = startDate.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: startDate.getMinutes() === 0 ? undefined : '2-digit',
        hour12: true
    }).toLowerCase();

    const endDate = new Date(wixBooking.endDate);
    const endTime = endDate.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: endDate.getMinutes() === 0 ? undefined : '2-digit',
        hour12: true
    }).toLowerCase();

    return {
        customerName: customerName,
        customerEmail: contact.email,
        pin: pin,
        bookingDate: bookingDate,
        startTime: startTime,
        endTime: endTime,
        location: 'ViFit Studio @ International Plaza' // Default location
    };
}

// ========== Booking Functions ==========

async function getFilteredBookings() {
    const now = new Date();
    // Use current time instead of today's start to exclude past bookings
    const currentTime = now.toISOString();
    const wixClient = await getWixClient();

    //0bbecb67-86fa-4a1f-9572-1e760f0b5dba  Studio Rental (For Instructors only)
    //0c3eb27c-6ee5-4ec2-894a-5d1a0d68b2dc Self Practice Pilates
    const query = {
        filter: {
            "$and": [
                {
                    "status": {
                        "$eq": "CONFIRMED"
                    }
                },
                {
                    "bookedEntity.item.slot.serviceId": {
                        "$in": [
                            "0bbecb67-86fa-4a1f-9572-1e760f0b5dba",
                            "0c3eb27c-6ee5-4ec2-894a-5d1a0d68b2dc"
                        ]
                    }
                },
                {
                    "startDate": {
                        "$gte": currentTime
                    }
                }
            ]
        },
        sort: [
            {
                "fieldName": "startDate",
                "order": "ASC"
            }
        ]
    };
    const options = {
        withFormSubmissions: true, // Include form submissions to get custom field data
    };
    try {
        console.log('Using extendedBookings.queryExtendedBookings...');
        console.log('wixClient.extendedBookings available:', !!wixClient.extendedBookings);

        if (wixClient.extendedBookings && wixClient.extendedBookings.queryExtendedBookings) {
            // Use the API query language as per documentation
            const result = await wixClient.extendedBookings.queryExtendedBookings(query, options);

            console.log('Query result keys:', Object.keys(result));
            console.log('Number of bookings found (before Door filter):', result.extendedBookings?.length || 0);

            // Apply client-side filtering for Door field (empty or null)
            const filteredByDoor = filterBookingsByDoorField(result.extendedBookings || []);
            console.log('Number of bookings found (after Door filter):', filteredByDoor.length);

            return filteredByDoor;
        } else {

            console.log('extendedBookings module not available, checking available methods...');
            console.log('wixClient keys:', Object.keys(wixClient));
            if (wixClient.extendedBookings) {
                console.log('extendedBookings methods:', Object.keys(wixClient.extendedBookings));
            }
            throw new Error('extendedBookings.queryExtendedBookings not available');
        }
    } catch (error) {
        console.error('Error fetching bookings:', error.message);
        throw error;
    }
}
//Self Practice Pilates

function displayBookingDetails(booking) {
    console.log('📅 BOOKING DETAILS:');
    console.log(booking)
    var contact = booking.contactDetails;
    console.log(`   Customer: ${contact.firstName || ''} ${contact.lastName || ''}`);
    console.log(`   Email: ${contact.email || ''}`);
    console.log(`   Service: ${booking.bookedEntity.title || ''}`);
    console.log(`   Pin: ${booking.door || ''}`);

    // Display Door field information for debugging
    const doorFieldInAdditional = booking.additionalFields?.find(field => field.label === 'Door');
    console.log(`   Door (additionalFields): ${doorFieldInAdditional?.value || 'Not found/Empty'}`);

    console.log(`   Date: ${formatBookingDate(booking.startDate)}`);
    console.log(`   Time: ${formatBookingTime(booking.startDate)} - ${formatBookingTime(booking.endDate)}`);
    console.log(`   Duration: ${calculateDuration(booking.startDate, booking.endDate)} hour(s)`);
    console.log(`   Status: ${booking.status}`);
    console.log('');
}

// ========== Custom Field Filtering Functions ==========

/**
 * Filters bookings to include only those where the "Door" field is empty or null
 * @param {Array} extendedBookings - Array of extended booking objects
 * @returns {Array} Filtered array of extended bookings
 */
function filterBookingsByDoorField(extendedBookings) {
    return extendedBookings.filter(extendedBooking => {
        const booking = extendedBooking.booking;
        const formSubmissions = extendedBooking.formSubmissions;

        const isDoorEmpty = isDoorFieldEmptyOrNull(booking, formSubmissions);

        // Log for debugging
        if (!isDoorEmpty) {
            console.log(`Filtering out booking ${booking.id} - Door field has value`);
        }

        return isDoorEmpty;
    });
}

/**
 * Checks if the Door field is empty or null in both additionalFields and formSubmissions
 * @param {Object} booking - The booking object
 * @param {Array} formSubmissions - Array of form submission objects
 * @returns {boolean} True if Door field is empty/null, false otherwise
 */
function isDoorFieldEmptyOrNull(booking, formSubmissions) {
    // Check additionalFields array for Door field
    const doorFieldInAdditional = booking.additionalFields?.find(field =>
        field.label === 'Door'
    );

    // Check formSubmissions array for Door field (case-insensitive)
    const doorFieldInSubmissions = formSubmissions?.find(submission =>
        submission.label?.toLowerCase() === 'door' || submission.key?.toLowerCase() === 'door'
    );

    // If Door field exists in additionalFields, check if it's empty
    if (doorFieldInAdditional) {
        const hasValue = doorFieldInAdditional.value && doorFieldInAdditional.value.trim() !== '';
        if (hasValue) {
            console.log(`Booking ${booking._id}: Door field found in additionalFields with value: "${doorFieldInAdditional.value}"`);
            return false; // Has a value, so exclude this booking
        }
    }

    // If Door field exists in formSubmissions, check if it's empty
    if (doorFieldInSubmissions) {
        const hasValue = doorFieldInSubmissions.value && doorFieldInSubmissions.value.trim() !== '';
        if (hasValue) {
            console.log(`Booking ${booking._id}: Door field found in formSubmissions with value: "${doorFieldInSubmissions.value}"`);
            return false; // Has a value, so exclude this booking
        }
    }

    // If we get here, either:
    // 1. Door field doesn't exist at all (include booking)
    // 2. Door field exists but is empty/null (include booking)
    console.log(`Booking ${booking._id}: Door field is empty or missing - including in results`);
    return true;
}

// ========== Main Workflow ==========

async function main() {
    try {
        logOperation('Startup', {});

        // 1. Authenticate with Wix
        logOperation('Authenticating with Wix', {});
        const accessToken = await getWixAccessToken();
        logOperation('Wix authentication successful', {});

        // 2. Retrieve bookings
        logOperation('Retrieving bookings', {});
        const bookings = await getFilteredBookings(accessToken);
        logOperation('Bookings retrieved', { count: bookings.length });

        if (bookings.length === 0) {
            console.log('No bookings found for the specified criteria.');
            return;
        }

        // 3. Display bookings interactively
        console.log(`Found ${bookings.length} bookings to process...\n`);
        for (const bookingObj of bookings) {
            var booking = bookingObj.booking
            console.log('=== EXTENDED BOOKING DEBUG ===');
            console.log('formSubmissions:', bookingObj.formSubmissions);
            console.log('===============================');
            displayBookingDetails(booking);

            // Prompt user to create a PIN for this booking
            const createPinAns = await prompt('Create PIN for this booking? (y/n): ');
            if (createPinAns.trim().toLowerCase().startsWith('y')) {
                try {
                    // Use booking date/time if available, else prompt
                    const dateStr = booking.startDate ? formatBookingDate(booking.startDate) : await prompt('Enter date (YYYY-MM-DD) [GMT+8]: ');
                    const startTimeStr = booking.startDate ? formatBookingTime(booking.startDate) : await prompt('Enter start time (HH:mm or HHmm) [GMT+8]: ');
                    const endTimeStr = booking.endDate ? formatBookingTime(booking.endDate) : await prompt('Enter end time (HH:mm or HHmm) [GMT+8, default 1 hour after start]: ');

                    // Parse times
                    const { hour: startHour, minute: startMinute } = parseTime(startTimeStr);
                    let endHour, endMinute;
                    if (endTimeStr.trim() === '') {
                        endHour = startHour + 1;
                        endMinute = startMinute;
                        if (endHour >= 24) {
                            endHour = 23;
                            endMinute = 59;
                        }
                    } else {
                        ({ hour: endHour, minute: endMinute } = parseTime(endTimeStr));
                    }

                    // Round to nearest hour for PIN creation (50-55 minutes sessions become full hours)
                    let roundedStartHour = startHour;
                    let roundedEndHour = endHour;

                    // If minutes >= 30, round up to next hour, otherwise round down
                    if (startMinute >= 30) {
                        roundedStartHour = startHour + 1;
                        if (roundedStartHour >= 24) roundedStartHour = 23;
                    }

                    if (endMinute >= 30) {
                        roundedEndHour = endHour + 1;
                        if (roundedEndHour >= 24) roundedEndHour = 23;
                    }

                    // Ensure minimum 1 hour duration
                    if (roundedEndHour <= roundedStartHour) {
                        roundedEndHour = roundedStartHour + 1;
                        if (roundedEndHour >= 24) roundedEndHour = 23;
                    }

                    console.log(`Original times: ${startHour}:${startMinute.toString().padStart(2, '0')} - ${endHour}:${endMinute.toString().padStart(2, '0')}`);
                    console.log(`Rounded for PIN: ${roundedStartHour}:00 - ${roundedEndHour}:00`);

                    // Build ISO 8601 with +08:00 using rounded times, then adjust for 15 min before/after
                    const startIsoRaw = toGmt8Iso(dateStr, roundedStartHour, 0);
                    const endIsoRaw = toGmt8Iso(dateStr, roundedEndHour, 0);

                    const startIso = addMinutesGmt8(startIsoRaw, -15);
                    const endIso = addMinutesGmt8(endIsoRaw, 15);

                    // Check if the start time is in the future
                    const now = new Date();
                    const startTime = new Date(startIso.replace('+08:00', '+08:00'));

                    if (startTime <= now) {
                        console.log('⚠️  WARNING: This booking time is in the past or too close to now.');
                        console.log(`   Booking start: ${startIso}`);
                        console.log(`   Current time:  ${now.toISOString()}`);
                        console.log('   The PIN API requires future dates. You may need to:');
                        console.log('   1. Skip this booking if it\'s already completed');
                        console.log('   2. Manually adjust the time if needed');

                        const continueAns = await prompt('Continue with PIN creation anyway? (y/n): ');
                        if (!continueAns.trim().toLowerCase().startsWith('y')) {
                            console.log('Skipping PIN creation for this booking.');
                            continue;
                        }
                    }

                    const accessName = buildAccessName(startIso, endIso);

                    const lockAccessToken = await getAccessToken();

                    let pin, result, attempts = 0;
                    do {
                        pin = randomPin();
                        validatePin(pin);
                        result = await createCustomPinJob(lockAccessToken, accessName, pin, startIso, endIso);
                        attempts++;
                        if (result.pinExists) {
                            console.log(`PIN ${pin} already exists, retrying...`);
                        }
                    } while (result.pinExists && attempts < 20);

                    if (result.jobId) {
                        console.log('Job created successfully.');
                        console.log('Job ID:', result.jobId);
                        console.log('Access Name:', accessName);
                        console.log('PIN:', pin);
                        console.log('Start:', startIso, '(GMT+8)');
                        console.log('End:', endIso, '(GMT+8)');

                        // Automatically copy email to clipboard
                        try {
                            const emailData = formatBookingForNotificationEmail(booking, pin);
                            const emailResult = await copyBookingNotificationToClipboard(emailData);
                            if (emailResult.success) {
                                console.log('✅ Email copied to clipboard successfully!');
                            } else {
                                console.log('❌ Failed to copy email to clipboard:', emailResult.error);
                            }
                        } catch (emailErr) {
                            console.error('Error preparing email:', emailErr.message);
                        }
                    } else if (result.pinExists) {
                        console.error('Failed to generate a unique PIN after 20 attempts.');
                    }
                } catch (err) {
                    console.error('Error creating PIN for booking:', err.message);
                }
            }
            await prompt('Press Enter to continue to next booking...');
        }

        logOperation('All bookings displayed', { count: bookings.length });
    } catch (error) {
        logOperation('Fatal error', { error: error.message }, false);
        console.error('Fatal error:', error.message);
        process.exit(1);
    }
}

// ========== Module Exports ==========

export {
    main,
    getFilteredBookings,
    formatBookingDate,
    formatBookingTime,
    calculateDuration,
    BOOKING_STATUS_FILTER
};

// CLI execution check
import { fileURLToPath } from 'url';

if (fileURLToPath(import.meta.url) === process.argv[1]) {
    main().catch(error => {
        console.error('Fatal error:', error.message);
        process.exit(1);
    });
}
