import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { ListProvidersResponse, GetConnectionResponse, ListConnectionsOptions, ListConnectionsResponse, ConnectByOAuthResponse, ConnectByOAuthApplicationErrors, ConnectByCredentialsResponse, ConnectByCredentialsApplicationErrors, ListCalendarsResponse, ListCalendarsApplicationErrors, ConnectionSyncConfig, UpdateSyncConfigResponse, UpdateSyncConfigApplicationErrors, DisconnectResponse, DisconnectApplicationErrors, ListEventsOptions, ListEventsResponse } from './index.typings.js';
export { Address, AddressLocation, AddressStreetOneOf, ApprovalStatus, ApprovalStatusWithLiterals, Availability, AvailabilityConstraints, AvailabilityPolicy, AvailabilityPolicyUpdated, BusinessSchedule, Calendar, CalendarConference, CalendarDateTime, CalendarType, CalendarTypeWithLiterals, Calendars, ConferenceProvider, ConferenceType, ConferenceTypeWithLiterals, ConnectByCredentialsRequest, ConnectByOAuthRequest, ConnectMethod, ConnectMethodWithLiterals, Connection, ConnectionSyncConfigListEventFromCalendars, ConnectionSyncConfigListEventFromCalendarsListFromOneOf, ConnectionSyncConfigSyncToCalendar, ConnectionSyncConfigSyncToCalendarSyncToOneOf, CursorPaging, CursorPagingMetadata, Cursors, Day, DayOfWeek, DayOfWeekWithLiterals, DayWithLiterals, DedicatedCalendar, DisconnectRequest, Empty, ErrorReason, ErrorReasonWithLiterals, Event, ExternalCalendar, ExternalCalendarInfo, ExternalCalendarOverrides, Frequency, GetConnectionRequest, HeadersEntry, Interval, IntervalSplit, LinkedSchedule, ListCalendarsRequest, ListConnectionsRequest, ListEventFromCalendars, ListEventFromCalendarsWithLiterals, ListEventsRequest, ListProvidersRequest, LocalDateTime, Location, LocationStatus, LocationStatusWithLiterals, LocationType, LocationTypeWithLiterals, LocationsAddress, LocationsAddressLocation, LocationsLocation, LocationsLocationType, LocationsLocationTypeWithLiterals, LocationsStreetAddress, MigrationData, MigrationEvent, MultipleSessionsCreated, Participant, ParticipantNotification, PathParametersEntry, Price, PrimaryCalendar, Provider, ProviderFeatures, QueryParametersEntry, Rate, RawHttpRequest, RawHttpResponse, RecurringInterval, RecurringIntervalType, RecurringIntervalTypeWithLiterals, RecurringSessionSplit, RecurringSessionsUpdated, Schedule, ScheduleCancelled, ScheduleCreated, ScheduleNotification, ScheduleNotificationEventOneOf, ScheduleStatus, ScheduleStatusWithLiterals, ScheduleUnassignedFromUser, ScheduleUpdated, ScheduleWithSessions, Session, SessionCancelled, SessionCreated, SessionStatus, SessionStatusWithLiterals, SessionType, SessionTypeWithLiterals, SessionUpdated, SessionVersion, SitePropertiesOnScheduleCreation, SpecialHourPeriod, SplitInterval, StaffData, Status, StatusWithLiterals, StreetAddress, Subdivision, SyncToCalendar, SyncToCalendarWithLiterals, SyncToErrorReason, SyncToErrorReasonWithLiterals, TimePeriod, Transparency, TransparencyWithLiterals, UpdateSyncConfigRequest, Version } from './index.typings.js';

declare function listProviders$1(httpClient: HttpClient): ListProvidersSignature;
interface ListProvidersSignature {
    /**
     * Retrieves a list of external calendar providers supported on the Wix site.
     *
     *
     * The list of external calendar providers includes:
     *
     * + External calendar providers that are supported by default, such as Google, Apple, and Microsoft.
     * + External calenders for which the site owner has enabled integration by installing an app.
     *
     * For each provider, check `features.connectMethods` to find out whether to use
     * _Connect By Credentials_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))
     * or _Connect By OAuth_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))
     * to establish a connection.
     */
    (): Promise<NonNullablePaths<ListProvidersResponse, `providers` | `providers.${number}.calendarType` | `providers.${number}.features.listEventFromCalendars` | `providers.${number}.features.syncToCalendar`, 5>>;
}
declare function getConnection$1(httpClient: HttpClient): GetConnectionSignature;
interface GetConnectionSignature {
    /**
     * Retrieves an external calendar connection by ID.
     *
     *
     * The `syncConfig` property contains configuration details about event import
     * from and event export to the external calendar.
     * @param - ID of the connection to retrieve.
     */
    (connectionId: string): Promise<NonNullablePaths<GetConnectionResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6>>;
}
declare function listConnections$1(httpClient: HttpClient): ListConnectionsSignature;
interface ListConnectionsSignature {
    /**
     * Retrieves a list of external calendar connections.
     *
     *
     * ## Filter
     *
     * You can filter by *schedule ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * ## Partial success
     *
     * By default, the call fails if details for at least 1 connection can't be
     * retrieved from the external provider. However, you can specify
     * `{"partialFailure": true}` to allow the call to succeed, if details for at
     * least 1 connection can be retrieved.
     * @param - Options to use when listing connections.
     */
    (options?: ListConnectionsOptions): Promise<NonNullablePaths<ListConnectionsResponse, `connections` | `connections.${number}.calendarType` | `connections.${number}.status` | `connections.${number}.errorReason` | `failedProviderIds`, 4>>;
}
declare function connectByOAuth$1(httpClient: HttpClient): ConnectByOAuthSignature;
interface ConnectByOAuthSignature {
    /**
     * Connects a *Wix schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to an external calendar account following [OAuth authorization protocol](https://oauth.net/2/).
     *
     *
     * ## Authorization flow
     *
     * The full authorization flow looks like this:
     *
     * 1. Call *Connect By OAuth* with the appropriate parameters.
     * 2. Redirect the owner of the external calendar account to the returned
     * `oAUthUrl`.
     * 3. The account owner authorizes access.
     * 4. The account owner is automatically redirected to the URL you've provided
     * in `redirectUrl` of the *Connect By OAuth* call.
     * 5. Save the new `connectionId`. You find it as a query parameter in the URL
     * to which the account owner is redirected.
     *
     * See *this sample flow*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/sample-flows#connect-an-external-calendar-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/sample-flows#connect-an-external-calendar-by-oauth))
     * for more details.
     *
     * ## Failure consequences
     *
     * If the attempt to connect fails, the account owner is still redirected to
     * the URL you specify in `redirectUrl`, but it incudes an `error` query
     * parameter set to 1 of the following values:
     *
     * - `reject`: The external calendar owner has rejected the authorization request.
     * - `unsupported`: Connecting to the user's external account type is not supported by the provider.
     * - `internal`: An error unrelated to the client or the request that prevents the server from fulfilling the request.
     *
     * ## Next steps
     *
     * After connecting an external calendar account to a Wix schedule, you can do
     * the following.
     *
     * ### Check the 2-way-sync settings
     *
     * Call *List Connections*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))
     * and filter by the relevant schedule ID.
     *
     * If needed, call *Update Sync Config*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))
     * to adjust the syncing configuration.
     *
     * ### Retrieve external calendar events
     *
     * Once a connection is successfully created, you can call *List Events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
     * to obtain an up-to-date list of events in the connected external calendars.
     *
     * ## When to call Connect By Credentials instead
     *
     * You can call *Connect By Credentials*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-credentials) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-credentials))
     * if:
     *
     * + The external calendar provider doesn't support OAuth.
     * + You don't want to redirect the account owner.
     *
     * Call *List Providers*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))
     * for a complete list of external calendar providers supported on the Wix site,
     * including which authorization methods they support.
     * @param - ID of the schedule to connect with the external calendar account.
     * @param - URL to redirect the user to after they authorize access to the external calendar account.
     *
     * If the connection is successfully established, the user is redirected to this URL with a query parameter `connectionId` containing the new connection ID.
     * If the attempt to connect fails, the user is redirected to this URL with a query parameter `error` containing the error type.
     * @param - ID of the external calendar provider. Find this with the `listProviders()` function.
     */
    (providerId: string, scheduleId: string, redirectUrl: string): Promise<ConnectByOAuthResponse & {
        __applicationErrorsType?: ConnectByOAuthApplicationErrors;
    }>;
}
declare function connectByCredentials$1(httpClient: HttpClient): ConnectByCredentialsSignature;
interface ConnectByCredentialsSignature {
    /**
     * Connects a *Wix schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to an external calendar account using credentials.
     *
     *
     * ## Next steps
     *
     * After connecting an external calendar account to a Wix schedule, you can do
     * the following.
     *
     * ### Check the 2-way-sync settings
     *
     * Call *List Connections*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections))
     * and filter by the relevant schedule ID.
     *
     * If needed, call *Update Sync Config*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/update-sync-config) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/update-sync-config))
     * to adjust the syncing configuration.
     *
     * ### Retrieve external calendar events
     *
     * Once a connection is successfully created, you can call *List Events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
     * to obtain an up-to-date list of events in the connected external calendars.
     *
     * ## When to call Connect By OAuth instead
     *
     * You could call *Connect By OAuth*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/connect-by-oauth) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/connect-by-o-auth))
     * if:
     *
     * + The external calendar provider doesn't support authorization by credentials.
     * + You don't have access to the external calendar account credentials.
     * + You want the account owner to approve the connection.
     *
     * Call *List Providers*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-providers) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-providers))
     * for a complete list of external calendar providers supported on the Wix site,
     * including which authorization methods they support.
     * @param - ID of the schedule to connect with the external calendar account.
     * @param - Email address for the external calendar account.
     * @param - Password for the external calendar account.
     * @param - ID of the external calendar provider. Find this with the `listProviders()` function.
     */
    (providerId: string, scheduleId: string, email: string, password: string): Promise<NonNullablePaths<ConnectByCredentialsResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6> & {
        __applicationErrorsType?: ConnectByCredentialsApplicationErrors;
    }>;
}
declare function listCalendars$1(httpClient: HttpClient): ListCalendarsSignature;
interface ListCalendarsSignature {
    /**
     * Retrieves details about the external calendar accounts associated with the specified connection.
     * @param - ID of the external calendar connection to list calendars for.
     */
    (connectionId: string): Promise<NonNullablePaths<ListCalendarsResponse, `calendars`, 2> & {
        __applicationErrorsType?: ListCalendarsApplicationErrors;
    }>;
}
declare function updateSyncConfig$1(httpClient: HttpClient): UpdateSyncConfigSignature;
interface UpdateSyncConfigSignature {
    /**
     * Updates the import and export settings for an external calendar connection's
     * events.
     *
     *
     * A connection's `syncConfig` object determines:
     *
     * + Whether events from 1 or more accounts belonging to the external
     * calendar are imported to the connected *Wix schedule*.
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     * If importing events is enabled, you can call _List Events_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
     * to retrieve external calendar events.
     * + Whether events from the Wix schedule are exported to 1 or more accounts
     * belonging to the external calendar.
     *
     * To check the current import and export settings, you can call
     * _Get Connection_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/get-connection) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/get-connection))
     * or _List Connections_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-connections) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-connections)).
     * @param - ID of the external calendar connection to update.
     * @param - Updated sync configuration details.
     */
    (connectionId: string, syncConfig: ConnectionSyncConfig): Promise<NonNullablePaths<UpdateSyncConfigResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6> & {
        __applicationErrorsType?: UpdateSyncConfigApplicationErrors;
    }>;
}
declare function disconnect$1(httpClient: HttpClient): DisconnectSignature;
interface DisconnectSignature {
    /**
     * Disconnects a Wix schedule from an external calendar and deletes all Wix
     * calendar *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * from the external calendar.
     *
     *
     * When an external calendar is disconnected, the connection's `status`
     * changes to `DISCONNECTED`.
     *
     * After disconnecting, _List Events_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/external-calendars/list-events) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/calendar/external-calendar-v2/list-events))
     * no longer returns events from the disconnected external calendar.
     * @param - ID of the external calendar connection to disconnect.
     */
    (connectionId: string): Promise<NonNullablePaths<DisconnectResponse, `connection.calendarType` | `connection.status` | `connection.errorReason` | `connection.syncConfig.listEventFromCalendars.calendars.calendars`, 6> & {
        __applicationErrorsType?: DisconnectApplicationErrors;
    }>;
}
declare function listEvents$1(httpClient: HttpClient): ListEventsSignature;
interface ListEventsSignature {
    /**
     * Retrieves a list of events from all external calendar accounts, based on
     * the provided filtering and paging.
     *
     *
     * ## Filters
     *
     * You must filter by specifying both `from` and `to` dates, unless you specify
     * `cursorPaging.cursor`.
     *
     * Additionally, you can specify `scheduleIds` or `userIds` to further limit
     * which events are returned. By default, events related to all schedules and
     * Wix users are returned.
     *
     * ## Sorting
     *
     * Returned events are sorted by start date in ascending order. You can't
     * adjust the sorting.
     *
     * ## Personal data
     *
     * By default, the following `event` fields aren't returned:
     *
     * + `calendarName`.
     * + `title`.
     * + `scheduleOwnerName`.
     *
     * You can retrieve these fields, by specifying `{"fieldsets": "OWN_PI"}`.
     *
     * ## Partial success
     *
     * By default, the call fails if events for at least 1 connection can't be
     * retrieved from an external provider. However, you can specify
     * `{"partialFailure": true}` to allow the call to succeed, if details for at
     * least 1 connection can be retrieved.
     * @param - Options to use when listing events.
     */
    (options?: ListEventsOptions): Promise<NonNullablePaths<ListEventsResponse, `events` | `events.${number}.calendarType` | `failedProviderIds`, 4>>;
}

declare const listProviders: MaybeContext<BuildRESTFunction<typeof listProviders$1> & typeof listProviders$1>;
declare const getConnection: MaybeContext<BuildRESTFunction<typeof getConnection$1> & typeof getConnection$1>;
declare const listConnections: MaybeContext<BuildRESTFunction<typeof listConnections$1> & typeof listConnections$1>;
declare const connectByOAuth: MaybeContext<BuildRESTFunction<typeof connectByOAuth$1> & typeof connectByOAuth$1>;
declare const connectByCredentials: MaybeContext<BuildRESTFunction<typeof connectByCredentials$1> & typeof connectByCredentials$1>;
declare const listCalendars: MaybeContext<BuildRESTFunction<typeof listCalendars$1> & typeof listCalendars$1>;
declare const updateSyncConfig: MaybeContext<BuildRESTFunction<typeof updateSyncConfig$1> & typeof updateSyncConfig$1>;
declare const disconnect: MaybeContext<BuildRESTFunction<typeof disconnect$1> & typeof disconnect$1>;
declare const listEvents: MaybeContext<BuildRESTFunction<typeof listEvents$1> & typeof listEvents$1>;

export { ConnectByCredentialsApplicationErrors, ConnectByCredentialsResponse, ConnectByOAuthApplicationErrors, ConnectByOAuthResponse, ConnectionSyncConfig, DisconnectApplicationErrors, DisconnectResponse, GetConnectionResponse, ListCalendarsApplicationErrors, ListCalendarsResponse, ListConnectionsOptions, ListConnectionsResponse, ListEventsOptions, ListEventsResponse, ListProvidersResponse, UpdateSyncConfigApplicationErrors, UpdateSyncConfigResponse, connectByCredentials, connectByOAuth, disconnect, getConnection, listCalendars, listConnections, listEvents, listProviders, updateSyncConfig };
