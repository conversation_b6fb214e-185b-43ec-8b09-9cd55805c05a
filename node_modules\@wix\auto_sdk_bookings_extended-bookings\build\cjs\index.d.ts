import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { QueryV2, QueryOptions, QueryExtendedBookingResponse, CommonQueryV2, QueryExtendedBookingsOptions, QueryExtendedBookingsResponse, CountExtendedBookingsOptions, CountExtendedBookingsResponse } from './index.typings.js';
export { ActionType, ActionTypeWithLiterals, Actor, ActorWithLiterals, AdditionalFeeRefund, Address, AddressLocation, AddressStreetOneOf, AggregatedRefundSummary, AllowedActions, Attendance, AttendanceStatus, AttendanceStatusWithLiterals, AuthorizationActionFailureDetails, AuthorizationCapture, AuthorizationCaptureStatus, AuthorizationCaptureStatusWithLiterals, AuthorizationDetails, AuthorizationVoid, AuthorizationVoidStatus, AuthorizationVoidStatusWithLiterals, Balance, BalanceSummary, BookedAddOn, BookedEntity, BookedEntityItemOneOf, BookedResource, BookedSchedule, BookedSlot, Booking, BookingFeeDetails, BookingFeeStatus, BookingFeeStatusWithLiterals, BookingParticipantsInfoOneOf, BookingPaymentStatus, BookingPaymentStatusWithLiterals, BookingPolicySettings, BookingSource, BookingStatus, BookingStatusWithLiterals, CatalogReference, Chargeback, ChargebackStatus, ChargebackStatusWithLiterals, CommonPaging, CommonQueryV2PagingMethodOneOf, ConferencingDetails, ContactDetails, CountExtendedBookingsRequest, CreditCardPaymentMethodDetails, CursorPaging, Cursors, CustomFormField, Duration, ExtendedBooking, ExtendedFields, ExternalReceiptInfo, FlowControlSettings, FormSubmission, GiftCardPaymentDetails, IdentificationData, IdentificationDataIdOneOf, IdentityType, IdentityTypeWithLiterals, Invoice, LineItemRefund, LineItemRefundSummary, Location, LocationType, LocationTypeWithLiterals, MembershipName, MembershipPaymentDetails, MembershipPaymentStatus, MembershipPaymentStatusWithLiterals, MultiServiceBookingInfo, MultiServiceBookingType, MultiServiceBookingTypeWithLiterals, Order, OrderInvoices, OrderLineItem, OrderStatus, OrderStatusWithLiterals, OrderTransactions, Paging, PagingMetadataV2, ParticipantChoices, ParticipantNotification, Payment, PaymentOptionType, PaymentOptionTypeWithLiterals, PaymentPaymentDetailsOneOf, PaymentReceiptInfoOneOf, PaymentStatus, PaymentStatusWithLiterals, Platform, PlatformWithLiterals, Price, PriceDescription, PriceSummary, QueryExtendedBookingRequest, QueryExtendedBookingsRequest, QueryV2PagingMethodOneOf, Reason, ReasonWithLiterals, Refund, RefundDetails, RefundItem, RefundItemsBreakdown, RefundStatus, RefundStatusInfo, RefundStatusWithLiterals, RefundTransaction, RegularPaymentDetails, RegularPaymentDetailsPaymentMethodDetailsOneOf, ScheduledAction, SelectedPaymentOption, SelectedPaymentOptionWithLiterals, ServiceChoice, ServiceChoiceChoiceOneOf, ServiceChoices, ShippingRefund, SortOrder, SortOrderWithLiterals, Sorting, Status, StatusWithLiterals, StreetAddress, Subdivision, TransactionStatus, TransactionStatusWithLiterals, ValueType, ValueTypeWithLiterals, Waiver, WixReceiptInfo } from './index.typings.js';

declare function query$1(httpClient: HttpClient): QuerySignature;
interface QuerySignature {
    /**
     * > **Deprecation Notice**
     * >
     * > **This endpoint has been replaced with [Query Extended Bookings](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings) and will be removed on May 31, 2025.**
     * > **If your app uses this endpoint, we recommend updating your code as soon as possible.**
     *
     *
     * Retrieves a list of bookings, given the provided paging, filtering, and sorting.
     *
     *
     * You can also retrieve information about which actions the customer can perform
     * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.
     *
     * Query Bookings runs with these defaults:
     *
     * - `createdDate` sorted in `DESC` order
     * - `cursorPaging.limit` is `50`
     *
     * You can check the overview about all
     * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)
     * for more information.
     *
     * `query.fields` and `query.fieldsets` aren't supported for this endpoint.
     *
     * When using filters for dates, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
     *
     * Bookings belonging to a schedule don't have a `sessionId`. Therefore you
     * must use the `sessionId` filter that isn't part of the `query` object to
     * filter bookings for courses.
     *
     * To learn about working with _Query_ endpoints, see
     * [API Query Language](https://dev.wix.com/api/rest/getting-started/api-query-language),
     * [Sorting and Paging](https://dev.wix.com/api/rest/getting-started/sorting-and-paging),
     * and [Field Projection](https://dev.wix.com/api/rest/getting-started/field-projection).
     *
     * When calling Query Bookings, the retrieved data may not contain your most recent changes. See
     * [Wix-data and Eventual Consistency](https://dev.wix.com/docs/rest/business-solutions/cms/eventual-consistency)
     * for more information.
     * @param - Information about filters, paging, and sorting.
     * @param - Additional options for performing the query.
     * @deprecated
     */
    (query: QueryV2, options?: QueryOptions): Promise<NonNullablePaths<QueryExtendedBookingResponse, `extendedBookings` | `extendedBookings.${number}.booking.totalParticipants` | `extendedBookings.${number}.booking.status` | `extendedBookings.${number}.booking.paymentStatus` | `extendedBookings.${number}.booking.selectedPaymentOption` | `extendedBookings.${number}.booking.createdBy.anonymousVisitorId` | `extendedBookings.${number}.booking.createdBy.memberId` | `extendedBookings.${number}.booking.createdBy.wixUserId` | `extendedBookings.${number}.booking.createdBy.appId` | `extendedBookings.${number}.allowedActions.cancel` | `extendedBookings.${number}.allowedActions.reschedule` | `extendedBookings.${number}.attendance.status` | `extendedBookings.${number}.attendance.numberOfAttendees` | `extendedBookings.${number}.bookingFeeDetails.cancellationFeeStatus` | `extendedBookings.${number}.order.number` | `extendedBookings.${number}.order.status` | `extendedBookings.${number}.order.paymentStatus` | `extendedBookings.${number}.waiver.status` | `extendedBookings.${number}.invoices.orderId` | `extendedBookings.${number}.transactions.orderId`, 6>>;
}
declare function queryExtendedBookings$1(httpClient: HttpClient): QueryExtendedBookingsSignature;
interface QueryExtendedBookingsSignature {
    /**
     * Retrieves a list of bookings, including additional extended information, given the provided paging, filtering, and sorting.
     *
     * `queryExtendedBookings()` doesn't use a query builder, instead it uses [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#with-the-api-query-language).
     *
     * Up to 100 extended bookings can be returned per request.
     *
     * `queryExtendedBookings()` runs with these defaults, which you can override:
     *
     * - `createdDate` sorted in `DESC` order
     * - `cursorPaging.limit` is `50`
     *
     *
     * You can retrieve information about which actions the customer can perform
     * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.
     *
     *
     * For field support, see
     * [supported filters](https://www.wix.com/velo/reference/wix-bookings-v2/extendedbookings/supported-filters)
     * for more information.
     *
     * You can specify a filter only once per query. If you specify a filter
     * more than once, only the first filter determines the extended bookings that are returned.
     *
     * When filtering by date, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
     * @param - Information about filters, paging, and sorting.
     * @param - Additional options for performing the query.
     */
    (query: CommonQueryV2, options?: QueryExtendedBookingsOptions): Promise<NonNullablePaths<QueryExtendedBookingsResponse, `extendedBookings` | `extendedBookings.${number}.booking.totalParticipants` | `extendedBookings.${number}.booking.status` | `extendedBookings.${number}.booking.paymentStatus` | `extendedBookings.${number}.booking.selectedPaymentOption` | `extendedBookings.${number}.booking.createdBy.anonymousVisitorId` | `extendedBookings.${number}.booking.createdBy.memberId` | `extendedBookings.${number}.booking.createdBy.wixUserId` | `extendedBookings.${number}.booking.createdBy.appId` | `extendedBookings.${number}.allowedActions.cancel` | `extendedBookings.${number}.allowedActions.reschedule` | `extendedBookings.${number}.attendance.status` | `extendedBookings.${number}.attendance.numberOfAttendees` | `extendedBookings.${number}.bookingFeeDetails.cancellationFeeStatus` | `extendedBookings.${number}.order.number` | `extendedBookings.${number}.order.status` | `extendedBookings.${number}.order.paymentStatus` | `extendedBookings.${number}.waiver.status` | `extendedBookings.${number}.invoices.orderId` | `extendedBookings.${number}.transactions.orderId`, 6>>;
}
declare function countExtendedBookings$1(httpClient: HttpClient): CountExtendedBookingsSignature;
interface CountExtendedBookingsSignature {
    /**
     * Counts the number of bookings matching the specified filters.
     *
     *
     * For field support see
     * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)
     * for more information.
     */
    (options?: CountExtendedBookingsOptions): Promise<NonNullablePaths<CountExtendedBookingsResponse, `count`, 2>>;
}

declare const query: MaybeContext<BuildRESTFunction<typeof query$1> & typeof query$1>;
declare const queryExtendedBookings: MaybeContext<BuildRESTFunction<typeof queryExtendedBookings$1> & typeof queryExtendedBookings$1>;
declare const countExtendedBookings: MaybeContext<BuildRESTFunction<typeof countExtendedBookings$1> & typeof countExtendedBookings$1>;

export { CommonQueryV2, CountExtendedBookingsOptions, CountExtendedBookingsResponse, QueryExtendedBookingResponse, QueryExtendedBookingsOptions, QueryExtendedBookingsResponse, QueryOptions, QueryV2, countExtendedBookings, query, queryExtendedBookings };
