import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { Category, CreateCategoryApplicationErrors, UpdateCategory, CategoriesQueryBuilder, CountCategoriesOptions, CountCategoriesResponse, MoveCategoryOptions, MoveCategoryResponse, CategoryCreatedEnvelope, CategoryDeletedEnvelope, CategoryUpdatedEnvelope } from './index.typings.js';
export { ActionEvent, BaseEventMetadata, CategoriesQueryResult, CountCategoriesRequest, CreateCategoryRequest, CreateCategoryResponse, CursorPaging, CursorPagingMetadata, CursorQuery, CursorQueryPagingMethodOneOf, Cursors, DeleteCategoryRequest, DeleteCategoryResponse, DomainEvent, DomainEventBodyOneOf, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, EventMetadata, ExtendedFields, GetCategoryRequest, GetCategoryResponse, IdentificationData, IdentificationDataIdOneOf, ImportCategoriesRequest, ImportCategoriesResponse, MessageEnvelope, MoveCategoryRequest, Position, PositionWithLiterals, PublishCategoriesUpdatedRequest, PublishCategoriesUpdatedResponse, QueryCategoriesRequest, QueryCategoriesResponse, RestoreInfo, SortOrder, SortOrderWithLiterals, Sorting, UpdateCategoryRequest, UpdateCategoryResponse, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.js';

declare function createCategory$1(httpClient: HttpClient): CreateCategorySignature;
interface CreateCategorySignature {
    /**
     * Creates a category.
     * @param - Category to create.
     * @returns Created category.
     */
    (category: NonNullablePaths<Category, `name`, 2>): Promise<Category & {
        __applicationErrorsType?: CreateCategoryApplicationErrors;
    }>;
}
declare function getCategory$1(httpClient: HttpClient): GetCategorySignature;
interface GetCategorySignature {
    /**
     * Retrieves a category.
     * @param - ID of the category to retrieve.
     * @returns Retrieved category.
     */
    (categoryId: string): Promise<Category>;
}
declare function updateCategory$1(httpClient: HttpClient): UpdateCategorySignature;
interface UpdateCategorySignature {
    /**
     * Updates a category.
     *
     *
     * Each time the category is updated, `revision` increments by 1.
     * You must specify the current `revision` to prevent unintended overwrites.
     *
     * You can't adjust a categories `sortOrder` with this method, call Move Category ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/move-category) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/move-category)) instead.
     * @param - Category ID.
     * @returns Updated category.
     */
    (_id: string, category: NonNullablePaths<UpdateCategory, `revision`, 2>): Promise<Category>;
}
declare function deleteCategory$1(httpClient: HttpClient): DeleteCategorySignature;
interface DeleteCategorySignature {
    /**
     * Deletes a category.
     *
     *
     * ## Impact on connected services
     *
     * When you delete a category, any services linked to it remain associated with the now-deleted category. Wix Bookings still displays these services to business owners in the dashboard, but they aren't visible to customers on the live site.
     * Attempting to call Update Service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service)) for a service that's linked to a deleted category fails, unless you specify a different, existing category ID in `service.category.id`.
     * @param - ID of the category to delete.
     */
    (categoryId: string): Promise<void>;
}
declare function queryCategories$1(httpClient: HttpClient): QueryCategoriesSignature;
interface QueryCategoriesSignature {
    /**
     * Creates a query to retrieve a list of `category` objects.
     *
     * The `queryCategories()` function builds a query to retrieve a list of `category` objects and returns a `categoriesQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-builder/find) function.
     *
     * You can refine the query by chaining `CategoriesQueryBuilder` functions onto the query. `CategoriesQueryBuilder` functions enable you to sort, filter, and control the results that `queryCategories()` returns.
     *
     * `queryCategories()` runs with the following `CategoriesQueryBuilder` defaults that you can override:
     *
     * + `limit` is `100`.
     * + Sorted by `createdDate` in ascending order.
     *
     * The functions that are chained to `queryCategories()` are applied in the order they are called. For example, if you apply `ascending("name")` and then `ascending("id")`, the results are sorted first by `name`, and then, if there are multiple results with the same `name`, the items are sorted by `id`.
     *
     * The following `CategoriesQueryBuilder` functions are supported for the `queryCategories()` function. For a full description of the `category` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-result/items) property in `CategoriesQueryResult`.
     */
    (): CategoriesQueryBuilder;
}
declare function countCategories$1(httpClient: HttpClient): CountCategoriesSignature;
interface CountCategoriesSignature {
    /**
     * Counts categories, given the specified filtering.
     *
     *
     * Refer to the Supported Filters article ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/filtering-and-sorting)) for a complete list of supported filters.
     */
    (options?: CountCategoriesOptions): Promise<NonNullablePaths<CountCategoriesResponse, `count`, 2>>;
}
declare function moveCategory$1(httpClient: HttpClient): MoveCategorySignature;
interface MoveCategorySignature {
    /**
     * Moves a category to the start, end, or immediately after a specified category by updating its `sortOrder` field.
     *
     * Wix Bookings assigns `sortOrder` values with large gaps between categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new values to restore larger gaps.
     * @param - ID of the category to move.
     */
    (categoryId: string, options?: MoveCategoryOptions): Promise<MoveCategoryResponse>;
}
declare const onCategoryCreated$1: EventDefinition<CategoryCreatedEnvelope, "wix.bookings.categories.v2.category_created">;
declare const onCategoryDeleted$1: EventDefinition<CategoryDeletedEnvelope, "wix.bookings.categories.v2.category_deleted">;
declare const onCategoryUpdated$1: EventDefinition<CategoryUpdatedEnvelope, "wix.bookings.categories.v2.category_updated">;

declare const createCategory: MaybeContext<BuildRESTFunction<typeof createCategory$1> & typeof createCategory$1>;
declare const getCategory: MaybeContext<BuildRESTFunction<typeof getCategory$1> & typeof getCategory$1>;
declare const updateCategory: MaybeContext<BuildRESTFunction<typeof updateCategory$1> & typeof updateCategory$1>;
declare const deleteCategory: MaybeContext<BuildRESTFunction<typeof deleteCategory$1> & typeof deleteCategory$1>;
declare const queryCategories: MaybeContext<BuildRESTFunction<typeof queryCategories$1> & typeof queryCategories$1>;
declare const countCategories: MaybeContext<BuildRESTFunction<typeof countCategories$1> & typeof countCategories$1>;
declare const moveCategory: MaybeContext<BuildRESTFunction<typeof moveCategory$1> & typeof moveCategory$1>;
/**
 * Triggered when a category is created.
 */
declare const onCategoryCreated: BuildEventDefinition<typeof onCategoryCreated$1>;
/**
 * Triggered when a category is deleted.
 */
declare const onCategoryDeleted: BuildEventDefinition<typeof onCategoryDeleted$1>;
/**
 * Triggered when a category is updated.
 */
declare const onCategoryUpdated: BuildEventDefinition<typeof onCategoryUpdated$1>;

export { CategoriesQueryBuilder, Category, CategoryCreatedEnvelope, CategoryDeletedEnvelope, CategoryUpdatedEnvelope, CountCategoriesOptions, CountCategoriesResponse, CreateCategoryApplicationErrors, MoveCategoryOptions, MoveCategoryResponse, UpdateCategory, countCategories, createCategory, deleteCategory, getCategory, moveCategory, onCategoryCreated, onCategoryDeleted, onCategoryUpdated, queryCategories, updateCategory };
