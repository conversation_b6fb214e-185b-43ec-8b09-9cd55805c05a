{"version": 3, "sources": ["../../src/bookings-availability-v2-time-slot-availability-time-slots.http.ts", "../../src/bookings-availability-v2-time-slot-availability-time-slots.meta.ts"], "sourcesContent": ["import { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_availability-time-slots';\n\n/**\n * Retrieves a list of appointment time slots that match the provided filters.\n *\n *\n * ## Defaults\n *\n * List Availability Time Slots uses the following defaults:\n *\n * - Sorts time slots by `localStartDate` in ascending order.\n * - `cursorPaging.limit` set to `1000`.\n * - Returns both bookable and un-bookable time slots.\n * - If `bookable` filter isn't specified, bookable slots are returned first.\n * - `fromLocalDate` is set to the current time.\n *\n * ## Filtering behavior\n *\n * The method automatically filters time slots:\n *\n * - **Past time slots**: Only time slots starting from the specified `fromLocalDate` are included. When you omit `fromLocalDate`, Wix Bookings uses the current time as the default.\n * - **Resource availability**: Only time slots with at least some available resources are returned.\n *\n * ## Service type limitations\n *\n * Only appointment-based services are supported when calling List Availability Time Slots.\n *\n * To retrieve class availability, you can call List Event Time Slots([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/event-time-slots/list-event-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-event-time-slots)).\n *\n * To retrieve course availability, you can follow the End-to-End Booking Flow for course([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flow#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n *\n * ## Available resources\n *\n * Time slots aren't returned when they have no available resources.\n *\n * Each time slot includes details for up to 10 available resources.\n * If there are more than 10 resources, you can call Get Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/availability/availability-time-slots/get-availability-time-slot)) and filter by the resource Type ID to get details for the remaining resources.\n *\n * ## Business hours exception\n *\n * Wix Bookings disregards business opening hours when all of the following conditions are met:\n *\n * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n * <-!-- 4. The service doesn't support add-ons ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/add-ons/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/add-ons/introduction)). -->\n *\n * In these cases, the working hours of the relevant staff member are used for availability calculation.\n */\nexport function listAvailabilityTimeSlots(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listAvailabilityTimeSlots({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [{ path: 'bookingPolicyViolations.earliestBookingDate' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.ListAvailabilityTimeSlots',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl({\n        protoPath: '/v2/time-slots',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlots.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listAvailabilityTimeSlots;\n}\n\n/**\n * Retrieves detailed information about a specific appointment time slot.\n *\n *\n * Call this method to get complete resource availability after finding a suitable slot with List Availability Time Slots ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).\n *\n * ## Defaults\n *\n * Get Availability Time Slot uses the following defaults:\n *\n * - Returns all available resources unless filtered by `resourceIds` or `includeResourceTypeIds`.\n * - Includes full booking status and capacity details\n *\n * ## Service type limitations\n *\n * Only appointment-based services are supported when calling Get Availability Time Slot.\n *\n * To retrieve class session availability, you can call Get Event Time Slot ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/get-event-time-slot)).\n *\n * To retrieve course availability, you can follow the End-to-End Booking Flow for courses ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/end-to-end-booking-flows#book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flow#book-a-course)).\n *\n * ## Business hours exception\n *\n * Wix Bookings disregards business opening hours when all of the following conditions are met:\n *\n * 1. 1 or more staff members ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) are needed to provide the service.\n * 2. No other resource type ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resource-types-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)) is needed to provide the service.\n * 3. The service doesn't have duration-based variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/service-options-and-variants/introduction)).\n *\n * In these cases, the working hours of the relevant staff member are used instead for availability calculation.\n */\nexport function getAvailabilityTimeSlot(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getAvailabilityTimeSlot({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.GetAvailabilityTimeSlot',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl({\n        protoPath: '/v2/time-slots/get',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlot.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getAvailabilityTimeSlot;\n}\n", "import * as ambassadorWixBookingsAvailabilityV2TimeSlot from './bookings-availability-v2-time-slot-availability-time-slots.http.js';\nimport * as ambassadorWixBookingsAvailabilityV2TimeSlotTypes from './bookings-availability-v2-time-slot-availability-time-slots.types.js';\nimport * as ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes from './bookings-availability-v2-time-slot-availability-time-slots.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function listAvailabilityTimeSlots(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.ListAvailabilityTimeSlotsRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.ListAvailabilityTimeSlotsRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.ListAvailabilityTimeSlotsResponse,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.ListAvailabilityTimeSlotsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.listAvailabilityTimeSlots(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/time-slots',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getAvailabilityTimeSlot(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.GetAvailabilityTimeSlotRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.GetAvailabilityTimeSlotRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.GetAvailabilityTimeSlotResponse,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.GetAvailabilityTimeSlotResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.getAvailabilityTimeSlot(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/time-slots/get',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,iEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAiDd,SAAS,0BACd,SAC4B;AAC5B,WAAS,4BAA4B,EAAE,KAAK,GAAQ;AAClD,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,8CAA8C,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iEAAiE;AAAA,QACpE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiCO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,iEAAiE;AAAA,QACpE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC9QO,SAASC,6BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACwC;AAAA,IAC1C;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,2BAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACwC;AAAA,IAC1C;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "listAvailabilityTimeSlots", "getAvailabilityTimeSlot"]}