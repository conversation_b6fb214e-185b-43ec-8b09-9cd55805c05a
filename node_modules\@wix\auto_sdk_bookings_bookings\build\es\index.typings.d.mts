import { NonNullablePaths } from '@wix/sdk-types';

/** An entity representing a scheduled appointment, class session, or course. */
interface Booking extends BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
    /**
     * Booking ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * An object describing the bookable entity - either a specific time slot or a recurring schedule.
     *
     * The structure depends on the type of service being booked:
     *
     * *For appointment services:** Use `slot` to book a specific time slot with a
     * service provider. Appointments are typically one-time sessions at a specific date and time.
     *
     * *For class services:** Use `slot` to book a specific class session. Classes
     * are individual sessions that can have multiple participants.
     *
     * *For course services:** Use `schedule` to book an entire course consisting of
     * multiple sessions over time. Courses are recurring, multi-session offerings.
     *
     * Choose the appropriate field based on your service type and booking requirements.
     */
    bookedEntity?: BookedEntity;
    /**
     * Contact details of the site visitor or member
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/members/members/introduction) | [REST](https://dev.wix.com/docs/rest/crm/members-contacts/members/members/introduction))
     * making the booking.
     */
    contactDetails?: ContactDetails;
    /** Additional custom fields submitted with the booking form. */
    additionalFields?: CustomFormField[];
    /**
     * Booking status. A booking is automatically confirmed if the service allows it
     * and an eCommerce order is created. It is automatically declined if there is a
     * double booking and the customer hasn't paid or is eligible for an automatic
     * refund. Currently, only payments with pricing plans are automatically refundable.
     */
    status?: BookingStatusWithLiterals;
    /**
     * The payment status of the booking. This field automatically syncs with the
     * `paymentStatus` of the corresponding eCommerce order
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup)| [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))
     * when customers use Wix eCommerce checkout.
     *
     * ## Integration patterns
     *
     * *When using Wix eCommerce checkout:** Wix Bookings automatically syncs the payment status based on the eCommerce order's payment status.
     * Do not manually update this field.
     *
     * *When using custom payment flows:** You can manually update the payment status with Confirm Booking or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) to reflect the customer's payment state.
     *
     * *For membership/pricing plan payments:** Wix Bookings automatically manages the payment status when customers pay with an active pricing plan ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/introduction)) subscription.
     *
     * All payment statuses are supported for every booking `status`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
    /**
     * Payment option selected by the customer. If the customer hasn't completed their checkout, they may still change the payment method. Must be one of the payment options offered by the service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)), unless `skipSelectedPaymentOptionValidation` is `true`.
     *
     * When undefined during an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)), Wix Bookings uses the service's default payment option
     */
    selectedPaymentOption?: SelectedPaymentOptionWithLiterals;
    /**
     * Date and time the booking was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /** External user ID that you can provide. */
    externalUserId?: string | null;
    /**
     * Revision number to be used when updating, rescheduling, or cancelling the booking.
     * Increments by 1 each time the booking is updated, rescheduled, or canceled. To prevent conflicting changes, the current revision must be specified when updating the booking.
     */
    revision?: string | null;
    /**
     * ID of the creator of the booking.
     * If `appId` and another ID are present, the other ID takes precedence.
     * @readonly
     */
    createdBy?: CommonIdentificationData;
    /**
     * The start date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the start date of the slot. For a schedule, this is the start date of the first session.
     * @readonly
     */
    startDate?: Date | null;
    /**
     * The end date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the end date of the slot. For a schedule, this is the end date of the last session.
     * @readonly
     */
    endDate?: Date | null;
    /**
     * Date and time the booking was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Custom field data for this object.
     * Extended fields must be configured in the app dashboard before they can be accessed with API calls.
     */
    extendedFields?: ExtendedFields;
    /**
     * Whether this booking overlaps with another confirmed booking. Returned
     * only if set to `true`.
     * @readonly
     */
    doubleBooked?: boolean | null;
}
/** @oneof */
interface BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
}
/**
 * A multi-service booking is considered available if all single-service bookings are available as returned from List Multi Service Availability Time Slots.
 * Currently, `SEPARATE_BOOKINGS` and `PARALLEL_BOOKINGS` are not supported.
 * Multi-service booking is available if each of its bookings is available separately.
 * For `SEQUENTIAL_BOOKINGS`, see `List Multi Service Availability Time Slots` documentation.
 */
declare enum MultiServiceBookingType {
    /** You must schedule single-service bookings back-to-back with each booking starting when the previous booking ends */
    SEQUENTIAL_BOOKINGS = "SEQUENTIAL_BOOKINGS",
    /** Not currently supported. */
    SEPARATE_BOOKINGS = "SEPARATE_BOOKINGS",
    /** Not currently supported. */
    PARALLEL_BOOKINGS = "PARALLEL_BOOKINGS"
}
/** @enumType */
type MultiServiceBookingTypeWithLiterals = MultiServiceBookingType | 'SEQUENTIAL_BOOKINGS' | 'SEPARATE_BOOKINGS' | 'PARALLEL_BOOKINGS';
interface BookedEntity extends BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
    /**
     * Session title at the time of booking. If there is no pre-existing session,
     * for example for appointment-based services, Wix Bookings sets `title` to the service name.
     * @readonly
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * List of tags for the booking.
     *
     * - "INDIVIDUAL": For bookings of appointment-based services. Including when the appointment is for a group of participants.
     * - "GROUP": For bookings of individual class sessions.
     * - "COURSE": For course bookings.
     */
    tags?: string[] | null;
}
/** @oneof */
interface BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
}
interface BookedSlot {
    /** Session ID. */
    sessionId?: string | null;
    /** Service ID. */
    serviceId?: string;
    /** Schedule ID. */
    scheduleId?: string;
    /**
     * ID of the corresponding event
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).
     * Available for both appointment and class bookings, not available for course bookings.
     * For appointment-based services, Wix Bookings automatically populates `eventId` when the booking `status` changes to `CONFIRMED`.
     * For class bookings, Wix Bookings automatically populates `eventId` upon booking creation.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
    /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    startDate?: string | null;
    /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    endDate?: string | null;
    /** The time zone according to which the slot was shown to the user when booking, and should be shown in the future. */
    timezone?: string | null;
    /**
     * Primary resource
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) for the booking.
     * For example, the staff member ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) providing the service.
     */
    resource?: BookedResource;
    /** Location where the session takes place. */
    location?: Location;
}
interface BookedResource {
    /**
     * ID of the booking's primary resource.
     * @format GUID
     */
    _id?: string;
    /**
     * Resource's name at the time of booking.
     * @maxLength 40
     */
    name?: string | null;
    /**
     * Resource's email at the time of booking.
     * @maxLength 500
     */
    email?: string | null;
    /**
     * ID of the schedule belonging to the booking's primary resource.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface Location {
    /**
     * Business location ID. Available only for locations that are business locations,
     * meaning the `location_type` is `"OWNER_BUSINESS"`.
     * @format GUID
     */
    _id?: string | null;
    /** Location name. */
    name?: string | null;
    /** The full address of this location. */
    formattedAddress?: string | null;
    /**
     * The full translated address of this location.
     * @maxLength 512
     */
    formattedAddressTranslated?: string | null;
    /**
     * Location type.
     *
     * - `"OWNER_BUSINESS"`: The business address, as set in the site’s general settings.
     * - `"OWNER_CUSTOM"`: The address as set when creating the service.
     * - `"CUSTOM"`: The address as set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface BookedSchedule {
    /** Schedule ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)). */
    scheduleId?: string;
    /**
     * Booked service ID.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Location ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)) where the schedule's sessions take place.
     * @readonly
     */
    location?: Location;
    /**
     * Time zone in which the slot or session was shown to the customer when they booked.
     * Also used whenever the customer reviews the booking's timing in the future.
     */
    timezone?: string | null;
    /**
     * Start time of the first session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    firstSessionStart?: string | null;
    /**
     * End time of the last session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    lastSessionEnd?: string | null;
}
interface ContactDetails {
    /**
     * Contact ID.
     * @format GUID
     */
    contactId?: string | null;
    /**
     * Contact's first name. When populated from a standard booking form, this
     * property corresponds to the `name` field.
     */
    firstName?: string | null;
    /** Contact's last name. */
    lastName?: string | null;
    /**
     * Contact's email. If no [contact](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/contacts/contact-v4/contact-object)
     * with this email exist, a new contact is created.
     * Used to validate coupon usage limitations per contact. If not specified,
     * the coupon usage limitation will not be enforced. (Coupon usage limitation
     * validation is not supported yet).
     * @format EMAIL
     */
    email?: string | null;
    /** Contact's phone number. */
    phone?: string | null;
    /** Contact's full address. */
    fullAddress?: Address;
    /**
     * Contact's time zone.
     * @deprecated
     */
    timeZone?: string | null;
    /**
     * Contact's country in [ISO 3166-1 alpha-2 code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
     * format.
     * @format COUNTRY
     */
    countryCode?: string | null;
}
/** Physical address */
interface Address extends AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
    /**
     * Country code.
     * @format COUNTRY
     */
    country?: string | null;
    /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /** Zip/postal code. */
    postalCode?: string | null;
    /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */
    addressLine2?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Free text to help find the address. */
    hint?: string | null;
    /** Coordinates of the physical address. */
    geocode?: AddressLocation;
    /** Country full name. */
    countryFullname?: string | null;
    /** Multi-level subdivisions from top to bottom. */
    subdivisions?: Subdivision[];
}
/** @oneof */
interface AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
}
interface StreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
interface AddressLocation {
    /** Address latitude. */
    latitude?: number | null;
    /** Address longitude. */
    longitude?: number | null;
}
interface Subdivision {
    /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    code?: string;
    /** Subdivision full name. */
    name?: string;
}
interface CustomFormField {
    /**
     * ID of the form field as defined in the form.
     * @format GUID
     */
    _id?: string;
    /** Value that was submitted for this field. */
    value?: string | null;
    /**
     * Form field's label at the time of submission.
     * @readonly
     */
    label?: string | null;
    valueType?: ValueTypeWithLiterals;
}
declare enum ValueType {
    /** Short text. This is the default value type. */
    SHORT_TEXT = "SHORT_TEXT",
    /** Long text. */
    LONG_TEXT = "LONG_TEXT",
    /** A text that represents the check box value. If selected the value is `true`, otherwise `false`. */
    CHECK_BOX = "CHECK_BOX"
}
/** @enumType */
type ValueTypeWithLiterals = ValueType | 'SHORT_TEXT' | 'LONG_TEXT' | 'CHECK_BOX';
/** Booking status. */
declare enum BookingStatus {
    /** The booking was created, but the customer hasn't completed the related eCommerce order yet. */
    CREATED = "CREATED",
    /**
     * The merchant has confirmed the booking and it appears in the business calendar. Merchants can set up their services
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) to automatically confirm all `PENDING` bookings.
     */
    CONFIRMED = "CONFIRMED",
    /**
     * The customer has canceled the booking. Depending on the relevant service's policy snapshot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
     * they may have to pay a cancellation fee ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction)).
     */
    CANCELED = "CANCELED",
    /** The merchant must manually confirm the booking before it appears in the business calendar. */
    PENDING = "PENDING",
    /** The merchant has declined the booking before the customer was charged. */
    DECLINED = "DECLINED",
    /**
     * The booking is on a waitlist.
     * Currently, you can't call Register to Waitlist ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/waitlist/register-to-waitlist)) for course or appointment bookings, even though this is supported in live sites.
     * You can call Register to Waitlist only for class session bookings.
     */
    WAITING_LIST = "WAITING_LIST"
}
/** @enumType */
type BookingStatusWithLiterals = BookingStatus | 'CREATED' | 'CONFIRMED' | 'CANCELED' | 'PENDING' | 'DECLINED' | 'WAITING_LIST';
/**
 * Payment status.
 * Automatically updated when using eCommerce checkout APIs.
 */
declare enum PaymentStatus {
    /** Undefined payment status. */
    UNDEFINED = "UNDEFINED",
    /** The booking isn't paid. */
    NOT_PAID = "NOT_PAID",
    /** The booking is fully paid. */
    PAID = "PAID",
    /** The booking is partially paid. */
    PARTIALLY_PAID = "PARTIALLY_PAID",
    /** The booking is refunded. */
    REFUNDED = "REFUNDED",
    /** The booking is free of charge. */
    EXEMPT = "EXEMPT"
}
/** @enumType */
type PaymentStatusWithLiterals = PaymentStatus | 'UNDEFINED' | 'NOT_PAID' | 'PAID' | 'PARTIALLY_PAID' | 'REFUNDED' | 'EXEMPT';
/**
 * Selected payment option.
 *
 * One of the payment options offered by the service.
 * This field is set when the user selects an option during booking.
 * If left undefined, the payment option is resolved by the service configuration on checkout.
 */
declare enum SelectedPaymentOption {
    /** Undefined payment option. */
    UNDEFINED = "UNDEFINED",
    /** Offline payment. */
    OFFLINE = "OFFLINE",
    /** Online payment. */
    ONLINE = "ONLINE",
    /** Payment using a Wix Pricing Plan. */
    MEMBERSHIP = "MEMBERSHIP",
    /**
     * Customers can pay only in person with a Wix Pricing Plan, while the Wix user
     * must manually redeem the pricing plan in the dashboard.
     */
    MEMBERSHIP_OFFLINE = "MEMBERSHIP_OFFLINE"
}
/** @enumType */
type SelectedPaymentOptionWithLiterals = SelectedPaymentOption | 'UNDEFINED' | 'OFFLINE' | 'ONLINE' | 'MEMBERSHIP' | 'MEMBERSHIP_OFFLINE';
interface BookingSource {
    /** Platform from which a booking was created. */
    platform?: PlatformWithLiterals;
    /** Actor that created this booking. */
    actor?: ActorWithLiterals;
    /**
     * Wix site ID of the application that created the booking.
     * @format GUID
     * @readonly
     */
    appDefId?: string | null;
    /**
     * Name of the application that created the booking, as saved in Wix Developers Center at the time of booking.
     * @readonly
     */
    appName?: string | null;
}
declare enum Platform {
    UNDEFINED_PLATFORM = "UNDEFINED_PLATFORM",
    WEB = "WEB",
    MOBILE_APP = "MOBILE_APP"
}
/** @enumType */
type PlatformWithLiterals = Platform | 'UNDEFINED_PLATFORM' | 'WEB' | 'MOBILE_APP';
declare enum Actor {
    UNDEFINED_ACTOR = "UNDEFINED_ACTOR",
    BUSINESS = "BUSINESS",
    CUSTOMER = "CUSTOMER"
}
/** @enumType */
type ActorWithLiterals = Actor | 'UNDEFINED_ACTOR' | 'BUSINESS' | 'CUSTOMER';
interface ParticipantNotification {
    /**
     * Whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean;
    /** Custom message to send to the participants about the changes to the booking. */
    message?: string | null;
}
interface CommonIdentificationData extends CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /**
     * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.
     * @format GUID
     */
    contactId?: string | null;
}
/** @oneof */
interface CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum IdentificationDataIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type IdentificationDataIdentityTypeWithLiterals = IdentificationDataIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/**
 * Settings that control booking flow behavior and override default business rules.
 *
 * These settings allow administrators to bypass standard validation checks
 * and policies when creating, confirming, rescheduling, or canceling bookings.
 * Most settings require elevated permissions to use.
 *
 * Use flow control settings to handle special scenarios like:
 * - Emergency bookings outside normal business hours
 * - Admin-initiated bookings that bypass availability checks
 * - Custom payment flows that don't use standard eCommerce checkout
 * - Overriding cancellation or rescheduling policies in exceptional cases
 */
interface FlowControlSettings {
    /** Whether availability is checked when creating or confirming the booking. */
    skipAvailabilityValidation?: boolean;
    /**
     * Whether the booking's `status` is automatically updated to `CONFIRMED` when
     * the customer completes the eCommerce checkout
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),
     * regardless of whether the relevant service requires manual business confirmation.
     */
    skipBusinessConfirmation?: boolean;
    /**
     * Whether the customer is allowed to pay with a payment method that isn't
     * supported for the relevant service.
     */
    skipSelectedPaymentOptionValidation?: boolean;
    /**
     * Whether the customer receives an automatic refund if there's a double booking
     * conflict. Only available if the customer has paid with a
     * pricing plan.
     */
    withRefund?: boolean | null;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface ParticipantChoices {
    /**
     * Information about the booked service choices. Includes the number of participants.
     * @minSize 1
     * @maxSize 20
     */
    serviceChoices?: ServiceChoices[];
}
interface ServiceChoices {
    /**
     * Number of participants for this variant ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    numberOfParticipants?: number | null;
    /**
     * Service choices for these participants.
     * @maxSize 5
     */
    choices?: ServiceChoice[];
}
interface ServiceChoice extends ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
    /**
     * ID of the corresponding option for the choice. For example, the choice `child`
     * could correspond to the option `ageGroup`. In this case, `optionId` is the ID
     * for the `ageGroup` option.
     * @format GUID
     */
    optionId?: string;
}
/** @oneof */
interface ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
}
interface Duration {
    /**
     * Duration of the service in minutes.
     * Min: 1 minute, Max: 30 days, 23 hours, and 59 minutes
     * @min 1
     * @max 44639
     */
    minutes?: number;
    /**
     * Name of the duration option.
     * Defaults to the formatted duration e.g. "1 hour, 30 minutes".
     * @maxLength 255
     */
    name?: string | null;
}
interface MultiServiceBookingInfo {
    /**
     * Multi-service booking ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /** Type of the multi-service booking. */
    type?: MultiServiceBookingTypeWithLiterals;
}
interface BookedAddOn {
    /**
     * The ID of the add-on.
     * @format GUID
     */
    _id?: string;
    /**
     * The ID of the add-on group.
     * @format GUID
     */
    groupId?: string;
    /**
     * The add-on duration in minutes at the time of booking.
     * @min 1
     * @max 1440
     * @readonly
     */
    durationInMinutes?: number | null;
    /**
     * The quantity of booked add-ons.
     * @min 1
     * @max 1000
     */
    quantity?: number | null;
    /**
     * Add-on `name` at the time of booking.
     * @maxLength 100
     * @readonly
     */
    name?: string | null;
    /**
     * Add-on name translated to the language the customer used during booking.
     * @maxLength 100
     * @readonly
     */
    nameTranslated?: string | null;
}
interface BookingFormFilled {
    /** The booking object that form was filled for. */
    booking?: Booking;
    /**
     * The submitted form data, where key is the form field and value is the data submitted for the given field.
     * See the [form submission object](https://dev.wix.com/docs/rest/crm/forms/form-submissions/submission-object)
     * for more details.
     */
    formSubmission?: Record<string, any> | null;
    /**
     * ID of the [form](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)
     * that was filled.
     * @format GUID
     */
    formId?: string | null;
}
interface SetBookingFormAndSubmissionIdRequest extends SetBookingFormAndSubmissionIdRequestCreatedByOneOf {
    /**
     * The visitor who created the booking.
     * @format GUID
     */
    visitorId?: string | null;
    /**
     * The member that created the booking.
     * @format GUID
     */
    memberId?: string | null;
    /**
     * The app that created the booking.
     * @format GUID
     */
    appId?: string | null;
    /**
     * ID of the booking to set `formId` and `submissionId` for.
     * @format GUID
     */
    bookingId?: string | null;
    /**
     * ID of the form to set on the booking.
     * @format GUID
     */
    formId?: string | null;
    /**
     * ID of the form submission to set on the booking.
     * @format GUID
     */
    submissionId?: string | null;
    /**
     * MetaSite ID
     * @format GUID
     */
    msid?: string | null;
    /**
     * Instance ID
     * @format GUID
     */
    instanceId?: string | null;
}
/** @oneof */
interface SetBookingFormAndSubmissionIdRequestCreatedByOneOf {
    /**
     * The visitor who created the booking.
     * @format GUID
     */
    visitorId?: string | null;
    /**
     * The member that created the booking.
     * @format GUID
     */
    memberId?: string | null;
    /**
     * The app that created the booking.
     * @format GUID
     */
    appId?: string | null;
}
interface SetBookingFormAndSubmissionIdResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: WebhooksIdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface WebhooksIdentificationData extends WebhooksIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface WebhooksIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
interface V2CreateBookingRequest extends V2CreateBookingRequestBookableItemOneOf, V2CreateBookingRequestParticipantsInfoOneOf {
    /**
     * Information about the slot to create a booking for.
     * If you set `slot.location.locationType` to `CUSTOM`, the created slot's
     * location is set to `slot.location.formattedAddress` when provided.
     * Otherwise it's set to `contactDetails.fullAddress.formattedAddress`.
     */
    slot?: Slot;
    /** Information about the schedule to create a booking for. */
    schedule?: BookedSchedule;
    /** Contact details of the customer booking the service. */
    contactDetails?: ContactDetails;
    /**
     * Booking status.
     * One of:
     * - `"CREATED"` - The booking was created.
     * - `"UPDATED"` - The booking was updated.
     * - `"CONFIRMED"` - The booking has been confirmed and appears on the bookings calendar.
     * Booking can be manually confirmed using the Set As Confirmed endpoint.
     * Booking can be automatically confirmed when the following requirements are met:
     * + The service is configured as automatically confirmed.
     * + Invoking eCommerce checkout API and an order has been created.
     * - `"CANCELED"` - The booking has been canceled and synced to bookings calendar.
     * The booking can be canceled using cancel API.
     * - `"PENDING"` - The booking waiting to be confirmed or declined buy the owner and is synced to bookings calendar.
     * Bookings can be manually set as pending using setAsPending API, requires manage booking status permissions.
     * Booking can be automatically set as pending when the following requirements are met:
     * + The Service is configured as manually confirmed.
     * + Invoking the eCommerce checkout API and an order has been created.
     * - `"WAITING_LIST"` - The booking is pending on a waiting list.
     * Booking can be created with this status when invoking waiting list join API.
     * - `"DECLINED"` - The booking was declined by the owner and synced to bookings calendar.
     * Booking can be manually declined using decline API and requires manage booking permissions.
     * Booking can be automatically declined when the following requirements are met:
     * + Invoking eCommerce checkout API and the order declined event has been sent.
     * + Invoking eCommerce checkout API and order approved event has been sent, but the booking is offline and the booking causes a double booking.
     */
    status?: BookingStatusWithLiterals;
    /**
     * Additional custom fields of the booking form. The customer must provide
     * information for each field when booking the service. For example, that they
     * bring their own towels or whether they use a wheelchair.
     *
     * Max: 100 fields
     * @maxSize 100
     */
    additionalFields?: CustomFormField[];
    /**
     * Total number of participants. Available only when the service doesn't have
     * [variants](https://dev.wix.com/api/rest/wix-bookings/service-options-and-variants/introduction).
     *
     * Max: `20`
     */
    numberOfParticipants?: number | null;
    /**
     * Internal business note. Not visible to the customer.
     *
     * Max: 200 characters
     */
    internalBusinessNote?: string | null;
    /**
     * Payment option the customer intends to use.
     * Must be one of the payment options defined for the service, unless
     * you pass `flowControlSettings.skipSelectedPaymentOptionValidation` as `true`.
     */
    selectedPaymentOption?: SelectedPaymentOptionWithLiterals;
    /**
     * Identifies the source (platform, actor and app) that created this booking.
     * This property of the booking cannot be changed.
     * The app_def_id and app_name will be resolved automatically.
     * TODO GAP See if we need this - might be able to get this data from the headers?
     */
    bookingSource?: BookingSource;
    /**
     * A user identifier of an external application user that initiated the book request.
     * Allows an external application to later identify its own bookings and correlate to its own internal users
     */
    externalUserId?: string | null;
    /** Information about a message to send to the customer. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     *
     * Default: `true`.
     */
    sendSmsReminder?: boolean | null;
    /**
     * Information about whether specific procedures of the standard Wix Bookings
     * creation flow are changed. For example, whether the availability is
     * checked before creating the booking or if additional payment options are
     * accepted.
     */
    flowControlSettings?: CreateBookingRequestFlowControlSettings;
}
/** @oneof */
interface V2CreateBookingRequestBookableItemOneOf {
    /**
     * Information about the slot to create a booking for.
     * If you set `slot.location.locationType` to `CUSTOM`, the created slot's
     * location is set to `slot.location.formattedAddress` when provided.
     * Otherwise it's set to `contactDetails.fullAddress.formattedAddress`.
     */
    slot?: Slot;
    /** Information about the schedule to create a booking for. */
    schedule?: BookedSchedule;
}
/** @oneof */
interface V2CreateBookingRequestParticipantsInfoOneOf {
}
interface Slot {
    /**
     * ID for the slot's corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     *
     * Deprecated. Please use `eventId` instead.
     * @deprecated ID for the slot's corresponding session, when the session is either a single session
     * or a specific session generated from a recurring session.
     *
     * Deprecated. Please use `eventId` instead.
     * @replacedBy event_id
     * @targetRemovalDate 2025-09-30
     */
    sessionId?: string | null;
    /** Service ID. */
    serviceId?: string;
    /** Schedule ID. */
    scheduleId?: string;
    /**
     * The start time of this slot in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339)
     * format.
     *
     * If `timezone` is specified,
     * dates are based on the local date/time. This means that the timezone offset
     * in the `start_date` is ignored.
     */
    startDate?: string | null;
    /**
     * The end time of this slot in
     * [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339) format.
     *
     * If `timezone` is specified,
     * dates are based on the local date/time. This means that the timezone offset
     * in the `end_date` is ignored.
     */
    endDate?: string | null;
    /**
     * The timezone for which slot availability is to be calculated.
     *
     * Learn more about [handling Daylight Savings Time (DST) for local time zones](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_handling-daylight-savings-time-dst-for-local-time-zones)
     * when calculating availability.
     */
    timezone?: string | null;
    /**
     * The resource required for this slot. Currently, the only supported resource
     * is the relevant staff member for the slot.
     */
    resource?: SlotResource;
    /** Geographic location of the slot. */
    location?: Location;
    /**
     * ID for the slot's corresponding event, when the event is either a single event
     * or a specific event generated from a recurring event.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
}
interface SlotResource {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Resource name. Read only.
     * @maxLength 1200
     */
    name?: string | null;
}
interface CreateBookingRequestFlowControlSettings {
    /**
     * Whether the availability is checked before creating the booking. When
     * passing `false` a booking is only created when the slot or schedule is
     * available. Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY`
     * permission scope when passing `true`.
     *
     * Default: `false`.
     */
    skipAvailabilityValidation?: boolean;
    /**
     * Whether `PENDING` bookings are automatically set to `CONFIRMED` for
     * services that normally require the owner's manual confirmation. Your
     * app must have the `BOOKINGS.OVERRIDE_AVAILABILITY` permission scope
     * when passing `true`.
     *
     * Default: `false`.
     */
    skipBusinessConfirmation?: boolean;
    /**
     * Whether customers can pay using a payment method that isn't supported
     * for the service, but that's supported for other services. Your app
     * must have the `BOOKINGS.MANAGE_PAYMENTS` permission scope when passing
     * `true`.
     *
     * Default: `false`.
     */
    skipSelectedPaymentOptionValidation?: boolean;
}
interface V2CreateBookingResponse {
    /** Created booking. */
    booking?: Booking;
}
interface V2CancelBookingRequest {
    /**
     * ID of the booking to cancel.
     * @format GUID
     */
    bookingId?: string;
    /**
     * Information about whether to notify the customer about the cancellation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when managing the booking.
     */
    revision?: string | null;
}
interface CancelBookingRequestFlowControlSettings {
    /**
     * Whether the cancellation policy applies when canceling the booking. When
     * passing `false` you can only cancel a booking if the cancellation policy
     * allows it. Your app must have the `BOOKINGS.IGNORE_BOOKING_POLICY `
     * permission scope when passing `true`.
     *
     * Default: `false`.
     */
    ignoreCancellationPolicy?: boolean;
    /**
     * Whether to issue a refund when canceling the booking.
     * The refund will be issued only if the booking is refundable.
     * Currently, booking is considered refundable when it was paid by membership.
     * If passing `true`, the booking flow control settings will be set with refund,
     * otherwise, either if `false` is passed or the field remains empty,
     * the booking flow control settings will be set with no refund.
     *
     * Default: `false`.
     */
    withRefund?: boolean | null;
}
interface V2CancelBookingResponse {
    /** Canceled booking. */
    booking?: Booking;
}
interface V2RescheduleBookingRequest extends V2RescheduleBookingRequestParticipantsInfoOneOf {
    /**
     * Id of the booking to reschedule.
     * @format GUID
     */
    bookingId?: string;
    /** Information about the new slot. */
    slot?: Slot;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be passed when
     * managing the booking.
     */
    revision?: string | null;
    /**
     * Information about whether to notify the customer about the rescheduling and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
}
/** @oneof */
interface V2RescheduleBookingRequestParticipantsInfoOneOf {
}
interface RescheduleBookingRequestFlowControlSettings {
    /**
     * Whether the rescheduling policy applies when rescheduling the booking.
     * When passing `false` you can only cancel a booking if the rescheduling
     * policy allows it. Your app must have the `BOOKINGS.IGNORE_BOOKING_POLICY `
     * permission scope when passing `true`.
     *
     * Default: `false`.
     */
    ignoreReschedulePolicy?: boolean;
    /**
     * Whether the availability is checked before rescheduling the booking.
     * When passing `false` a booking is only created when the slot or
     * schedule is available. Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY`
     * permission scope when passing `true`.
     *
     * Default: `false`.
     */
    skipAvailabilityValidation?: boolean;
    /**
     * Whether the rescheduled booking's status is automatically set to
     * `CONFIRMED` for services that normally require the owner's manual
     * confirmation. Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY`
     * permission scope when passing `true`.
     *
     * Default: `false`.
     */
    skipBusinessConfirmation?: boolean;
}
interface V2RescheduleBookingResponse {
    /** Rescheduled booking. */
    booking?: Booking;
}
interface V2ConfirmBookingRequest {
    /**
     * ID of the booking to confirm.
     * @format GUID
     */
    bookingId?: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be passed when
     * managing the booking.
     */
    revision?: string | null;
    /**
     * Information about whether to notify the customer about the confirmation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
}
interface V2ConfirmBookingResponse {
    booking?: Booking;
}
interface V2DeclineBookingRequest {
    /**
     * ID of the booking to decline.
     * @format GUID
     */
    bookingId?: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be passed when
     * managing the booking.
     */
    revision?: string | null;
    /**
     * Information about whether to notify the customer about the decline and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
}
interface V2DeclineBookingResponse {
    /** Declined booking. */
    booking?: Booking;
}
interface V2UpdateNumberOfParticipantsRequest extends V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {
    /**
     * ID of the booking to update the number of participants for.
     * @format GUID
     */
    bookingId?: string;
    /** Updated number of participants. */
    numberOfParticipants?: number | null;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be passed when
     * managing the booking.
     */
    revision?: string | null;
}
/** @oneof */
interface V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {
}
interface V2UpdateNumberOfParticipantsResponse {
    /** Booking with updated number of participants. */
    booking?: Booking;
}
interface ConfirmOrDeclineBookingRequest {
    /**
     * ID of the booking to confirm or decline.
     * @format GUID
     */
    bookingId: string;
    /**
     * Current payment status of the booking when using a custom checkout page and
     * not the *eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     *
     * The booking is declined if there is a double booking conflict and you provide
     * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
}
interface ConfirmOrDeclineBookingResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface BulkConfirmOrDeclineBookingRequest {
    /**
     * Bookings to confirm or decline.
     * @minSize 1
     * @maxSize 300
     */
    details: BulkConfirmOrDeclineBookingRequestBookingDetails[];
    /** Whether to return the confirmed or declined booking objects. */
    returnEntity?: boolean;
}
interface BulkConfirmOrDeclineBookingRequestBookingDetails {
    /**
     * ID of the booking to confirm or decline.
     * @format GUID
     */
    bookingId?: string;
    /**
     * Current payment status of the booking when using a custom checkout page and
     * not the *eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     *
     * The booking is declined if there is a double booking conflict and you provide
     * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
}
interface BulkConfirmOrDeclineBookingResponse {
    /** List of confirmed or declined bookings, including metadata. */
    results?: BulkBookingResult[];
    /** Total successes and failures of the Bulk Confirm Or Decline call. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkBookingResult {
    /**
     * Information about the booking that was created or updated.
     * Including its ID, index in the bulk request and whether it was
     * successfully created or updated.
     */
    itemMetadata?: ItemMetadata;
    /**
     * Created or updated booking. Available only if you requested
     * to return the booking entity.
     */
    item?: Booking;
}
interface ItemMetadata {
    /** Item ID. Should always be available, unless it's impossible (for example, when failing to create an item). */
    _id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface BookingChanged {
    /** The booking before the changes. */
    previousBooking?: Booking;
    /** The booking after the changes. */
    currentBooking?: Booking;
}
interface CreateBookingRequest {
    /** The booking to create. */
    booking: Booking;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     * Default: `true`.
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when creating a booking.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
}
interface CreateBookingFlowControlSettings {
    /**
     * Whether the availability is checked before creating the booking.
     *
     * - `false`: A booking is only created when the slot or schedule is available.
     * - `true`: The booking is created regardless of availability conflicts. Make sure the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has the required permissions.
     *
     * *Use cases for `true`:**
     * - Emergency or priority bookings that must be accommodated.
     * - Administrative bookings that override normal availability rules.
     * - Testing or demonstration purposes.
     *
     * Default: `false`.
     */
    skipAvailabilityValidation?: boolean;
    /**
     * Whether `PENDING` bookings are automatically set to `CONFIRMED` for
     * services that normally require the owner's manual confirmation.
     *
     * Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY` permission
     * when passing `true`.
     * Default: `false`.
     */
    skipBusinessConfirmation?: boolean;
    /**
     * Whether customers can pay using a payment method that isn't supported
     * for the service, but that's supported for other services.
     *
     * Your app must have the `BOOKINGS.MANAGE_PAYMENTS` permission when passing
     * `true`.
     * Default: `false`.
     */
    skipSelectedPaymentOptionValidation?: boolean;
}
interface CreateBookingResponse {
    /** Created booking. */
    booking?: Booking;
}
/**
 * The `fieldMask` should not include both the `numberOfParticipants` and `participantsInfo` paths. Including both results
 * in an error. `participantsInfo` is preferred over `numberOfParticipants`.
 */
interface UpdateBookingRequest {
    booking?: Booking;
}
interface UpdateBookingResponse {
    booking?: Booking;
}
interface LegacyCreateBookingRequest {
    booking?: Booking;
}
interface LegacyCreateBookingResponse {
    booking?: Booking;
}
/**
 * The `fieldMask` for each booking should not include both the `numberOfParticipants` and `participantsInfo` paths. Including both results
 * in an error. `participantsInfo` is preferred over `numberOfParticipants`.
 */
interface BulkUpdateBookingRequest {
    bookings?: MaskedBooking[];
}
interface MaskedBooking {
    booking?: Booking;
    mask?: string[];
}
interface BulkUpdateBookingResponse {
    /**
     * Information about the booking that was updated.
     * Including its ID, index in the bulk request and whether it was
     * successfully updated.
     */
    results?: ItemMetadata[];
    /** Total number of successes and failures for Bulk Update Bookings. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkCreateBookingRequest {
    /**
     * Bookings to create.
     *
     * Max: 8 bookings
     * @minSize 1
     * @maxSize 8
     */
    createBookingsInfo: CreateBookingInfo[];
    /** Whether to return the created bookings. */
    returnFullEntity?: boolean;
}
interface CreateBookingInfo {
    /** Booking to create. */
    booking?: Booking;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     * Default: `true`.
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when creating a booking.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
}
interface BulkCreateBookingResponse {
    /** List of individual Bulk Create Bookings results. */
    results?: BulkBookingResult[];
    /** Total number of successes and failures for Bulk Create Bookings. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface RescheduleBookingRequest extends RescheduleBookingRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Specify when not all
     * participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
    /**
     * ID of the booking to reschedule.
     * @format GUID
     */
    bookingId: string;
    /** New slot of the booking. */
    slot: V2Slot;
    /**
     * Revision number, which increments by 1 each time the booking is rescheduled.
     * To prevent conflicting changes, the current revision must be passed when
     * rescheduling the booking.
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer about the rescheduling and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when rescheduling a booking.
     */
    flowControlSettings?: RescheduleBookingFlowControlSettings;
}
/** @oneof */
interface RescheduleBookingRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Specify when not all
     * participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
interface V2Slot {
    /** Identifier for the underlying session when the session is a single session or generated from a recurring session. */
    sessionId?: string | null;
    /** Service identifier. Required. */
    serviceId?: string;
    /** Schedule identifier. Required. */
    scheduleId?: string;
    /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    startDate?: string | null;
    /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    endDate?: string | null;
    /** The timezone according to which the slot is calculated and presented. */
    timezone?: string | null;
    /**
     * The resource required for this slot.
     * When populated, the specified resource will be assigned to the slot upon confirmation according to its availability.
     * When empty, if `skip_availability_validation` is `false`, a random available resource will be assigned to the slot upon confirmation.
     * Otherwise, one of the service resources will be assigned to the slot randomly upon confirmation.
     */
    resource?: SlotSlotResource;
    /** Geographic location of the slot. */
    location?: SlotLocation;
    /**
     * Calendar event ID - not supported.
     * If not empty, on all write flows (create/update), it takes priority over `sessionId`.
     * So if both `sessionId` and `eventId` are provided, the `sessionId` will be based on the `eventId`.
     * Otherwise, if `eventId` is empty on write flow,
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
}
declare enum LocationLocationType {
    /** Undefined location type. */
    UNDEFINED = "UNDEFINED",
    /** The business address as set in the site’s general settings. */
    OWNER_BUSINESS = "OWNER_BUSINESS",
    /** The address set when creating the service. */
    OWNER_CUSTOM = "OWNER_CUSTOM",
    /** The address set for the individual session. */
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationLocationTypeWithLiterals = LocationLocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface SlotSlotResource {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Resource name.
     * @maxLength 1200
     */
    name?: string | null;
}
interface SlotLocation {
    /**
     * Business Location ID. Present if the location is a business location.
     * @format GUID
     */
    _id?: string | null;
    /** Location name. */
    name?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Location type. */
    locationType?: LocationLocationTypeWithLiterals;
}
interface RescheduleBookingFlowControlSettings {
    /**
     * Whether the rescheduling policy applies when rescheduling the booking.
     *
     * When passing `false`, you can only cancel a booking if the rescheduling
     * policy allows it.
     * Default: `false`.
     */
    ignoreReschedulePolicy?: boolean;
    /**
     * Whether the availability is checked before rescheduling the booking.
     *
     * When passing `false`, a booking is only created when the slot or
     * schedule is available.
     * Default: `false`.
     */
    skipAvailabilityValidation?: boolean;
    /**
     * Whether the rescheduled booking's status is automatically set to
     * `CONFIRMED` for services that normally require the owner's manual
     * confirmation.
     * Default: `false`.
     */
    skipBusinessConfirmation?: boolean;
}
interface RescheduleBookingResponse {
    /** Rescheduled booking. */
    booking?: Booking;
}
interface BookingRescheduled extends BookingRescheduledPreviousParticipantsInfoOneOf {
    /**
     * The previous total number of participants. Available only when the booking includes
     * a single service variant.
     */
    previousTotalParticipants?: number;
    /**
     * Information about the previous booked service choices and participants.
     * Available only when the booking includes multiple service variants.
     */
    previousParticipantsChoices?: ParticipantChoices;
    /** The rescheduled booking object. */
    booking?: Booking;
    /** Information about whether to notify the customer about the rescheduling and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings
     * rescheduling flow are changed. For example, whether the availability of
     * the new slot is checked before rescheduling the booking or if you can
     * reschedule the booking even though the rescheduling policy doesn't allow it.
     */
    flowControlSettings?: RescheduleBookingFlowControlSettings;
    /** ID of the rescheduling initiator. */
    initiatedBy?: IdentificationData;
    /** The previous status of the booking. */
    previousStatus?: BookingStatusWithLiterals;
    /** An object describing the previous slot or schedule of the booking. */
    previousBookedEntity?: BookedEntity;
    /**
     * The previous start date of the booking.
     * For a slot, this is the start date of the slot.
     * For a schedule, this is the start date of the first session.
     */
    previousStartDate?: Date | null;
    /**
     * The previous end date of the booking.
     * For a slot, this is the end date of the slot.
     * For a schedule, this is the end date of the last session.
     */
    previousEndDate?: Date | null;
}
/** @oneof */
interface BookingRescheduledPreviousParticipantsInfoOneOf {
    /**
     * The previous total number of participants. Available only when the booking includes
     * a single service variant.
     */
    previousTotalParticipants?: number;
    /**
     * Information about the previous booked service choices and participants.
     * Available only when the booking includes multiple service variants.
     */
    previousParticipantsChoices?: ParticipantChoices;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /**
     * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.
     * @format GUID
     * @readonly
     */
    contactId?: string | null;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum IdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type IdentityTypeWithLiterals = IdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
interface BulkRescheduleBookingRequest {
    /**
     * Reschedule multiple bookings to multiple slots.
     * @minSize 1
     */
    slotsBookings?: SlotBookings[];
    /**
     * Information about whether to notify the customer about the rescheduling and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
}
interface BulkRescheduleBookingRequestBooking {
    /**
     * ID of the booking to reschedule.
     * @format GUID
     */
    _id?: string;
    /**
     * Revision number, which increments by 1 each time the booking is rescheduled.
     * To prevent conflicting changes,
     * the current revision must be specified when rescheduling the booking.
     */
    revision?: string | null;
}
/** Bookings to be rescheduled to the given slot. */
interface SlotBookings {
    /**
     * The bookings details.
     * @minSize 1
     */
    bookings?: BulkRescheduleBookingRequestBooking[];
    /**
     * The slot to which the bookings were rescheduled.
     * These bookings are automatically assigned to the session, if given. Otherwise, a new session is created.
     */
    slot?: BookedSlot;
}
interface BulkRescheduleBookingResponse {
    /**
     * Information about the booking that was rescheduled.
     * Including its ID, index in the bulk request and whether it was
     * successfully rescheduled.
     */
    results?: ItemMetadata[];
    /** Total number of successes and failures for Bulk Reschedule Bookings. */
    bulkActionMetadata?: BulkActionMetadata;
}
/** Update the booked schedule of multiple bookings to the given schedule. */
interface BulkUpdateBookedScheduleRequest {
    /**
     * The bookings whose booked schedule is to be updated to the given schedule.
     * @minSize 1
     */
    bookings?: BookingDetails[];
    /** ID of the schedule to update. */
    scheduleId?: string;
    /**
     * Information about whether to notify the customer about the rescheduling and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
}
interface BookingDetails {
    /**
     * ID of the bookings to be updated.
     * @format GUID
     */
    _id?: string;
    revision?: string | null;
}
interface BulkUpdateBookedScheduleResponse {
    /**
     * Information about the schedule that was updated.
     * Including its ID, index in the bulk request and whether it was
     * succesfully updated.
     */
    results?: ItemMetadata[];
    /** Total number of successes and failures for Bulk Updated Booked Schedules. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface QueryBookingsRequest {
    /** Information about filters, paging, and sorting. */
    query?: QueryV2;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     */
    sort?: Sorting[];
    /** Array of projected fields. A list of specific field names to return. If `fieldsets` are also specified, the union of `fieldsets` and `fields` is returned. */
    fields?: string[];
    /** Array of named, predefined sets of projected fields. A array of predefined named sets of fields to be returned. Specifying multiple `fieldsets` will return the union of fields from all sets. If `fields` are also specified, the union of `fieldsets` and `fields` is returned. */
    fieldsets?: string[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /**
     * Cursor token pointing to a page of results. In the first request,
     * specify `cursorPaging.limit`. For following requests, specify the
     * retrieved `cursorPaging.cursor` token and not `query.filter` or
     * `query.sort`.
     */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryBookingsResponse {
    /** Retrieved bookings. */
    bookings?: Booking[];
    /** Paging metadata. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
    /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */
    cursors?: Cursors;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface ConfirmRequest {
    /**
     * ID of the booking to confirm.
     * @format GUID
     */
    _id?: string;
    /**
     * Information about whether to notify the customer about the confirmation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
}
interface ConfirmResponse {
    /** Confirmed booking. */
    booking?: Booking;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
}
interface ConfirmBookingRequest {
    /**
     * ID of the booking to confirm.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be passed when
     * updating the booking.
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer about the confirmation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when confirming a booking.
     */
    flowControlSettings?: ConfirmBookingFlowControlSettings;
}
interface ConfirmBookingFlowControlSettings {
    /**
     * Whether the availability is checked before confirming the booking.
     *
     * When specifying `false`, a booking is only updated with status `CONFIRMED`.
     * Default: `false`.
     */
    checkAvailabilityValidation?: boolean;
}
interface ConfirmBookingResponse {
    /** Confirmed booking. */
    booking?: Booking;
}
interface BookingConfirmed {
    /** The confirmed booking object. */
    booking?: Booking;
    /** Information about whether to notify the customer about the confirmation and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking overlaps with another existing confirmed booking. */
    doubleBooked?: boolean | null;
    /** ID of the confirmation initiator. */
    initiatedBy?: IdentificationData;
    /** The previous status of the booking. */
    previousStatus?: BookingStatusWithLiterals;
    /** The previous payment status of the booking. */
    previousPaymentStatus?: PaymentStatusWithLiterals;
}
interface ConsistentQueryBookingsRequest {
    /** Information about filters, paging, and sorting. */
    query?: QueryV2;
}
interface ConsistentQueryBookingsResponse {
    /** Retrieved bookings. */
    bookings?: Booking[];
    /** Paging metadata. */
    pagingMetadata?: PagingMetadataV2;
}
interface SetBookingSessionIdRequest {
    /**
     * ID of the booking to set `sessionId` for.
     * @format GUID
     */
    _id?: string;
    /** ID of the session to set on the booking. */
    sessionId?: string;
}
interface SetBookingSessionIdResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface SetBookingSubmissionIdRequest {
    /**
     * ID of the booking to set `submissionId` for.
     * @format GUID
     */
    bookingId: string;
    /**
     * ID of the form submission to set on the booking.
     * @format GUID
     */
    submissionId: string;
}
interface SetBookingSubmissionIdResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface UpdateExtendedFieldsRequest {
    /** ID of the entity to update. */
    _id: string;
    /** Identifier for the app whose extended fields are being updated. */
    namespace: string;
    /** Data to update. Structured according to the [schema](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields#json-schema-for-extended-fields) defined when the extended fields were configured. */
    namespaceData: Record<string, any> | null;
}
interface UpdateExtendedFieldsResponse {
    /**
     * Updated namespace.
     * @maxLength 164
     */
    namespace?: string;
    /** Updated data. */
    namespaceData?: Record<string, any> | null;
}
interface DeclineBookingRequest {
    /**
     * ID of the booking to decline.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * declining the booking.
     * @min 1
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to refund a declined booking.
     */
    flowControlSettings?: DeclineBookingFlowControlSettings;
}
interface DeclineBookingFlowControlSettings {
    /**
     * Whether to issue a refund when declining the booking.
     *
     * The refund will be issued only if the booking is refundable.
     * Currently, a booking is considered refundable when it was paid by membership.
     * If specifying `true`, the booking flow control settings will be set with a refund.
     * If you specify `false` or an empty field,
     * the booking flow control settings are set without refund.
     *
     * Default: `false`.
     */
    withRefund?: boolean | null;
}
interface DeclineBookingResponse {
    /** Declined booking. */
    booking?: Booking;
}
interface BookingDeclined {
    /** The declined booking object. */
    booking?: Booking;
    /** Information about whether to notify the customer about the decline and the message to send. */
    participantNotification?: ParticipantNotification;
    /** Whether this booking overlaps with another existing confirmed booking. */
    doubleBooked?: boolean | null;
    /** ID of the decline initiator. */
    initiatedBy?: IdentificationData;
    /** The previous status of the booking. */
    previousStatus?: BookingStatusWithLiterals;
    /** The previous payment status of the booking. */
    previousPaymentStatus?: PaymentStatusWithLiterals;
    /**
     * Information about whether specific procedures of the standard Wix Bookings
     * declining flow are changed. For example, whether to issue a refund.
     */
    flowControlSettings?: DeclineBookingFlowControlSettings;
}
interface CancelBookingRequest {
    /**
     * ID of the booking to cancel.
     * @format GUID
     */
    bookingId: string;
    /**
     * Information about whether to notify the customer about the cancellation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to allow a cancellation even though the service's
     * policy doesn't allow it.
     */
    flowControlSettings?: CancelBookingFlowControlSettings;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * managing the booking.
     */
    revision: string | null;
}
interface CancelBookingFlowControlSettings {
    /**
     * Whether the cancellation policy applies when canceling the booking.
     *
     * When passing `false`, you can only cancel a booking if the cancellation policy allows it.
     * Default: `false`.
     */
    ignoreCancellationPolicy?: boolean | null;
    /**
     * Whether to issue a refund when canceling the booking.
     *
     * The refund will be issued only if the booking is refundable.
     * Currently, a booking is considered refundable when it was paid by membership.
     * If you specify `true`, the booking flow control settings is set to include a refund.
     * If `false` is specified or the field remains empty,
     * the booking flow control settings are set without refund.
     *
     * Default: `false`.
     */
    withRefund?: boolean | null;
}
interface CancelBookingResponse {
    /** Canceled booking. */
    booking?: Booking;
}
interface BookingCanceled {
    /** The canceled booking object. */
    booking?: Booking;
    /** Information about whether to notify the customer about the cancellation and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings
     * cancellation flow are changed. For example, whether you can cancel
     * a booking even though the cancellation policy doesn't allow it or whether
     * to issue a refund.
     */
    flowControlSettings?: CancelBookingFlowControlSettings;
    /** ID of the cancellation initiator. */
    initiatedBy?: IdentificationData;
    /** The previous status of the booking. */
    previousStatus?: BookingStatusWithLiterals;
}
interface UpdateNumberOfParticipantsRequest extends UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices the participants have booked. Specify
     * when not all participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
    /**
     * ID of the booking to update the number of participants for.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified
     * when updating the booking.
     */
    revision: string | null;
}
/** @oneof */
interface UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices the participants have booked. Specify
     * when not all participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
interface UpdateNumberOfParticipantsResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface NumberOfParticipantsUpdated extends NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf {
    /**
     * The previous total number of participants. Available only when the booking includes
     * a single service variant.
     */
    previousTotalParticipants?: number;
    /**
     * Information about the previous booked service choices and participants.
     * Available only when the booking includes multiple service variants.
     */
    previousParticipantsChoices?: ParticipantChoices;
    /** The updated booking object. */
    booking?: Booking;
    /** ID of the participant number update initiator. */
    initiatedBy?: IdentificationData;
}
/** @oneof */
interface NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf {
    /**
     * The previous total number of participants. Available only when the booking includes
     * a single service variant.
     */
    previousTotalParticipants?: number;
    /**
     * Information about the previous booked service choices and participants.
     * Available only when the booking includes multiple service variants.
     */
    previousParticipantsChoices?: ParticipantChoices;
}
interface BulkCalculateAllowedActionsRequest {
    /**
     * The booking IDs for which we want to calculate the allowed actions.
     * @minSize 1
     */
    bookingIds?: string[] | null;
}
interface BulkCalculateAllowedActionsResponse {
    results?: BulkCalculateAllowedActionsResult[];
    /** Total number of successes and failures for Bulk Calculate Allowed Actions. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkCalculateAllowedActionsResult {
    /** Metadata for the booking. Including ID, index in the provided sequence, success status, and error. */
    itemMetadata?: ItemMetadata;
    /** Booking entity. */
    item?: AllowedActions;
}
/** Possible actions allowed for the booking. */
interface AllowedActions {
    /** Whether canceling the booking is allowed. */
    cancel?: boolean;
    /** Whether rescheduling the booking is allowed. */
    reschedule?: boolean;
}
interface GetSlotAvailabilityRequest {
    /** The slot for which the availability is checked. */
    slot?: V2Slot;
    /** The timezone for which availability is to be calculated. */
    timezone?: string | null;
}
interface GetSlotAvailabilityResponse {
    availability?: SlotAvailability;
    bookingPolicySettings?: BookingPolicySettings;
}
interface SlotAvailability {
    /** Identifier for the underlying session when the session is a single session or generated from a recurring session. Required. */
    slot?: V2Slot;
    /** Whether this available slot is bookable. */
    bookable?: boolean;
    /**
     * Total number of spots for this availability.
     * For example, for a class of 10 spots with 3 spots booked, `totalSpots` is 10 and `openSpots` is 7.
     */
    totalSpots?: number | null;
    /**
     * Number of open spots for this availability.
     * For appointments, if there are existing bookings with overlapping time, service & resource, `openSpots` is 0. Otherwise, `openSpots` is 1.
     */
    openSpots?: number | null;
    /** An object describing the slot's waiting list and its occupancy. */
    waitingList?: WaitingList;
    /** Indicators for booking policy violations for the slot. */
    bookingPolicyViolations?: BookingPolicyViolations;
    /** Indicates whether this slot is locked. */
    locked?: boolean | null;
}
interface WaitingList {
    /**
     * Total number of spots and open spots for this waiting list.
     * For example, a Yoga class of 10 waiting list spots with 3 registered on the waiting list has `total_spots`: 10 and `open_spots`: 7.
     */
    totalSpots?: number | null;
    openSpots?: number | null;
}
interface BookingPolicyViolations {
    /** Booking policy violation: Too early to book this slot. */
    tooEarlyToBook?: boolean | null;
    /** Booking policy violation: Too late to book this slot. */
    tooLateToBook?: boolean | null;
    /** Booking policy violation: Online booking is disabled for this slot. */
    bookOnlineDisabled?: boolean | null;
}
interface BookingPolicySettings {
    /** Booking policy settings for a given slot or schedule. */
    maxParticipantsPerBooking?: number | null;
}
interface AvailableResources {
    /**
     * Resource type ID.
     * @format GUID
     */
    resourceTypeId?: string | null;
    /**
     * Available resources for the slot.
     * `maxSize` is defined by 135 staff members + 3 resource types and 50 resources per type.
     * `Availability-2` currently has no `maxSize` defined.
     * @format GUID
     * @maxSize 275
     */
    resourceIds?: string[];
}
interface GetScheduleAvailabilityRequest {
    /**
     * ID of the schedule for which to check availability.
     * @format GUID
     */
    scheduleId?: string;
}
interface GetScheduleAvailabilityResponse {
    availability?: ScheduleAvailability;
    bookingPolicySettings?: BookingPolicySettings;
}
interface ScheduleAvailability {
    /** Total number of spots. */
    totalSpots?: number | null;
    /** Number of remaining open spots. */
    openSpots?: number | null;
    /** Indicators of booking policy violations for the specified schedule. */
    bookingPolicyViolations?: BookingPolicyViolations;
}
interface MarkBookingAsPendingRequest {
    /**
     * ID of the booking to mark as `PENDING`.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * managing the booking.
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability before updating the status.
     */
    flowControlSettings?: MarkBookingAsPendingFlowControlSettings;
}
interface MarkBookingAsPendingFlowControlSettings {
    /**
     * Whether to check for double bookings before updating the booking as pending.
     *
     * When passing `false`, a booking is only updated with status `PENDING`.
     * Default: `false`.
     */
    checkAvailabilityValidation?: boolean;
    /**
     * Whether to validate that the booking to be marked as pending has a `booking.slot.serviceId`
     * of a pending approval service.
     *
     * Default: `false`.
     */
    skipPendingApprovalServiceValidation?: boolean;
}
interface MarkBookingAsPendingResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface BookingMarkedAsPending {
    /** The booking object that was marked as pending. */
    booking?: Booking;
    /** Information about whether to notify the customer upon manual confirmation of the pending booking and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking overlaps with another existing confirmed booking. */
    doubleBooked?: boolean | null;
    /** ID of the mark as pending initiator. */
    initiatedBy?: IdentificationData;
    /** The previous status of the booking. */
    previousStatus?: BookingStatusWithLiterals;
    /** The previous payment status of the booking. */
    previousPaymentStatus?: PaymentStatusWithLiterals;
}
interface MigrationCheckIfClashesWithBlockedTimeRequest {
    msidAndBookingIds?: MsidAndBookingId[];
}
interface MsidAndBookingId {
    msid?: string;
    bookingId?: string;
}
interface MigrationCheckIfClashesWithBlockedTimeResponse {
    clashes?: Clash[];
}
interface Clash {
    msid?: string;
    bookingId?: string;
    sessionId?: string;
    resourceName?: string;
    contactName?: string;
}
interface CountBookingsRequest {
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
}
interface CountBookingsResponse {
    /** Number of bookings matching the specified filter. */
    count?: number;
}
interface CreateMultiServiceBookingRequest {
    /**
     * Between 2 to 8 new single-service booking specifications to create and combine in a multi-service booking.
     *
     * Each single-service booking specification must include `slot` details (`scheduleId`, `startDate`, and `endDate`).
     * Specify contact details, number of participants, and any additional fields as needed.
     *
     * For sequential bookings, ensure the timing allows services to be scheduled back-to-back.
     * @minSize 2
     * @maxSize 8
     */
    bookings: Booking[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to skip checking availability before updating the status.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
    /** Whether to return the created single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Multi-service booking type.
     *
     * Currently only `SEQUENTIAL_BOOKINGS` is supported.
     */
    multiServiceBookingType?: MultiServiceBookingTypeWithLiterals;
}
interface CreateMultiServiceBookingResponse {
    /**
     * Created multi-service booking.
     * Contains the single-service bookings in the same order as specified in the request.
     */
    multiServiceBooking?: MultiServiceBooking;
}
/**
 * A multi-service booking combines multiple single-service bookings into a package.
 *
 * Currently, multi-service bookings support only sequential scheduling where services are scheduled back-to-back at the same location, with each single-service booking starting when the previous 1 ends.
 */
interface MultiServiceBooking {
    /**
     * Multi-service booking ID.
     * @format GUID
     */
    _id?: string | null;
    /** The single-service bookings that make up the multi-service booking package. */
    bookings?: BookingResult[];
}
interface BookingResult {
    /**
     * Booking ID.
     * @format GUID
     */
    bookingId?: string | null;
    /** Booking entity. */
    booking?: Booking;
}
interface RescheduleMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to reschedule.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to reschedule. */
    rescheduleBookingsInfo: RescheduleBookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings rescheduling flow are changed.
     * For example, whether the availability of the new slot is checked before rescheduling the booking or if the customer can reschedule the booking even though the service's rescheduling policy doesn't allow it.
     */
    flowControlSettings?: RescheduleBookingFlowControlSettings;
    /** Whether to return the rescheduled bookings entities. */
    returnFullEntity?: boolean;
}
interface RescheduleBookingInfo extends RescheduleBookingInfoParticipantsInfoOneOf {
    /**
     * Total number of participants. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when all participants book the same variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when not all participants book the same variant.
     */
    participantsChoices?: ParticipantChoices;
    /**
     * ID of the booking to reschedule.
     * @format GUID
     */
    bookingId?: string | null;
    /** Information about the new slot. */
    slot?: V2Slot;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be specified when managing the booking.
     */
    revision?: string | null;
}
/** @oneof */
interface RescheduleBookingInfoParticipantsInfoOneOf {
    /**
     * Total number of participants. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when all participants book the same variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when not all participants book the same variant.
     */
    participantsChoices?: ParticipantChoices;
}
interface RescheduleMultiServiceBookingResponse {
    /** Rescheduled multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface GetMultiServiceBookingAvailabilityRequest {
    /**
     * ID of the multi-service booking to retrieve.
     * @format GUID
     */
    multiServiceBookingId: string | null;
}
interface GetMultiServiceBookingAvailabilityResponse {
    /** Whether all contained single-service bookings are bookable. */
    bookable?: boolean;
    /** Total number of spots. */
    totalCapacity?: number | null;
    /** Number of open spots. */
    remainingCapacity?: number | null;
    /** Indicators for policy violations of the multi-service booking. */
    policyViolations?: BookingPolicyViolations;
    /** Multi-service booking policy settings. */
    policySettings?: BookingPolicySettings;
    /** Details of the multi-service booking. */
    multiServiceBookingInfo?: GetMultiServiceBookingAvailabilityResponseBookingInfo[];
}
interface GetMultiServiceBookingAvailabilityResponseBookingInfo {
    /**
     * Booking ID.
     * @format GUID
     */
    bookingId?: string | null;
}
interface CancelMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to cancel.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings cancellation flow are changed.
     * For example, whether the customer can cancel the booking even though the service's cancellation policy doesn't allow it or whether to issue a refund upon cancellation.
     */
    flowControlSettings?: CancelBookingFlowControlSettings;
    /** Whether to return the canceled single-service bookings. */
    returnFullEntity?: boolean;
}
interface CancelMultiServiceBookingResponse {
    /** Canceled multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface MarkMultiServiceBookingAsPendingRequest {
    /**
     * ID of the multi-service booking to mark as `PENDING`.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to mark as `PENDING`. */
    markAsPendingBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to return the single-service bookings that were marked as `PENDING`.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings creation flow are changed.
     * For example, whether Wix Bookings checks availability before updating the booking.
     */
    flowControlSettings?: MarkBookingAsPendingFlowControlSettings;
}
interface BookingInfo {
    /**
     * ID of the single-service booking.
     * @format GUID
     */
    bookingId?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be specified when managing the booking.
     */
    revision?: string | null;
    /** Payment status to set for the single-service booking. */
    paymentStatus?: PaymentStatusWithLiterals;
}
interface MarkMultiServiceBookingAsPendingResponse {
    /** Updated multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface ConfirmMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to confirm its related bookings.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to confirm. */
    confirmBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /** Whether to return the confirmed single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings confirmation flow are changed.
     * For example, whether Wix Bookings checks availability before confirming the booking.
     */
    flowControlSettings?: ConfirmBookingFlowControlSettings;
}
interface ConfirmMultiServiceBookingResponse {
    /** Confirmed multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface DeclineMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to decline.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to decline. */
    declineBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /** Whether to return the declined single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings declining flow are changed.
     * For example, whether to issue a refund upon cancellation.
     */
    flowControlSettings?: DeclineBookingFlowControlSettings;
}
interface DeclineMultiServiceBookingResponse {
    /** Declined multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface BulkGetMultiServiceBookingAllowedActionsRequest {
    /**
     * IDs of the multi-service bookings to retrieve allowed actions for.
     * @format GUID
     * @minSize 1
     * @maxSize 50
     */
    multiServiceBookingIds: string[] | null;
}
interface BulkGetMultiServiceBookingAllowedActionsResponse {
    /**
     * Information about the multi-service bookings that were retrieved.
     * Includes their ID, index in the bulk request and whether they were successfully processed.
     */
    results?: BulkCalculateAllowedActionsResult[];
    /** Total number of successes and failures for Bulk Get Multi Service Booking Allowed Actions. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface GetMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking.
     * @format GUID
     */
    multiServiceBookingId: string | null;
}
interface GetMultiServiceBookingResponse {
    /** Retrieved multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
    /** Details about how many single-service bookings belong to the multi-service booking. */
    metadata?: MultiServiceBookingMetadata;
}
interface MultiServiceBookingMetadata {
    /**
     * Total number of `CONFIRMED` and `PENDING` single-service bookings belonging to the multi-service booking.
     * The total includes the number of single-service bookings which couldn't be retrieved due to lack of permissions.
     */
    totalNumberOfScheduledBookings?: number | null;
}
interface AddBookingsToMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /**
     * List of single-service booking IDs and their revision.
     * @maxSize 8
     */
    bookings: BookingIdAndRevision[];
    /**
     * Whether to return the single-service bookings that were added to the multi-service booking.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
}
interface BookingIdAndRevision {
    /**
     * ID of the single-service booking.
     * @format GUID
     */
    bookingId?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be specified when managing the booking.
     */
    revision?: string | null;
}
interface AddBookingsToMultiServiceBookingResponse {
    /** Single-service bookings that were added to the multi-service booking. */
    bookings?: BookingResult[];
}
interface RemoveBookingsFromMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /**
     * List of single-service booking IDs and their revision.
     * @maxSize 8
     */
    bookings?: BookingIdAndRevision[];
    /**
     * Whether to return the single-service bookings.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
}
interface RemoveBookingsFromMultiServiceBookingResponse {
    /** Single-service bookings that were removed from the multi-service booking. */
    bookings?: BookingResult[];
}
/** @docsIgnore */
type ConfirmOrDeclineBookingApplicationErrors = {
    code?: 'INVALID_BOOKING_STATUS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type BulkConfirmOrDeclineBookingApplicationErrors = {
    code?: 'DUPLICATED_BOOKINGS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type CreateBookingApplicationErrors = {
    code?: 'SESSION_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SCHEDULE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_RESOLVING_CUSTOM_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'VALIDATION_FAILURE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SESSION_CAPACITY_EXCEEDED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SCHEDULE_CAPACITY_EXCEEDED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SLOT_NOT_AVAILABLE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_VALIDATING_AVAILABILITY';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_POLICY_VIOLATION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNAUTHORIZED_OPERATION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_SERVICE_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_FLOW_SELECTED_RESOURCES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'CAN_NOT_CREATE_BOOKING_WITH_MULTI_SERVICE_BOOKING_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'CAN_NOT_SKIP_AVAILABILITY_VALIDATION_IF_RESOURCE_NOT_PROVIDED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_DATE_FORMAT';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_TIME_ZONE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'CONTACT_DETAILS_CONFLICT';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type BulkCreateBookingApplicationErrors = {
    code?: 'SESSION_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SCHEDULE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'RESOURCE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_RESOLVING_CUSTOM_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'VALIDATION_FAILURE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SLOT_NOT_AVAILABLE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'EMPTY_FORM_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNAUTHORIZED_OPERATION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'CAN_NOT_SKIP_AVAILABILITY_VALIDATION_IF_RESOURCE_NOT_PROVIDED';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type RescheduleBookingApplicationErrors = {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_POLICY_VIOLATION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNAUTHORIZED_OPERATION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SESSION_CAPACITY_EXCEEDED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SCHEDULE_CAPACITY_EXCEEDED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SLOT_NOT_AVAILABLE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'VALIDATION_FAILURE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'EMPTY_FORM_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SESSION_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SCHEDULE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'RESOURCE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_VALIDATING_AVAILABILITY';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_RESOLVING_SESSION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_RESOLVING_SCHEDULE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_SERVICE_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_ALLOWED_TO_UPDATE_BOOKING_IN_MULTI_SERVICE_BOOKING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ADD_ON_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ADD_ON_CHANGES_NOT_ALLOWED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ADD_ON_GROUP_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'PRICE_CHANGE_NOT_ALLOWED';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type ConfirmBookingApplicationErrors = {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_CONFIRMING_NON_PENDING_BOOKING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_BOOKING_STATUS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NO_OPEN_SPOTS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type SetBookingSubmissionIdApplicationErrors = {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type UpdateExtendedFieldsApplicationErrors = {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type DeclineBookingApplicationErrors = {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_DECLINE_NON_PENDING_BOOKING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_BOOKING_STATUS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type CancelBookingApplicationErrors = {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_POLICY_VIOLATION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'VALIDATION_FAILURE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FEATURE_LIMIT_EXCEEDED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'REFUND_NOT_ALLOWED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_ALLOWED_TO_UPDATE_BOOKING_IN_MULTI_SERVICE_BOOKING';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type UpdateNumberOfParticipantsApplicationErrors = {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type MarkBookingAsPendingApplicationErrors = {
    code?: 'BOOKING_MARK_BOOKING_AS_PENDING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NO_OPEN_SPOTS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_PENDING_APPROVAL_SERVICE';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type CreateMultiServiceBookingApplicationErrors = {
    code?: 'VALIDATION_FAILURE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SCHEDULE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'RESOURCE_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'EMPTY_FORM_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'FAILED_RESOLVING_CUSTOM_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SLOT_NOT_AVAILABLE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNAUTHORIZED_OPERATION';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'CAN_NOT_SKIP_AVAILABILITY_VALIDATION_IF_RESOURCE_NOT_PROVIDED';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type RescheduleMultiServiceBookingApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'REVISION_MISSING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_ALL_BOOKINGS_WERE_SENT';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'DUPLICATE_BOOKING_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SOME_BOOKINGS_UPDATES_FAILED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_REVISION_MISMATCH';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NO_OPEN_SPOTS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type GetMultiServiceBookingAvailabilityApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type CancelMultiServiceBookingApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ALL_BOOKINGS_ARE_ALREADY_DECLINED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ALL_BOOKINGS_ARE_ALREADY_CANCELED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type MarkMultiServiceBookingAsPendingApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'REVISION_MISSING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_ALL_BOOKINGS_WERE_SENT';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'DUPLICATE_BOOKING_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SOME_BOOKINGS_UPDATES_FAILED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_REVISION_MISMATCH';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NO_OPEN_SPOTS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type ConfirmMultiServiceBookingApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'REVISION_MISSING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_BOOKING_STATUS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ALL_BOOKINGS_ARE_ALREADY_CONFIRMED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_ALL_BOOKINGS_WERE_SENT';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'DUPLICATE_BOOKING_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SOME_BOOKINGS_UPDATES_FAILED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_REVISION_MISMATCH';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NO_OPEN_SPOTS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_ALL_BOOKINGS_HAVE_START_AND_END_DATE';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type DeclineMultiServiceBookingApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'REVISION_MISSING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'INVALID_BOOKING_STATUS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'ALL_BOOKINGS_ARE_ALREADY_DECLINED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'NOT_ALL_BOOKINGS_WERE_SENT';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'DUPLICATE_BOOKING_INFO';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'SOME_BOOKINGS_UPDATES_FAILED';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_REVISION_MISMATCH';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTI_SERVICE_BOOKING_INCLUDES_MULTIPLE_STAFF_MEMBERS';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type AddBookingsToMultiServiceBookingApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_REVISION_MISMATCH';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_ALREADY_PART_OF_ANOTHER_MULTI_SERVICE_BOOKING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTI_SERVICE_BOOKING_ALREADY_INCLUDES_BOOKING';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_TO_ADD_STATUS_NOT_VALID';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type RemoveBookingsFromMultiServiceBookingApplicationErrors = {
    code?: 'MULTI_SERVICE_BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_NOT_FOUND';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'BOOKING_REVISION_MISMATCH';
    description?: string;
    data?: Record<string, any>;
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: WebhooksIdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface BookingCanceledEnvelope {
    data: BookingCanceled;
    metadata: EventMetadata;
}
/**
 * Triggered when a booking is canceled.
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Read bookings calendar - including participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS
 * @permissionId BOOKINGS.BOOKING_READ
 * @webhook
 * @eventType wix.bookings.v2.booking_canceled
 * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings
 * @slug canceled
 */
declare function onBookingCanceled(handler: (event: BookingCanceledEnvelope) => void | Promise<void>): void;
interface BookingConfirmedEnvelope {
    data: BookingConfirmed;
    metadata: EventMetadata;
}
/**
 * Triggered when a booking is confirmed.
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Read bookings calendar - including participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS
 * @permissionId BOOKINGS.BOOKING_READ
 * @webhook
 * @eventType wix.bookings.v2.booking_confirmed
 * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings
 * @slug confirmed
 */
declare function onBookingConfirmed(handler: (event: BookingConfirmedEnvelope) => void | Promise<void>): void;
interface BookingCreatedEnvelope {
    entity: Booking;
    metadata: EventMetadata;
}
/**
 * Triggered when a booking is created.
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Read bookings calendar - including participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS
 * @permissionId BOOKINGS.READ_BOOKINGS
 * @permissionId BOOKINGS.BOOKING_READ
 * @webhook
 * @eventType wix.bookings.v2.booking_created
 * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings
 * @slug created
 */
declare function onBookingCreated(handler: (event: BookingCreatedEnvelope) => void | Promise<void>): void;
interface BookingDeclinedEnvelope {
    data: BookingDeclined;
    metadata: EventMetadata;
}
/**
 * Triggered when a booking is declined.
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Read bookings calendar - including participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS
 * @permissionId BOOKINGS.BOOKING_READ
 * @webhook
 * @eventType wix.bookings.v2.booking_declined
 * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings
 * @slug declined
 */
declare function onBookingDeclined(handler: (event: BookingDeclinedEnvelope) => void | Promise<void>): void;
interface BookingNumberOfParticipantsUpdatedEnvelope {
    data: NumberOfParticipantsUpdated;
    metadata: EventMetadata;
}
/**
 * Triggered when the number of participants is updated.
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Read bookings calendar - including participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS
 * @permissionId BOOKINGS.BOOKING_READ
 * @webhook
 * @eventType wix.bookings.v2.booking_number_of_participants_updated
 * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings
 * @slug number_of_participants_updated
 */
declare function onBookingNumberOfParticipantsUpdated(handler: (event: BookingNumberOfParticipantsUpdatedEnvelope) => void | Promise<void>): void;
interface BookingRescheduledEnvelope {
    data: BookingRescheduled;
    metadata: EventMetadata;
}
/**
 * Triggered when a booking is rescheduled.
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Read bookings calendar - including participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS
 * @permissionId BOOKINGS.BOOKING_READ
 * @webhook
 * @eventType wix.bookings.v2.booking_rescheduled
 * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings
 * @slug rescheduled
 */
declare function onBookingRescheduled(handler: (event: BookingRescheduledEnvelope) => void | Promise<void>): void;
interface BookingUpdatedEnvelope {
    entity: Booking;
    metadata: EventMetadata;
}
/**
 * Triggered when a booked schedule is updated.
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionScope Read bookings calendar - including participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-CALENDAR-WITH-PARTICIPANTS
 * @permissionId BOOKINGS.READ_BOOKINGS
 * @permissionId BOOKINGS.BOOKING_READ
 * @webhook
 * @eventType wix.bookings.v2.booking_updated
 * @serviceIdentifier com.wixpress.bookings.bookings.v2.Bookings
 * @slug updated
 */
declare function onBookingUpdated(handler: (event: BookingUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Updates the booking `status` to `CONFIRMED`, `PENDING`, or `DECLINED` based
 * on the `paymentStatus` you provide, double booking conflicts, and whether
 * the service requires business approval.
 *
 * ## eCommerce checkout restriction
 *
 * Call this method only when using a custom checkout page. Don't
 * call it when using a *Wix eCommerce checkout*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
 * In such cases, Wix automatically updates the booking status based on
 * the `paymentStatus` of the corresponding *Wix eCommerce order*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
 *
 * ## New booking status
 *
 * The booking `status` is set to `DECLINED` if both of the following conditions
 * are met:
 * + You provide `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT` as payment status.
 * + There is a double booking conflict.
 *
 * If only one or none of these conditions is met, `status` is set to `PENDING`
 * or `CONFIRMED` depending on whether the service requires business approval.
 *
 * ## Double bookings
 *
 * If there is a double booking conflict, but the booking has already been at least
 * partially paid, the method still marks the booking as `PENDING` or `CONFIRMED`.
 * Then, it also sets `doubleBooked` to `true`.
 *
 * ## Admin overwrites
 *
 * There are small but important differences in confirmation behavior if the
 * booking was created with special `flowControlSettings`:
 * + If the booking's `flowControlSettings.skipAvailabilityValidation` was set to
 * `true`, the booking is never declined regardless of double booking conflicts.
 * Instead, it's marked `CONFIRMED` or `PENDING`, depending on whether the
 * service requires business approval.
 * + If the booking's `flowControlSettings.skipBusinessConfirmation` was set to
 * `true`, the booking skips `PENDING` status and is marked `CONFIRMED`
 * immediately.
 * @param bookingId - ID of the booking to confirm or decline.
 * @public
 * @requiredField bookingId
 * @permissionId BOOKINGS.BOOKING_CONFIRM_OR_DECLINE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.confirmator.v2.Confirmator.ConfirmOrDeclineBooking
 */
declare function confirmOrDeclineBooking(bookingId: string, options?: ConfirmOrDeclineBookingOptions): Promise<NonNullablePaths<ConfirmOrDeclineBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: ConfirmOrDeclineBookingApplicationErrors;
}>;
interface ConfirmOrDeclineBookingOptions {
    /**
     * Current payment status of the booking when using a custom checkout page and
     * not the *eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     *
     * The booking is declined if there is a double booking conflict and you provide
     * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
}
/**
 * Confirms or declines up to 300 bookings.
 *
 *
 * See *Confirm Or Decline Booking*
 * ([SDK](https://dev.wix.com/docs/velo/api-reference/wix-bookings-v2/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking))
 * for details about when a booking is confirmed or declined.
 * @param details - Bookings to confirm or decline.
 * @public
 * @requiredField details
 * @requiredField details.bookingId
 * @permissionId BOOKINGS.BOOKING_CONFIRM_OR_DECLINE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.confirmator.v2.Confirmator.BulkConfirmOrDeclineBooking
 */
declare function bulkConfirmOrDeclineBooking(details: NonNullablePaths<BulkConfirmOrDeclineBookingRequestBookingDetails, `bookingId`, 2>[], options?: BulkConfirmOrDeclineBookingOptions): Promise<NonNullablePaths<BulkConfirmOrDeclineBookingResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.totalParticipants` | `results.${number}.item.status` | `results.${number}.item.paymentStatus` | `results.${number}.item.selectedPaymentOption` | `results.${number}.item.createdBy.anonymousVisitorId` | `results.${number}.item.createdBy.memberId` | `results.${number}.item.createdBy.wixUserId` | `results.${number}.item.createdBy.appId` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
    __applicationErrorsType?: BulkConfirmOrDeclineBookingApplicationErrors;
}>;
interface BulkConfirmOrDeclineBookingOptions {
    /** Whether to return the confirmed or declined booking objects. */
    returnEntity?: boolean;
}
/**
 * Creates a booking.
 *
 *
 * ## Appointment booking
 *
 * For appointment-based services, specify the relevant `slot` in
 * `bookedEntity.slot`. We recommend specifying the complete
 * `availabilityEntries.slot` returned in Query Availability
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
 * in your call's request to avoid failed calls due to unavailability.
 *
 * ## Class session booking
 *
 * For class services, specify the relevant event ID
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
 * as `bookedEntity.slot.eventId`.
 * We recommend retrieving the event ID from Query Availability's
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
 * `availabilityEntries.slot.eventId` to avoid failed calls due to unavailability.
 * Specifying an event ID leads to automatic calculations of `slot.startDate`, `slot.endDate`,
 * `slot.timezone`, `slot.resource`, and `slot.location`. All manually specified
 * values are overridden.
 *
 * ## Course booking
 *
 * For course services, specify the course's schedule ID in `bookedEntity.schedule.scheduleId`.
 * We recommend following [this sample flow](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course)
 * to minimize failed calls due to unavailability.
 *
 * ## Related resources
 *
 * Specifying a `resource` triggers an availability check, resulting in a failed
 * call if the resource is unavailable. Omitting a resource allows Wix Bookings
 * to assign a resource belonging to the relevant type randomly when the merchant
 * confirms the booking.
 *
 * ## Participant information
 *
 * You must specify either `participantsChoices` or `totalParticipants`.
 * The call fails if the specified `participantsChoices` aren't among the supported
 * _service options and variants_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
 *
 * ## Notify customers
 *
 * You can specify a `participantNotification.message` for the customer that's send
 * immediately. Ensure `participantNotification.notifyParticipants` is set to `true`
 * to send the message.
 *
 * If you specify `{"sendSmsReminder": true}`, the customer receives an SMS 24 hours
 * before the session starts. The phone number is taken from `contactDetails.phone`.
 *
 * ## Booking status
 *
 * Bookings default to the `CREATED` status, not affecting the business calendar
 * or resource availability. You can specify a different status when the calling
 * [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities)
 * has `Manage Bookings` permissions.
 *
 * ## Payment options
 *
 * The specified `selectedPaymentOption` indicates how the customer intends to
 * pay, allowing for later changes to a different method supported by the service.
 *
 * ## Payment status
 *
 * A booking is initially created with `{"paymentStatus": "UNDEFINED"}` regardless
 * of the payment status specified in Create Booking. If a customer uses an
 * _eCommerce checkout_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)),
 * Wix Bookings automatically syncs the booking's payment status from
 * the corresponding eCommerce order
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
 *
 * If a booking doesn't have a corresponding eCommerce order, for example, since
 * the customer didn't use the eCommerce checkout, you can update the booking's
 * payment status with Confirm Or Decline Booking
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).
 *
 * ## Booking form data
 *
 * When customers sign up for a service, they must fill out the booking form.
 * To create a booking with a completed booking form, specify the relevant data in
 * `formSubmission`. Ensure the values of the corresponding fields in
 * `booking.contactDetails` and `formSubmission` are identical. If these values
 * don't match, Create Booking fails. Therefore, we recommend specifying
 * only `booking.contactDetails.contactId` when providing `formSubmission`.
 *
 * ## Admin overwrites
 *
 * There are small but important differences when you specify special
 * `flowControlSettings`:
 *
 * - `{"skipAvailabilityValidation": true}`: The call succeeds
 * regardless of availability. If you don't specify any resource, the call
 * succeeds even if no resource of the relevant type is available.
 * - `{"skipBusinessConfirmation": true}`: Automatically confirms `PENDING`
 * bookings that require manual confirmation.
 * - `{"skipSelectedPaymentOptionValidation": true}`: Allows customers to pay
 * with payment methods that aren't supported for the service.
 *
 * When using special `flowControlSettings`, ensure you have sufficient
 * permissions. If you encounter failed calls due to insufficient permissions,
 * consider the following options:
 *
 * - **App developers** can use a higher
 * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),
 * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.
 * - **Site developers** can utilize
 * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).
 *
 * Granting additional permissions and using elevation permits method calls that
 * would typically fail due to authorization checks. Therefore, you should use
 * them intentionally and securely.
 * @param booking - The booking to create.
 * @public
 * @requiredField booking
 * @requiredField booking.additionalFields._id
 * @requiredField booking.bookedEntity
 * @permissionId BOOKINGS.BOOKING_CREATE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.CreateBooking
 */
declare function createBooking(booking: NonNullablePaths<Booking, `additionalFields.${number}._id` | `bookedEntity`, 4>, options?: CreateBookingOptions): Promise<NonNullablePaths<CreateBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: CreateBookingApplicationErrors;
}>;
interface CreateBookingOptions {
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     * Default: `true`.
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when creating a booking.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
}
/**
 * Creates up to 8 bookings.
 *
 *
 * See Create Booking
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking))
 * for more information.
 *
 * If any of the specified bookings is missing a required field the entire call
 * fails.
 *
 * If you specify 1 or more unavailable bookings, the call succeeds
 * while the unavailable bookings aren't created. Instead, they're counted as
 * failures in the returned `bulkActionMetadata`.
 * @param createBookingsInfo - Bookings to create.
 *
 * Max: 8 bookings
 * @public
 * @requiredField createBookingsInfo
 * @requiredField createBookingsInfo.booking
 * @requiredField createBookingsInfo.booking.additionalFields._id
 * @requiredField createBookingsInfo.booking.bookedEntity
 * @permissionId BOOKINGS.BOOKING_CREATE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.BulkCreateBooking
 */
declare function bulkCreateBooking(createBookingsInfo: NonNullablePaths<CreateBookingInfo, `booking` | `booking.additionalFields.${number}._id` | `booking.bookedEntity`, 5>[], options?: BulkCreateBookingOptions): Promise<NonNullablePaths<BulkCreateBookingResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.totalParticipants` | `results.${number}.item.status` | `results.${number}.item.paymentStatus` | `results.${number}.item.selectedPaymentOption` | `results.${number}.item.createdBy.anonymousVisitorId` | `results.${number}.item.createdBy.memberId` | `results.${number}.item.createdBy.wixUserId` | `results.${number}.item.createdBy.appId` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
    __applicationErrorsType?: BulkCreateBookingApplicationErrors;
}>;
interface BulkCreateBookingOptions {
    /** Whether to return the created bookings. */
    returnFullEntity?: boolean;
}
/**
 * Reschedules an appointment booking to a different slot or a class booking to
 * a different session.
 *
 *
 * ## Course booking limitation
 *
 * You can't reschedule course bookings.
 *
 * ## Appointment sessions
 *
 * For appointments, the old session is removed from the business calendar
 * while a new session is added. We recommend calling Query Availability
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
 * first and specifying the entire retrieved `slot`.
 *
 * ## Class sessions
 *
 * For classes, the new session must be an existing session belonging to the
 * same class. We recommend retrieving `availabilityEntries.slot.eventId`
 * from Query Availability
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/query-availability))
 * to avoid failed Reschedule Booking calls due to unavailability. Specify
 * only `slot.eventId` instead of the entire `slot` object.
 *
 * ## Notify customers
 *
 * You can specify a `participantNotification.message` for the customer. To send
 * the message, you must also specify `participantNotification.notifyParticipants`
 * as `true`.
 *
 * ## Admin overwrites
 *
 * There are small but important differences when you specify special
 * `flowControlSettings`:
 *
 * - `{"ignoreReschedulePolicy": true}`: The call succeeds even if the
 * service's `reschedulePolicy` doesn't allow it.
 * - `{"skipAvailabilityValidation": true}`: The call succeeds even if
 * the specified session, slot, or resource isn't available. If you don't
 * specify any resource, the call succeeds even if no resource of the relevant
 * type is available.
 * - `{"skipBusinessConfirmation": true}`: Any `PENDING` booking is
 * automatically confirmed even if the services requires the merchants's
 * manual confirmation.
 *
 * When using special `flowControlSettings`, ensure you have sufficient
 * permissions. If you encounter failed calls due to insufficient permissions,
 * consider the following options:
 *
 * - **App developers** can use a higher
 * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),
 * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.
 * - **Site developers** can utilize
 * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).
 *
 * Granting additional permissions and using elevation permits method calls that
 * would typically fail due to authorization checks. Therefore, you should use
 * them intentionally and securely.
 * @param bookingId - ID of the booking to reschedule.
 * @param slot - New slot of the booking.
 * @public
 * @requiredField bookingId
 * @requiredField options.revision
 * @requiredField slot
 * @param options - An object representing the available options for rescheduling a booking.
 * @permissionId BOOKINGS.BOOKING_RESCHEDULE
 * @applicableIdentity APP
 * @applicableIdentity MEMBER
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.RescheduleBooking
 */
declare function rescheduleBooking(bookingId: string, slot: V2Slot, options?: NonNullablePaths<RescheduleBookingOptions, `revision`, 2>): Promise<NonNullablePaths<RescheduleBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: RescheduleBookingApplicationErrors;
}>;
interface RescheduleBookingOptions extends RescheduleBookingOptionsParticipantsInfoOneOf {
    /**
     * Revision number, which increments by 1 each time the booking is rescheduled.
     * To prevent conflicting changes, the current revision must be passed when
     * rescheduling the booking.
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer about the rescheduling and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when rescheduling a booking.
     */
    flowControlSettings?: RescheduleBookingFlowControlSettings;
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Specify when not all
     * participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
/** @oneof */
interface RescheduleBookingOptionsParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Specify when not all
     * participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
/**
 * Updates the booking status to `CONFIRMED` without checking whether the relevant slot or schedule is still available.
 *
 *
 * ## eCommerce checkout restriction
 *
 * Call this method only when using a custom checkout page. Don't
 * call it when using a Wix eCommerce checkout
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
 * In such cases, Wix automatically updates the booking status based on
 * the `paymentStatus` of the corresponding Wix eCommerce order
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
 *
 * ## When to call Confirm Or Decline Booking instead
 *
 * Confirm Booking doesn't check whether a slot or schedule is still available.
 * For these checks, call Confirm or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) instead.
 *
 * ## Original status validation
 *
 * You can only confirm bookings with a status of `PENDING`, `CREATED`, or
 * `WAITING_LIST`.
 *
 * ## Double bookings
 *
 * Confirm Booking doesn't check whether a slot or schedule is still available.
 * You can specify
 *
 * ```json
 * {
 * "flowControlSettings": {
 * "checkAvailabilityValidation": true
 * },
 * "doubleBooked": true
 * }
 * ```
 * to forcefully set the booking's `doubleBooked` flag to `true`, regardless of
 * a potential double booking conflict. You must call with `Manage Bookings`
 * permissions to do so. For the default flow control settings
 * `{"checkAvailabilityValidation": false}`, the specified `doubleBooked` value
 * is ignored.
 *
 * ## Payment status
 *
 * Also updates the booking's `paymentStatus`, if you specify a new payment
 * status.
 *
 * ## Notify customers
 *
 * You can specify a `participantNotification.message` for the customer. To send
 * the message, you must also specify `participantNotification.notifyParticipants`
 * as `true`.
 * @param bookingId - ID of the booking to confirm.
 * @param revision - Revision number, which increments by 1 each time the booking is updated.
 * To prevent conflicting changes, the current revision must be passed when
 * updating the booking.
 * @public
 * @requiredField bookingId
 * @requiredField revision
 * @param options - An object representing the available options for canceling a booking.
 * @permissionId BOOKINGS.BOOKING_CONFIRM
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.ConfirmBooking
 */
declare function confirmBooking(bookingId: string, revision: string, options?: ConfirmBookingOptions): Promise<NonNullablePaths<ConfirmBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: ConfirmBookingApplicationErrors;
}>;
interface ConfirmBookingOptions {
    /**
     * Information about whether to notify the customer about the confirmation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when confirming a booking.
     */
    flowControlSettings?: ConfirmBookingFlowControlSettings;
}
/**
 * Setting `submission_id` for a booking after the form submission is created.
 * @param bookingId - ID of the booking to set `submissionId` for.
 * @param submissionId - ID of the form submission to set on the booking.
 * @public
 * @documentationMaturity preview
 * @requiredField bookingId
 * @requiredField submissionId
 * @permissionId BOOKINGS.BOOKING_SET_SUBMISSION_ID
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.SetBookingSubmissionId
 */
declare function setBookingSubmissionId(bookingId: string, submissionId: string): Promise<NonNullablePaths<SetBookingSubmissionIdResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: SetBookingSubmissionIdApplicationErrors;
}>;
/**
 * Updates the extended fields for a booking.
 *
 *
 * If you specify an extended field `namespace` that doesn't exist yet, it's
 * created.
 *
 * Learn more about [extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/about-backend-extensions#schema-plugin-extensions).
 * @public
 * @requiredField _id
 * @requiredField namespace
 * @requiredField options
 * @requiredField options.namespaceData
 * @param _id - ID of the booking for which to update extended fields.
 * @param namespace - [Namespace](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-reading-and-writing-schema-plugin-fields#namespaces) of the app for which to update extended fields.
 * @param options - Options for updating the booking's extended fields.
 * @permissionId BOOKINGS.BOOKING_UPDATE_EXTENDED_FIELDS
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.UpdateExtendedFields
 */
declare function updateExtendedFields(_id: string, namespace: string, options: NonNullablePaths<UpdateExtendedFieldsOptions, `namespaceData`, 2>): Promise<NonNullablePaths<UpdateExtendedFieldsResponse, `namespace`, 2> & {
    __applicationErrorsType?: UpdateExtendedFieldsApplicationErrors;
}>;
interface UpdateExtendedFieldsOptions {
    /** Data to update. Structured according to the [schema](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields#json-schema-for-extended-fields) defined when the extended fields were configured. */
    namespaceData: Record<string, any> | null;
}
/**
 * Updates the booking status to `DECLINED` and updates the relevant session's
 * `participants.approvalStatus` to `DECLINED` without checking whether the relevant
 * slot or schedule is still available.
 *
 *
 * ## eCommerce checkout restriction
 *
 * Call this method only when using a custom checkout page. Don't
 * call it when using a Wix eCommerce checkout
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
 * In such cases, Wix automatically updates the booking status based on
 * the `paymentStatus` of the corresponding Wix eCommerce order
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
 *
 * ## When to call Confirm Or Decline Booking instead
 *
 * The method doesn't check whether a slot or schedule is still available. For
 * these checks you can call Confirm or Decline Booking
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)).
 *
 * ## Original status validation
 *
 * You can only decline bookings with a `status` of `PENDING`, `CREATED`, or
 * `WAITING_LIST`.
 *
 * ## Payment status
 *
 * Also updates the booking's `paymentStatus`, if you specify a new payment
 * status.
 *
 * ## Notify customers
 *
 * You can specify a `participantNotification.message` for the customer. To send
 * the message, you must also specify `participantNotification.notifyParticipants`
 * as `true`.
 * @param bookingId - ID of the booking to decline.
 * @param revision - Revision number, which increments by 1 each time the booking is updated.
 *
 * To prevent conflicting changes, the current revision must be specified when
 * declining the booking.
 * @public
 * @requiredField bookingId
 * @requiredField revision
 * @param options - An object representing the available options for declining a booking.
 * @permissionId BOOKINGS.BOOKING_DECLINE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.DeclineBooking
 */
declare function declineBooking(bookingId: string, revision: string, options?: DeclineBookingOptions): Promise<NonNullablePaths<DeclineBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: DeclineBookingApplicationErrors;
}>;
interface DeclineBookingOptions {
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to refund a declined booking.
     */
    flowControlSettings?: DeclineBookingFlowControlSettings;
}
/**
 * Updates the booking status to `CANCELED`.
 *
 *
 * ## Appointments
 *
 * For appointments, the corresponding event is removed from the Bookings
 * calendar.
 *
 * ## Class and course bookings
 *
 * For class or course bookings, the relevant participants are removed
 * from the class session or the course. However, the class session or course
 * remain on the business calendar.
 *
 * ## Notify customers
 *
 * You can specify a `participantNotification.message` for the customer. To send
 * the message, you must also specify `participantNotification.notifyParticipants`
 * as `true`.
 *
 * ## Admin overwrites
 *
 * There are small but important differences when you specify special
 * `flowControlSettings`:
 *
 * - `{"ignoreCancellationPolicy": true}`: The call succeeds even if the
 * service's `cancellationPolicy` doesn't allow it.
 * - `{"withRefund": true}`: The customer is refunded even if the service's
 * `refundPolicy` doesn't allow it.
 * - `{"waiveCancellationFee": true}`: The customer doesn't have to pay
 * the cancellation fee, even if the service's `cancellationPolicy` requires it.
 *
 * When using special `flowControlSettings`, ensure you have sufficient
 * permissions. If you encounter failed calls due to insufficient permissions,
 * consider the following options:
 *
 * - **App developers** can use a higher
 * [permission](https://dev.wix.com/docs/build-apps/develop-your-app/access/authorization/about-permissions),
 * such as `MANAGE BOOKINGS - ALL PERMISSIONS`.
 * - **Site developers** can utilize
 * [elevation](https://dev.wix.com/docs/develop-websites/articles/coding-with-velo/authorization/elevation).
 *
 * Granting additional permissions and using elevation permits method calls that
 * would typically fail due to authorization checks. Therefore, you should use
 * them intentionally and securely.
 * @param bookingId - ID of the booking to cancel.
 * @public
 * @requiredField bookingId
 * @requiredField options.revision
 * @param options - An object representing the available options for canceling a booking.
 * @permissionId BOOKINGS.BOOKING_CANCEL
 * @applicableIdentity APP
 * @applicableIdentity MEMBER
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.CancelBooking
 */
declare function cancelBooking(bookingId: string, options?: NonNullablePaths<CancelBookingOptions, `revision`, 2>): Promise<NonNullablePaths<CancelBookingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: CancelBookingApplicationErrors;
}>;
interface CancelBookingOptions {
    /**
     * Information about whether to notify the customer about the cancellation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to allow a cancellation even though the service's
     * policy doesn't allow it.
     */
    flowControlSettings?: CancelBookingFlowControlSettings;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * managing the booking.
     */
    revision: string | null;
}
/**
 * Updates the number of participants for a class or course booking and changes
 * the `totalNumberOfParticipants` for the relevant sessions.
 *
 *
 * ## Appointment limitation
 *
 * You can't update the number of participants for appointment bookings.
 *
 * ## Participant information
 *
 * You must specify either `participantsChoices` or `totalParticipants`.
 * The call fails if the specified `participantsChoices` aren't among the
 * supported service options and variants
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
 * @param bookingId - ID of the booking to update the number of participants for.
 * @public
 * @requiredField bookingId
 * @requiredField options.revision
 * @permissionId BOOKINGS.NUMBER_OF_PARTICIPANTS_UPDATE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.UpdateNumberOfParticipants
 */
declare function updateNumberOfParticipants(bookingId: string, options?: NonNullablePaths<UpdateNumberOfParticipantsOptions, `revision`, 2>): Promise<NonNullablePaths<UpdateNumberOfParticipantsResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: UpdateNumberOfParticipantsApplicationErrors;
}>;
interface UpdateNumberOfParticipantsOptions extends UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf {
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified
     * when updating the booking.
     */
    revision: string | null;
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices the participants have booked. Specify
     * when not all participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
/** @oneof */
interface UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices the participants have booked. Specify
     * when not all participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
/** @param bookingId - ID of the booking to mark as `PENDING`.
 * @param revision - Revision number, which increments by 1 each time the booking is updated.
 *
 * To prevent conflicting changes, the current revision must be specified when
 * managing the booking.
 * @public
 * @documentationMaturity preview
 * @requiredField bookingId
 * @requiredField revision
 * @permissionId BOOKINGS.BOOKING_MARK_BOOKING_AS_PENDING
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.Bookings.MarkBookingAsPending
 */
declare function markBookingAsPending(bookingId: string, revision: string, options?: MarkBookingAsPendingOptions): Promise<NonNullablePaths<MarkBookingAsPendingResponse, `booking.totalParticipants` | `booking.participantsChoices.serviceChoices` | `booking.bookedEntity.slot.serviceId` | `booking.bookedEntity.slot.scheduleId` | `booking.bookedEntity.slot.resource._id` | `booking.bookedEntity.slot.location.locationType` | `booking.bookedEntity.schedule.scheduleId` | `booking.bookedEntity.tags` | `booking.contactDetails.fullAddress.streetAddress.number` | `booking.contactDetails.fullAddress.streetAddress.name` | `booking.contactDetails.fullAddress.streetAddress.apt` | `booking.contactDetails.fullAddress.subdivisions` | `booking.contactDetails.fullAddress.subdivisions.${number}.code` | `booking.contactDetails.fullAddress.subdivisions.${number}.name` | `booking.additionalFields` | `booking.additionalFields.${number}._id` | `booking.additionalFields.${number}.valueType` | `booking.status` | `booking.paymentStatus` | `booking.selectedPaymentOption` | `booking.createdBy.anonymousVisitorId` | `booking.createdBy.memberId` | `booking.createdBy.wixUserId` | `booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: MarkBookingAsPendingApplicationErrors;
}>;
interface MarkBookingAsPendingOptions {
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability before updating the status.
     */
    flowControlSettings?: MarkBookingAsPendingFlowControlSettings;
}
/**
 * Creates a multi-service booking and all included single-service bookings simultaneously.
 *
 *
 * ## When to call this method
 *
 * Create sequential appointments where customers book related services together. For adding existing single-service bookings to an existing multi-service booking, call Add Bookings to Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/add-bookings-to-multi-service-booking)) instead.
 *
 * ## Requirements and behavior
 *
 * __Package constraints__: Multi-service bookings support 2-8 appointment-based single-service bookings only (course and class bookings aren't supported). All single-service bookings must be at the same location with sequential scheduling and no gaps between appointments.
 *
 * __Timing specification__: You must provide complete `slot` details (`scheduleId`, `startDate`, `endDate`) for each single-service booking. Wix Bookings validates sequential timing but doesn't auto-calculate it.
 *
 * __Package pricing__: The total price equals the sum of individual services. Wix Bookings automatically syncs the payment status from the corresponding Wix eCommerce order ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecommerce/orders/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)) if the customer paid via an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
 *
 * __Package notifications__: Customers receive 1 unified notification for the entire multi-service booking. Wix Bookings doesn't send notifications for the package's individual single-service bookings.
 *
 * ## Related methods
 *
 * Verify availability first with List Multi Service Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)).
 *
 * See Create Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-booking)) for more details about constraints and effects of creating single-service bookings.
 * @param bookings - Between 2 to 8 new single-service booking specifications to create and combine in a multi-service booking.
 *
 * Each single-service booking specification must include `slot` details (`scheduleId`, `startDate`, and `endDate`).
 * Specify contact details, number of participants, and any additional fields as needed.
 *
 * For sequential bookings, ensure the timing allows services to be scheduled back-to-back.
 * @public
 * @documentationMaturity preview
 * @requiredField bookings
 * @requiredField bookings.additionalFields._id
 * @requiredField bookings.bookedEntity
 * @requiredField bookings.bookedEntity.item
 * @requiredField bookings.bookedEntity.item.slot
 * @requiredField bookings.bookedEntity.item.slot.endDate
 * @requiredField bookings.bookedEntity.item.slot.location.locationType
 * @requiredField bookings.bookedEntity.item.slot.scheduleId
 * @requiredField bookings.bookedEntity.item.slot.startDate
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_CREATE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.CreateMultiServiceBooking
 */
declare function createMultiServiceBooking(bookings: NonNullablePaths<Booking, `additionalFields.${number}._id` | `bookedEntity` | `bookedEntity.slot` | `bookedEntity.slot.endDate` | `bookedEntity.slot.location.locationType` | `bookedEntity.slot.scheduleId` | `bookedEntity.slot.startDate`, 5>[], options?: CreateMultiServiceBookingOptions): Promise<NonNullablePaths<CreateMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: CreateMultiServiceBookingApplicationErrors;
}>;
interface CreateMultiServiceBookingOptions {
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to skip checking availability before updating the status.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
    /** Whether to return the created single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Multi-service booking type.
     *
     * Currently only `SEQUENTIAL_BOOKINGS` is supported.
     */
    multiServiceBookingType?: MultiServiceBookingTypeWithLiterals;
}
/**
 * Reschedules a multi-service booking by changing the timing for all or specific single-service bookings in the package.
 *
 *
 * This method reschedules single-service bookings within the multi-service booking while maintaining sequential order. You must specify exact new timing for each service to ensure they remain back-to-back with no gaps or overlaps.
 *
 * This method fails if it can't reschedule at least 1 single-service booking. You must provide the current revision number for each single-service booking you're rescheduling to prevent conflicting changes.
 *
 * See Reschedule Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-booking)) for single-service booking rescheduling details.
 * @param multiServiceBookingId - ID of the multi-service booking to reschedule.
 * @param rescheduleBookingsInfo - Information about the single-service bookings to reschedule.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @requiredField rescheduleBookingsInfo
 * @requiredField rescheduleBookingsInfo.bookingId
 * @requiredField rescheduleBookingsInfo.revision
 * @requiredField rescheduleBookingsInfo.slot
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_RESCHEDULE
 * @applicableIdentity APP
 * @applicableIdentity MEMBER
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.RescheduleMultiServiceBooking
 */
declare function rescheduleMultiServiceBooking(multiServiceBookingId: string, rescheduleBookingsInfo: NonNullablePaths<RescheduleBookingInfo, `bookingId` | `revision` | `slot`, 2>[], options?: RescheduleMultiServiceBookingOptions): Promise<NonNullablePaths<RescheduleMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: RescheduleMultiServiceBookingApplicationErrors;
}>;
interface RescheduleMultiServiceBookingOptions {
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings rescheduling flow are changed.
     * For example, whether the availability of the new slot is checked before rescheduling the booking or if the customer can reschedule the booking even though the service's rescheduling policy doesn't allow it.
     */
    flowControlSettings?: RescheduleBookingFlowControlSettings;
    /** Whether to return the rescheduled bookings entities. */
    returnFullEntity?: boolean;
}
/**
 * Checks if the business can still accommodate an existing multi-service booking and returns overall bookability status, capacity details, and policy violations.
 *
 *
 * Wix Bookings considers:
 * - The relevant services' booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).
 * - The availability of all required resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)).
 *
 * Call this method to check if an existing multi-service booking is still valid after business configuration changes.
 * For example, staff changes, policy updates, or capacity modifications.
 *
 * For checking availability before creating new multi-service bookings, call List Multi Service Availability Time Slots
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/multi-service-availability-time-slots/list-multi-service-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-multi-service-availability-time-slots)) instead.
 * @param multiServiceBookingId - ID of the multi-service booking to retrieve.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_GET_AVAILABILITY
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBookingAvailability
 */
declare function getMultiServiceBookingAvailability(multiServiceBookingId: string): Promise<NonNullablePaths<GetMultiServiceBookingAvailabilityResponse, `bookable` | `multiServiceBookingInfo`, 2> & {
    __applicationErrorsType?: GetMultiServiceBookingAvailabilityApplicationErrors;
}>;
/**
 * Cancels a multi-service booking and all its associated single-service bookings.
 *
 *
 * Cancels the entire multi-service booking, updating the status of all single-service bookings to `CANCELED`.
 * The call fails if all single-service bookings are already canceled or declined.
 *
 * See Cancel Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/cancel-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/cancel-booking)) for single-service booking cancellation details.
 * @param multiServiceBookingId - ID of the multi-service booking to cancel.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_CANCEL
 * @applicableIdentity APP
 * @applicableIdentity MEMBER
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.CancelMultiServiceBooking
 */
declare function cancelMultiServiceBooking(multiServiceBookingId: string, options?: CancelMultiServiceBookingOptions): Promise<NonNullablePaths<CancelMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: CancelMultiServiceBookingApplicationErrors;
}>;
interface CancelMultiServiceBookingOptions {
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings cancellation flow are changed.
     * For example, whether the customer can cancel the booking even though the service's cancellation policy doesn't allow it or whether to issue a refund upon cancellation.
     */
    flowControlSettings?: CancelBookingFlowControlSettings;
    /** Whether to return the canceled single-service bookings. */
    returnFullEntity?: boolean;
}
/**
 * Updates the status for all single-service bookings in a multi-service booking to `PENDING`.
 *
 *
 * Call this method for multi-service bookings requiring manual business approval before confirmation.
 *
 * ## Status requirements
 *
 * - __Original status__: All single-service bookings must have `CREATED` status.
 * - __Target status__: All bookings move to `PENDING` together (all-or-nothing operation).
 *
 * ## Checkout restrictions
 *
 * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,
 * Wix Bookings automatically manages the bookings' statuses based on payment processing.
 *
 * ## Additional updates
 *
 * - __Payment status__: Updates if you specify a new `markAsPendingBookingsInfo.paymentStatus`.
 * - __Customer notifications__: Send messages using `participantNotification`.
 * - __Revision control__: Requires current revision numbers for all single-service bookings.
 *
 * See Mark Booking as Pending ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/mark-booking-as-pending)) for more details about marking single-service bookings as pending.
 * @param multiServiceBookingId - ID of the multi-service booking to mark as `PENDING`.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_MARK_AS_PENDING
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.MarkMultiServiceBookingAsPending
 */
declare function markMultiServiceBookingAsPending(multiServiceBookingId: string, options?: MarkMultiServiceBookingAsPendingOptions): Promise<NonNullablePaths<MarkMultiServiceBookingAsPendingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: MarkMultiServiceBookingAsPendingApplicationErrors;
}>;
interface MarkMultiServiceBookingAsPendingOptions {
    /** Information about the single-service bookings to mark as `PENDING`. */
    markAsPendingBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to return the single-service bookings that were marked as `PENDING`.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings creation flow are changed.
     * For example, whether Wix Bookings checks availability before updating the booking.
     */
    flowControlSettings?: MarkBookingAsPendingFlowControlSettings;
}
/**
 * Updates the status for all single-service bookings in a multi-service booking to `CONFIRMED`.
 *
 *
 * Call this method for multi-service bookings requiring manual business approval.
 *
 * ## Status requirements
 *
 * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.
 * - __Target status__: All bookings move to `CONFIRMED` together (all-or-nothing operation).
 *
 * ## Checkout restrictions
 *
 * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,
 * Wix Bookings automatically manages the bookings' statuses based on payment processing.
 *
 * ## Additional updates
 *
 * - __Payment status__: Updates if you specify a new `confirmBookingsInfo.paymentStatus`.
 * - __Customer notifications__: Send messages using `participantNotification`.
 * - __Revision control__: Requires current revision numbers for all single-service bookings.
 *
 * See Confirm Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-booking)) for more details about confirming single-service bookings.
 * @param multiServiceBookingId - ID of the multi-service booking to confirm its related bookings.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_CONFIRM
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.ConfirmMultiServiceBooking
 */
declare function confirmMultiServiceBooking(multiServiceBookingId: string, options?: ConfirmMultiServiceBookingOptions): Promise<NonNullablePaths<ConfirmMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: ConfirmMultiServiceBookingApplicationErrors;
}>;
interface ConfirmMultiServiceBookingOptions {
    /** Information about the single-service bookings to confirm. */
    confirmBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /** Whether to return the confirmed single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings confirmation flow are changed.
     * For example, whether Wix Bookings checks availability before confirming the booking.
     */
    flowControlSettings?: ConfirmBookingFlowControlSettings;
}
/**
 * Updates the status for all single-service bookings in a multi-service booking to `DECLINED`.
 *
 *
 * Call this method to reject multi-service bookings that can't be accommodated or don't meet business requirements.
 *
 * ## Status requirements
 *
 * - __Original status__: All single-service bookings must have `PENDING`, `CREATED`, or `WAITING_LIST` status.
 * - __Target status__: All bookings move to `DECLINED` together (all-or-nothing operation).
 *
 * ## Checkout restrictions
 *
 * Only call this method if the customer paid via a custom checkout. For Wix eCommerce checkouts,
 * Wix Bookings automatically manages the bookings' statuses based on payment processing.
 *
 * ## Additional updates
 *
 * - __Payment status__: Updates if you specify a new `declineBookingsInfo.paymentStatus`.
 * - __Customer notifications__: Send messages using `participantNotification`.
 * - __Revision control__: Requires current revision numbers for all single-service bookings.
 *
 * Refer to Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/decline-booking)) for more details about declining single-service bookings.
 * @param multiServiceBookingId - ID of the multi-service booking to decline.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_DECLINE
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.DeclineMultiServiceBooking
 */
declare function declineMultiServiceBooking(multiServiceBookingId: string, options?: DeclineMultiServiceBookingOptions): Promise<NonNullablePaths<DeclineMultiServiceBookingResponse, `multiServiceBooking.bookings` | `multiServiceBooking.bookings.${number}.booking.totalParticipants` | `multiServiceBooking.bookings.${number}.booking.status` | `multiServiceBooking.bookings.${number}.booking.paymentStatus` | `multiServiceBooking.bookings.${number}.booking.selectedPaymentOption` | `multiServiceBooking.bookings.${number}.booking.createdBy.anonymousVisitorId` | `multiServiceBooking.bookings.${number}.booking.createdBy.memberId` | `multiServiceBooking.bookings.${number}.booking.createdBy.wixUserId` | `multiServiceBooking.bookings.${number}.booking.createdBy.appId`, 7> & {
    __applicationErrorsType?: DeclineMultiServiceBookingApplicationErrors;
}>;
interface DeclineMultiServiceBookingOptions {
    /** Information about the single-service bookings to decline. */
    declineBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /** Whether to return the declined single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings declining flow are changed.
     * For example, whether to issue a refund upon cancellation.
     */
    flowControlSettings?: DeclineBookingFlowControlSettings;
}
/**
 * Retrieves information about which actions the customer can perform for up to 50 multi-service bookings.
 *
 *
 * For each multi-service booking, the response indicates which actions are currently allowed:
 * - `cancel`: Whether the customer can cancel the multi-service booking.
 * - `reschedule`: Whether the customer can adjust the multi-service booking's timing.
 *
 * Bear the following considerations in mind when calling this method:
 *
 * __Real-time validation__: Wix Bookings calculates allowed actions based on current multi-service booking status, booking policies ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)), and available resources ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) at the time of the call.
 *
 * __Permission context__: Depending on the permissions of the calling [identity](https://dev.wix.com/build-apps/develop-your-app/about-identities), you may see different allowed actions for the same multi-service booking. For example, if the identity has permissions to read only part of the multi-service booking, the response indicates which single-service bookings the identity can read.
 *
 * __Status dependencies__: Allowed actions change as bookings progress through their lifecycle (`CREATED` → `PENDING` → `CONFIRMED`/`DECLINED` → `CANCELED`).
 * Bookings can skip `PENDING` and move directly from `CREATED` to `CONFIRMED`/`DECLINED` based on service configuration.
 * @param multiServiceBookingIds - IDs of the multi-service bookings to retrieve allowed actions for.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingIds
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_GET_ALLOWED_ACTIONS
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.BulkGetMultiServiceBookingAllowedActions
 */
declare function bulkGetMultiServiceBookingAllowedActions(multiServiceBookingIds: string[]): Promise<NonNullablePaths<BulkGetMultiServiceBookingAllowedActionsResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `results.${number}.item.cancel` | `results.${number}.item.reschedule` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
/**
 * Retrieves a multi-service booking and all its associated single-service bookings.
 *
 *
 * Returns the complete multi-service booking information including its ID, associated single-service bookings, and the total number of scheduled single-service bookings.
 *
 * If you call on behalf of an [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) with permissions to read only part of the multi-service booking, only the permitted single-service bookings are retrieved.
 * The returned total number includes single-service bookings for which you don't have permissions.
 *
 * See Query Extended Bookings ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/extended-bookings/query-extended-bookings) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-reader-v2/query-extended-bookings)) for details about retrieving individual single-service bookings and additional metadata.
 * @param multiServiceBookingId - ID of the multi-service booking.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_READ
 * @applicableIdentity APP
 * @returns Retrieved multi-service booking.
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.GetMultiServiceBooking
 */
declare function getMultiServiceBooking(multiServiceBookingId: string): Promise<NonNullablePaths<MultiServiceBooking, `bookings` | `bookings.${number}.booking.totalParticipants` | `bookings.${number}.booking.status` | `bookings.${number}.booking.paymentStatus` | `bookings.${number}.booking.selectedPaymentOption` | `bookings.${number}.booking.createdBy.anonymousVisitorId` | `bookings.${number}.booking.createdBy.memberId` | `bookings.${number}.booking.createdBy.wixUserId` | `bookings.${number}.booking.createdBy.appId`, 6>>;
/**
 * Expands an existing multi-service booking by adding existing single-service bookings to the package.
 *
 *
 * ## When to call this method
 *
 * Call this method to add 1 or more existing single-service bookings to an existing multi-service booking.
 * For creating a new multi-service booking with new single-service bookings, call Create Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/create-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/create-multi-service-booking)) instead.
 *
 * ## Package integration
 *
 * The timing of the single-service bookings to add must be compatible with the multi-service booking's sequential schedule to ensure no gaps or overlaps between services in the updated package.
 *
 * ## Requirements and limitations
 *
 * - __Maximum capacity__: The total number of single-service bookings can't exceed 8.
 * - __Booking eligibility__: You can add only independent single-service bookings that aren't part of another multi-service booking.
 * - __Status compatibility__: Added bookings must have compatible status with the target multi-service booking.
 * - __Revision control__: You must provide current revision numbers for all single-service bookings to add.
 * @param multiServiceBookingId - ID of the multi-service booking.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @requiredField options.bookings
 * @requiredField options.bookings.bookingId
 * @requiredField options.bookings.revision
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_ADD_BOOKINGS
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.AddBookingsToMultiServiceBooking
 */
declare function addBookingsToMultiServiceBooking(multiServiceBookingId: string, options?: NonNullablePaths<AddBookingsToMultiServiceBookingOptions, `bookings` | `bookings.${number}.bookingId` | `bookings.${number}.revision`, 4>): Promise<NonNullablePaths<AddBookingsToMultiServiceBookingResponse, `bookings` | `bookings.${number}.booking.totalParticipants` | `bookings.${number}.booking.status` | `bookings.${number}.booking.paymentStatus` | `bookings.${number}.booking.selectedPaymentOption` | `bookings.${number}.booking.createdBy.anonymousVisitorId` | `bookings.${number}.booking.createdBy.memberId` | `bookings.${number}.booking.createdBy.wixUserId` | `bookings.${number}.booking.createdBy.appId`, 6> & {
    __applicationErrorsType?: AddBookingsToMultiServiceBookingApplicationErrors;
}>;
interface AddBookingsToMultiServiceBookingOptions {
    /**
     * List of single-service booking IDs and their revision.
     * @maxSize 8
     */
    bookings: BookingIdAndRevision[];
    /**
     * Whether to return the single-service bookings that were added to the multi-service booking.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
}
/**
 * Removes single-service bookings from a multi-service booking and converts them to independent single-service bookings.
 *
 *
 * ## Removal options
 *
 * __Remove all permitted bookings__: If you specify an empty `bookings` array, all single-service bookings for which the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has read permissions are removed from the multi-service booking.
 *
 * __Selective removal__: Specify single-service booking IDs and revisions to remove only specific single-service bookings from the package.
 *
 * __Sequential scheduling__: To maintain sequential scheduling, remove only first or last single-service bookings. For middle bookings, first reschedule all relevant single-service bookings to eliminate gaps. To do so, call Reschedule Multi Service Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/reschedule-multi-service-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/reschedule-multi-service-booking)) before removing the unwanted bookings.
 *
 * ## Removal behavior
 *
 * __Independent bookings__: Removed single-service bookings become independent bookings.
 * You can manage them using single-service booking methods.
 *
 * __Automatic cleanup__: Multi-service bookings must contain at least 2 services.
 * If removal results in only 1 remaining single-service booking for the multi-service booking, the entire multi-service booking is deleted and the remaining single-service booking becomes a standalone booking.
 *
 * __Revision control__: Specify current revision numbers to prevent conflicting modifications during the removal process.
 * @param multiServiceBookingId - ID of the multi-service booking.
 * @public
 * @documentationMaturity preview
 * @requiredField multiServiceBookingId
 * @permissionId MULTI_SERVICE_BOOKINGS.MULTI_SERVICE_BOOKING_REMOVE_BOOKINGS
 * @applicableIdentity APP
 * @fqn com.wixpress.bookings.bookings.v2.MultiServiceBookings.RemoveBookingsFromMultiServiceBooking
 */
declare function removeBookingsFromMultiServiceBooking(multiServiceBookingId: string, options?: RemoveBookingsFromMultiServiceBookingOptions): Promise<NonNullablePaths<RemoveBookingsFromMultiServiceBookingResponse, `bookings` | `bookings.${number}.booking.totalParticipants` | `bookings.${number}.booking.status` | `bookings.${number}.booking.paymentStatus` | `bookings.${number}.booking.selectedPaymentOption` | `bookings.${number}.booking.createdBy.anonymousVisitorId` | `bookings.${number}.booking.createdBy.memberId` | `bookings.${number}.booking.createdBy.wixUserId` | `bookings.${number}.booking.createdBy.appId`, 6> & {
    __applicationErrorsType?: RemoveBookingsFromMultiServiceBookingApplicationErrors;
}>;
interface RemoveBookingsFromMultiServiceBookingOptions {
    /**
     * List of single-service booking IDs and their revision.
     * @maxSize 8
     */
    bookings?: BookingIdAndRevision[];
    /**
     * Whether to return the single-service bookings.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
}

export { type ActionEvent, Actor, type ActorWithLiterals, type AddBookingsToMultiServiceBookingApplicationErrors, type AddBookingsToMultiServiceBookingOptions, type AddBookingsToMultiServiceBookingRequest, type AddBookingsToMultiServiceBookingResponse, type Address, type AddressLocation, type AddressStreetOneOf, type AllowedActions, type ApplicationError, type AvailableResources, type BaseEventMetadata, type BookedAddOn, type BookedEntity, type BookedEntityItemOneOf, type BookedResource, type BookedSchedule, type BookedSlot, type Booking, type BookingCanceled, type BookingCanceledEnvelope, type BookingChanged, type BookingConfirmed, type BookingConfirmedEnvelope, type BookingCreatedEnvelope, type BookingDeclined, type BookingDeclinedEnvelope, type BookingDetails, type BookingFormFilled, type BookingIdAndRevision, type BookingInfo, type BookingMarkedAsPending, type BookingNumberOfParticipantsUpdatedEnvelope, type BookingParticipantsInfoOneOf, type BookingPolicySettings, type BookingPolicyViolations, type BookingRescheduled, type BookingRescheduledEnvelope, type BookingRescheduledPreviousParticipantsInfoOneOf, type BookingResult, type BookingSource, BookingStatus, type BookingStatusWithLiterals, type BookingUpdatedEnvelope, type BulkActionMetadata, type BulkBookingResult, type BulkCalculateAllowedActionsRequest, type BulkCalculateAllowedActionsResponse, type BulkCalculateAllowedActionsResult, type BulkConfirmOrDeclineBookingApplicationErrors, type BulkConfirmOrDeclineBookingOptions, type BulkConfirmOrDeclineBookingRequest, type BulkConfirmOrDeclineBookingRequestBookingDetails, type BulkConfirmOrDeclineBookingResponse, type BulkCreateBookingApplicationErrors, type BulkCreateBookingOptions, type BulkCreateBookingRequest, type BulkCreateBookingResponse, type BulkGetMultiServiceBookingAllowedActionsRequest, type BulkGetMultiServiceBookingAllowedActionsResponse, type BulkRescheduleBookingRequest, type BulkRescheduleBookingRequestBooking, type BulkRescheduleBookingResponse, type BulkUpdateBookedScheduleRequest, type BulkUpdateBookedScheduleResponse, type BulkUpdateBookingRequest, type BulkUpdateBookingResponse, type CancelBookingApplicationErrors, type CancelBookingFlowControlSettings, type CancelBookingOptions, type CancelBookingRequest, type CancelBookingRequestFlowControlSettings, type CancelBookingResponse, type CancelMultiServiceBookingApplicationErrors, type CancelMultiServiceBookingOptions, type CancelMultiServiceBookingRequest, type CancelMultiServiceBookingResponse, type Clash, type CommonIdentificationData, type CommonIdentificationDataIdOneOf, type ConfirmBookingApplicationErrors, type ConfirmBookingFlowControlSettings, type ConfirmBookingOptions, type ConfirmBookingRequest, type ConfirmBookingResponse, type ConfirmMultiServiceBookingApplicationErrors, type ConfirmMultiServiceBookingOptions, type ConfirmMultiServiceBookingRequest, type ConfirmMultiServiceBookingResponse, type ConfirmOrDeclineBookingApplicationErrors, type ConfirmOrDeclineBookingOptions, type ConfirmOrDeclineBookingRequest, type ConfirmOrDeclineBookingResponse, type ConfirmRequest, type ConfirmResponse, type ConsistentQueryBookingsRequest, type ConsistentQueryBookingsResponse, type ContactDetails, type CountBookingsRequest, type CountBookingsResponse, type CreateBookingApplicationErrors, type CreateBookingFlowControlSettings, type CreateBookingInfo, type CreateBookingOptions, type CreateBookingRequest, type CreateBookingRequestFlowControlSettings, type CreateBookingResponse, type CreateMultiServiceBookingApplicationErrors, type CreateMultiServiceBookingOptions, type CreateMultiServiceBookingRequest, type CreateMultiServiceBookingResponse, type CursorPaging, type Cursors, type CustomFormField, type DeclineBookingApplicationErrors, type DeclineBookingFlowControlSettings, type DeclineBookingOptions, type DeclineBookingRequest, type DeclineBookingResponse, type DeclineMultiServiceBookingApplicationErrors, type DeclineMultiServiceBookingOptions, type DeclineMultiServiceBookingRequest, type DeclineMultiServiceBookingResponse, type DomainEvent, type DomainEventBodyOneOf, type Duration, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type EventMetadata, type ExtendedFields, type FlowControlSettings, type GetMultiServiceBookingAvailabilityApplicationErrors, type GetMultiServiceBookingAvailabilityRequest, type GetMultiServiceBookingAvailabilityResponse, type GetMultiServiceBookingAvailabilityResponseBookingInfo, type GetMultiServiceBookingRequest, type GetMultiServiceBookingResponse, type GetScheduleAvailabilityRequest, type GetScheduleAvailabilityResponse, type GetSlotAvailabilityRequest, type GetSlotAvailabilityResponse, type IdentificationData, type IdentificationDataIdOneOf, IdentificationDataIdentityType, type IdentificationDataIdentityTypeWithLiterals, IdentityType, type IdentityTypeWithLiterals, type ItemMetadata, type LegacyCreateBookingRequest, type LegacyCreateBookingResponse, type Location, LocationLocationType, type LocationLocationTypeWithLiterals, LocationType, type LocationTypeWithLiterals, type MarkBookingAsPendingApplicationErrors, type MarkBookingAsPendingFlowControlSettings, type MarkBookingAsPendingOptions, type MarkBookingAsPendingRequest, type MarkBookingAsPendingResponse, type MarkMultiServiceBookingAsPendingApplicationErrors, type MarkMultiServiceBookingAsPendingOptions, type MarkMultiServiceBookingAsPendingRequest, type MarkMultiServiceBookingAsPendingResponse, type MaskedBooking, type MessageEnvelope, type MigrationCheckIfClashesWithBlockedTimeRequest, type MigrationCheckIfClashesWithBlockedTimeResponse, type MsidAndBookingId, type MultiServiceBooking, type MultiServiceBookingInfo, type MultiServiceBookingMetadata, MultiServiceBookingType, type MultiServiceBookingTypeWithLiterals, type NumberOfParticipantsUpdated, type NumberOfParticipantsUpdatedPreviousParticipantsInfoOneOf, type Paging, type PagingMetadataV2, type ParticipantChoices, type ParticipantNotification, PaymentStatus, type PaymentStatusWithLiterals, Platform, type PlatformWithLiterals, type QueryBookingsRequest, type QueryBookingsResponse, type QueryV2, type QueryV2PagingMethodOneOf, type RemoveBookingsFromMultiServiceBookingApplicationErrors, type RemoveBookingsFromMultiServiceBookingOptions, type RemoveBookingsFromMultiServiceBookingRequest, type RemoveBookingsFromMultiServiceBookingResponse, type RescheduleBookingApplicationErrors, type RescheduleBookingFlowControlSettings, type RescheduleBookingInfo, type RescheduleBookingInfoParticipantsInfoOneOf, type RescheduleBookingOptions, type RescheduleBookingOptionsParticipantsInfoOneOf, type RescheduleBookingRequest, type RescheduleBookingRequestFlowControlSettings, type RescheduleBookingRequestParticipantsInfoOneOf, type RescheduleBookingResponse, type RescheduleMultiServiceBookingApplicationErrors, type RescheduleMultiServiceBookingOptions, type RescheduleMultiServiceBookingRequest, type RescheduleMultiServiceBookingResponse, type RestoreInfo, type ScheduleAvailability, SelectedPaymentOption, type SelectedPaymentOptionWithLiterals, type ServiceChoice, type ServiceChoiceChoiceOneOf, type ServiceChoices, type SetBookingFormAndSubmissionIdRequest, type SetBookingFormAndSubmissionIdRequestCreatedByOneOf, type SetBookingFormAndSubmissionIdResponse, type SetBookingSessionIdRequest, type SetBookingSessionIdResponse, type SetBookingSubmissionIdApplicationErrors, type SetBookingSubmissionIdRequest, type SetBookingSubmissionIdResponse, type Slot, type SlotAvailability, type SlotBookings, type SlotLocation, type SlotResource, type SlotSlotResource, SortOrder, type SortOrderWithLiterals, type Sorting, type StreetAddress, type Subdivision, type UpdateBookingRequest, type UpdateBookingResponse, type UpdateExtendedFieldsApplicationErrors, type UpdateExtendedFieldsOptions, type UpdateExtendedFieldsRequest, type UpdateExtendedFieldsResponse, type UpdateNumberOfParticipantsApplicationErrors, type UpdateNumberOfParticipantsOptions, type UpdateNumberOfParticipantsOptionsParticipantsInfoOneOf, type UpdateNumberOfParticipantsRequest, type UpdateNumberOfParticipantsRequestParticipantsInfoOneOf, type UpdateNumberOfParticipantsResponse, type V2CancelBookingRequest, type V2CancelBookingResponse, type V2ConfirmBookingRequest, type V2ConfirmBookingResponse, type V2CreateBookingRequest, type V2CreateBookingRequestBookableItemOneOf, type V2CreateBookingRequestParticipantsInfoOneOf, type V2CreateBookingResponse, type V2DeclineBookingRequest, type V2DeclineBookingResponse, type V2RescheduleBookingRequest, type V2RescheduleBookingRequestParticipantsInfoOneOf, type V2RescheduleBookingResponse, type V2Slot, type V2UpdateNumberOfParticipantsRequest, type V2UpdateNumberOfParticipantsRequestParticipantsInfoOneOf, type V2UpdateNumberOfParticipantsResponse, ValueType, type ValueTypeWithLiterals, type WaitingList, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, type WebhooksIdentificationData, type WebhooksIdentificationDataIdOneOf, addBookingsToMultiServiceBooking, bulkConfirmOrDeclineBooking, bulkCreateBooking, bulkGetMultiServiceBookingAllowedActions, cancelBooking, cancelMultiServiceBooking, confirmBooking, confirmMultiServiceBooking, confirmOrDeclineBooking, createBooking, createMultiServiceBooking, declineBooking, declineMultiServiceBooking, getMultiServiceBooking, getMultiServiceBookingAvailability, markBookingAsPending, markMultiServiceBookingAsPending, onBookingCanceled, onBookingConfirmed, onBookingCreated, onBookingDeclined, onBookingNumberOfParticipantsUpdated, onBookingRescheduled, onBookingUpdated, removeBookingsFromMultiServiceBooking, rescheduleBooking, rescheduleMultiServiceBooking, setBookingSubmissionId, updateExtendedFields, updateNumberOfParticipants };
