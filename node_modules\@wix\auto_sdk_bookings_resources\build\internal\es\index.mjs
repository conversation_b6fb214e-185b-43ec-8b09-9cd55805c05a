// src/bookings-resources-v2-resource-resources.public.ts
import { renameKeysFromRESTResponseToSDKResponse as renameKeysFromRESTResponseToSDKResponse2 } from "@wix/sdk-runtime/rename-all-nested-keys";
import { transformRESTTimestampToSDKTimestamp as transformRESTTimestampToSDKTimestamp2 } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths as transformPaths3 } from "@wix/sdk-runtime/transformations/transform-paths";
import { EventDefinition } from "@wix/sdk-types";

// src/bookings-resources-v2-resource-resources.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import { queryBuilder } from "@wix/sdk-runtime/query-builder";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-resources-v2-resource-resources.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKFloatToRESTFloat } from "@wix/sdk-runtime/transformations/float";
import { transformRESTFloatToSDKFloat } from "@wix/sdk-runtime/transformations/float";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsResourcesV2ResourcesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/bookings/v2/resources",
        destPath: "/v2/resources"
      },
      {
        srcPath: "/_api/bookings/v2/resources",
        destPath: "/v2/resources"
      },
      {
        srcPath: "/v2/bulk/resources",
        destPath: "/v2/bulk/resources"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/resources-2",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/resources",
        destPath: "/v2/resources"
      },
      {
        srcPath: "/bookings/v2/bulk/resources",
        destPath: "/v2/bulk/resources"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/resources-2",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_resources";
function createResource(payload) {
  function __createResource({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resource.createdDate" },
          { path: "resource.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.CreateResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resource.createdDate" },
            { path: "resource.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createResource;
}
function bulkCreateResources(payload) {
  function __bulkCreateResources({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resources.createdDate" },
          { path: "resources.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.BulkCreateResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/bulk/resources/create",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkCreateResources;
}
function getResource(payload) {
  function __getResource({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "GET",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.GetResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/{resourceId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resource.createdDate" },
            { path: "resource.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getResource;
}
function updateResource(payload) {
  function __updateResource({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resource.createdDate" },
          { path: "resource.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "PATCH",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.UpdateResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/{resource.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resource.createdDate" },
            { path: "resource.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateResource;
}
function bulkUpdateResources(payload) {
  function __bulkUpdateResources({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "resources.fieldMask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resources.resource.createdDate" },
          { path: "resources.resource.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.BulkUpdateResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/bulk/resources/update",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkUpdateResources;
}
function deleteResource(payload) {
  function __deleteResource({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "DELETE",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.DeleteResource",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/{resourceId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteResource;
}
function bulkDeleteResources(payload) {
  function __bulkDeleteResources({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.BulkDeleteResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/bulk/resources/delete",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkDeleteResources;
}
function searchResources(payload) {
  function __searchResources({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "search.aggregations.range.buckets.from" },
          { path: "search.aggregations.range.buckets.to" },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.from"
          },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.to"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.SearchResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/search",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resources.createdDate" },
            { path: "resources.updatedDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "aggregationData.results.ranges.results.from" },
            { path: "aggregationData.results.ranges.results.to" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from"
            },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.from"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.to"
            },
            { path: "aggregationData.results.scalar.value" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.scalar.value"
            },
            {
              path: "aggregationData.results.nested.results.results.*.scalar.value"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __searchResources;
}
function queryResources(payload) {
  function __queryResources({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.QueryResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resources.createdDate" },
            { path: "resources.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryResources;
}
function countResources(payload) {
  function __countResources({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.ResourcesService.CountResources",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcesServiceUrl({
        protoPath: "/v2/resources/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countResources;
}

// src/bookings-resources-v2-resource-resources.universal.ts
import { transformPaths as transformPaths2 } from "@wix/sdk-runtime/transformations/transform-paths";
var ManagementType = /* @__PURE__ */ ((ManagementType2) => {
  ManagementType2["WIX_USER"] = "WIX_USER";
  return ManagementType2;
})(ManagementType || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var SortType = /* @__PURE__ */ ((SortType2) => {
  SortType2["COUNT"] = "COUNT";
  SortType2["VALUE"] = "VALUE";
  return SortType2;
})(SortType || {});
var SortDirection = /* @__PURE__ */ ((SortDirection2) => {
  SortDirection2["DESC"] = "DESC";
  SortDirection2["ASC"] = "ASC";
  return SortDirection2;
})(SortDirection || {});
var MissingValues = /* @__PURE__ */ ((MissingValues2) => {
  MissingValues2["EXCLUDE"] = "EXCLUDE";
  MissingValues2["INCLUDE"] = "INCLUDE";
  return MissingValues2;
})(MissingValues || {});
var ScalarType = /* @__PURE__ */ ((ScalarType2) => {
  ScalarType2["UNKNOWN_SCALAR_TYPE"] = "UNKNOWN_SCALAR_TYPE";
  ScalarType2["COUNT_DISTINCT"] = "COUNT_DISTINCT";
  ScalarType2["MIN"] = "MIN";
  ScalarType2["MAX"] = "MAX";
  return ScalarType2;
})(ScalarType || {});
var NestedAggregationType = /* @__PURE__ */ ((NestedAggregationType2) => {
  NestedAggregationType2["UNKNOWN_AGGREGATION_TYPE"] = "UNKNOWN_AGGREGATION_TYPE";
  NestedAggregationType2["VALUE"] = "VALUE";
  NestedAggregationType2["RANGE"] = "RANGE";
  NestedAggregationType2["SCALAR"] = "SCALAR";
  NestedAggregationType2["DATE_HISTOGRAM"] = "DATE_HISTOGRAM";
  return NestedAggregationType2;
})(NestedAggregationType || {});
var Interval = /* @__PURE__ */ ((Interval2) => {
  Interval2["UNKNOWN_INTERVAL"] = "UNKNOWN_INTERVAL";
  Interval2["YEAR"] = "YEAR";
  Interval2["MONTH"] = "MONTH";
  Interval2["WEEK"] = "WEEK";
  Interval2["DAY"] = "DAY";
  Interval2["HOUR"] = "HOUR";
  Interval2["MINUTE"] = "MINUTE";
  Interval2["SECOND"] = "SECOND";
  return Interval2;
})(Interval || {});
var AggregationType = /* @__PURE__ */ ((AggregationType2) => {
  AggregationType2["UNKNOWN_AGGREGATION_TYPE"] = "UNKNOWN_AGGREGATION_TYPE";
  AggregationType2["VALUE"] = "VALUE";
  AggregationType2["RANGE"] = "RANGE";
  AggregationType2["SCALAR"] = "SCALAR";
  AggregationType2["DATE_HISTOGRAM"] = "DATE_HISTOGRAM";
  AggregationType2["NESTED"] = "NESTED";
  return AggregationType2;
})(AggregationType || {});
var Mode = /* @__PURE__ */ ((Mode2) => {
  Mode2["OR"] = "OR";
  Mode2["AND"] = "AND";
  return Mode2;
})(Mode || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createResource2(resource) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({ resource });
  const reqOpts = createResource(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resource;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resource: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resource"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkCreateResources2(resources, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resources,
    returnEntity: options?.returnEntity
  });
  const reqOpts = bulkCreateResources(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          resources: "$[0]",
          returnEntity: "$[1].returnEntity"
        },
        singleArgumentUnchanged: false
      },
      ["resources", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getResource2(resourceId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resourceId
  });
  const reqOpts = getResource(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resource;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateResource2(_id, resource) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resource: { ...resource, id: _id }
  });
  const reqOpts = updateResource(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.resource;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: { resource: "$[1]" },
        explicitPathsToArguments: { "resource.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "resource"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkUpdateResources2(resources, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resources,
    returnEntity: options?.returnEntity
  });
  const reqOpts = bulkUpdateResources(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          resources: "$[0]",
          returnEntity: "$[1].returnEntity"
        },
        singleArgumentUnchanged: false
      },
      ["resources", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteResource2(resourceId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    resourceId
  });
  const reqOpts = deleteResource(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkDeleteResources2(ids) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({ ids });
  const reqOpts = bulkDeleteResources(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { ids: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["ids"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function searchResources2(search) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({ search });
  const reqOpts = searchResources(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { search: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["search"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryResources2() {
  const { httpClient, sideEffects } = arguments[0];
  return queryBuilder({
    func: async (payload) => {
      const reqOpts = queryResources(payload);
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return renameKeysFromSDKRequestToRESTRequest({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({ data }) => {
      const transformedData = renameKeysFromRESTResponseToSDKResponse(
        transformPaths2(data, [])
      );
      return {
        items: transformedData?.resources,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = sdkTransformError(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countResources2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    filter: options?.filter,
    search: options?.search
  });
  const reqOpts = countResources(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          filter: "$[0].filter",
          search: "$[0].search"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-resources-v2-resource-resources.public.ts
function createResource3(httpClient) {
  return (resource) => createResource2(
    resource,
    // @ts-ignore
    { httpClient }
  );
}
function bulkCreateResources3(httpClient) {
  return (resources, options) => bulkCreateResources2(
    resources,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function getResource3(httpClient) {
  return (resourceId) => getResource2(
    resourceId,
    // @ts-ignore
    { httpClient }
  );
}
function updateResource3(httpClient) {
  return (_id, resource) => updateResource2(
    _id,
    resource,
    // @ts-ignore
    { httpClient }
  );
}
function bulkUpdateResources3(httpClient) {
  return (resources, options) => bulkUpdateResources2(
    resources,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function deleteResource3(httpClient) {
  return (resourceId) => deleteResource2(
    resourceId,
    // @ts-ignore
    { httpClient }
  );
}
function bulkDeleteResources3(httpClient) {
  return (ids) => bulkDeleteResources2(
    ids,
    // @ts-ignore
    { httpClient }
  );
}
function searchResources3(httpClient) {
  return (search) => searchResources2(
    search,
    // @ts-ignore
    { httpClient }
  );
}
function queryResources3(httpClient) {
  return () => queryResources2(
    // @ts-ignore
    { httpClient }
  );
}
function countResources3(httpClient) {
  return (options) => countResources2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onResourceCreated = EventDefinition(
  "wix.bookings.resources.v2.resource_created",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onResourceDeleted = EventDefinition(
  "wix.bookings.resources.v2.resource_deleted",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "undefined.createdDate" },
          { path: "undefined.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onResourceUpdated = EventDefinition(
  "wix.bookings.resources.v2.resource_updated",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();

// src/bookings-resources-v2-resource-resources.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
import { createEventModule } from "@wix/sdk-runtime/event-definition-modules";
var createResource4 = /* @__PURE__ */ createRESTModule(createResource3);
var bulkCreateResources4 = /* @__PURE__ */ createRESTModule(bulkCreateResources3);
var getResource4 = /* @__PURE__ */ createRESTModule(getResource3);
var updateResource4 = /* @__PURE__ */ createRESTModule(updateResource3);
var bulkUpdateResources4 = /* @__PURE__ */ createRESTModule(bulkUpdateResources3);
var deleteResource4 = /* @__PURE__ */ createRESTModule(deleteResource3);
var bulkDeleteResources4 = /* @__PURE__ */ createRESTModule(bulkDeleteResources3);
var searchResources4 = /* @__PURE__ */ createRESTModule(searchResources3);
var queryResources4 = /* @__PURE__ */ createRESTModule(queryResources3);
var countResources4 = /* @__PURE__ */ createRESTModule(countResources3);
var onResourceCreated2 = createEventModule(onResourceCreated);
var onResourceDeleted2 = createEventModule(onResourceDeleted);
var onResourceUpdated2 = createEventModule(onResourceUpdated);
export {
  AggregationType,
  Interval,
  ManagementType,
  MissingValues,
  Mode,
  NestedAggregationType,
  ScalarType,
  SortDirection,
  SortOrder,
  SortType,
  WebhookIdentityType,
  bulkCreateResources4 as bulkCreateResources,
  bulkDeleteResources4 as bulkDeleteResources,
  bulkUpdateResources4 as bulkUpdateResources,
  countResources4 as countResources,
  createResource4 as createResource,
  deleteResource4 as deleteResource,
  getResource4 as getResource,
  onResourceCreated2 as onResourceCreated,
  onResourceDeleted2 as onResourceDeleted,
  onResourceUpdated2 as onResourceUpdated,
  queryResources4 as queryResources,
  searchResources4 as searchResources,
  updateResource4 as updateResource
};
//# sourceMappingURL=index.mjs.map