"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  listPolicySnapshotsByBookingIds: () => listPolicySnapshotsByBookingIds2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      },
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policy-snapshots";
function listPolicySnapshotsByBookingIds(payload) {
  function __listPolicySnapshotsByBookingIds({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.policy_snapshots.v1.booking_policy_snapshot",
      method: "GET",
      methodFqn: "com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(
        { protoPath: "/v1/policy-snapshots", data: payload, host }
      ),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicySnapshots.createdDate" },
            { path: "bookingPolicySnapshots.policy.createdDate" },
            { path: "bookingPolicySnapshots.policy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listPolicySnapshotsByBookingIds;
}

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.meta.ts
function listPolicySnapshotsByBookingIds2() {
  const payload = {};
  const getRequestOptions = listPolicySnapshotsByBookingIds(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/policy-snapshots",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  listPolicySnapshotsByBookingIds
});
//# sourceMappingURL=meta.js.map