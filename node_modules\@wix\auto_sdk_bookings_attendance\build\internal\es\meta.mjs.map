{"version": 3, "sources": ["../../../src/bookings-v2-attendance-attendance.http.ts", "../../../src/bookings-v2-attendance-attendance.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '',\n      },\n      {\n        srcPath: '/bookings-attendance',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/v2/attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/_api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n      {\n        srcPath: '/bookings/attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/bookings-attendance',\n        destPath: '/v2/attendance',\n      },\n      {\n        srcPath: '/api/bookings-attendance/v2/bulk/attendance',\n        destPath: '/v2/bulk/attendance',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_attendance';\n\n/** Retrieves attendance information. */\nexport function getAttendance(payload: object): RequestOptionsFactory<any> {\n  function __getAttendance({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.GetAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/{attendanceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'attendance.updatedDate' },\n              { path: 'attendance.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getAttendance;\n}\n\n/**\n * Sets or updates attendance information for a booking session. This\n * information is stored in an `attendance` object.\n *\n * If an `attendance` object already exists for the session, it's updated.\n * Otherwise, a new object is created.\n *\n * By default, `numberOfAttendees` is set to `1`, but you can specify a higher\n * number if multiple participants attended. Do not set `numberOfAttendees` to\n * `0` to indicate no attendance, instead specify `{\"status\": \"NOT_ATTENDED\"}`.\n *\n * Validation guidelines:\n *\n * + The call succeeds for mismatches between `numberOfAttendees`\n * and `status`. For example, make sure that your code doesn't specify\n * `{\"status\": \"NOT_ATTENDED\"}` with `{\"numberOfAttendees\": 5}`.\n * + The API also allows `numberOfAttendees` to exceed the booking's\n * `numberOfParticipants`. Use higher values only when scenarios like\n * walk-ins justify the exception.\n */\nexport function setAttendance(payload: object): RequestOptionsFactory<any> {\n  function __setAttendance({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'attendance.updatedDate' },\n          { path: 'attendance.statusUpdatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.SetAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/set',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'attendance.updatedDate' },\n              { path: 'attendance.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setAttendance;\n}\n\n/**\n * Sets or updates attendance information for multiple booking sessions.\n *\n *\n * Refer to Set Attendance for detailed behavior of individual attendance\n * entries.\n *\n * The call fails entirely if any entry in `attendanceDetails` is missing a\n * required field.\n *\n * If attendance details are provided for a non-existent session, the call\n * succeeds for valid sessions while marking the unavailable session as a\n * failure in the response.\n */\nexport function bulkSetAttendance(payload: object): RequestOptionsFactory<any> {\n  function __bulkSetAttendance({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'attendanceList.updatedDate' },\n          { path: 'attendanceList.statusUpdatedDate' },\n          { path: 'attendanceDetails.attendance.updatedDate' },\n          { path: 'attendanceDetails.attendance.statusUpdatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.BulkSetAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/bulk/attendance/set',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkSetAttendance;\n}\n\n/**\n * Creates a query to retrieve a list of attendances.\n *\n * The `queryAttendances()` function builds a query to retrieve a list of attendances and returns a `AttendancesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to call the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-builder/find) function.\n *\n * You can refine the query by chaining `AttendancesQueryBuilder` functions onto the query. `AttendancesQueryBuilder` functions enable you to sort, filter, and control the results that `queryAttendances()` returns.\n *\n * `queryAttendances()` uses the following `AttendancesQueryBuilder` default values that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryAttendances()` are applied in the order they are called. For example, if you apply `ascending(\"status\")` and then `ascending(\"numberOfAttendees\")`, the results are sorted first by the `\"status\"`, and then, if there are multiple results with the same `\"status\"`, the items are sorted by `\"numberOfAttendees\"`.\n *\n * The following `AttendancesQueryBuilder` functions are supported for the `queryAttendances()` function. For a full description of the tip settings object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/attendance/attendances-query-result/items) property in `AttendancesQueryResult`.\n */\nexport function queryAttendance(payload: object): RequestOptionsFactory<any> {\n  function __queryAttendance({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.QueryAttendance',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'attendances.updatedDate' },\n              { path: 'attendances.statusUpdatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryAttendance;\n}\n\n/**\n * Counts attendance records by contact.\n *\n * Returns the total number of attendance records for the contact,\n * with optional filtering by attendance status and service IDs.\n *\n * Filtering options:\n * + **attendance_status**: Filter by attendance status (ATTENDED, NOT_ATTENDED)\n * + **service_id**: Filter by one or more service IDs from the bookings\n *\n * If no filters are provided, returns the total count of all attendance records\n * for the contact.\n */\nexport function countAttendances(payload: object): RequestOptionsFactory<any> {\n  function __countAttendances({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v2.attendance',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.attendance.v2.AttendanceService.CountAttendances',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAttendanceV2AttendanceServiceUrl({\n        protoPath: '/v2/attendance/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countAttendances;\n}\n", "import * as ambassadorWixBookingsV2Attendance from './bookings-v2-attendance-attendance.http.js';\nimport * as ambassadorWixBookingsV2AttendanceTypes from './bookings-v2-attendance-attendance.types.js';\nimport * as ambassadorWixBookingsV2AttendanceUniversalTypes from './bookings-v2-attendance-attendance.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function getAttendance(): __PublicMethodMetaInfo<\n  'GET',\n  { attendanceId: string },\n  ambassadorWixBookingsV2AttendanceUniversalTypes.GetAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceTypes.GetAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceUniversalTypes.GetAttendanceResponse,\n  ambassadorWixBookingsV2AttendanceTypes.GetAttendanceResponse\n> {\n  const payload = { attendanceId: ':attendanceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV2Attendance.getAttendance(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/attendance/{attendanceId}',\n    pathParams: { attendanceId: 'attendanceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function setAttendance(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV2AttendanceUniversalTypes.SetAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceTypes.SetAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceUniversalTypes.SetAttendanceResponse,\n  ambassadorWixBookingsV2AttendanceTypes.SetAttendanceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV2Attendance.setAttendance(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/attendance/set',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function bulkSetAttendance(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV2AttendanceUniversalTypes.BulkSetAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceTypes.BulkSetAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceUniversalTypes.BulkSetAttendanceResponse,\n  ambassadorWixBookingsV2AttendanceTypes.BulkSetAttendanceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV2Attendance.bulkSetAttendance(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/bulk/attendance/set',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryAttendance(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV2AttendanceUniversalTypes.QueryAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceTypes.QueryAttendanceRequest,\n  ambassadorWixBookingsV2AttendanceUniversalTypes.QueryAttendanceResponse,\n  ambassadorWixBookingsV2AttendanceTypes.QueryAttendanceResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV2Attendance.queryAttendance(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/attendance/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function countAttendances(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsV2AttendanceUniversalTypes.CountAttendancesRequest,\n  ambassadorWixBookingsV2AttendanceTypes.CountAttendancesRequest,\n  ambassadorWixBookingsV2AttendanceUniversalTypes.CountAttendancesResponse,\n  ambassadorWixBookingsV2AttendanceTypes.CountAttendancesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsV2Attendance.countAttendances(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/attendance/count',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,2DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAGd,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,yBAAyB;AAAA,YACjC,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsBO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yBAAyB;AAAA,UACjC,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,yBAAyB;AAAA,YACjC,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgBO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,6BAA6B;AAAA,UACrC,EAAE,MAAM,mCAAmC;AAAA,UAC3C,EAAE,MAAM,2CAA2C;AAAA,UACnD,EAAE,MAAM,iDAAiD;AAAA,QAC3D;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,iCAAiC;AAAA,UAC3C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,0BAA0B;AAAA,YAClC,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAeO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,2DAA2D;AAAA,QAC9D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC3UO,SAASC,iBAOd;AACA,QAAM,UAAU,EAAE,cAAc,gBAAgB;AAEhD,QAAM,oBAC8B,cAAc,OAAO;AAEzD,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,cAAc,eAAe;AAAA,IAC3C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,iBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC8B,cAAc,OAAO;AAEzD,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,qBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC8B,kBAAkB,OAAO;AAE7D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,mBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC8B,gBAAgB,OAAO;AAE3D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,oBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBAC8B,iBAAiB,OAAO;AAE5D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "getAttendance", "setAttendance", "bulkSetAttendance", "queryAttendance", "countAttendances"]}