"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  DayOfWeek: () => DayOfWeek,
  DeleteStatus: () => DeleteStatus,
  Namespace: () => Namespace,
  PlacementType: () => PlacementType,
  ResolutionMethod: () => ResolutionMethod,
  SiteCreatedContext: () => SiteCreatedContext,
  SortOrder: () => SortOrder,
  State: () => State,
  WebhookIdentityType: () => WebhookIdentityType,
  countBookingPolicies: () => countBookingPolicies4,
  createBookingPolicy: () => createBookingPolicy4,
  deleteBookingPolicy: () => deleteBookingPolicy4,
  getBookingPolicy: () => getBookingPolicy4,
  getStrictestBookingPolicy: () => getStrictestBookingPolicy4,
  onBookingPolicyCreated: () => onBookingPolicyCreated2,
  onBookingPolicyDefaultBookingPolicySet: () => onBookingPolicyDefaultBookingPolicySet2,
  onBookingPolicyDeleted: () => onBookingPolicyDeleted2,
  onBookingPolicyUpdated: () => onBookingPolicyUpdated2,
  queryBookingPolicies: () => queryBookingPolicies4,
  setDefaultBookingPolicy: () => setDefaultBookingPolicy4,
  updateBookingPolicy: () => updateBookingPolicy4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-v1-booking-policy-booking-policies.public.ts
var import_rename_all_nested_keys2 = require("@wix/sdk-runtime/rename-all-nested-keys");
var import_timestamp3 = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths3 = require("@wix/sdk-runtime/transformations/transform-paths");
var import_sdk_types = require("@wix/sdk-types");

// src/bookings-v1-booking-policy-booking-policies.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_query_builder = require("@wix/sdk-runtime/query-builder");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-v1-booking-policy-booking-policies.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsV1BookingPoliciesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v1/booking-policies/strictest",
        destPath: "/v1/booking-policies/strictest"
      },
      {
        srcPath: "/_api/bookings/v1/booking-policies/query",
        destPath: "/v1/booking-policies/query"
      },
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policies";
function createBookingPolicy(payload) {
  function __createBookingPolicy({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBookingPolicy;
}
function getBookingPolicy(payload) {
  function __getBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "GET",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getBookingPolicy;
}
function getStrictestBookingPolicy(payload) {
  function __getStrictestBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/strictest",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getStrictestBookingPolicy;
}
function updateBookingPolicy(payload) {
  function __updateBookingPolicy({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "PATCH",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicy.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateBookingPolicy;
}
function setDefaultBookingPolicy(payload) {
  function __setDefaultBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}:setDefault",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "currentDefaultBookingPolicy.createdDate" },
            { path: "currentDefaultBookingPolicy.updatedDate" },
            { path: "previousDefaultBookingPolicy.createdDate" },
            { path: "previousDefaultBookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setDefaultBookingPolicy;
}
function deleteBookingPolicy(payload) {
  function __deleteBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "DELETE",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteBookingPolicy;
}
function queryBookingPolicies(payload) {
  function __queryBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicies.createdDate" },
            { path: "bookingPolicies.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryBookingPolicies;
}
function countBookingPolicies(payload) {
  function __countBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CountBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countBookingPolicies;
}

// src/bookings-v1-booking-policy-booking-policies.universal.ts
var import_transform_paths2 = require("@wix/sdk-runtime/transformations/transform-paths");
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var PlacementType = /* @__PURE__ */ ((PlacementType2) => {
  PlacementType2["BEFORE"] = "BEFORE";
  PlacementType2["AFTER"] = "AFTER";
  PlacementType2["REPLACE"] = "REPLACE";
  return PlacementType2;
})(PlacementType || {});
var DayOfWeek = /* @__PURE__ */ ((DayOfWeek2) => {
  DayOfWeek2["MONDAY"] = "MONDAY";
  DayOfWeek2["TUESDAY"] = "TUESDAY";
  DayOfWeek2["WEDNESDAY"] = "WEDNESDAY";
  DayOfWeek2["THURSDAY"] = "THURSDAY";
  DayOfWeek2["FRIDAY"] = "FRIDAY";
  DayOfWeek2["SATURDAY"] = "SATURDAY";
  DayOfWeek2["SUNDAY"] = "SUNDAY";
  return DayOfWeek2;
})(DayOfWeek || {});
var ResolutionMethod = /* @__PURE__ */ ((ResolutionMethod2) => {
  ResolutionMethod2["QUERY_PARAM"] = "QUERY_PARAM";
  ResolutionMethod2["SUBDOMAIN"] = "SUBDOMAIN";
  ResolutionMethod2["SUBDIRECTORY"] = "SUBDIRECTORY";
  return ResolutionMethod2;
})(ResolutionMethod || {});
var State = /* @__PURE__ */ ((State2) => {
  State2["UNKNOWN"] = "UNKNOWN";
  State2["ENABLED"] = "ENABLED";
  State2["DISABLED"] = "DISABLED";
  State2["PENDING"] = "PENDING";
  State2["DEMO"] = "DEMO";
  return State2;
})(State || {});
var SiteCreatedContext = /* @__PURE__ */ ((SiteCreatedContext2) => {
  SiteCreatedContext2["OTHER"] = "OTHER";
  SiteCreatedContext2["FROM_TEMPLATE"] = "FROM_TEMPLATE";
  SiteCreatedContext2["DUPLICATE_BY_SITE_TRANSFER"] = "DUPLICATE_BY_SITE_TRANSFER";
  SiteCreatedContext2["DUPLICATE"] = "DUPLICATE";
  SiteCreatedContext2["OLD_SITE_TRANSFER"] = "OLD_SITE_TRANSFER";
  SiteCreatedContext2["FLASH"] = "FLASH";
  return SiteCreatedContext2;
})(SiteCreatedContext || {});
var Namespace = /* @__PURE__ */ ((Namespace2) => {
  Namespace2["UNKNOWN_NAMESPACE"] = "UNKNOWN_NAMESPACE";
  Namespace2["WIX"] = "WIX";
  Namespace2["SHOUT_OUT"] = "SHOUT_OUT";
  Namespace2["ALBUMS"] = "ALBUMS";
  Namespace2["WIX_STORES_TEST_DRIVE"] = "WIX_STORES_TEST_DRIVE";
  Namespace2["HOTELS"] = "HOTELS";
  Namespace2["CLUBS"] = "CLUBS";
  Namespace2["ONBOARDING_DRAFT"] = "ONBOARDING_DRAFT";
  Namespace2["DEV_SITE"] = "DEV_SITE";
  Namespace2["LOGOS"] = "LOGOS";
  Namespace2["VIDEO_MAKER"] = "VIDEO_MAKER";
  Namespace2["PARTNER_DASHBOARD"] = "PARTNER_DASHBOARD";
  Namespace2["DEV_CENTER_COMPANY"] = "DEV_CENTER_COMPANY";
  Namespace2["HTML_DRAFT"] = "HTML_DRAFT";
  Namespace2["SITELESS_BUSINESS"] = "SITELESS_BUSINESS";
  Namespace2["CREATOR_ECONOMY"] = "CREATOR_ECONOMY";
  Namespace2["DASHBOARD_FIRST"] = "DASHBOARD_FIRST";
  Namespace2["ANYWHERE"] = "ANYWHERE";
  Namespace2["HEADLESS"] = "HEADLESS";
  Namespace2["ACCOUNT_MASTER_CMS"] = "ACCOUNT_MASTER_CMS";
  Namespace2["RISE"] = "RISE";
  Namespace2["BRANDED_FIRST"] = "BRANDED_FIRST";
  Namespace2["NOWNIA"] = "NOWNIA";
  Namespace2["UGC_TEMPLATE"] = "UGC_TEMPLATE";
  Namespace2["CODUX"] = "CODUX";
  Namespace2["MEDIA_DESIGN_CREATOR"] = "MEDIA_DESIGN_CREATOR";
  Namespace2["SHARED_BLOG_ENTERPRISE"] = "SHARED_BLOG_ENTERPRISE";
  Namespace2["STANDALONE_FORMS"] = "STANDALONE_FORMS";
  Namespace2["STANDALONE_EVENTS"] = "STANDALONE_EVENTS";
  Namespace2["MIMIR"] = "MIMIR";
  return Namespace2;
})(Namespace || {});
var DeleteStatus = /* @__PURE__ */ ((DeleteStatus2) => {
  DeleteStatus2["UNKNOWN"] = "UNKNOWN";
  DeleteStatus2["TRASH"] = "TRASH";
  DeleteStatus2["DELETED"] = "DELETED";
  DeleteStatus2["PENDING_PURGE"] = "PENDING_PURGE";
  DeleteStatus2["PURGED_EXTERNALLY"] = "PURGED_EXTERNALLY";
  return DeleteStatus2;
})(DeleteStatus || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createBookingPolicy2(bookingPolicy) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingPolicy
  });
  const reqOpts = createBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.bookingPolicy;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicy: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicy"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getBookingPolicy2(bookingPolicyId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingPolicyId
  });
  const reqOpts = getBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.bookingPolicy;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getStrictestBookingPolicy2(bookingPolicyIds) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingPolicyIds
  });
  const reqOpts = getStrictestBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyIds: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyIds"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateBookingPolicy2(_id, bookingPolicy) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingPolicy: { ...bookingPolicy, id: _id }
  });
  const reqOpts = updateBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.bookingPolicy;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: { bookingPolicy: "$[1]" },
        explicitPathsToArguments: { "bookingPolicy.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "bookingPolicy"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function setDefaultBookingPolicy2(bookingPolicyId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingPolicyId
  });
  const reqOpts = setDefaultBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteBookingPolicy2(bookingPolicyId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingPolicyId
  });
  const reqOpts = deleteBookingPolicy(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingPolicyId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingPolicyId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryBookingPolicies2() {
  const { httpClient, sideEffects } = arguments[0];
  return (0, import_query_builder.queryBuilder)({
    func: async (payload) => {
      const reqOpts = queryBookingPolicies(payload);
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({
      data
    }) => {
      const transformedData = (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
        (0, import_transform_paths2.transformPaths)(data, [])
      );
      return {
        items: transformedData?.bookingPolicies,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = (0, import_transform_error.transformError)(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countBookingPolicies2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = countBookingPolicies(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-v1-booking-policy-booking-policies.public.ts
function createBookingPolicy3(httpClient) {
  return (bookingPolicy) => createBookingPolicy2(
    bookingPolicy,
    // @ts-ignore
    { httpClient }
  );
}
function getBookingPolicy3(httpClient) {
  return (bookingPolicyId) => getBookingPolicy2(
    bookingPolicyId,
    // @ts-ignore
    { httpClient }
  );
}
function getStrictestBookingPolicy3(httpClient) {
  return (bookingPolicyIds) => getStrictestBookingPolicy2(
    bookingPolicyIds,
    // @ts-ignore
    { httpClient }
  );
}
function updateBookingPolicy3(httpClient) {
  return (_id, bookingPolicy) => updateBookingPolicy2(
    _id,
    bookingPolicy,
    // @ts-ignore
    { httpClient }
  );
}
function setDefaultBookingPolicy3(httpClient) {
  return (bookingPolicyId) => setDefaultBookingPolicy2(
    bookingPolicyId,
    // @ts-ignore
    { httpClient }
  );
}
function deleteBookingPolicy3(httpClient) {
  return (bookingPolicyId) => deleteBookingPolicy2(
    bookingPolicyId,
    // @ts-ignore
    { httpClient }
  );
}
function queryBookingPolicies3(httpClient) {
  return () => queryBookingPolicies2(
    // @ts-ignore
    { httpClient }
  );
}
function countBookingPolicies3(httpClient) {
  return (options) => countBookingPolicies2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onBookingPolicyCreated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v1.booking_policy_created",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onBookingPolicyDefaultBookingPolicySet = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v1.booking_policy_default_booking_policy_set",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "data.currentDefaultBookingPolicy.createdDate" },
          { path: "data.currentDefaultBookingPolicy.updatedDate" },
          { path: "data.previousDefaultBookingPolicy.createdDate" },
          { path: "data.previousDefaultBookingPolicy.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onBookingPolicyDeleted = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v1.booking_policy_deleted",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "undefined.createdDate" },
          { path: "undefined.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onBookingPolicyUpdated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.v1.booking_policy_updated",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();

// src/bookings-v1-booking-policy-booking-policies.context.ts
var import_rest_modules3 = require("@wix/sdk-runtime/rest-modules");
var import_event_definition_modules = require("@wix/sdk-runtime/event-definition-modules");
var createBookingPolicy4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(createBookingPolicy3);
var getBookingPolicy4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(getBookingPolicy3);
var getStrictestBookingPolicy4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(getStrictestBookingPolicy3);
var updateBookingPolicy4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(updateBookingPolicy3);
var setDefaultBookingPolicy4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(setDefaultBookingPolicy3);
var deleteBookingPolicy4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(deleteBookingPolicy3);
var queryBookingPolicies4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryBookingPolicies3);
var countBookingPolicies4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(countBookingPolicies3);
var onBookingPolicyCreated2 = (0, import_event_definition_modules.createEventModule)(onBookingPolicyCreated);
var onBookingPolicyDefaultBookingPolicySet2 = (0, import_event_definition_modules.createEventModule)(onBookingPolicyDefaultBookingPolicySet);
var onBookingPolicyDeleted2 = (0, import_event_definition_modules.createEventModule)(onBookingPolicyDeleted);
var onBookingPolicyUpdated2 = (0, import_event_definition_modules.createEventModule)(onBookingPolicyUpdated);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  DayOfWeek,
  DeleteStatus,
  Namespace,
  PlacementType,
  ResolutionMethod,
  SiteCreatedContext,
  SortOrder,
  State,
  WebhookIdentityType,
  countBookingPolicies,
  createBookingPolicy,
  deleteBookingPolicy,
  getBookingPolicy,
  getStrictestBookingPolicy,
  onBookingPolicyCreated,
  onBookingPolicyDefaultBookingPolicySet,
  onBookingPolicyDeleted,
  onBookingPolicyUpdated,
  queryBookingPolicies,
  setDefaultBookingPolicy,
  updateBookingPolicy
});
//# sourceMappingURL=index.js.map