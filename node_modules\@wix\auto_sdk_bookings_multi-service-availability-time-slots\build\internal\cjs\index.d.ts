import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { ListMultiServiceAvailabilityTimeSlotsOptions, ListMultiServiceAvailabilityTimeSlotsResponse, ListMultiServiceAvailabilityTimeSlotsApplicationErrors, Service, Location, GetMultiServiceAvailabilityTimeSlotResponse, GetMultiServiceAvailabilityTimeSlotApplicationErrors } from './index.typings.js';
export { AvailableResources, BookingPolicyViolations, CursorPaging, CursorPagingMetadata, Cursors, CustomerChoices, EventInfo, GetAvailabilityTimeSlotRequest, GetAvailabilityTimeSlotRequestCustomerChoices, GetAvailabilityTimeSlotResponse, GetEventTimeSlotRequest, GetEventTimeSlotResponse, GetMultiServiceAvailabilityTimeSlotRequest, ListAvailabilityTimeSlotsRequest, ListAvailabilityTimeSlotsResponse, ListEventTimeSlotsRequest, ListEventTimeSlotsResponse, ListMultiServiceAvailabilityTimeSlotsRequest, LocationType, LocationTypeWithLiterals, NestedTimeSlot, NonBookableReasons, Resource, ResourceType, TimeSlot, V2CustomerChoices, WaitingList } from './index.typings.js';

declare function listMultiServiceAvailabilityTimeSlots$1(httpClient: HttpClient): ListMultiServiceAvailabilityTimeSlotsSignature;
interface ListMultiServiceAvailabilityTimeSlotsSignature {
    /**
     * Retrieves a list of multi-service time slots that match the provided filters.
     *
     *
     * ## Required filters
     *
     * You must specify one of:
     * - `services.serviceId`, `fromLocalDate`, `toLocalDate`, `location`, and `timeZone` (additional filters are optional).
     * - `cursorPaging.cursor` returned from a previous response.
     *
     * Each returned `timeSlot` acts as a container spanning the entire service sequence, with nested time slots providing individual service details.
     *
     * ## Defaults
     *
     * - Results are sorted by `localStartDate` in ascending order.
     * - `cursorPaging.limit` is `1000`.
     * - The response contains both bookable and non-bookable slots.
     *
     * ## Service type limitations
     *
     * Only appointment-type services are supported.
     *
     * To retrieve appointment availability for a single service, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).
     */
    (options?: ListMultiServiceAvailabilityTimeSlotsOptions): Promise<NonNullablePaths<ListMultiServiceAvailabilityTimeSlotsResponse, `timeSlots` | `timeSlots.${number}.location.locationType`, 5> & {
        __applicationErrorsType?: ListMultiServiceAvailabilityTimeSlotsApplicationErrors;
    }>;
}
declare function getMultiServiceAvailabilityTimeSlot$1(httpClient: HttpClient): GetMultiServiceAvailabilityTimeSlotSignature;
interface GetMultiServiceAvailabilityTimeSlotSignature {
    /**
     * Retrieves a multi-service time slot that matches the specified filters.
     *
     *
     * Call this method after finding a suitable slot with List Multi-Service Availability Time Slots to obtain full capacity, resource, and booking-policy details.
     *
     * The returned time slot acts as a container spanning the entire service sequence, with nested time slots providing detailed information for each individual service.
     *
     * ## Defaults
     *
     * - Returns all available resources unless you filter by `services.resourceIds` or `services.includeResourceTypeIds`.
     * - Includes full booking-status and capacity details.
     *
     * ## Service type limitations
     *
     * Only appointment-type services are supported.
     *
     * To retrieve appointment availability for a single service, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).
     * @param - Services for which the multi-service time slots are returned.
     * You can specify resource filters for each service.
     * @param - Local start date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @param - Local end date of the time slot in `YYYY-MM-DDThh:mm:ss` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * For example, `2026-01-30T13:30:00`.
     * @param - Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) for adjusting `fromLocalDate` and `toLocalDate` values.
     * For example, `America/New_York` or `UTC`.
     *
     * Default: `timeZone` specified in the business site properties ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/site-properties/get-site-properties) | [REST](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties)).
     * @param - Location for which the multi-service time slots are returned. If you filter by `{"type": "BUSINESS"}`, you must also specify a location ID. A filter for `location.name` is ignored.
     *
     * Required unless you specify `cursorPaging.cursor`.
     */
    (services: NonNullablePaths<Service, `serviceId`, 2>[], localStartDate: string, localEndDate: string, timeZone: string, location: NonNullablePaths<Location, `locationType`, 2>): Promise<NonNullablePaths<GetMultiServiceAvailabilityTimeSlotResponse, `timeSlot.location.locationType` | `timeSlot.availableResources` | `timeSlot.nestedTimeSlots` | `timeSlot.nestedTimeSlots.${number}.serviceId` | `timeSlot.nestedTimeSlots.${number}.localStartDate` | `timeSlot.nestedTimeSlots.${number}.localEndDate`, 5> & {
        __applicationErrorsType?: GetMultiServiceAvailabilityTimeSlotApplicationErrors;
    }>;
}

declare const listMultiServiceAvailabilityTimeSlots: MaybeContext<BuildRESTFunction<typeof listMultiServiceAvailabilityTimeSlots$1> & typeof listMultiServiceAvailabilityTimeSlots$1>;
declare const getMultiServiceAvailabilityTimeSlot: MaybeContext<BuildRESTFunction<typeof getMultiServiceAvailabilityTimeSlot$1> & typeof getMultiServiceAvailabilityTimeSlot$1>;

export { GetMultiServiceAvailabilityTimeSlotApplicationErrors, GetMultiServiceAvailabilityTimeSlotResponse, ListMultiServiceAvailabilityTimeSlotsApplicationErrors, ListMultiServiceAvailabilityTimeSlotsOptions, ListMultiServiceAvailabilityTimeSlotsResponse, Location, Service, getMultiServiceAvailabilityTimeSlot, listMultiServiceAvailabilityTimeSlots };
