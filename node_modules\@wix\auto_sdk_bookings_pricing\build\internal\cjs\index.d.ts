import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { BookingLineItem, PreviewPriceResponse, PreviewPriceApplicationErrors, Booking, CalculatePriceResponse, CalculatePriceApplicationErrors } from './index.typings.js';
export { Actor, ActorWithLiterals, Address, AddressLocation, AddressStreetOneOf, BookedAddOn, BookedEntity, BookedEntityItemOneOf, BookedResource, BookedSchedule, BookedSlot, BookingParticipantsInfoOneOf, BookingSource, BookingStatus, BookingStatusWithLiterals, CalculatePriceRequest, ContactDetails, CustomFormField, Duration, ExtendedFields, FlowControlSettings, IdentificationData, IdentificationDataIdOneOf, IdentityType, IdentityTypeWithLiterals, Location, LocationType, LocationTypeWithLiterals, MultiServiceBookingInfo, MultiServiceBookingType, MultiServiceBookingTypeWithLiterals, ParticipantChoices, ParticipantNotification, PaymentStatus, PaymentStatusWithLiterals, Platform, PlatformWithLiterals, PreviewPriceRequest, PriceDescriptionInfo, PriceInfo, PriceInfoTotalPriceOneOf, PricingServiceChoices, SelectedPaymentOption, SelectedPaymentOptionWithLiterals, ServiceChoice, ServiceChoiceChoiceOneOf, ServiceChoices, StreetAddress, Subdivision, ValueType, ValueTypeWithLiterals } from './index.typings.js';

declare function previewPrice$1(httpClient: HttpClient): PreviewPriceSignature;
interface PreviewPriceSignature {
    /**
     * Previews the base price for a set of line items belonging to the same
     * service, before a booking is created. During the booking flow, additional
     * taxes and fees may be added to the base price.
     *
     *
     * ## Response
     *
     * The response includes each line item's individual preview price and the
     * total of all line item preview prices. Note that the final price upon
     * booking creation may differ from the preview price.
     *
     * ## Errors
     *
     * _Preview Price_ fails if:
     *
     * - You specify line items that belong to different *services*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)).
     * - The site is using the *Bookings Pricing Integration SPI*
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)).
     *
     * ## When to call Calculate Price instead
     *
     * To retrieve the price of an existing booking, call *Calculate Price*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/calculate-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/calculate-price)).
     * @param - List of line items to preview the price for.
     */
    (bookingLineItems: BookingLineItem[]): Promise<NonNullablePaths<PreviewPriceResponse, `priceInfo.calculatedPrice` | `priceInfo.priceDescription` | `priceInfo.priceDescriptionInfo.original` | `priceInfo.bookingLineItems`, 4> & {
        __applicationErrorsType?: PreviewPriceApplicationErrors;
    }>;
}
declare function calculatePrice$1(httpClient: HttpClient): CalculatePriceSignature;
interface CalculatePriceSignature {
    /**
     * Calculates the base price of a booking.
     *
     *
     * The returned price serves as the foundation for charging the customer. During the
     * _eCommerce checkout_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/introduction)),
     * additional taxes and fees may be added to this base price.
     *
     * ## Price calculation method
     *
     * By default, Wix Bookings calculates a booking's price based on the relevant
     * `serviceOptionsAndVariants.variants.values.price`
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * You must then specify either `booking.bookedEntity.slot.serviceId` or
     * `booking.bookedEntity.schedule.scheduleId`.
     *
     * If the business uses the *Wix Bookings Pricing Integration service plugin*
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/introduction)),
     * the returned `priceInfo` object reflects values received from the SPI implementor in
     * _Calculate Price_
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-integration-service-plugin/calculate-price)).
     * In this case, it suffices to specify `booking.bookedEntity`.
     *
     * ## When to call Preview Price instead
     *
     * To estimate the price for multiple booking line items before the booking exists,
     * call *Preview Price*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/pricing/preview-price) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/pricing-api/preview-price)).
     * @param - _Booking_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/introduction))
     * to calculate the base price for.
     */
    (booking: Booking): Promise<NonNullablePaths<CalculatePriceResponse, `priceInfo.calculatedPrice` | `priceInfo.priceDescription` | `priceInfo.priceDescriptionInfo.original` | `priceInfo.bookingLineItems`, 4> & {
        __applicationErrorsType?: CalculatePriceApplicationErrors;
    }>;
}

declare const previewPrice: MaybeContext<BuildRESTFunction<typeof previewPrice$1> & typeof previewPrice$1>;
declare const calculatePrice: MaybeContext<BuildRESTFunction<typeof calculatePrice$1> & typeof calculatePrice$1>;

export { Booking, BookingLineItem, CalculatePriceApplicationErrors, CalculatePriceResponse, PreviewPriceApplicationErrors, PreviewPriceResponse, calculatePrice, previewPrice };
