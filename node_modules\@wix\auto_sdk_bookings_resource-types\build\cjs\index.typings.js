"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.typings.ts
var index_typings_exports = {};
__export(index_typings_exports, {
  CreateResourceTypeErrors: () => CreateResourceTypeErrors,
  RequestedFields: () => RequestedFields,
  SortOrder: () => SortOrder,
  WebhookIdentityType: () => WebhookIdentityType,
  countResourceTypes: () => countResourceTypes2,
  createResourceType: () => createResourceType2,
  deleteResourceType: () => deleteResourceType2,
  getResourceType: () => getResourceType2,
  queryResourceTypes: () => queryResourceTypes2,
  updateResourceType: () => updateResourceType2
});
module.exports = __toCommonJS(index_typings_exports);

// src/bookings-resources-v2-resource-type-resource-types.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_query_builder = require("@wix/sdk-runtime/query-builder");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-resources-v2-resource-type-resource-types.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      },
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/resource-types",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_resource-types";
function createResourceType(payload) {
  function __createResourceType({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createResourceType;
}
function getResourceType(payload) {
  function __getResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "GET",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getResourceType;
}
function updateResourceType(payload) {
  function __updateResourceType({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "PATCH",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceType.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateResourceType;
}
function deleteResourceType(payload) {
  function __deleteResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "DELETE",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteResourceType;
}
function queryResourceTypes(payload) {
  function __queryResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceTypes.createdDate" },
            { path: "resourceTypes.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryResourceTypes;
}
function countResourceTypes(payload) {
  function __countResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countResourceTypes;
}

// src/bookings-resources-v2-resource-type-resource-types.universal.ts
var import_transform_paths2 = require("@wix/sdk-runtime/transformations/transform-paths");
var RequestedFields = /* @__PURE__ */ ((RequestedFields2) => {
  RequestedFields2["TOTAL_RESOURCE_COUNT"] = "TOTAL_RESOURCE_COUNT";
  RequestedFields2["SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS"] = "SPECIFIC_LOCATION_TYPE_RESOURCE_COUNTS";
  RequestedFields2["DISTINCT_RESOURCE_LOCATIONS"] = "DISTINCT_RESOURCE_LOCATIONS";
  return RequestedFields2;
})(RequestedFields || {});
var CreateResourceTypeErrors = /* @__PURE__ */ ((CreateResourceTypeErrors2) => {
  CreateResourceTypeErrors2["UNKNOWN_CREATE_RESOURCE_TYPE_ERROR"] = "UNKNOWN_CREATE_RESOURCE_TYPE_ERROR";
  CreateResourceTypeErrors2["FAILED_TO_CREATE_RESOURCES"] = "FAILED_TO_CREATE_RESOURCES";
  return CreateResourceTypeErrors2;
})(CreateResourceTypeErrors || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createResourceType2(resourceType) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    resourceType
  });
  const reqOpts = createResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.resourceType;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceType: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceType"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getResourceType2(resourceTypeId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    resourceTypeId
  });
  const reqOpts = getResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.resourceType;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceTypeId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceTypeId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateResourceType2(_id, resourceType) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    resourceType: { ...resourceType, id: _id }
  });
  const reqOpts = updateResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.resourceType;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: { resourceType: "$[1]" },
        explicitPathsToArguments: { "resourceType.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "resourceType"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteResourceType2(resourceTypeId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    resourceTypeId
  });
  const reqOpts = deleteResourceType(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { resourceTypeId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["resourceTypeId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryResourceTypes2() {
  const { httpClient, sideEffects } = arguments[0];
  return (0, import_query_builder.queryBuilder)({
    func: async (payload) => {
      const reqOpts = queryResourceTypes(
        payload
      );
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({
      data
    }) => {
      const transformedData = (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
        (0, import_transform_paths2.transformPaths)(data, [])
      );
      return {
        items: transformedData?.resourceTypes,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = (0, import_transform_error.transformError)(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countResourceTypes2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = countResourceTypes(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CreateResourceTypeErrors,
  RequestedFields,
  SortOrder,
  WebhookIdentityType,
  countResourceTypes,
  createResourceType,
  deleteResourceType,
  getResourceType,
  queryResourceTypes,
  updateResourceType
});
//# sourceMappingURL=index.typings.js.map