import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { ServiceOptionsAndVariants, CreateServiceOptionsAndVariantsApplicationErrors, CloneServiceOptionsAndVariantsResponse, GetServiceOptionsAndVariantsByServiceIdResponse, UpdateServiceOptionsAndVariants, UpdateServiceOptionsAndVariantsApplicationErrors, DeleteServiceOptionsAndVariantsOptions, ServiceOptionsAndVariantsListQueryBuilder, ServiceOptionsAndVariantsCreatedEnvelope, ServiceOptionsAndVariantsDeletedEnvelope, ServiceOptionsAndVariantsUpdatedEnvelope } from './index.typings.js';
export { ActionEvent, Address, AddressHint, BaseEventMetadata, BusinessSchedule, Categories, ChangeContext, ChangeContextPayloadOneOf, CloneServiceOptionsAndVariantsRequest, ConsentPolicy, CreateServiceOptionsAndVariantsRequest, CreateServiceOptionsAndVariantsResponse, CursorPaging, Cursors, CustomServiceOption, DayOfWeek, DayOfWeekWithLiterals, DeleteServiceOptionsAndVariantsRequest, DeleteServiceOptionsAndVariantsResponse, DomainEvent, DomainEventBodyOneOf, Duration, DurationServiceOption, Empty, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, EventMetadata, ExtendedFields, GeoCoordinates, GetServiceOptionsAndVariantsByServiceIdRequest, GetServiceOptionsAndVariantsRequest, GetServiceOptionsAndVariantsResponse, IdentificationData, IdentificationDataIdOneOf, Locale, MessageEnvelope, Money, Multilingual, Paging, PagingMetadataV2, PlacementType, PlacementTypeWithLiterals, Properties, PropertiesChange, QueryServiceOptionsAndVariantsRequest, QueryServiceOptionsAndVariantsResponse, QueryV2, QueryV2PagingMethodOneOf, ResolutionMethod, ResolutionMethodWithLiterals, RestoreInfo, ServiceChoice, ServiceChoiceChoiceOneOf, ServiceOption, ServiceOptionOptionSpecificDataOneOf, ServiceOptionType, ServiceOptionTypeWithLiterals, ServiceOptions, ServiceOptionsAndVariantsListQueryResult, ServiceVariant, ServiceVariants, SiteCloned, SiteCreated, SitePropertiesEvent, SitePropertiesNotification, SortOrder, SortOrderWithLiterals, Sorting, SpecialHourPeriod, SupportedLanguage, TimePeriod, Translation, UpdateServiceOptionsAndVariantsRequest, UpdateServiceOptionsAndVariantsResponse, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.js';

declare function createServiceOptionsAndVariants$1(httpClient: HttpClient): CreateServiceOptionsAndVariantsSignature;
interface CreateServiceOptionsAndVariantsSignature {
    /**
     * Creates a `serviceOptionsAndVariants` object and for a service.
     *
     *
     * ## Calculate variants
     *
     * Before creating a `serviceOptionsAndVariants` object, you need to
     * anticipate and manually define all its variants, since Wix Bookings doesn't
     * automatically calculate them. For the actual
     * Create Service Options And Variants* call, specify both the `options` and
     * `variants` arrays.
     *
     * ## Limitations
     *
     * Wix Bookings allows you to connect only a single `serviceOptionsAndVariants`
     * object to a service. *Create Service Options And Variants* fails, if the
     * service already has a connected `serviceOptionsAndVariants` object.
     *
     * Currently, you can include only a single option per
     * `serviceOptionsAndVariants` object. Taken together, this means that services
     * are limited to a single option.
     *
     * ## Option ID
     *
     * When creating a`serviceOptionsAndVariants` object, you must specify an ID in
     * [UUID format](https://en.wikipedia.org/wiki/Universally_unique_identifier)
     * for its only option. You must reference this option ID for each variant as
     * `variants.values.choices.optionId`.
     *
     * ## Staff member option
     *
     * To creating an option based on the *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service, you need to specify `STAFF_MEMBER` as `options.values.type`.
     * Also, specify all staff member IDs as `variants.values.choices.staffMemberId`.
     * You could follow this *sample flow*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-staff-member-based-service-variants) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-staff-member-based-service-variants)).
     *
     * ## Custom option
     *
     * To create an option based on a custom parameter, specify `CUSTOM` as
     * `options.values.type`. Provide descriptive names for all custom choices as
     * `variants.values.choices.custom`. These names are displayed to customers
     * during the book flow. You could follow this *sample flow*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment)).
     *
     * ## Duration option
     *
     * To create an option based on appointment duration, specify `DURATION` as
     * `options.values.type` and set a descriptive name in `options.values.durationData.name`.
     * Also, indicate the appointment length in `minutes` and provide a descriptive
     * `name` for each duration choice in `variants.values.choices.duration`.
     * @param - Service options and variants to create.
     * @returns Information about the created service options and variants.
     */
    (serviceOptionsAndVariants: NonNullablePaths<ServiceOptionsAndVariants, `options` | `serviceId` | `variants`, 2>): Promise<NonNullablePaths<ServiceOptionsAndVariants, `options.values` | `options.values.${number}.customData.name` | `options.values.${number}._id` | `options.values.${number}.type` | `variants.values` | `variants.values.${number}.price.value` | `variants.values.${number}.price.currency`, 6> & {
        __applicationErrorsType?: CreateServiceOptionsAndVariantsApplicationErrors;
    }>;
}
declare function cloneServiceOptionsAndVariants$1(httpClient: HttpClient): CloneServiceOptionsAndVariantsSignature;
interface CloneServiceOptionsAndVariantsSignature {
    /**
     * Clones a `serviceOptionsAndVariants` object and connects it to a *service*
     * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).
     *
     *
     * The call fails if the service already has a connected
     * `serviceOptionsAndVariants` object.
     *
     * The cloned `serviceOptionsAndVariants` object gets a new, unique option ID.
     * The option ID of the existing `serviceOptionsAndVariants` object isn't reused.
     *
     * For example, you may call this method after *cloning a service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/clone-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/clone-service)).
     * @param - ID of the `serviceOptionsAndVariants` object to clone.
     * @param - ID of the service to which the cloned `serviceOptionsAndVariants` are
     * connected.
     */
    (cloneFromId: string, targetServiceId: string): Promise<NonNullablePaths<CloneServiceOptionsAndVariantsResponse, `serviceOptionsAndVariants.options.values` | `serviceOptionsAndVariants.options.values.${number}.customData.name` | `serviceOptionsAndVariants.options.values.${number}._id` | `serviceOptionsAndVariants.options.values.${number}.type` | `serviceOptionsAndVariants.variants.values` | `serviceOptionsAndVariants.variants.values.${number}.price.value` | `serviceOptionsAndVariants.variants.values.${number}.price.currency`, 7>>;
}
declare function getServiceOptionsAndVariants$1(httpClient: HttpClient): GetServiceOptionsAndVariantsSignature;
interface GetServiceOptionsAndVariantsSignature {
    /**
     * Retrieves a `serviceOptionsAndVariants` object by its ID.
     * @param - ID of the `serviceOptionsAndVariants` object to retrieve.
     * @returns Retrieved `serviceOptionsAndVariants` object.
     */
    (serviceOptionsAndVariantsId: string): Promise<NonNullablePaths<ServiceOptionsAndVariants, `options.values` | `options.values.${number}.customData.name` | `options.values.${number}._id` | `options.values.${number}.type` | `variants.values` | `variants.values.${number}.price.value` | `variants.values.${number}.price.currency`, 6>>;
}
declare function getServiceOptionsAndVariantsByServiceId$1(httpClient: HttpClient): GetServiceOptionsAndVariantsByServiceIdSignature;
interface GetServiceOptionsAndVariantsByServiceIdSignature {
    /**
     * Retrieves a `serviceOptionsAndVariants` object by *service ID*
     * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).
     * @param - ID of the service to retrieve options and variants for.
     */
    (serviceId: string): Promise<NonNullablePaths<GetServiceOptionsAndVariantsByServiceIdResponse, `serviceVariants.options.values` | `serviceVariants.options.values.${number}.customData.name` | `serviceVariants.options.values.${number}._id` | `serviceVariants.options.values.${number}.type` | `serviceVariants.variants.values` | `serviceVariants.variants.values.${number}.price.value` | `serviceVariants.variants.values.${number}.price.currency`, 7>>;
}
declare function updateServiceOptionsAndVariants$1(httpClient: HttpClient): UpdateServiceOptionsAndVariantsSignature;
interface UpdateServiceOptionsAndVariantsSignature {
    /**
     * Updates a `serviceOptionsAndVariants` object.
     *
     *
     * Currently, only a single option is supported per `serviceOptionsAndVariants` object.
     *
     * If you want to update `variants`, you must pass the entire list of supported
     * variants, not only newly added variants.
     *
     * If you want to update `options`, you must pass the entire list of supported
     * options, not only newly added options.
     * @param - ID of the `serviceOptionsAndVariants` object.
     * @param - Service options and variants to update.
     * @param - Options for updating the service options and variants.
     * @returns Updated `serviceOptionsAndVariants` object.
     */
    (_id: string, serviceOptionsAndVariants: NonNullablePaths<UpdateServiceOptionsAndVariants, `revision`, 2>): Promise<NonNullablePaths<ServiceOptionsAndVariants, `options.values` | `options.values.${number}.customData.name` | `options.values.${number}._id` | `options.values.${number}.type` | `variants.values` | `variants.values.${number}.price.value` | `variants.values.${number}.price.currency`, 6> & {
        __applicationErrorsType?: UpdateServiceOptionsAndVariantsApplicationErrors;
    }>;
}
declare function deleteServiceOptionsAndVariants$1(httpClient: HttpClient): DeleteServiceOptionsAndVariantsSignature;
interface DeleteServiceOptionsAndVariantsSignature {
    /**
     * Deletes a `serviceOptionsAndVariants` object.
     *
     *
     * Because each service can be connected to only a single `serviceOptionsAndVariants`
     * object, the service doesn't support *varied pricing*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments#service-rates))
     * after deleting a `serviceOptionsAndVariants` object. Instead, Wix Bookings
     * uses its standard price calculation.
     * @param - ID of the `serviceOptionsAndVariants` object to delete.
     * @param - Options for deleting the service options and variants.
     */
    (serviceOptionsAndVariantsId: string, options?: DeleteServiceOptionsAndVariantsOptions): Promise<void>;
}
declare function queryServiceOptionsAndVariants$1(httpClient: HttpClient): QueryServiceOptionsAndVariantsSignature;
interface QueryServiceOptionsAndVariantsSignature {
    /**
     * Creates a query to retrieve a list of `serviceOptionsAndVariants` objects.
     *
     * The `queryServiceOptionsAndVariants()` function builds a query to retrieve a list of `serviceOptionsAndVariants` objects and returns a `ServiceOptionsAndVariantsQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-builder/find) function.
     *
     * You can refine the query by chaining `ServiceOptionsAndVariantsQueryBuilder` functions onto the query. `ServiceOptionsAndVariantsQueryBuilder` functions enable you to sort, filter, and control the results that `queryServiceOptionsAndVariants()` returns.
     *
     * `queryServiceOptionsAndVariants()` runs with the following `ServiceOptionsAndVariantsQueryBuilder` default that you can override:
     *
     * + `limit` is `50`.
     * + Sorted by `id` in ascending order.
     *
     * The functions that are chained to `queryServiceOptionsAndVariants()` are applied in the order they are called. For example, if you apply `ascending("options.values.type")` and then `ascending("variants.values.price")`, the results are sorted first by the `"type"`, and then, if there are multiple results with the same `"type"`, the items are sorted by `"price"`.
     *
     * The following `ServiceOptionsAndVariantsQueryBuilder` functions are supported for the `queryServiceOptionsAndVariants()` function. For a full description of the `serviceOptionsAndVariants` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-result/items) property in `ServiceOptionsAndVariantsQueryResult`.
     */
    (): ServiceOptionsAndVariantsListQueryBuilder;
}
declare const onServiceOptionsAndVariantsCreated$1: EventDefinition<ServiceOptionsAndVariantsCreatedEnvelope, "wix.bookings.catalog.v1.service_options_and_variants_created">;
declare const onServiceOptionsAndVariantsDeleted$1: EventDefinition<ServiceOptionsAndVariantsDeletedEnvelope, "wix.bookings.catalog.v1.service_options_and_variants_deleted">;
declare const onServiceOptionsAndVariantsUpdated$1: EventDefinition<ServiceOptionsAndVariantsUpdatedEnvelope, "wix.bookings.catalog.v1.service_options_and_variants_updated">;

declare const createServiceOptionsAndVariants: MaybeContext<BuildRESTFunction<typeof createServiceOptionsAndVariants$1> & typeof createServiceOptionsAndVariants$1>;
declare const cloneServiceOptionsAndVariants: MaybeContext<BuildRESTFunction<typeof cloneServiceOptionsAndVariants$1> & typeof cloneServiceOptionsAndVariants$1>;
declare const getServiceOptionsAndVariants: MaybeContext<BuildRESTFunction<typeof getServiceOptionsAndVariants$1> & typeof getServiceOptionsAndVariants$1>;
declare const getServiceOptionsAndVariantsByServiceId: MaybeContext<BuildRESTFunction<typeof getServiceOptionsAndVariantsByServiceId$1> & typeof getServiceOptionsAndVariantsByServiceId$1>;
declare const updateServiceOptionsAndVariants: MaybeContext<BuildRESTFunction<typeof updateServiceOptionsAndVariants$1> & typeof updateServiceOptionsAndVariants$1>;
declare const deleteServiceOptionsAndVariants: MaybeContext<BuildRESTFunction<typeof deleteServiceOptionsAndVariants$1> & typeof deleteServiceOptionsAndVariants$1>;
declare const queryServiceOptionsAndVariants: MaybeContext<BuildRESTFunction<typeof queryServiceOptionsAndVariants$1> & typeof queryServiceOptionsAndVariants$1>;
/** */
declare const onServiceOptionsAndVariantsCreated: BuildEventDefinition<typeof onServiceOptionsAndVariantsCreated$1>;
/** */
declare const onServiceOptionsAndVariantsDeleted: BuildEventDefinition<typeof onServiceOptionsAndVariantsDeleted$1>;
/** */
declare const onServiceOptionsAndVariantsUpdated: BuildEventDefinition<typeof onServiceOptionsAndVariantsUpdated$1>;

export { CloneServiceOptionsAndVariantsResponse, CreateServiceOptionsAndVariantsApplicationErrors, DeleteServiceOptionsAndVariantsOptions, GetServiceOptionsAndVariantsByServiceIdResponse, ServiceOptionsAndVariants, ServiceOptionsAndVariantsCreatedEnvelope, ServiceOptionsAndVariantsDeletedEnvelope, ServiceOptionsAndVariantsListQueryBuilder, ServiceOptionsAndVariantsUpdatedEnvelope, UpdateServiceOptionsAndVariants, UpdateServiceOptionsAndVariantsApplicationErrors, cloneServiceOptionsAndVariants, createServiceOptionsAndVariants, deleteServiceOptionsAndVariants, getServiceOptionsAndVariants, getServiceOptionsAndVariantsByServiceId, onServiceOptionsAndVariantsCreated, onServiceOptionsAndVariantsDeleted, onServiceOptionsAndVariantsUpdated, queryServiceOptionsAndVariants, updateServiceOptionsAndVariants };
