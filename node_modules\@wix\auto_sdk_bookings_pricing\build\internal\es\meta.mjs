// src/bookings-v2-price-info-pricing.http.ts
import { transformSDKFloatToRESTFloat } from "@wix/sdk-runtime/transformations/float";
import { transformRESTFloatToSDKFloat } from "@wix/sdk-runtime/transformations/float";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsPricingBookingsPricingServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-pricing",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings-pricing/v2/price",
        destPath: "/v2/pricing"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings-pricing",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings-pricing/v2/price",
        destPath: "/v2/pricing"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/pricing/v2/pricing",
        destPath: "/v2/pricing"
      },
      {
        srcPath: "/bookings/v2/pricing",
        destPath: "/v2/pricing"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_pricing";
function previewPrice(payload) {
  function __previewPrice({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "bookingLineItems.pricePerParticipant" },
          { path: "bookingLineItems.serviceChoices.pricePerParticipant" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.price_info",
      method: "POST",
      methodFqn: "com.wixpress.bookings.pricing.BookingsPricingService.PreviewPrice",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({
        protoPath: "/v2/pricing/preview",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "priceInfo.calculatedPrice" },
            { path: "priceInfo.deposit" },
            { path: "priceInfo.bookingLineItems.pricePerParticipant" },
            {
              path: "priceInfo.bookingLineItems.serviceChoices.pricePerParticipant"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __previewPrice;
}
function calculatePrice(payload) {
  function __calculatePrice({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "booking.createdDate" },
          { path: "booking.startDate" },
          { path: "booking.endDate" },
          { path: "booking.updatedDate" },
          { path: "booking.canceledDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "booking.contactDetails.fullAddress.geocode.latitude" },
          { path: "booking.contactDetails.fullAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v2.price_info",
      method: "POST",
      methodFqn: "com.wixpress.bookings.pricing.BookingsPricingService.CalculatePrice",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPricingBookingsPricingServiceUrl({
        protoPath: "/v2/pricing/calculate",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "priceInfo.calculatedPrice" },
            { path: "priceInfo.deposit" },
            { path: "priceInfo.bookingLineItems.pricePerParticipant" },
            {
              path: "priceInfo.bookingLineItems.serviceChoices.pricePerParticipant"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __calculatePrice;
}

// src/bookings-v2-price-info-pricing.meta.ts
function previewPrice2() {
  const payload = {};
  const getRequestOptions = previewPrice(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/pricing/preview",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function calculatePrice2() {
  const payload = {};
  const getRequestOptions = calculatePrice(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/pricing/calculate",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  calculatePrice2 as calculatePrice,
  previewPrice2 as previewPrice
};
//# sourceMappingURL=meta.mjs.map