import { SearchSpec, Search, NonNullablePaths } from '@wix/sdk-types';

/**
 * An individual providing services within Wix Bookings. Only staff members who
 * are also Wix users can manage their working hours in the dashboard.
 */
interface StaffMember {
    /**
     * Staff member ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Staff member name.
     * @minLength 1
     * @maxLength 40
     */
    name?: string | null;
    /**
     * Staff member's email address.
     * @maxLength 320
     * @format EMAIL
     */
    email?: string | null;
    /**
     * Staff member's phone number.
     * @maxLength 20
     * @format PHONE
     */
    phone?: string | null;
    /**
     * Description for the staff member. For example,
     * `Experienced nail technician specialized in gel and acrylic nails`.
     * @maxLength 500
     */
    description?: string | null;
    /** Staff media. */
    mainMedia?: MediaItem;
    /**
     * Staff member's *resource ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)),
     * identical to `resource.id`.
     * @readonly
     * @format GUID
     */
    resourceId?: string | null;
    /**
     * Details about the *resource object*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
     * associated with the staff member. Available only if you specify `RESOURCE_DETAILS`
     * in the `fields` array.
     * @readonly
     */
    resource?: Resource;
    /**
     * Identity of the Wix user associated with the staff member. Learn more about
     * _identities_
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).
     * @readonly
     */
    associatedWixIdentity?: AssociatedWixIdentity;
    /**
     * Revision number, which increments by 1 each time the staff member is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the staff member.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time the staff member was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Time the staff member was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /** Extensions enabling users to save custom data related to the staff member. */
    extendedFields?: ExtendedFields;
    /**
     * Tags allowing you to classify staff members. Learn more about *tags*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).
     */
    tags?: Tags;
}
interface MediaItem extends MediaItemMediaOneOf {
    /** Staff member's main image. */
    image?: string;
}
/** @oneof */
interface MediaItemMediaOneOf {
    /** Staff member's main image. */
    image?: string;
}
interface Resource {
    /**
     * ID of the *resource*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
     * associated with the staff member.
     * @format GUID
     */
    _id?: string | null;
    /**
     * Working hour *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * of the staff member. By default, identical the opening hours of the
     * business's *default location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * If the staff has custom working hours, identical to `eventsSchedule`.
     * @maxSize 1
     */
    workingHoursSchedules?: WorkingHoursSchedule[];
    /**
     * Event *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * of the staff member.
     */
    eventsSchedule?: EventSchedule;
    /**
     * Whether the staff member works according to business's *default location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))
     * opening hours.
     *
     * `false`: The staff has custom working hours.
     *
     * Default: `true`
     */
    usesDefaultWorkingHours?: boolean;
}
interface WorkingHoursSchedule {
    /**
     * ID of the working hour *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * associated with the staff member. Currently, each staff member can't have more than a
     * single working hour schedule. Learn more about
     * _how Wix Bookings uses the Calendar APIs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).
     * @format GUID
     */
    _id?: string | null;
    /**
     * Whether the working hour schedule is shared with the business. If this field
     * isn't available, the schedule isn't shared but specific for the staff member.
     */
    shared?: boolean;
}
interface EventSchedule {
    /**
     * ID of the event *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * associated with the staff member. Learn more about
     * _how Wix Bookings uses the Calendar APIs_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).
     * @format GUID
     */
    _id?: string | null;
}
interface LocationOptions {
    /**
     * Whether the resource is available in all *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in all business locations.
     * - `false`: The resource is available only in specific locations.
     *
     * Default: `false`
     */
    availableInAllLocations?: boolean | null;
    /** Details of resource availability in specific locations. */
    specificLocationOptions?: SpecificLocation;
}
interface SpecificLocation {
    /**
     * Whether the resource is available in *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in business locations.
     * - `false`: The resource isn't available in business locations.
     *
     * Default: `false`
     */
    availableInBusinessLocations?: boolean | null;
    /**
     * Information about the business locations where the resource is available.
     * Not returned, if the resource is available in either all business locations
     * or in no business location.
     * You can specify up to 100 business locations.
     * @maxSize 100
     */
    businessLocations?: BusinessLocation[];
}
interface BusinessLocation {
    /**
     * ID of the business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * @format GUID
     */
    locationId?: string | null;
}
/** A staff member resource can be associated with a Wix user via assignment of a permissions role in the business manager. */
interface AssociatedWixIdentity {
    /**
     * Information about the identity connected to the staff member. Available only
     * if the staff member is connected to a Wix user. Learn more about *identities*
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).
     */
    identificationData?: CommonIdentificationData;
    /**
     * Connection status.
     * @readonly
     * @deprecated Connection status.
     * @replacedBy connection
     * @targetRemovalDate 2024-12-01
     */
    connectionStatus?: AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals;
    /**
     * Connection status. Available only if you specify `ASSOCIATED_IDENTITY_STATUS`
     * in the `fields` array.
     * @readonly
     */
    connection?: Connection;
}
interface CommonIdentificationData extends CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /**
     * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system. Retrievable via the [Contacts API](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/introduction)
     * @format GUID
     */
    contactId?: string | null;
}
/** @oneof */
interface CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum IdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type IdentityTypeWithLiterals = IdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
declare enum AssociatedWixIdentityConnectionStatusEnumConnectionStatus {
    /** There is no information about the connection status. */
    UNKNOWN = "UNKNOWN",
    /** The Wix user is connected to the staff member. */
    CONNECTED = "CONNECTED",
    /** The Wix user must accept the invitation to join **.wix.com.** or the site. */
    PENDING = "PENDING",
    /** The invitation to join **.wix.com.** or the site has expired. */
    EXPIRED = "EXPIRED",
    /** The Wix user was disconnected from the staff member. */
    DISCONNECTED = "DISCONNECTED"
}
/** @enumType */
type AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals = AssociatedWixIdentityConnectionStatusEnumConnectionStatus | 'UNKNOWN' | 'CONNECTED' | 'PENDING' | 'EXPIRED' | 'DISCONNECTED';
interface Connection {
    /**
     * Connection status of the Wix user and the staff member.
     * @readonly
     */
    status?: AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals;
}
interface AssociatedConferencingProviders {
    /**
     * Conferencing accounts connected to the staff member.
     * @maxSize 10
     */
    items?: AssociatedConferencingProvider[];
}
interface AssociatedConferencingProvider {
    /**
     * Conferencing provider ID. For example, Zoom integration identifier.
     * @format GUID
     */
    _id?: string;
    /**
     * Provider name. For example, Google Meet.
     * @maxLength 255
     */
    name?: string;
    /** Connection status. */
    connectionStatus?: ConnectionStatusWithLiterals;
    /**
     * Conferencing account email. Might not match staff email.
     * @maxLength 320
     * @format EMAIL
     */
    accountEmail?: string | null;
}
declare enum ConnectionStatus {
    /** Provider is connected to the site and the user is authenticated. */
    CONNECTED = "CONNECTED",
    /** Provider is not connected to the site or the user is not authenticated. */
    DISCONNECTED = "DISCONNECTED"
}
/** @enumType */
type ConnectionStatusWithLiterals = ConnectionStatus | 'CONNECTED' | 'DISCONNECTED';
interface AssociatedConferencingAccounts {
    /**
     * Conferencing accounts connected to the staff member.
     * @maxSize 10
     */
    items?: AssociatedConferencingAccount[];
}
interface AssociatedConferencingAccount extends AssociatedConferencingAccountAccountOneOf {
    /**
     * Conferencing for the staff member, configured via an external provider.
     * For example, a Zoom integration generating a link for sessions with the staff member.
     */
    providerAccount?: AssociatedConferencingProvider;
    /** Conferencing for the staff member, configured via a custom conference link. */
    customAccount?: CustomConferenceAccount;
}
/** @oneof */
interface AssociatedConferencingAccountAccountOneOf {
    /**
     * Conferencing for the staff member, configured via an external provider.
     * For example, a Zoom integration generating a link for sessions with the staff member.
     */
    providerAccount?: AssociatedConferencingProvider;
    /** Conferencing for the staff member, configured via a custom conference link. */
    customAccount?: CustomConferenceAccount;
}
interface CustomConferenceAccount {
    /**
     * Conferencing URL. For example, a Google Meet link.
     * @maxLength 2083
     */
    url?: string | null;
    /**
     * Optional password that protects the conferencing link.
     * @maxLength 320
     */
    password?: string | null;
    /**
     * Optional description of the conference link.
     * @maxLength 3000
     */
    description?: string | null;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
/**
 * Common object for tags.
 * Should be use as in this example:
 * message Foo {
 * string id = 1;
 * ...
 * Tags tags = 5
 * }
 *
 * example of taggable entity
 * {
 * id: "123"
 * tags: {
 * tags: {
 * tag_ids:["11","22"]
 * },
 * private_tags: {
 * tag_ids: ["33", "44"]
 * }
 * }
 * }
 */
interface Tags {
    /** Tags that require an additional permission in order to access them, normally not given to site members or visitors. */
    privateTags?: TagList;
    /** Tags that are exposed to anyone who has access to the labeled entity itself, including site members and visitors. */
    tags?: TagList;
}
interface TagList {
    /**
     * List of tag IDs
     * @maxSize 100
     * @maxLength 5
     */
    tagIds?: string[];
}
interface StaffMemberDisconnectedFromUser {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface StaffMemberConnectedToUser {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
/**
 * This message is used to notify that a staff member has been fully created. For now it is only needed for
 * producing the correct Resources V1 events. A decision will be made later whether to keep it or not.
 */
interface StaffMemberFullyCreated {
    /** Created staff member. */
    staffMember?: StaffMember;
}
interface CreateStaffMemberRequest {
    /** Staff member to create. */
    staffMember: StaffMember;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
declare enum RequestedFields {
    /** Includes `resource` in the response. */
    RESOURCE_DETAILS = "RESOURCE_DETAILS",
    /** Includes `associatedIdentityStatus` in the response. */
    ASSOCIATED_IDENTITY_STATUS = "ASSOCIATED_IDENTITY_STATUS"
}
/** @enumType */
type RequestedFieldsWithLiterals = RequestedFields | 'RESOURCE_DETAILS' | 'ASSOCIATED_IDENTITY_STATUS';
interface CreateStaffMemberResponse {
    /** Created staff member. */
    staffMember?: StaffMember;
}
interface GetStaffMemberRequest {
    /**
     * ID of the staff member to retrieve.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface GetStaffMemberResponse {
    /** Retrieved staff member. */
    staffMember?: StaffMember;
}
interface GetDeletedStaffMemberRequest {
    /**
     * ID of the deleted staff member to retrieve.
     * @format GUID
     */
    staffMemberId?: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface GetDeletedStaffMemberResponse {
    /** Retrieved staff member. */
    staffMember?: StaffMember;
}
interface ListDeletedStaffMembersRequest {
    /**
     * IDs of the deleted staff members to retrieve.
     *
     * Default: All deleted staff members are returned, given the provided paging.
     * @format GUID
     * @maxSize 100
     */
    staffMemberIds?: string[];
    /** Paging details. */
    paging?: CursorPaging;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface ListDeletedStaffMembersResponse {
    /** Retrieved staff members. */
    staffMembers?: StaffMember[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /**
     * Number of items returned in the current response page.
     *
     * This count reflects the actual number of items in the current result set,
     * which may be less than the requested limit if fewer items are available.
     */
    count?: number | null;
    /**
     * Navigation cursors for moving between result pages.
     *
     * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor
     * to retrieve subsequent pages and `prev` cursor to go back to previous pages.
     * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).
     */
    cursors?: Cursors;
    /**
     * Indicates whether additional results are available beyond the current page.
     *
     * - `true`: More results exist and can be retrieved using the `next` cursor.
     * - `false`: This is the final page of results.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor token for retrieving the next page of results.
     *
     * Use this token in subsequent requests to continue pagination forward.
     * Value is `null` when on the last page of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor token for retrieving the previous page of results.
     *
     * Use this token to navigate backwards through result pages.
     * Value is `null` when on the first page of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface RemoveStaffMemberFromTrashBinRequest {
    /**
     * ID of the staff member to delete permanently.
     * @format GUID
     */
    staffMemberId?: string;
}
interface RemoveStaffMemberFromTrashBinResponse {
}
interface RestoreStaffMemberFromTrashBinRequest {
    /**
     * ID of the staff member to restore from the trash bin.
     * @format GUID
     */
    staffMemberId?: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface RestoreStaffMemberFromTrashBinResponse {
    /** Restored staff member. */
    staffMember?: StaffMember;
}
interface UpdateStaffMemberRequest {
    /** Staff member to update. */
    staffMember: StaffMember;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface UpdateStaffMemberResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface DeleteStaffMemberRequest {
    /**
     * ID of the staff member to delete.
     * @format GUID
     */
    staffMemberId: string;
}
interface DeleteStaffMemberResponse {
}
interface QueryStaffMembersRequest {
    /**
     * Information about filters, paging, and sorting. See the article about
     * booking policy filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for all supported filters and sorting options.
     */
    query?: CursorQuery;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 10
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface QueryStaffMembersResponse {
    /** Retrieved staff members. */
    staffMembers?: StaffMember[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface QueryStaffMembersMultiLanguageRequest {
    /**
     * Information about filters, paging, and sorting. See the article about
     * booking policy filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for all supported filters and sorting options.
     */
    query?: CursorQuery;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface QueryStaffMembersMultiLanguageResponse {
    /** Retrieved staff members. */
    staffMembers?: StaffMember[];
    /** The retrieved staff members in the requested language according to the provided linguist aspect. */
    translatedStaffMembers?: StaffMember[];
    /** Paging metadata, including offset and count. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CountStaffMembersRequest {
    /**
     * Filter to base the count upon. See the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
}
interface CountStaffMembersResponse {
    /** Total number of staff members matching the given filter. */
    count?: number;
}
interface ConnectStaffMemberToUserRequest {
    /**
     * ID of the staff member to connect to the Wix user.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * Email of the Wix user to connect to the staff member.
     *
     * Default: Email of the staff member.
     * @maxLength 320
     * @format EMAIL
     */
    email?: string | null;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface ConnectStaffMemberToUserResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface SearchStaffMembersRequest {
    /**
     * Search criteria including filter, sort, and paging options.
     *
     * See the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)) for complete filter and sorting options.
     */
    search: CursorSearch;
    /**
     * Conditional fields to return in the response.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface CursorSearch extends CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. Can't be used together with 'paging'.
     * `cursor_paging.cursor` can't be used together with `filter` or `sort`.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object for narrowing search results. For example, to return only staff members with specific email domains: `"filter": {"email": {"$contains": "@company.com"}}`.
     *
     * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).
     */
    filter?: Record<string, any> | null;
    /**
     * Array of sort objects specifying result order. For example, to sort by creation date in descending order: `"sort": [{"fieldName": "createdDate", "order": "DESC"}]`.
     *
     * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).
     * @maxSize 10
     */
    sort?: Sorting[];
    /** Free text to match in searchable fields. */
    search?: SearchDetails;
}
/** @oneof */
interface CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. Can't be used together with 'paging'.
     * `cursor_paging.cursor` can't be used together with `filter` or `sort`.
     */
    cursorPaging?: CursorPaging;
}
interface SearchDetails {
    /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */
    mode?: ModeWithLiterals;
    /**
     * Search term or expression.
     * @maxLength 100
     */
    expression?: string | null;
    /**
     * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `resource.workingHoursSchedules.shared`.
     * @maxLength 200
     * @maxSize 20
     */
    fields?: string[];
    /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */
    fuzzy?: boolean;
}
declare enum Mode {
    /** At least 1 of the search terms must be present. */
    OR = "OR",
    /** All search terms must be present. */
    AND = "AND"
}
/** @enumType */
type ModeWithLiterals = Mode | 'OR' | 'AND';
interface SearchStaffMembersResponse {
    /**
     * Retrieved staff members that match the search criteria specified in the request.
     *
     * Each staff member includes their profile information such as name, email, phone number,
     * working hours schedule, connected user details, and any conditional fields requested
     * (resource details, conferencing providers, or identity status).
     */
    staffMembers?: StaffMember[];
    /**
     * Cursor-based paging metadata for navigating through search results.
     *
     * Contains pagination details including current cursor position, availability of additional
     * results, and item counts. Use the `next` cursor to retrieve subsequent result pages.
     * Note: Staff members don't support aggregations, so only cursor and count information is provided.
     */
    pagingMetadata?: CursorPagingMetadata;
    /**
     * Aggregation data results (currently reserved for future use).
     *
     * Staff member searches don't currently support aggregations. This field is reserved
     * for potential future aggregation capabilities such as grouping by working hours,
     * location assignments, or user connection status.
     */
    aggregationData?: AggregationData;
}
interface AggregationData {
    /**
     * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.
     * @maxSize 10000
     */
    results?: AggregationResults[];
}
interface ValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 100
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number;
}
interface RangeAggregationResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number;
}
declare enum ScalarType {
    UNKNOWN_SCALAR_TYPE = "UNKNOWN_SCALAR_TYPE",
    /** Total number of distinct values. */
    COUNT_DISTINCT = "COUNT_DISTINCT",
    /** Minimum value. */
    MIN = "MIN",
    /** Maximum value. */
    MAX = "MAX"
}
/** @enumType */
type ScalarTypeWithLiterals = ScalarType | 'UNKNOWN_SCALAR_TYPE' | 'COUNT_DISTINCT' | 'MIN' | 'MAX';
interface NestedAggregationResults extends NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /**
     * User-defined name of aggregation, matches the one specified in request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that matches result. */
    type?: AggregationTypeWithLiterals;
    /**
     * Field to aggregate by, matches the one specified in request.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
}
declare enum AggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR"
}
/** @enumType */
type AggregationTypeWithLiterals = AggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR';
interface ValueResults {
    /**
     * Array of value aggregation results, each containing a field value and the count of entities with that value.
     * @maxSize 250
     */
    results?: ValueAggregationResult[];
}
interface RangeResults {
    /**
     * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.
     * @maxSize 50
     */
    results?: RangeAggregationResult[];
}
interface AggregationResultsScalarResult {
    /** Type of scalar aggregation. */
    type?: ScalarTypeWithLiterals;
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Nested aggregations result data. */
    nestedResults?: NestedAggregationResults;
}
interface ValueResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number | null;
}
interface RangeResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number | null;
}
interface ScalarResult {
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedResultValue extends NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
/** @oneof */
interface NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
interface Results {
    /** Map of nested aggregation results, keyed by aggregation name. */
    results?: Record<string, NestedResultValue>;
}
interface DateHistogramResult {
    /**
     * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * @maxLength 100
     */
    value?: string;
    /** Count of documents in the bucket. */
    count?: number;
}
interface GroupByValueResults {
    /**
     * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.
     * @maxSize 1000
     */
    results?: NestedValueAggregationResult[];
}
interface DateHistogramResults {
    /**
     * Array of date histogram aggregation results, each containing a date bucket and its count.
     * @maxSize 200
     */
    results?: DateHistogramResult[];
}
/**
 * Results of `NESTED` aggregation type in a flattened form.
 * Aggregations in resulting array are keyed by requested aggregation `name`.
 */
interface NestedResults {
    /**
     * Array of nested aggregation result groups, each containing multiple aggregation results.
     * @maxSize 1000
     */
    results?: Results[];
}
interface AggregationResults extends AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
    /**
     * User-defined name of aggregation as derived from search request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that must match specified kind as derived from search request. */
    type?: AggregationTypeWithLiterals;
    /**
     * Field to aggregate by as derived from search request.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
}
interface DisconnectStaffMemberFromUserRequest {
    /**
     * ID of the staff member to disconnect from its Wix user.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface DisconnectStaffMemberFromUserResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface AssignWorkingHoursScheduleRequest {
    /**
     * ID of the staff member to assign the schedule to.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to assign to the staff member.
     *
     * Must be either the staff member's event schedule ID or the working hour
     * schedule ID for a business location.
     * @format GUID
     */
    scheduleId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface AssignWorkingHoursScheduleResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface AssignCustomScheduleRequest {
    /**
     * ID of the staff member for which to assign a working hour schedule.
     * @format GUID
     */
    staffMemberId: string;
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to assign to the staff member.
     * @format GUID
     */
    scheduleId: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface AssignCustomScheduleResponse {
    /** Updated staff member. */
    staffMember?: StaffMember;
}
interface BulkUpdateStaffMemberTagsRequest {
    /**
     * IDs of staff members to update tags for.
     * @minSize 1
     * @maxSize 100
     * @format GUID
     */
    ids: string[];
    /** Tags to add to the staff members. */
    assignTags?: Tags;
    /** Tags to remove from the staff members. */
    unassignTags?: Tags;
}
interface BulkUpdateStaffMemberTagsResponse {
    /**
     * List of update results.
     * @minSize 1
     * @maxSize 100
     */
    results?: BulkUpdateStaffMemberTagsResult[];
    /** Bulk action metadata. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface ItemMetadata {
    /**
     * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).
     * @format GUID
     */
    _id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkUpdateStaffMemberTagsResult {
    /** Metadata about an individual update operation. */
    itemMetadata?: ItemMetadata;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface BulkUpdateStaffMemberTagsByFilterRequest {
    /**
     * Filter to base the update upon. See the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter: Record<string, any> | null;
    /** Tags to add to the staff members. */
    assignTags?: Tags;
    /** Tags to remove from the staff members. */
    unassignTags?: Tags;
}
interface BulkUpdateStaffMemberTagsByFilterResponse {
    /**
     * Job ID for the bulk update operation.
     * @format GUID
     */
    jobId?: string;
}
interface RestoreStaffRequest {
    /**
     * ID of the staff member to restore.
     * @format GUID
     */
    staffMemberId?: string;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface RestoreStaffResponse {
    /** Restored staff member. */
    staffMember?: StaffMember;
}
interface Empty {
}
interface PolicyRemovedFromContributor {
    /** @format GUID */
    accountId?: string;
    /** @format GUID */
    metaSiteId?: string;
    policyIds?: string[];
}
interface PolicyUpdatedForContributor {
    /** @format GUID */
    accountId?: string;
    /** @format GUID */
    metaSiteId?: string;
    oldPolicyIds?: string[];
    newPolicyIds?: string[];
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface ScheduleNotification extends ScheduleNotificationEventOneOf {
    scheduleCreated?: ScheduleCreated;
    scheduleUpdated?: ScheduleUpdated;
    scheduleCancelled?: ScheduleCancelled;
    sessionCreated?: SessionCreated;
    sessionUpdated?: SessionUpdated;
    sessionCancelled?: SessionCancelled;
    availabilityPolicyUpdated?: AvailabilityPolicyUpdated;
    /** @deprecated */
    intervalSplit?: IntervalSplit;
    recurringSessionSplit?: RecurringSessionSplit;
    /**
     * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.
     * @deprecated
     */
    scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;
    preserveFutureSessionsWithParticipants?: boolean | null;
    /**
     * Whether to notify participants about changed sessions. deprecated, use participant_notification
     * @deprecated
     */
    notifyParticipants?: boolean;
    /** site properties. Optional. Given in create schedule notification. */
    siteProperties?: SitePropertiesOnScheduleCreation;
    instanceId?: string;
}
/** @oneof */
interface ScheduleNotificationEventOneOf {
    scheduleCreated?: ScheduleCreated;
    scheduleUpdated?: ScheduleUpdated;
    scheduleCancelled?: ScheduleCancelled;
    sessionCreated?: SessionCreated;
    sessionUpdated?: SessionUpdated;
    sessionCancelled?: SessionCancelled;
    availabilityPolicyUpdated?: AvailabilityPolicyUpdated;
    /** @deprecated */
    intervalSplit?: IntervalSplit;
    recurringSessionSplit?: RecurringSessionSplit;
    /**
     * Inspect `schedule.scheduleOwnerUserId` on `scheduleUpdated` instead.
     * @deprecated
     */
    scheduleUnassignedFromUser?: ScheduleUnassignedFromUser;
}
interface ScheduleCreated {
    schedule?: Schedule;
}
interface Schedule {
    /** Schedule ID. */
    _id?: string;
    /** ID of the schedule's owner entity. This may be a resource ID or a service ID. */
    scheduleOwnerId?: string | null;
    /**
     * Schedule's time zone in [Area/Location](https://en.wikipedia.org/wiki/Tz_database) format. Read-only.
     * Derived from the Wix Business time zone.
     * @readonly
     */
    timeZone?: string | null;
    /**
     * Deprecated. Please use the [Sessions API](https://dev.wix.com/api/rest/wix-bookings/schedules-and-sessions/session) instead.
     * @deprecated
     */
    intervals?: RecurringInterval[];
    /**
     * Default title for the schedule's sessions. Maximum length: 6000 characters.
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * __Deprecated.__
     * Tags for grouping schedules. These tags are the default tags for the schedule's sessions.
     * The Wix Bookings app uses the following predefined tags to set schedule type: `"INDIVIDUAL"`, `"GROUP"`, and `"COURSE"`. Once the schedule type is set using these tags, you cannot update it. In addition to the app's tags, you can create and update your own tags.
     * @deprecated
     */
    tags?: string[] | null;
    /** Default location for the schedule's sessions. */
    location?: Location;
    /**
     * Maximum number of participants that can be added to the schedule's sessions.
     * Must be at most `1` for schedule whose availability is affected by another schedule. E.g, appointment schedules of the Wix Bookings app.
     * @min 1
     * @max 1000
     */
    capacity?: number | null;
    /**
     * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.
     * @deprecated
     */
    rate?: Rate;
    /**
     * __Deprecated.__
     * @deprecated
     */
    availability?: Availability;
    /**
     * Number of participants registered to sessions in this schedule, calculated as the sum of the party sizes.
     * @readonly
     */
    totalNumberOfParticipants?: number;
    /**
     * *Partial list** of participants which are registered to sessions in this schedule.
     * Participants who are registered in the schedule are automatically registered to any session that is created for the schedule.
     * To retrieve the full list of schedule participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).
     * @readonly
     */
    participants?: Participant[];
    /**
     * __Deprecated.__
     * @deprecated
     */
    externalCalendarOverrides?: ExternalCalendarOverrides;
    /**
     * Schedule status. Default: Created
     * @readonly
     */
    status?: ScheduleStatusWithLiterals;
    /**
     * Schedule creation date.
     * @readonly
     */
    created?: Date | null;
    /**
     * Schedule last update date.
     * @readonly
     */
    updated?: Date | null;
    /**
     * Schedule version number, updated each time the schedule is updated.
     * @readonly
     */
    version?: number;
    /**
     * Fields which were inherited from the Business Info page under Settings in the Dashboard.
     * @readonly
     */
    inheritedFields?: string[];
    /**
     * __Deprecated.__
     * @deprecated
     */
    conferenceProvider?: ConferenceProvider;
    /**
     * A conference created for the schedule. This is used when a participant is added to a schedule.
     * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.
     * @deprecated
     */
    calendarConference?: CalendarConference;
}
interface RecurringInterval {
    /**
     * The recurring interval identifier.
     * @readonly
     */
    _id?: string;
    /** The start time of the recurring interval. Required. */
    start?: Date | null;
    /** The end time of the recurring interval. Optional. Empty value indicates that there is no end time. */
    end?: Date | null;
    /** The interval rules. The day, hour and minutes the interval is recurring. */
    interval?: Interval;
    /** The frequency of the interval. Optional. The default is frequency with the default repetition. */
    frequency?: Frequency;
    /** Specifies the list of linked schedules and the way this link affects the corresponding schedules' availability. Can be calculated from the schedule or overridden on the recurring interval. */
    affectedSchedules?: LinkedSchedule[];
    /** The type of recurring interval. */
    intervalType?: RecurringIntervalTypeWithLiterals;
}
interface Interval {
    /** The day the interval occurs. Optional. The default is the day of the recurring interval's start time. */
    daysOfWeek?: DayWithLiterals;
    /**
     * The hour of the day the interval occurs. Must be consistent with the interval start time. Optional. The default is 0. Minimum: 0, maximum: 23.
     * @max 23
     */
    hourOfDay?: number | null;
    /**
     * The minutes of the hour the interval accrues. Must be consistent with the interval end time. Optional. The default is 0. Minimum: 0, maximum: 59.
     * @max 59
     */
    minuteOfHour?: number | null;
    /** The duration of the interval in minutes. Required. Part of the session end time calculation. */
    duration?: number;
}
declare enum Day {
    /** Undefined. */
    UNDEFINED = "UNDEFINED",
    /** Monday. */
    MON = "MON",
    /** Tuesday. */
    TUE = "TUE",
    /** Wednesday. */
    WED = "WED",
    /** Thursday. */
    THU = "THU",
    /** Friday. */
    FRI = "FRI",
    /** Saturday. */
    SAT = "SAT",
    /** Sunday. */
    SUN = "SUN"
}
/** @enumType */
type DayWithLiterals = Day | 'UNDEFINED' | 'MON' | 'TUE' | 'WED' | 'THU' | 'FRI' | 'SAT' | 'SUN';
interface Frequency {
    /**
     * The frequency of the recurrence in weeks. i.e. when this value is 4, the interval occurs every 4 weeks. Optional. The default is 1. minimum: 1, maximum: 52.
     * @min 1
     * @max 52
     */
    repetition?: number | null;
}
interface LinkedSchedule {
    /**
     * Schedule ID.
     * @format GUID
     */
    scheduleId?: string;
    /** Sets this schedule's availability for the duration of the linked schedule's sessions. Default is `"BUSY"`. */
    transparency?: TransparencyWithLiterals;
    /**
     * Owner ID, of the linked schedule.
     * @readonly
     */
    scheduleOwnerId?: string;
}
declare enum Transparency {
    UNDEFINED = "UNDEFINED",
    /** The schedule can have available slots during the linked schedule's sessions. */
    FREE = "FREE",
    /** The schedule can't have available slots during the linked schedule's sessions. */
    BUSY = "BUSY"
}
/** @enumType */
type TransparencyWithLiterals = Transparency | 'UNDEFINED' | 'FREE' | 'BUSY';
declare enum RecurringIntervalType {
    /** The default value. Sessions for this interval will be of type EVENT. */
    UNDEFINED = "UNDEFINED",
    /** A recurring interval of events. */
    EVENT = "EVENT",
    /** Deprecated. */
    TIME_AVAILABILITY = "TIME_AVAILABILITY",
    /** A recurring interval for availability. */
    AVAILABILITY = "AVAILABILITY"
}
/** @enumType */
type RecurringIntervalTypeWithLiterals = RecurringIntervalType | 'UNDEFINED' | 'EVENT' | 'TIME_AVAILABILITY' | 'AVAILABILITY';
interface Location {
    /**
     * Location type.
     * One of:
     * - `"OWNER_BUSINESS"` The business address as set in the site’s general settings.
     * - `"OWNER_CUSTOM"` The address as set when creating the service.
     * - `"CUSTOM"` The address set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
    /**
     * Free text address used when locationType is `OWNER_CUSTOM`.
     * @deprecated
     */
    address?: string | null;
    /** Custom address, used when locationType is `"OWNER_CUSTOM"`. Might be used when locationType is `"CUSTOM"` in case the owner sets a custom address for the session which is different from the default. */
    customAddress?: Address;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
/** Physical address */
interface Address extends AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
    /**
     * Country code.
     * @format COUNTRY
     */
    country?: string | null;
    /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /** Zip/postal code. */
    postalCode?: string | null;
    /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */
    addressLine2?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Free text to help find the address. */
    hint?: string | null;
    /** Coordinates of the physical address. */
    geocode?: AddressLocation;
    /** Country full name. */
    countryFullname?: string | null;
    /** Multi-level subdivisions from top to bottom. */
    subdivisions?: Subdivision[];
}
/** @oneof */
interface AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
}
interface StreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
interface AddressLocation {
    /** Address latitude. */
    latitude?: number | null;
    /** Address longitude. */
    longitude?: number | null;
}
interface Subdivision {
    /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    code?: string;
    /** Subdivision full name. */
    name?: string;
}
interface LocationsLocation {
    /**
     * Location ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Location name.
     * @maxLength 150
     */
    name?: string;
    /**
     * Location description.
     * @maxLength 500
     */
    description?: string | null;
    /**
     * Whether this is the default location. There can only be one default location per site. The default location can't be archived.
     * @readonly
     */
    default?: boolean;
    /**
     * Location status. Defaults to `ACTIVE`.
     * __Notes:__
     * - [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)
     * doesn't affect the location's status.
     * - `INACTIVE` status is currently not supported.
     */
    status?: LocationStatusWithLiterals;
    /**
     * Location type.
     *
     * **Note:** Currently not supported.
     * @deprecated
     */
    locationType?: LocationsLocationTypeWithLiterals;
    /** Fax number. */
    fax?: string | null;
    /** Timezone in `America/New_York` format. */
    timeZone?: string | null;
    /** Email address. */
    email?: string | null;
    /** Phone number. */
    phone?: string | null;
    /** Address. */
    address?: LocationsAddress;
    /**
     * Business schedule. Array of weekly recurring time periods when the location is open for business. Limited to 100 time periods.
     *
     * __Note:__ Not supported by Wix Bookings.
     */
    businessSchedule?: BusinessSchedule;
    /**
     * Revision number, which increments by 1 each time the location is updated.
     * To prevent conflicting changes, the existing revision must be used when updating a location.
     */
    revision?: string | null;
    /**
     * Whether the location is archived. Archived locations can't be updated.
     * __Note:__ [Archiving a location](https://dev.wix.com/api/rest/business-info/locations/archive-location)
     * doesn't affect its `status`.
     * @readonly
     */
    archived?: boolean;
    /**
     * Location types.
     * @maxSize 10
     */
    locationTypes?: LocationsLocationTypeWithLiterals[];
}
/** For future use */
declare enum LocationStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE"
}
/** @enumType */
type LocationStatusWithLiterals = LocationStatus | 'ACTIVE' | 'INACTIVE';
/** For future use */
declare enum LocationsLocationType {
    UNKNOWN = "UNKNOWN",
    BRANCH = "BRANCH",
    OFFICES = "OFFICES",
    RECEPTION = "RECEPTION",
    HEADQUARTERS = "HEADQUARTERS",
    INVENTORY = "INVENTORY"
}
/** @enumType */
type LocationsLocationTypeWithLiterals = LocationsLocationType | 'UNKNOWN' | 'BRANCH' | 'OFFICES' | 'RECEPTION' | 'HEADQUARTERS' | 'INVENTORY';
interface LocationsAddress {
    /**
     * 2-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format.
     * @format COUNTRY
     */
    country?: string | null;
    /** Code for a subdivision (such as state, prefecture, or province) in [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) format. */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /**
     * Postal or zip code.
     * @maxLength 20
     */
    postalCode?: string | null;
    /** Street address. Includes street name, number, and apartment number in separate fields. */
    streetAddress?: LocationsStreetAddress;
    /** Full address of the location. */
    formatted?: string | null;
    /** Geographic coordinates of location. */
    location?: LocationsAddressLocation;
}
/** Street address. Includes street name, number, and apartment number in separate fields. */
interface LocationsStreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
/** Address Geolocation */
interface LocationsAddressLocation {
    /** Latitude of the location. Must be between -90 and 90. */
    latitude?: number | null;
    /** Longitude of the location. Must be between -180 and 180. */
    longitude?: number | null;
}
/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */
interface BusinessSchedule {
    /**
     * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.
     * @maxSize 100
     */
    periods?: TimePeriod[];
    /**
     * Exceptions to the business's regular hours. The business can be open or closed during the exception.
     * @maxSize 100
     */
    specialHourPeriod?: SpecialHourPeriod[];
}
/** Weekly recurring time periods when the business is regularly open or the service is available. */
interface TimePeriod {
    /** Day of the week the period starts on. */
    openDay?: DayOfWeekWithLiterals;
    /**
     * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     */
    openTime?: string;
    /** Day of the week the period ends on. */
    closeDay?: DayOfWeekWithLiterals;
    /**
     * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     *
     * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.
     */
    closeTime?: string;
}
/** Enumerates the days of the week. */
declare enum DayOfWeek {
    MONDAY = "MONDAY",
    TUESDAY = "TUESDAY",
    WEDNESDAY = "WEDNESDAY",
    THURSDAY = "THURSDAY",
    FRIDAY = "FRIDAY",
    SATURDAY = "SATURDAY",
    SUNDAY = "SUNDAY"
}
/** @enumType */
type DayOfWeekWithLiterals = DayOfWeek | 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY';
/** Exception to the business's regular hours. The business can be open or closed during the exception. */
interface SpecialHourPeriod {
    /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    startDate?: string;
    /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    endDate?: string;
    /**
     * Whether the business is closed (or the service is not available) during the exception.
     *
     * Default: `true`.
     */
    isClosed?: boolean;
    /** Additional info about the exception. For example, "We close earlier on New Year's Eve." */
    comment?: string;
}
interface Rate {
    /**
     * Mapping between a named price option, for example, adult or child prices, and the price, currency, and down payment amount.
     * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.
     */
    labeledPriceOptions?: Record<string, Price>;
    /**
     * Textual price information used when **Price Per Session** is set to **Custom Price** in the app's service details page.
     * When present in an update request, the `default_varied_price` is ignored to support backward compatibility.
     */
    priceText?: string | null;
}
interface Price {
    /**
     * Required payment amount.
     * @format DECIMAL_VALUE
     */
    amount?: string;
    /**
     * Currency in which the amount is quoted.
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Amount of a down payment or deposit as part of the transaction.
     * @format DECIMAL_VALUE
     */
    downPayAmount?: string;
}
/**
 * <!-- Needs updating when recurrence has been tested
 * Schedule's availability calculation is executed by the schedule's available intervals and this additional information.
 * Schedule's available intervals are recurring intervals (defined in the schedule) minus sessions that has no more spots for bookings (including time between_slots), or schedule's sessions with open spots for bookings.-->
 */
interface Availability {
    /** Date and time the schedule starts to be available for booking. */
    start?: Date | null;
    /** Date and time the schedule stops being available for booking. No value indicates no end time. */
    end?: Date | null;
    /** Other schedules that impact the availability calculation. Relevant only when there are availability constraints. */
    linkedSchedules?: LinkedSchedule[];
    /** Constraints for calculating the schedule's availability. */
    constraints?: AvailabilityConstraints;
}
/** Describes how to calculate the specific slots that are available for booking. */
interface AvailabilityConstraints {
    /**
     * A list of duration options for slots, in minutes. Minimum value for a duration is 1.
     * The availability calculation generates slots with these durations, where there is no conflict with existing sessions or other availability constraints.
     * @min 1
     */
    slotDurations?: number[];
    /**
     * The number of minutes between the `end` of one slot, and the `start` of the next.
     * Minimum value is 0, maximum value is 120.
     * @max 720
     */
    timeBetweenSlots?: number;
    /**
     * Specify how to split the slots in intervals of minutes.
     * This value indicates the time between available slots' start time. e.g., from 5 minute slots (3:00, 3:05, 3:15) and 1 hour slots (3:00, 4:00, 5:00).
     * Optional. The default is the first duration in slot_durations field.
     * Deprecated. Use the `split_slots_interval.value_in_minutes`.
     * @deprecated
     */
    splitInterval?: number | null;
    /**
     * An object defining the time between available slots' start times.  For example, a slot with slots_split_interval=5 can start every 5 minutes. The default is the slot duration.
     * @readonly
     */
    slotsSplitInterval?: SplitInterval;
}
/** The time between available slots' start times. For example, For 5 minute slots, 3:00, 3:05, 3:15 etc. For 1 hour slots, 3:00, 4:00, 5:00 etc. */
interface SplitInterval {
    /**
     * Whether the slot duration is used as the split interval value.
     * If `same_as_duration` is `true`, the `value_in_minutes` is the sum of the first duration in
     * `schedule.availabilityConstraints.SlotDurations` field, and `schedule.availabilityConstraints.TimeBetweenSlots` field.
     */
    sameAsDuration?: boolean | null;
    /** Number of minutes between available slots' start times when `same_as_duration` is `false`. */
    valueInMinutes?: number | null;
}
interface Participant {
    /**
     * Participant ID. Currently represents the booking.id.
     * @format GUID
     */
    _id?: string;
    /**
     * Contact ID.
     * @format GUID
     */
    contactId?: string | null;
    /** Participant's name. */
    name?: string | null;
    /** Participant's phone number. */
    phone?: string | null;
    /** Participant's email address. */
    email?: string | null;
    /** Group or party size. The number of people attending. Defaults to 0. Maximum is 250. */
    partySize?: number;
    /**
     * Approval status for the participant.
     * <!-- Commented out untill updateParticipant is exposed Generally the same status as the booking, unless updated using the `updateParticipant()` API. Defaults to `"UNDEFINED"`.-->
     */
    approvalStatus?: ApprovalStatusWithLiterals;
    /**
     * Whether the participant was inherited from the schedule, as opposed to being booked directly to the session.
     * @readonly
     */
    inherited?: boolean;
}
declare enum ApprovalStatus {
    /** Default. */
    UNDEFINED = "UNDEFINED",
    /** Pending business approval. */
    PENDING = "PENDING",
    /** Approved by the business. */
    APPROVED = "APPROVED",
    /** Declined by the business. */
    DECLINED = "DECLINED"
}
/** @enumType */
type ApprovalStatusWithLiterals = ApprovalStatus | 'UNDEFINED' | 'PENDING' | 'APPROVED' | 'DECLINED';
interface ExternalCalendarOverrides {
    /** Synced title of the external calendar event. */
    title?: string | null;
    /** Synced description of the external calendar event. */
    description?: string | null;
}
declare enum ScheduleStatus {
    /** Undefined schedule status. */
    UNDEFINED = "UNDEFINED",
    /** The schedule was created. */
    CREATED = "CREATED",
    /** The schedule was cancelled. */
    CANCELLED = "CANCELLED"
}
/** @enumType */
type ScheduleStatusWithLiterals = ScheduleStatus | 'UNDEFINED' | 'CREATED' | 'CANCELLED';
interface Version {
    /** Schedule version number, updated each time the schedule is updated. */
    scheduleVersion?: number | null;
    /** Participants version number, updated each time the schedule participants are updated. */
    participantsVersion?: number | null;
}
interface ConferenceProvider {
    /** Conferencing provider ID */
    providerId?: string;
}
interface CalendarConference {
    /** Wix Calendar conference ID. */
    _id?: string;
    /** Conference meeting ID in the provider's conferencing system. */
    externalId?: string;
    /** Conference provider ID. */
    providerId?: string;
    /** URL used by the host to start the conference. */
    hostUrl?: string;
    /** URL used by a guest to join the conference. */
    guestUrl?: string;
    /** Password to join the conference. */
    password?: string | null;
    /** Conference description. */
    description?: string | null;
    /** Conference type. */
    conferenceType?: ConferenceTypeWithLiterals;
    /** ID of the account owner in the video conferencing service. */
    accountOwnerId?: string | null;
}
declare enum ConferenceType {
    /** Undefined conference type. */
    UNDEFINED = "UNDEFINED",
    /** API-generated online meeting. */
    ONLINE_MEETING_PROVIDER = "ONLINE_MEETING_PROVIDER",
    /** User-defined meeting. */
    CUSTOM = "CUSTOM"
}
/** @enumType */
type ConferenceTypeWithLiterals = ConferenceType | 'UNDEFINED' | 'ONLINE_MEETING_PROVIDER' | 'CUSTOM';
interface ScheduleUpdated {
    /** The old schedule before the update. */
    oldSchedule?: Schedule;
    /** The new schedule after the update. */
    newSchedule?: Schedule;
    /**
     * Recurring sessions updated event. If this field is given, the reason for the schedule updated event was
     * updating at least one of the given schedule's recurring sessions.
     * This event is triggered by create/update/delete recurring session apis.
     */
    recurringSessions?: RecurringSessionsUpdated;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
    /**
     * Whether this notification was created as a result of an anonymization request, such as GDPR.
     * An anonymized participant will have the following details:
     * name = "deleted"
     * phone = "deleted"
     * email = "<EMAIL>"
     */
    triggeredByAnonymizeRequest?: boolean | null;
}
interface RecurringSessionsUpdated {
    /** Old schedule's recurring session list. */
    oldRecurringSessions?: Session[];
    /** New schedule's recurring session list. */
    newRecurringSessions?: Session[];
}
interface Session {
    /**
     * Session ID.
     * @readonly
     */
    _id?: string | null;
    /**
     * ID of the schedule that the session belongs to.
     * @immutable
     */
    scheduleId?: string;
    /**
     * ID of the resource or service that the session's schedule belongs to.
     * @readonly
     */
    scheduleOwnerId?: string | null;
    /** Original start date and time of the session in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. */
    originalStart?: Date | null;
    /** An object specifying the start date and time of the session. If the session is a recurring session, `start` must contain a `localDateTime`. */
    start?: CalendarDateTime;
    /**
     * An object specifying the end date and time of the session. The `end` time must be after the `start` time and be same type as `start`.
     * If the session is a recurring session, `end` must contain a `localDateTime`.
     */
    end?: CalendarDateTime;
    /**
     * An object specifying a list of schedules and the way each schedule's availability is affected by the session. For example, the schedule of an instructor is affected by sessions of the class that they instruct.
     * The array is inherited from the schedule and can be overridden even if the session is a recurring session.
     */
    affectedSchedules?: LinkedSchedule[];
    /**
     * Session title.
     * The value is inherited from the schedule and can be overridden unless the session is a recurring session.
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * __Deprecated.__
     * Tags for the session.
     * The value is inherited from the schedule and can be overridden unless the session is a recurring session.
     * @maxLength 200
     * @deprecated
     */
    tags?: string[] | null;
    /**
     * An object describing the location where the session takes place.
     * Defaults to the schedule location.
     * For single sessions, `session.location.businessLocation` can only be provided for locations that are defined in the schedule using `schedule.location` or `schedule.availability.locations`.
     */
    location?: Location;
    /**
     * Maximum number of participants that can be added to the session. Defaults to the schedule capacity.
     * The value is inherited from the schedule and can be overridden unless the session is a recurring session.
     * @max 1000
     */
    capacity?: number | null;
    /**
     * Deprecated. Please use the [Booking Services V2](https://dev.wix.com/api/rest/wix-bookings/services-v2) payment instead.
     * @deprecated
     */
    rate?: Rate;
    /**
     * Time reserved after the session end time, derived from the schedule availability constraints and the time between slots. Read-only.
     * If the session is a recurring session, this field must be empty.
     */
    timeReservedAfter?: number | null;
    /**
     * Additional information about the session.
     * Notes are not supported for recurring sessions.
     * @maxLength 10000
     */
    notes?: string;
    /**
     * The number of participants booked for the session. Read-only.
     * Calculated as the sum of the party sizes.
     * @readonly
     */
    totalNumberOfParticipants?: number;
    /**
     * *Partial list** list of participants booked for the session.
     * The list includes participants who have registered for this specific session, and participants who have registered for a schedule that includes this session.
     * If the session is a recurring session, this field must be empty.
     * To retrieve the full list of session participants please use the [Query Extended Bookings API](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings).
     */
    participants?: Participant[];
    /**
     * A list of properties for which values were inherited from the schedule.
     * This does not include participants that were inherited from the schedule.
     * @readonly
     */
    inheritedFields?: string[];
    /**
     * __Deprecated.__
     * @deprecated
     */
    externalCalendarOverrides?: ExternalCalendarOverrides;
    /**
     * Session status.
     * @readonly
     */
    status?: StatusWithLiterals;
    /**
     * Recurring interval ID. Defined when a session will be a recurring session. read-only. Optional.
     * For example, when creating a class service  with recurring sessions, you add a recurrence rule to create recurring sessions.
     * This field is omitted for single sessions or instances of recurring sessions.
     * Specified when the session was originally generated from a schedule recurring interval.
     * Deprecated. Use `recurringSessionId`.
     * @readonly
     * @deprecated
     */
    recurringIntervalId?: string | null;
    /**
     * The ID of the recurring session if this session is an instance of a recurrence. Use this ID to update the recurrence and all of the instances.
     * @readonly
     */
    recurringSessionId?: string | null;
    /** Session type. */
    type?: SessionTypeWithLiterals;
    /**
     * A conference created for the session according to the details set in the schedule's conference provider information.
     * If the session is a recurring session, this field is inherited from the schedule.
     * **Partially deprecated.** Only `hostUrl` and `guestUrl` are to be supported.
     * @deprecated
     */
    calendarConference?: CalendarConference;
    /**
     * A string representing a recurrence rule (RRULE) for a recurring session, as defined in [iCalendar RFC 5545](https://icalendar.org/iCalendar-RFC-5545/3-3-10-recurrence-rule.html).
     * If the session is an instance of a recurrence pattern, the `instanceOfRecurrence` property will be contain the recurrence rule and this property will be empty.
     * The RRULE defines a rule for repeating a session.
     * Supported parameters are:
     *
     * |Keyword|Description|Supported values|
     * |--|--|---|
     * |`FREQ`|The frequency at which the session is recurs. Required.|`WEEKLY`|
     * |`INTERVAL`|How often, in terms of `FREQ`, the session recurs. Default is 1. Optional.|
     * |`UNTIL`|The UTC end date and time of the recurrence. Optional.|
     * |`BYDAY`|Day of the week when the event should recur. Required.|One of: `MO`, `TU`, `WE`, `TH`, `FR`, `SA`, `SU`|
     *
     *
     * For example, a session that repeats every second week on a Monday until January 7, 2022 at 8 AM:
     * `"FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20220107T080000Z"`
     *
     * <!--ORIGINAL COMMENTS:
     * `FREQ` — The frequency with which the session should be repeated (such as DAILY or WEEKLY).
     * Supported `WEEKLY` value is supported.
     * INTERVAL — Works together with FREQ to specify how often the session should be repeated. For example, FREQ=WEEKLY;INTERVAL=2 means once every two weeks. Optional. Default value is 1.
     * COUNT — The number of times this event should be repeated. Not yet supported.
     * UNTIL — The UTC date & time until which the session should be repeated. This parameter is optional. When it is not specified, the event repeats forever.
     * The format is a short ISO date, followed by 'T' and a short time with seconds and without milliseconds, terminated by the UTC designator 'Z'. For example, until Jan. 19th 2018 at 7:00 AM: 'UNTIL=20180119T070000Z'.
     * BYDAY - The days of the week when the event should be repeated. Currently, only a single day is supported. This parameter is mandatory.
     * Possible values are: MO, TU, WE, TH, FR, SA, SU
     * Note that DTSTART and DTEND lines are not allowed in this field; session start and end times are specified in the start and end fields.
     * **Example**: FREQ=WEEKLY;INTERVAL=2;BYDAY=MO;UNTIL=20200427T070000Z
     * ORIGINAL COMMENTS-->
     */
    recurrence?: string | null;
    /**
     * A string representing a recurrence rule (RRULE) if the session is an instance of a recurrence pattern.
     * Empty when the session is not an instance of a recurrence rule, or if the session defines a recurrence pattern, and `recurrence` is not empty.
     * @readonly
     */
    instanceOfRecurrence?: string | null;
    /**
     * The session version.
     * Composed by the schedule, session and participants versions.
     * @readonly
     */
    version?: SessionVersion;
}
interface CalendarDateTime {
    /**
     * UTC date-time in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Coordinated_Universal_Time_(UTC)) format. If a time zone offset is specified, the time is converted to UTC. For example, if you specify  `new Date('2021-01-06T16:00:00.000-07:00')`, the stored value will be `"2021-01-06T23:00:00.000Z"`.
     * Required if `localDateTime` is not specified.
     * If `localDateTime` is specified, `timestamp` is calculated as `localDateTime`, using the business's time zone.
     */
    timestamp?: Date | null;
    /** An object containing the local date and time for the business's time zone. */
    localDateTime?: LocalDateTime;
    /**
     * The time zone. Optional. Derived from the schedule's time zone.
     * In case this field is associated with recurring session, this field is empty.
     * @readonly
     */
    timeZone?: string | null;
}
interface LocalDateTime {
    /** Year. 4-digit format. */
    year?: number | null;
    /**
     * Month number, from 1-12.
     * @min 1
     * @max 12
     */
    monthOfYear?: number | null;
    /** Day of the month, from 1-31. */
    dayOfMonth?: number | null;
    /**
     * Hour of the day in 24-hour format, from 0-23.
     * @max 23
     */
    hourOfDay?: number | null;
    /**
     * Minute, from 0-59.
     * @max 59
     */
    minutesOfHour?: number | null;
}
interface ExternalCalendarInfo {
    /** The external calendar type (e.g. Google Calendar, iCal, etc). */
    calendarType?: CalendarTypeWithLiterals;
}
declare enum CalendarType {
    /** There is no information about the external calendar type. */
    UNDEFINED = "UNDEFINED",
    /** [Google Calendar](https://developers.google.com/calendar/api/guides/overview). */
    GOOGLE = "GOOGLE",
    /** Apple iCalendar. */
    I_CAL = "I_CAL",
    /** __Deprecated__. Use `MICROSOFT` instead. */
    OUTLOOK = "OUTLOOK",
    /** __Deprecated__. Use `MICROSOFT` instead. */
    OFFICE_365 = "OFFICE_365",
    /** Microsoft Calendar. For example, Office 365 calendar or Outlook calendar. */
    MICROSOFT = "MICROSOFT",
    /** A different type of external calendar, not listed here. */
    OTHER = "OTHER"
}
/** @enumType */
type CalendarTypeWithLiterals = CalendarType | 'UNDEFINED' | 'GOOGLE' | 'I_CAL' | 'OUTLOOK' | 'OFFICE_365' | 'MICROSOFT' | 'OTHER';
declare enum Status {
    /** Undefined status. */
    UNDEFINED = "UNDEFINED",
    /** Session is confirmed. Default status. */
    CONFIRMED = "CONFIRMED",
    /**
     * Session is cancelled.
     * A cancelled session can be the cancellation of a recurring session that should no longer be displayed or a deleted single session.
     * The ListSessions returns cancelled sessions only if 'includeDelete' flag is set to true.
     */
    CANCELLED = "CANCELLED"
}
/** @enumType */
type StatusWithLiterals = Status | 'UNDEFINED' | 'CONFIRMED' | 'CANCELLED';
declare enum SessionType {
    UNDEFINED = "UNDEFINED",
    /**
     * Creates an event on the calendar for the owner of the schedule that the session belongs to.
     * Default type.
     */
    EVENT = "EVENT",
    /** Represents a resource's available working hours. */
    WORKING_HOURS = "WORKING_HOURS",
    /** Deprecated. Please use WORKING_HOURS. */
    TIME_AVAILABILITY = "TIME_AVAILABILITY",
    /** Deprecated. Represents a resource's available hours. Please use WORKING_HOURS. */
    AVAILABILITY = "AVAILABILITY"
}
/** @enumType */
type SessionTypeWithLiterals = SessionType | 'UNDEFINED' | 'EVENT' | 'WORKING_HOURS' | 'TIME_AVAILABILITY' | 'AVAILABILITY';
interface SessionVersion {
    /** Incremental version number, which is updated on each change to the session or on changes affecting the session. */
    number?: string | null;
}
interface ParticipantNotification {
    /**
     * Whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean;
    /** Custom message to send to the participants about the changes to the booking. */
    message?: string | null;
}
interface ScheduleCancelled {
    schedule?: Schedule;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
    oldSchedule?: Schedule;
}
interface SessionCreated {
    session?: Session;
}
interface SessionUpdated {
    oldSession?: Session;
    newSession?: Session;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
    /**
     * Whether this notification was created as a result of an anonymization request, such as GDPR.
     * An anonymized participant will have the following details:
     * name = "deleted"
     * phone = "deleted"
     * email = "<EMAIL>"
     */
    triggeredByAnonymizeRequest?: boolean | null;
}
interface SessionCancelled {
    session?: Session;
    /** Whether to notify participants about the change and an optional custom message */
    participantNotification?: ParticipantNotification;
}
interface AvailabilityPolicyUpdated {
    availabilityPolicy?: AvailabilityPolicy;
}
/** Availability policy applied to all site schedules. */
interface AvailabilityPolicy {
    /** Specify how to split the schedule slots in intervals of minutes. */
    splitInterval?: SplitInterval;
}
interface IntervalSplit {
    scheduleId?: string;
    intervals?: RecurringInterval[];
    newScheduleVersion?: number | null;
    oldScheduleVersion?: number | null;
}
interface RecurringSessionSplit {
    scheduleId?: string;
    recurringSessions?: Session[];
    newScheduleVersion?: number | null;
    oldScheduleVersion?: number | null;
}
/** Schedule unassigned from user. */
interface ScheduleUnassignedFromUser {
    /**
     * The Wix user id.
     * @format GUID
     */
    userId?: string | null;
    /** The schedule that was unassigned from the user. */
    schedule?: Schedule;
}
interface MultipleSessionsCreated {
    schedulesWithSessions?: ScheduleWithSessions[];
}
interface ScheduleWithSessions {
    schedule?: Schedule;
    siteProperties?: SitePropertiesOnScheduleCreation;
    sessions?: Session[];
}
interface SitePropertiesOnScheduleCreation {
    /** The global time zone value. */
    timeZone?: string | null;
}
interface MigrationEvent {
    migrationData?: MigrationData;
}
interface MigrationData {
    businessId?: string | null;
    staffs?: StaffData[];
}
interface StaffData {
    resourceId?: string;
    syncRequestEmail?: string;
    refreshToken?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface StaffMemberConnectedToUserEnvelope {
    data: StaffMemberConnectedToUser;
    metadata: EventMetadata;
}
/**
 * Triggered when a Wix user is connected to a staff member.
 *
 *
 * The event is triggered when the connection status changes to `CONNECTED`. It
 * isn't triggered when Wix Bookings sends an invitation to become a Wix user or
 * join a site.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @webhook
 * @eventType wix.bookings.staff.v1.staff_member_connected_to_user
 * @slug connected_to_user
 */
declare function onStaffMemberConnectedToUser(handler: (event: StaffMemberConnectedToUserEnvelope) => void | Promise<void>): void;
interface StaffMemberCreatedEnvelope {
    entity: StaffMember;
    metadata: EventMetadata;
}
/**
 * Triggered when a staff member is created.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @webhook
 * @eventType wix.bookings.staff.v1.staff_member_created
 * @slug created
 */
declare function onStaffMemberCreated(handler: (event: StaffMemberCreatedEnvelope) => void | Promise<void>): void;
interface StaffMemberDeletedEnvelope {
    entity: StaffMember;
    metadata: EventMetadata;
}
/**
 * Triggered when a staff member is deleted.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @webhook
 * @eventType wix.bookings.staff.v1.staff_member_deleted
 * @slug deleted
 */
declare function onStaffMemberDeleted(handler: (event: StaffMemberDeletedEnvelope) => void | Promise<void>): void;
interface StaffMemberDisconnectedFromUserEnvelope {
    data: StaffMemberDisconnectedFromUser;
    metadata: EventMetadata;
}
/**
 * Triggered when a Wix user is disconnected from a staff member.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @webhook
 * @eventType wix.bookings.staff.v1.staff_member_disconnected_from_user
 * @slug disconnected_from_user
 */
declare function onStaffMemberDisconnectedFromUser(handler: (event: StaffMemberDisconnectedFromUserEnvelope) => void | Promise<void>): void;
interface StaffMemberFullyCreatedEnvelope {
    data: StaffMemberFullyCreated;
    metadata: EventMetadata;
}
/**
 * Triggered when a staff member is created.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @webhook
 * @eventType wix.bookings.staff.v1.staff_member_fully_created
 * @slug fully_created
 */
declare function onStaffMemberFullyCreated(handler: (event: StaffMemberFullyCreatedEnvelope) => void | Promise<void>): void;
interface StaffMemberUpdatedEnvelope {
    entity: StaffMember;
    metadata: EventMetadata;
}
/**
 * Triggered when a staff member is updated.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @webhook
 * @eventType wix.bookings.staff.v1.staff_member_updated
 * @slug updated
 */
declare function onStaffMemberUpdated(handler: (event: StaffMemberUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Creates a staff member.
 *
 *
 * By default, the staff member works during the business working hours. You
 * could follow *this sample flow*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))
 * to set custom working hours.
 * @param staffMember - Staff member to create.
 * @public
 * @documentationMaturity preview
 * @requiredField staffMember
 * @requiredField staffMember.name
 * @param options - Options for creating the staff member.
 * @permissionId BOOKINGS.STAFF_MEMBER_CREATE
 * @applicableIdentity APP
 * @returns Created staff member.
 * @fqn wix.bookings.staff.v1.StaffMembersService.CreateStaffMember
 */
declare function createStaffMember(staffMember: NonNullablePaths<StaffMember, `name`, 2>, options?: CreateStaffMemberOptions): Promise<NonNullablePaths<StaffMember, `resource.workingHoursSchedules` | `resource.workingHoursSchedules.${number}.shared` | `resource.usesDefaultWorkingHours` | `associatedWixIdentity.identificationData.anonymousVisitorId` | `associatedWixIdentity.identificationData.memberId` | `associatedWixIdentity.identificationData.wixUserId` | `associatedWixIdentity.identificationData.appId` | `associatedWixIdentity.connectionStatus` | `associatedWixIdentity.connection.status` | `tags.privateTags.tagIds`, 5>>;
interface CreateStaffMemberOptions {
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
/**
 * Retrieves a staff member.
 * @param staffMemberId - ID of the staff member to retrieve.
 * @public
 * @documentationMaturity preview
 * @requiredField staffMemberId
 * @param options - Option for retrieving the staff member.
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @applicableIdentity APP
 * @returns Retrieved staff member.
 * @fqn wix.bookings.staff.v1.StaffMembersService.GetStaffMember
 */
declare function getStaffMember(staffMemberId: string, options?: GetStaffMemberOptions): Promise<NonNullablePaths<StaffMember, `resource.workingHoursSchedules` | `resource.workingHoursSchedules.${number}.shared` | `resource.usesDefaultWorkingHours` | `associatedWixIdentity.identificationData.anonymousVisitorId` | `associatedWixIdentity.identificationData.memberId` | `associatedWixIdentity.identificationData.wixUserId` | `associatedWixIdentity.identificationData.appId` | `associatedWixIdentity.connectionStatus` | `associatedWixIdentity.connection.status` | `tags.privateTags.tagIds`, 5>>;
interface GetStaffMemberOptions {
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
/**
 * Updates a staff member.
 *
 *
 * Each time the staff member is updated, `revision` increments by 1. You must
 * include current revision of the staff member when updating it. This ensures
 * you're working with the latest service information and prevents unintended
 * overwrites.
 * @param _id - Staff member ID.
 * @public
 * @documentationMaturity preview
 * @requiredField _id
 * @requiredField staffMember
 * @requiredField staffMember.revision
 * @param staffMember - The staff member to update
 * @param options - Options for updating the staff member.
 * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE
 * @applicableIdentity APP
 * @returns Updated staff member.
 * @fqn wix.bookings.staff.v1.StaffMembersService.UpdateStaffMember
 */
declare function updateStaffMember(_id: string, staffMember: NonNullablePaths<UpdateStaffMember, `revision`, 2>, options?: UpdateStaffMemberOptions): Promise<NonNullablePaths<StaffMember, `resource.workingHoursSchedules` | `resource.workingHoursSchedules.${number}.shared` | `resource.usesDefaultWorkingHours` | `associatedWixIdentity.identificationData.anonymousVisitorId` | `associatedWixIdentity.identificationData.memberId` | `associatedWixIdentity.identificationData.wixUserId` | `associatedWixIdentity.identificationData.appId` | `associatedWixIdentity.connectionStatus` | `associatedWixIdentity.connection.status` | `tags.privateTags.tagIds`, 5>>;
interface UpdateStaffMember {
    /**
     * Staff member ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Staff member name.
     * @minLength 1
     * @maxLength 40
     */
    name?: string | null;
    /**
     * Staff member's email address.
     * @maxLength 320
     * @format EMAIL
     */
    email?: string | null;
    /**
     * Staff member's phone number.
     * @maxLength 20
     * @format PHONE
     */
    phone?: string | null;
    /**
     * Description for the staff member. For example,
     * `Experienced nail technician specialized in gel and acrylic nails`.
     * @maxLength 500
     */
    description?: string | null;
    /** Staff media. */
    mainMedia?: MediaItem;
    /**
     * Staff member's *resource ID*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)),
     * identical to `resource.id`.
     * @readonly
     * @format GUID
     */
    resourceId?: string | null;
    /**
     * Details about the *resource object*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
     * associated with the staff member. Available only if you specify `RESOURCE_DETAILS`
     * in the `fields` array.
     * @readonly
     */
    resource?: Resource;
    /**
     * Identity of the Wix user associated with the staff member. Learn more about
     * _identities_
     * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/about-identities) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/about-identities)).
     * @readonly
     */
    associatedWixIdentity?: AssociatedWixIdentity;
    /**
     * Revision number, which increments by 1 each time the staff member is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the staff member.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time the staff member was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Time the staff member was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /** Extensions enabling users to save custom data related to the staff member. */
    extendedFields?: ExtendedFields;
    /**
     * Tags allowing you to classify staff members. Learn more about *tags*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).
     */
    tags?: Tags;
}
interface UpdateStaffMemberOptions {
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
/**
 * Deletes a staff member.
 *
 *
 * Also deletes the *resource*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction))
 * associated with the staff member.
 * @param staffMemberId - ID of the staff member to delete.
 * @public
 * @documentationMaturity preview
 * @requiredField staffMemberId
 * @permissionId BOOKINGS.STAFF_MEMBER_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.DeleteStaffMember
 */
declare function deleteStaffMember(staffMemberId: string): Promise<void>;
/**
 * Creates a query to retrieve a list of staff members.
 *
 * The `queryStaffMembers()` function builds a query to retrieve a list of staff members and returns a `StaffMembersQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-builder/find) function.
 *
 * You can refine the query by chaining `StaffMembersQueryBuilder` functions onto the query. `StaffMembersQueryBuilder` functions enable you to sort, filter, and control the results that `queryStaffMembers()` returns.
 *
 * `queryStaffMembers()` runs with the following `StaffMembersQueryBuilder` default that you can override:
 *
 * + `limit` is `50`.
 * + Sorted by `id` in ascending order.
 *
 * The functions that are chained to `staffMembersTypes()` are applied in the order they are called.
 *
 * The following `StaffMembersQueryBuilder` functions are supported for the `queryStaffMembers()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/staff-members-query-result/items) property in `StaffMembersQueryResult`.
 * @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.QueryStaffMembers
 */
declare function queryStaffMembers(options?: QueryStaffMembersOptions): StaffMembersQueryBuilder;
interface QueryStaffMembersOptions {
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[] | undefined;
}
interface QueryCursorResult {
    cursors: Cursors;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface StaffMembersQueryResult extends QueryCursorResult {
    items: StaffMember[];
    query: StaffMembersQueryBuilder;
    next: () => Promise<StaffMembersQueryResult>;
    prev: () => Promise<StaffMembersQueryResult>;
}
interface StaffMembersQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    eq: (propertyName: '_id' | 'name' | 'email' | 'phone' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    ne: (propertyName: '_id' | 'name' | 'email' | 'phone' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    ge: (propertyName: '_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    gt: (propertyName: '_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    le: (propertyName: '_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     * @documentationMaturity preview
     */
    lt: (propertyName: '_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `string`.
     * @param string - String to compare against. Case-insensitive.
     * @documentationMaturity preview
     */
    startsWith: (propertyName: '_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId', value: string) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     * @documentationMaturity preview
     */
    hasSome: (propertyName: '_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any[]) => StaffMembersQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     * @documentationMaturity preview
     */
    hasAll: (propertyName: string, value: any[]) => StaffMembersQueryBuilder;
    /** @documentationMaturity preview */
    in: (propertyName: '_id' | 'name' | 'email' | 'phone' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: any) => StaffMembersQueryBuilder;
    /** @documentationMaturity preview */
    exists: (propertyName: '_id' | 'name' | 'email' | 'phone' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | '_createdDate' | '_updatedDate', value: boolean) => StaffMembersQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments.
     * @documentationMaturity preview
     */
    ascending: (...propertyNames: Array<'_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | 'associatedWixIdentity.identificationData.identityType' | 'default' | '_createdDate' | '_updatedDate'>) => StaffMembersQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments.
     * @documentationMaturity preview
     */
    descending: (...propertyNames: Array<'_id' | 'description' | 'resourceId' | 'resource.id' | 'associatedWixIdentity.identificationData.wixUserId' | 'associatedWixIdentity.identificationData.contactId' | 'associatedWixIdentity.identificationData.identityType' | 'default' | '_createdDate' | '_updatedDate'>) => StaffMembersQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object.
     * @documentationMaturity preview
     */
    limit: (limit: number) => StaffMembersQueryBuilder;
    /** @param cursor - A pointer to specific record
     * @documentationMaturity preview
     */
    skipTo: (cursor: string) => StaffMembersQueryBuilder;
    /** @documentationMaturity preview */
    find: () => Promise<StaffMembersQueryResult>;
}
/**
 * Counts how many staff members match the given filter.
 *
 *
 * ## Filter
 *
 * Refer to the _supported filters article_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
 * for a complete list of supported filters and sorting options.
 *
 * ## See also
 *
 * To learn about working with filters in general, see
 * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).
 * @public
 * @documentationMaturity preview
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.CountStaffMembers
 */
declare function countStaffMembers(options?: CountStaffMembersOptions): Promise<NonNullablePaths<CountStaffMembersResponse, `count`, 2>>;
interface CountStaffMembersOptions {
    /**
     * Filter to base the count upon. See the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
}
/**
 * Connects a Wix user to a staff member, enabling them to manage their own
 * working hour schedule in the dashboard.
 *
 *
 * By default, Wix Bookings uses the staff member's `email`. However, you can
 * specify an alternative email address. If no existing Wix user is associated
 * with that email, Wix sends them an invitation to become a Wix user. If an
 * existing user is found but not linked to the site, Wix Bookings sends an
 * invitation to join the site.
 *
 * To check the connection status, call *Get Staff Member*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/get-staff-member) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/get-staff-member)),
 * and specify `ASSOCIATED_IDENTITY_STATUS` in the `fields` parameter.
 *
 * You must call *Disconnect Staff Member From User*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/disconnect-staff-member-from-user) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/disconnect-staff-member-from-user))
 * before connecting a different Wix user to the staff member.
 * @param staffMemberId - ID of the staff member to connect to the Wix user.
 * @public
 * @documentationMaturity preview
 * @requiredField staffMemberId
 * @param options - Options for connecting the staff member to a Wix user.
 * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.ConnectStaffMemberToUser
 */
declare function connectStaffMemberToUser(staffMemberId: string, options?: ConnectStaffMemberToUserOptions): Promise<NonNullablePaths<ConnectStaffMemberToUserResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
interface ConnectStaffMemberToUserOptions {
    /**
     * Email of the Wix user to connect to the staff member.
     *
     * Default: Email of the staff member.
     * @maxLength 320
     * @format EMAIL
     */
    email?: string | null;
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface SearchStaffMembersOptions {
    /**
     * Conditional fields to return in the response.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
interface StaffMemberSearchSpec extends SearchSpec {
    searchable: ['name'];
    paging: 'cursor';
    wql: [
        {
            operators: '*';
            fields: ['description', 'email', 'name', 'phone', 'resourceId'];
            sort: 'BOTH';
        }
    ];
}
type CommonSearchWithEntityContext = Search<StaffMember, StaffMemberSearchSpec>;
type StaffMemberSearch = {
    /**
    Cursor-based paging for result navigation. Can't be used together with 'paging'.
    `cursor_paging.cursor` can't be used together with `filter` or `sort`.
    */
    cursorPaging?: {
        /**
      Number of items to load.
      @max: 100
      */
        limit?: NonNullable<CommonSearchWithEntityContext['cursorPaging']>['limit'] | null;
        /**
      Pointer to the next or previous page in the list of results.
    
      You can get the relevant cursor token
      from the `pagingMetadata` object in the previous call's response.
      Not relevant for the first request.
      @maxLength: 16000
      */
        cursor?: NonNullable<CommonSearchWithEntityContext['cursorPaging']>['cursor'] | null;
    };
    /**
    Filter object for narrowing search results. For example, to return only staff members with specific email domains: `"filter": {"email": {"$contains": "@company.com"}}`.
  
    Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).
    */
    filter?: CommonSearchWithEntityContext['filter'] | null;
    /**
    Array of sort objects specifying result order. For example, to sort by creation date in descending order: `"sort": [{"fieldName": "createdDate", "order": "DESC"}]`.
  
    Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)).
    @maxSize: 10
    */
    sort?: {
        /**
      Name of the field to sort by.
      @maxLength: 512
      */
        fieldName?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['fieldName'];
        /**
      Sort order.
      */
        order?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['order'];
    }[];
    /**
    Free text to match in searchable fields.
    */
    search?: {
        /**
      Search mode. Defines the search logic for combining multiple terms in the `expression`.
      */
        mode?: NonNullable<CommonSearchWithEntityContext['search']>['mode'];
        /**
      Search term or expression.
      @maxLength: 100
      */
        expression?: NonNullable<CommonSearchWithEntityContext['search']>['expression'] | null;
        /**
      Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `resource.workingHoursSchedules.shared`.
      @maxLength: 200,
      @maxSize: 20
      */
        fields?: NonNullable<CommonSearchWithEntityContext['search']>['fields'];
        /**
      Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions.
      */
        fuzzy?: NonNullable<CommonSearchWithEntityContext['search']>['fuzzy'];
    };
};
/**
 * Disconnects a staff member from a Wix user, clearing the `associatedWixIdentity`
 * field. Consequently, the user can no longer manage the staff member's working
 * hour schedule in the dashboard.
 *
 *
 * You must call *Disconnect Staff Member From User* before connecting a
 * different Wix user to the staff member.
 * @param staffMemberId - ID of the staff member to disconnect from its Wix user.
 * @public
 * @documentationMaturity preview
 * @requiredField staffMemberId
 * @param options - Options to disconnect the staff member from a Wix user.
 * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.DisconnectStaffMemberFromUser
 */
declare function disconnectStaffMemberFromUser(staffMemberId: string, options?: DisconnectStaffMemberFromUserOptions): Promise<NonNullablePaths<DisconnectStaffMemberFromUserResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
interface DisconnectStaffMemberFromUserOptions {
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
/**
 * Updates a staff member's working hours based on the specified *schedule ID*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
 *
 *
 * ## Default working hours
 *
 * By default, staff members work during the opening hours of the business's
 * _default location_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
 *
 * ## Schedule ID
 *
 * You can specify either the ID of the business's working hour schedule or the
 * staff member's event schedule. The call fails if you specify a different
 * schedule ID.
 *
 * ### Staff event schedule
 *
 * To customize a staff member's working hours, specify their event schedule ID
 * as `scheduleId`. Refer to *this sample flow*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/sample-flows#backend-modules_bookings_staff-members_assign-a-staff-working-hour-schedule) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/sample-flows#assign-a-staff-working-hour-schedule))
 * for more details.
 *
 * ### Business working hour schedule
 *
 * To reset a staff member's working hours to the default business hours,
 * specify the ID of the business working hour schedule as `scheduleId`.
 * @param staffMemberId - ID of the staff member to assign the schedule to.
 * @param scheduleId - ID of the *schedule*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
 * to assign to the staff member.
 *
 * Must be either the staff member's event schedule ID or the working hour
 * schedule ID for a business location.
 * @public
 * @documentationMaturity preview
 * @requiredField scheduleId
 * @requiredField staffMemberId
 * @param options - Options for setting the staff member's working hours schedule.
 * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule
 */
declare function assignWorkingHoursSchedule(staffMemberId: string, scheduleId: string, options?: AssignWorkingHoursScheduleOptions): Promise<NonNullablePaths<AssignWorkingHoursScheduleResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
interface AssignWorkingHoursScheduleOptions {
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
/**
 * Assigns a custom working hours schedule to the staff member
 *
 *
 * The working hours schedule is a schedule that defines the working hours of a staff member,
 * and dictate when the staff member is available for bookings.
 *
 * By default staff members use the shared business working hours schedule. By assigning a custom working hours schedule to a staff member,
 * you can define specific working hours for that staff member.
 *
 * To create and manage schedules and working hours sessions, use [Events API](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction).
 * @param staffMemberId - ID of the staff member for which to assign a working hour schedule.
 * @param scheduleId - ID of the *schedule*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
 * to assign to the staff member.
 * @public
 * @documentationMaturity preview
 * @requiredField scheduleId
 * @requiredField staffMemberId
 * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.AssignCustomSchedule
 * @deprecated
 * @replacedBy wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule
 * @targetRemovalDate 2024-12-31
 */
declare function assignCustomSchedule(staffMemberId: string, scheduleId: string, options?: AssignCustomScheduleOptions): Promise<NonNullablePaths<AssignCustomScheduleResponse, `staffMember.resource.workingHoursSchedules` | `staffMember.resource.workingHoursSchedules.${number}.shared` | `staffMember.resource.usesDefaultWorkingHours` | `staffMember.associatedWixIdentity.identificationData.anonymousVisitorId` | `staffMember.associatedWixIdentity.identificationData.memberId` | `staffMember.associatedWixIdentity.identificationData.wixUserId` | `staffMember.associatedWixIdentity.identificationData.appId` | `staffMember.associatedWixIdentity.connectionStatus` | `staffMember.associatedWixIdentity.connection.status` | `staffMember.tags.privateTags.tagIds`, 6>>;
interface AssignCustomScheduleOptions {
    /**
     * Conditional fields to return.
     * @maxSize 3
     */
    fields?: RequestedFieldsWithLiterals[];
}
/**
 * Synchronously updates *tags*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).
 * for up to 100 staff members.
 *
 *
 * If you specify a tag both in `assignTags` and `unassignTags`, the call
 * succeeds and the tag is assigned.
 * @param ids - IDs of staff members to update tags for.
 * @public
 * @documentationMaturity preview
 * @requiredField ids
 * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTags
 */
declare function bulkUpdateStaffMemberTags(ids: string[], options?: BulkUpdateStaffMemberTagsOptions): Promise<NonNullablePaths<BulkUpdateStaffMemberTagsResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
interface BulkUpdateStaffMemberTagsOptions {
    /** Tags to add to the staff members. */
    assignTags?: Tags;
    /** Tags to remove from the staff members. */
    unassignTags?: Tags;
}
/**
 * Asynchronously updates *tags*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/tags/tags/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/tags/introduction)).
 * for staff members, given the provided filtering.
 *
 *
 * If you specify a tag both in `assignTags` and `unassignTags`, the call
 * succeeds and the tag is assigned.
 *
 * ## Filter
 *
 * Refer to the supported filters article
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
 * for a complete list of supported filters and sorting options.
 * @param filter - Filter to base the update upon. See the *supported filters article*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
 * for a complete list of supported filters.
 * @public
 * @documentationMaturity preview
 * @requiredField filter
 * @permissionId BOOKINGS.STAFF_MEMBER_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTagsByFilter
 */
declare function bulkUpdateStaffMemberTagsByFilter(filter: Record<string, any>, options?: BulkUpdateStaffMemberTagsByFilterOptions): Promise<NonNullablePaths<BulkUpdateStaffMemberTagsByFilterResponse, `jobId`, 2>>;
interface BulkUpdateStaffMemberTagsByFilterOptions {
    /** Tags to add to the staff members. */
    assignTags?: Tags;
    /** Tags to remove from the staff members. */
    unassignTags?: Tags;
}

export { type MediaItemMediaOneOf as $, type AssignWorkingHoursScheduleOptions as A, type BulkUpdateStaffMemberTagsOptions as B, type CreateStaffMemberOptions as C, type DisconnectStaffMemberFromUserOptions as D, AggregationType as E, Day as F, type GetStaffMemberOptions as G, RecurringIntervalType as H, IdentityType as I, LocationStatus as J, LocationsLocationType as K, LocationType as L, Mode as M, DayOfWeek as N, ApprovalStatus as O, ScheduleStatus as P, type QueryStaffMembersOptions as Q, RequestedFields as R, type StaffMember as S, Transparency as T, type UpdateStaffMember as U, ConferenceType as V, CalendarType as W, Status as X, SessionType as Y, WebhookIdentityType as Z, type MediaItem as _, type UpdateStaffMemberOptions as a, type ValueResult as a$, type Resource as a0, type WorkingHoursSchedule as a1, type EventSchedule as a2, type LocationOptions as a3, type SpecificLocation as a4, type BusinessLocation as a5, type AssociatedWixIdentity as a6, type CommonIdentificationData as a7, type CommonIdentificationDataIdOneOf as a8, type Connection as a9, type RestoreStaffMemberFromTrashBinResponse as aA, type UpdateStaffMemberRequest as aB, type UpdateStaffMemberResponse as aC, type DeleteStaffMemberRequest as aD, type DeleteStaffMemberResponse as aE, type QueryStaffMembersRequest as aF, type CursorQuery as aG, type CursorQueryPagingMethodOneOf as aH, type Sorting as aI, type QueryStaffMembersResponse as aJ, type QueryStaffMembersMultiLanguageRequest as aK, type QueryStaffMembersMultiLanguageResponse as aL, type CountStaffMembersRequest as aM, type ConnectStaffMemberToUserRequest as aN, type SearchStaffMembersRequest as aO, type CursorSearch as aP, type CursorSearchPagingMethodOneOf as aQ, type SearchDetails as aR, type AggregationData as aS, type ValueAggregationResult as aT, type RangeAggregationResult as aU, type NestedAggregationResults as aV, type NestedAggregationResultsResultOneOf as aW, type ValueResults as aX, type RangeResults as aY, type AggregationResultsScalarResult as aZ, type NestedValueAggregationResult as a_, type AssociatedConferencingProviders as aa, type AssociatedConferencingProvider as ab, type AssociatedConferencingAccounts as ac, type AssociatedConferencingAccount as ad, type AssociatedConferencingAccountAccountOneOf as ae, type CustomConferenceAccount as af, type ExtendedFields as ag, type Tags as ah, type TagList as ai, type StaffMemberDisconnectedFromUser as aj, type StaffMemberConnectedToUser as ak, type StaffMemberFullyCreated as al, type CreateStaffMemberRequest as am, type CreateStaffMemberResponse as an, type GetStaffMemberRequest as ao, type GetStaffMemberResponse as ap, type GetDeletedStaffMemberRequest as aq, type GetDeletedStaffMemberResponse as ar, type ListDeletedStaffMembersRequest as as, type CursorPaging as at, type ListDeletedStaffMembersResponse as au, type CursorPagingMetadata as av, type Cursors as aw, type RemoveStaffMemberFromTrashBinRequest as ax, type RemoveStaffMemberFromTrashBinResponse as ay, type RestoreStaffMemberFromTrashBinRequest as az, type StaffMembersQueryBuilder as b, type ScheduleUpdated as b$, type RangeResult as b0, type ScalarResult as b1, type NestedResultValue as b2, type NestedResultValueResultOneOf as b3, type Results as b4, type DateHistogramResult as b5, type GroupByValueResults as b6, type DateHistogramResults as b7, type NestedResults as b8, type AggregationResults as b9, type RecurringInterval as bA, type Interval as bB, type Frequency as bC, type LinkedSchedule as bD, type Location as bE, type Address as bF, type AddressStreetOneOf as bG, type StreetAddress as bH, type AddressLocation as bI, type Subdivision as bJ, type LocationsLocation as bK, type LocationsAddress as bL, type LocationsStreetAddress as bM, type LocationsAddressLocation as bN, type BusinessSchedule as bO, type TimePeriod as bP, type SpecialHourPeriod as bQ, type Rate as bR, type Price as bS, type Availability as bT, type AvailabilityConstraints as bU, type SplitInterval as bV, type Participant as bW, type ExternalCalendarOverrides as bX, type Version as bY, type ConferenceProvider as bZ, type CalendarConference as b_, type AggregationResultsResultOneOf as ba, type DisconnectStaffMemberFromUserRequest as bb, type AssignWorkingHoursScheduleRequest as bc, type AssignCustomScheduleRequest as bd, type BulkUpdateStaffMemberTagsRequest as be, type ItemMetadata as bf, type ApplicationError as bg, type BulkUpdateStaffMemberTagsResult as bh, type BulkActionMetadata as bi, type BulkUpdateStaffMemberTagsByFilterRequest as bj, type RestoreStaffRequest as bk, type RestoreStaffResponse as bl, type Empty as bm, type PolicyRemovedFromContributor as bn, type PolicyUpdatedForContributor as bo, type DomainEvent as bp, type DomainEventBodyOneOf as bq, type EntityCreatedEvent as br, type RestoreInfo as bs, type EntityUpdatedEvent as bt, type EntityDeletedEvent as bu, type ActionEvent as bv, type ScheduleNotification as bw, type ScheduleNotificationEventOneOf as bx, type ScheduleCreated as by, type Schedule as bz, type CountStaffMembersOptions as c, countStaffMembers as c$, type RecurringSessionsUpdated as c0, type Session as c1, type CalendarDateTime as c2, type LocalDateTime as c3, type ExternalCalendarInfo as c4, type SessionVersion as c5, type ParticipantNotification as c6, type ScheduleCancelled as c7, type SessionCreated as c8, type SessionUpdated as c9, type AggregationTypeWithLiterals as cA, type DayWithLiterals as cB, type TransparencyWithLiterals as cC, type RecurringIntervalTypeWithLiterals as cD, type LocationTypeWithLiterals as cE, type LocationStatusWithLiterals as cF, type LocationsLocationTypeWithLiterals as cG, type DayOfWeekWithLiterals as cH, type ApprovalStatusWithLiterals as cI, type ScheduleStatusWithLiterals as cJ, type ConferenceTypeWithLiterals as cK, type CalendarTypeWithLiterals as cL, type StatusWithLiterals as cM, type SessionTypeWithLiterals as cN, type WebhookIdentityTypeWithLiterals as cO, type CommonSearchWithEntityContext as cP, onStaffMemberConnectedToUser as cQ, onStaffMemberCreated as cR, onStaffMemberDeleted as cS, onStaffMemberDisconnectedFromUser as cT, onStaffMemberFullyCreated as cU, onStaffMemberUpdated as cV, createStaffMember as cW, getStaffMember as cX, updateStaffMember as cY, deleteStaffMember as cZ, queryStaffMembers as c_, type SessionCancelled as ca, type AvailabilityPolicyUpdated as cb, type AvailabilityPolicy as cc, type IntervalSplit as cd, type RecurringSessionSplit as ce, type ScheduleUnassignedFromUser as cf, type MultipleSessionsCreated as cg, type ScheduleWithSessions as ch, type SitePropertiesOnScheduleCreation as ci, type MigrationEvent as cj, type MigrationData as ck, type StaffData as cl, type MessageEnvelope as cm, type IdentificationData as cn, type IdentificationDataIdOneOf as co, type BaseEventMetadata as cp, type EventMetadata as cq, type StaffMembersQueryResult as cr, type StaffMemberSearchSpec as cs, type IdentityTypeWithLiterals as ct, type AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals as cu, type ConnectionStatusWithLiterals as cv, type RequestedFieldsWithLiterals as cw, type SortOrderWithLiterals as cx, type ModeWithLiterals as cy, type ScalarTypeWithLiterals as cz, type CountStaffMembersResponse as d, connectStaffMemberToUser as d0, disconnectStaffMemberFromUser as d1, assignWorkingHoursSchedule as d2, assignCustomSchedule as d3, bulkUpdateStaffMemberTags as d4, bulkUpdateStaffMemberTagsByFilter as d5, type ConnectStaffMemberToUserOptions as e, type ConnectStaffMemberToUserResponse as f, type StaffMemberSearch as g, type SearchStaffMembersOptions as h, type SearchStaffMembersResponse as i, type DisconnectStaffMemberFromUserResponse as j, type AssignWorkingHoursScheduleResponse as k, type AssignCustomScheduleOptions as l, type AssignCustomScheduleResponse as m, type BulkUpdateStaffMemberTagsResponse as n, type BulkUpdateStaffMemberTagsByFilterOptions as o, type BulkUpdateStaffMemberTagsByFilterResponse as p, type StaffMemberConnectedToUserEnvelope as q, type StaffMemberCreatedEnvelope as r, type StaffMemberDeletedEnvelope as s, type StaffMemberDisconnectedFromUserEnvelope as t, type StaffMemberFullyCreatedEnvelope as u, type StaffMemberUpdatedEnvelope as v, AssociatedWixIdentityConnectionStatusEnumConnectionStatus as w, ConnectionStatus as x, SortOrder as y, ScalarType as z };
