"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  assignCustomSchedule: () => assignCustomSchedule2,
  assignWorkingHoursSchedule: () => assignWorkingHoursSchedule2,
  bulkUpdateStaffMemberTags: () => bulkUpdateStaffMemberTags2,
  bulkUpdateStaffMemberTagsByFilter: () => bulkUpdateStaffMemberTagsByFilter2,
  connectStaffMemberToUser: () => connectStaffMemberToUser2,
  countStaffMembers: () => countStaffMembers2,
  createStaffMember: () => createStaffMember2,
  deleteStaffMember: () => deleteStaffMember2,
  disconnectStaffMemberFromUser: () => disconnectStaffMemberFromUser2,
  getStaffMember: () => getStaffMember2,
  queryStaffMembers: () => queryStaffMembers2,
  searchStaffMembers: () => searchStaffMembers2,
  updateStaffMember: () => updateStaffMember2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-staff-v1-staff-member-staff-members.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_float = require("@wix/sdk-runtime/transformations/float");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsStaffV1StaffMembersServiceUrl(opts) {
  const domainToMappings = {
    "www._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/staff-members",
        destPath: "/v1/staff-members"
      },
      {
        srcPath: "/bookings/v1/bulk/staff-members",
        destPath: "/v1/bulk/staff-members"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_staff-members";
function createStaffMember(payload) {
  function __createStaffMember({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "staffMember.createdDate" },
          { path: "staffMember.updatedDate" },
          { path: "staffMember.mainMedia.image.urlExpirationDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.CreateStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createStaffMember;
}
function getStaffMember(payload) {
  function __getStaffMember({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "GET",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.GetStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getStaffMember;
}
function updateStaffMember(payload) {
  function __updateStaffMember({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "staffMember.createdDate" },
          { path: "staffMember.updatedDate" },
          { path: "staffMember.mainMedia.image.urlExpirationDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "PATCH",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.UpdateStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMember.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateStaffMember;
}
function deleteStaffMember(payload) {
  function __deleteStaffMember({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "DELETE",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.DeleteStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteStaffMember;
}
function queryStaffMembers(payload) {
  function __queryStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.QueryStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMembers.createdDate" },
            { path: "staffMembers.updatedDate" },
            { path: "staffMembers.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryStaffMembers;
}
function countStaffMembers(payload) {
  function __countStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.CountStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countStaffMembers;
}
function connectStaffMemberToUser(payload) {
  function __connectStaffMemberToUser({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.ConnectStaffMemberToUser",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/connect-staff-member-to-user",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __connectStaffMemberToUser;
}
function searchStaffMembers(payload) {
  function __searchStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/search",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMembers.createdDate" },
            { path: "staffMembers.updatedDate" },
            { path: "staffMembers.mainMedia.image.urlExpirationDate" }
          ]
        },
        {
          transformFn: import_float.transformRESTFloatToSDKFloat,
          paths: [
            { path: "aggregationData.results.ranges.results.from" },
            { path: "aggregationData.results.ranges.results.to" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from"
            },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.from"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.to"
            },
            { path: "aggregationData.results.scalar.value" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.scalar.value"
            },
            {
              path: "aggregationData.results.nested.results.results.*.scalar.value"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __searchStaffMembers;
}
function disconnectStaffMemberFromUser(payload) {
  function __disconnectStaffMemberFromUser({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.DisconnectStaffMemberFromUser",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __disconnectStaffMemberFromUser;
}
function assignWorkingHoursSchedule(payload) {
  function __assignWorkingHoursSchedule({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/assign-working-hours-schedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __assignWorkingHoursSchedule;
}
function assignCustomSchedule(payload) {
  function __assignCustomSchedule({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.AssignCustomSchedule",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/assign-custom-schedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __assignCustomSchedule;
}
function bulkUpdateStaffMemberTags(payload) {
  function __bulkUpdateStaffMemberTags({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTags",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/bulk/staff-members/update-tags",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkUpdateStaffMemberTags;
}
function bulkUpdateStaffMemberTagsByFilter(payload) {
  function __bulkUpdateStaffMemberTagsByFilter({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTagsByFilter",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/bulk/staff-members/update-tags-by-filter",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkUpdateStaffMemberTagsByFilter;
}

// src/bookings-staff-v1-staff-member-staff-members.meta.ts
function createStaffMember2() {
  const payload = {};
  const getRequestOptions = createStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getStaffMember2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = getStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/staff-members/{staffMemberId}",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateStaffMember2() {
  const payload = { staffMember: { id: ":staffMemberId" } };
  const getRequestOptions = updateStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v1/staff-members/{staffMember.id}",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteStaffMember2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = deleteStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v1/staff-members/{staffMemberId}",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryStaffMembers2() {
  const payload = {};
  const getRequestOptions = queryStaffMembers(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countStaffMembers2() {
  const payload = {};
  const getRequestOptions = countStaffMembers(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function connectStaffMemberToUser2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = connectStaffMemberToUser(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/connect-staff-member-to-user",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function searchStaffMembers2() {
  const payload = {};
  const getRequestOptions = searchStaffMembers(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/search",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function disconnectStaffMemberFromUser2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = disconnectStaffMemberFromUser(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function assignWorkingHoursSchedule2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = assignWorkingHoursSchedule(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/assign-working-hours-schedule",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function assignCustomSchedule2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = assignCustomSchedule(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/assign-custom-schedule",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkUpdateStaffMemberTags2() {
  const payload = {};
  const getRequestOptions = bulkUpdateStaffMemberTags(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/bulk/staff-members/update-tags",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkUpdateStaffMemberTagsByFilter2() {
  const payload = {};
  const getRequestOptions = bulkUpdateStaffMemberTagsByFilter(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/bulk/staff-members/update-tags-by-filter",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  assignCustomSchedule,
  assignWorkingHoursSchedule,
  bulkUpdateStaffMemberTags,
  bulkUpdateStaffMemberTagsByFilter,
  connectStaffMemberToUser,
  countStaffMembers,
  createStaffMember,
  deleteStaffMember,
  disconnectStaffMemberFromUser,
  getStaffMember,
  queryStaffMembers,
  searchStaffMembers,
  updateStaffMember
});
//# sourceMappingURL=meta.js.map