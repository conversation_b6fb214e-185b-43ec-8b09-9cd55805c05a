// src/bookings-availability-v1-slot-availability-availability-calendar.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v2/availability/schedule",
        destPath: "/v2/availability/schedule"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      },
      {
        srcPath: "/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      },
      {
        srcPath: "/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      },
      {
        srcPath: "/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "editor-flow.wixapps.net": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/bookings/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/bookings/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar",
        destPath: ""
      },
      {
        srcPath: "/_api/availability-calendar/v2/availability/schedule",
        destPath: "/v2/availability/schedule"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      },
      {
        srcPath: "/_api/availability-calendar/v1/availability/multislot",
        destPath: "/v1/availability/multislot"
      }
    ],
    "bookings.wixapps.net": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/availability-calendar/v1/availability/query",
        destPath: "/v1/availability/query"
      }
    ],
    "*.wixforms.com": [
      {
        srcPath: "/_api/availability-calendar",
        destPath: ""
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/availability-calendar",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_availability-calendar";
function queryAvailability(payload) {
  function __queryAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v1.slot_availability",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.AvailabilityCalendar.QueryAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({
        protoPath: "/v1/availability/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryAvailability;
}
function getScheduleAvailability(payload) {
  function __getScheduleAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v1.slot_availability",
      method: "GET",
      methodFqn: "com.wixpress.bookings.availability.AvailabilityCalendar.GetScheduleAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({
        protoPath: "/v2/availability/schedule/{scheduleId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __getScheduleAvailability;
}
function calculateMultiSlotAvailability(payload) {
  function __calculateMultiSlotAvailability({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v1.slot_availability",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.AvailabilityCalendar.CalculateMultiSlotAvailability",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({
        protoPath: "/v1/availability/multislot",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __calculateMultiSlotAvailability;
}

// src/bookings-availability-v1-slot-availability-availability-calendar.meta.ts
function queryAvailability2() {
  const payload = {};
  const getRequestOptions = queryAvailability(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/availability/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getScheduleAvailability2() {
  const payload = { scheduleId: ":scheduleId" };
  const getRequestOptions = getScheduleAvailability(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/availability/schedule/{scheduleId}",
    pathParams: { scheduleId: "scheduleId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function calculateMultiSlotAvailability2() {
  const payload = {};
  const getRequestOptions = calculateMultiSlotAvailability(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/availability/multislot",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  calculateMultiSlotAvailability2 as calculateMultiSlotAvailability,
  getScheduleAvailability2 as getScheduleAvailability,
  queryAvailability2 as queryAvailability
};
//# sourceMappingURL=meta.mjs.map