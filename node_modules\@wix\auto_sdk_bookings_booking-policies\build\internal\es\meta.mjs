// src/bookings-v1-booking-policy-booking-policies.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsV1BookingPoliciesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v1/booking-policies/strictest",
        destPath: "/v1/booking-policies/strictest"
      },
      {
        srcPath: "/_api/bookings/v1/booking-policies/query",
        destPath: "/v1/booking-policies/query"
      },
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policies";
function createBookingPolicy(payload) {
  function __createBookingPolicy({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBookingPolicy;
}
function getBookingPolicy(payload) {
  function __getBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "GET",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getBookingPolicy;
}
function getStrictestBookingPolicy(payload) {
  function __getStrictestBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/strictest",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getStrictestBookingPolicy;
}
function updateBookingPolicy(payload) {
  function __updateBookingPolicy({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "PATCH",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicy.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateBookingPolicy;
}
function setDefaultBookingPolicy(payload) {
  function __setDefaultBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}:setDefault",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "currentDefaultBookingPolicy.createdDate" },
            { path: "currentDefaultBookingPolicy.updatedDate" },
            { path: "previousDefaultBookingPolicy.createdDate" },
            { path: "previousDefaultBookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setDefaultBookingPolicy;
}
function deleteBookingPolicy(payload) {
  function __deleteBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "DELETE",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteBookingPolicy;
}
function queryBookingPolicies(payload) {
  function __queryBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicies.createdDate" },
            { path: "bookingPolicies.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryBookingPolicies;
}
function countBookingPolicies(payload) {
  function __countBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CountBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countBookingPolicies;
}

// src/bookings-v1-booking-policy-booking-policies.meta.ts
function createBookingPolicy2() {
  const payload = {};
  const getRequestOptions = createBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getBookingPolicy2() {
  const payload = { bookingPolicyId: ":bookingPolicyId" };
  const getRequestOptions = getBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/booking-policies/{bookingPolicyId}",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getStrictestBookingPolicy2() {
  const payload = {};
  const getRequestOptions = getStrictestBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/strictest",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateBookingPolicy2() {
  const payload = { bookingPolicy: { id: ":bookingPolicyId" } };
  const getRequestOptions = updateBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v1/booking-policies/{bookingPolicy.id}",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function setDefaultBookingPolicy2() {
  const payload = { bookingPolicyId: ":bookingPolicyId" };
  const getRequestOptions = setDefaultBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/{bookingPolicyId}:setDefault",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteBookingPolicy2() {
  const payload = { bookingPolicyId: ":bookingPolicyId" };
  const getRequestOptions = deleteBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v1/booking-policies/{bookingPolicyId}",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryBookingPolicies2() {
  const payload = {};
  const getRequestOptions = queryBookingPolicies(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countBookingPolicies2() {
  const payload = {};
  const getRequestOptions = countBookingPolicies(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  countBookingPolicies2 as countBookingPolicies,
  createBookingPolicy2 as createBookingPolicy,
  deleteBookingPolicy2 as deleteBookingPolicy,
  getBookingPolicy2 as getBookingPolicy,
  getStrictestBookingPolicy2 as getStrictestBookingPolicy,
  queryBookingPolicies2 as queryBookingPolicies,
  setDefaultBookingPolicy2 as setDefaultBookingPolicy,
  updateBookingPolicy2 as updateBookingPolicy
};
//# sourceMappingURL=meta.mjs.map