import { CreateBackupRequest as CreateBackupRequest$1, CreateBackupResponse as CreateBackupResponse$1, ListBackupsRequest as ListBackupsRequest$1, ListBackupsResponse as ListBackupsResponse$1, RestoreBackupRequest as RestoreBackupRequest$1, RestoreBackupResponse as RestoreBackupResponse$1, RestorePartialBackupRequest as RestorePartialBackupRequest$1, RestorePartialBackupResponse as RestorePartialBackupResponse$1, ListRestorationsRequest as ListRestorationsRequest$1, ListRestorationsResponse as ListRestorationsResponse$1, DeleteBackupRequest as DeleteBackupRequest$1, DeleteBackupResponse as DeleteBackupResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

interface Backup {
    /**
     * Backup ID.
     * @format GUID
     * @readonly
     */
    id?: string;
    /**
     * Backup status.
     * @readonly
     */
    status?: StatusWithLiterals;
    /**
     * Type of backup, based on how it was triggered.
     * @readonly
     */
    type?: TypeWithLiterals;
    /**
     * Date and time the backup was requested.
     * @readonly
     */
    requestedDate?: Date | null;
    /**
     * Date and time the backup commenced. Value is `null` until the backup process begins in the background.
     * @readonly
     */
    startedDate?: Date | null;
    /**
     * Date and time the backup process finished. Value is `null` until the backup process is completed in the background.
     * @readonly
     */
    finishedDate?: Date | null;
    /**
     * Date and time the backup was deleted. Value is `null` if that backup hasn't been deleted.
     * @readonly
     */
    deletedDate?: Date | null;
    /**
     * Backup size in bytes. Value is `null` until the backup process is completed.
     * @readonly
     */
    sizeInBytes?: string | null;
    /**
     * IDs and display names of collections the backup contains.
     * @readonly
     * @maxSize 1000
     */
    collections?: Collection[];
}
declare enum Status {
    /** Backup creation is in progress. */
    PENDING = "PENDING",
    /** Backup has been created successfully and can be used for data restoration. */
    READY = "READY",
    /** Backup creation has failed. */
    FAILED = "FAILED",
    /** Backup has been deleted. */
    DELETED = "DELETED",
    /** Backup has been canceled. */
    CANCELED = "CANCELED"
}
/** @enumType */
type StatusWithLiterals = Status | 'PENDING' | 'READY' | 'FAILED' | 'DELETED' | 'CANCELED';
declare enum Type {
    /** Backup taken on demand. */
    ON_DEMAND = "ON_DEMAND",
    /** Backup taken automatically by the system on a regular schedule. */
    AUTO = "AUTO"
}
/** @enumType */
type TypeWithLiterals = Type | 'ON_DEMAND' | 'AUTO';
interface Collection {
    /**
     * Collection ID.
     * @readonly
     * @maxLength 255
     */
    id?: string;
    /**
     * Collection display name.
     * @readonly
     * @maxLength 1000
     */
    displayName?: string | null;
}
interface Restoration {
    /**
     * Restoration ID.
     * @format GUID
     * @readonly
     */
    id?: string;
    /**
     * Details of the backup used for the restoration.
     * @readonly
     */
    backup?: Backup;
    /**
     * Status of restoration.
     * @readonly
     */
    status?: RestorationStatusWithLiterals;
    /**
     * Date and time the restoration was requested.
     * @readonly
     */
    requestedDate?: Date | null;
    /**
     * Date and time the restoration commenced. Value is `null` until the restoration process begins in the background.
     * @readonly
     */
    startedDate?: Date | null;
    /**
     * Date and time the restoration finished. Value is `null` until the restoration process is completed in the background.
     * @readonly
     */
    finishedDate?: Date | null;
    /**
     * Restored collections.
     * @readonly
     * @maxSize 1000
     */
    restorationCollections?: RestorationCollection[];
}
declare enum RestorationStatus {
    /** Restoration from a backup is in progress. */
    PENDING = "PENDING",
    /** Restoration from a backup has been successful. */
    COMPLETED = "COMPLETED",
    /** Restoration from a backup has failed. */
    FAILED = "FAILED"
}
/** @enumType */
type RestorationStatusWithLiterals = RestorationStatus | 'PENDING' | 'COMPLETED' | 'FAILED';
interface RestorationCollection {
    /**
     * Collections to be restored.
     *
     * Note: If collections have a multi-reference relationship,
     * the preexisting references will be restored if at least one of those collections are restored.
     * @minLength 1
     * @maxLength 1000
     */
    dataCollectionId?: string;
    /**
     * Destination where to restore the collection.
     * When not specified destination is taken from backup.
     */
    restoreDestination?: RestoreDestination;
}
interface RestoreDestination {
    /**
     * Collection ID.
     * @minLength 1
     * @maxLength 255
     */
    dataCollectionId?: string;
    /**
     * Collection's display name as shown in the CMS. If empty, `displayName` is taken from `backup.collections`.
     * @maxLength 1000
     */
    displayName?: string | null;
}
interface CreateBackupRequest {
}
interface CreateBackupResponse {
    /** Details of the requested backup. */
    backup?: Backup;
}
interface ListBackupsRequest {
    /**
     * Statuses to filter by.
     * If provided, only backups with the specified statuses are listed.
     * For example, to list only completed backups, use `?status=READY`.
     * To list completed and pending backups, use `?status=READY&status=PENDING`.
     *
     * Default: No filtering
     * @maxSize 10
     */
    status?: StatusWithLiterals[];
    /**
     * Type to filter by. If provided, only backups of the specified type are listed.
     *
     * Default: No filtering
     * @maxSize 10
     */
    type?: TypeWithLiterals[];
    /** Paging preferences. */
    paging?: Paging;
}
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface ListBackupsResponse {
    /**
     * Retrieved backups.
     * @maxSize 1000
     */
    backups?: Backup[];
    /** Paging information. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
}
interface RestoreBackupRequest {
    /**
     * ID of backup to be restored.
     * @format GUID
     */
    backupId: string;
}
interface RestoreBackupResponse {
    /** Details of data restoration from backup. */
    restoration?: Restoration;
}
interface RestorePartialBackupRequest {
    /**
     * ID of backup to be restored.
     * @format GUID
     */
    backupId: string;
    /**
     * **Required.** Collections to be restored.
     *
     * Note: If collections have a multi-reference relationship,
     * the preexisting references will be restored if at least one of those collections are restored.
     * @maxSize 1000
     */
    restorationCollections?: RestorationCollection[];
}
interface RestorePartialBackupResponse {
    /** Details of data restoration from backup. */
    restoration?: Restoration;
}
interface ListRestorationsRequest {
    /**
     * Statuses to filter by. If provided, only restorations with the specified statuses are listed.
     * For example, to list only completed restorations, use `?status=COMPLETED`.
     * To list completed and pending restorations, use `?status=COMPLETED&status=PENDING`.
     *
     * Default: No filtering
     * @maxSize 10
     */
    status?: RestorationStatusWithLiterals[];
    /** Offset and limit of items to retrieve. */
    paging?: Paging;
}
interface ListRestorationsResponse {
    /**
     * Retrieved restorations.
     * @maxSize 1000
     */
    restorations?: Restoration[];
    /** Paging information. */
    pagingMetadata?: PagingMetadataV2;
}
interface DeleteBackupRequest {
    /**
     * ID of the backup to be deleted.
     * @format GUID
     */
    backupId: string;
}
interface DeleteBackupResponse {
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createBackup(): __PublicMethodMetaInfo<'POST', {}, CreateBackupRequest$1, CreateBackupRequest, CreateBackupResponse$1, CreateBackupResponse>;
declare function listBackups(): __PublicMethodMetaInfo<'GET', {}, ListBackupsRequest$1, ListBackupsRequest, ListBackupsResponse$1, ListBackupsResponse>;
declare function restoreBackup(): __PublicMethodMetaInfo<'POST', {
    backupId: string;
}, RestoreBackupRequest$1, RestoreBackupRequest, RestoreBackupResponse$1, RestoreBackupResponse>;
declare function restorePartialBackup(): __PublicMethodMetaInfo<'POST', {
    backupId: string;
}, RestorePartialBackupRequest$1, RestorePartialBackupRequest, RestorePartialBackupResponse$1, RestorePartialBackupResponse>;
declare function listRestorations(): __PublicMethodMetaInfo<'GET', {}, ListRestorationsRequest$1, ListRestorationsRequest, ListRestorationsResponse$1, ListRestorationsResponse>;
declare function deleteBackup(): __PublicMethodMetaInfo<'DELETE', {
    backupId: string;
}, DeleteBackupRequest$1, DeleteBackupRequest, DeleteBackupResponse$1, DeleteBackupResponse>;

export { type __PublicMethodMetaInfo, createBackup, deleteBackup, listBackups, listRestorations, restoreBackup, restorePartialBackup };
