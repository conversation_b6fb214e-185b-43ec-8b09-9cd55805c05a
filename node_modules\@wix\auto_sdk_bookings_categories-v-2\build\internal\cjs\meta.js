"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  countCategories: () => countCategories2,
  createCategory: () => createCategory2,
  deleteCategory: () => deleteCategory2,
  getCategory: () => getCategory2,
  moveCategory: () => moveCategory2,
  queryCategories: () => queryCategories2,
  updateCategory: () => updateCategory2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-categories-v2-category-categories-v-2.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsCategoriesV2CategoriesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/categories",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_categories-v-2";
function createCategory(payload) {
  function __createCategory({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "category.createdDate" },
          { path: "category.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.CreateCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createCategory;
}
function getCategory(payload) {
  function __getCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "GET",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.GetCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{categoryId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getCategory;
}
function updateCategory(payload) {
  function __updateCategory({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "category.createdDate" },
          { path: "category.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "PATCH",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.UpdateCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{category.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateCategory;
}
function deleteCategory(payload) {
  function __deleteCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "DELETE",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.DeleteCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{categoryId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteCategory;
}
function queryCategories(payload) {
  function __queryCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.QueryCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "categories.createdDate" },
            { path: "categories.updatedDate" }
          ]
        }
      ]),
      fallback: [
        {
          method: "POST",
          url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
            protoPath: "/v2/categories/query",
            data: payload,
            host
          }),
          data: payload
        }
      ]
    };
    return metadata;
  }
  return __queryCategories;
}
function countCategories(payload) {
  function __countCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.CountCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countCategories;
}
function moveCategory(payload) {
  function __moveCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.MoveCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/set-position/{categoryId}",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __moveCategory;
}

// src/bookings-categories-v2-category-categories-v-2.meta.ts
function createCategory2() {
  const payload = {};
  const getRequestOptions = createCategory(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/categories",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getCategory2() {
  const payload = { categoryId: ":categoryId" };
  const getRequestOptions = getCategory(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/categories/{categoryId}",
    pathParams: { categoryId: "categoryId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateCategory2() {
  const payload = { category: { id: ":categoryId" } };
  const getRequestOptions = updateCategory(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v2/categories/{category.id}",
    pathParams: { categoryId: "categoryId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteCategory2() {
  const payload = { categoryId: ":categoryId" };
  const getRequestOptions = deleteCategory(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v2/categories/{categoryId}",
    pathParams: { categoryId: "categoryId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryCategories2() {
  const payload = {};
  const getRequestOptions = queryCategories(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/categories/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countCategories2() {
  const payload = {};
  const getRequestOptions = countCategories(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/categories/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function moveCategory2() {
  const payload = { categoryId: ":categoryId" };
  const getRequestOptions = moveCategory(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/categories/set-position/{categoryId}",
    pathParams: { categoryId: "categoryId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  countCategories,
  createCategory,
  deleteCategory,
  getCategory,
  moveCategory,
  queryCategories,
  updateCategory
});
//# sourceMappingURL=meta.js.map