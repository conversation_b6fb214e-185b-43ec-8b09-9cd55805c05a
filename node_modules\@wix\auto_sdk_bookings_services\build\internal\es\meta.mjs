// src/bookings-services-v2-service-services.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKFloatToRESTFloat } from "@wix/sdk-runtime/transformations/float";
import { transformRESTFloatToSDKFloat } from "@wix/sdk-runtime/transformations/float";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsServicesV2ServicesServiceUrl(opts) {
  const domainToMappings = {
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/services-2",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      },
      {
        srcPath: "/bookings/services/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/services-2",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
function resolveWixBookingsServicesV2AddOnGroupsServiceUrl(opts) {
  const domainToMappings = {
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/services-2",
        destPath: ""
      },
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      },
      {
        srcPath: "/bookings/services/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      },
      {
        srcPath: "/_api/bookings/v2/bulk/services",
        destPath: "/v2/bulk/services"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/bookings/v2/services",
        destPath: "/v2/services"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/services-2",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_services";
function createAddOnGroup(payload) {
  function __createAddOnGroup({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.CreateAddOnGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/create",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __createAddOnGroup;
}
function deleteAddOnGroup(payload) {
  function __deleteAddOnGroup({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.DeleteAddOnGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/delete",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __deleteAddOnGroup;
}
function updateAddOnGroup(payload) {
  function __updateAddOnGroup({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.UpdateAddOnGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/update",
        data: serializedData,
        host
      }),
      data: serializedData
    };
    return metadata;
  }
  return __updateAddOnGroup;
}
function listAddOnGroupsByServiceId(payload) {
  function __listAddOnGroupsByServiceId({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.ListAddOnGroupsByServiceId",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/list-add-on-groups-by-service-id",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __listAddOnGroupsByServiceId;
}
function setAddOnsForGroup(payload) {
  function __setAddOnsForGroup({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.AddOnGroupsService.SetAddOnsForGroup",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({
        protoPath: "/v2/services/add-on-groups/set-add-ons-for-group",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __setAddOnsForGroup;
}
function createService(payload) {
  function __createService({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "service.createdDate" },
          { path: "service.updatedDate" },
          { path: "service.media.items.image.urlExpirationDate" },
          { path: "service.media.mainMedia.image.urlExpirationDate" },
          { path: "service.media.coverMedia.image.urlExpirationDate" },
          { path: "service.bookingPolicy.createdDate" },
          { path: "service.bookingPolicy.updatedDate" },
          { path: "service.schedule.firstSessionStart" },
          { path: "service.schedule.lastSessionEnd" },
          { path: "service.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "service.supportedSlugs.createdDate" },
          { path: "service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "service.locations.business.address.geocode.latitude" },
          { path: "service.locations.business.address.geocode.longitude" },
          { path: "service.locations.custom.address.geocode.latitude" },
          { path: "service.locations.custom.address.geocode.longitude" },
          { path: "service.locations.calculatedAddress.geocode.latitude" },
          { path: "service.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.CreateService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createService;
}
function bulkCreateServices(payload) {
  function __bulkCreateServices({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "services.createdDate" },
          { path: "services.updatedDate" },
          { path: "services.media.items.image.urlExpirationDate" },
          { path: "services.media.mainMedia.image.urlExpirationDate" },
          { path: "services.media.coverMedia.image.urlExpirationDate" },
          { path: "services.bookingPolicy.createdDate" },
          { path: "services.bookingPolicy.updatedDate" },
          { path: "services.schedule.firstSessionStart" },
          { path: "services.schedule.lastSessionEnd" },
          { path: "services.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "services.supportedSlugs.createdDate" },
          { path: "services.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "services.locations.business.address.geocode.latitude" },
          { path: "services.locations.business.address.geocode.longitude" },
          { path: "services.locations.custom.address.geocode.latitude" },
          { path: "services.locations.custom.address.geocode.longitude" },
          { path: "services.locations.calculatedAddress.geocode.latitude" },
          { path: "services.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkCreateServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/create",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.media.items.image.urlExpirationDate" },
            { path: "results.item.media.mainMedia.image.urlExpirationDate" },
            { path: "results.item.media.coverMedia.image.urlExpirationDate" },
            { path: "results.item.bookingPolicy.createdDate" },
            { path: "results.item.bookingPolicy.updatedDate" },
            { path: "results.item.schedule.firstSessionStart" },
            { path: "results.item.schedule.lastSessionEnd" },
            {
              path: "results.item.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "results.item.supportedSlugs.createdDate" },
            { path: "results.item.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.locations.business.address.geocode.latitude"
            },
            {
              path: "results.item.locations.business.address.geocode.longitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.latitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.longitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkCreateServices;
}
function getService(payload) {
  function __getService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "GET",
      methodFqn: "wix.bookings.services.v2.ServicesService.GetService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getService;
}
function updateService(payload) {
  function __updateService({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "service.createdDate" },
          { path: "service.updatedDate" },
          { path: "service.media.items.image.urlExpirationDate" },
          { path: "service.media.mainMedia.image.urlExpirationDate" },
          { path: "service.media.coverMedia.image.urlExpirationDate" },
          { path: "service.bookingPolicy.createdDate" },
          { path: "service.bookingPolicy.updatedDate" },
          { path: "service.schedule.firstSessionStart" },
          { path: "service.schedule.lastSessionEnd" },
          { path: "service.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "service.supportedSlugs.createdDate" },
          { path: "service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "service.locations.business.address.geocode.latitude" },
          { path: "service.locations.business.address.geocode.longitude" },
          { path: "service.locations.custom.address.geocode.latitude" },
          { path: "service.locations.custom.address.geocode.longitude" },
          { path: "service.locations.calculatedAddress.geocode.latitude" },
          { path: "service.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "PATCH",
      methodFqn: "wix.bookings.services.v2.ServicesService.UpdateService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{service.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateService;
}
function bulkUpdateServices(payload) {
  function __bulkUpdateServices({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "services.mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "services.service.createdDate" },
          { path: "services.service.updatedDate" },
          { path: "services.service.media.items.image.urlExpirationDate" },
          { path: "services.service.media.mainMedia.image.urlExpirationDate" },
          { path: "services.service.media.coverMedia.image.urlExpirationDate" },
          { path: "services.service.bookingPolicy.createdDate" },
          { path: "services.service.bookingPolicy.updatedDate" },
          { path: "services.service.schedule.firstSessionStart" },
          { path: "services.service.schedule.lastSessionEnd" },
          {
            path: "services.service.staffMembers.mainMedia.image.urlExpirationDate"
          },
          {
            path: "services.service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "services.service.supportedSlugs.createdDate" },
          { path: "services.service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          {
            path: "services.service.locations.business.address.geocode.latitude"
          },
          {
            path: "services.service.locations.business.address.geocode.longitude"
          },
          {
            path: "services.service.locations.custom.address.geocode.latitude"
          },
          {
            path: "services.service.locations.custom.address.geocode.longitude"
          },
          {
            path: "services.service.locations.calculatedAddress.geocode.latitude"
          },
          {
            path: "services.service.locations.calculatedAddress.geocode.longitude"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkUpdateServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/update",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.media.items.image.urlExpirationDate" },
            { path: "results.item.media.mainMedia.image.urlExpirationDate" },
            { path: "results.item.media.coverMedia.image.urlExpirationDate" },
            { path: "results.item.bookingPolicy.createdDate" },
            { path: "results.item.bookingPolicy.updatedDate" },
            { path: "results.item.schedule.firstSessionStart" },
            { path: "results.item.schedule.lastSessionEnd" },
            {
              path: "results.item.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "results.item.supportedSlugs.createdDate" },
            { path: "results.item.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.locations.business.address.geocode.latitude"
            },
            {
              path: "results.item.locations.business.address.geocode.longitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.latitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.longitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkUpdateServices;
}
function bulkUpdateServicesByFilter(payload) {
  function __bulkUpdateServicesByFilter({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "service.createdDate" },
          { path: "service.updatedDate" },
          { path: "service.media.items.image.urlExpirationDate" },
          { path: "service.media.mainMedia.image.urlExpirationDate" },
          { path: "service.media.coverMedia.image.urlExpirationDate" },
          { path: "service.bookingPolicy.createdDate" },
          { path: "service.bookingPolicy.updatedDate" },
          { path: "service.schedule.firstSessionStart" },
          { path: "service.schedule.lastSessionEnd" },
          { path: "service.staffMembers.mainMedia.image.urlExpirationDate" },
          {
            path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
          },
          { path: "service.supportedSlugs.createdDate" },
          { path: "service.mainSlug.createdDate" }
        ]
      },
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "service.locations.business.address.geocode.latitude" },
          { path: "service.locations.business.address.geocode.longitude" },
          { path: "service.locations.custom.address.geocode.latitude" },
          { path: "service.locations.custom.address.geocode.longitude" },
          { path: "service.locations.calculatedAddress.geocode.latitude" },
          { path: "service.locations.calculatedAddress.geocode.longitude" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkUpdateServicesByFilter",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/update-by-filter",
        data: serializedData,
        host
      }),
      data: serializedData
    };
    return metadata;
  }
  return __bulkUpdateServicesByFilter;
}
function deleteService(payload) {
  function __deleteService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "DELETE",
      methodFqn: "wix.bookings.services.v2.ServicesService.DeleteService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteService;
}
function bulkDeleteServices(payload) {
  function __bulkDeleteServices({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkDeleteServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/delete",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "results.item.createdDate" },
            { path: "results.item.updatedDate" },
            { path: "results.item.media.items.image.urlExpirationDate" },
            { path: "results.item.media.mainMedia.image.urlExpirationDate" },
            { path: "results.item.media.coverMedia.image.urlExpirationDate" },
            { path: "results.item.bookingPolicy.createdDate" },
            { path: "results.item.bookingPolicy.updatedDate" },
            { path: "results.item.schedule.firstSessionStart" },
            { path: "results.item.schedule.lastSessionEnd" },
            {
              path: "results.item.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "results.item.supportedSlugs.createdDate" },
            { path: "results.item.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "results.item.locations.business.address.geocode.latitude"
            },
            {
              path: "results.item.locations.business.address.geocode.longitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.latitude"
            },
            {
              path: "results.item.locations.custom.address.geocode.longitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "results.item.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __bulkDeleteServices;
}
function bulkDeleteServicesByFilter(payload) {
  function __bulkDeleteServicesByFilter({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.BulkDeleteServicesByFilter",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/bulk/services/delete-by-filter",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkDeleteServicesByFilter;
}
function queryServices(payload) {
  function __queryServices({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "services.createdDate" },
            { path: "services.updatedDate" },
            { path: "services.media.items.image.urlExpirationDate" },
            { path: "services.media.mainMedia.image.urlExpirationDate" },
            { path: "services.media.coverMedia.image.urlExpirationDate" },
            { path: "services.bookingPolicy.createdDate" },
            { path: "services.bookingPolicy.updatedDate" },
            { path: "services.schedule.firstSessionStart" },
            { path: "services.schedule.lastSessionEnd" },
            {
              path: "services.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "services.supportedSlugs.createdDate" },
            { path: "services.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "services.locations.business.address.geocode.latitude" },
            { path: "services.locations.business.address.geocode.longitude" },
            { path: "services.locations.custom.address.geocode.latitude" },
            { path: "services.locations.custom.address.geocode.longitude" },
            { path: "services.locations.calculatedAddress.geocode.latitude" },
            {
              path: "services.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryServices;
}
function searchServices(payload) {
  function __searchServices({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "search.aggregations.range.buckets.from" },
          { path: "search.aggregations.range.buckets.to" },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.from"
          },
          {
            path: "search.aggregations.nested.nestedAggregations.range.buckets.to"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.SearchServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/search",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "services.createdDate" },
            { path: "services.updatedDate" },
            { path: "services.media.items.image.urlExpirationDate" },
            { path: "services.media.mainMedia.image.urlExpirationDate" },
            { path: "services.media.coverMedia.image.urlExpirationDate" },
            { path: "services.bookingPolicy.createdDate" },
            { path: "services.bookingPolicy.updatedDate" },
            { path: "services.schedule.firstSessionStart" },
            { path: "services.schedule.lastSessionEnd" },
            {
              path: "services.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "services.supportedSlugs.createdDate" },
            { path: "services.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "services.locations.business.address.geocode.latitude" },
            { path: "services.locations.business.address.geocode.longitude" },
            { path: "services.locations.custom.address.geocode.latitude" },
            { path: "services.locations.custom.address.geocode.longitude" },
            { path: "services.locations.calculatedAddress.geocode.latitude" },
            {
              path: "services.locations.calculatedAddress.geocode.longitude"
            },
            { path: "aggregationData.results.ranges.results.from" },
            { path: "aggregationData.results.ranges.results.to" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from"
            },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.from"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.to"
            },
            { path: "aggregationData.results.scalar.value" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.scalar.value"
            },
            {
              path: "aggregationData.results.nested.results.results.*.scalar.value"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __searchServices;
}
function queryPolicies(payload) {
  function __queryPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/policies/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicies.bookingPolicy.createdDate" },
            { path: "bookingPolicies.bookingPolicy.updatedDate" },
            { path: "bookingPolicies.services.createdDate" },
            { path: "bookingPolicies.services.updatedDate" },
            {
              path: "bookingPolicies.services.media.items.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.services.media.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.services.media.coverMedia.image.urlExpirationDate"
            },
            { path: "bookingPolicies.services.bookingPolicy.createdDate" },
            { path: "bookingPolicies.services.bookingPolicy.updatedDate" },
            { path: "bookingPolicies.services.schedule.firstSessionStart" },
            { path: "bookingPolicies.services.schedule.lastSessionEnd" },
            {
              path: "bookingPolicies.services.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "bookingPolicies.services.supportedSlugs.createdDate" },
            { path: "bookingPolicies.services.mainSlug.createdDate" },
            { path: "bookingPolicies.connectedServices.createdDate" },
            { path: "bookingPolicies.connectedServices.updatedDate" },
            {
              path: "bookingPolicies.connectedServices.media.items.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.media.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.media.coverMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.bookingPolicy.createdDate"
            },
            {
              path: "bookingPolicies.connectedServices.bookingPolicy.updatedDate"
            },
            {
              path: "bookingPolicies.connectedServices.schedule.firstSessionStart"
            },
            {
              path: "bookingPolicies.connectedServices.schedule.lastSessionEnd"
            },
            {
              path: "bookingPolicies.connectedServices.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "bookingPolicies.connectedServices.supportedSlugs.createdDate"
            },
            {
              path: "bookingPolicies.connectedServices.mainSlug.createdDate"
            }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "bookingPolicies.services.locations.business.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.services.locations.business.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.services.locations.custom.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.services.locations.custom.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.services.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "bookingPolicies.services.locations.calculatedAddress.geocode.longitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.business.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.business.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.custom.address.geocode.latitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.custom.address.geocode.longitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "bookingPolicies.connectedServices.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryPolicies;
}
function queryBookingForms(payload) {
  function __queryBookingForms({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryBookingForms",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/booking-forms/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryBookingForms;
}
function countServices(payload) {
  function __countServices({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.CountServices",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countServices;
}
function queryLocations(payload) {
  function __queryLocations({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryLocations",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/locations/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "businessLocations.locations.business.address.geocode.latitude"
            },
            {
              path: "businessLocations.locations.business.address.geocode.longitude"
            },
            {
              path: "businessLocations.locations.custom.address.geocode.latitude"
            },
            {
              path: "businessLocations.locations.custom.address.geocode.longitude"
            },
            {
              path: "businessLocations.locations.calculatedAddress.geocode.latitude"
            },
            {
              path: "businessLocations.locations.calculatedAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryLocations;
}
function queryCategories(payload) {
  function __queryCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.QueryCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/categories/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryCategories;
}
function setServiceLocations(payload) {
  function __setServiceLocations({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFloatToRESTFloat,
        paths: [
          { path: "locations.business.address.geocode.latitude" },
          { path: "locations.business.address.geocode.longitude" },
          { path: "locations.custom.address.geocode.latitude" },
          { path: "locations.custom.address.geocode.longitude" },
          { path: "locations.calculatedAddress.geocode.latitude" },
          { path: "locations.calculatedAddress.geocode.longitude" },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.latitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.longitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.latitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.longitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.latitude"
          },
          {
            path: "removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.longitude"
          }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.SetServiceLocations",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/locations",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setServiceLocations;
}
function enablePricingPlansForService(payload) {
  function __enablePricingPlansForService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.EnablePricingPlansForService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/pricing-plans/add",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __enablePricingPlansForService;
}
function disablePricingPlansForService(payload) {
  function __disablePricingPlansForService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.DisablePricingPlansForService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/pricing-plans/remove",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __disablePricingPlansForService;
}
function setCustomSlug(payload) {
  function __setCustomSlug({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.SetCustomSlug",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/{serviceId}/slugs/custom",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "slug.createdDate" },
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setCustomSlug;
}
function validateSlug(payload) {
  function __validateSlug({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.ValidateSlug",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/slugs/validate",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __validateSlug;
}
function cloneService(payload) {
  function __cloneService({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.services.v2.service",
      method: "POST",
      methodFqn: "wix.bookings.services.v2.ServicesService.CloneService",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsServicesV2ServicesServiceUrl({
        protoPath: "/v2/services/clone",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "service.createdDate" },
            { path: "service.updatedDate" },
            { path: "service.media.items.image.urlExpirationDate" },
            { path: "service.media.mainMedia.image.urlExpirationDate" },
            { path: "service.media.coverMedia.image.urlExpirationDate" },
            { path: "service.bookingPolicy.createdDate" },
            { path: "service.bookingPolicy.updatedDate" },
            { path: "service.schedule.firstSessionStart" },
            { path: "service.schedule.lastSessionEnd" },
            {
              path: "service.staffMembers.mainMedia.image.urlExpirationDate"
            },
            {
              path: "service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate"
            },
            { path: "service.supportedSlugs.createdDate" },
            { path: "service.mainSlug.createdDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "service.locations.business.address.geocode.latitude" },
            { path: "service.locations.business.address.geocode.longitude" },
            { path: "service.locations.custom.address.geocode.latitude" },
            { path: "service.locations.custom.address.geocode.longitude" },
            { path: "service.locations.calculatedAddress.geocode.latitude" },
            { path: "service.locations.calculatedAddress.geocode.longitude" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __cloneService;
}

// src/bookings-services-v2-service-services.meta.ts
function createAddOnGroup2() {
  const payload = {};
  const getRequestOptions = createAddOnGroup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/add-on-groups/create",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteAddOnGroup2() {
  const payload = {};
  const getRequestOptions = deleteAddOnGroup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/add-on-groups/delete",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateAddOnGroup2() {
  const payload = {};
  const getRequestOptions = updateAddOnGroup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/add-on-groups/update",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function listAddOnGroupsByServiceId2() {
  const payload = {};
  const getRequestOptions = listAddOnGroupsByServiceId(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/add-on-groups/list-add-on-groups-by-service-id",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function setAddOnsForGroup2() {
  const payload = {};
  const getRequestOptions = setAddOnsForGroup(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/add-on-groups/set-add-ons-for-group",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function createService2() {
  const payload = {};
  const getRequestOptions = createService(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkCreateServices2() {
  const payload = {};
  const getRequestOptions = bulkCreateServices(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/services/create",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getService2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = getService(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/services/{serviceId}",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateService2() {
  const payload = { service: { id: ":serviceId" } };
  const getRequestOptions = updateService(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v2/services/{service.id}",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkUpdateServices2() {
  const payload = {};
  const getRequestOptions = bulkUpdateServices(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/services/update",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkUpdateServicesByFilter2() {
  const payload = {};
  const getRequestOptions = bulkUpdateServicesByFilter(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/services/update-by-filter",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteService2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = deleteService(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v2/services/{serviceId}",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkDeleteServices2() {
  const payload = {};
  const getRequestOptions = bulkDeleteServices(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/services/delete",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkDeleteServicesByFilter2() {
  const payload = {};
  const getRequestOptions = bulkDeleteServicesByFilter(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/bulk/services/delete-by-filter",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryServices2() {
  const payload = {};
  const getRequestOptions = queryServices(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function searchServices2() {
  const payload = {};
  const getRequestOptions = searchServices(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/search",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryPolicies2() {
  const payload = {};
  const getRequestOptions = queryPolicies(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/policies/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryBookingForms2() {
  const payload = {};
  const getRequestOptions = queryBookingForms(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/booking-forms/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countServices2() {
  const payload = {};
  const getRequestOptions = countServices(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryLocations2() {
  const payload = {};
  const getRequestOptions = queryLocations(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/locations/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryCategories2() {
  const payload = {};
  const getRequestOptions = queryCategories(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/categories/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function setServiceLocations2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = setServiceLocations(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/{serviceId}/locations",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function enablePricingPlansForService2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = enablePricingPlansForService(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/{serviceId}/pricing-plans/add",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function disablePricingPlansForService2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = disablePricingPlansForService(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/{serviceId}/pricing-plans/remove",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function setCustomSlug2() {
  const payload = { serviceId: ":serviceId" };
  const getRequestOptions = setCustomSlug(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/{serviceId}/slugs/custom",
    pathParams: { serviceId: "serviceId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function validateSlug2() {
  const payload = {};
  const getRequestOptions = validateSlug(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/slugs/validate",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function cloneService2() {
  const payload = {};
  const getRequestOptions = cloneService(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/services/clone",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  bulkCreateServices2 as bulkCreateServices,
  bulkDeleteServices2 as bulkDeleteServices,
  bulkDeleteServicesByFilter2 as bulkDeleteServicesByFilter,
  bulkUpdateServices2 as bulkUpdateServices,
  bulkUpdateServicesByFilter2 as bulkUpdateServicesByFilter,
  cloneService2 as cloneService,
  countServices2 as countServices,
  createAddOnGroup2 as createAddOnGroup,
  createService2 as createService,
  deleteAddOnGroup2 as deleteAddOnGroup,
  deleteService2 as deleteService,
  disablePricingPlansForService2 as disablePricingPlansForService,
  enablePricingPlansForService2 as enablePricingPlansForService,
  getService2 as getService,
  listAddOnGroupsByServiceId2 as listAddOnGroupsByServiceId,
  queryBookingForms2 as queryBookingForms,
  queryCategories2 as queryCategories,
  queryLocations2 as queryLocations,
  queryPolicies2 as queryPolicies,
  queryServices2 as queryServices,
  searchServices2 as searchServices,
  setAddOnsForGroup2 as setAddOnsForGroup,
  setCustomSlug2 as setCustomSlug,
  setServiceLocations2 as setServiceLocations,
  updateAddOnGroup2 as updateAddOnGroup,
  updateService2 as updateService,
  validateSlug2 as validateSlug
};
//# sourceMappingURL=meta.mjs.map