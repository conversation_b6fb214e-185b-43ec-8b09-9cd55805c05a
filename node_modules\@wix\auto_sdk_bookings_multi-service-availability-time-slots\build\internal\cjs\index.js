"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  LocationType: () => LocationType,
  getMultiServiceAvailabilityTimeSlot: () => getMultiServiceAvailabilityTimeSlot4,
  listMultiServiceAvailabilityTimeSlots: () => listMultiServiceAvailabilityTimeSlots4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-availability-v2-time-slot-multi-service-availability-time-slots.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-availability-v2-time-slot-multi-service-availability-time-slots.http.ts
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsAvailabilityV2MultiServiceAvailabilityTimeSlotsUrl(opts) {
  const domainToMappings = {
    "www.wixapis.com": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    _: [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ]
  };
  return (0, import_rest_modules.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_multi-service-availability-time-slots";
function listMultiServiceAvailabilityTimeSlots(payload) {
  function __listMultiServiceAvailabilityTimeSlots({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [{ path: "bookingPolicyViolations.earliestBookingDate" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.availability.v2.time_slot",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.v2.MultiServiceAvailabilityTimeSlots.ListMultiServiceAvailabilityTimeSlots",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityV2MultiServiceAvailabilityTimeSlotsUrl(
        {
          protoPath: "/v2/multi-service-time-slots",
          data: serializedData,
          host
        }
      ),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "timeSlots.bookingPolicyViolations.earliestBookingDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listMultiServiceAvailabilityTimeSlots;
}
function getMultiServiceAvailabilityTimeSlot(payload) {
  function __getMultiServiceAvailabilityTimeSlot({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v2.time_slot",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.v2.MultiServiceAvailabilityTimeSlots.GetMultiServiceAvailabilityTimeSlot",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityV2MultiServiceAvailabilityTimeSlotsUrl(
        { protoPath: "/v2/multi-service-time-slots/get", data: payload, host }
      ),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "timeSlot.bookingPolicyViolations.earliestBookingDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getMultiServiceAvailabilityTimeSlot;
}

// src/bookings-availability-v2-time-slot-multi-service-availability-time-slots.universal.ts
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNKNOWN_LOCATION_TYPE"] = "UNKNOWN_LOCATION_TYPE";
  LocationType2["BUSINESS"] = "BUSINESS";
  LocationType2["CUSTOM"] = "CUSTOM";
  LocationType2["CUSTOMER"] = "CUSTOMER";
  return LocationType2;
})(LocationType || {});
async function listMultiServiceAvailabilityTimeSlots2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    services: options?.services,
    fromLocalDate: options?.fromLocalDate,
    toLocalDate: options?.toLocalDate,
    timeZone: options?.timeZone,
    location: options?.location,
    bookable: options?.bookable,
    bookingPolicyViolations: options?.bookingPolicyViolations,
    timeSlotsPerDay: options?.timeSlotsPerDay,
    cursorPaging: options?.cursorPaging
  });
  const reqOpts = listMultiServiceAvailabilityTimeSlots(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          services: "$[0].services",
          fromLocalDate: "$[0].fromLocalDate",
          toLocalDate: "$[0].toLocalDate",
          timeZone: "$[0].timeZone",
          location: "$[0].location",
          bookable: "$[0].bookable",
          bookingPolicyViolations: "$[0].bookingPolicyViolations",
          timeSlotsPerDay: "$[0].timeSlotsPerDay",
          cursorPaging: "$[0].cursorPaging"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getMultiServiceAvailabilityTimeSlot2(services, localStartDate, localEndDate, timeZone, location) {
  const { httpClient, sideEffects } = arguments[5];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    services,
    localStartDate,
    localEndDate,
    timeZone,
    location
  });
  const reqOpts = getMultiServiceAvailabilityTimeSlot(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          services: "$[0]",
          localStartDate: "$[1]",
          localEndDate: "$[2]",
          timeZone: "$[3]",
          location: "$[4]"
        },
        singleArgumentUnchanged: false
      },
      ["services", "localStartDate", "localEndDate", "timeZone", "location"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-availability-v2-time-slot-multi-service-availability-time-slots.public.ts
function listMultiServiceAvailabilityTimeSlots3(httpClient) {
  return (options) => listMultiServiceAvailabilityTimeSlots2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function getMultiServiceAvailabilityTimeSlot3(httpClient) {
  return (services, localStartDate, localEndDate, timeZone, location) => getMultiServiceAvailabilityTimeSlot2(
    services,
    localStartDate,
    localEndDate,
    timeZone,
    location,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-availability-v2-time-slot-multi-service-availability-time-slots.context.ts
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
var listMultiServiceAvailabilityTimeSlots4 = /* @__PURE__ */ (0, import_rest_modules2.createRESTModule)(listMultiServiceAvailabilityTimeSlots3);
var getMultiServiceAvailabilityTimeSlot4 = /* @__PURE__ */ (0, import_rest_modules2.createRESTModule)(getMultiServiceAvailabilityTimeSlot3);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  LocationType,
  getMultiServiceAvailabilityTimeSlot,
  listMultiServiceAvailabilityTimeSlots
});
//# sourceMappingURL=index.js.map