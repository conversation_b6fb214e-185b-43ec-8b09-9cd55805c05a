import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { CreateBackupResponse, ListBackupsOptions, ListBackupsResponse, RestoreBackupResponse, RestorationCollection, RestorePartialBackupResponse, ListRestorationsOptions, ListRestorationsResponse, BackupStateChangedEnvelope, BackupRestorationStateChangedEnvelope } from './index.typings.js';
export { ActionEvent, Backup, BackupStateChanged, BaseEventMetadata, CancelBackupRequest, CancelBackupResponse, Collection, CreateBackupRequest, DeleteAllRequest, DeleteBackupRequest, DeleteBackupResponse, DisableInstanceRequest, DomainEvent, DomainEventBodyOneOf, Empty, EnableInstanceRequest, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, EventMetadata, FailRestorationRequest, GetBackupMetadataRequest, GetBackupMetadataResponse, IdentificationData, IdentificationDataIdOneOf, ListBackupsRequest, ListRestorationsRequest, MessageEnvelope, MigrateNamespaceRequest, MigrateNamespaceResponse, MoveRequest, Paging, PagingMetadataV2, RebuildRequest, RemoveDeletedRequest, Restoration, RestorationStateChanged, RestorationStatus, RestorationStatusWithLiterals, RestoreBackupRequest, RestoreDestination, RestoreInfo, RestorePartialBackupRequest, Status, StatusWithLiterals, TakeBackupRequest, Type, TypeWithLiterals, UpdateBackupMetadataRequest, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.js';

declare function createBackup$1(httpClient: HttpClient): CreateBackupSignature;
interface CreateBackupSignature {
    /**
     * Creates an on-demand backup of live content in a site's collections.
     *
     *
     * By default, all of the site's collections are included in the backup. For a partial backup, specify which collections to include in the `backup.collections` parameter.
     *
     * The process of creating a backup takes time.
     * You can check whether a backup has completed successfully with List Backups.
     *
     * You can store up to 3 on-demand backups for each site.
     * If 3 on-demand backups already exist, the oldest existing on-demand backup for the site is deleted when a new one is created. Automated backups are not affected.
     */
    (): Promise<NonNullablePaths<CreateBackupResponse, `backup._id` | `backup.status` | `backup.type` | `backup.collections` | `backup.collections.${number}._id`, 5>>;
}
declare function listBackups$1(httpClient: HttpClient): ListBackupsSignature;
interface ListBackupsSignature {
    /**
     * Retrieves a list of all backups for a site.
     *
     * Results are sorted by requested date, with the newest first.
     *
     * You can use this method to check whether a backup initiated with Create Backup has been completed successfully.
     */
    (options?: ListBackupsOptions): Promise<NonNullablePaths<ListBackupsResponse, `backups` | `backups.${number}._id` | `backups.${number}.status` | `backups.${number}.type`, 4>>;
}
declare function restoreBackup$1(httpClient: HttpClient): RestoreBackupSignature;
interface RestoreBackupSignature {
    /**
     * Restores all data from a backup.
     *
     * The process of restoring data from a backup takes time.
     * You can check whether your restoration has completed successfully with List Restorations.
     * @param - ID of backup to be restored.
     */
    (backupId: string): Promise<NonNullablePaths<RestoreBackupResponse, `restoration._id` | `restoration.backup._id` | `restoration.backup.status` | `restoration.backup.type` | `restoration.backup.collections` | `restoration.backup.collections.${number}._id` | `restoration.status` | `restoration.restorationCollections` | `restoration.restorationCollections.${number}.dataCollectionId` | `restoration.restorationCollections.${number}.restoreDestination.dataCollectionId`, 6>>;
}
declare function restorePartialBackup$1(httpClient: HttpClient): RestorePartialBackupSignature;
interface RestorePartialBackupSignature {
    /**
     * Restores specific collections from a backup.
     *
     * The process of restoring data from a backup takes time.
     * You can check whether your restoration has completed successfully with List Restorations.
     * @param - ID of backup to be restored.
     * @param - **Required.** Collections to be restored.
     *
     * Note: If collections have a multi-reference relationship,
     * the preexisting references will be restored if at least one of those collections are restored.
     */
    (backupId: string, restorationCollections: RestorationCollection[]): Promise<NonNullablePaths<RestorePartialBackupResponse, `restoration._id` | `restoration.backup._id` | `restoration.backup.status` | `restoration.backup.type` | `restoration.backup.collections` | `restoration.backup.collections.${number}._id` | `restoration.status` | `restoration.restorationCollections` | `restoration.restorationCollections.${number}.dataCollectionId` | `restoration.restorationCollections.${number}.restoreDestination.dataCollectionId`, 6>>;
}
declare function listRestorations$1(httpClient: HttpClient): ListRestorationsSignature;
interface ListRestorationsSignature {
    /**
     * Retrieves a list of all data restorations from backups.
     *
     * Results are sorted by requested date, with the newest first.
     *
     * You can use this method to check whether a restoration initiated with Restore Backup has been completed successfully.
     */
    (options?: ListRestorationsOptions): Promise<NonNullablePaths<ListRestorationsResponse, `restorations` | `restorations.${number}._id` | `restorations.${number}.backup._id` | `restorations.${number}.backup.status` | `restorations.${number}.backup.type` | `restorations.${number}.status`, 5>>;
}
declare function deleteBackup$1(httpClient: HttpClient): DeleteBackupSignature;
interface DeleteBackupSignature {
    /**
     * Deletes a backup.
     *
     * The process of deleting a backup takes time.
     * You can check whether a backup has been deleted successfully with List Backups.
     * @param - ID of the backup to be deleted.
     */
    (backupId: string): Promise<void>;
}
declare const onBackupStateChanged$1: EventDefinition<BackupStateChangedEnvelope, "wix.data.v2.backup_backup_state_changed">;
declare const onBackupRestorationStateChanged$1: EventDefinition<BackupRestorationStateChangedEnvelope, "wix.data.v2.backup_restoration_state_changed">;

declare const createBackup: MaybeContext<BuildRESTFunction<typeof createBackup$1> & typeof createBackup$1>;
declare const listBackups: MaybeContext<BuildRESTFunction<typeof listBackups$1> & typeof listBackups$1>;
declare const restoreBackup: MaybeContext<BuildRESTFunction<typeof restoreBackup$1> & typeof restoreBackup$1>;
declare const restorePartialBackup: MaybeContext<BuildRESTFunction<typeof restorePartialBackup$1> & typeof restorePartialBackup$1>;
declare const listRestorations: MaybeContext<BuildRESTFunction<typeof listRestorations$1> & typeof listRestorations$1>;
declare const deleteBackup: MaybeContext<BuildRESTFunction<typeof deleteBackup$1> & typeof deleteBackup$1>;
/**
 * Triggered when a backup's status is changed. This includes when a backup is created and its status is set to `PENDING`.
 */
declare const onBackupStateChanged: BuildEventDefinition<typeof onBackupStateChanged$1>;
/**
 * Triggered when a restoration's status is changed. This includes when a new restoration is initiated and its status is `PENDING`.
 */
declare const onBackupRestorationStateChanged: BuildEventDefinition<typeof onBackupRestorationStateChanged$1>;

export { BackupRestorationStateChangedEnvelope, BackupStateChangedEnvelope, CreateBackupResponse, ListBackupsOptions, ListBackupsResponse, ListRestorationsOptions, ListRestorationsResponse, RestorationCollection, RestoreBackupResponse, RestorePartialBackupResponse, createBackup, deleteBackup, listBackups, listRestorations, onBackupRestorationStateChanged, onBackupStateChanged, restoreBackup, restorePartialBackup };
