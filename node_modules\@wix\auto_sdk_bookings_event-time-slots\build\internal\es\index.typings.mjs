// src/bookings-availability-v2-time-slot-event-time-slots.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-availability-v2-time-slot-event-time-slots.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsAvailabilityV2EventTimeSlotsUrl(opts) {
  const domainToMappings = {
    "www.wixapis.com": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    _: [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_event-time-slots";
function listEventTimeSlots(payload) {
  function __listEventTimeSlots({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [{ path: "bookingPolicyViolations.earliestBookingDate" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.availability.v2.time_slot",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.v2.EventTimeSlots.ListEventTimeSlots",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityV2EventTimeSlotsUrl({
        protoPath: "/v2/time-slots/event",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "timeSlots.bookingPolicyViolations.earliestBookingDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listEventTimeSlots;
}
function getEventTimeSlot(payload) {
  function __getEventTimeSlot({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v2.time_slot",
      method: "GET",
      methodFqn: "com.wixpress.bookings.availability.v2.EventTimeSlots.GetEventTimeSlot",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityV2EventTimeSlotsUrl({
        protoPath: "/v2/time-slots/event/{eventId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "timeSlot.bookingPolicyViolations.earliestBookingDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getEventTimeSlot;
}

// src/bookings-availability-v2-time-slot-event-time-slots.universal.ts
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNKNOWN_LOCATION_TYPE"] = "UNKNOWN_LOCATION_TYPE";
  LocationType2["BUSINESS"] = "BUSINESS";
  LocationType2["CUSTOM"] = "CUSTOM";
  LocationType2["CUSTOMER"] = "CUSTOMER";
  return LocationType2;
})(LocationType || {});
async function listEventTimeSlots2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    fromLocalDate: options?.fromLocalDate,
    toLocalDate: options?.toLocalDate,
    timeZone: options?.timeZone,
    serviceIds: options?.serviceIds,
    includeNonBookable: options?.includeNonBookable,
    minBookableCapacity: options?.minBookableCapacity,
    eventFilter: options?.eventFilter,
    maxSlotsPerDay: options?.maxSlotsPerDay,
    cursorPaging: options?.cursorPaging,
    bookingPolicyViolations: options?.bookingPolicyViolations
  });
  const reqOpts = listEventTimeSlots(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          fromLocalDate: "$[0].fromLocalDate",
          toLocalDate: "$[0].toLocalDate",
          timeZone: "$[0].timeZone",
          serviceIds: "$[0].serviceIds",
          includeNonBookable: "$[0].includeNonBookable",
          minBookableCapacity: "$[0].minBookableCapacity",
          eventFilter: "$[0].eventFilter",
          maxSlotsPerDay: "$[0].maxSlotsPerDay",
          cursorPaging: "$[0].cursorPaging",
          bookingPolicyViolations: "$[0].bookingPolicyViolations"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getEventTimeSlot2(eventId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    eventId,
    timeZone: options?.timeZone
  });
  const reqOpts = getEventTimeSlot(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          eventId: "$[0]",
          timeZone: "$[1].timeZone"
        },
        singleArgumentUnchanged: false
      },
      ["eventId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
export {
  LocationType,
  getEventTimeSlot2 as getEventTimeSlot,
  listEventTimeSlots2 as listEventTimeSlots
};
//# sourceMappingURL=index.typings.mjs.map