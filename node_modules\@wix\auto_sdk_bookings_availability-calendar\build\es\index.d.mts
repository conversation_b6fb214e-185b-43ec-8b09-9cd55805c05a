import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { QueryV2, QueryAvailabilityOptions, QueryAvailabilityResponse, GetScheduleAvailabilityResponse, GetScheduleAvailabilityApplicationErrors, CalculateMultiSlotAvailabilityOptions, CalculateMultiSlotAvailabilityResponse } from './index.typings.mjs';
export { AvailabilityV2CustomerChoices, AvailabilityWaitingList, AvailableResources, BookingPolicySettings, BookingPolicyViolations, CalculateMultiSlotAvailabilityRequest, CursorPaging, CursorPagingMetadata, Cursors, CustomerChoices, EventInfo, GetAvailabilityTimeSlotRequest, GetAvailabilityTimeSlotResponse, GetScheduleAvailabilityRequest, GetSlotAvailabilityRequest, GetSlotAvailabilityResponse, Location, LocationLocationType, LocationLocationTypeWithLiterals, LocationType, LocationTypeWithLiterals, NestedTimeSlot, PagingMetadataV2, QueryAvailabilityRequest, QueryV2PagingMethodOneOf, Resource, ResourcesFilter, RuleBasedConstraints, ScheduleAvailability, Service_availabilityBookingPolicyViolations, Service_availabilityNestedTimeSlot, Slot, SlotAvailability, SlotResource, SortOrder, SortOrderWithLiterals, Sorting, TimeSlot, TimeSlotLocation, V2CustomerChoices, WaitingList } from './index.typings.mjs';

declare function queryAvailability$1(httpClient: HttpClient): QueryAvailabilitySignature;
interface QueryAvailabilitySignature {
    /**
     * Retrieves appointment slots or class events that match specified filters.
     *
     * <blockquote>
     *
     * Query Availability doesn't use a query builder, instead it uses
     * [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language).
     *
     * </blockquote>
     *
     * ## Course limitations
     *
     * Course events can't be retrieved with this method. Follow this
     * *sample flow* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/sample-flows#backend-modules_bookings_bookings_book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course))
     * to check course availability.
     *
     * ## Query filter
     *
     * You must include `serviceId` in your query filter. For performance, also specify
     * both `startDate` and `endDate`. Refer to the
     * *supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))
     * for a complete list of supported filters.
     *
     * ## Limit the number of slots returned per day
     *
     * To limit the maximum number of appointment slots returned for each date,
     * specify `slotsPerDay`. For example,  specifying `{"slotsPerDay": 3}` returns
     * a maximum of 3 appointment slots/class events for each day that's within the query
     * filter's date range.
     *
     * ## Unavailable slots
     *
     * By default, both available and unavailable appointment slots/ class events
     * are returned. To retrieve only available slots/events, specify
     * `{"bookable": true}` in the filter.
     *
     * ## Sorting
     *
     * Slots/events can be sorted only by `startDate`. Unavailable slots/events
     * always appear after available ones.
     *
     * ## Availability calculation
     *
     * Wix Bookings calculates availability differently for appointment-based
     * services and classes.
     *
     * ### Appointment availability
     *
     * An appointment slot is `bookable` if:
     * - The slot's `startDate` is later than or equal to `query.filter.startDate`.
     * - The slot's `endDate` is before or equal to `query.filter.endDate`.
     * - The service's `onlineBooking.enabled` is `true`.
     * - The service's early/late booking policies are either not enabled or the
     *   current time is within the defined limits.
     *
     * ### Class event availability
     *
     * A class event is `bookable` if:
     * - The event's `startDate` is later than or equal to `query.filter.startDate`.
     * - The event's `endDate` is before or equal to `query.filter.endDate`.
     * - The event's `remainingCapacity` is greater than `0`.
     * - The service's `onlineBooking.enabled` is `true`.
     * - The service's early/late booking policies are either not enabled or the
     *   current time is within the defined limits.
     *
     * If you want to restrict returned class events to those with a minimum number
     * of remaining spots, specify `openSpots` in the filter. By default, all class
     * events, even those with no open spot, are returned.
     *
     * ## Daylight Savings Time (DST)
     *
     * Due to DST, some local times may not exist or may occur twice. For example,
     * in Santiago, Chile, on September 7, 2025, 0:05 AM doesn't exist because the
     * clock advances from 0:00 AM to 1:00 AM.
     *
     * *Query Availability* automatically adjusts for these gaps. Non-existent times
     * are moved forward by 1 hour, while existing times remain unchanged. For
     * instance, if you call *Query Availability* with a `startDate` of
     * `2025-09-07T00:00:01.000` and an `endDate` of `2025-09-08T00:00:02.000`, the
     * adjusted query will use `2025-09-07T01:00:01.000` as the new start time.
     * @param - Query options. Refer to the
     * _supported filters article_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))
     * for a complete list of supported filters.
     * @param - Options to use for querying availability.
     */
    (query: NonNullablePaths<QueryV2, `filter`, 2>, options?: QueryAvailabilityOptions): Promise<NonNullablePaths<QueryAvailabilityResponse, `availabilityEntries` | `availabilityEntries.${number}.slot.serviceId` | `availabilityEntries.${number}.slot.scheduleId` | `availabilityEntries.${number}.slot.location.locationType` | `availabilityEntries.${number}.bookable` | `availabilityEntries.${number}.isFromV2`, 6>>;
}
declare function getScheduleAvailability$1(httpClient: HttpClient): GetScheduleAvailabilitySignature;
interface GetScheduleAvailabilitySignature {
    /**
     * Returns availability for a given schedule ID
     * The availability for a course is calculated by:
     * - Checking for total spots by the schedule's capacity
     * - Checking for open spots by subtracting the current number of participants from the total spots
     * current number of participants is calculated by summing the number of participants of all bookings booked to the schedule
     * @param - The schedule ID for which availability is being checked.
     * @deprecated
     */
    (scheduleId: string): Promise<GetScheduleAvailabilityResponse & {
        __applicationErrorsType?: GetScheduleAvailabilityApplicationErrors;
    }>;
}
declare function calculateMultiSlotAvailability$1(httpClient: HttpClient): CalculateMultiSlotAvailabilitySignature;
interface CalculateMultiSlotAvailabilitySignature {
    /** */
    (options?: CalculateMultiSlotAvailabilityOptions): Promise<NonNullablePaths<CalculateMultiSlotAvailabilityResponse, `slots` | `slots.${number}.slot.serviceId` | `slots.${number}.slot.scheduleId` | `slots.${number}.slot.location.locationType` | `slots.${number}.bookable` | `slots.${number}.isFromV2`, 6>>;
}

declare const queryAvailability: MaybeContext<BuildRESTFunction<typeof queryAvailability$1> & typeof queryAvailability$1>;
declare const getScheduleAvailability: MaybeContext<BuildRESTFunction<typeof getScheduleAvailability$1> & typeof getScheduleAvailability$1>;
declare const calculateMultiSlotAvailability: MaybeContext<BuildRESTFunction<typeof calculateMultiSlotAvailability$1> & typeof calculateMultiSlotAvailability$1>;

export { CalculateMultiSlotAvailabilityOptions, CalculateMultiSlotAvailabilityResponse, GetScheduleAvailabilityApplicationErrors, GetScheduleAvailabilityResponse, QueryAvailabilityOptions, QueryAvailabilityResponse, QueryV2, calculateMultiSlotAvailability, getScheduleAvailability, queryAvailability };
