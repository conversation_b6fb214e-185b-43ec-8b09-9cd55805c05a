import { cm as CursorSearch, x as SearchServicesResponse } from './bookings-services-v2-service-services.universal-C8m36LwQ.js';
export { al as Action, bB as ActionEvent, ed as ActionWithLiterals, bK as AddOn, bL as AddOnAddOnInfoOneOf, bn as AddOnDetails, A as AddOnGroup, bM as AddOnGroupDetail, dH as Address, dI as AddressHint, aP as AddressLocation, co as Aggregation, cE as AggregationData, cp as AggregationKindOneOf, cX as AggregationResults, cY as AggregationResultsResultOneOf, cL as AggregationResultsScalarResult, ai as AggregationType, ea as AggregationTypeWithLiterals, bW as ApplicationError, b5 as AvailabilityConstraints, dW as BaseEventMetadata, dx as Behavior, dy as BehaviorBehaviorOneOf, dt as Benefit, ds as BenefitNotification, aq as BenefitType, ei as BenefitTypeWithLiterals, aW as BookAfterStartPolicy, d1 as BookingForm, aS as BookingPolicy, c$ as BookingPolicyWithServices, bX as BulkActionMetadata, B as BulkCreateServicesOptions, bT as BulkCreateServicesRequest, j as BulkCreateServicesResponse, t as BulkDeleteServicesByFilterOptions, cb as BulkDeleteServicesByFilterRequest, u as BulkDeleteServicesByFilterResponse, r as BulkDeleteServicesOptions, ca as BulkDeleteServicesRequest, s as BulkDeleteServicesResponse, bU as BulkServiceResult, o as BulkUpdateServicesByFilterOptions, c6 as BulkUpdateServicesByFilterRequest, p as BulkUpdateServicesByFilterResponse, m as BulkUpdateServicesOptions, c4 as BulkUpdateServicesRequest, n as BulkUpdateServicesResponse, aQ as BusinessLocationOptions, d7 as BusinessLocations, dK as BusinessSchedule, b0 as CancellationFeePolicy, aX as CancellationPolicy, b1 as CancellationWindow, b2 as CancellationWindowFeeOneOf, dF as Categories, dq as Category, dp as CategoryNotification, ap as CategoryNotificationEvent, eh as CategoryNotificationEventWithLiterals, dR as ChangeContext, dS as ChangeContextPayloadOneOf, an as CloneErrors, ef as CloneErrorsWithLiterals, dn as CloneServiceRequest, a2 as CloneServiceResponse, aM as CommonAddress, aN as CommonAddressStreetOneOf, eo as CommonSearchWithEntityContext, aK as Conferencing, d3 as ConnectedService, dP as ConsentPolicy, G as CountServicesOptions, d4 as CountServicesRequest, H as CountServicesResponse, C as CreateAddOnGroupOptions, bF as CreateAddOnGroupRequest, a as CreateAddOnGroupResponse, bO as CreateServiceRequest, bP as CreateServiceResponse, i as CreateServiceValidationErrors, as as CrudType, ek as CrudTypeWithLiterals, ch as CursorPaging, cD as CursorPagingMetadata, y as CursorQuery, c_ as CursorQueryPagingMethodOneOf, cn as CursorSearchPagingMethodOneOf, ck as Cursors, aR as CustomLocationOptions, d8 as CustomLocations, aG as CustomPayment, d9 as CustomerLocations, cw as DateHistogramAggregation, cT as DateHistogramResult, cV as DateHistogramResults, au as DayOfWeek, em as DayOfWeekWithLiterals, br as Delete, b as DeleteAddOnGroupApplicationErrors, D as DeleteAddOnGroupOptions, bG as DeleteAddOnGroupRequest, bH as DeleteAddOnGroupResponse, q as DeleteServiceOptions, c7 as DeleteServiceRequest, c9 as DeleteServiceResponse, X as DisablePricingPlansForServiceApplicationErrors, V as DisablePricingPlansForServiceOptions, dk as DisablePricingPlansForServiceRequest, W as DisablePricingPlansForServiceResponse, dv as Discount, dw as DiscountDiscountOneOf, bv as DomainEvent, bw as DomainEventBodyOneOf, b6 as Duration, dr as Empty, T as EnablePricingPlansForServiceApplicationErrors, di as EnablePricingPlansForServiceRequest, R as EnablePricingPlansForServiceResponse, bx as EntityCreatedEvent, bA as EntityDeletedEvent, bz as EntityUpdatedEvent, du as EntryPass, ar as Event, dX as EventMetadata, ej as EventWithLiterals, bi as ExtendedFields, bS as FieldViolation, aE as FixedPayment, aA as Form, d2 as FormDetails, aB as FormSettings, dJ as GeoCoordinates, b_ as GetServiceAvailabilityConstraintsRequest, b$ as GetServiceAvailabilityConstraintsResponse, bY as GetServiceRequest, bZ as GetServiceResponse, cA as GroupByAggregation, cB as GroupByAggregationKindOneOf, cU as GroupByValueResults, dA as HtmlSitePublished, bD as IdentificationData, bE as IdentificationDataIdOneOf, cr as IncludeMissingValuesOptions, ah as Interval, e9 as IntervalWithLiterals, dj as InvalidPricingPlan, am as InvalidSlugError, ee as InvalidSlugErrorWithLiterals, bV as ItemMetadata, bk as Keyword, aU as LimitEarlyBookingPolicy, aV as LimitLateBookingPolicy, L as ListAddOnGroupsByServiceIdOptions, bJ as ListAddOnGroupsByServiceIdRequest, e as ListAddOnGroupsByServiceIdResponse, dG as Locale, N as Location, aL as LocationOptionsOneOf, a8 as LocationType, e0 as LocationTypeWithLiterals, c5 as MaskedService, aw as Media, ax as MediaItem, ay as MediaItemItemOneOf, bC as MessageEnvelope, ae as MissingValues, e6 as MissingValuesWithLiterals, aj as Mode, eb as ModeWithLiterals, aF as Money, dh as MoveToNewLocationsOptions, dN as Multilingual, cz as NestedAggregation, cx as NestedAggregationItem, cy as NestedAggregationItemKindOneOf, cH as NestedAggregationResults, cI as NestedAggregationResultsResultOneOf, ag as NestedAggregationType, e8 as NestedAggregationTypeWithLiterals, cQ as NestedResultValue, cR as NestedResultValueResultOneOf, cW as NestedResults, cM as NestedValueAggregationResult, aJ as OnlineBooking, dB as Page, cg as Paging, cj as PagingMetadataV2, c8 as ParticipantNotification, a_ as ParticipantsPolicy, aC as Payment, aI as PaymentOptions, aD as PaymentRateOneOf, at as PlacementType, el as PlacementTypeWithLiterals, aT as PolicyDescription, dE as Properties, dT as PropertiesChange, F as QueryBookingFormsApplicationErrors, z as QueryBookingFormsOptions, d0 as QueryBookingFormsRequest, E as QueryBookingFormsResponse, db as QueryCategoriesFilter, K as QueryCategoriesOptions, da as QueryCategoriesRequest, M as QueryCategoriesResponse, d6 as QueryLocationsFilter, I as QueryLocationsOptions, d5 as QueryLocationsRequest, J as QueryLocationsResponse, cZ as QueryPoliciesRequest, Q as QueryPoliciesResponse, dc as QueryServicesMultiLanguageRequest, dd as QueryServicesMultiLanguageResponse, cc as QueryServicesRequest, ci as QueryServicesResponse, cd as QueryV2, ce as QueryV2PagingMethodOneOf, cu as RangeAggregation, cG as RangeAggregationResult, cq as RangeBucket, cO as RangeResult, cK as RangeResults, a7 as RateType, d$ as RateTypeWithLiterals, bo as ReindexMessage, bp as ReindexMessageActionOneOf, df as RemovedLocationSessionsAction, dg as RemovedLocationSessionsActionActionOptionsOneOf, ak as RequestedFields, ec as RequestedFieldsWithLiterals, aY as ReschedulePolicy, av as ResolutionMethod, en as ResolutionMethodWithLiterals, bb as ResourceGroup, bc as ResourceIds, bf as ResourceType, a$ as ResourcesPolicy, by as RestoreInfo, cS as Results, b3 as SaveCreditCardPolicy, cv as ScalarAggregation, cP as ScalarResult, af as ScalarType, e7 as ScalarTypeWithLiterals, b4 as Schedule, bs as Schema, cC as SearchDetails, cl as SearchServicesRequest, bj as SeoSchema, h as Service, c0 as ServiceAvailabilityConstraints, a3 as ServiceCreatedEnvelope, a4 as ServiceDeletedEnvelope, bd as ServiceResource, be as ServiceResourceSelectionOneOf, dZ as ServiceSearchSpec, a6 as ServiceType, d_ as ServiceTypeWithLiterals, a5 as ServiceUpdatedEnvelope, v as ServicesQueryBuilder, dY as ServicesQueryResult, bu as ServicesUrlsChanged, g as SetAddOnsForGroupApplicationErrors, S as SetAddOnsForGroupOptions, bN as SetAddOnsForGroupRequest, f as SetAddOnsForGroupResponse, _ as SetCustomSlugApplicationErrors, bt as SetCustomSlugEvent, Y as SetCustomSlugOptions, dl as SetCustomSlugRequest, Z as SetCustomSlugResponse, $ as SetCustomSlugValidationErrors, O as SetServiceLocationsOptions, de as SetServiceLocationsRequest, P as SetServiceLocationsResponse, bm as Settings, dV as SiteCloned, dU as SiteCreated, dD as SitePropertiesEvent, dC as SitePropertiesNotification, bg as Slug, ad as SortDirection, e5 as SortDirectionWithLiterals, ab as SortOrder, e3 as SortOrderWithLiterals, ac as SortType, e4 as SortTypeWithLiterals, cf as Sorting, dM as SpecialHourPeriod, c1 as SplitInterval, b8 as StaffMediaItem, b9 as StaffMediaItemItemOneOf, b7 as StaffMember, ba as StaffMemberDetails, ao as Status, eg as StatusWithLiterals, aO as StreetAddress, dO as SupportedLanguage, bl as Tag, dL as TimePeriod, dQ as Translation, bh as URLs, d as UpdateAddOnGroupApplicationErrors, U as UpdateAddOnGroupOptions, bI as UpdateAddOnGroupRequest, c as UpdateAddOnGroupResponse, k as UpdateService, c2 as UpdateServiceRequest, c3 as UpdateServiceResponse, l as UpdateServiceValidationErrors, bq as Upsert, dz as UserDomainInfoChangedEvent, az as V2Category, aa as V2RequestedFields, e2 as V2RequestedFieldsWithLiterals, bQ as ValidateServiceRequest, bR as ValidateServiceResponse, a0 as ValidateSlugOptions, dm as ValidateSlugRequest, a1 as ValidateSlugResponse, cs as ValueAggregation, ct as ValueAggregationOptionsOneOf, cF as ValueAggregationResult, cN as ValueResult, cJ as ValueResults, aH as VariedPayment, aZ as WaitlistPolicy, a9 as WebhookIdentityType, e1 as WebhookIdentityTypeWithLiterals, ey as bulkCreateServices, eE as bulkDeleteServices, eF as bulkDeleteServicesByFilter, eB as bulkUpdateServices, eC as bulkUpdateServicesByFilter, eR as cloneService, eJ as countServices, es as createAddOnGroup, ex as createService, et as deleteAddOnGroup, eD as deleteService, eO as disablePricingPlansForService, eN as enablePricingPlansForService, ez as getService, ev as listAddOnGroupsByServiceId, ep as onServiceCreated, eq as onServiceDeleted, er as onServiceUpdated, eI as queryBookingForms, eL as queryCategories, eK as queryLocations, eH as queryPolicies, eG as queryServices, ew as setAddOnsForGroup, eP as setCustomSlug, eM as setServiceLocations, eu as updateAddOnGroup, eA as updateService, eQ as validateSlug } from './bookings-services-v2-service-services.universal-C8m36LwQ.js';
import '@wix/sdk-types';

/** @hidden */
type ServiceSearch = {};
/**
 * Retrieves a list of up to 100 services, given the provided filtering, paging,
 * and sorting.
 *
 *
 * ## Defaults
 *
 * Search Services has the following default settings, which you can override:
 * + Sorted by `createdDate` in ascending order.
 * + `paging.limit` set to `100`.
 * + `paging.offset` set to `0`.
 *
 * ## Filters
 *
 * When using filters for dates, you must use
 * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).
 *
 * Refer to the supported filters article
 * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting))
 * for a complete list of supported filters and sorting options.
 *
 * ## See also
 *
 * To learn about working with *Search* methods, see
 * _API Query Language_
 * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))
 * and *Sorting and Paging*
 * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).
 * @param search - Search criteria including filter, sort, aggregations, and paging options.
 *
 * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)) for complete filter and sorting options.
 * @public
 * @documentationMaturity preview
 * @requiredField search
 * @permissionId BOOKINGS.SERVICES_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.services.v2.ServicesService.SearchServices
 */
declare function searchServices(search: CursorSearch): Promise<SearchServicesResponse>;

export { CursorSearch, SearchServicesResponse, type ServiceSearch, searchServices };
