{"version": 3, "sources": ["../../../src/bookings-availability-v2-time-slot-multi-service-availability-time-slots.http.ts", "../../../src/bookings-availability-v2-time-slot-multi-service-availability-time-slots.meta.ts"], "sourcesContent": ["import { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAvailabilityV2MultiServiceAvailabilityTimeSlotsUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/service-availability/v2/multi-service-time-slots',\n        destPath: '/v2/multi-service-time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots',\n        destPath: '/v2/time-slots',\n      },\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/service-availability/v2/time-slots/event',\n        destPath: '/v2/time-slots/event',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME =\n  '@wix/auto_sdk_bookings_multi-service-availability-time-slots';\n\n/**\n * Retrieves a list of multi-service time slots that match the provided filters.\n *\n *\n * ## Required filters\n *\n * You must specify one of:\n * - `services.serviceId`, `fromLocalDate`, `toLocalDate`, `location`, and `timeZone` (additional filters are optional).\n * - `cursorPaging.cursor` returned from a previous response.\n *\n * Each returned `timeSlot` acts as a container spanning the entire service sequence, with nested time slots providing individual service details.\n *\n * ## Defaults\n *\n * - Results are sorted by `localStartDate` in ascending order.\n * - `cursorPaging.limit` is `1000`.\n * - The response contains both bookable and non-bookable slots.\n *\n * ## Service type limitations\n *\n * Only appointment-type services are supported.\n *\n * To retrieve appointment availability for a single service, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).\n */\nexport function listMultiServiceAvailabilityTimeSlots(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listMultiServiceAvailabilityTimeSlots({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [{ path: 'bookingPolicyViolations.earliestBookingDate' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.MultiServiceAvailabilityTimeSlots.ListMultiServiceAvailabilityTimeSlots',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2MultiServiceAvailabilityTimeSlotsUrl(\n        {\n          protoPath: '/v2/multi-service-time-slots',\n          data: serializedData,\n          host,\n        }\n      ),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlots.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __listMultiServiceAvailabilityTimeSlots;\n}\n\n/**\n * Retrieves a multi-service time slot that matches the specified filters.\n *\n *\n * Call this method after finding a suitable slot with List Multi-Service Availability Time Slots to obtain full capacity, resource, and booking-policy details.\n *\n * The returned time slot acts as a container spanning the entire service sequence, with nested time slots providing detailed information for each individual service.\n *\n * ## Defaults\n *\n * - Returns all available resources unless you filter by `services.resourceIds` or `services.includeResourceTypeIds`.\n * - Includes full booking-status and capacity details.\n *\n * ## Service type limitations\n *\n * Only appointment-type services are supported.\n *\n * To retrieve appointment availability for a single service, call List Availability Time Slots ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/time-slots/availability-time-slots/list-availability-time-slots) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/time-slots-v2/list-availability-time-slots)).\n */\nexport function getMultiServiceAvailabilityTimeSlot(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getMultiServiceAvailabilityTimeSlot({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v2.time_slot',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.v2.MultiServiceAvailabilityTimeSlots.GetMultiServiceAvailabilityTimeSlot',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityV2MultiServiceAvailabilityTimeSlotsUrl(\n        { protoPath: '/v2/multi-service-time-slots/get', data: payload, host }\n      ),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'timeSlot.bookingPolicyViolations.earliestBookingDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getMultiServiceAvailabilityTimeSlot;\n}\n", "import * as ambassadorWixBookingsAvailabilityV2TimeSlot from './bookings-availability-v2-time-slot-multi-service-availability-time-slots.http.js';\nimport * as ambassadorWixBookingsAvailabilityV2TimeSlotTypes from './bookings-availability-v2-time-slot-multi-service-availability-time-slots.types.js';\nimport * as ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes from './bookings-availability-v2-time-slot-multi-service-availability-time-slots.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function listMultiServiceAvailabilityTimeSlots(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.ListMultiServiceAvailabilityTimeSlotsRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.ListMultiServiceAvailabilityTimeSlotsRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.ListMultiServiceAvailabilityTimeSlotsResponse,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.ListMultiServiceAvailabilityTimeSlotsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.listMultiServiceAvailabilityTimeSlots(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/multi-service-time-slots',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getMultiServiceAvailabilityTimeSlot(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.GetMultiServiceAvailabilityTimeSlotRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.GetMultiServiceAvailabilityTimeSlotRequest,\n  ambassadorWixBookingsAvailabilityV2TimeSlotUniversalTypes.GetMultiServiceAvailabilityTimeSlotResponse,\n  ambassadorWixBookingsAvailabilityV2TimeSlotTypes.GetMultiServiceAvailabilityTimeSlotResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV2TimeSlot.getMultiServiceAvailabilityTimeSlot(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/multi-service-time-slots/get',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,6EACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eACJ;AA0BK,SAAS,sCACd,SAC4B;AAC5B,WAAS,wCAAwC,EAAE,KAAK,GAAQ;AAC9D,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,8CAA8C,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAqBO,SAAS,oCACd,SAC4B;AAC5B,WAAS,sCAAsC,EAAE,KAAK,GAAQ;AAC5D,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK;AAAA,QACH,EAAE,WAAW,oCAAoC,MAAM,SAAS,KAAK;AAAA,MACvE;AAAA,MACA,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC5OO,SAASC,yCAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACwC;AAAA,IAC1C;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,uCAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACwC;AAAA,IAC1C;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "listMultiServiceAvailabilityTimeSlots", "getMultiServiceAvailabilityTimeSlot"]}