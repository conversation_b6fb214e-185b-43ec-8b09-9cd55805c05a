{"version": 3, "sources": ["../../../src/bookings-availability-v1-slot-availability-availability-calendar.http.ts", "../../../src/bookings-availability-v1-slot-availability-availability-calendar.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    _: [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v2/availability/schedule',\n        destPath: '/v2/availability/schedule',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n      {\n        srcPath: '/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n      {\n        srcPath: '/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n      {\n        srcPath: '/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'editor-flow.wixapps.net': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/bookings/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/bookings/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v2/availability/schedule',\n        destPath: '/v2/availability/schedule',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'bookings.wixapps.net': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    '*.wixforms.com': [\n      {\n        srcPath: '/_api/availability-calendar',\n        destPath: '',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/availability-calendar',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_availability-calendar';\n\n/**\n * Retrieves appointment slots or class events that match specified filters.\n *\n * <blockquote>\n *\n * Query Availability doesn't use a query builder, instead it uses\n * [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language).\n *\n * </blockquote>\n *\n * ## Course limitations\n *\n * Course events can't be retrieved with this method. Follow this\n * *sample flow* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/sample-flows#backend-modules_bookings_bookings_book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course))\n * to check course availability.\n *\n * ## Query filter\n *\n * You must include `serviceId` in your query filter. For performance, also specify\n * both `startDate` and `endDate`. Refer to the\n * *supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))\n * for a complete list of supported filters.\n *\n * ## Limit the number of slots returned per day\n *\n * To limit the maximum number of appointment slots returned for each date,\n * specify `slotsPerDay`. For example,  specifying `{\"slotsPerDay\": 3}` returns\n * a maximum of 3 appointment slots/class events for each day that's within the query\n * filter's date range.\n *\n * ## Unavailable slots\n *\n * By default, both available and unavailable appointment slots/ class events\n * are returned. To retrieve only available slots/events, specify\n * `{\"bookable\": true}` in the filter.\n *\n * ## Sorting\n *\n * Slots/events can be sorted only by `startDate`. Unavailable slots/events\n * always appear after available ones.\n *\n * ## Availability calculation\n *\n * Wix Bookings calculates availability differently for appointment-based\n * services and classes.\n *\n * ### Appointment availability\n *\n * An appointment slot is `bookable` if:\n * - The slot's `startDate` is later than or equal to `query.filter.startDate`.\n * - The slot's `endDate` is before or equal to `query.filter.endDate`.\n * - The service's `onlineBooking.enabled` is `true`.\n * - The service's early/late booking policies are either not enabled or the\n *   current time is within the defined limits.\n *\n * ### Class event availability\n *\n * A class event is `bookable` if:\n * - The event's `startDate` is later than or equal to `query.filter.startDate`.\n * - The event's `endDate` is before or equal to `query.filter.endDate`.\n * - The event's `remainingCapacity` is greater than `0`.\n * - The service's `onlineBooking.enabled` is `true`.\n * - The service's early/late booking policies are either not enabled or the\n *   current time is within the defined limits.\n *\n * If you want to restrict returned class events to those with a minimum number\n * of remaining spots, specify `openSpots` in the filter. By default, all class\n * events, even those with no open spot, are returned.\n *\n * ## Daylight Savings Time (DST)\n *\n * Due to DST, some local times may not exist or may occur twice. For example,\n * in Santiago, Chile, on September 7, 2025, 0:05 AM doesn't exist because the\n * clock advances from 0:00 AM to 1:00 AM.\n *\n * *Query Availability* automatically adjusts for these gaps. Non-existent times\n * are moved forward by 1 hour, while existing times remain unchanged. For\n * instance, if you call *Query Availability* with a `startDate` of\n * `2025-09-07T00:00:01.000` and an `endDate` of `2025-09-08T00:00:02.000`, the\n * adjusted query will use `2025-09-07T01:00:01.000` as the new start time.\n */\nexport function queryAvailability(payload: object): RequestOptionsFactory<any> {\n  function __queryAvailability({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v1.slot_availability',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.AvailabilityCalendar.QueryAvailability',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({\n        protoPath: '/v1/availability/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryAvailability;\n}\n\n/**\n * Returns availability for a given schedule ID\n * The availability for a course is calculated by:\n * - Checking for total spots by the schedule's capacity\n * - Checking for open spots by subtracting the current number of participants from the total spots\n * current number of participants is calculated by summing the number of participants of all bookings booked to the schedule\n * @deprecated\n */\nexport function getScheduleAvailability(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getScheduleAvailability({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v1.slot_availability',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.AvailabilityCalendar.GetScheduleAvailability',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({\n        protoPath: '/v2/availability/schedule/{scheduleId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __getScheduleAvailability;\n}\n\nexport function calculateMultiSlotAvailability(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __calculateMultiSlotAvailability({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v1.slot_availability',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.AvailabilityCalendar.CalculateMultiSlotAvailability',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({\n        protoPath: '/v1/availability/multislot',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __calculateMultiSlotAvailability;\n}\n", "import * as ambassadorWixBookingsAvailabilityV1SlotAvailability from './bookings-availability-v1-slot-availability-availability-calendar.http.js';\nimport * as ambassadorWixBookingsAvailabilityV1SlotAvailabilityTypes from './bookings-availability-v1-slot-availability-availability-calendar.types.js';\nimport * as ambassadorWixBookingsAvailabilityV1SlotAvailabilityUniversalTypes from './bookings-availability-v1-slot-availability-availability-calendar.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function queryAvailability(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityUniversalTypes.QueryAvailabilityRequest,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityTypes.QueryAvailabilityRequest,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityUniversalTypes.QueryAvailabilityResponse,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityTypes.QueryAvailabilityResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV1SlotAvailability.queryAvailability(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/availability/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getScheduleAvailability(): __PublicMethodMetaInfo<\n  'GET',\n  { scheduleId: string },\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityUniversalTypes.GetScheduleAvailabilityRequest,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityTypes.GetScheduleAvailabilityRequest,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityUniversalTypes.GetScheduleAvailabilityResponse,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityTypes.GetScheduleAvailabilityResponse\n> {\n  const payload = { scheduleId: ':scheduleId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV1SlotAvailability.getScheduleAvailability(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/availability/schedule/{scheduleId}',\n    pathParams: { scheduleId: 'scheduleId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function calculateMultiSlotAvailability(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityUniversalTypes.CalculateMultiSlotAvailabilityRequest,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityTypes.CalculateMultiSlotAvailabilityRequest,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityUniversalTypes.CalculateMultiSlotAvailabilityResponse,\n  ambassadorWixBookingsAvailabilityV1SlotAvailabilityTypes.CalculateMultiSlotAvailabilityResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsAvailabilityV1SlotAvailability.calculateMultiSlotAvailability(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/availability/multislot',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,kBAAkB;AAI3B,SAAS,8DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAmFd,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8DAA8D;AAAA,QACjE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8DAA8D;AAAA,QACjE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,SAAS,+BACd,SAC4B;AAC5B,WAAS,iCAAiC,EAAE,KAAK,GAAQ;AACvD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8DAA8D;AAAA,QACjE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC/SO,SAASA,qBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACgD;AAAA,IAClD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,2BAOd;AACA,QAAM,UAAU,EAAE,YAAY,cAAc;AAE5C,QAAM,oBACgD;AAAA,IAClD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,YAAY,aAAa;AAAA,IACvC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kCAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACgD;AAAA,IAClD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["queryAvailability", "getScheduleAvailability", "calculateMultiSlotAvailability"]}