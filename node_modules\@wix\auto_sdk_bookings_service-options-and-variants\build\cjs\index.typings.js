"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.typings.ts
var index_typings_exports = {};
__export(index_typings_exports, {
  DayOfWeek: () => DayOfWeek,
  PlacementType: () => PlacementType,
  ResolutionMethod: () => ResolutionMethod,
  ServiceOptionType: () => ServiceOptionType,
  SortOrder: () => SortOrder,
  WebhookIdentityType: () => WebhookIdentityType,
  cloneServiceOptionsAndVariants: () => cloneServiceOptionsAndVariants2,
  createServiceOptionsAndVariants: () => createServiceOptionsAndVariants2,
  deleteServiceOptionsAndVariants: () => deleteServiceOptionsAndVariants2,
  getServiceOptionsAndVariants: () => getServiceOptionsAndVariants2,
  getServiceOptionsAndVariantsByServiceId: () => getServiceOptionsAndVariantsByServiceId2,
  queryServiceOptionsAndVariants: () => queryServiceOptionsAndVariants2,
  updateServiceOptionsAndVariants: () => updateServiceOptionsAndVariants2
});
module.exports = __toCommonJS(index_typings_exports);

// src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_query_builder = require("@wix/sdk-runtime/query-builder");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      },
      {
        srcPath: "/_api/bookings/v1/service-options-and-variants",
        destPath: "/api"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/serviceOptionsAndVariants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "bo._base_domain_": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "wixbo.ai": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ],
    "wix-bo.com": [
      {
        srcPath: "/_api/v1/service-options-and-variants",
        destPath: "/v1/serviceOptionsAndVariants"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_service-options-and-variants";
function createServiceOptionsAndVariants(payload) {
  function __createServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CreateServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __createServiceOptionsAndVariants;
}
function cloneServiceOptionsAndVariants(payload) {
  function __cloneServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CloneServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{cloneFromId}/clone",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __cloneServiceOptionsAndVariants;
}
function getServiceOptionsAndVariants(payload) {
  function __getServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "GET",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      fallback: [
        {
          method: "GET",
          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
            protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
            data: payload,
            host
          }),
          params: (0, import_rest_modules.toURLSearchParams)(payload)
        }
      ]
    };
    return metadata;
  }
  return __getServiceOptionsAndVariants;
}
function getServiceOptionsAndVariantsByServiceId(payload) {
  function __getServiceOptionsAndVariantsByServiceId({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "GET",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariantsByServiceId",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      fallback: [
        {
          method: "GET",
          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
            protoPath: "/v1/serviceOptionsAndVariants/service_id/{serviceId}",
            data: payload,
            host
          }),
          params: (0, import_rest_modules.toURLSearchParams)(payload)
        }
      ]
    };
    return metadata;
  }
  return __getServiceOptionsAndVariantsByServiceId;
}
function updateServiceOptionsAndVariants(payload) {
  function __updateServiceOptionsAndVariants({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "PATCH",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.UpdateServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}",
        data: serializedData,
        host
      }),
      data: serializedData
    };
    return metadata;
  }
  return __updateServiceOptionsAndVariants;
}
function deleteServiceOptionsAndVariants(payload) {
  function __deleteServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "DELETE",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.DeleteServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteServiceOptionsAndVariants;
}
function queryServiceOptionsAndVariants(payload) {
  function __queryServiceOptionsAndVariants({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.catalog.v1.service_options_and_variants",
      method: "POST",
      methodFqn: "wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.QueryServiceOptionsAndVariants",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({
        protoPath: "/v1/serviceOptionsAndVariants/query",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __queryServiceOptionsAndVariants;
}

// src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.ts
var import_transform_paths2 = require("@wix/sdk-runtime/transformations/transform-paths");
var ServiceOptionType = /* @__PURE__ */ ((ServiceOptionType2) => {
  ServiceOptionType2["UNKNOWN"] = "UNKNOWN";
  ServiceOptionType2["CUSTOM"] = "CUSTOM";
  ServiceOptionType2["STAFF_MEMBER"] = "STAFF_MEMBER";
  ServiceOptionType2["DURATION"] = "DURATION";
  return ServiceOptionType2;
})(ServiceOptionType || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var PlacementType = /* @__PURE__ */ ((PlacementType2) => {
  PlacementType2["BEFORE"] = "BEFORE";
  PlacementType2["AFTER"] = "AFTER";
  PlacementType2["REPLACE"] = "REPLACE";
  return PlacementType2;
})(PlacementType || {});
var DayOfWeek = /* @__PURE__ */ ((DayOfWeek2) => {
  DayOfWeek2["MONDAY"] = "MONDAY";
  DayOfWeek2["TUESDAY"] = "TUESDAY";
  DayOfWeek2["WEDNESDAY"] = "WEDNESDAY";
  DayOfWeek2["THURSDAY"] = "THURSDAY";
  DayOfWeek2["FRIDAY"] = "FRIDAY";
  DayOfWeek2["SATURDAY"] = "SATURDAY";
  DayOfWeek2["SUNDAY"] = "SUNDAY";
  return DayOfWeek2;
})(DayOfWeek || {});
var ResolutionMethod = /* @__PURE__ */ ((ResolutionMethod2) => {
  ResolutionMethod2["QUERY_PARAM"] = "QUERY_PARAM";
  ResolutionMethod2["SUBDOMAIN"] = "SUBDOMAIN";
  ResolutionMethod2["SUBDIRECTORY"] = "SUBDIRECTORY";
  return ResolutionMethod2;
})(ResolutionMethod || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createServiceOptionsAndVariants2(serviceOptionsAndVariants) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceOptionsAndVariants
  });
  const reqOpts = createServiceOptionsAndVariants(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.serviceOptionsAndVariants;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { serviceOptionsAndVariants: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["serviceOptionsAndVariants"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function cloneServiceOptionsAndVariants2(cloneFromId, targetServiceId) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    cloneFromId,
    targetServiceId
  });
  const reqOpts = cloneServiceOptionsAndVariants(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          cloneFromId: "$[0]",
          targetServiceId: "$[1]"
        },
        singleArgumentUnchanged: false
      },
      ["cloneFromId", "targetServiceId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getServiceOptionsAndVariants2(serviceOptionsAndVariantsId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceOptionsAndVariantsId
  });
  const reqOpts = getServiceOptionsAndVariants(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.serviceOptionsAndVariants;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { serviceOptionsAndVariantsId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["serviceOptionsAndVariantsId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getServiceOptionsAndVariantsByServiceId2(serviceId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceId
  });
  const reqOpts = getServiceOptionsAndVariantsByServiceId(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { serviceId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["serviceId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateServiceOptionsAndVariants2(_id, serviceOptionsAndVariants) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceOptionsAndVariants: { ...serviceOptionsAndVariants, id: _id }
  });
  const reqOpts = updateServiceOptionsAndVariants(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.serviceOptionsAndVariants;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: { serviceOptionsAndVariants: "$[1]" },
        explicitPathsToArguments: { "serviceOptionsAndVariants.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "serviceOptionsAndVariants"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteServiceOptionsAndVariants2(serviceOptionsAndVariantsId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    serviceOptionsAndVariantsId,
    revision: options?.revision
  });
  const reqOpts = deleteServiceOptionsAndVariants(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceOptionsAndVariantsId: "$[0]",
          revision: "$[1].revision"
        },
        singleArgumentUnchanged: false
      },
      ["serviceOptionsAndVariantsId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryServiceOptionsAndVariants2() {
  const { httpClient, sideEffects } = arguments[0];
  return (0, import_query_builder.queryBuilder)({
    func: async (payload) => {
      const reqOpts = queryServiceOptionsAndVariants(
        payload
      );
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({
      data
    }) => {
      const transformedData = (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
        (0, import_transform_paths2.transformPaths)(data, [])
      );
      return {
        items: transformedData?.serviceOptionsAndVariantsList,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = (0, import_transform_error.transformError)(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  DayOfWeek,
  PlacementType,
  ResolutionMethod,
  ServiceOptionType,
  SortOrder,
  WebhookIdentityType,
  cloneServiceOptionsAndVariants,
  createServiceOptionsAndVariants,
  deleteServiceOptionsAndVariants,
  getServiceOptionsAndVariants,
  getServiceOptionsAndVariantsByServiceId,
  queryServiceOptionsAndVariants,
  updateServiceOptionsAndVariants
});
//# sourceMappingURL=index.typings.js.map