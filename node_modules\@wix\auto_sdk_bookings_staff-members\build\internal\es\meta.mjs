// src/bookings-staff-v1-staff-member-staff-members.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformRESTFloatToSDKFloat } from "@wix/sdk-runtime/transformations/float";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsStaffV1StaffMembersServiceUrl(opts) {
  const domainToMappings = {
    "www._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/staff-members",
        destPath: "/v1/staff-members"
      },
      {
        srcPath: "/bookings/v1/bulk/staff-members",
        destPath: "/v1/bulk/staff-members"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_staff-members";
function createStaffMember(payload) {
  function __createStaffMember({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "staffMember.createdDate" },
          { path: "staffMember.updatedDate" },
          { path: "staffMember.mainMedia.image.urlExpirationDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.CreateStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createStaffMember;
}
function getStaffMember(payload) {
  function __getStaffMember({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "GET",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.GetStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getStaffMember;
}
function updateStaffMember(payload) {
  function __updateStaffMember({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "staffMember.createdDate" },
          { path: "staffMember.updatedDate" },
          { path: "staffMember.mainMedia.image.urlExpirationDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "PATCH",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.UpdateStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMember.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateStaffMember;
}
function deleteStaffMember(payload) {
  function __deleteStaffMember({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "DELETE",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.DeleteStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteStaffMember;
}
function queryStaffMembers(payload) {
  function __queryStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.QueryStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMembers.createdDate" },
            { path: "staffMembers.updatedDate" },
            { path: "staffMembers.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryStaffMembers;
}
function countStaffMembers(payload) {
  function __countStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.CountStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countStaffMembers;
}
function connectStaffMemberToUser(payload) {
  function __connectStaffMemberToUser({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.ConnectStaffMemberToUser",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/connect-staff-member-to-user",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __connectStaffMemberToUser;
}
function searchStaffMembers(payload) {
  function __searchStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/search",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMembers.createdDate" },
            { path: "staffMembers.updatedDate" },
            { path: "staffMembers.mainMedia.image.urlExpirationDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "aggregationData.results.ranges.results.from" },
            { path: "aggregationData.results.ranges.results.to" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from"
            },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.from"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.to"
            },
            { path: "aggregationData.results.scalar.value" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.scalar.value"
            },
            {
              path: "aggregationData.results.nested.results.results.*.scalar.value"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __searchStaffMembers;
}
function disconnectStaffMemberFromUser(payload) {
  function __disconnectStaffMemberFromUser({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.DisconnectStaffMemberFromUser",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __disconnectStaffMemberFromUser;
}
function assignWorkingHoursSchedule(payload) {
  function __assignWorkingHoursSchedule({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/assign-working-hours-schedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __assignWorkingHoursSchedule;
}
function assignCustomSchedule(payload) {
  function __assignCustomSchedule({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.AssignCustomSchedule",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/assign-custom-schedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __assignCustomSchedule;
}
function bulkUpdateStaffMemberTags(payload) {
  function __bulkUpdateStaffMemberTags({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTags",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/bulk/staff-members/update-tags",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkUpdateStaffMemberTags;
}
function bulkUpdateStaffMemberTagsByFilter(payload) {
  function __bulkUpdateStaffMemberTagsByFilter({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTagsByFilter",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/bulk/staff-members/update-tags-by-filter",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkUpdateStaffMemberTagsByFilter;
}

// src/bookings-staff-v1-staff-member-staff-members.meta.ts
function createStaffMember2() {
  const payload = {};
  const getRequestOptions = createStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getStaffMember2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = getStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/staff-members/{staffMemberId}",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateStaffMember2() {
  const payload = { staffMember: { id: ":staffMemberId" } };
  const getRequestOptions = updateStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v1/staff-members/{staffMember.id}",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteStaffMember2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = deleteStaffMember(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v1/staff-members/{staffMemberId}",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryStaffMembers2() {
  const payload = {};
  const getRequestOptions = queryStaffMembers(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countStaffMembers2() {
  const payload = {};
  const getRequestOptions = countStaffMembers(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function connectStaffMemberToUser2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = connectStaffMemberToUser(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/connect-staff-member-to-user",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function searchStaffMembers2() {
  const payload = {};
  const getRequestOptions = searchStaffMembers(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/search",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function disconnectStaffMemberFromUser2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = disconnectStaffMemberFromUser(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function assignWorkingHoursSchedule2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = assignWorkingHoursSchedule(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/assign-working-hours-schedule",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function assignCustomSchedule2() {
  const payload = { staffMemberId: ":staffMemberId" };
  const getRequestOptions = assignCustomSchedule(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/staff-members/{staffMemberId}/assign-custom-schedule",
    pathParams: { staffMemberId: "staffMemberId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkUpdateStaffMemberTags2() {
  const payload = {};
  const getRequestOptions = bulkUpdateStaffMemberTags(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/bulk/staff-members/update-tags",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function bulkUpdateStaffMemberTagsByFilter2() {
  const payload = {};
  const getRequestOptions = bulkUpdateStaffMemberTagsByFilter(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/bulk/staff-members/update-tags-by-filter",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  assignCustomSchedule2 as assignCustomSchedule,
  assignWorkingHoursSchedule2 as assignWorkingHoursSchedule,
  bulkUpdateStaffMemberTags2 as bulkUpdateStaffMemberTags,
  bulkUpdateStaffMemberTagsByFilter2 as bulkUpdateStaffMemberTagsByFilter,
  connectStaffMemberToUser2 as connectStaffMemberToUser,
  countStaffMembers2 as countStaffMembers,
  createStaffMember2 as createStaffMember,
  deleteStaffMember2 as deleteStaffMember,
  disconnectStaffMemberFromUser2 as disconnectStaffMemberFromUser,
  getStaffMember2 as getStaffMember,
  queryStaffMembers2 as queryStaffMembers,
  searchStaffMembers2 as searchStaffMembers,
  updateStaffMember2 as updateStaffMember
};
//# sourceMappingURL=meta.mjs.map