import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { ResourceType, CreateResourceTypeApplicationErrors, UpdateResourceType, UpdateResourceTypeApplicationErrors, ResourceTypesQueryBuilder, CountResourceTypesOptions, CountResourceTypesResponse, ResourceTypeCreatedEnvelope, ResourceTypeDeletedEnvelope, ResourceTypeUpdatedEnvelope } from './index.typings.mjs';
export { ActionEvent, BaseEventMetadata, BusinessLocation, CountResourceTypesRequest, CreateResourceTypeErrors, CreateResourceTypeErrorsWithLiterals, CreateResourceTypeRequest, CreateResourceTypeResponse, CursorPaging, CursorPagingMetadata, CursorQuery, CursorQueryPagingMethodOneOf, Cursors, DeleteResourceTypeRequest, DeleteResourceTypeResponse, DistinctLocationIds, DomainEvent, DomainEventBodyOneOf, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, EventMetadata, ExtendedFields, GetResourceTypeRequest, GetResourceTypeResponse, IdentificationData, IdentificationDataIdOneOf, LocationOptions, MessageEnvelope, QueryResourceTypesRequest, QueryResourceTypesResponse, RequestedFields, RequestedFieldsWithLiterals, ResourceCounts, ResourceTypesQueryResult, RestoreInfo, SortOrder, SortOrderWithLiterals, Sorting, SpecificLocation, UpdateResourceTypeRequest, UpdateResourceTypeResponse, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.mjs';

declare function createResourceType$1(httpClient: HttpClient): CreateResourceTypeSignature;
interface CreateResourceTypeSignature {
    /**
     * Creates a new resource type.
     * @param - Resource type to create.
     * @returns Created resource type.
     */
    (resourceType: NonNullablePaths<ResourceType, `name`, 2>): Promise<ResourceType & {
        __applicationErrorsType?: CreateResourceTypeApplicationErrors;
    }>;
}
declare function getResourceType$1(httpClient: HttpClient): GetResourceTypeSignature;
interface GetResourceTypeSignature {
    /**
     * Retrieves a resource type.
     * @param - ID of the resource type to retrieve.
     * @returns Retrieved resource type.
     */
    (resourceTypeId: string): Promise<ResourceType>;
}
declare function updateResourceType$1(httpClient: HttpClient): UpdateResourceTypeSignature;
interface UpdateResourceTypeSignature {
    /**
     * Updates a resource type.
     *
     *
     * Each time the resource type is updated, `revision` increments by 1. You must
     * include current revision of the resource type when updating it. This ensures
     * you're working with the latest service information and prevents unintended overwrites.
     * @param - Resource type ID.
     * @returns Updated resource type.
     */
    (_id: string, resourceType: NonNullablePaths<UpdateResourceType, `revision`, 2>): Promise<ResourceType & {
        __applicationErrorsType?: UpdateResourceTypeApplicationErrors;
    }>;
}
declare function deleteResourceType$1(httpClient: HttpClient): DeleteResourceTypeSignature;
interface DeleteResourceTypeSignature {
    /**
     * Deletes a resource type.
     *
     *
     * Deleting a resource type also automatically deletes all resources connected to it.
     * @param - ID of the resource type to delete.
     */
    (resourceTypeId: string): Promise<void>;
}
declare function queryResourceTypes$1(httpClient: HttpClient): QueryResourceTypesSignature;
interface QueryResourceTypesSignature {
    /**
     * Creates a query to retrieve a list of resource types.
     *
     * The `queryResourceTypes()` function builds a query to retrieve a list of resource types and returns a `ResourceTypesQueryBuilder` object.
     *
     * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-builder/find) function.
     *
     * You can refine the query by chaining `ResourceTypesQueryBuilder` functions onto the query. `ResourceTypesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResourceTypes()` returns.
     *
     * `queryResourceTypes()` runs with the following `ResourceTypesQueryBuilder` default that you can override:
     *
     * + `limit` is `50`.
     * + Sorted by `id` in ascending order.
     *
     * The functions that are chained to `queryResourceTypes()` are applied in the order they are called.
     *
     * The following `ResourceTypesQueryBuilder` functions are supported for the `queryResourceTypes()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-result/items) property in `ResourceTypesQueryResult`.
     */
    (): ResourceTypesQueryBuilder;
}
declare function countResourceTypes$1(httpClient: HttpClient): CountResourceTypesSignature;
interface CountResourceTypesSignature {
    /**
     * Counts resource types, given the provided filtering.
     *
     *
     * Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     * @param - Filter to base the count on. See
     * [queryResourceTypes()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/query-resource-types)
     * for supported filters.
     */
    (options?: CountResourceTypesOptions): Promise<NonNullablePaths<CountResourceTypesResponse, `count`, 2>>;
}
declare const onResourceTypeCreated$1: EventDefinition<ResourceTypeCreatedEnvelope, "wix.bookings.resources.v2.resource_type_created">;
declare const onResourceTypeDeleted$1: EventDefinition<ResourceTypeDeletedEnvelope, "wix.bookings.resources.v2.resource_type_deleted">;
declare const onResourceTypeUpdated$1: EventDefinition<ResourceTypeUpdatedEnvelope, "wix.bookings.resources.v2.resource_type_updated">;

declare const createResourceType: MaybeContext<BuildRESTFunction<typeof createResourceType$1> & typeof createResourceType$1>;
declare const getResourceType: MaybeContext<BuildRESTFunction<typeof getResourceType$1> & typeof getResourceType$1>;
declare const updateResourceType: MaybeContext<BuildRESTFunction<typeof updateResourceType$1> & typeof updateResourceType$1>;
declare const deleteResourceType: MaybeContext<BuildRESTFunction<typeof deleteResourceType$1> & typeof deleteResourceType$1>;
declare const queryResourceTypes: MaybeContext<BuildRESTFunction<typeof queryResourceTypes$1> & typeof queryResourceTypes$1>;
declare const countResourceTypes: MaybeContext<BuildRESTFunction<typeof countResourceTypes$1> & typeof countResourceTypes$1>;
/**
 * Triggered when a resource type is created.
 */
declare const onResourceTypeCreated: BuildEventDefinition<typeof onResourceTypeCreated$1>;
/**
 * Triggered when a resource type is deleted.
 */
declare const onResourceTypeDeleted: BuildEventDefinition<typeof onResourceTypeDeleted$1>;
/**
 * Triggered when a resource type is updated.
 */
declare const onResourceTypeUpdated: BuildEventDefinition<typeof onResourceTypeUpdated$1>;

export { CountResourceTypesOptions, CountResourceTypesResponse, CreateResourceTypeApplicationErrors, ResourceType, ResourceTypeCreatedEnvelope, ResourceTypeDeletedEnvelope, ResourceTypeUpdatedEnvelope, ResourceTypesQueryBuilder, UpdateResourceType, UpdateResourceTypeApplicationErrors, countResourceTypes, createResourceType, deleteResourceType, getResourceType, onResourceTypeCreated, onResourceTypeDeleted, onResourceTypeUpdated, queryResourceTypes, updateResourceType };
