"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  ActionType: () => ActionType,
  Actor: () => Actor,
  AttendanceStatus: () => AttendanceStatus,
  AuthorizationCaptureStatus: () => AuthorizationCaptureStatus,
  AuthorizationVoidStatus: () => AuthorizationVoidStatus,
  BookingFeeStatus: () => BookingFeeStatus,
  BookingPaymentStatus: () => BookingPaymentStatus,
  BookingStatus: () => BookingStatus,
  ChargebackStatus: () => ChargebackStatus,
  IdentityType: () => IdentityType,
  LocationType: () => LocationType,
  MembershipPaymentStatus: () => MembershipPaymentStatus,
  MultiServiceBookingType: () => MultiServiceBookingType,
  OrderStatus: () => OrderStatus,
  PaymentOptionType: () => PaymentOptionType,
  PaymentStatus: () => PaymentStatus,
  Platform: () => Platform,
  Reason: () => Reason,
  RefundStatus: () => RefundStatus,
  SelectedPaymentOption: () => SelectedPaymentOption,
  SortOrder: () => SortOrder,
  Status: () => Status,
  TransactionStatus: () => TransactionStatus,
  ValueType: () => ValueType,
  countExtendedBookings: () => countExtendedBookings4,
  query: () => query4,
  queryExtendedBookings: () => queryExtendedBookings4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-reader-v2-extended-booking-extended-bookings.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-reader-v2-extended-booking-extended-bookings.http.ts
var import_float = require("@wix/sdk-runtime/transformations/float");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsReaderV2BookingsReaderUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "editor._base_domain": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      },
      {
        srcPath: "/bookings/bookings-reader",
        destPath: ""
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_extended-bookings";
function query(payload) {
  function __query({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.reader.v2.extended_booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.reader.v2.BookingsReader.Query",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({
        protoPath: "/v2/extended-booking/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "extendedBookings.booking.createdDate" },
            { path: "extendedBookings.booking.startDate" },
            { path: "extendedBookings.booking.endDate" },
            { path: "extendedBookings.booking.updatedDate" },
            { path: "extendedBookings.booking.canceledDate" },
            { path: "extendedBookings.transactions.payments.createdDate" },
            { path: "extendedBookings.transactions.payments.updatedDate" },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate"
            },
            { path: "extendedBookings.transactions.refunds.createdDate" }
          ]
        },
        {
          transformFn: import_float.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __query;
}
function queryExtendedBookings(payload) {
  function __queryExtendedBookings({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.reader.v2.extended_booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.reader.v2.BookingsReader.QueryExtendedBookings",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({
        protoPath: "/v2/extended-bookings/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "extendedBookings.booking.createdDate" },
            { path: "extendedBookings.booking.startDate" },
            { path: "extendedBookings.booking.endDate" },
            { path: "extendedBookings.booking.updatedDate" },
            { path: "extendedBookings.booking.canceledDate" },
            { path: "extendedBookings.transactions.payments.createdDate" },
            { path: "extendedBookings.transactions.payments.updatedDate" },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate"
            },
            { path: "extendedBookings.transactions.refunds.createdDate" }
          ]
        },
        {
          transformFn: import_float.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryExtendedBookings;
}
function countExtendedBookings(payload) {
  function __countExtendedBookings({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.reader.v2.extended_booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.reader.v2.BookingsReader.CountExtendedBookings",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({
        protoPath: "/v2/extended-bookings/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countExtendedBookings;
}

// src/bookings-reader-v2-extended-booking-extended-bookings.universal.ts
var AttendanceStatus = /* @__PURE__ */ ((AttendanceStatus2) => {
  AttendanceStatus2["NOT_SET"] = "NOT_SET";
  AttendanceStatus2["ATTENDED"] = "ATTENDED";
  AttendanceStatus2["NOT_ATTENDED"] = "NOT_ATTENDED";
  return AttendanceStatus2;
})(AttendanceStatus || {});
var MultiServiceBookingType = /* @__PURE__ */ ((MultiServiceBookingType2) => {
  MultiServiceBookingType2["SEQUENTIAL_BOOKINGS"] = "SEQUENTIAL_BOOKINGS";
  MultiServiceBookingType2["SEPARATE_BOOKINGS"] = "SEPARATE_BOOKINGS";
  MultiServiceBookingType2["PARALLEL_BOOKINGS"] = "PARALLEL_BOOKINGS";
  return MultiServiceBookingType2;
})(MultiServiceBookingType || {});
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNDEFINED"] = "UNDEFINED";
  LocationType2["OWNER_BUSINESS"] = "OWNER_BUSINESS";
  LocationType2["OWNER_CUSTOM"] = "OWNER_CUSTOM";
  LocationType2["CUSTOM"] = "CUSTOM";
  return LocationType2;
})(LocationType || {});
var ValueType = /* @__PURE__ */ ((ValueType2) => {
  ValueType2["SHORT_TEXT"] = "SHORT_TEXT";
  ValueType2["LONG_TEXT"] = "LONG_TEXT";
  ValueType2["CHECK_BOX"] = "CHECK_BOX";
  return ValueType2;
})(ValueType || {});
var BookingStatus = /* @__PURE__ */ ((BookingStatus2) => {
  BookingStatus2["CREATED"] = "CREATED";
  BookingStatus2["CONFIRMED"] = "CONFIRMED";
  BookingStatus2["CANCELED"] = "CANCELED";
  BookingStatus2["PENDING"] = "PENDING";
  BookingStatus2["DECLINED"] = "DECLINED";
  BookingStatus2["WAITING_LIST"] = "WAITING_LIST";
  return BookingStatus2;
})(BookingStatus || {});
var BookingPaymentStatus = /* @__PURE__ */ ((BookingPaymentStatus2) => {
  BookingPaymentStatus2["UNDEFINED"] = "UNDEFINED";
  BookingPaymentStatus2["NOT_PAID"] = "NOT_PAID";
  BookingPaymentStatus2["PAID"] = "PAID";
  BookingPaymentStatus2["PARTIALLY_PAID"] = "PARTIALLY_PAID";
  BookingPaymentStatus2["REFUNDED"] = "REFUNDED";
  BookingPaymentStatus2["EXEMPT"] = "EXEMPT";
  return BookingPaymentStatus2;
})(BookingPaymentStatus || {});
var SelectedPaymentOption = /* @__PURE__ */ ((SelectedPaymentOption2) => {
  SelectedPaymentOption2["UNDEFINED"] = "UNDEFINED";
  SelectedPaymentOption2["OFFLINE"] = "OFFLINE";
  SelectedPaymentOption2["ONLINE"] = "ONLINE";
  SelectedPaymentOption2["MEMBERSHIP"] = "MEMBERSHIP";
  SelectedPaymentOption2["MEMBERSHIP_OFFLINE"] = "MEMBERSHIP_OFFLINE";
  return SelectedPaymentOption2;
})(SelectedPaymentOption || {});
var Platform = /* @__PURE__ */ ((Platform2) => {
  Platform2["UNDEFINED_PLATFORM"] = "UNDEFINED_PLATFORM";
  Platform2["WEB"] = "WEB";
  Platform2["MOBILE_APP"] = "MOBILE_APP";
  return Platform2;
})(Platform || {});
var Actor = /* @__PURE__ */ ((Actor2) => {
  Actor2["UNDEFINED_ACTOR"] = "UNDEFINED_ACTOR";
  Actor2["BUSINESS"] = "BUSINESS";
  Actor2["CUSTOMER"] = "CUSTOMER";
  return Actor2;
})(Actor || {});
var IdentityType = /* @__PURE__ */ ((IdentityType2) => {
  IdentityType2["UNKNOWN"] = "UNKNOWN";
  IdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  IdentityType2["MEMBER"] = "MEMBER";
  IdentityType2["WIX_USER"] = "WIX_USER";
  IdentityType2["APP"] = "APP";
  return IdentityType2;
})(IdentityType || {});
var BookingFeeStatus = /* @__PURE__ */ ((BookingFeeStatus2) => {
  BookingFeeStatus2["UNKNOWN_STATUS"] = "UNKNOWN_STATUS";
  BookingFeeStatus2["NOT_YET_APPLIED_TO_ORDER"] = "NOT_YET_APPLIED_TO_ORDER";
  BookingFeeStatus2["APPLIED_TO_ORDER"] = "APPLIED_TO_ORDER";
  return BookingFeeStatus2;
})(BookingFeeStatus || {});
var PaymentOptionType = /* @__PURE__ */ ((PaymentOptionType2) => {
  PaymentOptionType2["FULL_PAYMENT_ONLINE"] = "FULL_PAYMENT_ONLINE";
  PaymentOptionType2["FULL_PAYMENT_OFFLINE"] = "FULL_PAYMENT_OFFLINE";
  PaymentOptionType2["MEMBERSHIP"] = "MEMBERSHIP";
  PaymentOptionType2["DEPOSIT_ONLINE"] = "DEPOSIT_ONLINE";
  PaymentOptionType2["MEMBERSHIP_OFFLINE"] = "MEMBERSHIP_OFFLINE";
  PaymentOptionType2["MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER"] = "MEMBERSHIP_ONLINE_WITH_OFFLINE_REMAINDER";
  return PaymentOptionType2;
})(PaymentOptionType || {});
var OrderStatus = /* @__PURE__ */ ((OrderStatus2) => {
  OrderStatus2["INITIALIZED"] = "INITIALIZED";
  OrderStatus2["APPROVED"] = "APPROVED";
  OrderStatus2["CANCELED"] = "CANCELED";
  OrderStatus2["PENDING"] = "PENDING";
  OrderStatus2["REJECTED"] = "REJECTED";
  return OrderStatus2;
})(OrderStatus || {});
var PaymentStatus = /* @__PURE__ */ ((PaymentStatus2) => {
  PaymentStatus2["UNSPECIFIED"] = "UNSPECIFIED";
  PaymentStatus2["NOT_PAID"] = "NOT_PAID";
  PaymentStatus2["PAID"] = "PAID";
  PaymentStatus2["PARTIALLY_REFUNDED"] = "PARTIALLY_REFUNDED";
  PaymentStatus2["FULLY_REFUNDED"] = "FULLY_REFUNDED";
  PaymentStatus2["PENDING"] = "PENDING";
  PaymentStatus2["PARTIALLY_PAID"] = "PARTIALLY_PAID";
  PaymentStatus2["PENDING_MERCHANT"] = "PENDING_MERCHANT";
  PaymentStatus2["CANCELED"] = "CANCELED";
  PaymentStatus2["DECLINED"] = "DECLINED";
  return PaymentStatus2;
})(PaymentStatus || {});
var Status = /* @__PURE__ */ ((Status2) => {
  Status2["UNDEFINED"] = "UNDEFINED";
  Status2["SIGNED"] = "SIGNED";
  Status2["NOT_SIGNED"] = "NOT_SIGNED";
  return Status2;
})(Status || {});
var TransactionStatus = /* @__PURE__ */ ((TransactionStatus2) => {
  TransactionStatus2["UNDEFINED"] = "UNDEFINED";
  TransactionStatus2["APPROVED"] = "APPROVED";
  TransactionStatus2["PENDING"] = "PENDING";
  TransactionStatus2["PENDING_MERCHANT"] = "PENDING_MERCHANT";
  TransactionStatus2["CANCELED"] = "CANCELED";
  TransactionStatus2["DECLINED"] = "DECLINED";
  TransactionStatus2["REFUNDED"] = "REFUNDED";
  TransactionStatus2["PARTIALLY_REFUNDED"] = "PARTIALLY_REFUNDED";
  TransactionStatus2["AUTHORIZED"] = "AUTHORIZED";
  TransactionStatus2["VOIDED"] = "VOIDED";
  return TransactionStatus2;
})(TransactionStatus || {});
var AuthorizationCaptureStatus = /* @__PURE__ */ ((AuthorizationCaptureStatus2) => {
  AuthorizationCaptureStatus2["UNKNOWN_STATUS"] = "UNKNOWN_STATUS";
  AuthorizationCaptureStatus2["PENDING"] = "PENDING";
  AuthorizationCaptureStatus2["SUCCEEDED"] = "SUCCEEDED";
  AuthorizationCaptureStatus2["FAILED"] = "FAILED";
  return AuthorizationCaptureStatus2;
})(AuthorizationCaptureStatus || {});
var AuthorizationVoidStatus = /* @__PURE__ */ ((AuthorizationVoidStatus2) => {
  AuthorizationVoidStatus2["UNKNOWN_STATUS"] = "UNKNOWN_STATUS";
  AuthorizationVoidStatus2["PENDING"] = "PENDING";
  AuthorizationVoidStatus2["SUCCEEDED"] = "SUCCEEDED";
  AuthorizationVoidStatus2["FAILED"] = "FAILED";
  return AuthorizationVoidStatus2;
})(AuthorizationVoidStatus || {});
var Reason = /* @__PURE__ */ ((Reason2) => {
  Reason2["UNKNOWN_REASON"] = "UNKNOWN_REASON";
  Reason2["MANUAL"] = "MANUAL";
  Reason2["SCHEDULED"] = "SCHEDULED";
  return Reason2;
})(Reason || {});
var ActionType = /* @__PURE__ */ ((ActionType2) => {
  ActionType2["UNKNOWN_ACTION_TYPE"] = "UNKNOWN_ACTION_TYPE";
  ActionType2["VOID"] = "VOID";
  ActionType2["CAPTURE"] = "CAPTURE";
  return ActionType2;
})(ActionType || {});
var ChargebackStatus = /* @__PURE__ */ ((ChargebackStatus2) => {
  ChargebackStatus2["UNSPECIFIED"] = "UNSPECIFIED";
  ChargebackStatus2["APPROVED"] = "APPROVED";
  ChargebackStatus2["REVERSED"] = "REVERSED";
  return ChargebackStatus2;
})(ChargebackStatus || {});
var MembershipPaymentStatus = /* @__PURE__ */ ((MembershipPaymentStatus2) => {
  MembershipPaymentStatus2["CHARGED"] = "CHARGED";
  MembershipPaymentStatus2["CHARGE_FAILED"] = "CHARGE_FAILED";
  return MembershipPaymentStatus2;
})(MembershipPaymentStatus || {});
var RefundStatus = /* @__PURE__ */ ((RefundStatus2) => {
  RefundStatus2["PENDING"] = "PENDING";
  RefundStatus2["SUCCEEDED"] = "SUCCEEDED";
  RefundStatus2["FAILED"] = "FAILED";
  RefundStatus2["SCHEDULED"] = "SCHEDULED";
  RefundStatus2["STARTED"] = "STARTED";
  return RefundStatus2;
})(RefundStatus || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
async function query2(query5, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    query: query5,
    withBookingAllowedActions: options?.withBookingAllowedActions,
    withBookingAttendanceInfo: options?.withBookingAttendanceInfo,
    sessionId: options?.sessionId,
    withEcomOrder: options?.withEcomOrder,
    withEcomInvoices: options?.withEcomInvoices,
    withEcomTransactions: options?.withEcomTransactions,
    withFormSubmissions: options?.withFormSubmissions
  });
  const reqOpts = query(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          query: "$[0]",
          withBookingAllowedActions: "$[1].withBookingAllowedActions",
          withBookingAttendanceInfo: "$[1].withBookingAttendanceInfo",
          sessionId: "$[1].sessionId",
          withEcomOrder: "$[1].withEcomOrder",
          withEcomInvoices: "$[1].withEcomInvoices",
          withEcomTransactions: "$[1].withEcomTransactions",
          withFormSubmissions: "$[1].withFormSubmissions"
        },
        singleArgumentUnchanged: false
      },
      ["query", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function queryExtendedBookings2(query5, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    query: query5,
    withBookingAllowedActions: options?.withBookingAllowedActions,
    withBookingAttendanceInfo: options?.withBookingAttendanceInfo,
    withBookingConferencingDetails: options?.withBookingConferencingDetails,
    withBookingPolicySettings: options?.withBookingPolicySettings,
    withBookingFeeDetails: options?.withBookingFeeDetails,
    withEcomOrder: options?.withEcomOrder,
    withEcomInvoices: options?.withEcomInvoices,
    withEcomTransactions: options?.withEcomTransactions,
    withFormSubmissions: options?.withFormSubmissions
  });
  const reqOpts = queryExtendedBookings(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          query: "$[0]",
          withBookingAllowedActions: "$[1].withBookingAllowedActions",
          withBookingAttendanceInfo: "$[1].withBookingAttendanceInfo",
          withBookingConferencingDetails: "$[1].withBookingConferencingDetails",
          withBookingPolicySettings: "$[1].withBookingPolicySettings",
          withBookingFeeDetails: "$[1].withBookingFeeDetails",
          withEcomOrder: "$[1].withEcomOrder",
          withEcomInvoices: "$[1].withEcomInvoices",
          withEcomTransactions: "$[1].withEcomTransactions",
          withFormSubmissions: "$[1].withFormSubmissions"
        },
        singleArgumentUnchanged: false
      },
      ["query", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function countExtendedBookings2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = countExtendedBookings(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-reader-v2-extended-booking-extended-bookings.public.ts
function query3(httpClient) {
  return (query5, options) => query2(
    query5,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function queryExtendedBookings3(httpClient) {
  return (query5, options) => queryExtendedBookings2(
    query5,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function countExtendedBookings3(httpClient) {
  return (options) => countExtendedBookings2(
    options,
    // @ts-ignore
    { httpClient }
  );
}

// src/bookings-reader-v2-extended-booking-extended-bookings.context.ts
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
var query4 = /* @__PURE__ */ (0, import_rest_modules2.createRESTModule)(query3);
var queryExtendedBookings4 = /* @__PURE__ */ (0, import_rest_modules2.createRESTModule)(queryExtendedBookings3);
var countExtendedBookings4 = /* @__PURE__ */ (0, import_rest_modules2.createRESTModule)(countExtendedBookings3);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ActionType,
  Actor,
  AttendanceStatus,
  AuthorizationCaptureStatus,
  AuthorizationVoidStatus,
  BookingFeeStatus,
  BookingPaymentStatus,
  BookingStatus,
  ChargebackStatus,
  IdentityType,
  LocationType,
  MembershipPaymentStatus,
  MultiServiceBookingType,
  OrderStatus,
  PaymentOptionType,
  PaymentStatus,
  Platform,
  Reason,
  RefundStatus,
  SelectedPaymentOption,
  SortOrder,
  Status,
  TransactionStatus,
  ValueType,
  countExtendedBookings,
  query,
  queryExtendedBookings
});
//# sourceMappingURL=index.js.map