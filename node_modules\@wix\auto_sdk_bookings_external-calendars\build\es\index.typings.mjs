// src/bookings-calendar-v2-external-calendar-external-calendars.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-calendar-v2-external-calendar-external-calendars.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(opts) {
  const domainToMappings = {
    "api._api_base_domain_": [
      {
        srcPath: "/external-calendar-2",
        destPath: ""
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/external-calendar",
        destPath: "/v2/external-calendar"
      },
      {
        srcPath: "/_api/bookings/v2/external-calendars",
        destPath: "/v2/external-calendars"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/external-calendars",
        destPath: "/v2/external-calendars"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_external-calendars";
function listProviders(payload) {
  function __listProviders({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListProviders",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        { protoPath: "/v2/external-calendars/providers", data: payload, host }
      ),
      params: toURLSearchParams(payload),
      fallback: [
        {
          method: "GET",
          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
            {
              protoPath: "/v2/external-calendars/providers",
              data: payload,
              host
            }
          ),
          params: toURLSearchParams(payload)
        }
      ]
    };
    return metadata;
  }
  return __listProviders;
}
function getConnection(payload) {
  function __getConnection({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.GetConnection",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}",
          data: payload,
          host
        }
      ),
      params: toURLSearchParams(payload),
      fallback: [
        {
          method: "GET",
          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
            {
              protoPath: "/v2/external-calendars/connections/{connectionId}",
              data: payload,
              host
            }
          ),
          params: toURLSearchParams(payload)
        }
      ]
    };
    return metadata;
  }
  return __getConnection;
}
function listConnections(payload) {
  function __listConnections({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListConnections",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        { protoPath: "/v2/external-calendars/connections", data: payload, host }
      ),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __listConnections;
}
function connectByOAuth(payload) {
  function __connectByOAuth({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "POST",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByOAuth",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections:connectByOAuth",
          data: payload,
          host
        }
      ),
      data: payload
    };
    return metadata;
  }
  return __connectByOAuth;
}
function connectByCredentials(payload) {
  function __connectByCredentials({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "POST",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByCredentials",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections:connectByCredentials",
          data: payload,
          host
        }
      ),
      data: payload
    };
    return metadata;
  }
  return __connectByCredentials;
}
function listCalendars(payload) {
  function __listCalendars({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListCalendars",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}/calendars",
          data: payload,
          host
        }
      ),
      params: toURLSearchParams(payload),
      fallback: [
        {
          method: "GET",
          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
            {
              protoPath: "/v2/external-calendars/connections/{connectionId}/calendars",
              data: payload,
              host
            }
          ),
          params: toURLSearchParams(payload)
        }
      ]
    };
    return metadata;
  }
  return __listCalendars;
}
function updateSyncConfig(payload) {
  function __updateSyncConfig({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "PATCH",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.UpdateSyncConfig",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}/sync-config",
          data: serializedData,
          host
        }
      ),
      data: serializedData
    };
    return metadata;
  }
  return __updateSyncConfig;
}
function disconnect(payload) {
  function __disconnect({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "POST",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.Disconnect",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}/disconnect",
          data: payload,
          host
        }
      ),
      data: payload
    };
    return metadata;
  }
  return __disconnect;
}
function listEvents(payload) {
  function __listEvents({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListEvents",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        { protoPath: "/v2/external-calendars/events", data: payload, host }
      ),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __listEvents;
}

// src/bookings-calendar-v2-external-calendar-external-calendars.universal.ts
var CalendarType = /* @__PURE__ */ ((CalendarType2) => {
  CalendarType2["UNDEFINED"] = "UNDEFINED";
  CalendarType2["GOOGLE"] = "GOOGLE";
  CalendarType2["I_CAL"] = "I_CAL";
  CalendarType2["OUTLOOK"] = "OUTLOOK";
  CalendarType2["OFFICE_365"] = "OFFICE_365";
  CalendarType2["MICROSOFT"] = "MICROSOFT";
  CalendarType2["OTHER"] = "OTHER";
  return CalendarType2;
})(CalendarType || {});
var ConnectMethod = /* @__PURE__ */ ((ConnectMethod2) => {
  ConnectMethod2["UNDEFINED"] = "UNDEFINED";
  ConnectMethod2["OAUTH"] = "OAUTH";
  ConnectMethod2["CREDENTIALS"] = "CREDENTIALS";
  return ConnectMethod2;
})(ConnectMethod || {});
var ListEventFromCalendars = /* @__PURE__ */ ((ListEventFromCalendars2) => {
  ListEventFromCalendars2["UNDEFINED"] = "UNDEFINED";
  ListEventFromCalendars2["NOT_SUPPORTED"] = "NOT_SUPPORTED";
  ListEventFromCalendars2["PRIMARY_CALENDAR_ONLY"] = "PRIMARY_CALENDAR_ONLY";
  ListEventFromCalendars2["SPECIFIC_CALENDARS"] = "SPECIFIC_CALENDARS";
  return ListEventFromCalendars2;
})(ListEventFromCalendars || {});
var SyncToCalendar = /* @__PURE__ */ ((SyncToCalendar2) => {
  SyncToCalendar2["UNDEFINED"] = "UNDEFINED";
  SyncToCalendar2["NOT_SUPPORTED"] = "NOT_SUPPORTED";
  SyncToCalendar2["PRIMARY_CALENDAR_ONLY"] = "PRIMARY_CALENDAR_ONLY";
  SyncToCalendar2["SPECIFIC_CALENDAR"] = "SPECIFIC_CALENDAR";
  SyncToCalendar2["DEDICATED_CALENDAR"] = "DEDICATED_CALENDAR";
  return SyncToCalendar2;
})(SyncToCalendar || {});
var Status = /* @__PURE__ */ ((Status2) => {
  Status2["UNDEFINED"] = "UNDEFINED";
  Status2["CONNECTED"] = "CONNECTED";
  Status2["SYNC_IN_PROGRESS"] = "SYNC_IN_PROGRESS";
  Status2["SYNCED"] = "SYNCED";
  Status2["DISCONNECTED"] = "DISCONNECTED";
  Status2["ERROR"] = "ERROR";
  return Status2;
})(Status || {});
var ErrorReason = /* @__PURE__ */ ((ErrorReason2) => {
  ErrorReason2["UNDEFINED"] = "UNDEFINED";
  ErrorReason2["TOKEN_REVOKED"] = "TOKEN_REVOKED";
  ErrorReason2["EXTERNAL_CALENDAR_CREATION_FAILED"] = "EXTERNAL_CALENDAR_CREATION_FAILED";
  ErrorReason2["EXTERNAL_CALENDAR_DELETED"] = "EXTERNAL_CALENDAR_DELETED";
  return ErrorReason2;
})(ErrorReason || {});
var SyncToErrorReason = /* @__PURE__ */ ((SyncToErrorReason2) => {
  SyncToErrorReason2["UNDEFINED"] = "UNDEFINED";
  SyncToErrorReason2["CALENDAR_CREATION_FAILURE"] = "CALENDAR_CREATION_FAILURE";
  SyncToErrorReason2["CALENDAR_DELETED"] = "CALENDAR_DELETED";
  return SyncToErrorReason2;
})(SyncToErrorReason || {});
var Day = /* @__PURE__ */ ((Day2) => {
  Day2["UNDEFINED"] = "UNDEFINED";
  Day2["MON"] = "MON";
  Day2["TUE"] = "TUE";
  Day2["WED"] = "WED";
  Day2["THU"] = "THU";
  Day2["FRI"] = "FRI";
  Day2["SAT"] = "SAT";
  Day2["SUN"] = "SUN";
  return Day2;
})(Day || {});
var Transparency = /* @__PURE__ */ ((Transparency2) => {
  Transparency2["UNDEFINED"] = "UNDEFINED";
  Transparency2["FREE"] = "FREE";
  Transparency2["BUSY"] = "BUSY";
  return Transparency2;
})(Transparency || {});
var RecurringIntervalType = /* @__PURE__ */ ((RecurringIntervalType2) => {
  RecurringIntervalType2["UNDEFINED"] = "UNDEFINED";
  RecurringIntervalType2["EVENT"] = "EVENT";
  RecurringIntervalType2["TIME_AVAILABILITY"] = "TIME_AVAILABILITY";
  RecurringIntervalType2["AVAILABILITY"] = "AVAILABILITY";
  return RecurringIntervalType2;
})(RecurringIntervalType || {});
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNDEFINED"] = "UNDEFINED";
  LocationType2["OWNER_BUSINESS"] = "OWNER_BUSINESS";
  LocationType2["OWNER_CUSTOM"] = "OWNER_CUSTOM";
  LocationType2["CUSTOM"] = "CUSTOM";
  return LocationType2;
})(LocationType || {});
var LocationStatus = /* @__PURE__ */ ((LocationStatus2) => {
  LocationStatus2["ACTIVE"] = "ACTIVE";
  LocationStatus2["INACTIVE"] = "INACTIVE";
  return LocationStatus2;
})(LocationStatus || {});
var LocationsLocationType = /* @__PURE__ */ ((LocationsLocationType2) => {
  LocationsLocationType2["UNKNOWN"] = "UNKNOWN";
  LocationsLocationType2["BRANCH"] = "BRANCH";
  LocationsLocationType2["OFFICES"] = "OFFICES";
  LocationsLocationType2["RECEPTION"] = "RECEPTION";
  LocationsLocationType2["HEADQUARTERS"] = "HEADQUARTERS";
  LocationsLocationType2["INVENTORY"] = "INVENTORY";
  return LocationsLocationType2;
})(LocationsLocationType || {});
var DayOfWeek = /* @__PURE__ */ ((DayOfWeek2) => {
  DayOfWeek2["MONDAY"] = "MONDAY";
  DayOfWeek2["TUESDAY"] = "TUESDAY";
  DayOfWeek2["WEDNESDAY"] = "WEDNESDAY";
  DayOfWeek2["THURSDAY"] = "THURSDAY";
  DayOfWeek2["FRIDAY"] = "FRIDAY";
  DayOfWeek2["SATURDAY"] = "SATURDAY";
  DayOfWeek2["SUNDAY"] = "SUNDAY";
  return DayOfWeek2;
})(DayOfWeek || {});
var ApprovalStatus = /* @__PURE__ */ ((ApprovalStatus2) => {
  ApprovalStatus2["UNDEFINED"] = "UNDEFINED";
  ApprovalStatus2["PENDING"] = "PENDING";
  ApprovalStatus2["APPROVED"] = "APPROVED";
  ApprovalStatus2["DECLINED"] = "DECLINED";
  return ApprovalStatus2;
})(ApprovalStatus || {});
var ScheduleStatus = /* @__PURE__ */ ((ScheduleStatus2) => {
  ScheduleStatus2["UNDEFINED"] = "UNDEFINED";
  ScheduleStatus2["CREATED"] = "CREATED";
  ScheduleStatus2["CANCELLED"] = "CANCELLED";
  return ScheduleStatus2;
})(ScheduleStatus || {});
var ConferenceType = /* @__PURE__ */ ((ConferenceType2) => {
  ConferenceType2["UNDEFINED"] = "UNDEFINED";
  ConferenceType2["ONLINE_MEETING_PROVIDER"] = "ONLINE_MEETING_PROVIDER";
  ConferenceType2["CUSTOM"] = "CUSTOM";
  return ConferenceType2;
})(ConferenceType || {});
var SessionStatus = /* @__PURE__ */ ((SessionStatus2) => {
  SessionStatus2["UNDEFINED"] = "UNDEFINED";
  SessionStatus2["CONFIRMED"] = "CONFIRMED";
  SessionStatus2["CANCELLED"] = "CANCELLED";
  return SessionStatus2;
})(SessionStatus || {});
var SessionType = /* @__PURE__ */ ((SessionType2) => {
  SessionType2["UNDEFINED"] = "UNDEFINED";
  SessionType2["EVENT"] = "EVENT";
  SessionType2["WORKING_HOURS"] = "WORKING_HOURS";
  SessionType2["TIME_AVAILABILITY"] = "TIME_AVAILABILITY";
  SessionType2["AVAILABILITY"] = "AVAILABILITY";
  return SessionType2;
})(SessionType || {});
async function listProviders2() {
  const { httpClient, sideEffects } = arguments[0];
  const payload = renameKeysFromSDKRequestToRESTRequest({});
  const reqOpts = listProviders(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {},
        singleArgumentUnchanged: false
      },
      []
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getConnection2(connectionId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    connectionId
  });
  const reqOpts = getConnection(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { connectionId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["connectionId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function listConnections2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    scheduleIds: options?.scheduleIds,
    partialFailure: options?.partialFailure
  });
  const reqOpts = listConnections(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          scheduleIds: "$[0].scheduleIds",
          partialFailure: "$[0].partialFailure"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function connectByOAuth2(providerId, scheduleId, redirectUrl) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    providerId,
    scheduleId,
    redirectUrl
  });
  const reqOpts = connectByOAuth(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          providerId: "$[0]",
          scheduleId: "$[1]",
          redirectUrl: "$[2]"
        },
        singleArgumentUnchanged: false
      },
      ["providerId", "scheduleId", "redirectUrl"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function connectByCredentials2(providerId, scheduleId, email, password) {
  const { httpClient, sideEffects } = arguments[4];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    providerId,
    scheduleId,
    email,
    password
  });
  const reqOpts = connectByCredentials(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          providerId: "$[0]",
          scheduleId: "$[1]",
          email: "$[2]",
          password: "$[3]"
        },
        singleArgumentUnchanged: false
      },
      ["providerId", "scheduleId", "email", "password"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function listCalendars2(connectionId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    connectionId
  });
  const reqOpts = listCalendars(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { connectionId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["connectionId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateSyncConfig2(connectionId, syncConfig) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    connectionId,
    syncConfig
  });
  const reqOpts = updateSyncConfig(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { connectionId: "$[0]", syncConfig: "$[1]" },
        singleArgumentUnchanged: false
      },
      ["connectionId", "syncConfig"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function disconnect2(connectionId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    connectionId
  });
  const reqOpts = disconnect(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { connectionId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["connectionId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function listEvents2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    from: options?.from,
    to: options?.to,
    scheduleIds: options?.scheduleIds,
    userIds: options?.userIds,
    allDay: options?.allDay,
    fieldsets: options?.fieldsets,
    cursorPaging: options?.cursorPaging,
    partialFailure: options?.partialFailure
  });
  const reqOpts = listEvents(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          from: "$[0].from",
          to: "$[0].to",
          scheduleIds: "$[0].scheduleIds",
          userIds: "$[0].userIds",
          allDay: "$[0].allDay",
          fieldsets: "$[0].fieldsets",
          cursorPaging: "$[0].cursorPaging",
          partialFailure: "$[0].partialFailure"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
export {
  ApprovalStatus,
  CalendarType,
  ConferenceType,
  ConnectMethod,
  Day,
  DayOfWeek,
  ErrorReason,
  ListEventFromCalendars,
  LocationStatus,
  LocationType,
  LocationsLocationType,
  RecurringIntervalType,
  ScheduleStatus,
  SessionStatus,
  SessionType,
  Status,
  SyncToCalendar,
  SyncToErrorReason,
  Transparency,
  connectByCredentials2 as connectByCredentials,
  connectByOAuth2 as connectByOAuth,
  disconnect2 as disconnect,
  getConnection2 as getConnection,
  listCalendars2 as listCalendars,
  listConnections2 as listConnections,
  listEvents2 as listEvents,
  listProviders2 as listProviders,
  updateSyncConfig2 as updateSyncConfig
};
//# sourceMappingURL=index.typings.mjs.map