"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  countResourceTypes: () => countResourceTypes2,
  createResourceType: () => createResourceType2,
  deleteResourceType: () => deleteResourceType2,
  getResourceType: () => getResourceType2,
  queryResourceTypes: () => queryResourceTypes2,
  updateResourceType: () => updateResourceType2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-resources-v2-resource-type-resource-types.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      },
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/resource-types",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/resources/resource-types",
        destPath: "/v2/resources/resource-types"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_resource-types";
function createResourceType(payload) {
  function __createResourceType({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createResourceType;
}
function getResourceType(payload) {
  function __getResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "GET",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getResourceType;
}
function updateResourceType(payload) {
  function __updateResourceType({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "resourceType.createdDate" },
          { path: "resourceType.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "PATCH",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceType.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceType.createdDate" },
            { path: "resourceType.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateResourceType;
}
function deleteResourceType(payload) {
  function __deleteResourceType({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "DELETE",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/{resourceTypeId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteResourceType;
}
function queryResourceTypes(payload) {
  function __queryResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "resourceTypes.createdDate" },
            { path: "resourceTypes.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryResourceTypes;
}
function countResourceTypes(payload) {
  function __countResourceTypes({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.resources.v2.resource_type",
      method: "POST",
      methodFqn: "wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({
        protoPath: "/v2/resources/resource-types/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countResourceTypes;
}

// src/bookings-resources-v2-resource-type-resource-types.meta.ts
function createResourceType2() {
  const payload = {};
  const getRequestOptions = createResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/resource-types",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getResourceType2() {
  const payload = { resourceTypeId: ":resourceTypeId" };
  const getRequestOptions = getResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/resources/resource-types/{resourceTypeId}",
    pathParams: { resourceTypeId: "resourceTypeId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateResourceType2() {
  const payload = { resourceType: { id: ":resourceTypeId" } };
  const getRequestOptions = updateResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v2/resources/resource-types/{resourceType.id}",
    pathParams: { resourceTypeId: "resourceTypeId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteResourceType2() {
  const payload = { resourceTypeId: ":resourceTypeId" };
  const getRequestOptions = deleteResourceType(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v2/resources/resource-types/{resourceTypeId}",
    pathParams: { resourceTypeId: "resourceTypeId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryResourceTypes2() {
  const payload = {};
  const getRequestOptions = queryResourceTypes(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/resource-types/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countResourceTypes2() {
  const payload = {};
  const getRequestOptions = countResourceTypes(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/resources/resource-types/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  countResourceTypes,
  createResourceType,
  deleteResourceType,
  getResourceType,
  queryResourceTypes,
  updateResourceType
});
//# sourceMappingURL=meta.js.map