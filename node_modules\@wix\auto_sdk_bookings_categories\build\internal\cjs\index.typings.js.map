{"version": 3, "sources": ["../../../index.typings.ts", "../../../src/bookings-v1-category-categories.universal.ts", "../../../src/bookings-v1-category-categories.http.ts"], "sourcesContent": ["export * from './src/bookings-v1-category-categories.universal.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsV1Category from './bookings-v1-category-categories.http.js';\n\n/** Categories are used to group multiple services together. A service must be associated with a category in order to be exposed in the Wix Bookings UI. */\nexport interface Category {\n  /**\n   * Category ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Category name.\n   * @maxLength 500\n   */\n  name?: string | null;\n  /**\n   * @internal\n   * @internal\n   * @readonly\n   */\n  status?: StatusWithLiterals;\n  /**\n   * @internal\n   * @internal */\n  sortOrder?: number | null;\n}\n\nexport enum Status {\n  /** The category was created. */\n  CREATED = 'CREATED',\n  /** The category was deleted. */\n  DELETED = 'DELETED',\n}\n\n/** @enumType */\nexport type StatusWithLiterals = Status | 'CREATED' | 'DELETED';\n\nexport interface ListCategoryRequest {\n  /**\n   * IDs of the categories to retrieve.\n   *\n   * Default: All categories are retrieved.\n   * @format GUID\n   */\n  categoryIds?: string[];\n  /**\n   * @internal\n   * @internal */\n  includeDeleted?: boolean | null;\n}\n\nexport interface ListCategoryResponse {\n  /** Retrieved categories. */\n  categories?: Category[];\n}\n\n/** An event sent every time a category entity is changed. */\nexport interface CategoryNotification {\n  category?: Category;\n  event?: EventWithLiterals;\n}\n\nexport enum Event {\n  /** Category was updated. */\n  Updated = 'Updated',\n  /** Category was deleted. */\n  Deleted = 'Deleted',\n  /** Category was created. */\n  Created = 'Created',\n}\n\n/** @enumType */\nexport type EventWithLiterals = Event | 'Updated' | 'Deleted' | 'Created';\n\nexport interface CreateCategoryRequest {\n  /** Category to create. */\n  category: Category;\n}\n\nexport interface CreateCategoryResponse {\n  /** Created category. */\n  category?: Category;\n}\n\nexport interface BatchCreateCategoryRequest {\n  /** Categories to create. */\n  categories?: Category[];\n}\n\nexport interface BatchCreateCategoryResponse {\n  /** Created categories. */\n  categories?: Category[];\n}\n\nexport interface UpdateCategoryRequest {\n  /** Category to update. */\n  category: Category;\n}\n\nexport interface UpdateCategoryResponse {\n  /** Updated category. */\n  category?: Category;\n}\n\nexport interface DeleteCategoryRequest {\n  /**\n   * ID of the category to delete.\n   * @format GUID\n   */\n  _id: string | null;\n  /**\n   * Whether to delete all the services associated with the category.\n   *\n   * Default: `false`\n   */\n  deleteServices?: boolean | null;\n}\n\nexport interface DeleteCategoryResponse {\n  /**\n   * ID of the deleted category.\n   * @format GUID\n   */\n  _id?: string | null;\n}\n\nexport interface BatchDeleteCategoryRequest {\n  /**\n   * IDs of the categories to delete.\n   * @format GUID\n   */\n  ids?: string[] | null;\n}\n\nexport interface BatchDeleteCategoryResponse {}\n\nexport interface BatchUpdateCategoryRequest {\n  /** List of categories to be updated. */\n  categories?: Category[];\n  /** Field mask of fields to update. */\n  fieldMask?: string[];\n}\n\nexport interface BatchUpdateCategoryResponse {}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n/** @docsIgnore */\nexport type UpdateCategoryApplicationErrors = {\n  code?: 'NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface CategoryNotificationEnvelope {\n  data: CategoryNotification;\n  metadata: BaseEventMetadata;\n}\n\n/** @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.CATEGORIES_READ\n * @webhook\n * @eventType com.wixpress.bookings.services.api.v1.CategoryNotification\n * @serviceIdentifier com.wixpress.bookings.services.api.v1.CategoriesService\n * @slug category_notification\n * @documentationMaturity preview\n * @deprecated\n */\nexport declare function onCategoryNotification(\n  handler: (event: CategoryNotificationEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Retrieves all categories.\n * @public\n * @documentationMaturity preview\n * @param options - Options to use when listing categories.\n * @permissionId BOOKINGS.CATEGORIES_READ\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.services.api.v1.CategoriesService.List\n * @deprecated\n */\nexport async function listCategories(\n  options?: ListCategoriesOptions\n): Promise<\n  NonNullablePaths<\n    ListCategoryResponse,\n    `categories` | `categories.${number}.status`,\n    4\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    categoryIds: options?.categoryIds,\n    includeDeleted: options?.includeDeleted,\n  });\n\n  const reqOpts = ambassadorWixBookingsV1Category.list(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          categoryIds: '$[0].categoryIds',\n          includeDeleted: '$[0].includeDeleted',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListCategoriesOptions {\n  /**\n   * IDs of the categories to retrieve.\n   *\n   * Default: All categories are retrieved.\n   * @format GUID\n   */\n  categoryIds?: string[];\n  /** @internal */\n  includeDeleted?: boolean | null;\n}\n\n/**\n * Creates a category.\n * @param category - Category to create.\n * @public\n * @documentationMaturity preview\n * @requiredField category\n * @requiredField category.name\n * @permissionId BOOKINGS.MANAGE_CATEGORIES\n * @applicableIdentity APP\n * @returns Created category.\n * @fqn com.wixpress.bookings.services.api.v1.CategoriesService.Create\n * @deprecated\n */\nexport async function createCategory(\n  category: NonNullablePaths<Category, `name`, 2>\n): Promise<NonNullablePaths<Category, `status`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ category: category });\n\n  const reqOpts = ambassadorWixBookingsV1Category.create(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { category: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['category']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates a category.\n *\n *\n * Each time the category is updated, revision increments by 1. You must include\n * the number of the existing revision when updating the category. This ensures\n * you're working with the latest service information and prevents unintended\n * overwrites.\n * @param _id - Category ID.\n * @public\n * @documentationMaturity preview\n * @requiredField _id\n * @requiredField category\n * @requiredField category.name\n * @permissionId BOOKINGS.MANAGE_CATEGORIES\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.services.api.v1.CategoriesService.Update\n * @deprecated\n */\nexport async function updateCategory(\n  _id: string,\n  category: NonNullablePaths<UpdateCategory, `name`, 2>\n): Promise<\n  NonNullablePaths<UpdateCategoryResponse, `category.status`, 3> & {\n    __applicationErrorsType?: UpdateCategoryApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    category: { ...category, id: _id },\n  });\n\n  const reqOpts = ambassadorWixBookingsV1Category.update(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { category: '$[1]' },\n        explicitPathsToArguments: { 'category.id': '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'category']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateCategory {\n  /**\n   * Category ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Category name.\n   * @maxLength 500\n   */\n  name?: string | null;\n  /**\n   * @internal\n   * @readonly\n   */\n  status?: StatusWithLiterals;\n  /** @internal */\n  sortOrder?: number | null;\n}\n\n/**\n * Deletes a category.\n *\n *\n * You can specify `{\"deleteServices\": true}` to also delete all associated\n * services. Learn more about *deleting a service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service)).\n * @param _id - ID of the category to delete.\n * @public\n * @documentationMaturity preview\n * @requiredField _id\n * @permissionId BOOKINGS.MANAGE_CATEGORIES\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.services.api.v1.CategoriesService._delete\n * @deprecated\n */\nexport async function deleteCategory(\n  _id: string,\n  options?: DeleteCategoryOptions\n): Promise<DeleteCategoryResponse> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    id: _id,\n    deleteServices: options?.deleteServices,\n  });\n\n  const reqOpts = ambassadorWixBookingsV1Category._delete(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          id: '$[0]',\n          deleteServices: '$[1].deleteServices',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DeleteCategoryOptions {\n  /**\n   * Whether to delete all the services associated with the category.\n   *\n   * Default: `false`\n   */\n  deleteServices?: boolean | null;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/bookings/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings/v1/categories',\n        destPath: '/v1/categories',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/categories-proxy',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n    'bookings._base_domain_': [\n      {\n        srcPath: '/_api/services-server/v1/categories',\n        destPath: '/v1/categories',\n      },\n      {\n        srcPath: '/_api/services-server/v1/batch/categories',\n        destPath: '/v1/batch/categories',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_categories';\n\n/**\n * Retrieves all categories.\n * @deprecated\n */\nexport function list(payload: object): RequestOptionsFactory<any> {\n  function __list({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'GET' as any,\n      methodFqn: 'com.wixpress.bookings.services.api.v1.CategoriesService.List',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __list;\n}\n\n/**\n * Creates a category.\n * @deprecated\n */\nexport function create(payload: object): RequestOptionsFactory<any> {\n  function __create({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.services.api.v1.CategoriesService.Create',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __create;\n}\n\n/**\n * Updates a category.\n *\n *\n * Each time the category is updated, revision increments by 1. You must include\n * the number of the existing revision when updating the category. This ensures\n * you're working with the latest service information and prevents unintended\n * overwrites.\n * @deprecated\n */\nexport function update(payload: object): RequestOptionsFactory<any> {\n  function __update({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'PUT' as any,\n      methodFqn:\n        'com.wixpress.bookings.services.api.v1.CategoriesService.Update',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories/{category.id}',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __update;\n}\n\n/**\n * Deletes a category.\n *\n *\n * You can specify `{\"deleteServices\": true}` to also delete all associated\n * services. Learn more about *deleting a service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service)).\n * @deprecated\n */\nexport function _delete(payload: object): RequestOptionsFactory<any> {\n  function ___delete({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.v1.category',\n      method: 'DELETE' as any,\n      methodFqn:\n        'com.wixpress.bookings.services.api.v1.CategoriesService._delete',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({\n        protoPath: '/v1/categories/{id}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return ___delete;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,oCAGO;;;ACJP,0BAAkC;AAClC,IAAAA,uBAA2B;AAI3B,SAAS,4DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAMd,SAAS,KAAK,SAA6C;AAChE,WAAS,OAAO,EAAE,KAAK,GAAQ;AAC7B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,OAAO,SAA6C;AAClE,WAAS,SAAS,EAAE,KAAK,GAAQ;AAC/B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAYO,SAAS,OAAO,SAA6C;AAClE,WAAS,SAAS,EAAE,KAAK,GAAQ;AAC/B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,QAAQ,SAA6C;AACnE,WAAS,UAAU,EAAE,KAAK,GAAQ;AAChC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,4DAA4D;AAAA,QAC/D,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADjLO,IAAK,SAAL,kBAAKC,YAAL;AAEL,EAAAA,QAAA,aAAU;AAEV,EAAAA,QAAA,aAAU;AAJA,SAAAA;AAAA,GAAA;AAmCL,IAAK,QAAL,kBAAKC,WAAL;AAEL,EAAAA,OAAA,aAAU;AAEV,EAAAA,OAAA,aAAU;AAEV,EAAAA,OAAA,aAAU;AANA,SAAAA;AAAA,GAAA;AAqJL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AA+EZ,eAAsB,eACpB,SAOA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,aAAa,SAAS;AAAA,IACtB,gBAAgB,SAAS;AAAA,EAC3B,CAAC;AAED,QAAM,UAA0C,KAAK,OAAO;AAE5D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,aAAa;AAAA,UACb,gBAAgB;AAAA,QAClB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA2BA,eAAsB,eACpB,UACkD;AAElD,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC,EAAE,SAAmB,CAAC;AAE5E,QAAM,UAA0C,OAAO,OAAO;AAE9D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI,GAAG;AAAA,EAC/D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,UAAU,OAAO;AAAA,QAC7C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU;AAAA,IACb;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAqBA,eAAsB,eACpB,KACA,UAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,UAAU,EAAE,GAAG,UAAU,IAAI,IAAI;AAAA,EACnC,CAAC;AAED,QAAM,UAA0C,OAAO,OAAO;AAE9D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,UAAU,OAAO;AAAA,QAC3C,0BAA0B,EAAE,eAAe,OAAO;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,UAAU;AAAA,IACpB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAuCA,eAAsB,eACpB,KACA,SACiC;AAEjC,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,IAAI;AAAA,IACJ,gBAAgB,SAAS;AAAA,EAC3B,CAAC;AAED,QAAM,UAA0C,QAAQ,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAA;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,IAAI;AAAA,UACJ,gBAAgB;AAAA,QAClB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,SAAS;AAAA,IACnB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;", "names": ["import_rest_modules", "Status", "Event", "WebhookIdentityType", "sdkTransformError"]}