"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  countExtendedBookings: () => countExtendedBookings2,
  query: () => query2,
  queryExtendedBookings: () => queryExtendedBookings2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-reader-v2-extended-booking-extended-bookings.http.ts
var import_float = require("@wix/sdk-runtime/transformations/float");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsReaderV2BookingsReaderUrl(opts) {
  const domainToMappings = {
    _: [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "editor._base_domain": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      },
      {
        srcPath: "/bookings/bookings-reader",
        destPath: ""
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings-reader",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_extended-bookings";
function query(payload) {
  function __query({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.reader.v2.extended_booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.reader.v2.BookingsReader.Query",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({
        protoPath: "/v2/extended-booking/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "extendedBookings.booking.createdDate" },
            { path: "extendedBookings.booking.startDate" },
            { path: "extendedBookings.booking.endDate" },
            { path: "extendedBookings.booking.updatedDate" },
            { path: "extendedBookings.booking.canceledDate" },
            { path: "extendedBookings.transactions.payments.createdDate" },
            { path: "extendedBookings.transactions.payments.updatedDate" },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate"
            },
            { path: "extendedBookings.transactions.refunds.createdDate" }
          ]
        },
        {
          transformFn: import_float.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __query;
}
function queryExtendedBookings(payload) {
  function __queryExtendedBookings({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.reader.v2.extended_booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.reader.v2.BookingsReader.QueryExtendedBookings",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({
        protoPath: "/v2/extended-bookings/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "extendedBookings.booking.createdDate" },
            { path: "extendedBookings.booking.startDate" },
            { path: "extendedBookings.booking.endDate" },
            { path: "extendedBookings.booking.updatedDate" },
            { path: "extendedBookings.booking.canceledDate" },
            { path: "extendedBookings.transactions.payments.createdDate" },
            { path: "extendedBookings.transactions.payments.updatedDate" },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate"
            },
            {
              path: "extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate"
            },
            { path: "extendedBookings.transactions.refunds.createdDate" }
          ]
        },
        {
          transformFn: import_float.transformRESTFloatToSDKFloat,
          paths: [
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.latitude"
            },
            {
              path: "extendedBookings.booking.contactDetails.fullAddress.geocode.longitude"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryExtendedBookings;
}
function countExtendedBookings(payload) {
  function __countExtendedBookings({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.reader.v2.extended_booking",
      method: "POST",
      methodFqn: "com.wixpress.bookings.reader.v2.BookingsReader.CountExtendedBookings",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({
        protoPath: "/v2/extended-bookings/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countExtendedBookings;
}

// src/bookings-reader-v2-extended-booking-extended-bookings.meta.ts
function query2() {
  const payload = {};
  const getRequestOptions = query(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/extended-booking/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryExtendedBookings2() {
  const payload = {};
  const getRequestOptions = queryExtendedBookings(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/extended-bookings/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countExtendedBookings2() {
  const payload = {};
  const getRequestOptions = countExtendedBookings(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/extended-bookings/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  countExtendedBookings,
  query,
  queryExtendedBookings
});
//# sourceMappingURL=meta.js.map