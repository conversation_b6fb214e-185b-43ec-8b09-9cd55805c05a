{"name": "@wix/auto_sdk_bookings_extended-bookings", "version": "1.0.48", "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "keywords": [], "sideEffects": false, "main": "./build/cjs/index.js", "types": "./build/cjs/index.d.ts", "exports": {".": {"import": "./build/es/index.mjs", "require": "./build/cjs/index.js", "types": "./build/es/index.d.mts"}, "./package.json": "./package.json", "./meta": {"import": "./build/es/meta.mjs", "require": "./build/cjs/meta.js", "types": "./build/es/meta.d.mts"}}, "files": ["build", "meta", "service-plugins"], "dependencies": {"@wix/sdk-runtime": "^0.3.55", "@wix/sdk-types": "^1.13.35"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.3.2"}, "scripts": {"build": "tsup", "test": ":"}, "wix": {"artifact": {"artifactId": "auto-sdk-bookings-extended-bookings", "groupId": "com.wixpress.public-sdk-dependencies"}, "sdkDependency": {"fqdnNamespace": "extendedBookings", "fqdn": "wix.bookings.reader.v2.extended_booking"}}, "falconPackageHash": "c04908e293245e4ca6ede6a25bd823a7171d4567dd8511c8879cc09e"}