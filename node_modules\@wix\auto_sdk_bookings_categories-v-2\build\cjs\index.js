"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.ts
var index_exports = {};
__export(index_exports, {
  Position: () => Position,
  SortOrder: () => SortOrder,
  WebhookIdentityType: () => WebhookIdentityType,
  countCategories: () => countCategories4,
  createCategory: () => createCategory4,
  deleteCategory: () => deleteCategory4,
  getCategory: () => getCategory4,
  moveCategory: () => moveCategory4,
  onCategoryCreated: () => onCategoryCreated2,
  onCategoryDeleted: () => onCategoryDeleted2,
  onCategoryUpdated: () => onCategoryUpdated2,
  queryCategories: () => queryCategories4,
  updateCategory: () => updateCategory4
});
module.exports = __toCommonJS(index_exports);

// src/bookings-categories-v2-category-categories-v-2.public.ts
var import_rename_all_nested_keys2 = require("@wix/sdk-runtime/rename-all-nested-keys");
var import_timestamp3 = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths3 = require("@wix/sdk-runtime/transformations/transform-paths");
var import_sdk_types = require("@wix/sdk-types");

// src/bookings-categories-v2-category-categories-v-2.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_query_builder = require("@wix/sdk-runtime/query-builder");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-categories-v2-category-categories-v-2.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsCategoriesV2CategoriesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/categories",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v2/categories",
        destPath: "/v2/categories"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_categories-v-2";
function createCategory(payload) {
  function __createCategory({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "category.createdDate" },
          { path: "category.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.CreateCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createCategory;
}
function getCategory(payload) {
  function __getCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "GET",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.GetCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{categoryId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getCategory;
}
function updateCategory(payload) {
  function __updateCategory({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "category.createdDate" },
          { path: "category.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "PATCH",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.UpdateCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{category.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateCategory;
}
function deleteCategory(payload) {
  function __deleteCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "DELETE",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.DeleteCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/{categoryId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteCategory;
}
function queryCategories(payload) {
  function __queryCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.QueryCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "categories.createdDate" },
            { path: "categories.updatedDate" }
          ]
        }
      ]),
      fallback: [
        {
          method: "POST",
          url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
            protoPath: "/v2/categories/query",
            data: payload,
            host
          }),
          data: payload
        }
      ]
    };
    return metadata;
  }
  return __queryCategories;
}
function countCategories(payload) {
  function __countCategories({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.CountCategories",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countCategories;
}
function moveCategory(payload) {
  function __moveCategory({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.categories.v2.category",
      method: "POST",
      methodFqn: "wix.bookings.categories.v2.CategoriesService.MoveCategory",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsCategoriesV2CategoriesServiceUrl({
        protoPath: "/v2/categories/set-position/{categoryId}",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "category.createdDate" },
            { path: "category.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __moveCategory;
}

// src/bookings-categories-v2-category-categories-v-2.universal.ts
var import_transform_paths2 = require("@wix/sdk-runtime/transformations/transform-paths");
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var Position = /* @__PURE__ */ ((Position2) => {
  Position2["UNKNOWN_POSITION"] = "UNKNOWN_POSITION";
  Position2["LAST"] = "LAST";
  Position2["FIRST"] = "FIRST";
  Position2["AFTER_CATEGORY"] = "AFTER_CATEGORY";
  return Position2;
})(Position || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createCategory2(category) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ category });
  const reqOpts = createCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.category;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { category: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getCategory2(categoryId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    categoryId
  });
  const reqOpts = getCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.category;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { categoryId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["categoryId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateCategory2(_id, category) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    category: { ...category, id: _id }
  });
  const reqOpts = updateCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data)?.category;
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: { category: "$[1]" },
        explicitPathsToArguments: { "category.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteCategory2(categoryId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    categoryId
  });
  const reqOpts = deleteCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { categoryId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["categoryId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryCategories2() {
  const { httpClient, sideEffects } = arguments[0];
  return (0, import_query_builder.queryBuilder)({
    func: async (payload) => {
      const reqOpts = queryCategories(payload);
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, {}];
      return (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({ data }) => {
      const transformedData = (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(
        (0, import_transform_paths2.transformPaths)(data, [])
      );
      return {
        items: transformedData?.categories,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = (0, import_transform_error.transformError)(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countCategories2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    filter: options?.filter
  });
  const reqOpts = countCategories(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function moveCategory2(categoryId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    categoryId,
    position: options?.position,
    afterCategoryId: options?.afterCategoryId
  });
  const reqOpts = moveCategory(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          categoryId: "$[0]",
          position: "$[1].position",
          afterCategoryId: "$[1].afterCategoryId"
        },
        singleArgumentUnchanged: false
      },
      ["categoryId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-categories-v2-category-categories-v-2.public.ts
function createCategory3(httpClient) {
  return (category) => createCategory2(
    category,
    // @ts-ignore
    { httpClient }
  );
}
function getCategory3(httpClient) {
  return (categoryId) => getCategory2(
    categoryId,
    // @ts-ignore
    { httpClient }
  );
}
function updateCategory3(httpClient) {
  return (_id, category) => updateCategory2(
    _id,
    category,
    // @ts-ignore
    { httpClient }
  );
}
function deleteCategory3(httpClient) {
  return (categoryId) => deleteCategory2(
    categoryId,
    // @ts-ignore
    { httpClient }
  );
}
function queryCategories3(httpClient) {
  return () => queryCategories2(
    // @ts-ignore
    { httpClient }
  );
}
function countCategories3(httpClient) {
  return (options) => countCategories2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function moveCategory3(httpClient) {
  return (categoryId, options) => moveCategory2(
    categoryId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onCategoryCreated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.categories.v2.category_created",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onCategoryDeleted = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.categories.v2.category_deleted",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();
var onCategoryUpdated = (0, import_sdk_types.EventDefinition)(
  "wix.bookings.categories.v2.category_updated",
  true,
  (event) => (0, import_rename_all_nested_keys2.renameKeysFromRESTResponseToSDKResponse)(
    (0, import_transform_paths3.transformPaths)(event, [
      {
        transformFn: import_timestamp3.transformRESTTimestampToSDKTimestamp,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      }
    ])
  )
)();

// src/bookings-categories-v2-category-categories-v-2.context.ts
var import_rest_modules3 = require("@wix/sdk-runtime/rest-modules");
var import_event_definition_modules = require("@wix/sdk-runtime/event-definition-modules");
var createCategory4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(createCategory3);
var getCategory4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(getCategory3);
var updateCategory4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(updateCategory3);
var deleteCategory4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(deleteCategory3);
var queryCategories4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(queryCategories3);
var countCategories4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(countCategories3);
var moveCategory4 = /* @__PURE__ */ (0, import_rest_modules3.createRESTModule)(moveCategory3);
var onCategoryCreated2 = (0, import_event_definition_modules.createEventModule)(onCategoryCreated);
var onCategoryDeleted2 = (0, import_event_definition_modules.createEventModule)(onCategoryDeleted);
var onCategoryUpdated2 = (0, import_event_definition_modules.createEventModule)(onCategoryUpdated);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Position,
  SortOrder,
  WebhookIdentityType,
  countCategories,
  createCategory,
  deleteCategory,
  getCategory,
  moveCategory,
  onCategoryCreated,
  onCategoryDeleted,
  onCategoryUpdated,
  queryCategories,
  updateCategory
});
//# sourceMappingURL=index.js.map