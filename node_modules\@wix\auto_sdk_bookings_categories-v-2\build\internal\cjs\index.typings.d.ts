import { NonNullablePaths } from '@wix/sdk-types';

/** Categories organize services ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) by controlling the order in which customers see services on the live site and business owners see them in the dashboard. */
interface Category {
    /**
     * Category ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the category is updated.
     * To prevent conflicting changes, you must specify the current revision when updating the category.
     *
     * Ignored when creating a category.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the category was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the Category was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Category name.
     * @minLength 1
     * @maxLength 100
     */
    name?: string | null;
    /**
     * Defines the category's position in the categories list relative to other categories.
     * Wix Bookings assigns `sortOrder` values with large gaps between adjacent categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new `sortOrder` values to restore larger gaps.
     * @readonly
     */
    sortOrder?: number | null;
    /**
     * Custom field data for the category object.
     *
     * [Extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-schema-plugin-extensions) must be configured in the app dashboard before they can be accessed with API calls.
     */
    extendedFields?: ExtendedFields;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateCategoryRequest {
    /** Category to create. */
    category: Category;
}
interface CreateCategoryResponse {
    /** Created category. */
    category?: Category;
}
interface GetCategoryRequest {
    /**
     * ID of the category to retrieve.
     * @format GUID
     */
    categoryId: string;
}
interface GetCategoryResponse {
    /** Retrieved category. */
    category?: Category;
}
interface UpdateCategoryRequest {
    /** Category to update. */
    category: Category;
}
interface UpdateCategoryResponse {
    /** Updated category. */
    category?: Category;
}
interface DeleteCategoryRequest {
    /**
     * ID of the category to delete.
     * @format GUID
     */
    categoryId: string;
}
interface DeleteCategoryResponse {
}
interface QueryCategoriesRequest {
    /** WQL expression. */
    query?: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 5
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryCategoriesResponse {
    /** Retrieved categories. */
    categories?: Category[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Cursor strings that point to the next page, previous page, or both. */
    cursors?: Cursors;
    /**
     * Whether there are more pages to retrieve following the current page.
     *
     * + `true`: Another page of results can be retrieved.
     * + `false`: This is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface CountCategoriesRequest {
    /** Filter to base the count on. */
    filter?: Record<string, any> | null;
}
interface CountCategoriesResponse {
    /** Number of categories matching the filter. */
    count?: number;
}
interface MoveCategoryRequest {
    /**
     * ID of the category to move.
     * @format GUID
     */
    categoryId: string;
    /** New position of the category. */
    position?: PositionWithLiterals;
    /**
     * ID of the reference category.
     * Specify only for `{"position": "AFTER_CATEGORY"}`.
     * @format GUID
     */
    afterCategoryId?: string | null;
}
declare enum Position {
    UNKNOWN_POSITION = "UNKNOWN_POSITION",
    /** Place the category at the end of the list. */
    LAST = "LAST",
    /** Place the category at the beginning of the list. */
    FIRST = "FIRST",
    /** Place the category immediately after a specific category. */
    AFTER_CATEGORY = "AFTER_CATEGORY"
}
/** @enumType */
type PositionWithLiterals = Position | 'UNKNOWN_POSITION' | 'LAST' | 'FIRST' | 'AFTER_CATEGORY';
interface MoveCategoryResponse {
    /** Updated category. */
    category?: Category;
}
interface ImportCategoriesRequest {
    /**
     * List of categories to import.
     * @minSize 1
     * @maxSize 100
     */
    categories?: Category[];
}
interface ImportCategoriesResponse {
}
interface PublishCategoriesUpdatedRequest {
}
interface PublishCategoriesUpdatedResponse {
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type CreateCategoryApplicationErrors = {
    code?: 'CATEGORY_LIMIT_REACHED';
    description?: string;
    data?: Record<string, any>;
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface CategoryCreatedEnvelope {
    entity: Category;
    metadata: EventMetadata;
}
/**
 * Triggered when a category is created.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.CATEGORY_READ
 * @webhook
 * @eventType wix.bookings.categories.v2.category_created
 * @slug created
 */
declare function onCategoryCreated(handler: (event: CategoryCreatedEnvelope) => void | Promise<void>): void;
interface CategoryDeletedEnvelope {
    entity: Category;
    metadata: EventMetadata;
}
/**
 * Triggered when a category is deleted.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.CATEGORY_READ
 * @webhook
 * @eventType wix.bookings.categories.v2.category_deleted
 * @slug deleted
 */
declare function onCategoryDeleted(handler: (event: CategoryDeletedEnvelope) => void | Promise<void>): void;
interface CategoryUpdatedEnvelope {
    entity: Category;
    metadata: EventMetadata;
}
/**
 * Triggered when a category is updated.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.CATEGORY_READ
 * @webhook
 * @eventType wix.bookings.categories.v2.category_updated
 * @slug updated
 */
declare function onCategoryUpdated(handler: (event: CategoryUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Creates a category.
 * @param category - Category to create.
 * @public
 * @requiredField category
 * @requiredField category.name
 * @permissionId BOOKINGS.CATEGORY_CREATE
 * @applicableIdentity APP
 * @returns Created category.
 * @fqn wix.bookings.categories.v2.CategoriesService.CreateCategory
 */
declare function createCategory(category: NonNullablePaths<Category, `name`, 2>): Promise<Category & {
    __applicationErrorsType?: CreateCategoryApplicationErrors;
}>;
/**
 * Retrieves a category.
 * @param categoryId - ID of the category to retrieve.
 * @public
 * @requiredField categoryId
 * @permissionId BOOKINGS.CATEGORY_READ
 * @applicableIdentity APP
 * @applicableIdentity VISITOR
 * @returns Retrieved category.
 * @fqn wix.bookings.categories.v2.CategoriesService.GetCategory
 */
declare function getCategory(categoryId: string): Promise<Category>;
/**
 * Updates a category.
 *
 *
 * Each time the category is updated, `revision` increments by 1.
 * You must specify the current `revision` to prevent unintended overwrites.
 *
 * You can't adjust a categories `sortOrder` with this method, call Move Category ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/move-category) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/move-category)) instead.
 * @param _id - Category ID.
 * @public
 * @requiredField _id
 * @requiredField category
 * @requiredField category.revision
 * @permissionId BOOKINGS.CATEGORY_UPDATE
 * @applicableIdentity APP
 * @returns Updated category.
 * @fqn wix.bookings.categories.v2.CategoriesService.UpdateCategory
 */
declare function updateCategory(_id: string, category: NonNullablePaths<UpdateCategory, `revision`, 2>): Promise<Category>;
interface UpdateCategory {
    /**
     * Category ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the category is updated.
     * To prevent conflicting changes, you must specify the current revision when updating the category.
     *
     * Ignored when creating a category.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the category was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Date and time the Category was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Category name.
     * @minLength 1
     * @maxLength 100
     */
    name?: string | null;
    /**
     * Defines the category's position in the categories list relative to other categories.
     * Wix Bookings assigns `sortOrder` values with large gaps between adjacent categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new `sortOrder` values to restore larger gaps.
     * @readonly
     */
    sortOrder?: number | null;
    /**
     * Custom field data for the category object.
     *
     * [Extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-schema-plugin-extensions) must be configured in the app dashboard before they can be accessed with API calls.
     */
    extendedFields?: ExtendedFields;
}
/**
 * Deletes a category.
 *
 *
 * ## Impact on connected services
 *
 * When you delete a category, any services linked to it remain associated with the now-deleted category. Wix Bookings still displays these services to business owners in the dashboard, but they aren't visible to customers on the live site.
 * Attempting to call Update Service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service)) for a service that's linked to a deleted category fails, unless you specify a different, existing category ID in `service.category.id`.
 * @param categoryId - ID of the category to delete.
 * @public
 * @requiredField categoryId
 * @permissionId BOOKINGS.CATEGORY_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.categories.v2.CategoriesService.DeleteCategory
 */
declare function deleteCategory(categoryId: string): Promise<void>;
/**
 * Creates a query to retrieve a list of `category` objects.
 *
 * The `queryCategories()` function builds a query to retrieve a list of `category` objects and returns a `categoriesQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-builder/find) function.
 *
 * You can refine the query by chaining `CategoriesQueryBuilder` functions onto the query. `CategoriesQueryBuilder` functions enable you to sort, filter, and control the results that `queryCategories()` returns.
 *
 * `queryCategories()` runs with the following `CategoriesQueryBuilder` defaults that you can override:
 *
 * + `limit` is `100`.
 * + Sorted by `createdDate` in ascending order.
 *
 * The functions that are chained to `queryCategories()` are applied in the order they are called. For example, if you apply `ascending("name")` and then `ascending("id")`, the results are sorted first by `name`, and then, if there are multiple results with the same `name`, the items are sorted by `id`.
 *
 * The following `CategoriesQueryBuilder` functions are supported for the `queryCategories()` function. For a full description of the `category` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/categories-query-result/items) property in `CategoriesQueryResult`.
 * @public
 * @permissionId BOOKINGS.CATEGORY_READ
 * @applicableIdentity APP
 * @applicableIdentity VISITOR
 * @fqn wix.bookings.categories.v2.CategoriesService.QueryCategories
 */
declare function queryCategories(): CategoriesQueryBuilder;
interface QueryCursorResult {
    cursors: Cursors;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface CategoriesQueryResult extends QueryCursorResult {
    items: Category[];
    query: CategoriesQueryBuilder;
    next: () => Promise<CategoriesQueryResult>;
    prev: () => Promise<CategoriesQueryResult>;
}
interface CategoriesQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    eq: (propertyName: '_id' | 'name' | 'sortOrder', value: any) => CategoriesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ne: (propertyName: '_id' | 'name' | 'sortOrder', value: any) => CategoriesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ge: (propertyName: '_id' | 'name' | 'sortOrder', value: any) => CategoriesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    gt: (propertyName: '_id' | 'name' | 'sortOrder', value: any) => CategoriesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    le: (propertyName: '_id' | 'name' | 'sortOrder', value: any) => CategoriesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    lt: (propertyName: '_id' | 'name' | 'sortOrder', value: any) => CategoriesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `string`.
     * @param string - String to compare against. Case-insensitive.
     */
    startsWith: (propertyName: '_id' | 'name', value: string) => CategoriesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     */
    hasSome: (propertyName: '_id' | 'name' | 'sortOrder', value: any[]) => CategoriesQueryBuilder;
    in: (propertyName: '_id' | 'name' | 'sortOrder', value: any) => CategoriesQueryBuilder;
    exists: (propertyName: '_id' | 'name' | 'sortOrder', value: boolean) => CategoriesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    ascending: (...propertyNames: Array<'_id' | 'name' | 'sortOrder'>) => CategoriesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    descending: (...propertyNames: Array<'_id' | 'name' | 'sortOrder'>) => CategoriesQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */
    limit: (limit: number) => CategoriesQueryBuilder;
    /** @param cursor - A pointer to specific record */
    skipTo: (cursor: string) => CategoriesQueryBuilder;
    find: () => Promise<CategoriesQueryResult>;
}
/**
 * Counts categories, given the specified filtering.
 *
 *
 * Refer to the Supported Filters article ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/filtering-and-sorting)) for a complete list of supported filters.
 * @public
 * @permissionId BOOKINGS.CATEGORY_READ
 * @applicableIdentity APP
 * @applicableIdentity VISITOR
 * @fqn wix.bookings.categories.v2.CategoriesService.CountCategories
 */
declare function countCategories(options?: CountCategoriesOptions): Promise<NonNullablePaths<CountCategoriesResponse, `count`, 2>>;
interface CountCategoriesOptions {
    /** Filter to base the count on. */
    filter?: Record<string, any> | null;
}
/**
 * Moves a category to the start, end, or immediately after a specified category by updating its `sortOrder` field.
 *
 * Wix Bookings assigns `sortOrder` values with large gaps between categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new values to restore larger gaps.
 * @param categoryId - ID of the category to move.
 * @public
 * @requiredField categoryId
 * @permissionId BOOKINGS.CATEGORY_MOVE
 * @applicableIdentity APP
 * @fqn wix.bookings.categories.v2.CategoriesService.MoveCategory
 */
declare function moveCategory(categoryId: string, options?: MoveCategoryOptions): Promise<MoveCategoryResponse>;
interface MoveCategoryOptions {
    /** New position of the category. */
    position?: PositionWithLiterals;
    /**
     * ID of the reference category.
     * Specify only for `{"position": "AFTER_CATEGORY"}`.
     * @format GUID
     */
    afterCategoryId?: string | null;
}

export { type ActionEvent, type BaseEventMetadata, type CategoriesQueryBuilder, type CategoriesQueryResult, type Category, type CategoryCreatedEnvelope, type CategoryDeletedEnvelope, type CategoryUpdatedEnvelope, type CountCategoriesOptions, type CountCategoriesRequest, type CountCategoriesResponse, type CreateCategoryApplicationErrors, type CreateCategoryRequest, type CreateCategoryResponse, type CursorPaging, type CursorPagingMetadata, type CursorQuery, type CursorQueryPagingMethodOneOf, type Cursors, type DeleteCategoryRequest, type DeleteCategoryResponse, type DomainEvent, type DomainEventBodyOneOf, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type EventMetadata, type ExtendedFields, type GetCategoryRequest, type GetCategoryResponse, type IdentificationData, type IdentificationDataIdOneOf, type ImportCategoriesRequest, type ImportCategoriesResponse, type MessageEnvelope, type MoveCategoryOptions, type MoveCategoryRequest, type MoveCategoryResponse, Position, type PositionWithLiterals, type PublishCategoriesUpdatedRequest, type PublishCategoriesUpdatedResponse, type QueryCategoriesRequest, type QueryCategoriesResponse, type RestoreInfo, SortOrder, type SortOrderWithLiterals, type Sorting, type UpdateCategory, type UpdateCategoryRequest, type UpdateCategoryResponse, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, countCategories, createCategory, deleteCategory, getCategory, moveCategory, onCategoryCreated, onCategoryDeleted, onCategoryUpdated, queryCategories, updateCategory };
