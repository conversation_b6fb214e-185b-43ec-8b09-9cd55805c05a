import { HttpClient, NonNullablePaths, MaybeContext, BuildRESTFunction } from '@wix/sdk-types';
import { ListBookingFeesByBookingIdsOptions, ListBookingFeesByBookingIdsResponse, ListBookingFeesByBookingIdsApplicationErrors, ApplyBookingFeesToOrderOptions, ApplyBookingFeesToOrderResponse, ApplyBookingFeesToOrderApplicationErrors, CollectAppliedBookingFeesOptions, CollectAppliedBookingFeesResponse, CollectAppliedBookingFeesApplicationErrors } from './index.typings.js';
export { ActionEvent, ApplyBookingFeesToOrderRequest, BookingFee, BookingFeeStatus, BookingFeeStatusWithLiterals, BusinessNotification, CancellationFee, CollectAppliedBookingFeesRequest, CursorPagingMetadata, Cursors, DomainEvent, DomainEventBodyOneOf, EcomOrderInfo, EntityCreatedEvent, EntityDeletedEvent, EntityUpdatedEvent, FailedToApplyBookingFeeToOrder, FailedToCollectAppliedBookingFees, IdentificationData, IdentificationDataIdOneOf, ListBookingFeesByBookingIdsRequest, ListNonPreviewBookingFeesByBookingIdsRequest, ListNonPreviewBookingFeesByBookingIdsResponse, MessageEnvelope, Money, PolicyDetails, Price, RestoreInfo, Trigger, TriggerWithLiterals, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.js';

declare function listBookingFeesByBookingIds$1(httpClient: HttpClient): ListBookingFeesByBookingIdsSignature;
interface ListBookingFeesByBookingIdsSignature {
    /**
     * Retrieves booking fees by booking IDs.
     *
     *
     * Instead of returning `bookingFee` objects with the `cancellationFee.price.value`
     * set to `0.00`, the method doesn't return a `bookingFee` object for the relevant
     * booking IDs. For example, no `bookingFee` object is returned if the canceled
     * booking was free or if the booking was canceled before the start of the earliest
     * cancellation window with an associated fee.
     *
     * If the service's booking policy has been updated since the booking was created,
     * booking fees are calculated according to the *booking policy snapshot*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
     * rather than the current version of the *policy*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).
     *
     * This method calculates the cancellation fee amount based on the time of the
     * call, you can't specify a time. Similarly, it calculates the cancellation fee
     * based on the number of participants who canceled, not a provided number.
     *
     * A `cancellationFee.status` of `PREVIEW` indicates that the booking fee is
     * informational only; the customer isn't required to pay it. When the `status` is
     * set to `UNKNOWN_STATUS` there is no eCommerce order associated with the booking. For
     * example, if a custom checkout was used for the booking instead of the
     * _eCommerce checkout_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     *
     * If multiple events would trigger the calculation of a booking fee, for example
     * when a booking is first canceled and then marked as not attended, Wix calculates
     * the booking fee based on the first trigger. In this example, the booking
     * cancellation.
     * @param - Options to use when listing booking fees.
     */
    (options?: ListBookingFeesByBookingIdsOptions): Promise<NonNullablePaths<ListBookingFeesByBookingIdsResponse, `bookingFees` | `bookingFees.${number}.cancellationFee.price.value` | `bookingFees.${number}.cancellationFee.price.currency` | `bookingFees.${number}.cancellationFee.status` | `bookingFees.${number}.cancellationFee.trigger`, 6> & {
        __applicationErrorsType?: ListBookingFeesByBookingIdsApplicationErrors;
    }>;
}
declare function applyBookingFeesToOrder$1(httpClient: HttpClient): ApplyBookingFeesToOrderSignature;
interface ApplyBookingFeesToOrderSignature {
    /**
     * Applies booking fees to an _eCommerce order_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction)).
     *
     *
     * The booking fees are added as a single additional fee to the eCommerce order.
     * The order's `additionalFee.lineItemIds` array is set to the list of corresponding
     * booking IDs. By default, the `additionalFee.price.amount` is the sum of all
     * booking fee prices. But you may provide a `priceOverride` instead. The override
     * price can't be higher than the sum of all booking fees.
     *
     * Apply Booking Fees to Order also updates the prices of all affected line items
     * in the relevant eCommerce order to zero. After a cancellation fee is applied
     * to an eCommerce order, the cancellation fee's `price.value` is updated to `0.00`
     * and its trigger is set to `UNKNOWN_TRIGGER`. You can retrieve the fee amount
     * from the corresponding `additionalFee` object of the eCommerce order with
     * _Search Orders_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/search-orders) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/search-orders)).
     *
     * If you apply multiple booking fees to an eCommerce order, they either all fail or
     * all succeed together. For example, the call fails if the booking fees are associated
     * with different eCommmerce orders.
     * @param - IDs of the bookings for which to apply booking fees to an eCommerce order.
     * @param - Options to use when applying booking fees to an eCommerce order.
     */
    (bookingIds: string[], options?: NonNullablePaths<ApplyBookingFeesToOrderOptions, `priceOverride.currency` | `priceOverride.value`, 3>): Promise<NonNullablePaths<ApplyBookingFeesToOrderResponse, `bookingFees` | `bookingFees.${number}.cancellationFee.price.value` | `bookingFees.${number}.cancellationFee.price.currency` | `bookingFees.${number}.cancellationFee.status` | `bookingFees.${number}.cancellationFee.trigger` | `ecomOrderInfo.additionalFeePrice.amount` | `ecomOrderInfo.additionalFeePrice.formattedAmount`, 6> & {
        __applicationErrorsType?: ApplyBookingFeesToOrderApplicationErrors;
    }>;
}
declare function collectAppliedBookingFees$1(httpClient: HttpClient): CollectAppliedBookingFeesSignature;
interface CollectAppliedBookingFeesSignature {
    /**
     * Collects booking fees by charging the customer using the payment method that's
     * saved on the corresponding *eCommerce order*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))
     *
     *
     * <blockquote class="warning">
     *
     * __Warning:__
     * Currently, there is no validation that prevents idempotent requests.
     * This means that your code must make sure to not charge customers multiple
     * times for the same booking fee. You could use
     * _List Transactions For Single Order_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/order-transactions/list-transactions-for-single-order) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/order-transactions/list-transactions-for-single-order))
     * to check which charges were made previously for an eCommerce order.
     *
     * </blockquote>
     *
     * An HTTP status of `200` means that all booking fees were successfully collected.
     * Any other HTPP status means that collection failed.
     *
     * Collects the order's `additionalFees.price.amount` that's related to the
     * booking fees. If there are multiple additional fees on the eCommerce order,
     * the amount that's collected differs from `priceSummary.totalAdditionalFees.amount`.
     *
     * Possible failure reasons include:
     * + The order's `status` isn't `APPROVED`.
     * + There is no payment method saved on the order.
     * + The order's `balanceSummary.balance.amount` is lower than the
     * `additionalFees.price.amount` to collect.
     * + The order's `additionalFeeId` doesn't belong to a Wix Bookings cancellation fee.
     * @param - ID of the eCommerce order that includes the booking fees as a single
     * `additionalFee`.
     * @param - Options to use when collecting booking fees that have been applied to an eCommerce order.
     */
    (orderId: string, options?: NonNullablePaths<CollectAppliedBookingFeesOptions, `additionalFeeId`, 2>): Promise<NonNullablePaths<CollectAppliedBookingFeesResponse, `collectedPrice.amount` | `collectedPrice.formattedAmount`, 3> & {
        __applicationErrorsType?: CollectAppliedBookingFeesApplicationErrors;
    }>;
}

declare const listBookingFeesByBookingIds: MaybeContext<BuildRESTFunction<typeof listBookingFeesByBookingIds$1> & typeof listBookingFeesByBookingIds$1>;
declare const applyBookingFeesToOrder: MaybeContext<BuildRESTFunction<typeof applyBookingFeesToOrder$1> & typeof applyBookingFeesToOrder$1>;
declare const collectAppliedBookingFees: MaybeContext<BuildRESTFunction<typeof collectAppliedBookingFees$1> & typeof collectAppliedBookingFees$1>;

export { ApplyBookingFeesToOrderApplicationErrors, ApplyBookingFeesToOrderOptions, ApplyBookingFeesToOrderResponse, CollectAppliedBookingFeesApplicationErrors, CollectAppliedBookingFeesOptions, CollectAppliedBookingFeesResponse, ListBookingFeesByBookingIdsApplicationErrors, ListBookingFeesByBookingIdsOptions, ListBookingFeesByBookingIdsResponse, applyBookingFeesToOrder, collectAppliedBookingFees, listBookingFeesByBookingIds };
