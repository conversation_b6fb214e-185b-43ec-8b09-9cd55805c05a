// src/bookings-staff-v1-staff-member-staff-members.public.ts
import { renameKeysFromRESTResponseToSDKResponse as renameKeysFromRESTResponseToSDKResponse2 } from "@wix/sdk-runtime/rename-all-nested-keys";
import { transformRESTImageToSDKImage as transformRESTImageToSDKImage2 } from "@wix/sdk-runtime/transformations/image";
import { transformRESTTimestampToSDKTimestamp as transformRESTTimestampToSDKTimestamp2 } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths as transformPaths3 } from "@wix/sdk-runtime/transformations/transform-paths";
import { EventDefinition } from "@wix/sdk-types";

// src/bookings-staff-v1-staff-member-staff-members.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import { queryBuilder } from "@wix/sdk-runtime/query-builder";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-staff-v1-staff-member-staff-members.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformRESTFloatToSDKFloat } from "@wix/sdk-runtime/transformations/float";
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformSDKFieldMaskToRESTFieldMask } from "@wix/sdk-runtime/transformations/field-mask";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveWixBookingsStaffV1StaffMembersServiceUrl(opts) {
  const domainToMappings = {
    "www._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/staff-members",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/staff-members",
        destPath: "/v1/staff-members"
      },
      {
        srcPath: "/bookings/v1/bulk/staff-members",
        destPath: "/v1/bulk/staff-members"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_staff-members";
function createStaffMember(payload) {
  function __createStaffMember({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "staffMember.createdDate" },
          { path: "staffMember.updatedDate" },
          { path: "staffMember.mainMedia.image.urlExpirationDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.CreateStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createStaffMember;
}
function getStaffMember(payload) {
  function __getStaffMember({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "GET",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.GetStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getStaffMember;
}
function updateStaffMember(payload) {
  function __updateStaffMember({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      },
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "staffMember.createdDate" },
          { path: "staffMember.updatedDate" },
          { path: "staffMember.mainMedia.image.urlExpirationDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "PATCH",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.UpdateStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMember.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateStaffMember;
}
function deleteStaffMember(payload) {
  function __deleteStaffMember({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "DELETE",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.DeleteStaffMember",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __deleteStaffMember;
}
function queryStaffMembers(payload) {
  function __queryStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.QueryStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMembers.createdDate" },
            { path: "staffMembers.updatedDate" },
            { path: "staffMembers.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryStaffMembers;
}
function countStaffMembers(payload) {
  function __countStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.CountStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countStaffMembers;
}
function connectStaffMemberToUser(payload) {
  function __connectStaffMemberToUser({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.ConnectStaffMemberToUser",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/connect-staff-member-to-user",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __connectStaffMemberToUser;
}
function searchStaffMembers(payload) {
  function __searchStaffMembers({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/search",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMembers.createdDate" },
            { path: "staffMembers.updatedDate" },
            { path: "staffMembers.mainMedia.image.urlExpirationDate" }
          ]
        },
        {
          transformFn: transformRESTFloatToSDKFloat,
          paths: [
            { path: "aggregationData.results.ranges.results.from" },
            { path: "aggregationData.results.ranges.results.to" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from"
            },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.from"
            },
            {
              path: "aggregationData.results.nested.results.results.*.range.to"
            },
            { path: "aggregationData.results.scalar.value" },
            {
              path: "aggregationData.results.groupedByValue.results.nestedResults.scalar.value"
            },
            {
              path: "aggregationData.results.nested.results.results.*.scalar.value"
            }
          ]
        }
      ])
    };
    return metadata;
  }
  return __searchStaffMembers;
}
function disconnectStaffMemberFromUser(payload) {
  function __disconnectStaffMemberFromUser({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.DisconnectStaffMemberFromUser",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/disconnect-staff-member-from-user",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __disconnectStaffMemberFromUser;
}
function assignWorkingHoursSchedule(payload) {
  function __assignWorkingHoursSchedule({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.AssignWorkingHoursSchedule",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/assign-working-hours-schedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __assignWorkingHoursSchedule;
}
function assignCustomSchedule(payload) {
  function __assignCustomSchedule({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.AssignCustomSchedule",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/staff-members/{staffMemberId}/assign-custom-schedule",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "staffMember.createdDate" },
            { path: "staffMember.updatedDate" },
            { path: "staffMember.mainMedia.image.urlExpirationDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __assignCustomSchedule;
}
function bulkUpdateStaffMemberTags(payload) {
  function __bulkUpdateStaffMemberTags({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTags",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/bulk/staff-members/update-tags",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkUpdateStaffMemberTags;
}
function bulkUpdateStaffMemberTagsByFilter(payload) {
  function __bulkUpdateStaffMemberTagsByFilter({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.staff.v1.staff_member",
      method: "POST",
      methodFqn: "wix.bookings.staff.v1.StaffMembersService.BulkUpdateStaffMemberTagsByFilter",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsStaffV1StaffMembersServiceUrl({
        protoPath: "/v1/bulk/staff-members/update-tags-by-filter",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __bulkUpdateStaffMemberTagsByFilter;
}

// src/bookings-staff-v1-staff-member-staff-members.universal.ts
import { transformSDKImageToRESTImage } from "@wix/sdk-runtime/transformations/image";
import { transformRESTImageToSDKImage } from "@wix/sdk-runtime/transformations/image";
import { transformPaths as transformPaths2 } from "@wix/sdk-runtime/transformations/transform-paths";
var IdentityType = /* @__PURE__ */ ((IdentityType2) => {
  IdentityType2["UNKNOWN"] = "UNKNOWN";
  IdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  IdentityType2["MEMBER"] = "MEMBER";
  IdentityType2["WIX_USER"] = "WIX_USER";
  IdentityType2["APP"] = "APP";
  return IdentityType2;
})(IdentityType || {});
var AssociatedWixIdentityConnectionStatusEnumConnectionStatus = /* @__PURE__ */ ((AssociatedWixIdentityConnectionStatusEnumConnectionStatus2) => {
  AssociatedWixIdentityConnectionStatusEnumConnectionStatus2["UNKNOWN"] = "UNKNOWN";
  AssociatedWixIdentityConnectionStatusEnumConnectionStatus2["CONNECTED"] = "CONNECTED";
  AssociatedWixIdentityConnectionStatusEnumConnectionStatus2["PENDING"] = "PENDING";
  AssociatedWixIdentityConnectionStatusEnumConnectionStatus2["EXPIRED"] = "EXPIRED";
  AssociatedWixIdentityConnectionStatusEnumConnectionStatus2["DISCONNECTED"] = "DISCONNECTED";
  return AssociatedWixIdentityConnectionStatusEnumConnectionStatus2;
})(AssociatedWixIdentityConnectionStatusEnumConnectionStatus || {});
var ConnectionStatus = /* @__PURE__ */ ((ConnectionStatus2) => {
  ConnectionStatus2["CONNECTED"] = "CONNECTED";
  ConnectionStatus2["DISCONNECTED"] = "DISCONNECTED";
  return ConnectionStatus2;
})(ConnectionStatus || {});
var RequestedFields = /* @__PURE__ */ ((RequestedFields2) => {
  RequestedFields2["RESOURCE_DETAILS"] = "RESOURCE_DETAILS";
  RequestedFields2["ASSOCIATED_IDENTITY_STATUS"] = "ASSOCIATED_IDENTITY_STATUS";
  return RequestedFields2;
})(RequestedFields || {});
var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "ASC";
  SortOrder2["DESC"] = "DESC";
  return SortOrder2;
})(SortOrder || {});
var Mode = /* @__PURE__ */ ((Mode2) => {
  Mode2["OR"] = "OR";
  Mode2["AND"] = "AND";
  return Mode2;
})(Mode || {});
var ScalarType = /* @__PURE__ */ ((ScalarType2) => {
  ScalarType2["UNKNOWN_SCALAR_TYPE"] = "UNKNOWN_SCALAR_TYPE";
  ScalarType2["COUNT_DISTINCT"] = "COUNT_DISTINCT";
  ScalarType2["MIN"] = "MIN";
  ScalarType2["MAX"] = "MAX";
  return ScalarType2;
})(ScalarType || {});
var AggregationType = /* @__PURE__ */ ((AggregationType2) => {
  AggregationType2["UNKNOWN_AGGREGATION_TYPE"] = "UNKNOWN_AGGREGATION_TYPE";
  AggregationType2["VALUE"] = "VALUE";
  AggregationType2["RANGE"] = "RANGE";
  AggregationType2["SCALAR"] = "SCALAR";
  return AggregationType2;
})(AggregationType || {});
var Day = /* @__PURE__ */ ((Day2) => {
  Day2["UNDEFINED"] = "UNDEFINED";
  Day2["MON"] = "MON";
  Day2["TUE"] = "TUE";
  Day2["WED"] = "WED";
  Day2["THU"] = "THU";
  Day2["FRI"] = "FRI";
  Day2["SAT"] = "SAT";
  Day2["SUN"] = "SUN";
  return Day2;
})(Day || {});
var Transparency = /* @__PURE__ */ ((Transparency2) => {
  Transparency2["UNDEFINED"] = "UNDEFINED";
  Transparency2["FREE"] = "FREE";
  Transparency2["BUSY"] = "BUSY";
  return Transparency2;
})(Transparency || {});
var RecurringIntervalType = /* @__PURE__ */ ((RecurringIntervalType2) => {
  RecurringIntervalType2["UNDEFINED"] = "UNDEFINED";
  RecurringIntervalType2["EVENT"] = "EVENT";
  RecurringIntervalType2["TIME_AVAILABILITY"] = "TIME_AVAILABILITY";
  RecurringIntervalType2["AVAILABILITY"] = "AVAILABILITY";
  return RecurringIntervalType2;
})(RecurringIntervalType || {});
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNDEFINED"] = "UNDEFINED";
  LocationType2["OWNER_BUSINESS"] = "OWNER_BUSINESS";
  LocationType2["OWNER_CUSTOM"] = "OWNER_CUSTOM";
  LocationType2["CUSTOM"] = "CUSTOM";
  return LocationType2;
})(LocationType || {});
var LocationStatus = /* @__PURE__ */ ((LocationStatus2) => {
  LocationStatus2["ACTIVE"] = "ACTIVE";
  LocationStatus2["INACTIVE"] = "INACTIVE";
  return LocationStatus2;
})(LocationStatus || {});
var LocationsLocationType = /* @__PURE__ */ ((LocationsLocationType2) => {
  LocationsLocationType2["UNKNOWN"] = "UNKNOWN";
  LocationsLocationType2["BRANCH"] = "BRANCH";
  LocationsLocationType2["OFFICES"] = "OFFICES";
  LocationsLocationType2["RECEPTION"] = "RECEPTION";
  LocationsLocationType2["HEADQUARTERS"] = "HEADQUARTERS";
  LocationsLocationType2["INVENTORY"] = "INVENTORY";
  return LocationsLocationType2;
})(LocationsLocationType || {});
var DayOfWeek = /* @__PURE__ */ ((DayOfWeek2) => {
  DayOfWeek2["MONDAY"] = "MONDAY";
  DayOfWeek2["TUESDAY"] = "TUESDAY";
  DayOfWeek2["WEDNESDAY"] = "WEDNESDAY";
  DayOfWeek2["THURSDAY"] = "THURSDAY";
  DayOfWeek2["FRIDAY"] = "FRIDAY";
  DayOfWeek2["SATURDAY"] = "SATURDAY";
  DayOfWeek2["SUNDAY"] = "SUNDAY";
  return DayOfWeek2;
})(DayOfWeek || {});
var ApprovalStatus = /* @__PURE__ */ ((ApprovalStatus2) => {
  ApprovalStatus2["UNDEFINED"] = "UNDEFINED";
  ApprovalStatus2["PENDING"] = "PENDING";
  ApprovalStatus2["APPROVED"] = "APPROVED";
  ApprovalStatus2["DECLINED"] = "DECLINED";
  return ApprovalStatus2;
})(ApprovalStatus || {});
var ScheduleStatus = /* @__PURE__ */ ((ScheduleStatus2) => {
  ScheduleStatus2["UNDEFINED"] = "UNDEFINED";
  ScheduleStatus2["CREATED"] = "CREATED";
  ScheduleStatus2["CANCELLED"] = "CANCELLED";
  return ScheduleStatus2;
})(ScheduleStatus || {});
var ConferenceType = /* @__PURE__ */ ((ConferenceType2) => {
  ConferenceType2["UNDEFINED"] = "UNDEFINED";
  ConferenceType2["ONLINE_MEETING_PROVIDER"] = "ONLINE_MEETING_PROVIDER";
  ConferenceType2["CUSTOM"] = "CUSTOM";
  return ConferenceType2;
})(ConferenceType || {});
var CalendarType = /* @__PURE__ */ ((CalendarType2) => {
  CalendarType2["UNDEFINED"] = "UNDEFINED";
  CalendarType2["GOOGLE"] = "GOOGLE";
  CalendarType2["I_CAL"] = "I_CAL";
  CalendarType2["OUTLOOK"] = "OUTLOOK";
  CalendarType2["OFFICE_365"] = "OFFICE_365";
  CalendarType2["MICROSOFT"] = "MICROSOFT";
  CalendarType2["OTHER"] = "OTHER";
  return CalendarType2;
})(CalendarType || {});
var Status = /* @__PURE__ */ ((Status2) => {
  Status2["UNDEFINED"] = "UNDEFINED";
  Status2["CONFIRMED"] = "CONFIRMED";
  Status2["CANCELLED"] = "CANCELLED";
  return Status2;
})(Status || {});
var SessionType = /* @__PURE__ */ ((SessionType2) => {
  SessionType2["UNDEFINED"] = "UNDEFINED";
  SessionType2["EVENT"] = "EVENT";
  SessionType2["WORKING_HOURS"] = "WORKING_HOURS";
  SessionType2["TIME_AVAILABILITY"] = "TIME_AVAILABILITY";
  SessionType2["AVAILABILITY"] = "AVAILABILITY";
  return SessionType2;
})(SessionType || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createStaffMember2(staffMember, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = transformPaths2(
    renameKeysFromSDKRequestToRESTRequest({
      staffMember,
      fields: options?.fields
    }),
    [
      {
        transformFn: transformSDKImageToRESTImage,
        paths: [{ path: "staffMember.mainMedia.image" }]
      }
    ]
  );
  const reqOpts = createStaffMember(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMember.mainMedia.image" }]
        }
      ])
    )?.staffMember;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          staffMember: "$[0]",
          fields: "$[1].fields"
        },
        singleArgumentUnchanged: false
      },
      ["staffMember", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getStaffMember2(staffMemberId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    staffMemberId,
    fields: options?.fields
  });
  const reqOpts = getStaffMember(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMember.mainMedia.image" }]
        }
      ])
    )?.staffMember;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          staffMemberId: "$[0]",
          fields: "$[1].fields"
        },
        singleArgumentUnchanged: false
      },
      ["staffMemberId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateStaffMember2(_id, staffMember, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = transformPaths2(
    renameKeysFromSDKRequestToRESTRequest({
      staffMember: { ...staffMember, id: _id },
      fields: options?.fields
    }),
    [
      {
        transformFn: transformSDKImageToRESTImage,
        paths: [{ path: "staffMember.mainMedia.image" }]
      }
    ]
  );
  const reqOpts = updateStaffMember(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMember.mainMedia.image" }]
        }
      ])
    )?.staffMember;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: { staffMember: "$[1]" },
        explicitPathsToArguments: {
          "staffMember.id": "$[0]",
          fields: "$[2].fields"
        },
        singleArgumentUnchanged: false
      },
      ["_id", "staffMember", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteStaffMember2(staffMemberId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    staffMemberId
  });
  const reqOpts = deleteStaffMember(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { staffMemberId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["staffMemberId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
function queryStaffMembers2(options) {
  const { httpClient, sideEffects } = arguments[1];
  return queryBuilder({
    func: async (payload) => {
      const reqOpts = queryStaffMembers(
        { ...payload, ...options ?? {} }
      );
      sideEffects?.onSiteCall?.();
      try {
        const result = await httpClient.request(reqOpts);
        sideEffects?.onSuccess?.(result);
        return result;
      } catch (err) {
        sideEffects?.onError?.(err);
        throw err;
      }
    },
    requestTransformer: (query) => {
      const args = [query, options];
      return renameKeysFromSDKRequestToRESTRequest({
        ...args?.[1],
        query: args?.[0]
      });
    },
    responseTransformer: ({
      data
    }) => {
      const transformedData = renameKeysFromRESTResponseToSDKResponse(
        transformPaths2(data, [
          {
            transformFn: transformRESTImageToSDKImage,
            paths: [{ path: "staffMembers.mainMedia.image" }]
          }
        ])
      );
      return {
        items: transformedData?.staffMembers,
        pagingMetadata: transformedData?.pagingMetadata
      };
    },
    errorTransformer: (err) => {
      const transformedError = sdkTransformError(err, {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { query: "$[0]" },
        singleArgumentUnchanged: false
      });
      throw transformedError;
    },
    pagingMethod: "CURSOR",
    transformationPaths: {}
  });
}
async function countStaffMembers2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    filter: options?.filter
  });
  const reqOpts = countStaffMembers(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { filter: "$[0].filter" },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function connectStaffMemberToUser2(staffMemberId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    staffMemberId,
    email: options?.email,
    fields: options?.fields
  });
  const reqOpts = connectStaffMemberToUser(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMember.mainMedia.image" }]
        }
      ])
    );
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          staffMemberId: "$[0]",
          email: "$[1].email",
          fields: "$[1].fields"
        },
        singleArgumentUnchanged: false
      },
      ["staffMemberId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function searchStaffMembers2(search, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    search,
    fields: options?.fields
  });
  const reqOpts = searchStaffMembers(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMembers.mainMedia.image" }]
        }
      ])
    );
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { search: "$[0]", fields: "$[1].fields" },
        singleArgumentUnchanged: false
      },
      ["search", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function disconnectStaffMemberFromUser2(staffMemberId, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    staffMemberId,
    fields: options?.fields
  });
  const reqOpts = disconnectStaffMemberFromUser(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMember.mainMedia.image" }]
        }
      ])
    );
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          staffMemberId: "$[0]",
          fields: "$[1].fields"
        },
        singleArgumentUnchanged: false
      },
      ["staffMemberId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function assignWorkingHoursSchedule2(staffMemberId, scheduleId, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    staffMemberId,
    scheduleId,
    fields: options?.fields
  });
  const reqOpts = assignWorkingHoursSchedule(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMember.mainMedia.image" }]
        }
      ])
    );
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          staffMemberId: "$[0]",
          scheduleId: "$[1]",
          fields: "$[2].fields"
        },
        singleArgumentUnchanged: false
      },
      ["staffMemberId", "scheduleId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function assignCustomSchedule2(staffMemberId, scheduleId, options) {
  const { httpClient, sideEffects } = arguments[3];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    staffMemberId,
    scheduleId,
    fields: options?.fields
  });
  const reqOpts = assignCustomSchedule(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(
      transformPaths2(result.data, [
        {
          transformFn: transformRESTImageToSDKImage,
          paths: [{ path: "staffMember.mainMedia.image" }]
        }
      ])
    );
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          staffMemberId: "$[0]",
          scheduleId: "$[1]",
          fields: "$[2].fields"
        },
        singleArgumentUnchanged: false
      },
      ["staffMemberId", "scheduleId", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkUpdateStaffMemberTags2(ids, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    ids,
    assignTags: options?.assignTags,
    unassignTags: options?.unassignTags
  });
  const reqOpts = bulkUpdateStaffMemberTags(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          ids: "$[0]",
          assignTags: "$[1].assignTags",
          unassignTags: "$[1].unassignTags"
        },
        singleArgumentUnchanged: false
      },
      ["ids", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function bulkUpdateStaffMemberTagsByFilter2(filter, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    filter,
    assignTags: options?.assignTags,
    unassignTags: options?.unassignTags
  });
  const reqOpts = bulkUpdateStaffMemberTagsByFilter(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          filter: "$[0]",
          assignTags: "$[1].assignTags",
          unassignTags: "$[1].unassignTags"
        },
        singleArgumentUnchanged: false
      },
      ["filter", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-staff-v1-staff-member-staff-members.public.ts
function createStaffMember3(httpClient) {
  return (staffMember, options) => createStaffMember2(
    staffMember,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function getStaffMember3(httpClient) {
  return (staffMemberId, options) => getStaffMember2(
    staffMemberId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function updateStaffMember3(httpClient) {
  return (_id, staffMember, options) => updateStaffMember2(
    _id,
    staffMember,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function deleteStaffMember3(httpClient) {
  return (staffMemberId) => deleteStaffMember2(
    staffMemberId,
    // @ts-ignore
    { httpClient }
  );
}
function queryStaffMembers3(httpClient) {
  return (options) => queryStaffMembers2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function countStaffMembers3(httpClient) {
  return (options) => countStaffMembers2(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function connectStaffMemberToUser3(httpClient) {
  return (staffMemberId, options) => connectStaffMemberToUser2(
    staffMemberId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function searchStaffMembers3(httpClient) {
  return (search, options) => searchStaffMembers2(
    search,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function disconnectStaffMemberFromUser3(httpClient) {
  return (staffMemberId, options) => disconnectStaffMemberFromUser2(
    staffMemberId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function assignWorkingHoursSchedule3(httpClient) {
  return (staffMemberId, scheduleId, options) => assignWorkingHoursSchedule2(
    staffMemberId,
    scheduleId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function assignCustomSchedule3(httpClient) {
  return (staffMemberId, scheduleId, options) => assignCustomSchedule2(
    staffMemberId,
    scheduleId,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkUpdateStaffMemberTags3(httpClient) {
  return (ids, options) => bulkUpdateStaffMemberTags2(
    ids,
    options,
    // @ts-ignore
    { httpClient }
  );
}
function bulkUpdateStaffMemberTagsByFilter3(httpClient) {
  return (filter, options) => bulkUpdateStaffMemberTagsByFilter2(
    filter,
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onStaffMemberConnectedToUser = EventDefinition(
  "wix.bookings.staff.v1.staff_member_connected_to_user",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "data.staffMember.createdDate" },
          { path: "data.staffMember.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: transformRESTImageToSDKImage2,
        paths: [{ path: "data.staffMember.mainMedia.image" }]
      }
    ])
  )
)();
var onStaffMemberCreated = EventDefinition(
  "wix.bookings.staff.v1.staff_member_created",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: transformRESTImageToSDKImage2,
        paths: [{ path: "entity.mainMedia.image" }]
      }
    ])
  )
)();
var onStaffMemberDeleted = EventDefinition(
  "wix.bookings.staff.v1.staff_member_deleted",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: transformRESTImageToSDKImage2,
        paths: [{ path: "entity.mainMedia.image" }]
      }
    ])
  )
)();
var onStaffMemberDisconnectedFromUser = EventDefinition(
  "wix.bookings.staff.v1.staff_member_disconnected_from_user",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "data.staffMember.createdDate" },
          { path: "data.staffMember.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: transformRESTImageToSDKImage2,
        paths: [{ path: "data.staffMember.mainMedia.image" }]
      }
    ])
  )
)();
var onStaffMemberFullyCreated = EventDefinition(
  "wix.bookings.staff.v1.staff_member_fully_created",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "data.staffMember.createdDate" },
          { path: "data.staffMember.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: transformRESTImageToSDKImage2,
        paths: [{ path: "data.staffMember.mainMedia.image" }]
      }
    ])
  )
)();
var onStaffMemberUpdated = EventDefinition(
  "wix.bookings.staff.v1.staff_member_updated",
  true,
  (event) => renameKeysFromRESTResponseToSDKResponse2(
    transformPaths3(event, [
      {
        transformFn: transformRESTTimestampToSDKTimestamp2,
        paths: [
          { path: "entity.createdDate" },
          { path: "entity.updatedDate" },
          { path: "metadata.eventTime" }
        ]
      },
      {
        transformFn: transformRESTImageToSDKImage2,
        paths: [{ path: "entity.mainMedia.image" }]
      }
    ])
  )
)();

// src/bookings-staff-v1-staff-member-staff-members.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
import { createEventModule } from "@wix/sdk-runtime/event-definition-modules";
var createStaffMember4 = /* @__PURE__ */ createRESTModule(createStaffMember3);
var getStaffMember4 = /* @__PURE__ */ createRESTModule(getStaffMember3);
var updateStaffMember4 = /* @__PURE__ */ createRESTModule(updateStaffMember3);
var deleteStaffMember4 = /* @__PURE__ */ createRESTModule(deleteStaffMember3);
var queryStaffMembers4 = /* @__PURE__ */ createRESTModule(queryStaffMembers3);
var countStaffMembers4 = /* @__PURE__ */ createRESTModule(countStaffMembers3);
var connectStaffMemberToUser4 = /* @__PURE__ */ createRESTModule(connectStaffMemberToUser3);
var searchStaffMembers4 = /* @__PURE__ */ createRESTModule(searchStaffMembers3);
var disconnectStaffMemberFromUser4 = /* @__PURE__ */ createRESTModule(disconnectStaffMemberFromUser3);
var assignWorkingHoursSchedule4 = /* @__PURE__ */ createRESTModule(assignWorkingHoursSchedule3);
var assignCustomSchedule4 = /* @__PURE__ */ createRESTModule(assignCustomSchedule3);
var bulkUpdateStaffMemberTags4 = /* @__PURE__ */ createRESTModule(bulkUpdateStaffMemberTags3);
var bulkUpdateStaffMemberTagsByFilter4 = /* @__PURE__ */ createRESTModule(bulkUpdateStaffMemberTagsByFilter3);
var onStaffMemberConnectedToUser2 = createEventModule(onStaffMemberConnectedToUser);
var onStaffMemberCreated2 = createEventModule(onStaffMemberCreated);
var onStaffMemberDeleted2 = createEventModule(onStaffMemberDeleted);
var onStaffMemberDisconnectedFromUser2 = createEventModule(onStaffMemberDisconnectedFromUser);
var onStaffMemberFullyCreated2 = createEventModule(onStaffMemberFullyCreated);
var onStaffMemberUpdated2 = createEventModule(onStaffMemberUpdated);
export {
  AggregationType,
  ApprovalStatus,
  AssociatedWixIdentityConnectionStatusEnumConnectionStatus,
  CalendarType,
  ConferenceType,
  ConnectionStatus,
  Day,
  DayOfWeek,
  IdentityType,
  LocationStatus,
  LocationType,
  LocationsLocationType,
  Mode,
  RecurringIntervalType,
  RequestedFields,
  ScalarType,
  ScheduleStatus,
  SessionType,
  SortOrder,
  Status,
  Transparency,
  WebhookIdentityType,
  assignCustomSchedule4 as assignCustomSchedule,
  assignWorkingHoursSchedule4 as assignWorkingHoursSchedule,
  bulkUpdateStaffMemberTags4 as bulkUpdateStaffMemberTags,
  bulkUpdateStaffMemberTagsByFilter4 as bulkUpdateStaffMemberTagsByFilter,
  connectStaffMemberToUser4 as connectStaffMemberToUser,
  countStaffMembers4 as countStaffMembers,
  createStaffMember4 as createStaffMember,
  deleteStaffMember4 as deleteStaffMember,
  disconnectStaffMemberFromUser4 as disconnectStaffMemberFromUser,
  getStaffMember4 as getStaffMember,
  onStaffMemberConnectedToUser2 as onStaffMemberConnectedToUser,
  onStaffMemberCreated2 as onStaffMemberCreated,
  onStaffMemberDeleted2 as onStaffMemberDeleted,
  onStaffMemberDisconnectedFromUser2 as onStaffMemberDisconnectedFromUser,
  onStaffMemberFullyCreated2 as onStaffMemberFullyCreated,
  onStaffMemberUpdated2 as onStaffMemberUpdated,
  queryStaffMembers4 as queryStaffMembers,
  searchStaffMembers4 as searchStaffMembers,
  updateStaffMember4 as updateStaffMember
};
//# sourceMappingURL=index.mjs.map