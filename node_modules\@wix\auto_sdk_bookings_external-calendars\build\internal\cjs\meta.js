"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  connectByCredentials: () => connectByCredentials2,
  connectByOAuth: () => connectByOAuth2,
  disconnect: () => disconnect2,
  getConnection: () => getConnection2,
  listCalendars: () => listCalendars2,
  listConnections: () => listConnections2,
  listEvents: () => listEvents2,
  listProviders: () => listProviders2,
  updateSyncConfig: () => updateSyncConfig2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-calendar-v2-external-calendar-external-calendars.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(opts) {
  const domainToMappings = {
    "api._api_base_domain_": [
      {
        srcPath: "/external-calendar-2",
        destPath: ""
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v2/external-calendar",
        destPath: "/v2/external-calendar"
      },
      {
        srcPath: "/_api/bookings/v2/external-calendars",
        destPath: "/v2/external-calendars"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v2/external-calendars",
        destPath: "/v2/external-calendars"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_external-calendars";
function listProviders(payload) {
  function __listProviders({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListProviders",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        { protoPath: "/v2/external-calendars/providers", data: payload, host }
      ),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      fallback: [
        {
          method: "GET",
          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
            {
              protoPath: "/v2/external-calendars/providers",
              data: payload,
              host
            }
          ),
          params: (0, import_rest_modules.toURLSearchParams)(payload)
        }
      ]
    };
    return metadata;
  }
  return __listProviders;
}
function getConnection(payload) {
  function __getConnection({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.GetConnection",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}",
          data: payload,
          host
        }
      ),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      fallback: [
        {
          method: "GET",
          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
            {
              protoPath: "/v2/external-calendars/connections/{connectionId}",
              data: payload,
              host
            }
          ),
          params: (0, import_rest_modules.toURLSearchParams)(payload)
        }
      ]
    };
    return metadata;
  }
  return __getConnection;
}
function listConnections(payload) {
  function __listConnections({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListConnections",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        { protoPath: "/v2/external-calendars/connections", data: payload, host }
      ),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __listConnections;
}
function connectByOAuth(payload) {
  function __connectByOAuth({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "POST",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByOAuth",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections:connectByOAuth",
          data: payload,
          host
        }
      ),
      data: payload
    };
    return metadata;
  }
  return __connectByOAuth;
}
function connectByCredentials(payload) {
  function __connectByCredentials({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "POST",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ConnectByCredentials",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections:connectByCredentials",
          data: payload,
          host
        }
      ),
      data: payload
    };
    return metadata;
  }
  return __connectByCredentials;
}
function listCalendars(payload) {
  function __listCalendars({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListCalendars",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}/calendars",
          data: payload,
          host
        }
      ),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      fallback: [
        {
          method: "GET",
          url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
            {
              protoPath: "/v2/external-calendars/connections/{connectionId}/calendars",
              data: payload,
              host
            }
          ),
          params: (0, import_rest_modules.toURLSearchParams)(payload)
        }
      ]
    };
    return metadata;
  }
  return __listCalendars;
}
function updateSyncConfig(payload) {
  function __updateSyncConfig({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "fieldMask" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "PATCH",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.UpdateSyncConfig",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}/sync-config",
          data: serializedData,
          host
        }
      ),
      data: serializedData
    };
    return metadata;
  }
  return __updateSyncConfig;
}
function disconnect(payload) {
  function __disconnect({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "POST",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.Disconnect",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        {
          protoPath: "/v2/external-calendars/connections/{connectionId}/disconnect",
          data: payload,
          host
        }
      ),
      data: payload
    };
    return metadata;
  }
  return __disconnect;
}
function listEvents(payload) {
  function __listEvents({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.calendar.v2.external_calendar",
      method: "GET",
      methodFqn: "com.wix.bookings.externalcalendar.api.v2.ExternalCalendarService.ListEvents",
      packageName: PACKAGE_NAME,
      url: resolveComWixBookingsExternalcalendarApiV2ExternalCalendarServiceUrl(
        { protoPath: "/v2/external-calendars/events", data: payload, host }
      ),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __listEvents;
}

// src/bookings-calendar-v2-external-calendar-external-calendars.meta.ts
function listProviders2() {
  const payload = {};
  const getRequestOptions = listProviders(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/external-calendars/providers",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getConnection2() {
  const payload = { connectionId: ":connectionId" };
  const getRequestOptions = getConnection(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/external-calendars/connections/{connectionId}",
    pathParams: { connectionId: "connectionId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function listConnections2() {
  const payload = {};
  const getRequestOptions = listConnections(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/external-calendars/connections",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function connectByOAuth2() {
  const payload = {};
  const getRequestOptions = connectByOAuth(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/external-calendars/connections:connectByOAuth",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function connectByCredentials2() {
  const payload = {};
  const getRequestOptions = connectByCredentials(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/external-calendars/connections:connectByCredentials",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function listCalendars2() {
  const payload = { connectionId: ":connectionId" };
  const getRequestOptions = listCalendars(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/external-calendars/connections/{connectionId}/calendars",
    pathParams: { connectionId: "connectionId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateSyncConfig2() {
  const payload = { connectionId: ":connectionId" };
  const getRequestOptions = updateSyncConfig(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v2/external-calendars/connections/{connectionId}/sync-config",
    pathParams: { connectionId: "connectionId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function disconnect2() {
  const payload = { connectionId: ":connectionId" };
  const getRequestOptions = disconnect(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v2/external-calendars/connections/{connectionId}/disconnect",
    pathParams: { connectionId: "connectionId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function listEvents2() {
  const payload = {};
  const getRequestOptions = listEvents(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v2/external-calendars/events",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  connectByCredentials,
  connectByOAuth,
  disconnect,
  getConnection,
  listCalendars,
  listConnections,
  listEvents,
  listProviders,
  updateSyncConfig
});
//# sourceMappingURL=meta.js.map