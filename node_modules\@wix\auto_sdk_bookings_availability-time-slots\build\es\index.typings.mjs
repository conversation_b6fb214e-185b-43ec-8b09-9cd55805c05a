// src/bookings-availability-v2-time-slot-availability-time-slots.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-availability-v2-time-slot-availability-time-slots.http.ts
import { transformSDKTimestampToRESTTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl(opts) {
  const domainToMappings = {
    "www.wixapis.com": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      }
    ],
    _: [
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/service-availability/v2/multi-service-time-slots",
        destPath: "/v2/multi-service-time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots",
        destPath: "/v2/time-slots"
      },
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ],
    "editor.wixapps.net": [
      {
        srcPath: "/_api/service-availability/v2/time-slots/event",
        destPath: "/v2/time-slots/event"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_availability-time-slots";
function listAvailabilityTimeSlots(payload) {
  function __listAvailabilityTimeSlots({ host }) {
    const serializedData = transformPaths(payload, [
      {
        transformFn: transformSDKTimestampToRESTTimestamp,
        paths: [{ path: "bookingPolicyViolations.earliestBookingDate" }]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.availability.v2.time_slot",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.ListAvailabilityTimeSlots",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl({
        protoPath: "/v2/time-slots",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "timeSlots.bookingPolicyViolations.earliestBookingDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listAvailabilityTimeSlots;
}
function getAvailabilityTimeSlot(payload) {
  function __getAvailabilityTimeSlot({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.availability.v2.time_slot",
      method: "POST",
      methodFqn: "com.wixpress.bookings.availability.v2.AvailabilityTimeSlots.GetAvailabilityTimeSlot",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsAvailabilityV2AvailabilityTimeSlotsUrl({
        protoPath: "/v2/time-slots/get",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "timeSlot.bookingPolicyViolations.earliestBookingDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getAvailabilityTimeSlot;
}

// src/bookings-availability-v2-time-slot-availability-time-slots.universal.ts
var LocationType = /* @__PURE__ */ ((LocationType2) => {
  LocationType2["UNKNOWN_LOCATION_TYPE"] = "UNKNOWN_LOCATION_TYPE";
  LocationType2["BUSINESS"] = "BUSINESS";
  LocationType2["CUSTOM"] = "CUSTOM";
  LocationType2["CUSTOMER"] = "CUSTOMER";
  return LocationType2;
})(LocationType || {});
async function listAvailabilityTimeSlots2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    serviceId: options?.serviceId,
    fromLocalDate: options?.fromLocalDate,
    toLocalDate: options?.toLocalDate,
    timeZone: options?.timeZone,
    locations: options?.locations,
    resourceIds: options?.resourceIds,
    includeResourceTypeIds: options?.includeResourceTypeIds,
    bookable: options?.bookable,
    bookingPolicyViolations: options?.bookingPolicyViolations,
    timeSlotsPerDay: options?.timeSlotsPerDay,
    cursorPaging: options?.cursorPaging,
    resourceTypes: options?.resourceTypes
  });
  const reqOpts = listAvailabilityTimeSlots(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceId: "$[0].serviceId",
          fromLocalDate: "$[0].fromLocalDate",
          toLocalDate: "$[0].toLocalDate",
          timeZone: "$[0].timeZone",
          locations: "$[0].locations",
          resourceIds: "$[0].resourceIds",
          includeResourceTypeIds: "$[0].includeResourceTypeIds",
          bookable: "$[0].bookable",
          bookingPolicyViolations: "$[0].bookingPolicyViolations",
          timeSlotsPerDay: "$[0].timeSlotsPerDay",
          cursorPaging: "$[0].cursorPaging",
          resourceTypes: "$[0].resourceTypes"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function getAvailabilityTimeSlot2(serviceId, localStartDate, localEndDate, timeZone, location, options) {
  const { httpClient, sideEffects } = arguments[6];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    serviceId,
    localStartDate,
    localEndDate,
    timeZone,
    location,
    resourceIds: options?.resourceIds,
    includeResourceTypeIds: options?.includeResourceTypeIds
  });
  const reqOpts = getAvailabilityTimeSlot(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          serviceId: "$[0]",
          localStartDate: "$[1]",
          localEndDate: "$[2]",
          timeZone: "$[3]",
          location: "$[4]",
          resourceIds: "$[5].resourceIds",
          includeResourceTypeIds: "$[5].includeResourceTypeIds"
        },
        singleArgumentUnchanged: false
      },
      [
        "serviceId",
        "localStartDate",
        "localEndDate",
        "timeZone",
        "location",
        "options"
      ]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
export {
  LocationType,
  getAvailabilityTimeSlot2 as getAvailabilityTimeSlot,
  listAvailabilityTimeSlots2 as listAvailabilityTimeSlots
};
//# sourceMappingURL=index.typings.mjs.map