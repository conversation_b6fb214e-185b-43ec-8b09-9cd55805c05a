import { bF as CreateAddOnGroupRequest$1, a as CreateAddOnGroupResponse$1, bG as DeleteAddOnGroupRequest$1, bH as DeleteAddOnGroupResponse$1, bI as UpdateAddOnGroupRequest$1, c as UpdateAddOnGroupResponse$1, bJ as ListAddOnGroupsByServiceIdRequest$1, e as ListAddOnGroupsByServiceIdResponse$1, bN as SetAddOnsForGroupRequest$1, f as SetAddOnsForGroupResponse$1, bO as CreateServiceRequest$1, bP as CreateServiceResponse$1, bT as BulkCreateServicesRequest$1, j as BulkCreateServicesResponse$1, bY as GetServiceRequest$1, bZ as GetServiceResponse$1, c2 as UpdateServiceRequest$1, c3 as UpdateServiceResponse$1, c4 as BulkUpdateServicesRequest$1, n as BulkUpdateServicesResponse$1, c6 as BulkUpdateServicesByFilterRequest$1, p as BulkUpdateServicesByFilterResponse$1, c7 as DeleteServiceRequest$1, c9 as DeleteServiceResponse$1, ca as BulkDeleteServicesRequest$1, s as BulkDeleteServicesResponse$1, cb as BulkDeleteServicesByFilterRequest$1, u as BulkDeleteServicesByFilterResponse$1, cc as QueryServicesRequest$1, ci as QueryServicesResponse$1, cl as SearchServicesRequest$1, x as SearchServicesResponse$1, cZ as QueryPoliciesRequest$1, Q as QueryPoliciesResponse$1, d0 as QueryBookingFormsRequest$1, E as QueryBookingFormsResponse$1, d4 as CountServicesRequest$1, H as CountServicesResponse$1, d5 as QueryLocationsRequest$1, J as QueryLocationsResponse$1, da as QueryCategoriesRequest$1, M as QueryCategoriesResponse$1, de as SetServiceLocationsRequest$1, P as SetServiceLocationsResponse$1, di as EnablePricingPlansForServiceRequest$1, R as EnablePricingPlansForServiceResponse$1, dk as DisablePricingPlansForServiceRequest$1, W as DisablePricingPlansForServiceResponse$1, dl as SetCustomSlugRequest$1, Z as SetCustomSlugResponse$1, dm as ValidateSlugRequest$1, a1 as ValidateSlugResponse$1, dn as CloneServiceRequest$1, a2 as CloneServiceResponse$1 } from './bookings-services-v2-service-services.universal-C8m36LwQ.mjs';
import '@wix/sdk-types';

/** The `service` object represents an offering that a business provides to its customers. */
interface Service {
    /**
     * Service ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Service type.
     * Learn more about *service types*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)).
     */
    type?: ServiceTypeWithLiterals;
    /**
     * Order of the service within a *category*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object)).
     */
    sortOrder?: number | null;
    /**
     * Service name.
     * @maxLength 400
     * @minLength 1
     */
    name?: string | null;
    /**
     * Service description. For example, `High-class hair styling, cuts, straightening and color`.
     * @maxLength 7000
     */
    description?: string | null;
    /**
     * Short service description, such as `Hair styling`.
     * @maxLength 6000
     */
    tagLine?: string | null;
    /**
     * Default maximum number of customers that can book the service. The service cannot be booked beyond this capacity.
     * @min 1
     * @max 1000
     */
    defaultCapacity?: number | null;
    /** Media associated with the service. */
    media?: Media;
    /** Whether the service is hidden from Wix Bookings pages and widgets. */
    hidden?: boolean | null;
    /**
     * _Category_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object))
     * the service is associated with.
     */
    category?: V2Category;
    /** Form the customer filled out when booking the service. */
    form?: Form;
    /**
     * Payment options for booking the service.
     * Learn more about *service payments*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)).
     */
    payment?: Payment;
    /** Online booking settings. */
    onlineBooking?: OnlineBooking;
    /** Conferencing options for the service. */
    conferencing?: Conferencing;
    /**
     * The locations this service is offered at. Read more about *service locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations)).
     * @immutable
     * @maxSize 500
     */
    locations?: Location[];
    /**
     * _Policy_
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction))
     * determining under what conditions this service can be booked. For example, whether the service can only be booked up to 30 minutes before it begins.
     */
    bookingPolicy?: BookingPolicy;
    /**
     * The service's *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),
     * which can be used to manage the service's *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).
     */
    schedule?: Schedule;
    /**
     * IDs of the *staff members*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service. Available only for appointment-based services.
     * @maxSize 220
     * @format GUID
     */
    staffMemberIds?: string[];
    /**
     * Information about which resources must be available so customers can book the service.
     * For example, a meeting room or equipment.
     * @maxSize 3
     */
    serviceResources?: ServiceResource[];
    /**
     * A slug is the last part of the URL address that serves as a unique identifier of the service.
     * The list of supported slugs includes past service names for backwards compatibility, and a custom slug if one was set by the business owner.
     * @readonly
     * @maxSize 100
     */
    supportedSlugs?: Slug[];
    /**
     * Active slug for the service.
     * Learn more about *service slugs*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-slugs) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-slugs)).
     * @readonly
     */
    mainSlug?: Slug;
    /**
     * URLs to various service-related pages, such as the calendar page and the booking page.
     * @readonly
     */
    urls?: URLs;
    /** Extensions enabling users to save custom data related to the service. */
    extendedFields?: ExtendedFields;
    /** Custom SEO data for the service. */
    seoData?: SeoSchema;
    /**
     * Date and time the service was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Date and time the service was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Revision number, which increments by 1 each time the service is updated. To
     * prevent conflicting changes, the existing revision must be used when updating
     * a service.
     * @readonly
     */
    revision?: string | null;
}
declare enum ServiceType {
    /** Appointment-based service. */
    APPOINTMENT = "APPOINTMENT",
    /** Class service. */
    CLASS = "CLASS",
    /** Course service. */
    COURSE = "COURSE"
}
/** @enumType */
type ServiceTypeWithLiterals = ServiceType | 'APPOINTMENT' | 'CLASS' | 'COURSE';
interface Media {
    /**
     * Media items associated with the service.
     * @maxSize 100
     */
    items?: MediaItem[];
    /** Primary media associated with the service. */
    mainMedia?: MediaItem;
    /** Cover media associated with the service. */
    coverMedia?: MediaItem;
}
interface MediaItem extends MediaItemItemOneOf {
    /** Details of the image associated with the service, such as URL and size. */
    image?: Image;
}
/** @oneof */
interface MediaItemItemOneOf {
    /** Details of the image associated with the service, such as URL and size. */
    image?: Image;
}
interface Image {
    /**
     * WixMedia image ID. (e.g. "4b3901ffcb8d7ad81a613779d92c9702.jpg")
     * @maxLength 2048
     */
    id?: string;
    /**
     * Image URL. (similar to image.id e.g. "4b3901ffcb8d7ad81a613779d92c9702.jpg")
     * @maxLength 2048
     */
    url?: string;
    /** Original image height. */
    height?: number;
    /** Original image width. */
    width?: number;
    /**
     * Image alt text.
     * @maxLength 134061
     */
    altText?: string | null;
    /**
     * Image file name.
     * @readonly
     * @maxLength 2048
     */
    filename?: string | null;
}
interface V2Category {
    /**
     * Category ID.
     * @format GUID
     */
    id?: string;
    /**
     * Category name.
     * @maxLength 500
     * @readonly
     */
    name?: string | null;
    /**
     * Order of a category within a category list.
     * @readonly
     */
    sortOrder?: number | null;
}
interface Form {
    /**
     * ID of the form associated with the service.
     * The form information that you submit when booking includes contact details, participants, and other form fields set up for the service.
     * You can manage the service booking form fields using the Bookings Forms API.
     * @format GUID
     */
    id?: string;
}
interface Payment extends PaymentRateOneOf {
    /**
     * The details for the fixed price of the service.
     *
     * Required when: `rateType` is `FIXED`
     */
    fixed?: FixedPayment;
    /**
     * The details for the custom price of the service.
     *
     * Required when: `rateType` is `CUSTOM`
     */
    custom?: CustomPayment;
    /**
     * The details for the varied pricing of the service.
     * Read more about [varied price options](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online#offering-varied-price-options).
     *
     * Required when: `rateType` is `VARIED`
     */
    varied?: VariedPayment;
    /** The rate the customer is expected to pay for the service. */
    rateType?: RateTypeWithLiterals;
    /** The payment options a customer can use to pay for the service. */
    options?: PaymentOptions;
    /**
     * IDs of pricing plans that can be used as payment for the service.
     * @readonly
     * @maxSize 75
     * @format GUID
     */
    pricingPlanIds?: string[];
}
/** @oneof */
interface PaymentRateOneOf {
    /**
     * The details for the fixed price of the service.
     *
     * Required when: `rateType` is `FIXED`
     */
    fixed?: FixedPayment;
    /**
     * The details for the custom price of the service.
     *
     * Required when: `rateType` is `CUSTOM`
     */
    custom?: CustomPayment;
    /**
     * The details for the varied pricing of the service.
     * Read more about [varied price options](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online#offering-varied-price-options).
     *
     * Required when: `rateType` is `VARIED`
     */
    varied?: VariedPayment;
}
declare enum RateType {
    /** Unknown rate type. */
    UNKNOWN_RATE_TYPE = "UNKNOWN_RATE_TYPE",
    /** The service has a fixed price. */
    FIXED = "FIXED",
    /** The service has a custom price, expressed as a price description. */
    CUSTOM = "CUSTOM",
    /** This service is offered with a set of different prices based on different terms. */
    VARIED = "VARIED",
    /** This service is offered free of charge. */
    NO_FEE = "NO_FEE"
}
/** @enumType */
type RateTypeWithLiterals = RateType | 'UNKNOWN_RATE_TYPE' | 'FIXED' | 'CUSTOM' | 'VARIED' | 'NO_FEE';
interface FixedPayment {
    /**
     * The fixed price required to book the service.
     *
     * Required when: `rateType` is `FIXED`
     */
    price?: Money;
    /**
     * The deposit price required to book the service.
     *
     * Required when: `rateType` is `FIXED` and `paymentOptions.deposit` is `true`
     */
    deposit?: Money;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gt:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     * @readonly
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface CustomPayment {
    /**
     * A custom description explaining to the customer how to pay for the service.
     * @maxLength 50
     */
    description?: string | null;
}
interface VariedPayment {
    /** The default price for the service without any variants. It will also be used as the default price for any new variant. */
    defaultPrice?: Money;
    /**
     * The deposit price required to book the service.
     *
     * Required when: `rateType` is `VARIED` and `paymentOptions.deposit` is `true`
     */
    deposit?: Money;
    /**
     * The minimal price a customer may pay for this service, based on its variants.
     * @readonly
     */
    minPrice?: Money;
    /**
     * The maximum price a customer may pay for this service, based on its variants.
     * @readonly
     */
    maxPrice?: Money;
}
interface PaymentOptions {
    /**
     * Customers can pay for the service online.
     * When `true`:
     * + `rateType` must be either `FIXED` or `VARIED`.
     * + `fixed.price` or `varied.default_price` must be specified respectively. Read more about [getting paid online](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online).
     */
    online?: boolean | null;
    /** Customers can pay for the service in person. */
    inPerson?: boolean | null;
    /**
     * This service requires a deposit to be made online in order to book it.
     * When `true`:
     * + `rateType` must be `VARIED` or `FIXED`.
     * + A `deposit` must be specified.
     */
    deposit?: boolean | null;
    /** Customers can pay for the service using a pricing plan. */
    pricingPlan?: boolean | null;
}
interface OnlineBooking {
    /**
     * Whether the service can be booked online.
     * When set to `true`, customers can book the service online. Configure the payment options via the `service.payment` property.
     * When set to `false`, customers cannot book the service online, and the service can only be paid for in person.
     */
    enabled?: boolean | null;
    /** Booking the service requires approval by the Wix user. */
    requireManualApproval?: boolean | null;
    /** Multiple customers can request to book the same time slot. This is relevant when `requireManualApproval` is `true`. */
    allowMultipleRequests?: boolean | null;
}
interface Conferencing {
    /** Whether a conference link is generated for the service's sessions. */
    enabled?: boolean | null;
}
interface Location extends LocationOptionsOneOf {
    /** Information about business locations. */
    business?: BusinessLocationOptions;
    /** Information about custom locations. */
    custom?: CustomLocationOptions;
    /**
     * Location ID.
     * @format GUID
     * @readonly
     */
    id?: string;
    /**
     * Location type.
     *
     * Default: `CUSTOM`
     */
    type?: LocationTypeWithLiterals;
    /**
     * Location address. Empty for `{"type": "CUSTOMER"}`.
     * @readonly
     */
    calculatedAddress?: CommonAddress;
}
/** @oneof */
interface LocationOptionsOneOf {
    /** Information about business locations. */
    business?: BusinessLocationOptions;
    /** Information about custom locations. */
    custom?: CustomLocationOptions;
}
declare enum LocationType {
    UNKNOWN_LOCATION_TYPE = "UNKNOWN_LOCATION_TYPE",
    /**
     * Location set by the business that is not a standard business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     */
    CUSTOM = "CUSTOM",
    /**
     * Business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     */
    BUSINESS = "BUSINESS",
    /**
     * The customer specifies any address when booking. Available only for
     * appointment-based services.
     */
    CUSTOMER = "CUSTOMER"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNKNOWN_LOCATION_TYPE' | 'CUSTOM' | 'BUSINESS' | 'CUSTOMER';
interface CommonAddress extends CommonAddressStreetOneOf {
    /** Street name and number. */
    streetAddress?: StreetAddress;
    /** @maxLength 255 */
    addressLine?: string | null;
    /**
     * 2-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format.
     * @format COUNTRY
     */
    country?: string | null;
    /**
     * Code for a subdivision (such as state, prefecture, or province) in [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) format.
     * @maxLength 255
     */
    subdivision?: string | null;
    /**
     * City name.
     * @maxLength 255
     */
    city?: string | null;
    /**
     * Postal or zip code.
     * @maxLength 255
     */
    postalCode?: string | null;
    /**
     * Full address of the location.
     * @maxLength 512
     */
    formattedAddress?: string | null;
}
/** @oneof */
interface CommonAddressStreetOneOf {
    /** Street name and number. */
    streetAddress?: StreetAddress;
    /** @maxLength 255 */
    addressLine?: string | null;
}
/** Street address. Includes street name, number, and apartment number in separate fields. */
interface StreetAddress {
    /**
     * Street number.
     * @maxLength 255
     */
    number?: string;
    /**
     * Street name.
     * @maxLength 255
     */
    name?: string;
    /**
     * Apartment number.
     * @maxLength 255
     */
    apt?: string;
}
interface BusinessLocationOptions {
    /**
     * ID of the business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * When setting a business location, specify only the location ID. Other location details are overwritten.
     * @format GUID
     */
    id?: string;
    /**
     * Business location name.
     * @readonly
     * @maxLength 150
     */
    name?: string;
    /**
     * Whether this is the default location. There can only be a single default location per site.
     * @readonly
     */
    default?: boolean | null;
    /**
     * Business location address.
     * @readonly
     */
    address?: CommonAddress;
    /**
     * Business location email.
     * @format EMAIL
     * @readonly
     */
    email?: string | null;
    /**
     * Business location phone.
     * @format PHONE
     * @readonly
     */
    phone?: string | null;
}
interface CustomLocationOptions {
    /**
     * ID of the custom location.
     * @format GUID
     * @readonly
     */
    id?: string;
    /** Address of the custom location. */
    address?: CommonAddress;
}
/**
 * `BookingPolicy` is the main entity of `BookingPolicyService` and specifies a set of rules for booking a service
 * by visitors and members.
 *
 * Each `BookingPolicy` consists of a number of sub-policies. When the Bookings App is provisioned to a meta site then a
 * default `BookingPolicy` will be created with defaults for each of these sub-policies. This also applies when a request
 * is received to create a new `BookingPolicy` and one or more of these sub-policies are not provided.
 *
 * Sub-policies are defined in separate objects as specified below.
 *
 * - The `LimitEarlyBookingPolicy` object defines the policy for limiting early bookings.
 * - The `LimitLateBookingPolicy` object defines the policy for limiting late bookings.
 * - The `BookAfterStartPolicy` object defines the policy for booking after the start of the schedule.
 * - The `CancellationPolicy` object defines the policy for canceling a booked entity.
 * - The `ReschedulePolicy` object defines the policy for rescheduling booked entity.
 * - The `WaitlistPolicy` object defines the policy for a waitlist.
 * - The `ParticipantsPolicy` object defines the policy regarding the participants per booking.
 * - The `ResourcesPolicy` object defines the policy regarding the resources per booking.
 * - The `CancellationFeePolicy` object defines the policy regarding cancellation fees.
 * - The `SaveCreditCardPolicy` object defines the policy for saving credit card details.
 *
 * By default each sub-policy is disabled. A more detailed specification of the default settings of each sub-policy
 * can be found in the description of the corresponding object.
 *
 * Partial updates are supported on the main entity level, however in order to update a sub-policy the client needs to provide the whole sub-policy object.
 */
interface BookingPolicy {
    /**
     * The ID to the policy for the booking.
     * @format GUID
     */
    id?: string;
    /**
     * Date and time the policy was created.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Date and time the policy was updated.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Name of the policy.
     * @maxLength 400
     * @readonly
     */
    name?: string | null;
    /**
     * Custom description for the policy. This policy is displayed to the participant.
     * @readonly
     */
    customPolicyDescription?: PolicyDescription;
    /**
     * Whether the policy is the default for the meta site.
     * @readonly
     */
    default?: boolean | null;
    /**
     * Policy for limiting early bookings.
     * @readonly
     */
    limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;
    /**
     * Policy for limiting late bookings.
     * @readonly
     */
    limitLateBookingPolicy?: LimitLateBookingPolicy;
    /**
     * Policy on booking an entity after the start of the schedule.
     * @readonly
     */
    bookAfterStartPolicy?: BookAfterStartPolicy;
    /**
     * Policy for canceling a booked entity.
     * @readonly
     */
    cancellationPolicy?: CancellationPolicy;
    /**
     * Policy for rescheduling a booked entity.
     * @readonly
     */
    reschedulePolicy?: ReschedulePolicy;
    /**
     * Waitlist policy for the service.
     * @readonly
     */
    waitlistPolicy?: WaitlistPolicy;
    /**
     * Policy regarding the participants per booking.
     * @readonly
     */
    participantsPolicy?: ParticipantsPolicy;
    /**
     * Policy for allocating resources.
     * @readonly
     */
    resourcesPolicy?: ResourcesPolicy;
    /**
     * Rules for cancellation fees.
     * @readonly
     */
    cancellationFeePolicy?: CancellationFeePolicy;
    /**
     * Rule for saving credit card.
     * @readonly
     */
    saveCreditCardPolicy?: SaveCreditCardPolicy;
}
/** A description of the policy to display to participants. */
interface PolicyDescription {
    /**
     * Whether the description should be displayed. If `true`, the description is displayed.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * The description to display.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    description?: string;
}
/** The policy for limiting early bookings. */
interface LimitEarlyBookingPolicy {
    /**
     * Whether there is a limit on how early a customer
     * can book. When `false`, there is no limit on the earliest
     * booking time and customers can book in advance, as early as they like.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Maximum number of minutes before the start of the session that a booking can be made. This value must be greater
     * than `latest_booking_in_minutes` in the `LimitLateBookingPolicy` policy.
     *
     * Default: 10080 minutes (7 days)
     * Min: 1 minute
     * @min 1
     */
    earliestBookingInMinutes?: number;
}
/**
 * The policy for limiting late bookings.
 *
 * This policy and the `BookAfterStartPolicy` policy cannot be enabled at the same time. So if this policy
 * is enabled, `BookAfterStartPolicy` must be disabled.
 */
interface LimitLateBookingPolicy {
    /**
     * Whether there is a limit on how late a customer
     * can book. When `false`, there is no limit on the latest
     * booking time and customers can book up to the last minute.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Minimum number of minutes before the start of the session that a booking can be made.
     * For a schedule, this is relative to the start time of the next booked session, excluding past-booked sessions.
     * This value must be less than `earliest_booking_in_minutes` in the `LimitEarlyBookingPolicy` policy.
     *
     * Default: 1440 minutes (1 day)
     * Min: 1 minute
     * @min 1
     */
    latestBookingInMinutes?: number;
}
/**
 * The policy for whether a session can be booked after the start of the schedule.
 * This policy and `LimitLateBookingPolicy` cannot be enabled at the same time. So if this policy
 * is enabled, the `LimitLateBookingPolicy` policy must be disabled.
 */
interface BookAfterStartPolicy {
    /**
     * Whether booking is allowed after the start of the schedule. When `true`,
     * customers can book after the start of the schedule.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
/** The policy for canceling a booked session. */
interface CancellationPolicy {
    /**
     * Whether canceling a booking is allowed. When `true`, customers
     * can cancel the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there is a limit on the latest cancellation time. When `true`,
     * a time limit is enforced.
     *
     * Default: `false`
     */
    limitLatestCancellation?: boolean;
    /**
     * Minimum number of minutes before the start of the booked session that the booking can be canceled.
     *
     * Default: 1440 minutes (1 day)
     * Min: 1 minute
     * @min 1
     */
    latestCancellationInMinutes?: number;
}
/** The policy for rescheduling a booked session. */
interface ReschedulePolicy {
    /**
     * Whether rescheduling a booking is allowed. When `true`, customers
     * can reschedule the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there is a limit on the latest reschedule time. When `true`,
     * a time limit is enforced.
     *
     * Default: `false`
     */
    limitLatestReschedule?: boolean;
    /**
     * Minimum number of minutes before the start of the booked session that the booking can be rescheduled.
     *
     * Default: 1440 minutes (1 day)
     * Min: 1 minute
     * @min 1
     */
    latestRescheduleInMinutes?: number;
}
/** The policy for the waitlist. */
interface WaitlistPolicy {
    /**
     * Whether the session has a waitlist. If `true`, there is a waitlist.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Number of spots available in the waitlist.
     *
     * Default: 10 spots
     * Min: 1 spot
     * @min 1
     */
    capacity?: number;
    /**
     * Amount of time a participant is given to book, once notified that a spot is available.
     *
     * Default: 10 minutes
     * Min: 1 spot
     * @min 1
     */
    reservationTimeInMinutes?: number;
}
/** The policy for the maximum number of participants per booking. */
interface ParticipantsPolicy {
    /**
     * Maximum number of participants allowed.
     *
     * Default: 1 participant
     * Min: 1 participant
     * @min 1
     */
    maxParticipantsPerBooking?: number;
}
/** The policy regarding the allocation of resources (e.g. staff members). */
interface ResourcesPolicy {
    /**
     * `true` if this policy is enabled, `false` otherwise.
     * When `false` then the client must always select a resource when booking an appointment.
     */
    enabled?: boolean;
    /**
     * `true`, if it is allowed to automatically assign a resource when booking an appointment,
     * `false`, if the client must always select a resource.
     *
     * Default: `false`
     */
    autoAssignAllowed?: boolean;
}
interface CancellationFeePolicy {
    /**
     * Whether canceling a booking will result in a cancellation fee
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Cancellation windows describing the time of cancellation and the fee to charge.
     * @maxSize 2
     */
    cancellationWindows?: CancellationWindow[];
    /**
     * Whether the cancellation fee should not be automatically collected when customer cancels the booking.
     *
     * Default: `true`
     */
    autoCollectFeeEnabled?: boolean | null;
}
interface CancellationWindow extends CancellationWindowFeeOneOf {
    /** Amount to be charged as a cancellation fee. */
    amount?: Money;
    /**
     * Percentage of the original price to be charged as a cancellation fee.
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
    /**
     * The fee will be applied if the booked session starts within this start time in minutes.
     * @min 1
     */
    startInMinutes?: number | null;
}
/** @oneof */
interface CancellationWindowFeeOneOf {
    /** Amount to be charged as a cancellation fee. */
    amount?: Money;
    /**
     * Percentage of the original price to be charged as a cancellation fee.
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
}
interface SaveCreditCardPolicy {
    /** Default: `false` */
    enabled?: boolean;
}
interface Schedule {
    /**
     * ID of the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * to which the service's events belong.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Start time of the first session in the schedule. For courses only.
     * @readonly
     */
    firstSessionStart?: Date | null;
    /**
     * End time of the last session in the schedule. For courses only.
     * @readonly
     */
    lastSessionEnd?: Date | null;
    /** Limitations affecting the service availability. */
    availabilityConstraints?: AvailabilityConstraints;
}
interface AvailabilityConstraints {
    /**
     * Calculated list of all supported session durations for the service. For
     * appointment-based services without varied pricing based on session length, it
     * matches the single value in the `sessionDurations` array. For appointment-based
     * services with varied pricing based on session length, it includes session
     * durations for all *variants*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),
     * while ignoring `sessionDurations`.
     * For courses and classes, it includes durations for all future
     * recurring sessions but excludes durations for one-off or past recurring sessions.
     * @readonly
     * @maxSize 50
     */
    durations?: Duration[];
    /**
     * List of supported session durations in minutes.
     *
     * - For appointment-based services, specify `sessionDurations` when creating a service.
     * - For appointment-based services with varied pricing by session length, you must still specify `sessionDurations`, but the values are ignored. Actual durations are taken from the service variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * - For classes or courses, don't specify `sessionDurations` when creating a service.
     *
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     * @maxSize 50
     */
    sessionDurations?: number[];
    /**
     * The number of minutes between the end of a session and the start of the next.
     *
     *
     * Min: `0` minutes
     * Max: `720` minutes
     * @max 720
     */
    timeBetweenSessions?: number;
}
interface Duration {
    /**
     * The duration of the service in minutes.
     *
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     */
    minutes?: number;
}
interface ServiceResource extends ServiceResourceSelectionOneOf {
    /**
     * Details about the required *resource type*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     */
    resourceType?: ResourceType;
}
/** @oneof */
interface ServiceResourceSelectionOneOf {
}
interface ResourceType {
    /**
     * The type of the resource.
     * @format GUID
     */
    id?: string | null;
    /**
     * The name of the resource type.
     * @readonly
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
}
interface Slug {
    /**
     * The unique part of service's URL that identifies the service's information page. For example, `service-1` in `https:/example.com/services/service-1`.
     * @maxLength 500
     */
    name?: string;
    /**
     * Whether the slug was generated or customized. If `true`, the slug was customized manually by the business owner. Otherwise, the slug was automatically generated from the service name.
     * @readonly
     */
    custom?: boolean | null;
    /**
     * Date and time the slug was created. This is a system field.
     * @readonly
     */
    createdDate?: Date | null;
}
interface URLs {
    /**
     * The URL for the service page.
     * @readonly
     */
    servicePage?: PageUrlV2;
    /**
     * The URL for the booking entry point. It can be either to the calendar or to the service page.
     * @readonly
     */
    bookingPage?: PageUrlV2;
    /**
     * The URL for the calendar. Can be empty if no calendar exists.
     * @readonly
     */
    calendarPage?: PageUrlV2;
}
interface PageUrlV2 {
    /**
     * The relative path for the page within the site. For example, `/product-page/a-product`.
     * @maxLength 2048
     */
    relativePath?: string;
    /**
     * The page's full URL. For example, `https://mysite.com/product-page/a-product`.
     * @maxLength 2048
     */
    url?: string | null;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
/**
 * The SEO schema object contains data about different types of meta tags. It makes sure that the information about your page is presented properly to search engines.
 * The search engines use this information for ranking purposes, or to display snippets in the search results.
 * This data will override other sources of tags (for example patterns) and will be included in the <head> section of the HTML document, while not being displayed on the page itself.
 */
interface SeoSchema {
    /** SEO tag information. */
    tags?: Tag[];
    /** SEO general settings. */
    settings?: Settings;
}
interface Keyword {
    /** Keyword value. */
    term?: string;
    /** Whether the keyword is the main focus keyword. */
    isMain?: boolean;
    /**
     * The source that added the keyword terms to the SEO settings.
     * @maxLength 1000
     */
    origin?: string | null;
}
interface Tag {
    /**
     * SEO tag type.
     *
     *
     * Supported values: `title`, `meta`, `script`, `link`.
     */
    type?: string;
    /**
     * A `{"key": "value"}` pair object where each SEO tag property (`"name"`, `"content"`, `"rel"`, `"href"`) contains a value.
     * For example: `{"name": "description", "content": "the description itself"}`.
     */
    props?: Record<string, any> | null;
    /** SEO tag metadata. For example, `{"height": 300, "width": 240}`. */
    meta?: Record<string, any> | null;
    /** SEO tag inner content. For example, `<title> inner content </title>`. */
    children?: string;
    /** Whether the tag is a [custom tag](https://support.wix.com/en/article/adding-additional-meta-tags-to-your-sites-pages). */
    custom?: boolean;
    /** Whether the tag is disabled. If the tag is disabled, people can't find your page when searching for this phrase in search engines. */
    disabled?: boolean;
}
interface Settings {
    /**
     * Whether the [automatical redirect visits](https://support.wix.com/en/article/customizing-your-pages-seo-settings-in-the-seo-panel) from the old URL to the new one is enabled.
     *
     *
     * Default: `false` (automatical redirect is enabled).
     */
    preventAutoRedirect?: boolean;
    /**
     * User-selected keyword terms for a specific page.
     * @maxSize 5
     */
    keywords?: Keyword[];
}
interface AddOnGroup {
    /**
     * ID of the group.
     * @readonly
     * @format GUID
     */
    id?: string | null;
    /**
     * The name of the group.
     * @maxLength 100
     */
    name?: string | null;
    /**
     * The maximum number of AddOns that can be selected from this group.
     * If not set, there is no upper limit.
     */
    maxNumberOfAddOns?: number | null;
    /**
     * List of AddOn IDs that are part of this group.
     * @format GUID
     * @maxSize 7
     */
    addOnIds?: string[] | null;
    /**
     * The group prompt.
     * @maxLength 200
     */
    prompt?: string | null;
}
interface CreateAddOnGroupRequest {
    /** AddOnGroup to create. */
    addOnGroup: AddOnGroup;
    /**
     * ID of the service to create the AddOnGroup for.
     * @format GUID
     */
    serviceId?: string | null;
}
interface CreateAddOnGroupResponse {
    /** Created AddOnGroup. */
    addOnGroup?: AddOnGroup;
}
interface DeleteAddOnGroupRequest {
    /**
     * ID of the AddOnGroup to delete.
     * @format GUID
     */
    addOnGroupId: string | null;
    /**
     * ID of the service from which to delete the AddOnGroup.
     * @format GUID
     */
    serviceId: string | null;
}
interface DeleteAddOnGroupResponse {
}
interface UpdateAddOnGroupRequest {
    /** AddOnGroup to update. */
    addOnGroup: AddOnGroup;
    /**
     * ID of the service that contains the AddOnGroup.
     * @format GUID
     */
    serviceId: string | null;
}
interface UpdateAddOnGroupResponse {
    /** Updated AddOnGroup */
    addOnGroup?: AddOnGroup;
}
interface ListAddOnGroupsByServiceIdRequest {
    /**
     * ID of the service to retrieve AddOnGroups for.
     * @format GUID
     */
    serviceId: string | null;
    /**
     * List of group ids to return. If not provided, all groups are returned.
     * @format GUID
     * @maxSize 3
     */
    groupIds?: string[] | null;
}
interface ListAddOnGroupsByServiceIdResponse {
    /**
     * List of group IDs and their linked AddOns.
     * @maxSize 3
     */
    addOnGroupsDetails?: AddOnGroupDetail[];
}
interface AddOn extends AddOnAddOnInfoOneOf {
    /** The AddOn description. */
    durationInMinutes?: number;
    /** The AddOn max quantity. */
    maxQuantity?: number;
    /**
     * The AddOn ID.
     * @format GUID
     */
    addOnId?: string | null;
    /**
     * The AddOn name.
     * @maxLength 100
     */
    name?: string | null;
    /** The AddOn price. */
    price?: Money;
}
/** @oneof */
interface AddOnAddOnInfoOneOf {
    /** The AddOn description. */
    durationInMinutes?: number;
    /** The AddOn max quantity. */
    maxQuantity?: number;
}
interface AddOnGroupDetail {
    /**
     * The group ID.
     * @format GUID
     */
    groupId?: string | null;
    /** The group max number of AddOns. */
    maxNumberOfAddOns?: number | null;
    /**
     * The group name.
     * @maxLength 100
     */
    groupName?: string | null;
    /**
     * The AddOns information linked to the group.
     * @maxSize 7
     */
    addOns?: AddOn[];
    /**
     * The group prompt.
     * @maxLength 200
     */
    prompt?: string | null;
}
interface SetAddOnsForGroupRequest {
    /**
     * The service ID to set AddOns for.
     * @format GUID
     */
    serviceId: string | null;
    /**
     * The group ID to set AddOns for.
     * @format GUID
     */
    groupId: string | null;
    /**
     * The IDs of AddOns to set.
     * @format GUID
     * @minSize 1
     * @maxSize 7
     */
    addOnIds: string[] | null;
}
interface SetAddOnsForGroupResponse {
    /** The updated AddOnGroup. */
    addOnGroup?: AddOnGroup;
}
interface CreateServiceRequest {
    /** Service to create. */
    service: Service;
}
interface CreateServiceResponse {
    /** Created service. */
    service?: Service;
}
interface BulkCreateServicesRequest {
    /**
     * Services to create.
     * @maxSize 100
     */
    services: Service[];
    /** Whether to return the created service objects. */
    returnEntity?: boolean;
}
interface BulkCreateServicesResponse {
    /** The result of each service creation. */
    results?: BulkServiceResult[];
    /** Create statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkServiceResult {
    /** Update metadata. */
    itemMetadata?: ItemMetadata;
    /** Updated service. */
    item?: Service;
}
interface ItemMetadata {
    /**
     * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).
     * @format GUID
     */
    id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface GetServiceRequest {
    /**
     * ID of the service to retrieve.
     * @format GUID
     */
    serviceId: string;
}
interface GetServiceResponse {
    /** Retrieved service. */
    service?: Service;
}
interface UpdateServiceRequest {
    /** Service to update. */
    service: Service;
}
interface UpdateServiceResponse {
    /** Updated service. */
    service?: Service;
}
interface BulkUpdateServicesRequest {
    /**
     * Services to update.
     * @maxSize 100
     */
    services?: MaskedService[];
    /** Whether to include the updated services in the response. Default: `false` */
    returnEntity?: boolean;
}
interface MaskedService {
    /** Service to update. */
    service?: Service;
    /** Explicit list of fields to update. */
    mask?: string[];
}
interface BulkUpdateServicesResponse {
    /** The result of each service update. */
    results?: BulkServiceResult[];
    /** Update statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkUpdateServicesByFilterRequest {
    /** Filter to identify the services to update. */
    filter: Record<string, any> | null;
    /** Service to update. */
    service: Service;
}
interface BulkUpdateServicesByFilterResponse {
    /**
     * ID of the service update job.
     *
     * Pass this ID to [Get Async Job](https://dev.wix.com/docs/rest/business-management/async-job/introduction) to retrieve job details and metadata.
     * @format GUID
     */
    jobId?: string;
}
interface DeleteServiceRequest {
    /**
     * ID of the service to delete.
     * @format GUID
     */
    serviceId: string;
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /**
     * Whether to notify participants about the change and an optional
     * custom message.
     */
    participantNotification?: ParticipantNotification;
}
interface ParticipantNotification {
    /**
     * Whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean | null;
    /**
     * Custom message to send to the participants about the changes to the booking.
     * @maxLength 2000
     */
    message?: string | null;
}
interface DeleteServiceResponse {
}
interface BulkDeleteServicesRequest {
    /**
     * IDs of the services to delete.
     * @format GUID
     * @maxSize 100
     */
    ids: string[];
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`.
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /**
     * Whether to notify participants about the change and an optional
     * custom message.
     */
    participantNotification?: ParticipantNotification;
}
interface BulkDeleteServicesResponse {
    /** The result of each service removal. */
    results?: BulkServiceResult[];
    /** Delete statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkDeleteServicesByFilterRequest {
    /** Filter to identify the services that need to be deleted. */
    filter: Record<string, any> | null;
    /**
     * Whether to preserve future sessions with participants.
     *
     * Default: `false`.
     */
    preserveFutureSessionsWithParticipants?: boolean;
    /** Whether to notify participants about the change and an optional custom message. */
    participantNotification?: ParticipantNotification;
}
interface BulkDeleteServicesByFilterResponse {
    /**
     * ID of the service deletion job.
     *
     * Pass this ID to [Get Async Job](https://dev.wix.com/docs/rest/business-management/async-job/introduction) to retrieve job details and metadata.
     * @format GUID
     */
    jobId?: string;
}
interface QueryServicesRequest {
    /** WQL expression. */
    query: QueryV2;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /**
     * Filter object in the following format:
     *
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     *
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`
     *
     * Read more about [supported fields and operators](https://dev.wix.com/api/rest/wix-bookings/services-v2/filtering-and-sorting).
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[ {"fieldName":"sortField1","order":"ASC"},
     * {"fieldName":"sortField2","order":"DESC"} ]`
     *
     * Read more about [sorting](https://dev.wix.com/api/rest/wix-bookings/services-v2/filtering-and-sorting#wix-bookings_services-v2_filtering-and-sorting_sorting).
     * @maxSize 50
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface CursorPaging {
    /**
     * Number of items to load.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * You can get the relevant cursor token
     * from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryServicesResponse {
    /** The retrieved services. */
    services?: Service[];
    /** Paging metadata, including offset and count. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
    /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */
    cursors?: Cursors;
}
interface Cursors {
    /**
     * Cursor token for retrieving the next page of results.
     *
     * Use this token in subsequent requests to continue pagination forward.
     * Value is `null` when on the last page of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor token for retrieving the previous page of results.
     *
     * Use this token to navigate backwards through result pages.
     * Value is `null` when on the first page of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface SearchServicesRequest {
    /**
     * Search criteria including filter, sort, aggregations, and paging options.
     *
     * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)) for complete filter and sorting options.
     */
    search: CursorSearch;
}
interface CursorSearch extends CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object for narrowing search results. For example, to return only services with specific payment options: `"filter": {"payment.options.online": true, "payment.options.in_person": false}`.
     *
     * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).
     */
    filter?: Record<string, any> | null;
    /**
     * Array of sort objects specifying result order. For example, to sort by creation date in descending order: `"sort": [{"fieldName": "createdDate", "order": "DESC"}]`.
     *
     * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).
     * @maxSize 10
     */
    sort?: Sorting[];
    /**
     * Aggregations for grouping data into categories (facets) and providing summaries for each category.
     * For example, use aggregations to categorize search results by service type, payment options, or locations.
     * @maxSize 10
     */
    aggregations?: Aggregation[];
    /** Free text to match in searchable fields. */
    search?: SearchDetails;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.
     *
     * Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).
     * @maxLength 50
     */
    timeZone?: string | null;
}
/** @oneof */
interface CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
}
interface Aggregation extends AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
    /**
     * Deprecated, use `nested` instead.
     * @deprecated Deprecated, use `nested` instead.
     * @replacedBy kind.nested
     * @targetRemovalDate 2025-01-01
     */
    groupBy?: GroupByAggregation;
}
/** @oneof */
interface AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
}
interface RangeBucket {
    /** Inclusive lower bound of the range. Required if `to` isn't specified. */
    from?: number | null;
    /** Exclusive upper bound of the range. Required if `from` isn't specified. */
    to?: number | null;
}
declare enum SortType {
    COUNT = "COUNT",
    VALUE = "VALUE"
}
/** @enumType */
type SortTypeWithLiterals = SortType | 'COUNT' | 'VALUE';
declare enum SortDirection {
    DESC = "DESC",
    ASC = "ASC"
}
/** @enumType */
type SortDirectionWithLiterals = SortDirection | 'DESC' | 'ASC';
declare enum MissingValues {
    EXCLUDE = "EXCLUDE",
    INCLUDE = "INCLUDE"
}
/** @enumType */
type MissingValuesWithLiterals = MissingValues | 'EXCLUDE' | 'INCLUDE';
interface IncludeMissingValuesOptions {
    /**
     * Custom bucket name for missing values.
     *
     * Default values:
     * - string: `N/A`
     * - int: `0`
     * - bool: `false`
     * @maxLength 20
     */
    addToBucket?: string;
}
declare enum ScalarType {
    UNKNOWN_SCALAR_TYPE = "UNKNOWN_SCALAR_TYPE",
    /** Total number of distinct values. */
    COUNT_DISTINCT = "COUNT_DISTINCT",
    /** Minimum value. */
    MIN = "MIN",
    /** Maximum value. */
    MAX = "MAX"
}
/** @enumType */
type ScalarTypeWithLiterals = ScalarType | 'UNKNOWN_SCALAR_TYPE' | 'COUNT_DISTINCT' | 'MIN' | 'MAX';
interface ValueAggregation extends ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
    /** Whether to sort by number of matches or value of the field. */
    sortType?: SortTypeWithLiterals;
    /** Whether to sort in ascending or descending order. */
    sortDirection?: SortDirectionWithLiterals;
    /**
     * Number of aggregations to return.
     *
     * Min: `1`
     * Max: `250`
     * Default: `10`
     */
    limit?: number | null;
    /**
     * Whether missing values should be included or excluded from the aggregation results.
     *
     * Default: `EXCLUDE`
     */
    missingValues?: MissingValuesWithLiterals;
}
/** @oneof */
interface ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
}
declare enum NestedAggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM"
}
/** @enumType */
type NestedAggregationTypeWithLiterals = NestedAggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM';
interface RangeAggregation {
    /**
     * List of range buckets defining the ranges for aggregation. During aggregation, each entity is placed in the first bucket where its value falls within the specified range bounds.
     * @maxSize 50
     */
    buckets?: RangeBucket[];
}
interface ScalarAggregation {
    /** Operator for the scalar aggregation, for example `COUNT_DISTINCT`, `MIN`, `MAX`. */
    type?: ScalarTypeWithLiterals;
}
interface DateHistogramAggregation {
    /** Time interval for date histogram aggregation, for example `DAY`, `HOUR`, `MONTH`. */
    interval?: IntervalWithLiterals;
}
declare enum Interval {
    /** Unknown interval. */
    UNKNOWN_INTERVAL = "UNKNOWN_INTERVAL",
    /** Yearly interval. */
    YEAR = "YEAR",
    /** Monthly interval. */
    MONTH = "MONTH",
    /** Weekly interval. */
    WEEK = "WEEK",
    /** Daily interval. */
    DAY = "DAY",
    /** Hourly interval. */
    HOUR = "HOUR",
    /** Minute interval. */
    MINUTE = "MINUTE",
    /** Second interval. */
    SECOND = "SECOND"
}
/** @enumType */
type IntervalWithLiterals = Interval | 'UNKNOWN_INTERVAL' | 'YEAR' | 'MONTH' | 'WEEK' | 'DAY' | 'HOUR' | 'MINUTE' | 'SECOND';
interface NestedAggregationItem extends NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: NestedAggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
}
declare enum AggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM",
    /** Flattened list of aggregations, where each aggregation is nested within previous 1. */
    NESTED = "NESTED"
}
/** @enumType */
type AggregationTypeWithLiterals = AggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM' | 'NESTED';
/** Nested aggregation for multi-level faceted search. Allows exploring large amounts of data through multiple levels of categorization, where each subsequent aggregation is nested within the previous aggregation to create hierarchical data summaries. */
interface NestedAggregation {
    /**
     * Flattened list of aggregations where each next aggregation is nested within the previous 1.
     * @minSize 2
     * @maxSize 10
     */
    nestedAggregations?: NestedAggregationItem[];
}
interface GroupByAggregation extends GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
}
interface SearchDetails {
    /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */
    mode?: ModeWithLiterals;
    /**
     * Search term or expression.
     * @maxLength 200
     */
    expression?: string | null;
    /**
     * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `order.address.streetName`.
     * @maxSize 10
     * @maxLength 200
     */
    fields?: string[];
    /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */
    fuzzy?: boolean;
}
declare enum Mode {
    /** At least 1 of the search terms must be present. */
    OR = "OR",
    /** All search terms must be present. */
    AND = "AND"
}
/** @enumType */
type ModeWithLiterals = Mode | 'OR' | 'AND';
interface SearchServicesResponse {
    /**
     * Retrieved services that match the search criteria specified in the request.
     *
     * Each service includes all standard service information including name, description,
     * pricing details, location options, schedule information, and payment configuration.
     */
    services?: Service[];
    /**
     * Cursor-based paging metadata for navigating search results.
     *
     * Contains the current page's cursor information, whether there are more results available,
     * and count details. Use the `next` cursor to retrieve subsequent pages of results.
     */
    pagingMetadata?: CursorPagingMetadata;
    /**
     * Aggregation results based on the aggregations specified in the search request.
     *
     * Provides categorized data summaries such as service counts by type, location distribution,
     * payment method statistics, or custom aggregations. Available only when aggregations
     * are requested in the search criteria.
     */
    aggregationData?: AggregationData;
}
interface CursorPagingMetadata {
    /**
     * Number of items returned in the current response page.
     *
     * This count reflects the actual number of items in the current result set,
     * which may be less than the requested limit if fewer items are available.
     */
    count?: number | null;
    /**
     * Navigation cursors for moving between result pages.
     *
     * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor
     * to retrieve subsequent pages and `prev` cursor to go back to previous pages.
     * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).
     */
    cursors?: Cursors;
    /**
     * Indicates whether additional results are available beyond the current page.
     *
     * - `true`: More results exist and can be retrieved using the `next` cursor.
     * - `false`: This is the final page of results.
     */
    hasNext?: boolean | null;
}
interface AggregationData {
    /**
     * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.
     * @maxSize 10000
     */
    results?: AggregationResults[];
}
interface ValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 100
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number;
}
interface RangeAggregationResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number;
}
interface NestedAggregationResults extends NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /**
     * User-defined name of aggregation, matches the one specified in request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that matches result. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
}
interface ValueResults {
    /**
     * Array of value aggregation results, each containing a field value and the count of entities with that value.
     * @maxSize 250
     */
    results?: ValueAggregationResult[];
}
interface RangeResults {
    /**
     * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.
     * @maxSize 50
     */
    results?: RangeAggregationResult[];
}
interface AggregationResultsScalarResult {
    /** Type of scalar aggregation. */
    type?: ScalarTypeWithLiterals;
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Nested aggregations. */
    nestedResults?: NestedAggregationResults;
}
interface ValueResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number | null;
}
interface RangeResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number | null;
}
interface ScalarResult {
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedResultValue extends NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
/** @oneof */
interface NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
interface Results {
    /** Map of nested aggregation results, keyed by aggregation name. */
    results?: Record<string, NestedResultValue>;
}
interface DateHistogramResult {
    /**
     * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * @maxLength 100
     */
    value?: string;
    /** Count of documents in the bucket. */
    count?: number;
}
interface GroupByValueResults {
    /**
     * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.
     * @maxSize 1000
     */
    results?: NestedValueAggregationResult[];
}
interface DateHistogramResults {
    /**
     * Array of date histogram aggregation results, each containing a date bucket and its count.
     * @maxSize 200
     */
    results?: DateHistogramResult[];
}
/**
 * Results of `NESTED` aggregation type in a flattened form.
 * Aggregations in resulting array are keyed by requested aggregation `name`.
 */
interface NestedResults {
    /**
     * Array of nested aggregation result groups, each containing multiple aggregation results.
     * @maxSize 1000
     */
    results?: Results[];
}
interface AggregationResults extends AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
    /**
     * User-defined name of aggregation as derived from search request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that must match specified kind as derived from search request. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
}
interface QueryPoliciesRequest {
    /**
     * Information about filters, paging, and sorting. See the article about
     * booking policy filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))
     * for all supported filters and sorting options.
     */
    query: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 50
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface QueryPoliciesResponse {
    /** Retrieved booking policies and information about the services using them. */
    bookingPolicies?: BookingPolicyWithServices[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface BookingPolicyWithServices {
    /** Retrieved booking policy. */
    bookingPolicy?: BookingPolicy;
    /**
     * Up to 5 services connected to the booking policy. If `totalServiceCount` is
     * greater than 5, there are additional services connected to the policy.
     * You can call *Search Services*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))
     * specifying the relevant policy ID in the filter, to retrieve all services that are
     * connected to a booking policy.
     * @maxSize 5
     */
    connectedServices?: Service[];
    /** Total number of services connected to the booking policy. */
    totalServiceCount?: number;
}
interface QueryBookingFormsRequest {
    /**
     * Information about filters, paging, and sorting. See the article about
     * form filters ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object))
     * for all supported filters and sorting options.
     */
    query: CursorQuery;
    /**
     * Conditional fields to return.
     * @maxSize 1
     */
    conditionalFields?: RequestedFieldsWithLiterals[];
}
declare enum RequestedFields {
    /** Unknown requested conditional field. */
    UNKNOWN_REQUESTED_FIELD = "UNKNOWN_REQUESTED_FIELD",
    /** Whether to return the site's default booking form. */
    DEFAULT_BOOKING_FORM = "DEFAULT_BOOKING_FORM"
}
/** @enumType */
type RequestedFieldsWithLiterals = RequestedFields | 'UNKNOWN_REQUESTED_FIELD' | 'DEFAULT_BOOKING_FORM';
interface QueryBookingFormsResponse {
    /** Retrieved booking forms and information about connected services. */
    bookingForms?: BookingForm[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
    /** The site's default booking form and information about connected services. */
    defaultBookingForm?: BookingForm;
}
interface BookingForm {
    /** Information about the retrieved booking form. */
    formDetails?: FormDetails;
    /**
     * Up to 5 services connected to the booking form. If `totalServiceCount` is
     * greater than 5, there are additional services connected to the policy.
     * You can call *Search Services*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))
     * specifying the relevant policy ID in the filter, to retrieve all services that are
     * connected to a booking policy.
     * @maxSize 5
     */
    connectedServices?: ConnectedService[];
    /** Total number of services connected to the booking form. */
    totalServiceCount?: number;
}
interface FormDetails {
    /**
     * Form ID.
     * @format GUID
     */
    formId?: string;
    /**
     * Form name.
     * @maxLength 200
     */
    name?: string | null;
    /**
     * Revision number, which increments by 1 each time the form is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the form.
     * @readonly
     */
    revision?: string | null;
}
interface ConnectedService {
    /**
     * ID of the service connected to the booking form.
     * @format GUID
     */
    id?: string | null;
    /**
     * Name of the service connected to the booking form.
     * @maxLength 400
     * @minLength 1
     */
    name?: string | null;
}
interface CountServicesRequest {
    /**
     * Query filter to base the count on. See supported filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     */
    filter?: Record<string, any> | null;
}
interface CountServicesResponse {
    /** Number of services matching the specified filter. */
    count?: number;
}
interface QueryLocationsRequest {
    /** Filter. */
    filter?: QueryLocationsFilter;
}
interface QueryLocationsFilter {
    /**
     * Service filter. See supported filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     */
    services?: Record<string, any> | null;
    /**
     * List of business IDs to filter by.
     * @format GUID
     * @maxSize 100
     */
    businessLocationIds?: string[];
}
interface QueryLocationsResponse {
    /**
     * Retrieved business locations and whether each location is connected to at
     * least one service.
     */
    businessLocations?: BusinessLocations;
    /**
     * Retrieved custom locations and whether each location is connected to at
     * least one service.
     */
    customLocations?: CustomLocations;
    /**
     * Retrieved customer locations and whether each location is connected to at
     * least one service.
     */
    customerLocations?: CustomerLocations;
}
interface BusinessLocations {
    /**
     * Whether at least one service matching the filter is connected to any of the
     * retrieved business locations.
     */
    exists?: boolean;
    /**
     * Retrieved business locations.
     * @maxSize 100
     */
    locations?: Location[];
}
interface CustomLocations {
    /**
     * Whether at least one service matching the filter is connected to any of the
     * retrieved custom locations.
     */
    exists?: boolean;
}
interface CustomerLocations {
    /**
     * Whether at least one service matching the filter is connected to any of the
     * retrieved customer locations.
     */
    exists?: boolean;
}
interface QueryCategoriesRequest {
    /** Filter. */
    filter?: QueryCategoriesFilter;
}
interface QueryCategoriesFilter {
    /**
     * Service filter. See supported filters
     * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))
     * for more details.
     */
    services?: Record<string, any> | null;
    /**
     * List of category IDs to filter by.
     * @format GUID
     * @maxSize 100
     */
    categoryIds?: string[];
}
interface QueryCategoriesResponse {
    /** Retrieved categories. */
    categories?: V2Category[];
}
interface SetServiceLocationsRequest {
    /**
     * ID of the service.
     * @format GUID
     */
    serviceId: string;
    /**
     * List of locations replacing existing service locations. Omitting an existing location removes it from the service. Specify `removedLocationSessionsAction` to determine the handling of future sessions scheduled at that location.
     * @maxSize 100
     */
    locations: Location[];
    /**
     * The action to perform on sessions currently set to a removed location. For
     * example, move existing sessions to a new specified location.
     * If not specified, sessions will not be moved to a new location.
     */
    removedLocationSessionsAction?: RemovedLocationSessionsAction;
    /**
     * Whether to notify participants about the change of location, and an
     * Optional custom message. The notification is sent only to participants of sessions that are affected by the change.
     */
    participantNotification?: ParticipantNotification;
}
interface RemovedLocationSessionsAction extends RemovedLocationSessionsActionActionOptionsOneOf {
    /**
     * Details about the new location of future events that were scheduled to take
     * place at a removed location.
     */
    moveToLocationOptions?: MoveToNewLocationsOptions;
    /** Information about what to do with future events at the removed location. */
    action?: ActionWithLiterals;
}
/** @oneof */
interface RemovedLocationSessionsActionActionOptionsOneOf {
    /**
     * Details about the new location of future events that were scheduled to take
     * place at a removed location.
     */
    moveToLocationOptions?: MoveToNewLocationsOptions;
}
declare enum Action {
    UNKNOWN_ACTION_TYPE = "UNKNOWN_ACTION_TYPE",
    /** Retain all future sessions at their current location. This is the default. */
    KEEP_AT_CURRENT_LOCATION = "KEEP_AT_CURRENT_LOCATION",
    /** Move future events to a new location. */
    MOVE_TO_LOCATION = "MOVE_TO_LOCATION",
    /**
     * Cancel all future events at the removed location.
     * Currently not supported.
     */
    DELETE = "DELETE"
}
/** @enumType */
type ActionWithLiterals = Action | 'UNKNOWN_ACTION_TYPE' | 'KEEP_AT_CURRENT_LOCATION' | 'MOVE_TO_LOCATION' | 'DELETE';
interface MoveToNewLocationsOptions {
    /** The new location to move existing sessions currently set to a removed location, used when `action` is `MOVE_TO_LOCATION`. */
    newLocation?: Location;
}
interface SetServiceLocationsResponse {
    /** The updated service with the newly set locations. */
    service?: Service;
}
interface EnablePricingPlansForServiceRequest {
    /**
     * ID of the service to update.
     * @format GUID
     */
    serviceId: string;
    /**
     * IDs of the *pricing plans*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * to add to the service's `payment.pricingPlanIds` array.
     * @format GUID
     * @maxSize 100
     */
    pricingPlanIds: string[];
}
interface EnablePricingPlansForServiceResponse {
    /** Updated service. */
    service?: Service;
}
interface DisablePricingPlansForServiceRequest {
    /**
     * ID of the service to update.
     * @format GUID
     */
    serviceId: string;
    /**
     * IDs of the *pricing plans*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))
     * to remove from the service's `payment.pricingPlanIds` array.
     * @format GUID
     * @maxSize 75
     */
    pricingPlanIds?: string[];
}
interface DisablePricingPlansForServiceResponse {
    /** Updated service. */
    service?: Service;
}
interface SetCustomSlugRequest {
    /**
     * ID of the service for which to update the active slug.
     * @format GUID
     */
    serviceId: string;
    /**
     * Slug to set as the active service slug.
     * @maxLength 500
     */
    slug?: string;
}
interface SetCustomSlugResponse {
    /** Updated active service slug. */
    slug?: Slug;
    /** Updated service. */
    service?: Service;
}
interface ValidateSlugRequest {
    /**
     * IO of the service to check custom slug validity for.
     * @format GUID
     */
    serviceId: string;
    /**
     * Custom slug to validate.
     * @maxLength 500
     */
    slug?: string;
}
interface ValidateSlugResponse {
    /** Whether the slug is valid. */
    valid?: boolean;
    /**
     * Valid slug. Available only if `{"valid": true}`.
     * @maxLength 500
     */
    slug?: string | null;
    /**
     * Reasons why the slug is invalid. Available only if `{"valid": false}`.
     * @maxSize 3
     */
    errors?: InvalidSlugErrorWithLiterals[];
}
declare enum InvalidSlugError {
    /** Unknown slug error. */
    UNKNOWN_SLUG_ERROR = "UNKNOWN_SLUG_ERROR",
    /** Slug contains illegal characters. */
    SLUG_CONTAINS_ILLEGAL_CHARACTERS = "SLUG_CONTAINS_ILLEGAL_CHARACTERS",
    /** Slug is already associated with another service. */
    SLUG_ALREADY_EXISTS = "SLUG_ALREADY_EXISTS"
}
/** @enumType */
type InvalidSlugErrorWithLiterals = InvalidSlugError | 'UNKNOWN_SLUG_ERROR' | 'SLUG_CONTAINS_ILLEGAL_CHARACTERS' | 'SLUG_ALREADY_EXISTS';
interface CloneServiceRequest {
    /**
     * ID of the service to clone.
     * @format GUID
     */
    sourceServiceId: string;
}
interface CloneServiceResponse {
    /** Cloned service. */
    service?: Service;
    /**
     * Information about connected entities that couldn't be cloned. For example,
     * future recurring events, the booking form, service variants, and connected
     * pricing plans.
     */
    errors?: CloneErrorsWithLiterals[];
}
declare enum CloneErrors {
    /**
     * Failed to clone the original service's *options and variants*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     */
    OPTIONS_AND_VARIANTS = "OPTIONS_AND_VARIANTS",
    /** Failed to clone the original service's *booking form*. */
    FORM = "FORM"
}
/** @enumType */
type CloneErrorsWithLiterals = CloneErrors | 'OPTIONS_AND_VARIANTS' | 'FORM';

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createAddOnGroup(): __PublicMethodMetaInfo<'POST', {}, CreateAddOnGroupRequest$1, CreateAddOnGroupRequest, CreateAddOnGroupResponse$1, CreateAddOnGroupResponse>;
declare function deleteAddOnGroup(): __PublicMethodMetaInfo<'POST', {}, DeleteAddOnGroupRequest$1, DeleteAddOnGroupRequest, DeleteAddOnGroupResponse$1, DeleteAddOnGroupResponse>;
declare function updateAddOnGroup(): __PublicMethodMetaInfo<'POST', {}, UpdateAddOnGroupRequest$1, UpdateAddOnGroupRequest, UpdateAddOnGroupResponse$1, UpdateAddOnGroupResponse>;
declare function listAddOnGroupsByServiceId(): __PublicMethodMetaInfo<'POST', {}, ListAddOnGroupsByServiceIdRequest$1, ListAddOnGroupsByServiceIdRequest, ListAddOnGroupsByServiceIdResponse$1, ListAddOnGroupsByServiceIdResponse>;
declare function setAddOnsForGroup(): __PublicMethodMetaInfo<'POST', {}, SetAddOnsForGroupRequest$1, SetAddOnsForGroupRequest, SetAddOnsForGroupResponse$1, SetAddOnsForGroupResponse>;
declare function createService(): __PublicMethodMetaInfo<'POST', {}, CreateServiceRequest$1, CreateServiceRequest, CreateServiceResponse$1, CreateServiceResponse>;
declare function bulkCreateServices(): __PublicMethodMetaInfo<'POST', {}, BulkCreateServicesRequest$1, BulkCreateServicesRequest, BulkCreateServicesResponse$1, BulkCreateServicesResponse>;
declare function getService(): __PublicMethodMetaInfo<'GET', {
    serviceId: string;
}, GetServiceRequest$1, GetServiceRequest, GetServiceResponse$1, GetServiceResponse>;
declare function updateService(): __PublicMethodMetaInfo<'PATCH', {
    serviceId: string;
}, UpdateServiceRequest$1, UpdateServiceRequest, UpdateServiceResponse$1, UpdateServiceResponse>;
declare function bulkUpdateServices(): __PublicMethodMetaInfo<'POST', {}, BulkUpdateServicesRequest$1, BulkUpdateServicesRequest, BulkUpdateServicesResponse$1, BulkUpdateServicesResponse>;
declare function bulkUpdateServicesByFilter(): __PublicMethodMetaInfo<'POST', {}, BulkUpdateServicesByFilterRequest$1, BulkUpdateServicesByFilterRequest, BulkUpdateServicesByFilterResponse$1, BulkUpdateServicesByFilterResponse>;
declare function deleteService(): __PublicMethodMetaInfo<'DELETE', {
    serviceId: string;
}, DeleteServiceRequest$1, DeleteServiceRequest, DeleteServiceResponse$1, DeleteServiceResponse>;
declare function bulkDeleteServices(): __PublicMethodMetaInfo<'POST', {}, BulkDeleteServicesRequest$1, BulkDeleteServicesRequest, BulkDeleteServicesResponse$1, BulkDeleteServicesResponse>;
declare function bulkDeleteServicesByFilter(): __PublicMethodMetaInfo<'POST', {}, BulkDeleteServicesByFilterRequest$1, BulkDeleteServicesByFilterRequest, BulkDeleteServicesByFilterResponse$1, BulkDeleteServicesByFilterResponse>;
declare function queryServices(): __PublicMethodMetaInfo<'POST', {}, QueryServicesRequest$1, QueryServicesRequest, QueryServicesResponse$1, QueryServicesResponse>;
declare function searchServices(): __PublicMethodMetaInfo<'POST', {}, SearchServicesRequest$1, SearchServicesRequest, SearchServicesResponse$1, SearchServicesResponse>;
declare function queryPolicies(): __PublicMethodMetaInfo<'POST', {}, QueryPoliciesRequest$1, QueryPoliciesRequest, QueryPoliciesResponse$1, QueryPoliciesResponse>;
declare function queryBookingForms(): __PublicMethodMetaInfo<'POST', {}, QueryBookingFormsRequest$1, QueryBookingFormsRequest, QueryBookingFormsResponse$1, QueryBookingFormsResponse>;
declare function countServices(): __PublicMethodMetaInfo<'POST', {}, CountServicesRequest$1, CountServicesRequest, CountServicesResponse$1, CountServicesResponse>;
declare function queryLocations(): __PublicMethodMetaInfo<'POST', {}, QueryLocationsRequest$1, QueryLocationsRequest, QueryLocationsResponse$1, QueryLocationsResponse>;
declare function queryCategories(): __PublicMethodMetaInfo<'POST', {}, QueryCategoriesRequest$1, QueryCategoriesRequest, QueryCategoriesResponse$1, QueryCategoriesResponse>;
declare function setServiceLocations(): __PublicMethodMetaInfo<'POST', {
    serviceId: string;
}, SetServiceLocationsRequest$1, SetServiceLocationsRequest, SetServiceLocationsResponse$1, SetServiceLocationsResponse>;
declare function enablePricingPlansForService(): __PublicMethodMetaInfo<'POST', {
    serviceId: string;
}, EnablePricingPlansForServiceRequest$1, EnablePricingPlansForServiceRequest, EnablePricingPlansForServiceResponse$1, EnablePricingPlansForServiceResponse>;
declare function disablePricingPlansForService(): __PublicMethodMetaInfo<'POST', {
    serviceId: string;
}, DisablePricingPlansForServiceRequest$1, DisablePricingPlansForServiceRequest, DisablePricingPlansForServiceResponse$1, DisablePricingPlansForServiceResponse>;
declare function setCustomSlug(): __PublicMethodMetaInfo<'POST', {
    serviceId: string;
}, SetCustomSlugRequest$1, SetCustomSlugRequest, SetCustomSlugResponse$1, SetCustomSlugResponse>;
declare function validateSlug(): __PublicMethodMetaInfo<'POST', {}, ValidateSlugRequest$1, ValidateSlugRequest, ValidateSlugResponse$1, ValidateSlugResponse>;
declare function cloneService(): __PublicMethodMetaInfo<'POST', {}, CloneServiceRequest$1, CloneServiceRequest, CloneServiceResponse$1, CloneServiceResponse>;

export { type __PublicMethodMetaInfo, bulkCreateServices, bulkDeleteServices, bulkDeleteServicesByFilter, bulkUpdateServices, bulkUpdateServicesByFilter, cloneService, countServices, createAddOnGroup, createService, deleteAddOnGroup, deleteService, disablePricingPlansForService, enablePricingPlansForService, getService, listAddOnGroupsByServiceId, queryBookingForms, queryCategories, queryLocations, queryPolicies, queryServices, searchServices, setAddOnsForGroup, setCustomSlug, setServiceLocations, updateAddOnGroup, updateService, validateSlug };
