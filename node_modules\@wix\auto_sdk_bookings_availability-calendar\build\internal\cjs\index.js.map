{"version": 3, "sources": ["../../../index.ts", "../../../src/bookings-availability-v1-slot-availability-availability-calendar.universal.ts", "../../../src/bookings-availability-v1-slot-availability-availability-calendar.http.ts", "../../../src/bookings-availability-v1-slot-availability-availability-calendar.public.ts", "../../../src/bookings-availability-v1-slot-availability-availability-calendar.context.ts"], "sourcesContent": ["export * from './src/bookings-availability-v1-slot-availability-availability-calendar.context.js';\n", "import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameKeysFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport * as ambassadorWixBookingsAvailabilityV1SlotAvailability from './bookings-availability-v1-slot-availability-availability-calendar.http.js';\n\nexport interface SlotAvailability {\n  /**\n   * The slot for the corresponding session, when the session is either a single session\n   * or a specific session generated from a recurring session.\n   */\n  slot?: Slot;\n  /**\n   * Whether the slot is bookable. Bookability is determined by checking a\n   * session's open slots and booking policies. Locks are not taken into\n   * account.\n   */\n  bookable?: boolean;\n  /**\n   * Total number of spots for this slot.\n   * For example, if a session has a total of 10 spots and 3 spots are booked,\n   * `spotsTotal` is 10 and `openSpots` is 7.\n   */\n  totalSpots?: number | null;\n  /** Number of open spots for this slot. */\n  openSpots?: number | null;\n  /** An object describing the slot's waitlist and its occupancy. */\n  waitingList?: AvailabilityWaitingList;\n  /** Booking policy violations for the slot. */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /**\n   * Indicates whether the slot is locked because a waitlist exists.\n   * When a slot frees up, the slot is offered to the next customer on the waitlist. Read-only.\n   */\n  locked?: boolean | null;\n  /**\n   * Deprecated. not in use since 2025-06-15.\n   * @deprecated Deprecated. not in use since 2025-06-15.\n   * @targetRemovalDate 2025-06-15\n   */\n  isFromV2?: boolean;\n}\n\nexport interface Slot {\n  /**\n   * ID for the slot's corresponding session, when the session is either a single session\n   * or a specific session generated from a recurring session.\n   *\n   * Deprecated. Please use `eventId` instead.\n   * @deprecated ID for the slot's corresponding session, when the session is either a single session\n   * or a specific session generated from a recurring session.\n   *\n   * Deprecated. Please use `eventId` instead.\n   * @replacedBy event_id\n   * @targetRemovalDate 2025-09-30\n   */\n  sessionId?: string | null;\n  /** Service ID. */\n  serviceId?: string;\n  /** Schedule ID. */\n  scheduleId?: string;\n  /**\n   * The start time of this slot in [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339)\n   * format.\n   *\n   * If `timezone` is specified,\n   * dates are based on the local date/time. This means that the timezone offset\n   * in the `start_date` is ignored.\n   */\n  startDate?: string | null;\n  /**\n   * The end time of this slot in\n   * [RFC 3339](https://www.rfc-editor.org/rfc/rfc3339) format.\n   *\n   * If `timezone` is specified,\n   * dates are based on the local date/time. This means that the timezone offset\n   * in the `end_date` is ignored.\n   */\n  endDate?: string | null;\n  /**\n   * The timezone for which slot availability is to be calculated.\n   *\n   * Learn more about [handling Daylight Savings Time (DST) for local time zones](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_handling-daylight-savings-time-dst-for-local-time-zones)\n   * when calculating availability.\n   */\n  timezone?: string | null;\n  /**\n   * The resource required for this slot. Currently, the only supported resource\n   * is the relevant staff member for the slot.\n   */\n  resource?: SlotResource;\n  /** Geographic location of the slot. */\n  location?: Location;\n  /**\n   * ID for the slot's corresponding event, when the event is either a single event\n   * or a specific event generated from a recurring event.\n   * @minLength 36\n   * @maxLength 250\n   */\n  eventId?: string | null;\n}\n\nexport interface SlotResource {\n  /**\n   * Resource ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Resource name. Read only.\n   * @maxLength 1200\n   */\n  name?: string | null;\n}\n\nexport interface Location {\n  /**\n   * Business location ID. Available only for locations that are business locations,\n   * meaning the `location_type` is `\"OWNER_BUSINESS\"`.\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Location name. */\n  name?: string | null;\n  /** The full address of this location. */\n  formattedAddress?: string | null;\n  /**\n   * The full translated address of this location.\n   * @maxLength 512\n   */\n  formattedAddressTranslated?: string | null;\n  /**\n   * Location type.\n   *\n   * - `\"OWNER_BUSINESS\"`: The business address, as set in the site’s general settings.\n   * - `\"OWNER_CUSTOM\"`: The address as set when creating the service.\n   * - `\"CUSTOM\"`: The address as set for the individual session.\n   */\n  locationType?: LocationTypeWithLiterals;\n}\n\nexport enum LocationType {\n  UNDEFINED = 'UNDEFINED',\n  OWNER_BUSINESS = 'OWNER_BUSINESS',\n  OWNER_CUSTOM = 'OWNER_CUSTOM',\n  CUSTOM = 'CUSTOM',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNDEFINED'\n  | 'OWNER_BUSINESS'\n  | 'OWNER_CUSTOM'\n  | 'CUSTOM';\n\nexport interface AvailabilityWaitingList {\n  /**\n   * Total number of spots and open spots for this waitlist.\n   * For example, a Yoga class with 10 waitlist spots and 3 registered\n   * on the waitlist has 10 `total_spots` and 7 `open_spots`.\n   */\n  totalSpots?: number | null;\n  /** Number of open spots for this waitlist. */\n  openSpots?: number | null;\n}\n\nexport interface BookingPolicyViolations {\n  /** Bookings policy violation. Too early to book this slot. */\n  tooEarlyToBook?: boolean | null;\n  /** Bookings policy violation. Too late to book this slot. */\n  tooLateToBook?: boolean | null;\n  /** Bookings policy violation. Online booking is disabled for this slot. */\n  bookOnlineDisabled?: boolean | null;\n}\n\nexport interface NestedTimeSlot {\n  /** @format GUID */\n  serviceId?: string;\n  /** @maxLength 30 */\n  start?: string;\n  /** @maxLength 30 */\n  end?: string;\n  resource?: SlotResource;\n  /** Schedule ID. */\n  scheduleId?: string;\n}\n\nexport interface QueryAvailabilityRequest {\n  /**\n   * Query options. Refer to the\n   * _supported filters article_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))\n   * for a complete list of supported filters.\n   */\n  query: QueryV2;\n  /**\n   * Time zone override for the `endDate` and `startDate` filters, in\n   * [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database), such as\n   * `America/New_York` or `UTC`.\n   *\n   * For example, if you specify `(\"startDate\": \"2025-11-25T17:00:00+01:00\"}` (note\n   * the +01:00 offset) in combination with `{\"timezone\": \"Europe/Bucharest\"}`\n   * (+02:00 offset), *Query Availability* adjusts the effective start time to\n   * reflect the specified time zone. In this case, the start time in UTC would\n   * be `2025-11-25T15:00:00` (using the +02:00 offset).\n   *\n   * Learn more about *handling Daylight Savings Time*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst) | [REST](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst)).\n   *\n   * Default: No modification applied to the `endDate` and `startDate` filters.\n   */\n  timezone?: string | null;\n  /**\n   * Maximum number of appointment slots to return for each date. For example, if\n   * you specify `{\"slotsPerDay\": 3}`, a maximum of 3 appointment slots/class events is\n   * returned for each day that's within the query filter's date range.\n   */\n  slotsPerDay?: number | null;\n}\n\nexport interface QueryV2 extends QueryV2PagingMethodOneOf {\n  /**\n   * Filter object. For a list of\n   * fields you can filter by, see Availability Calendar supported filters ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting)).\n   * You must include `serviceId`, `startDate` and `endDate` in the filter. This avoids large results that can impact performance.\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort options.\n   * Currently, only sorting by `startDate` is supported. For details\n   * on sorting, see Availability Calendar supported sorting ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting#sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting#sorting)).\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface QueryV2PagingMethodOneOf {}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface CursorPaging {\n  /**\n   * Maximum number of items to return in the results.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\n/**\n * Selected customer choices.\n * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.\n */\nexport interface V2CustomerChoices {\n  /**\n   * The selected customer duration in minutes.\n   * Min: `1 minute`\n   * Max: `44639 minutes` (30 days, 23 hours, and 59 minutes)\n   * @min 1\n   * @max 44639\n   */\n  durationInMinutes?: number | null;\n  /**\n   * The selected add-ons IDs.\n   * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.\n   * @format GUID\n   * @maxSize 21\n   */\n  addOnIds?: string[] | null;\n  /**\n   * The selected duration choice ID.\n   * @format GUID\n   */\n  durationChoiceId?: string | null;\n}\n\nexport interface QueryAvailabilityResponse {\n  /** List of appointment slots or class events matching the filter. */\n  availabilityEntries?: SlotAvailability[];\n}\n\nexport interface PagingMetadataV2 {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  offset?: number | null;\n  /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */\n  total?: number | null;\n  /** Flag that indicates the server failed to calculate the `total` field. */\n  tooManyToCount?: boolean | null;\n  /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */\n  cursors?: Cursors;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor string pointing to the next page in the list of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor pointing to the previous page in the list of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface GetSlotAvailabilityRequest {\n  /** The slot for which the availability is checked. */\n  slot?: Slot;\n  /**\n   * The timezone for which slot availability is to be calculated. If specified,\n   * dates are based on the local date/time, meaning that the timezone offset\n   * in the date's format is ignored.\n   *\n   * Learn more about [handling Daylight Savings Time (DST) for local time zones](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_handling-daylight-savings-time-dst-for-local-time-zones)\n   * when calculating availability.\n   */\n  timezone?: string | null;\n}\n\nexport interface GetSlotAvailabilityResponse {\n  availability?: SlotAvailability;\n  bookingPolicySettings?: BookingPolicySettings;\n}\n\nexport interface BookingPolicySettings {\n  /**\n   * The policy defining the maximum number of participants that can\n   * be booked for a slot or a schedule.\n   */\n  maxParticipantsPerBooking?: number | null;\n}\n\nexport interface GetScheduleAvailabilityRequest {\n  /**\n   * The schedule ID for which availability is being checked.\n   * @format GUID\n   */\n  scheduleId: string;\n}\n\nexport interface GetScheduleAvailabilityResponse {\n  availability?: ScheduleAvailability;\n  bookingPolicySettings?: BookingPolicySettings;\n}\n\nexport interface ScheduleAvailability {\n  /**\n   * The total number of spots defined for the schedule, including\n   * both open and non-available spots.\n   */\n  totalSpots?: number | null;\n  /** The number of open spots defined for the schedule. */\n  openSpots?: number | null;\n  /** Booking policy violations for the schedule. */\n  bookingPolicyViolations?: BookingPolicyViolations;\n}\n\nexport interface CalculateMultiSlotAvailabilityRequest {\n  /** @maxLength 30 */\n  from?: string;\n  /** @maxLength 30 */\n  to?: string;\n  /** @maxLength 30 */\n  timeZone?: string;\n  /** TODO good definition of what bookable means https://github.com/wix-private/scheduler/pull/18267/files?file-filters%5B%5D=.proto&show-viewed-files=true#r1199809006 */\n  bookable?: boolean | null;\n  /**\n   * each nested field is checked on its own. i.e. if `too_early_to_book` is defined and `too_late_to_book` is not defined\n   * we will return slots for which `too_early_to_book` is same as on the request, regardless of `too_late_to_book`.\n   */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /**\n   * support filtering by location type, or by locationId. Other fields like `name` are ignored\n   * must be set, and must have locationType. If locationType is `OWNER_BUSINESS`, must have location_id\n   */\n  location?: Location;\n  /**\n   * @minSize 1\n   * @maxSize 6\n   */\n  slots?: RuleBasedConstraints[];\n  /**\n   * Maximum number of slots to load for each date. For example, if `slots_per_day` is set to `3`,\n   * at most 3 available slots are returned for each day in the date range specified in the query's\n   * `filter`.\n   *\n   * When a day has both bookable and non-bookable slots, bookable slots are returned first.\n   * Non-bookable slots are returned according to the specified filters, after all\n   * bookable slots are already included.\n   */\n  slotsPerDay?: number | null;\n  cursorPaging?: CursorPaging;\n}\n\nexport interface RuleBasedConstraints {\n  /** @format GUID */\n  serviceId?: string;\n  resourcesFilter?: ResourcesFilter;\n  /** will be passed to availability-2, and to the availability-constraints SPI */\n  customerChoices?: AvailabilityV2CustomerChoices;\n}\n\nexport interface ResourcesFilter {\n  resourceIds?: string[];\n}\n\n/**\n * Selected customer choices.\n * These choices are selected by the customer during the booking flow and can be utilized to calculate the corresponding service's configuration properties.\n */\nexport interface AvailabilityV2CustomerChoices {\n  /**\n   * Selected customer duration in minutes.\n   * Min: `1` minute\n   * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)\n   * Default: `15` minutes\n   * @min 1\n   * @max 44639\n   */\n  durationInMinutes?: number | null;\n  /**\n   * Selected add-on IDs.\n   *\n   * Max: Calculated as the product of the maximum number of add-on groups multiplied by the maximum number of add-ons per group. Currently 21 (3 groups × 7 add-ons per group), but may change in the future.\n   * @format GUID\n   * @maxSize 21\n   */\n  addOnIds?: string[] | null;\n  /**\n   * Selected duration choice ID.\n   * @format GUID\n   */\n  durationChoiceId?: string | null;\n}\n\nexport interface CalculateMultiSlotAvailabilityResponse {\n  slots?: SlotAvailability[];\n  cursorPagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface CursorPagingMetadata {\n  /** Number of items returned in current page. */\n  count?: number | null;\n  /** Cursor strings that point to the next page, previous page, or both. */\n  cursors?: Cursors;\n  /**\n   * Whether there are more pages to retrieve following the current page.\n   *\n   * + `true`: Another page of results can be retrieved.\n   * + `false`: This is the last page.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface GetAvailabilityTimeSlotRequest {\n  /**\n   * Service ID of the time slot.\n   * Currently supported only for services of type `APPOINTMENT`.\n   * @format GUID\n   */\n  serviceId?: string;\n  /**\n   * Local start date of the time slot, in ISO-8601 format.\n   * For example, \"2024-01-30T13:30:00\".\n   * @format LOCAL_DATE_TIME\n   */\n  localStartDate?: string;\n  /**\n   * Local end date of the time slot, in ISO-8601 format.\n   * For example, \"2024-01-30T14:30:00\".\n   * @format LOCAL_DATE_TIME\n   */\n  localEndDate?: string;\n  /**\n   * Time zone, in IANA time zone format.\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n  /**\n   * The location of the time slot.\n   *\n   * You must provide a specific `locationType`.\n   * If locationType is `BUSINESS`, you __must__ also provide a `locationId`.\n   *\n   * <blockquote class=\"warning\">\n   * <p>Supports filtering by location type, or by location ID.\n   * Other fields like <code class=\"grey-background\">name</code> are ignored.</p>\n   * </blockquote>\n   */\n  location?: TimeSlotLocation;\n  /**\n   * Resources to include in response.\n   *\n   * If specified,\n   * the returned `TimeSlot` contains only the available resources out of provided list.\n   * Otherwise, the returned `TimeSlot` returned with all possible `AvailableResources`.\n   * @format GUID\n   * @maxSize 135\n   */\n  resourceIds?: string[];\n  /**\n   * Resource type IDs to include in response, this is in addition to the requested `resourceIds`.\n   *\n   * If specified in request, the returned `TimeSlot` contains only the `AvailableResources` with `ResourceTypeId`\n   * out of those specified, each contains all the available resources of this type.\n   * @format GUID\n   * @maxSize 100\n   */\n  includeResourceTypeIds?: string[];\n  /**\n   * Deprecated. not in use since 2025-06-15.\n   * @deprecated Deprecated. not in use since 2025-06-15.\n   * @targetRemovalDate 2025-06-15\n   */\n  fromV2?: boolean | null;\n}\n\nexport interface TimeSlotLocation {\n  /**\n   * Business Location ID. Present only if the location is a business location.\n   * @format GUID\n   */\n  _id?: string | null;\n  /**\n   * The location name.\n   * @maxLength 250\n   */\n  name?: string | null;\n  /**\n   * A string representation for the full address of the location.\n   * @maxLength 300\n   */\n  formattedAddress?: string | null;\n  /**\n   * The type of location:\n   * - `CUSTOM`: The location is specific to this service, and is not derived from the business location.\n   * - `BUSINESS`: A business location, either the default business address, or locations defined for the business by the Business Info.\n   * - `CUSTOMER`: The location is determined by the customer and is not set up beforehand.\n   */\n  locationType?: LocationLocationTypeWithLiterals;\n}\n\nexport enum LocationLocationType {\n  UNKNOWN_LOCATION_TYPE = 'UNKNOWN_LOCATION_TYPE',\n  /** A business location, either the default business address, or locations defined for the business by the Business Info. */\n  BUSINESS = 'BUSINESS',\n  /** The location is unique to this service and isn't defined as one of the business locations. */\n  CUSTOM = 'CUSTOM',\n  /** The location can be determined by the customer and is not set up beforehand. */\n  CUSTOMER = 'CUSTOMER',\n}\n\n/** @enumType */\nexport type LocationLocationTypeWithLiterals =\n  | LocationLocationType\n  | 'UNKNOWN_LOCATION_TYPE'\n  | 'BUSINESS'\n  | 'CUSTOM'\n  | 'CUSTOMER';\n\nexport interface CustomerChoices {\n  /**\n   * The selected add-ons IDs.\n   * Max: Derived from max amount of Add-On groups * max amount of Add-Ons per group.\n   * @format GUID\n   * @maxSize 21\n   */\n  addOnIds?: string[] | null;\n  /**\n   * The selected duration choice ID.\n   * @format GUID\n   */\n  durationChoiceId?: string | null;\n}\n\nexport interface GetAvailabilityTimeSlotResponse {\n  /** Time slot. */\n  timeSlot?: TimeSlot;\n  /**\n   * Time zone, in IANA time zone format.\n   * @minLength 1\n   * @maxLength 150\n   */\n  timeZone?: string | null;\n}\n\n/**\n * The `TimeSlot` object represents the availability information\n * for an `Appointment` service's specific slot, including:\n *\n * 1. Whether the slot is bookable for the given service?\n *\n * 2. In what location the service is available for this slot?\n *\n * 3. Which available resources can provide the service for this slot?\n *\n * 4. Does booking the slot for the service violates any of the service booking policies?\n *\n * 5. What is the total capacity and remaining capacity of the service at the time of the calculation of the `TimeSlot`?\n *\n * > __Note:__\n * > When the `TimeSlot` has a non empty `NestedTimeSlots`, it represents the availability information\n * > for a given list of `Appointment` services within a specific time slot.\n */\nexport interface TimeSlot {\n  /**\n   * Service ID.\n   *\n   * > Not returned from `MultiServiceAvailabilityTimeSlots` API calls.\n   * > Instead, each nested time slot has its own serviceId.\n   * @format GUID\n   */\n  serviceId?: string | null;\n  /**\n   * Local start date of the time slot in ISO-8601 format.\n   * For example, \"2024-01-30T13:30:00\".\n   * @format LOCAL_DATE_TIME\n   */\n  localStartDate?: string | null;\n  /**\n   * Local end date of the time slot in ISO-8601 format.\n   * For example, \"2024-01-30T14:30:00\".\n   * @format LOCAL_DATE_TIME\n   */\n  localEndDate?: string | null;\n  /**\n   * Whether the slot is bookable according to the service's booking policies.\n   *\n   * If booking this time slot does not violates any of the service's booking policies,\n   * the returned value is `true`. Otherwise, returns `false`.\n   */\n  bookable?: boolean | null;\n  /** The geographic location of the slot. */\n  location?: TimeSlotLocation;\n  /**\n   * Total number of spots for the slot.\n   * @min 1\n   * @max 1000\n   */\n  totalCapacity?: number | null;\n  /**\n   * Remaining number of spots for the slot.\n   * For example, for an appointment service with total capacity of 1 spot and one booked spot, the remaining capacity will be 0.\n   * @max 1000\n   */\n  remainingCapacity?: number | null;\n  /**\n   * Indicators for booking policy violations for the slot.\n   *\n   * Each nested field is checked on its own. i.e. if `tooEarlyToBook` is defined and `bookOnlineDisabled` is not defined\n   * we will return also slots for which `tooEarlyToBook` is same as on the request, regardless of `bookOnlineDisabled`.\n   */\n  bookingPolicyViolations?: Service_availabilityBookingPolicyViolations;\n  /**\n   * List of `AvailableResources` for the time slot.\n   * Each `AvailableResources` contains information about available resources of the same type.\n   *\n   * > Not returned from `MultiServiceAvailabilityTimeSlots` API calls.\n   * > Instead, each nested time slot has its own available resources.\n   */\n  availableResources?: AvailableResources[];\n  /**\n   * > Nested time slots.\n   * > Returned only from `MultiServiceAvailabilityTimeSlots` API calls.\n   * @maxSize 8\n   */\n  nestedTimeSlots?: Service_availabilityNestedTimeSlot[];\n}\n\n/** relevant for event based slots, and not for availability based slots */\nexport interface EventInfo {}\n\nexport interface WaitingList {\n  /**\n   * Total number of spots in this wait list.\n   * @min 1\n   */\n  totalCapacity?: number | null;\n  /**\n   * Number of remaining spots for this wait list.\n   * For example, a Yoga event with 10 waitList spots and 3 registered\n   * on the waitList has 10 `total_capacity` and 7 `remaining_capacity`.\n   */\n  remainingCapacity?: number | null;\n}\n\nexport interface Service_availabilityBookingPolicyViolations {\n  /** Bookings policy violation. Too early to book this slot. */\n  tooEarlyToBook?: boolean | null;\n  /** Bookings policy violation. Too late to book this slot. */\n  tooLateToBook?: boolean | null;\n  /** Bookings policy violation. Online booking is disabled for the `TimeSlot` service. */\n  bookOnlineDisabled?: boolean | null;\n}\n\nexport interface AvailableResources {\n  /**\n   * Resource type ID.\n   * @format GUID\n   */\n  resourceTypeId?: string | null;\n  /**\n   * Available resources for the time slot.\n   *\n   * + When returned from `ListAvailabilityTimeSlots`, empty by default.\n   * + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,\n   * contains __up__ to 10 available resources out of those provided.\n   *\n   * + When returned from `GetAvailabilityTimeSlots`, contains all available resources by default.\n   * + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,\n   * contains all of the available resources out of those provided.\n   *\n   *\n   * > + When returned from `ListMultiServiceAvailabilityTimeSlots`, empty by default.\n   * > + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,\n   * > contains __up__ to 10 available resources out of those provided.\n   *\n   * > + When returned from `GetMultiServiceAvailabilityTimeSlots`, contains all available resources by default.\n   * > + + If you provided `includeResourceTypeIds` or  `resourceIds` in request,\n   * > contains all of the available resources out of those provided.\n   */\n  resources?: Resource[];\n  /**\n   * Whether there are more available resources for the slot that are not listed in `resources` due to size limitations.\n   * @readonly\n   */\n  hasMoreAvailableResources?: boolean | null;\n}\n\nexport interface Resource {\n  /**\n   * Resource ID.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Resource name.\n   * @minLength 1\n   * @maxLength 40\n   */\n  name?: string | null;\n}\n\nexport interface Service_availabilityNestedTimeSlot {\n  /**\n   * Service ID of the nested time slot.\n   * @format GUID\n   */\n  serviceId?: string;\n  /**\n   * Local start date of the nested time slot in ISO-8601 format.\n   * For example, \"2024-01-30T13:30:00\".\n   * @format LOCAL_DATE_TIME\n   */\n  localStartDate?: string;\n  /**\n   * Local end date of the nested time slot in ISO-8601 format.\n   * For example, \"2024-01-30T14:30:00\".\n   * @format LOCAL_DATE_TIME\n   */\n  localEndDate?: string;\n  /**\n   * List of `AvailableResources` for the nested time slot.\n   * Each `AvailableResources` contains information about available resources of the same type.\n   * @maxSize 10\n   */\n  availableResources?: AvailableResources[];\n}\n\n/** @docsIgnore */\nexport type GetScheduleAvailabilityApplicationErrors =\n  | {\n      code?: 'FAILED_VALIDATING_AVAILABILITY';\n      description?: string;\n      data?: Record<string, any>;\n    }\n  | {\n      code?: 'FAILED_RESOLVING_SCHEDULE';\n      description?: string;\n      data?: Record<string, any>;\n    };\n\n/**\n * Retrieves appointment slots or class events that match specified filters.\n *\n * <blockquote>\n *\n * Query Availability doesn't use a query builder, instead it uses\n * [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language).\n *\n * </blockquote>\n *\n * ## Course limitations\n *\n * Course events can't be retrieved with this method. Follow this\n * *sample flow* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/sample-flows#backend-modules_bookings_bookings_book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course))\n * to check course availability.\n *\n * ## Query filter\n *\n * You must include `serviceId` in your query filter. For performance, also specify\n * both `startDate` and `endDate`. Refer to the\n * *supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))\n * for a complete list of supported filters.\n *\n * ## Limit the number of slots returned per day\n *\n * To limit the maximum number of appointment slots returned for each date,\n * specify `slotsPerDay`. For example,  specifying `{\"slotsPerDay\": 3}` returns\n * a maximum of 3 appointment slots/class events for each day that's within the query\n * filter's date range.\n *\n * ## Unavailable slots\n *\n * By default, both available and unavailable appointment slots/ class events\n * are returned. To retrieve only available slots/events, specify\n * `{\"bookable\": true}` in the filter.\n *\n * ## Sorting\n *\n * Slots/events can be sorted only by `startDate`. Unavailable slots/events\n * always appear after available ones.\n *\n * ## Availability calculation\n *\n * Wix Bookings calculates availability differently for appointment-based\n * services and classes.\n *\n * ### Appointment availability\n *\n * An appointment slot is `bookable` if:\n * - The slot's `startDate` is later than or equal to `query.filter.startDate`.\n * - The slot's `endDate` is before or equal to `query.filter.endDate`.\n * - The service's `onlineBooking.enabled` is `true`.\n * - The service's early/late booking policies are either not enabled or the\n *   current time is within the defined limits.\n *\n * ### Class event availability\n *\n * A class event is `bookable` if:\n * - The event's `startDate` is later than or equal to `query.filter.startDate`.\n * - The event's `endDate` is before or equal to `query.filter.endDate`.\n * - The event's `remainingCapacity` is greater than `0`.\n * - The service's `onlineBooking.enabled` is `true`.\n * - The service's early/late booking policies are either not enabled or the\n *   current time is within the defined limits.\n *\n * If you want to restrict returned class events to those with a minimum number\n * of remaining spots, specify `openSpots` in the filter. By default, all class\n * events, even those with no open spot, are returned.\n *\n * ## Daylight Savings Time (DST)\n *\n * Due to DST, some local times may not exist or may occur twice. For example,\n * in Santiago, Chile, on September 7, 2025, 0:05 AM doesn't exist because the\n * clock advances from 0:00 AM to 1:00 AM.\n *\n * *Query Availability* automatically adjusts for these gaps. Non-existent times\n * are moved forward by 1 hour, while existing times remain unchanged. For\n * instance, if you call *Query Availability* with a `startDate` of\n * `2025-09-07T00:00:01.000` and an `endDate` of `2025-09-08T00:00:02.000`, the\n * adjusted query will use `2025-09-07T01:00:01.000` as the new start time.\n * @param query - Query options. Refer to the\n * _supported filters article_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))\n * for a complete list of supported filters.\n * @public\n * @documentationMaturity preview\n * @requiredField query\n * @requiredField query.filter\n * @param options - Options to use for querying availability.\n * @permissionId BOOKINGS.AVAILABILITY_READ\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.availability.AvailabilityCalendar.QueryAvailability\n */\nexport async function queryAvailability(\n  query: NonNullablePaths<QueryV2, `filter`, 2>,\n  options?: QueryAvailabilityOptions\n): Promise<\n  NonNullablePaths<\n    QueryAvailabilityResponse,\n    | `availabilityEntries`\n    | `availabilityEntries.${number}.slot.serviceId`\n    | `availabilityEntries.${number}.slot.scheduleId`\n    | `availabilityEntries.${number}.slot.location.locationType`\n    | `availabilityEntries.${number}.bookable`\n    | `availabilityEntries.${number}.isFromV2`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    query: query,\n    timezone: options?.timezone,\n    slotsPerDay: options?.slotsPerDay,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsAvailabilityV1SlotAvailability.queryAvailability(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          query: '$[0]',\n          timezone: '$[1].timezone',\n          slotsPerDay: '$[1].slotsPerDay',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['query', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface QueryAvailabilityOptions {\n  /**\n   * Time zone override for the `endDate` and `startDate` filters, in\n   * [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database), such as\n   * `America/New_York` or `UTC`.\n   *\n   * For example, if you specify `(\"startDate\": \"2025-11-25T17:00:00+01:00\"}` (note\n   * the +01:00 offset) in combination with `{\"timezone\": \"Europe/Bucharest\"}`\n   * (+02:00 offset), *Query Availability* adjusts the effective start time to\n   * reflect the specified time zone. In this case, the start time in UTC would\n   * be `2025-11-25T15:00:00` (using the +02:00 offset).\n   *\n   * Learn more about *handling Daylight Savings Time*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst) | [REST](https://dev.wix.com/api/rest/wix-bookings/availability-calendar/query-availability#wix-bookings_availability-calendar_query-availability_daylight-savings-time-dst)).\n   *\n   * Default: No modification applied to the `endDate` and `startDate` filters.\n   */\n  timezone?: string | null;\n  /**\n   * Maximum number of appointment slots to return for each date. For example, if\n   * you specify `{\"slotsPerDay\": 3}`, a maximum of 3 appointment slots/class events is\n   * returned for each day that's within the query filter's date range.\n   */\n  slotsPerDay?: number | null;\n}\n\n/**\n * Returns availability for a given schedule ID\n * The availability for a course is calculated by:\n * - Checking for total spots by the schedule's capacity\n * - Checking for open spots by subtracting the current number of participants from the total spots\n * current number of participants is calculated by summing the number of participants of all bookings booked to the schedule\n * @param scheduleId - The schedule ID for which availability is being checked.\n * @public\n * @documentationMaturity preview\n * @requiredField scheduleId\n * @fqn com.wixpress.bookings.availability.AvailabilityCalendar.GetScheduleAvailability\n * @deprecated\n */\nexport async function getScheduleAvailability(scheduleId: string): Promise<\n  GetScheduleAvailabilityResponse & {\n    __applicationErrorsType?: GetScheduleAvailabilityApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    scheduleId: scheduleId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsAvailabilityV1SlotAvailability.getScheduleAvailability(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { scheduleId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['scheduleId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/** @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.AVAILABILITY_READ_MULTI_SLOT\n * @applicableIdentity APP\n * @fqn com.wixpress.bookings.availability.AvailabilityCalendar.CalculateMultiSlotAvailability\n */\nexport async function calculateMultiSlotAvailability(\n  options?: CalculateMultiSlotAvailabilityOptions\n): Promise<\n  NonNullablePaths<\n    CalculateMultiSlotAvailabilityResponse,\n    | `slots`\n    | `slots.${number}.slot.serviceId`\n    | `slots.${number}.slot.scheduleId`\n    | `slots.${number}.slot.location.locationType`\n    | `slots.${number}.bookable`\n    | `slots.${number}.isFromV2`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    from: options?.from,\n    to: options?.to,\n    timeZone: options?.timeZone,\n    bookable: options?.bookable,\n    bookingPolicyViolations: options?.bookingPolicyViolations,\n    location: options?.location,\n    slots: options?.slots,\n    slotsPerDay: options?.slotsPerDay,\n    cursorPaging: options?.cursorPaging,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsAvailabilityV1SlotAvailability.calculateMultiSlotAvailability(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          from: '$[0].from',\n          to: '$[0].to',\n          timeZone: '$[0].timeZone',\n          bookable: '$[0].bookable',\n          bookingPolicyViolations: '$[0].bookingPolicyViolations',\n          location: '$[0].location',\n          slots: '$[0].slots',\n          slotsPerDay: '$[0].slotsPerDay',\n          cursorPaging: '$[0].cursorPaging',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CalculateMultiSlotAvailabilityOptions {\n  /** @maxLength 30 */\n  from?: string;\n  /** @maxLength 30 */\n  to?: string;\n  /** @maxLength 30 */\n  timeZone?: string;\n  /** TODO good definition of what bookable means https://github.com/wix-private/scheduler/pull/18267/files?file-filters%5B%5D=.proto&show-viewed-files=true#r1199809006 */\n  bookable?: boolean | null;\n  /**\n   * each nested field is checked on its own. i.e. if `too_early_to_book` is defined and `too_late_to_book` is not defined\n   * we will return slots for which `too_early_to_book` is same as on the request, regardless of `too_late_to_book`.\n   */\n  bookingPolicyViolations?: BookingPolicyViolations;\n  /**\n   * support filtering by location type, or by locationId. Other fields like `name` are ignored\n   * must be set, and must have locationType. If locationType is `OWNER_BUSINESS`, must have location_id\n   */\n  location?: Location;\n  /**\n   * @minSize 1\n   * @maxSize 6\n   */\n  slots?: RuleBasedConstraints[];\n  /**\n   * Maximum number of slots to load for each date. For example, if `slots_per_day` is set to `3`,\n   * at most 3 available slots are returned for each day in the date range specified in the query's\n   * `filter`.\n   *\n   * When a day has both bookable and non-bookable slots, bookable slots are returned first.\n   * Non-bookable slots are returned according to the specified filters, after all\n   * bookable slots are already included.\n   */\n  slotsPerDay?: number | null;\n  cursorPaging?: CursorPaging;\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    _: [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v2/availability/schedule',\n        destPath: '/v2/availability/schedule',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n      {\n        srcPath: '/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n      {\n        srcPath: '/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n      {\n        srcPath: '/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'editor-flow.wixapps.net': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/bookings/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/bookings/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v2/availability/schedule',\n        destPath: '/v2/availability/schedule',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/multislot',\n        destPath: '/v1/availability/multislot',\n      },\n    ],\n    'bookings.wixapps.net': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/availability-calendar/v1/availability/query',\n        destPath: '/v1/availability/query',\n      },\n    ],\n    '*.wixforms.com': [\n      {\n        srcPath: '/_api/availability-calendar',\n        destPath: '',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/availability-calendar',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_availability-calendar';\n\n/**\n * Retrieves appointment slots or class events that match specified filters.\n *\n * <blockquote>\n *\n * Query Availability doesn't use a query builder, instead it uses\n * [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language).\n *\n * </blockquote>\n *\n * ## Course limitations\n *\n * Course events can't be retrieved with this method. Follow this\n * *sample flow* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/sample-flows#backend-modules_bookings_bookings_book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course))\n * to check course availability.\n *\n * ## Query filter\n *\n * You must include `serviceId` in your query filter. For performance, also specify\n * both `startDate` and `endDate`. Refer to the\n * *supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))\n * for a complete list of supported filters.\n *\n * ## Limit the number of slots returned per day\n *\n * To limit the maximum number of appointment slots returned for each date,\n * specify `slotsPerDay`. For example,  specifying `{\"slotsPerDay\": 3}` returns\n * a maximum of 3 appointment slots/class events for each day that's within the query\n * filter's date range.\n *\n * ## Unavailable slots\n *\n * By default, both available and unavailable appointment slots/ class events\n * are returned. To retrieve only available slots/events, specify\n * `{\"bookable\": true}` in the filter.\n *\n * ## Sorting\n *\n * Slots/events can be sorted only by `startDate`. Unavailable slots/events\n * always appear after available ones.\n *\n * ## Availability calculation\n *\n * Wix Bookings calculates availability differently for appointment-based\n * services and classes.\n *\n * ### Appointment availability\n *\n * An appointment slot is `bookable` if:\n * - The slot's `startDate` is later than or equal to `query.filter.startDate`.\n * - The slot's `endDate` is before or equal to `query.filter.endDate`.\n * - The service's `onlineBooking.enabled` is `true`.\n * - The service's early/late booking policies are either not enabled or the\n *   current time is within the defined limits.\n *\n * ### Class event availability\n *\n * A class event is `bookable` if:\n * - The event's `startDate` is later than or equal to `query.filter.startDate`.\n * - The event's `endDate` is before or equal to `query.filter.endDate`.\n * - The event's `remainingCapacity` is greater than `0`.\n * - The service's `onlineBooking.enabled` is `true`.\n * - The service's early/late booking policies are either not enabled or the\n *   current time is within the defined limits.\n *\n * If you want to restrict returned class events to those with a minimum number\n * of remaining spots, specify `openSpots` in the filter. By default, all class\n * events, even those with no open spot, are returned.\n *\n * ## Daylight Savings Time (DST)\n *\n * Due to DST, some local times may not exist or may occur twice. For example,\n * in Santiago, Chile, on September 7, 2025, 0:05 AM doesn't exist because the\n * clock advances from 0:00 AM to 1:00 AM.\n *\n * *Query Availability* automatically adjusts for these gaps. Non-existent times\n * are moved forward by 1 hour, while existing times remain unchanged. For\n * instance, if you call *Query Availability* with a `startDate` of\n * `2025-09-07T00:00:01.000` and an `endDate` of `2025-09-08T00:00:02.000`, the\n * adjusted query will use `2025-09-07T01:00:01.000` as the new start time.\n */\nexport function queryAvailability(payload: object): RequestOptionsFactory<any> {\n  function __queryAvailability({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v1.slot_availability',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.AvailabilityCalendar.QueryAvailability',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({\n        protoPath: '/v1/availability/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryAvailability;\n}\n\n/**\n * Returns availability for a given schedule ID\n * The availability for a course is calculated by:\n * - Checking for total spots by the schedule's capacity\n * - Checking for open spots by subtracting the current number of participants from the total spots\n * current number of participants is calculated by summing the number of participants of all bookings booked to the schedule\n * @deprecated\n */\nexport function getScheduleAvailability(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getScheduleAvailability({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v1.slot_availability',\n      method: 'GET' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.AvailabilityCalendar.GetScheduleAvailability',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({\n        protoPath: '/v2/availability/schedule/{scheduleId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __getScheduleAvailability;\n}\n\nexport function calculateMultiSlotAvailability(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __calculateMultiSlotAvailability({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.availability.v1.slot_availability',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.availability.AvailabilityCalendar.CalculateMultiSlotAvailability',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsAvailabilityAvailabilityCalendarUrl({\n        protoPath: '/v1/availability/multislot',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __calculateMultiSlotAvailability;\n}\n", "import { HttpClient, NonNullablePaths } from '@wix/sdk-types';\nimport {\n  CalculateMultiSlotAvailabilityOptions,\n  CalculateMultiSlotAvailabilityResponse,\n  GetScheduleAvailabilityApplicationErrors,\n  GetScheduleAvailabilityResponse,\n  QueryAvailabilityOptions,\n  QueryAvailabilityResponse,\n  QueryV2,\n  calculateMultiSlotAvailability as universalCalculateMultiSlotAvailability,\n  getScheduleAvailability as universalGetScheduleAvailability,\n  queryAvailability as universalQueryAvailability,\n} from './bookings-availability-v1-slot-availability-availability-calendar.universal.js';\n\nexport const __metadata = { PACKAGE_NAME: '@wix/bookings' };\n\nexport function queryAvailability(\n  httpClient: HttpClient\n): QueryAvailabilitySignature {\n  return (\n    query: NonNullablePaths<QueryV2, `filter`, 2>,\n    options?: QueryAvailabilityOptions\n  ) =>\n    universalQueryAvailability(\n      query,\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface QueryAvailabilitySignature {\n  /**\n   * Retrieves appointment slots or class events that match specified filters.\n   *\n   * <blockquote>\n   *\n   * Query Availability doesn't use a query builder, instead it uses\n   * [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language).\n   *\n   * </blockquote>\n   *\n   * ## Course limitations\n   *\n   * Course events can't be retrieved with this method. Follow this\n   * *sample flow* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/sample-flows#backend-modules_bookings_bookings_book-a-course) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/end-to-end-booking-flows#book-a-course))\n   * to check course availability.\n   *\n   * ## Query filter\n   *\n   * You must include `serviceId` in your query filter. For performance, also specify\n   * both `startDate` and `endDate`. Refer to the\n   * *supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))\n   * for a complete list of supported filters.\n   *\n   * ## Limit the number of slots returned per day\n   *\n   * To limit the maximum number of appointment slots returned for each date,\n   * specify `slotsPerDay`. For example,  specifying `{\"slotsPerDay\": 3}` returns\n   * a maximum of 3 appointment slots/class events for each day that's within the query\n   * filter's date range.\n   *\n   * ## Unavailable slots\n   *\n   * By default, both available and unavailable appointment slots/ class events\n   * are returned. To retrieve only available slots/events, specify\n   * `{\"bookable\": true}` in the filter.\n   *\n   * ## Sorting\n   *\n   * Slots/events can be sorted only by `startDate`. Unavailable slots/events\n   * always appear after available ones.\n   *\n   * ## Availability calculation\n   *\n   * Wix Bookings calculates availability differently for appointment-based\n   * services and classes.\n   *\n   * ### Appointment availability\n   *\n   * An appointment slot is `bookable` if:\n   * - The slot's `startDate` is later than or equal to `query.filter.startDate`.\n   * - The slot's `endDate` is before or equal to `query.filter.endDate`.\n   * - The service's `onlineBooking.enabled` is `true`.\n   * - The service's early/late booking policies are either not enabled or the\n   *   current time is within the defined limits.\n   *\n   * ### Class event availability\n   *\n   * A class event is `bookable` if:\n   * - The event's `startDate` is later than or equal to `query.filter.startDate`.\n   * - The event's `endDate` is before or equal to `query.filter.endDate`.\n   * - The event's `remainingCapacity` is greater than `0`.\n   * - The service's `onlineBooking.enabled` is `true`.\n   * - The service's early/late booking policies are either not enabled or the\n   *   current time is within the defined limits.\n   *\n   * If you want to restrict returned class events to those with a minimum number\n   * of remaining spots, specify `openSpots` in the filter. By default, all class\n   * events, even those with no open spot, are returned.\n   *\n   * ## Daylight Savings Time (DST)\n   *\n   * Due to DST, some local times may not exist or may occur twice. For example,\n   * in Santiago, Chile, on September 7, 2025, 0:05 AM doesn't exist because the\n   * clock advances from 0:00 AM to 1:00 AM.\n   *\n   * *Query Availability* automatically adjusts for these gaps. Non-existent times\n   * are moved forward by 1 hour, while existing times remain unchanged. For\n   * instance, if you call *Query Availability* with a `startDate` of\n   * `2025-09-07T00:00:01.000` and an `endDate` of `2025-09-08T00:00:02.000`, the\n   * adjusted query will use `2025-09-07T01:00:01.000` as the new start time.\n   * @param - Query options. Refer to the\n   * _supported filters article_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/supported-filters-and-sorting) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/time-slots/availability-calendar/supported-fields-filters-and-sorting))\n   * for a complete list of supported filters.\n   * @param - Options to use for querying availability.\n   */\n  (\n    query: NonNullablePaths<QueryV2, `filter`, 2>,\n    options?: QueryAvailabilityOptions\n  ): Promise<\n    NonNullablePaths<\n      QueryAvailabilityResponse,\n      | `availabilityEntries`\n      | `availabilityEntries.${number}.slot.serviceId`\n      | `availabilityEntries.${number}.slot.scheduleId`\n      | `availabilityEntries.${number}.slot.location.locationType`\n      | `availabilityEntries.${number}.bookable`\n      | `availabilityEntries.${number}.isFromV2`,\n      6\n    >\n  >;\n}\n\nexport function getScheduleAvailability(\n  httpClient: HttpClient\n): GetScheduleAvailabilitySignature {\n  return (scheduleId: string) =>\n    universalGetScheduleAvailability(\n      scheduleId,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface GetScheduleAvailabilitySignature {\n  /**\n   * Returns availability for a given schedule ID\n   * The availability for a course is calculated by:\n   * - Checking for total spots by the schedule's capacity\n   * - Checking for open spots by subtracting the current number of participants from the total spots\n   * current number of participants is calculated by summing the number of participants of all bookings booked to the schedule\n   * @param - The schedule ID for which availability is being checked.\n   * @deprecated\n   */\n  (scheduleId: string): Promise<\n    GetScheduleAvailabilityResponse & {\n      __applicationErrorsType?: GetScheduleAvailabilityApplicationErrors;\n    }\n  >;\n}\n\nexport function calculateMultiSlotAvailability(\n  httpClient: HttpClient\n): CalculateMultiSlotAvailabilitySignature {\n  return (options?: CalculateMultiSlotAvailabilityOptions) =>\n    universalCalculateMultiSlotAvailability(\n      options,\n      // @ts-ignore\n      { httpClient }\n    );\n}\n\ninterface CalculateMultiSlotAvailabilitySignature {\n  /** */\n  (options?: CalculateMultiSlotAvailabilityOptions): Promise<\n    NonNullablePaths<\n      CalculateMultiSlotAvailabilityResponse,\n      | `slots`\n      | `slots.${number}.slot.serviceId`\n      | `slots.${number}.slot.scheduleId`\n      | `slots.${number}.slot.location.locationType`\n      | `slots.${number}.bookable`\n      | `slots.${number}.isFromV2`,\n      6\n    >\n  >;\n}\n\nexport {\n  AvailabilityV2CustomerChoices,\n  AvailabilityWaitingList,\n  AvailableResources,\n  BookingPolicySettings,\n  BookingPolicyViolations,\n  CalculateMultiSlotAvailabilityOptions,\n  CalculateMultiSlotAvailabilityRequest,\n  CalculateMultiSlotAvailabilityResponse,\n  CursorPaging,\n  CursorPagingMetadata,\n  Cursors,\n  CustomerChoices,\n  EventInfo,\n  GetAvailabilityTimeSlotRequest,\n  GetAvailabilityTimeSlotResponse,\n  GetScheduleAvailabilityRequest,\n  GetScheduleAvailabilityResponse,\n  GetSlotAvailabilityRequest,\n  GetSlotAvailabilityResponse,\n  Location,\n  LocationLocationType,\n  LocationType,\n  NestedTimeSlot,\n  PagingMetadataV2,\n  QueryAvailabilityOptions,\n  QueryAvailabilityRequest,\n  QueryAvailabilityResponse,\n  QueryV2,\n  QueryV2PagingMethodOneOf,\n  Resource,\n  ResourcesFilter,\n  RuleBasedConstraints,\n  ScheduleAvailability,\n  Service_availabilityBookingPolicyViolations,\n  Service_availabilityNestedTimeSlot,\n  Slot,\n  SlotAvailability,\n  SlotResource,\n  SortOrder,\n  Sorting,\n  TimeSlot,\n  TimeSlotLocation,\n  V2CustomerChoices,\n  WaitingList,\n} from './bookings-availability-v1-slot-availability-availability-calendar.universal.js';\n", "import {\n  queryAvailability as publicQueryAvailability,\n  getScheduleAvailability as publicGetScheduleAvailability,\n  calculateMultiSlotAvailability as publicCalculateMultiSlotAvailability,\n} from './bookings-availability-v1-slot-availability-availability-calendar.public.js';\nimport { createRESTModule } from '@wix/sdk-runtime/rest-modules';\nimport { BuildRESTFunction, MaybeContext } from '@wix/sdk-types';\n\nexport const queryAvailability: MaybeContext<\n  BuildRESTFunction<typeof publicQueryAvailability> &\n    typeof publicQueryAvailability\n> = /*#__PURE__*/ createRESTModule(publicQueryAvailability);\nexport const getScheduleAvailability: MaybeContext<\n  BuildRESTFunction<typeof publicGetScheduleAvailability> &\n    typeof publicGetScheduleAvailability\n> = /*#__PURE__*/ createRESTModule(publicGetScheduleAvailability);\nexport const calculateMultiSlotAvailability: MaybeContext<\n  BuildRESTFunction<typeof publicCalculateMultiSlotAvailability> &\n    typeof publicCalculateMultiSlotAvailability\n> = /*#__PURE__*/ createRESTModule(publicCalculateMultiSlotAvailability);\n\nexport {\n  LocationType,\n  SortOrder,\n  LocationLocationType,\n} from './bookings-availability-v1-slot-availability-availability-calendar.universal.js';\nexport {\n  SlotAvailability,\n  Slot,\n  SlotResource,\n  Location,\n  AvailabilityWaitingList,\n  BookingPolicyViolations,\n  NestedTimeSlot,\n  QueryAvailabilityRequest,\n  QueryV2,\n  QueryV2PagingMethodOneOf,\n  Sorting,\n  CursorPaging,\n  V2CustomerChoices,\n  QueryAvailabilityResponse,\n  PagingMetadataV2,\n  Cursors,\n  GetSlotAvailabilityRequest,\n  GetSlotAvailabilityResponse,\n  BookingPolicySettings,\n  GetScheduleAvailabilityRequest,\n  GetScheduleAvailabilityResponse,\n  ScheduleAvailability,\n  CalculateMultiSlotAvailabilityRequest,\n  RuleBasedConstraints,\n  ResourcesFilter,\n  AvailabilityV2CustomerChoices,\n  CalculateMultiSlotAvailabilityResponse,\n  CursorPagingMetadata,\n  GetAvailabilityTimeSlotRequest,\n  TimeSlotLocation,\n  CustomerChoices,\n  GetAvailabilityTimeSlotResponse,\n  TimeSlot,\n  EventInfo,\n  WaitingList,\n  Service_availabilityBookingPolicyViolations,\n  AvailableResources,\n  Resource,\n  Service_availabilityNestedTimeSlot,\n  QueryAvailabilityOptions,\n  CalculateMultiSlotAvailabilityOptions,\n} from './bookings-availability-v1-slot-availability-availability-calendar.universal.js';\nexport {\n  LocationTypeWithLiterals,\n  SortOrderWithLiterals,\n  LocationLocationTypeWithLiterals,\n  GetScheduleAvailabilityApplicationErrors,\n} from './bookings-availability-v1-slot-availability-availability-calendar.universal.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wCAAAA;AAAA,EAAA,+BAAAC;AAAA,EAAA,yBAAAC;AAAA;AAAA;;;ACAA,6BAAoD;AACpD,oCAGO;;;ACJP,0BAAkC;AAClC,IAAAC,uBAA2B;AAI3B,SAAS,8DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,aAAO,iCAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAmFd,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8DAA8D;AAAA,QACjE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,wBACd,SAC4B;AAC5B,WAAS,0BAA0B,EAAE,KAAK,GAAQ;AAChD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8DAA8D;AAAA,QACjE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,uCAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,SAAS,+BACd,SAC4B;AAC5B,WAAS,iCAAiC,EAAE,KAAK,GAAQ;AACvD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,8DAA8D;AAAA,QACjE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADrLO,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,oBAAiB;AACjB,EAAAA,cAAA,kBAAe;AACf,EAAAA,cAAA,YAAS;AAJC,SAAAA;AAAA,GAAA;AA4GL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AAgUL,IAAK,uBAAL,kBAAKC,0BAAL;AACL,EAAAA,sBAAA,2BAAwB;AAExB,EAAAA,sBAAA,cAAW;AAEX,EAAAA,sBAAA,YAAS;AAET,EAAAA,sBAAA,cAAW;AAPD,SAAAA;AAAA,GAAA;AA6UZ,eAAsBC,mBACpB,OACA,SAYA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,IACA,UAAU,SAAS;AAAA,IACnB,aAAa,SAAS;AAAA,EACxB,CAAC;AAED,QAAM,UACgD;AAAA,IAClD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAC;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,OAAO;AAAA,UACP,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS,SAAS;AAAA,IACrB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyCA,eAAsBC,yBAAwB,YAI5C;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UACgD;AAAA,IAClD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAD;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,YAAY,OAAO;AAAA,QAC/C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY;AAAA,IACf;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAQA,eAAsBE,gCACpB,SAYA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,cAAU,qEAAsC;AAAA,IACpD,MAAM,SAAS;AAAA,IACf,IAAI,SAAS;AAAA,IACb,UAAU,SAAS;AAAA,IACnB,UAAU,SAAS;AAAA,IACnB,yBAAyB,SAAS;AAAA,IAClC,UAAU,SAAS;AAAA,IACnB,OAAO,SAAS;AAAA,IAChB,aAAa,SAAS;AAAA,IACtB,cAAc,SAAS;AAAA,EACzB,CAAC;AAED,QAAM,UACgD;AAAA,IAClD;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,eAAO,uEAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,uBAAmB,uBAAAF;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,MAAM;AAAA,UACN,IAAI;AAAA,UACJ,UAAU;AAAA,UACV,UAAU;AAAA,UACV,yBAAyB;AAAA,UACzB,UAAU;AAAA,UACV,OAAO;AAAA,UACP,aAAa;AAAA,UACb,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;;;AE7kCO,SAASG,mBACd,YAC4B;AAC5B,SAAO,CACL,OACA,YAEAA;AAAA,IACE;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AA0GO,SAASC,yBACd,YACkC;AAClC,SAAO,CAAC,eACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;AAmBO,SAASC,gCACd,YACyC;AACzC,SAAO,CAAC,YACNA;AAAA,IACE;AAAA;AAAA,IAEA,EAAE,WAAW;AAAA,EACf;AACJ;;;ACvKA,IAAAC,uBAAiC;AAG1B,IAAMC,qBAGK,2DAAiBA,kBAAuB;AACnD,IAAMC,2BAGK,2DAAiBA,wBAA6B;AACzD,IAAMC,kCAGK,2DAAiBA,+BAAoC;", "names": ["calculateMultiSlotAvailability", "getScheduleAvailability", "queryAvailability", "import_rest_modules", "LocationType", "SortOrder", "LocationLocationType", "queryAvailability", "sdkTransformError", "getScheduleAvailability", "calculateMultiSlotAvailability", "queryAvailability", "getScheduleAvailability", "calculateMultiSlotAvailability", "import_rest_modules", "queryAvailability", "getScheduleAvailability", "calculateMultiSlotAvailability"]}