import { HttpClient, NonNullablePaths, EventDefinition, MaybeContext, BuildRESTFunction, BuildEventDefinition } from '@wix/sdk-types';
import { ListCategoriesOptions, ListCategoryResponse, Category, UpdateCategory, UpdateCategoryResponse, UpdateCategoryApplicationErrors, DeleteCategoryOptions, DeleteCategoryResponse, CategoryNotificationEnvelope } from './index.typings.mjs';
export { BaseEventMetadata, BatchCreateCategoryRequest, BatchCreateCategoryResponse, BatchDeleteCategoryRequest, BatchDeleteCategoryResponse, BatchUpdateCategoryRequest, BatchUpdateCategoryResponse, CategoryNotification, CreateCategoryRequest, CreateCategoryResponse, DeleteCategoryRequest, Event, EventWithLiterals, IdentificationData, IdentificationDataIdOneOf, ListCategoryRequest, MessageEnvelope, Status, StatusWithLiterals, UpdateCategoryRequest, WebhookIdentityType, WebhookIdentityTypeWithLiterals } from './index.typings.mjs';

declare function listCategories$1(httpClient: HttpClient): ListCategoriesSignature;
interface ListCategoriesSignature {
    /**
     * Retrieves all categories.
     * @param - Options to use when listing categories.
     * @deprecated
     */
    (options?: ListCategoriesOptions): Promise<NonNullablePaths<ListCategoryResponse, `categories` | `categories.${number}.status`, 4>>;
}
declare function createCategory$1(httpClient: HttpClient): CreateCategorySignature;
interface CreateCategorySignature {
    /**
     * Creates a category.
     * @param - Category to create.
     * @returns Created category.
     * @deprecated
     */
    (category: NonNullablePaths<Category, `name`, 2>): Promise<NonNullablePaths<Category, `status`, 2>>;
}
declare function updateCategory$1(httpClient: HttpClient): UpdateCategorySignature;
interface UpdateCategorySignature {
    /**
     * Updates a category.
     *
     *
     * Each time the category is updated, revision increments by 1. You must include
     * the number of the existing revision when updating the category. This ensures
     * you're working with the latest service information and prevents unintended
     * overwrites.
     * @param - Category ID.
     * @deprecated
     */
    (_id: string, category: NonNullablePaths<UpdateCategory, `name`, 2>): Promise<NonNullablePaths<UpdateCategoryResponse, `category.status`, 3> & {
        __applicationErrorsType?: UpdateCategoryApplicationErrors;
    }>;
}
declare function deleteCategory$1(httpClient: HttpClient): DeleteCategorySignature;
interface DeleteCategorySignature {
    /**
     * Deletes a category.
     *
     *
     * You can specify `{"deleteServices": true}` to also delete all associated
     * services. Learn more about *deleting a service*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service)).
     * @param - ID of the category to delete.
     * @deprecated
     */
    (_id: string, options?: DeleteCategoryOptions): Promise<DeleteCategoryResponse>;
}
declare const onCategoryNotification$1: EventDefinition<CategoryNotificationEnvelope, "com.wixpress.bookings.services.api.v1.CategoryNotification">;

declare const listCategories: MaybeContext<BuildRESTFunction<typeof listCategories$1> & typeof listCategories$1>;
declare const createCategory: MaybeContext<BuildRESTFunction<typeof createCategory$1> & typeof createCategory$1>;
declare const updateCategory: MaybeContext<BuildRESTFunction<typeof updateCategory$1> & typeof updateCategory$1>;
declare const deleteCategory: MaybeContext<BuildRESTFunction<typeof deleteCategory$1> & typeof deleteCategory$1>;
/** @deprecated */
declare const onCategoryNotification: BuildEventDefinition<typeof onCategoryNotification$1>;

export { Category, CategoryNotificationEnvelope, DeleteCategoryOptions, DeleteCategoryResponse, ListCategoriesOptions, ListCategoryResponse, UpdateCategory, UpdateCategoryApplicationErrors, UpdateCategoryResponse, createCategory, deleteCategory, listCategories, onCategoryNotification, updateCategory };
