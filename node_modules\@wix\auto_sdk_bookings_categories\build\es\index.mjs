// src/bookings-v1-category-categories.public.ts
import { renameKeys<PERSON>romRESTResponseToSDKResponse as renameKeysFromRESTResponseToSDKResponse2 } from "@wix/sdk-runtime/rename-all-nested-keys";
import { EventDefinition } from "@wix/sdk-types";

// src/bookings-v1-category-categories.universal.ts
import { transformError as sdkTransformError } from "@wix/sdk-runtime/transform-error";
import {
  renameKeysFromSDKRequestToRESTRequest,
  renameKeysFromRESTResponseToSDKResponse
} from "@wix/sdk-runtime/rename-all-nested-keys";

// src/bookings-v1-category-categories.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl(opts) {
  const domainToMappings = {
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "www._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/bookings/v1/batch/categories",
        destPath: "/v1/batch/categories"
      },
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/bookings/v1/categories",
        destPath: "/v1/categories"
      }
    ],
    "api._api_base_domain_": [
      {
        srcPath: "/categories-proxy",
        destPath: ""
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ],
    "bookings._base_domain_": [
      {
        srcPath: "/_api/services-server/v1/categories",
        destPath: "/v1/categories"
      },
      {
        srcPath: "/_api/services-server/v1/batch/categories",
        destPath: "/v1/batch/categories"
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_categories";
function list(payload) {
  function __list({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "GET",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService.List",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return __list;
}
function create(payload) {
  function __create({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "POST",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService.Create",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __create;
}
function update(payload) {
  function __update({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "PUT",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService.Update",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories/{category.id}",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __update;
}
function _delete(payload) {
  function ___delete({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.category",
      method: "DELETE",
      methodFqn: "com.wixpress.bookings.services.api.v1.CategoriesService._delete",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsServicesApiV1CategoriesServiceUrl({
        protoPath: "/v1/categories/{id}",
        data: payload,
        host
      }),
      params: toURLSearchParams(payload)
    };
    return metadata;
  }
  return ___delete;
}

// src/bookings-v1-category-categories.universal.ts
var Status = /* @__PURE__ */ ((Status2) => {
  Status2["CREATED"] = "CREATED";
  Status2["DELETED"] = "DELETED";
  return Status2;
})(Status || {});
var Event = /* @__PURE__ */ ((Event2) => {
  Event2["Updated"] = "Updated";
  Event2["Deleted"] = "Deleted";
  Event2["Created"] = "Created";
  return Event2;
})(Event || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function listCategories(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    categoryIds: options?.categoryIds,
    includeDeleted: options?.includeDeleted
  });
  const reqOpts = list(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          categoryIds: "$[0].categoryIds",
          includeDeleted: "$[0].includeDeleted"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function createCategory(category) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = renameKeysFromSDKRequestToRESTRequest({ category });
  const reqOpts = create(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data)?.category;
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { category: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function updateCategory(_id, category) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    category: { ...category, id: _id }
  });
  const reqOpts = update(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: { category: "$[1]" },
        explicitPathsToArguments: { "category.id": "$[0]" },
        singleArgumentUnchanged: false
      },
      ["_id", "category"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteCategory(_id, options) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = renameKeysFromSDKRequestToRESTRequest({
    id: _id,
    deleteServices: options?.deleteServices
  });
  const reqOpts = _delete(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return renameKeysFromRESTResponseToSDKResponse(result.data);
  } catch (err) {
    const transformedError = sdkTransformError(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          id: "$[0]",
          deleteServices: "$[1].deleteServices"
        },
        singleArgumentUnchanged: false
      },
      ["_id", "options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}

// src/bookings-v1-category-categories.public.ts
function listCategories2(httpClient) {
  return (options) => listCategories(
    options,
    // @ts-ignore
    { httpClient }
  );
}
function createCategory2(httpClient) {
  return (category) => createCategory(
    category,
    // @ts-ignore
    { httpClient }
  );
}
function updateCategory2(httpClient) {
  return (_id, category) => updateCategory(
    _id,
    category,
    // @ts-ignore
    { httpClient }
  );
}
function deleteCategory2(httpClient) {
  return (_id, options) => deleteCategory(
    _id,
    options,
    // @ts-ignore
    { httpClient }
  );
}
var onCategoryNotification = EventDefinition(
  "com.wixpress.bookings.services.api.v1.CategoryNotification",
  false,
  (event) => renameKeysFromRESTResponseToSDKResponse2(event)
)();

// src/bookings-v1-category-categories.context.ts
import { createRESTModule } from "@wix/sdk-runtime/rest-modules";
import { createEventModule } from "@wix/sdk-runtime/event-definition-modules";
var listCategories3 = /* @__PURE__ */ createRESTModule(listCategories2);
var createCategory3 = /* @__PURE__ */ createRESTModule(createCategory2);
var updateCategory3 = /* @__PURE__ */ createRESTModule(updateCategory2);
var deleteCategory3 = /* @__PURE__ */ createRESTModule(deleteCategory2);
var onCategoryNotification2 = createEventModule(onCategoryNotification);
export {
  Event,
  Status,
  WebhookIdentityType,
  createCategory3 as createCategory,
  deleteCategory3 as deleteCategory,
  listCategories3 as listCategories,
  onCategoryNotification2 as onCategoryNotification,
  updateCategory3 as updateCategory
};
//# sourceMappingURL=index.mjs.map