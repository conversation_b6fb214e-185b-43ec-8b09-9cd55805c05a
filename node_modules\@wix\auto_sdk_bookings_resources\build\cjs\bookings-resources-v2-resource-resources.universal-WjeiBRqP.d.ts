import { SearchSpec, Search, NonNullablePaths } from '@wix/sdk-types';

/**
 * A resource represents an entity, such as a room or a staff member, that can be
 * scheduled for use in a *booking*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/about-the-bookings-apis)).
 * The availability of a resource is tracked to ensure that it
 * can be allocated at a requested time slot and to prevent double bookings.
 */
interface Resource extends ResourceCompositionDetailsOneOf {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the resource is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the resource.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was created.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was last updated.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the resource.
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
    /**
     * ID of the *resource type*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     *
     * Once a type has been set it can't be modified. You can create a resource
     * without specifying a type. However, customers can't book such resources.
     * @format GUID
     * @immutable
     */
    typeId?: string | null;
    /**
     * Information about the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * specifying the working hours and locations of the resource.
     *
     * Resources without a working hour schedule are available 24/7 at the locations
     * specified in the field `locationOptions`.
     * When both `workingHoursSchedules` and `locationOptions` are set,
     * `workingHoursSchedules` takes precedence.
     */
    workingHoursSchedules?: WorkingHoursSchedules;
    /** Information about the location where the resource is available. */
    locationOptions?: LocationOptions;
    /**
     * Schedule containing the *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * for which the resource has been booked.
     * @readonly
     */
    eventsSchedule?: EventsSchedule;
    /** Extensions enabling users to save custom data related to the resource. */
    extendedFields?: ExtendedFields;
}
/** @oneof */
interface ResourceCompositionDetailsOneOf {
}
interface WorkingHoursSchedule {
    /**
     * ID of the working hour *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     * @format GUID
     */
    scheduleId?: string | null;
    /**
     * Whether the schedule is shared by multiple resources or unique for this resource.
     *
     * Default: `false`
     * @readonly
     */
    shared?: boolean | null;
}
interface SingleResource {
    /**
     * Schedule details that specify the working hours and locations of this resource.
     *
     * When this field is missing or empty, the resource is assumed to be available
     * around the clock (24/7) at the locations specified in `locationOptions`. When
     * both `workingHoursSchedules` and `locationOptions` are set,
     * `workingHoursSchedules` takes precedence.
     */
    workingHoursSchedules?: V2WorkingHoursSchedules;
    /**
     * Information about the location where the resource is available.
     *
     * If you don't specify `locationOptions`, Wix Bookings automatically sets
     * `{"availableInAllLocations": true}`.
     */
    locationOptions?: LocationOptions;
    /**
     * Schedule containing the *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * for which the resource has been booked.
     * @readonly
     */
    eventsSchedule?: Schedule;
}
interface V2WorkingHoursSchedules {
    /**
     * Schedules specifying the working hours of the resource. Currently, only a single schedule is supported.
     * @maxSize 1
     */
    values?: Schedule[];
}
/** Deprecated and subject to removal */
interface Schedule {
    /**
     * Schedule ID. See [Schedules API](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/schedule-object) for more information.
     * @format GUID
     */
    scheduleId?: string | null;
    /**
     * Whether this schedule is used by multiple resources or unique for this resource.
     *
     * `true` if it is a shared schedule (for example from a business or location), `false` this is a custom schedule specific to the resource.
     * Default: `false`.
     */
    shared?: boolean | null;
}
interface LocationOptions {
    /**
     * Whether the resource is available in all *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in all business locations.
     * - `false`: The resource is available only in specific locations.
     *
     * Default: `false`
     */
    availableInAllLocations?: boolean | null;
    /** Details of resource availability in specific locations. */
    specificLocationOptions?: SpecificLocation;
}
interface SpecificLocation {
    /**
     * Whether the resource is available in *business locations*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations#backend-modules_bookings_services_location-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations#location-types)).
     *
     * - `true`: The resource is available in business locations.
     * - `false`: The resource isn't available in business locations.
     *
     * Default: `false`
     */
    availableInBusinessLocations?: boolean | null;
    /**
     * Information about the business locations where the resource is available.
     * Not returned, if the resource is available in either all business locations
     * or in no business location.
     * You can specify up to 100 business locations.
     * @maxSize 100
     */
    businessLocations?: BusinessLocation[];
}
interface BusinessLocation {
    /**
     * ID of the business *location*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).
     * @format GUID
     */
    locationId?: string | null;
}
interface WorkingHoursSchedules {
    /**
     * Schedules specifying the working hours of the resource. Currently, only a single schedule is supported.
     * @maxSize 1
     */
    values?: WorkingHoursSchedule[];
}
interface EventsSchedule {
    /**
     * ID of the event *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     * @format GUID
     */
    scheduleId?: string | null;
}
declare enum ManagementType {
    /** The resource is managed by a Wix user. */
    WIX_USER = "WIX_USER"
}
/** @enumType */
type ManagementTypeWithLiterals = ManagementType | 'WIX_USER';
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateResourceRequest {
    /** Resource to create. */
    resource: Resource;
}
interface CreateResourceResponse {
    /** Created resource. */
    resource?: Resource;
}
interface BulkCreateResourcesRequest {
    /**
     * Resources to create.
     * @maxSize 50
     */
    resources: Resource[];
    /**
     * Whether to include the created resources in the response.
     *
     * Default: `false`
     */
    returnEntity?: boolean;
}
interface BulkCreateResourcesResponse {
    /** The result of each resource creation. */
    results?: BulkResourceResult[];
    /** Create statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkResourceResult {
    /** Item metadata. */
    itemMetadata?: ItemMetadata;
    /** The resulting resource after the bulk operation. */
    item?: Resource;
}
interface ItemMetadata {
    /**
     * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).
     * @format GUID
     */
    _id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface GetResourceRequest {
    /**
     * ID of the resource to retrieve.
     * @format GUID
     */
    resourceId: string;
}
interface GetResourceResponse {
    /** Retrieved resource. */
    resource?: Resource;
}
interface GetDeletedResourceRequest {
    /**
     * ID of the deleted resource to retrieve from the trash bin.
     * @format GUID
     */
    resourceId?: string;
}
interface GetDeletedResourceResponse {
    /** Retrieved resource. */
    resource?: Resource;
}
interface ListDeletedResourcesRequest {
    /**
     * IDs of the resources to retrieve.
     * @format GUID
     * @maxSize 100
     */
    resourceIds?: string[];
    /** Paging details, allowing you specify a limit and a cursor. */
    paging?: CursorPaging;
}
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface ListDeletedResourcesResponse {
    /** Retrieved resources. */
    resources?: Resource[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /**
     * Number of items returned in the current response page.
     *
     * This count reflects the actual number of items in the current result set,
     * which may be less than the requested limit if fewer items are available.
     */
    count?: number | null;
    /**
     * Navigation cursors for moving between result pages.
     *
     * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor
     * to retrieve subsequent pages and `prev` cursor to go back to previous pages.
     * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).
     */
    cursors?: Cursors;
    /**
     * Indicates whether additional results are available beyond the current page.
     *
     * - `true`: More results exist and can be retrieved using the `next` cursor.
     * - `false`: This is the final page of results.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor token for retrieving the next page of results.
     *
     * Use this token in subsequent requests to continue pagination forward.
     * Value is `null` when on the last page of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor token for retrieving the previous page of results.
     *
     * Use this token to navigate backwards through result pages.
     * Value is `null` when on the first page of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface RemoveResourceFromTrashBinRequest {
    /**
     * ID of the resource to permanently delete from the trash bin.
     * @format GUID
     */
    resourceId?: string;
}
interface RemoveResourceFromTrashBinResponse {
}
interface RestoreResourceFromTrashBinRequest {
    /**
     * ID of the resource to restore from the trash bin.
     * @format GUID
     */
    resourceId?: string;
}
interface RestoreResourceFromTrashBinResponse {
    /** Restored resource. */
    resource?: Resource;
}
interface UpdateResourceRequest {
    /** Resource to update. */
    resource: Resource;
}
interface UpdateResourceResponse {
    /** Updated resource. */
    resource?: Resource;
}
interface BulkUpdateResourcesRequest {
    /**
     * Resources to update.
     * @minSize 1
     * @maxSize 100
     */
    resources: MaskedResource[];
    /**
     * Whether to include the updated resources in the response.
     *
     * Default: `false`
     */
    returnEntity?: boolean;
}
interface MaskedResource {
    /** Resource to update. */
    resource?: Resource;
    /** Explicit list of fields to update. */
    fieldMask?: string[];
}
interface BulkUpdateResourcesResponse {
    /** The result of each resource update. */
    results?: BulkResourceResult[];
    /** Update statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface DeleteResourceRequest {
    /**
     * ID of the resource to delete.
     * @format GUID
     */
    resourceId: string;
}
interface DeleteResourceResponse {
}
interface BulkDeleteResourcesRequest {
    /**
     * IDs of the resources to delete.
     * @format GUID
     * @maxSize 50
     */
    ids: string[];
}
interface BulkDeleteResourcesResponse {
    /** The result of each resource removal. */
    results?: BulkResourceResult[];
    /** Delete statistics. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface SearchResourcesRequest {
    /**
     * Search criteria including filter, sort, aggregations, and paging options.
     *
     * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for complete filter and sorting options.
     */
    search?: CursorSearch;
}
interface CursorSearch extends CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
    /**
     * Filter object for narrowing search results. For example, to return only resources available at specific business locations: `"filter": {"single_resource.location_options.specific_location_options.business_locations.location_id": {"$in": ["location-id-1", "location-id-2"]}}`.
     *
     * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).
     */
    filter?: Record<string, any> | null;
    /**
     * Array of sort objects specifying result order. For example, to sort by resource name in ascending order: `"sort": [{"fieldName": "name", "order": "ASC"}]`.
     *
     * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).
     * @maxSize 10
     */
    sort?: Sorting[];
    /**
     * Aggregations for grouping data into categories (facets) and providing summaries for each category.
     * For example, use aggregations to categorize search results by resource type, management type, or location availability.
     * @maxSize 10
     */
    aggregations?: Aggregation[];
    /** Free text to match in searchable fields. */
    search?: SearchDetails;
    /**
     * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.
     *
     * Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).
     * @maxLength 50
     */
    timeZone?: string | null;
}
/** @oneof */
interface CursorSearchPagingMethodOneOf {
    /**
     * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
     * `filter`, `sort`, or `search` can't be specified.
     */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Aggregation extends AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
    /**
     * Aggregation name, returned in `aggregations.results.name`.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: AggregationTypeWithLiterals;
    /**
     * Field to aggregate by. Use dot notation to specify a JSON path. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
    /**
     * Deprecated. Use `nested` instead.
     * @deprecated Deprecated. Use `nested` instead.
     * @replacedBy kind.nested
     * @targetRemovalDate 2024-03-30
     */
    groupBy?: GroupByAggregation;
}
/** @oneof */
interface AggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /** Range aggregation configuration. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. */
    dateHistogram?: DateHistogramAggregation;
    /** Nested aggregation configuration. */
    nested?: NestedAggregation;
}
interface RangeBucket {
    /** Inclusive lower bound of the range. Required if `to` isn't specified. */
    from?: number | null;
    /** Exclusive upper bound of the range. Required if `from` isn't specified. */
    to?: number | null;
}
declare enum SortType {
    /** Number of matches in the results. */
    COUNT = "COUNT",
    /** Alphabetically by the field value. */
    VALUE = "VALUE"
}
/** @enumType */
type SortTypeWithLiterals = SortType | 'COUNT' | 'VALUE';
declare enum SortDirection {
    /** Descending order. */
    DESC = "DESC",
    /** Ascending order. */
    ASC = "ASC"
}
/** @enumType */
type SortDirectionWithLiterals = SortDirection | 'DESC' | 'ASC';
declare enum MissingValues {
    /** Exclude missing values from the aggregation results. */
    EXCLUDE = "EXCLUDE",
    /** Include missing values in the aggregation results. */
    INCLUDE = "INCLUDE"
}
/** @enumType */
type MissingValuesWithLiterals = MissingValues | 'EXCLUDE' | 'INCLUDE';
interface IncludeMissingValuesOptions {
    /**
     * Custom bucket name for missing values.
     *
     * Default values:
     * - string: `N/A`
     * - int: `0`
     * - bool: `false`
     * @maxLength 20
     */
    addToBucket?: string;
}
declare enum ScalarType {
    UNKNOWN_SCALAR_TYPE = "UNKNOWN_SCALAR_TYPE",
    /** Total number of distinct values. */
    COUNT_DISTINCT = "COUNT_DISTINCT",
    /** Minimum value. */
    MIN = "MIN",
    /** Maximum value. */
    MAX = "MAX"
}
/** @enumType */
type ScalarTypeWithLiterals = ScalarType | 'UNKNOWN_SCALAR_TYPE' | 'COUNT_DISTINCT' | 'MIN' | 'MAX';
interface ValueAggregation extends ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
    /** Whether to sort by number of matches or value of the field. */
    sortType?: SortTypeWithLiterals;
    /** Whether to sort in ascending or descending order. */
    sortDirection?: SortDirectionWithLiterals;
    /**
     * Number of aggregations to return.
     *
     * Min: `1`
     * Max: `250`
     * Default: `10`
     */
    limit?: number | null;
    /**
     * Whether missing values should be included or excluded from the aggregation results.
     *
     * Default: `EXCLUDE`
     */
    missingValues?: MissingValuesWithLiterals;
}
/** @oneof */
interface ValueAggregationOptionsOneOf {
    /** Options for including missing values in the aggregation results. */
    includeOptions?: IncludeMissingValuesOptions;
}
declare enum NestedAggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM"
}
/** @enumType */
type NestedAggregationTypeWithLiterals = NestedAggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM';
interface RangeAggregation {
    /**
     * List of range buckets defining the ranges for aggregation. During aggregation, each entity is placed in the first bucket where its value falls within the specified range bounds.
     * @maxSize 50
     */
    buckets?: RangeBucket[];
}
interface ScalarAggregation {
    /** Operator for the scalar aggregation, for example `COUNT_DISTINCT`, `MIN`, `MAX`. */
    type?: ScalarTypeWithLiterals;
}
interface DateHistogramAggregation {
    /** Time interval for date histogram aggregation, for example `DAY`, `HOUR`, `MONTH`. */
    interval?: IntervalWithLiterals;
}
declare enum Interval {
    UNKNOWN_INTERVAL = "UNKNOWN_INTERVAL",
    /** Yearly interval */
    YEAR = "YEAR",
    /** Monthly interval */
    MONTH = "MONTH",
    /** Weekly interval */
    WEEK = "WEEK",
    /** Daily interval */
    DAY = "DAY",
    /** Hourly interval */
    HOUR = "HOUR",
    /** Minute interval */
    MINUTE = "MINUTE",
    /** Second interval */
    SECOND = "SECOND"
}
/** @enumType */
type IntervalWithLiterals = Interval | 'UNKNOWN_INTERVAL' | 'YEAR' | 'MONTH' | 'WEEK' | 'DAY' | 'HOUR' | 'MINUTE' | 'SECOND';
interface NestedAggregationItem extends NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /** Type of aggregation. Client must specify matching aggregation field below. */
    type?: NestedAggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationItemKindOneOf {
    /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */
    value?: ValueAggregation;
    /** Range aggregation configuration. Calculates counts within user-defined value ranges. */
    range?: RangeAggregation;
    /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */
    scalar?: ScalarAggregation;
    /** Date histogram aggregation configuration. Calculates counts within time intervals. */
    dateHistogram?: DateHistogramAggregation;
}
declare enum AggregationType {
    UNKNOWN_AGGREGATION_TYPE = "UNKNOWN_AGGREGATION_TYPE",
    /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */
    VALUE = "VALUE",
    /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */
    RANGE = "RANGE",
    /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */
    SCALAR = "SCALAR",
    /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */
    DATE_HISTOGRAM = "DATE_HISTOGRAM",
    /** Flattened list of aggregations, where each aggregation is nested within previous 1. */
    NESTED = "NESTED"
}
/** @enumType */
type AggregationTypeWithLiterals = AggregationType | 'UNKNOWN_AGGREGATION_TYPE' | 'VALUE' | 'RANGE' | 'SCALAR' | 'DATE_HISTOGRAM' | 'NESTED';
/** Nested aggregation for multi-level faceted search. Allows exploring large amounts of data through multiple levels of categorization, where each subsequent aggregation is nested within the previous aggregation to create hierarchical data summaries. */
interface NestedAggregation {
    /**
     * Flattened list of aggregations where each next aggregation is nested within the previous 1.
     * @minSize 2
     * @maxSize 3
     */
    nestedAggregations?: NestedAggregationItem[];
}
interface GroupByAggregation extends GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
    /**
     * User-defined name of aggregation. Must be unique and will appear in aggregation results.
     * @maxLength 100
     */
    name?: string | null;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface GroupByAggregationKindOneOf {
    /** Value aggregation configuration. */
    value?: ValueAggregation;
}
interface SearchDetails {
    /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */
    mode?: ModeWithLiterals;
    /**
     * Search term or expression.
     * @maxLength 100
     */
    expression?: string | null;
    /**
     * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `locationOptions.availableInAllLocations`.
     * @maxLength 200
     * @maxSize 20
     */
    fields?: string[];
    /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */
    fuzzy?: boolean;
}
declare enum Mode {
    /** At least 1 of the search terms must be present. */
    OR = "OR",
    /** All search terms must be present. */
    AND = "AND"
}
/** @enumType */
type ModeWithLiterals = Mode | 'OR' | 'AND';
interface SearchResourcesResponse {
    /**
     * Retrieved resources that match the search criteria specified in the request.
     *
     * Each resource includes all available information such as name, type, management details,
     * location options, working hour schedules, and availability configurations.
     */
    resources?: Resource[];
    /**
     * Cursor-based paging metadata for navigating through search results.
     *
     * Contains navigation information including the current page cursor, whether additional
     * results are available, and result counts. Use the `next` cursor to fetch subsequent pages.
     */
    pagingMetadata?: CursorPagingMetadata;
    /**
     * Aggregation results derived from the aggregations specified in the search request.
     *
     * Provides analytical summaries such as resource counts by type, location distribution,
     * management type statistics, or custom groupings. Only populated when aggregations
     * are included in the search criteria.
     */
    aggregationData?: AggregationData;
}
interface AggregationData {
    /**
     * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.
     * @maxSize 10000
     */
    results?: AggregationResults[];
}
interface ValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 100
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number;
}
interface RangeAggregationResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number;
}
interface NestedAggregationResults extends NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /**
     * User-defined name of aggregation, matches the one specified in request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that matches result. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface NestedAggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
}
interface ValueResults {
    /**
     * Array of value aggregation results, each containing a field value and the count of entities with that value.
     * @maxSize 250
     */
    results?: ValueAggregationResult[];
}
interface RangeResults {
    /**
     * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.
     * @maxSize 50
     */
    results?: RangeAggregationResult[];
}
interface AggregationResultsScalarResult {
    /** Type of scalar aggregation. */
    type?: ScalarTypeWithLiterals;
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedValueAggregationResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Nested aggregations result data. */
    nestedResults?: NestedAggregationResults;
}
interface ValueResult {
    /**
     * Value of the field.
     * @maxLength 1000
     */
    value?: string;
    /** Count of entities with this value. */
    count?: number | null;
}
interface RangeResult {
    /** Inclusive lower bound of the range. */
    from?: number | null;
    /** Exclusive upper bound of the range. */
    to?: number | null;
    /** Count of entities in this range. */
    count?: number | null;
}
interface ScalarResult {
    /** Value of the scalar aggregation. */
    value?: number;
}
interface NestedResultValue extends NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
/** @oneof */
interface NestedResultValueResultOneOf {
    /** Value aggregation result. */
    value?: ValueResult;
    /** Range aggregation result. */
    range?: RangeResult;
    /** Scalar aggregation result. */
    scalar?: ScalarResult;
    /** Date histogram aggregation result. */
    dateHistogram?: ValueResult;
}
interface Results {
    /** Map of nested aggregation results, keyed by aggregation name. */
    results?: Record<string, NestedResultValue>;
}
interface DateHistogramResult {
    /**
     * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).
     * @maxLength 100
     */
    value?: string;
    /** Count of documents in the bucket. */
    count?: number;
}
interface GroupByValueResults {
    /**
     * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.
     * @maxSize 1000
     */
    results?: NestedValueAggregationResult[];
}
interface DateHistogramResults {
    /**
     * Array of date histogram aggregation results, each containing a date bucket and its count.
     * @maxSize 200
     */
    results?: DateHistogramResult[];
}
/**
 * Results of `NESTED` aggregation type in a flattened form
 * Aggregations in resulting array are keyed by requested aggregation `name`.
 */
interface NestedResults {
    /**
     * Array of nested aggregation result groups, each containing multiple aggregation results.
     * @maxSize 1000
     */
    results?: Results[];
}
interface AggregationResults extends AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
    /**
     * User-defined name of aggregation as derived from search request.
     * @maxLength 100
     */
    name?: string;
    /** Type of aggregation that must match specified kind as derived from search request. */
    type?: AggregationTypeWithLiterals;
    /**
     * Path to the field to aggregate by in dot notation. For example `name` or `locationOptions.businessLocations.locationId`.
     * @maxLength 200
     */
    fieldPath?: string;
}
/** @oneof */
interface AggregationResultsResultOneOf {
    /** Value aggregation results. */
    values?: ValueResults;
    /** Range aggregation results. */
    ranges?: RangeResults;
    /** Scalar aggregation results. */
    scalar?: AggregationResultsScalarResult;
    /** Group by value aggregation results. */
    groupedByValue?: GroupByValueResults;
    /** Date histogram aggregation results. */
    dateHistogram?: DateHistogramResults;
    /** Nested aggregation results. */
    nested?: NestedResults;
}
interface QueryResourcesRequest {
    /**
     * Query for retrieving resources. Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    query?: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 5
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface QueryResourcesResponse {
    /** Retrieved resources. */
    resources?: Resource[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CountResourcesRequest {
    /**
     * Filter to base the count on. Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
    /**
     * Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.
     * @deprecated Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.
     * @targetRemovalDate 2024-05-01
     */
    search?: SearchDetails;
}
interface CountResourcesResponse {
    /** Total number of resources matching the filter. */
    count?: number;
}
interface FixResourceSchedulesRequest {
    /**
     * ID of the resource to fix schedules for.
     * @format GUID
     */
    resourceId?: string;
    /** Working hour schedule that must be set for this resource. */
    workingHoursSchedule?: Schedule;
}
interface FixResourceSchedulesResponse {
    /** Updated resource. */
    resource?: Resource;
}
interface ReindexTenantRequest {
}
interface ReindexTenantResponse {
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface Empty {
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type CreateResourceValidationErrors = {
    ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';
} | {
    ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';
};
/** @docsIgnore */
type BulkCreateResourcesValidationErrors = {
    ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';
} | {
    ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';
} | {
    ruleName?: 'RESOURCE_WORKING_HOURS_NOT_SUPPORTED_IN_BULK_REQUEST';
};
/** @docsIgnore */
type UpdateResourceValidationErrors = {
    ruleName?: 'RESOURCE_NAME_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';
} | {
    ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';
} | {
    ruleName?: 'RESOURCE_WORKING_HOURS_SCHEDULE_ID_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_WORKING_HOURS_SHARED_IS_REQUIRED';
};
/** @docsIgnore */
type BulkUpdateResourcesValidationErrors = {
    ruleName?: 'RESOURCE_NAME_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_LOCATION_ID_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_NOT_ALLOWED';
} | {
    ruleName?: 'RESOURCE_SPECIFIC_LOCATION_OPTIONS_MUST_BE_SET';
} | {
    ruleName?: 'RESOURCE_AVAILABLE_IN_BUSINESS_LOCATION_MUST_BE_TRUE';
} | {
    ruleName?: 'RESOURCE_WORKING_HOURS_SCHEDULE_ID_IS_REQUIRED';
} | {
    ruleName?: 'RESOURCE_WORKING_HOURS_SHARED_IS_REQUIRED';
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface ResourceCreatedEnvelope {
    entity: Resource;
    metadata: EventMetadata;
}
/**
 * Triggered when a resource is created.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.RESOURCE_READ
 * @webhook
 * @eventType wix.bookings.resources.v2.resource_created
 * @serviceIdentifier wix.bookings.resources.v2.ResourcesService
 * @slug created
 */
declare function onResourceCreated(handler: (event: ResourceCreatedEnvelope) => void | Promise<void>): void;
interface ResourceDeletedEnvelope {
    metadata: EventMetadata;
}
/**
 * Triggered when a resource is deleted.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.RESOURCE_READ
 * @webhook
 * @eventType wix.bookings.resources.v2.resource_deleted
 * @serviceIdentifier wix.bookings.resources.v2.ResourcesService
 * @slug deleted
 */
declare function onResourceDeleted(handler: (event: ResourceDeletedEnvelope) => void | Promise<void>): void;
interface ResourceUpdatedEnvelope {
    entity: Resource;
    metadata: EventMetadata;
}
/**
 * Triggered when an resource is updated.
 * @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.RESOURCE_READ
 * @webhook
 * @eventType wix.bookings.resources.v2.resource_updated
 * @slug updated
 */
declare function onResourceUpdated(handler: (event: ResourceUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Creates a new resource.
 *
 *
 * ## Connected schedules
 *
 * A new event *schedule*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration))
 * is automatically created for the resource.
 *
 * ## Locations
 *
 * If you don't specify `locationOptions`, Wix Bookings automatically sets
 * `locationOptions.availableInAllLocations` to `true`.
 *
 * If you specify 1 business location ID in `locationOptions.specificLocationOptions.businessLocations`,
 * you must specify `locationOptions.specificLocationOptions.availableInBusinessLocations`
 * as `true`. Currently, you can specify only a single business location.
 * @param resource - Resource to create.
 * @public
 * @requiredField resource
 * @requiredField resource.name
 * @permissionId BOOKINGS.RESOURCE_CREATE
 * @applicableIdentity APP
 * @returns Created resource.
 * @fqn wix.bookings.resources.v2.ResourcesService.CreateResource
 */
declare function createResource(resource: NonNullablePaths<Resource, `name`, 2>): Promise<NonNullablePaths<Resource, `workingHoursSchedules.values` | `locationOptions.specificLocationOptions.businessLocations`, 4> & {
    __validationErrorsType?: CreateResourceValidationErrors;
}>;
/**
 * Creates up to 50 resources.
 *
 *
 * Refer to *Create Resource*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/create-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/create-resource))
 * for more details.
 * @param resources - Resources to create.
 * @public
 * @requiredField resources
 * @requiredField resources.name
 * @permissionId BOOKINGS.RESOURCE_CREATE
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.ResourcesService.BulkCreateResources
 */
declare function bulkCreateResources(resources: NonNullablePaths<Resource, `name`, 2>[], options?: BulkCreateResourcesOptions): Promise<NonNullablePaths<BulkCreateResourcesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
    __validationErrorsType?: BulkCreateResourcesValidationErrors;
}>;
interface BulkCreateResourcesOptions {
    /**
     * Whether to include the created resources in the response.
     *
     * Default: `false`
     */
    returnEntity?: boolean;
}
/**
 * Retrieves a resource.
 * @param resourceId - ID of the resource to retrieve.
 * @public
 * @requiredField resourceId
 * @permissionId BOOKINGS.RESOURCE_READ
 * @applicableIdentity APP
 * @returns Retrieved resource.
 * @fqn wix.bookings.resources.v2.ResourcesService.GetResource
 */
declare function getResource(resourceId: string): Promise<NonNullablePaths<Resource, `workingHoursSchedules.values` | `locationOptions.specificLocationOptions.businessLocations`, 4>>;
/**
 * Updates a resource.
 *
 *
 * Each time the resource is updated, `revision` increments by 1. You must include the current revision of the resource when updating it.
 * This ensures you're working with the latest service information and prevents unintended overwrites.
 * @param _id - Resource ID.
 * @public
 * @requiredField _id
 * @requiredField resource
 * @requiredField resource.revision
 * @permissionId BOOKINGS.RESOURCE_UPDATE
 * @applicableIdentity APP
 * @returns Updated resource.
 * @fqn wix.bookings.resources.v2.ResourcesService.UpdateResource
 */
declare function updateResource(_id: string, resource: NonNullablePaths<UpdateResource, `revision`, 2>): Promise<NonNullablePaths<Resource, `workingHoursSchedules.values` | `locationOptions.specificLocationOptions.businessLocations`, 4> & {
    __validationErrorsType?: UpdateResourceValidationErrors;
}>;
interface UpdateResource {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * Revision number, which increments by 1 each time the resource is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating the resource.
     * @readonly
     */
    revision?: string | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was created.
     * @readonly
     */
    _createdDate?: Date | null;
    /**
     * Time in `YYYY-MM-DDThh:mm:ss.sssZ` format the resource was last updated.
     * @readonly
     */
    _updatedDate?: Date | null;
    /**
     * Name of the resource.
     * @maxLength 40
     * @minLength 1
     */
    name?: string | null;
    /**
     * ID of the *resource type*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).
     *
     * Once a type has been set it can't be modified. You can create a resource
     * without specifying a type. However, customers can't book such resources.
     * @format GUID
     * @immutable
     */
    typeId?: string | null;
    /**
     * Information about the *schedule*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))
     * specifying the working hours and locations of the resource.
     *
     * Resources without a working hour schedule are available 24/7 at the locations
     * specified in the field `locationOptions`.
     * When both `workingHoursSchedules` and `locationOptions` are set,
     * `workingHoursSchedules` takes precedence.
     */
    workingHoursSchedules?: WorkingHoursSchedules;
    /** Information about the location where the resource is available. */
    locationOptions?: LocationOptions;
    /**
     * Schedule containing the *events*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))
     * for which the resource has been booked.
     * @readonly
     */
    eventsSchedule?: EventsSchedule;
    /** Extensions enabling users to save custom data related to the resource. */
    extendedFields?: ExtendedFields;
}
/**
 * Updates multiple resources.
 *
 *
 * Refer to *Update Resource*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/update-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/update-resource))
 * for more details.
 * @param resources - Resources to update.
 * @public
 * @documentationMaturity preview
 * @requiredField resources
 * @requiredField resources.resource._id
 * @requiredField resources.resource.revision
 * @permissionId BOOKINGS.RESOURCE_UPDATE
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.ResourcesService.BulkUpdateResources
 */
declare function bulkUpdateResources(resources: NonNullablePaths<MaskedResource, `resource._id` | `resource.revision`, 3>[], options?: BulkUpdateResourcesOptions): Promise<NonNullablePaths<BulkUpdateResourcesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6> & {
    __validationErrorsType?: BulkUpdateResourcesValidationErrors;
}>;
interface BulkUpdateResourcesOptions {
    /**
     * Whether to include the updated resources in the response.
     *
     * Default: `false`
     */
    returnEntity?: boolean;
}
/**
 * Deletes a resource.
 *
 *
 * Deleting a resource cancels its event schedule and all its working hour
 * schedules that aren't shared with another resource. Learn more about
 * _how Bookings uses the Calendar APIs_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/wix-bookings-integration) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/wix-bookings-integration)).
 * @param resourceId - ID of the resource to delete.
 * @public
 * @requiredField resourceId
 * @permissionId BOOKINGS.RESOURCE_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.ResourcesService.DeleteResource
 */
declare function deleteResource(resourceId: string): Promise<void>;
/**
 * Deletes multiple resources.
 *
 *
 * Refer to *Delete Resource*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/delete-resource) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/delete-resource))
 * for more details.
 * @param ids - IDs of the resources to delete.
 * @public
 * @requiredField ids
 * @permissionId BOOKINGS.RESOURCE_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.ResourcesService.BulkDeleteResources
 */
declare function bulkDeleteResources(ids: string[]): Promise<NonNullablePaths<BulkDeleteResourcesResponse, `results` | `results.${number}.itemMetadata.originalIndex` | `results.${number}.itemMetadata.success` | `results.${number}.itemMetadata.error.code` | `results.${number}.itemMetadata.error.description` | `bulkActionMetadata.totalSuccesses` | `bulkActionMetadata.totalFailures` | `bulkActionMetadata.undetailedFailures`, 6>>;
interface ResourceSearchSpec extends SearchSpec {
    aggregatable: [
        '_createdDate',
        '_id',
        '_updatedDate',
        'appId',
        'managementType',
        'singleResource.locationOptions.availableInAllLocations',
        'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations',
        'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations',
        'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations',
        'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId',
        'type'
    ];
    paging: 'cursor';
    wql: [
        {
            operators: ['$hasAll', '$hasSome'];
            fields: [
                'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId'
            ];
            sort: 'BOTH';
        },
        {
            operators: '*';
            fields: [
                '_createdDate',
                '_id',
                '_updatedDate',
                'appId',
                'managementType',
                'name',
                'singleResource.locationOptions.availableInAllLocations',
                'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations',
                'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations',
                'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations',
                'type'
            ];
            sort: 'BOTH';
        }
    ];
}
type CommonSearchWithEntityContext = Search<Resource, ResourceSearchSpec>;
type ResourceSearch = {
    /**
    Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,
    `filter`, `sort`, or `search` can't be specified.
    */
    cursorPaging?: {
        /**
      Maximum number of items to return in the results.
      @max: 100
      */
        limit?: NonNullable<CommonSearchWithEntityContext['cursorPaging']>['limit'] | null;
        /**
      Pointer to the next or previous page in the list of results.
    
      Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
      Not relevant for the first request.
      @maxLength: 16000
      */
        cursor?: NonNullable<CommonSearchWithEntityContext['cursorPaging']>['cursor'] | null;
    };
    /**
    Filter object for narrowing search results. For example, to return only resources available at specific business locations: `"filter": {"single_resource.location_options.specific_location_options.business_locations.location_id": {"$in": ["location-id-1", "location-id-2"]}}`.
  
    Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).
    */
    filter?: CommonSearchWithEntityContext['filter'] | null;
    /**
    Array of sort objects specifying result order. For example, to sort by resource name in ascending order: `"sort": [{"fieldName": "name", "order": "ASC"}]`.
  
    Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)).
    @maxSize: 10
    */
    sort?: {
        /**
      Name of the field to sort by.
      @maxLength: 512
      */
        fieldName?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['fieldName'];
        /**
      Sort order.
      */
        order?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['order'];
    }[];
    /**
    Aggregations for grouping data into categories (facets) and providing summaries for each category.
    For example, use aggregations to categorize search results by resource type, management type, or location availability.
    @maxSize: 10
    */
    aggregations?: {
        /**
      Value aggregation configuration.
      */
        value?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['value'];
        /**
      Range aggregation configuration.
      */
        range?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['range'];
        /**
      Scalar aggregation configuration.
      */
        scalar?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['scalar'];
        /**
      Date histogram aggregation configuration.
      */
        dateHistogram?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['dateHistogram'];
        /**
      Nested aggregation configuration.
      */
        nested?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['nested'];
        /**
      Aggregation name, returned in `aggregations.results.name`.
      @maxLength: 100
      */
        name?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['name'] | null;
        /**
      Type of aggregation. Client must specify matching aggregation field below.
      */
        type?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['type'];
        /**
      Field to aggregate by. Use dot notation to specify a JSON path. For example `name` or `locationOptions.businessLocations.locationId`.
      @maxLength: 200
      */
        fieldPath?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['fieldPath'];
        /**
      Deprecated. Use `nested` instead.
      @deprecated: Deprecated. Use `nested` instead.,
      @replacedBy: kind.nested,
      @targetRemovalDate: 2024-03-30
      */
        groupBy?: NonNullable<CommonSearchWithEntityContext['aggregations']>[number]['groupBy'];
    }[];
    /**
    Free text to match in searchable fields.
    */
    search?: {
        /**
      Search mode. Defines the search logic for combining multiple terms in the `expression`.
      */
        mode?: NonNullable<CommonSearchWithEntityContext['search']>['mode'];
        /**
      Search term or expression.
      @maxLength: 100
      */
        expression?: NonNullable<CommonSearchWithEntityContext['search']>['expression'] | null;
        /**
      Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `locationOptions.availableInAllLocations`.
      @maxLength: 200,
      @maxSize: 20
      */
        fields?: NonNullable<CommonSearchWithEntityContext['search']>['fields'];
        /**
      Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions.
      */
        fuzzy?: NonNullable<CommonSearchWithEntityContext['search']>['fuzzy'];
    };
    /**
    Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.
  
    Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).
    @maxLength: 50
    */
    timeZone?: CommonSearchWithEntityContext['timeZone'] | null;
};
/**
 * Creates a query to retrieve a list of resources.
 *
 * The `queryResources()` function builds a query to retrieve a list of resources and returns a `ResourcesQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-builder/find) function.
 *
 * You can refine the query by chaining `ResourcesQueryBuilder` functions onto the query. `ResourcesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResources()` returns.
 *
 * `queryResources()` runs with the following `ResourcesQueryBuilder` default that you can override:
 *
 * + `limit` is `50`.
 * + Sorted by `id` in ascending order.
 *
 * The functions that are chained to `queryResources()` are applied in the order they are called. For example, if you apply `ascending("typeId")` and then `ascending("name")`, the results are sorted first by the `"typeId"`, and then, if there are multiple results with the same `"typeId"`, the items are sorted by `"name"`.
 *
 * The following `ResourcesQueryBuilder` functions are supported for the `queryResources()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/resources-query-result/items) property in `ResourcesQueryResult`.
 * @public
 * @permissionId BOOKINGS.RESOURCE_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.ResourcesService.QueryResources
 */
declare function queryResources(): ResourcesQueryBuilder;
interface QueryCursorResult {
    cursors: Cursors;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface ResourcesQueryResult extends QueryCursorResult {
    items: Resource[];
    query: ResourcesQueryBuilder;
    next: () => Promise<ResourcesQueryResult>;
    prev: () => Promise<ResourcesQueryResult>;
}
interface ResourcesQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    eq: (propertyName: 'singleResource.locationOptions.availableInAllLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations' | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'locationOptions.availableInAllLocations' | 'eventsSchedule.scheduleId', value: any) => ResourcesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ne: (propertyName: 'singleResource.locationOptions.availableInAllLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations' | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'locationOptions.availableInAllLocations' | 'eventsSchedule.scheduleId', value: any) => ResourcesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ge: (propertyName: 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'eventsSchedule.scheduleId', value: any) => ResourcesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    gt: (propertyName: 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'eventsSchedule.scheduleId', value: any) => ResourcesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    le: (propertyName: 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'eventsSchedule.scheduleId', value: any) => ResourcesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    lt: (propertyName: 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'eventsSchedule.scheduleId', value: any) => ResourcesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `string`.
     * @param string - String to compare against. Case-insensitive.
     */
    startsWith: (propertyName: 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | 'name' | 'typeId' | 'eventsSchedule.scheduleId', value: string) => ResourcesQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     */
    hasSome: (propertyName: 'singleResource.locationOptions.availableInAllLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations' | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'locationOptions.availableInAllLocations' | 'eventsSchedule.scheduleId', value: any[]) => ResourcesQueryBuilder;
    in: (propertyName: 'singleResource.locationOptions.availableInAllLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations' | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'locationOptions.availableInAllLocations' | 'eventsSchedule.scheduleId', value: any) => ResourcesQueryBuilder;
    exists: (propertyName: 'singleResource.locationOptions.availableInAllLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations' | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'name' | 'typeId' | 'locationOptions.availableInAllLocations' | 'eventsSchedule.scheduleId', value: boolean) => ResourcesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    ascending: (...propertyNames: Array<'singleResource.locationOptions.availableInAllLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations' | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'type' | 'typeId' | 'locationOptions.availableInAllLocations' | 'eventsSchedule.scheduleId' | 'appId' | 'managementType'>) => ResourcesQueryBuilder;
    /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    descending: (...propertyNames: Array<'singleResource.locationOptions.availableInAllLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInCustomerLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInCustomLocations' | 'singleResource.locationOptions.specificLocationOptions.availableInBusinessLocations' | 'singleResource.locationOptions.specificLocationOptions.businessLocations.locationId' | '_id' | '_createdDate' | '_updatedDate' | 'type' | 'typeId' | 'locationOptions.availableInAllLocations' | 'eventsSchedule.scheduleId' | 'appId' | 'managementType'>) => ResourcesQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */
    limit: (limit: number) => ResourcesQueryBuilder;
    /** @param cursor - A pointer to specific record */
    skipTo: (cursor: string) => ResourcesQueryBuilder;
    find: () => Promise<ResourcesQueryResult>;
}
/**
 * Counts resources according to given criteria.
 *
 *
 * Refer to the *supported filters article*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
 * for a complete list of supported filters and sorting options.
 * @public
 * @param options - Filter to base the count on. See *the supported filters article* ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting)) for a complete list of filtering options.
 * @permissionId BOOKINGS.RESOURCE_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.resources.v2.ResourcesService.CountResources
 */
declare function countResources(options?: CountResourcesOptions): Promise<NonNullablePaths<CountResourcesResponse, `count`, 2>>;
interface CountResourcesOptions {
    /**
     * Filter to base the count on. Refer to the *supported filters article*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))
     * for a complete list of supported filters.
     */
    filter?: Record<string, any> | null;
    /**
     * Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.
     * @deprecated Free text to match in searchable fields. Field is deprecated due to decision to use query instead of search for performing the count.
     * @targetRemovalDate 2024-05-01
     */
    search?: SearchDetails;
}

export { type ListDeletedResourcesRequest as $, AggregationType as A, type BulkCreateResourcesOptions as B, type CreateResourceValidationErrors as C, type BusinessLocation as D, type WorkingHoursSchedules as E, type EventsSchedule as F, type ExtendedFields as G, type CreateResourceRequest as H, Interval as I, type CreateResourceResponse as J, type BulkCreateResourcesRequest as K, type LocationOptions as L, type MaskedResource as M, NestedAggregationType as N, type BulkResourceResult as O, type ItemMetadata as P, type ApplicationError as Q, type Resource as R, type SearchResourcesResponse as S, type BulkActionMetadata as T, type UpdateResource as U, type V2WorkingHoursSchedules as V, WebhookIdentityType as W, type GetResourceRequest as X, type GetResourceResponse as Y, type GetDeletedResourceRequest as Z, type GetDeletedResourceResponse as _, type BulkCreateResourcesResponse as a, type DomainEvent as a$, type CursorPaging as a0, type ListDeletedResourcesResponse as a1, type CursorPagingMetadata as a2, type Cursors as a3, type RemoveResourceFromTrashBinRequest as a4, type RemoveResourceFromTrashBinResponse as a5, type RestoreResourceFromTrashBinRequest as a6, type RestoreResourceFromTrashBinResponse as a7, type UpdateResourceRequest as a8, type UpdateResourceResponse as a9, type NestedAggregationResults as aA, type NestedAggregationResultsResultOneOf as aB, type ValueResults as aC, type RangeResults as aD, type AggregationResultsScalarResult as aE, type NestedValueAggregationResult as aF, type ValueResult as aG, type RangeResult as aH, type ScalarResult as aI, type NestedResultValue as aJ, type NestedResultValueResultOneOf as aK, type Results as aL, type DateHistogramResult as aM, type GroupByValueResults as aN, type DateHistogramResults as aO, type NestedResults as aP, type AggregationResults as aQ, type AggregationResultsResultOneOf as aR, type QueryResourcesRequest as aS, type CursorQuery as aT, type CursorQueryPagingMethodOneOf as aU, type QueryResourcesResponse as aV, type CountResourcesRequest as aW, type FixResourceSchedulesRequest as aX, type FixResourceSchedulesResponse as aY, type ReindexTenantRequest as aZ, type ReindexTenantResponse as a_, type BulkUpdateResourcesRequest as aa, type DeleteResourceRequest as ab, type DeleteResourceResponse as ac, type BulkDeleteResourcesRequest as ad, type SearchResourcesRequest as ae, type CursorSearch as af, type CursorSearchPagingMethodOneOf as ag, type Sorting as ah, type Aggregation as ai, type AggregationKindOneOf as aj, type RangeBucket as ak, type IncludeMissingValuesOptions as al, type ValueAggregation as am, type ValueAggregationOptionsOneOf as an, type RangeAggregation as ao, type ScalarAggregation as ap, type DateHistogramAggregation as aq, type NestedAggregationItem as ar, type NestedAggregationItemKindOneOf as as, type NestedAggregation as at, type GroupByAggregation as au, type GroupByAggregationKindOneOf as av, type SearchDetails as aw, type AggregationData as ax, type ValueAggregationResult as ay, type RangeAggregationResult as az, type BulkCreateResourcesValidationErrors as b, type DomainEventBodyOneOf as b0, type EntityCreatedEvent as b1, type RestoreInfo as b2, type EntityUpdatedEvent as b3, type EntityDeletedEvent as b4, type ActionEvent as b5, type Empty as b6, type MessageEnvelope as b7, type IdentificationData as b8, type IdentificationDataIdOneOf as b9, queryResources as bA, countResources as bB, type BaseEventMetadata as ba, type EventMetadata as bb, type ResourceSearchSpec as bc, type ResourcesQueryResult as bd, type ManagementTypeWithLiterals as be, type SortOrderWithLiterals as bf, type SortTypeWithLiterals as bg, type SortDirectionWithLiterals as bh, type MissingValuesWithLiterals as bi, type ScalarTypeWithLiterals as bj, type NestedAggregationTypeWithLiterals as bk, type IntervalWithLiterals as bl, type AggregationTypeWithLiterals as bm, type ModeWithLiterals as bn, type WebhookIdentityTypeWithLiterals as bo, type CommonSearchWithEntityContext as bp, onResourceCreated as bq, onResourceDeleted as br, onResourceUpdated as bs, createResource as bt, bulkCreateResources as bu, getResource as bv, updateResource as bw, bulkUpdateResources as bx, deleteResource as by, bulkDeleteResources as bz, type UpdateResourceValidationErrors as c, type BulkUpdateResourcesOptions as d, type BulkUpdateResourcesResponse as e, type BulkUpdateResourcesValidationErrors as f, type BulkDeleteResourcesResponse as g, type ResourceSearch as h, type ResourcesQueryBuilder as i, type CountResourcesOptions as j, type CountResourcesResponse as k, type ResourceCreatedEnvelope as l, type ResourceDeletedEnvelope as m, type ResourceUpdatedEnvelope as n, ManagementType as o, SortOrder as p, SortType as q, SortDirection as r, MissingValues as s, ScalarType as t, Mode as u, type ResourceCompositionDetailsOneOf as v, type WorkingHoursSchedule as w, type SingleResource as x, type Schedule as y, type SpecificLocation as z };
