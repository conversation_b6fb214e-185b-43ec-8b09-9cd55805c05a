"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.typings.ts
var index_typings_exports = {};
__export(index_typings_exports, {
  RestorationStatus: () => RestorationStatus,
  Status: () => Status,
  Type: () => Type,
  WebhookIdentityType: () => WebhookIdentityType,
  createBackup: () => createBackup2,
  deleteBackup: () => deleteBackup2,
  listBackups: () => listBackups2,
  listRestorations: () => listRestorations2,
  restoreBackup: () => restoreBackup2,
  restorePartialBackup: () => restorePartialBackup2
});
module.exports = __toCommonJS(index_typings_exports);

// src/data-v2-backup-backups.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/data-v2-backup-backups.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressDstoreApiBackupV2BackupServiceUrl(opts) {
  const domainToMappings = {
    "www._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "manage._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "editor._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "blocks._base_domain_": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "create.editorx": [
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/_api/data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/wix-data/v1/backups",
        destPath: "/api/v1/backups"
      },
      {
        srcPath: "/wix-data/v2/backups",
        destPath: "/v2/backups"
      }
    ],
    "*.dev.wix-code.com": [
      {
        srcPath: "/data/v2/backups",
        destPath: "/v2/backups"
      },
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      }
    ],
    _: [
      {
        srcPath: "/data/v2/backups",
        destPath: "/v2/backups"
      },
      {
        srcPath: "/_api/data/backups",
        destPath: "/api/v1/backups"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_data_backups";
function createBackup(payload) {
  function __createBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "POST",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.CreateBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "backup.requestedDate" },
            { path: "backup.startedDate" },
            { path: "backup.finishedDate" },
            { path: "backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBackup;
}
function listBackups(payload) {
  function __listBackups({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "GET",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.ListBackups",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "backups.requestedDate" },
            { path: "backups.startedDate" },
            { path: "backups.finishedDate" },
            { path: "backups.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listBackups;
}
function restoreBackup(payload) {
  function __restoreBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "POST",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.RestoreBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/{backupId}/restore",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "restoration.requestedDate" },
            { path: "restoration.startedDate" },
            { path: "restoration.finishedDate" },
            { path: "restoration.backup.requestedDate" },
            { path: "restoration.backup.startedDate" },
            { path: "restoration.backup.finishedDate" },
            { path: "restoration.backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __restoreBackup;
}
function restorePartialBackup(payload) {
  function __restorePartialBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "POST",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.RestorePartialBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/{backupId}/partial-restore",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "restoration.requestedDate" },
            { path: "restoration.startedDate" },
            { path: "restoration.finishedDate" },
            { path: "restoration.backup.requestedDate" },
            { path: "restoration.backup.startedDate" },
            { path: "restoration.backup.finishedDate" },
            { path: "restoration.backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __restorePartialBackup;
}
function listRestorations(payload) {
  function __listRestorations({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "GET",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.ListRestorations",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/restorations",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "restorations.requestedDate" },
            { path: "restorations.startedDate" },
            { path: "restorations.finishedDate" },
            { path: "restorations.backup.requestedDate" },
            { path: "restorations.backup.startedDate" },
            { path: "restorations.backup.finishedDate" },
            { path: "restorations.backup.deletedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listRestorations;
}
function deleteBackup(payload) {
  function __deleteBackup({ host }) {
    const metadata = {
      entityFqdn: "wix.data.v2.backup",
      method: "DELETE",
      methodFqn: "com.wixpress.dstore.api.backup.v2.BackupService.DeleteBackup",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressDstoreApiBackupV2BackupServiceUrl({
        protoPath: "/v2/backups/{backupId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteBackup;
}

// src/data-v2-backup-backups.universal.ts
var Status = /* @__PURE__ */ ((Status2) => {
  Status2["PENDING"] = "PENDING";
  Status2["READY"] = "READY";
  Status2["FAILED"] = "FAILED";
  Status2["DELETED"] = "DELETED";
  Status2["CANCELED"] = "CANCELED";
  return Status2;
})(Status || {});
var Type = /* @__PURE__ */ ((Type2) => {
  Type2["ON_DEMAND"] = "ON_DEMAND";
  Type2["AUTO"] = "AUTO";
  return Type2;
})(Type || {});
var RestorationStatus = /* @__PURE__ */ ((RestorationStatus2) => {
  RestorationStatus2["PENDING"] = "PENDING";
  RestorationStatus2["COMPLETED"] = "COMPLETED";
  RestorationStatus2["FAILED"] = "FAILED";
  return RestorationStatus2;
})(RestorationStatus || {});
var WebhookIdentityType = /* @__PURE__ */ ((WebhookIdentityType2) => {
  WebhookIdentityType2["UNKNOWN"] = "UNKNOWN";
  WebhookIdentityType2["ANONYMOUS_VISITOR"] = "ANONYMOUS_VISITOR";
  WebhookIdentityType2["MEMBER"] = "MEMBER";
  WebhookIdentityType2["WIX_USER"] = "WIX_USER";
  WebhookIdentityType2["APP"] = "APP";
  return WebhookIdentityType2;
})(WebhookIdentityType || {});
async function createBackup2() {
  const { httpClient, sideEffects } = arguments[0];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({});
  const reqOpts = createBackup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {},
        singleArgumentUnchanged: false
      },
      []
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function listBackups2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    status: options?.status,
    type: options?.type,
    paging: options?.paging
  });
  const reqOpts = listBackups(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          status: "$[0].status",
          type: "$[0].type",
          paging: "$[0].paging"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function restoreBackup2(backupId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ backupId });
  const reqOpts = restoreBackup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { backupId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["backupId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function restorePartialBackup2(backupId, restorationCollections) {
  const { httpClient, sideEffects } = arguments[2];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    backupId,
    restorationCollections
  });
  const reqOpts = restorePartialBackup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          backupId: "$[0]",
          restorationCollections: "$[1]"
        },
        singleArgumentUnchanged: false
      },
      ["backupId", "restorationCollections"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function listRestorations2(options) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    status: options?.status,
    paging: options?.paging
  });
  const reqOpts = listRestorations(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: {
          status: "$[0].status",
          paging: "$[0].paging"
        },
        singleArgumentUnchanged: false
      },
      ["options"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
async function deleteBackup2(backupId) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({ backupId });
  const reqOpts = deleteBackup(payload);
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { backupId: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["backupId"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  RestorationStatus,
  Status,
  Type,
  WebhookIdentityType,
  createBackup,
  deleteBackup,
  listBackups,
  listRestorations,
  restoreBackup,
  restorePartialBackup
});
//# sourceMappingURL=index.typings.js.map