{"version": 3, "sources": ["../../../src/bookings-resources-v2-resource-type-resource-types.http.ts", "../../../src/bookings-resources-v2-resource-type-resource-types.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n      {\n        srcPath: '/_api/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/resource-types',\n        destPath: '',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v2/resources/resource-types',\n        destPath: '/v2/resources/resource-types',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_resource-types';\n\n/** Creates a new resource type. */\nexport function createResourceType(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __createResourceType({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resourceType.createdDate' },\n          { path: 'resourceType.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CreateResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceType.createdDate' },\n              { path: 'resourceType.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createResourceType;\n}\n\n/** Retrieves a resource type. */\nexport function getResourceType(payload: object): RequestOptionsFactory<any> {\n  function __getResourceType({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'GET' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.GetResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/{resourceTypeId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceType.createdDate' },\n              { path: 'resourceType.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getResourceType;\n}\n\n/**\n * Updates a resource type.\n *\n *\n * Each time the resource type is updated, `revision` increments by 1. You must\n * include current revision of the resource type when updating it. This ensures\n * you're working with the latest service information and prevents unintended overwrites.\n */\nexport function updateResourceType(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __updateResourceType({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'resourceType.createdDate' },\n          { path: 'resourceType.updatedDate' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'PATCH' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.UpdateResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/{resourceType.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceType.createdDate' },\n              { path: 'resourceType.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateResourceType;\n}\n\n/**\n * Deletes a resource type.\n *\n *\n * Deleting a resource type also automatically deletes all resources connected to it.\n */\nexport function deleteResourceType(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __deleteResourceType({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'DELETE' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.DeleteResourceType',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/{resourceTypeId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteResourceType;\n}\n\n/**\n * Creates a query to retrieve a list of resource types.\n *\n * The `queryResourceTypes()` function builds a query to retrieve a list of resource types and returns a `ResourceTypesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-builder/find) function.\n *\n * You can refine the query by chaining `ResourceTypesQueryBuilder` functions onto the query. `ResourceTypesQueryBuilder` functions enable you to sort, filter, and control the results that `queryResourceTypes()` returns.\n *\n * `queryResourceTypes()` runs with the following `ResourceTypesQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryResourceTypes()` are applied in the order they are called.\n *\n * The following `ResourceTypesQueryBuilder` functions are supported for the `queryResourceTypes()` function. For a full description of the resource object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/resource-types-query-result/items) property in `ResourceTypesQueryResult`.\n */\nexport function queryResourceTypes(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryResourceTypes({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.QueryResourceTypes',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'resourceTypes.createdDate' },\n              { path: 'resourceTypes.updatedDate' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryResourceTypes;\n}\n\n/**\n * Counts resource types, given the provided filtering.\n *\n *\n * Refer to the *supported filters article*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/filtering-and-sorting))\n * for a complete list of supported filters.\n */\nexport function countResourceTypes(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __countResourceTypes({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.resources.v2.resource_type',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.resources.v2.resourcetypes.ResourceTypesService.CountResourceTypes',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsResourcesV2ResourcetypesResourceTypesServiceUrl({\n        protoPath: '/v2/resources/resource-types/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countResourceTypes;\n}\n", "import * as ambassadorWixBookingsResourcesV2ResourceType from './bookings-resources-v2-resource-type-resource-types.http.js';\nimport * as ambassadorWixBookingsResourcesV2ResourceTypeTypes from './bookings-resources-v2-resource-type-resource-types.types.js';\nimport * as ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes from './bookings-resources-v2-resource-type-resource-types.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function createResourceType(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.CreateResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.CreateResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.CreateResourceTypeResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.CreateResourceTypeResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2ResourceType.createResourceType(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/resources/resource-types',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getResourceType(): __PublicMethodMetaInfo<\n  'GET',\n  { resourceTypeId: string },\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.GetResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.GetResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.GetResourceTypeResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.GetResourceTypeResponse\n> {\n  const payload = { resourceTypeId: ':resourceTypeId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2ResourceType.getResourceType(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v2/resources/resource-types/{resourceTypeId}',\n    pathParams: { resourceTypeId: 'resourceTypeId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateResourceType(): __PublicMethodMetaInfo<\n  'PATCH',\n  { resourceTypeId: string },\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.UpdateResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.UpdateResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.UpdateResourceTypeResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.UpdateResourceTypeResponse\n> {\n  const payload = { resourceType: { id: ':resourceTypeId' } } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2ResourceType.updateResourceType(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PATCH',\n    path: '/v2/resources/resource-types/{resourceType.id}',\n    pathParams: { resourceTypeId: 'resourceTypeId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteResourceType(): __PublicMethodMetaInfo<\n  'DELETE',\n  { resourceTypeId: string },\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.DeleteResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.DeleteResourceTypeRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.DeleteResourceTypeResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.DeleteResourceTypeResponse\n> {\n  const payload = { resourceTypeId: ':resourceTypeId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2ResourceType.deleteResourceType(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'DELETE',\n    path: '/v2/resources/resource-types/{resourceTypeId}',\n    pathParams: { resourceTypeId: 'resourceTypeId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryResourceTypes(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.QueryResourceTypesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.QueryResourceTypesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.QueryResourceTypesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.QueryResourceTypesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2ResourceType.queryResourceTypes(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/resources/resource-types/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function countResourceTypes(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.CountResourceTypesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.CountResourceTypesRequest,\n  ambassadorWixBookingsResourcesV2ResourceTypeUniversalTypes.CountResourceTypesResponse,\n  ambassadorWixBookingsResourcesV2ResourceTypeTypes.CountResourceTypesResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsResourcesV2ResourceType.countResourceTypes(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/resources/resource-types/count',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,kEACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAGd,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,2BAA2B;AAAA,QACrC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,2BAA2B;AAAA,QACrC;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kEAAkE;AAAA,QACrE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC9QO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACyC,mBAAmB,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,mBAOd;AACA,QAAM,UAAU,EAAE,gBAAgB,kBAAkB;AAEpD,QAAM,oBACyC,gBAAgB,OAAO;AAEtE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,gBAAgB,iBAAiB;AAAA,IAC/C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,EAAE,cAAc,EAAE,IAAI,kBAAkB,EAAE;AAE1D,QAAM,oBACyC,mBAAmB,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,gBAAgB,iBAAiB;AAAA,IAC/C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,EAAE,gBAAgB,kBAAkB;AAEpD,QAAM,oBACyC,mBAAmB,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,gBAAgB,iBAAiB;AAAA,IAC/C,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACyC,mBAAmB,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,sBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACyC,mBAAmB,OAAO;AAEzE,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "createResourceType", "getResourceType", "updateResourceType", "deleteResourceType", "queryResourceTypes", "countResourceTypes"]}