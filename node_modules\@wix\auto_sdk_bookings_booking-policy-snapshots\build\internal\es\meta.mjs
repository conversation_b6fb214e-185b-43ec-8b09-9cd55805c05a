// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.ts
import { toURLSearchParams } from "@wix/sdk-runtime/rest-modules";
import { transformRESTTimestampToSDKTimestamp } from "@wix/sdk-runtime/transformations/timestamp";
import { transformPaths } from "@wix/sdk-runtime/transformations/transform-paths";
import { resolveUrl } from "@wix/sdk-runtime/rest-modules";
function resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      },
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ]
  };
  return resolveUrl(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policy-snapshots";
function listPolicySnapshotsByBookingIds(payload) {
  function __listPolicySnapshotsByBookingIds({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.policy_snapshots.v1.booking_policy_snapshot",
      method: "GET",
      methodFqn: "com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(
        { protoPath: "/v1/policy-snapshots", data: payload, host }
      ),
      params: toURLSearchParams(payload),
      transformResponse: (payload2) => transformPaths(payload2, [
        {
          transformFn: transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicySnapshots.createdDate" },
            { path: "bookingPolicySnapshots.policy.createdDate" },
            { path: "bookingPolicySnapshots.policy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listPolicySnapshotsByBookingIds;
}

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.meta.ts
function listPolicySnapshotsByBookingIds2() {
  const payload = {};
  const getRequestOptions = listPolicySnapshotsByBookingIds(
    payload
  );
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/policy-snapshots",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
export {
  listPolicySnapshotsByBookingIds2 as listPolicySnapshotsByBookingIds
};
//# sourceMappingURL=meta.mjs.map