import { NonNullablePaths } from '@wix/sdk-types';

interface Backup {
    /**
     * Backup ID.
     * @format GUID
     * @readonly
     */
    _id?: string;
    /**
     * Backup status.
     * @readonly
     */
    status?: StatusWithLiterals;
    /**
     * Type of backup, based on how it was triggered.
     * @readonly
     */
    type?: TypeWithLiterals;
    /**
     * Date and time the backup was requested.
     * @readonly
     */
    requestedDate?: Date | null;
    /**
     * Date and time the backup commenced. Value is `null` until the backup process begins in the background.
     * @readonly
     */
    startedDate?: Date | null;
    /**
     * Date and time the backup process finished. Value is `null` until the backup process is completed in the background.
     * @readonly
     */
    finishedDate?: Date | null;
    /**
     * Date and time the backup was deleted. Value is `null` if that backup hasn't been deleted.
     * @readonly
     */
    deletedDate?: Date | null;
    /**
     * Backup size in bytes. Value is `null` until the backup process is completed.
     * @readonly
     */
    sizeInBytes?: string | null;
    /**
     * IDs and display names of collections the backup contains.
     * @readonly
     * @maxSize 1000
     */
    collections?: Collection[];
}
declare enum Status {
    /** Backup creation is in progress. */
    PENDING = "PENDING",
    /** Backup has been created successfully and can be used for data restoration. */
    READY = "READY",
    /** Backup creation has failed. */
    FAILED = "FAILED",
    /** Backup has been deleted. */
    DELETED = "DELETED",
    /** Backup has been canceled. */
    CANCELED = "CANCELED"
}
/** @enumType */
type StatusWithLiterals = Status | 'PENDING' | 'READY' | 'FAILED' | 'DELETED' | 'CANCELED';
declare enum Type {
    /** Backup taken on demand. */
    ON_DEMAND = "ON_DEMAND",
    /** Backup taken automatically by the system on a regular schedule. */
    AUTO = "AUTO"
}
/** @enumType */
type TypeWithLiterals = Type | 'ON_DEMAND' | 'AUTO';
interface Collection {
    /**
     * Collection ID.
     * @readonly
     * @maxLength 255
     */
    _id?: string;
    /**
     * Collection display name.
     * @readonly
     * @maxLength 1000
     */
    displayName?: string | null;
}
/** event triggered when backup state is changed (i.e. it was created, started, completed or failed) */
interface BackupStateChanged {
    /**
     * current state of backup
     * @readonly
     */
    backup?: Backup;
}
/** event triggered when backup restoration state is changed (i.e. it was created, started or completed) */
interface RestorationStateChanged {
    /**
     * current state of restoration
     * @readonly
     */
    restoration?: Restoration;
}
interface Restoration {
    /**
     * Restoration ID.
     * @format GUID
     * @readonly
     */
    _id?: string;
    /**
     * Details of the backup used for the restoration.
     * @readonly
     */
    backup?: Backup;
    /**
     * Status of restoration.
     * @readonly
     */
    status?: RestorationStatusWithLiterals;
    /**
     * Date and time the restoration was requested.
     * @readonly
     */
    requestedDate?: Date | null;
    /**
     * Date and time the restoration commenced. Value is `null` until the restoration process begins in the background.
     * @readonly
     */
    startedDate?: Date | null;
    /**
     * Date and time the restoration finished. Value is `null` until the restoration process is completed in the background.
     * @readonly
     */
    finishedDate?: Date | null;
    /**
     * Restored collections.
     * @readonly
     * @maxSize 1000
     */
    restorationCollections?: RestorationCollection[];
}
declare enum RestorationStatus {
    /** Restoration from a backup is in progress. */
    PENDING = "PENDING",
    /** Restoration from a backup has been successful. */
    COMPLETED = "COMPLETED",
    /** Restoration from a backup has failed. */
    FAILED = "FAILED"
}
/** @enumType */
type RestorationStatusWithLiterals = RestorationStatus | 'PENDING' | 'COMPLETED' | 'FAILED';
interface RestorationCollection {
    /**
     * Collections to be restored.
     *
     * Note: If collections have a multi-reference relationship,
     * the preexisting references will be restored if at least one of those collections are restored.
     * @minLength 1
     * @maxLength 1000
     */
    dataCollectionId?: string;
    /**
     * Destination where to restore the collection.
     * When not specified destination is taken from backup.
     */
    restoreDestination?: RestoreDestination;
}
interface RestoreDestination {
    /**
     * Collection ID.
     * @minLength 1
     * @maxLength 255
     */
    dataCollectionId?: string;
    /**
     * Collection's display name as shown in the CMS. If empty, `displayName` is taken from `backup.collections`.
     * @maxLength 1000
     */
    displayName?: string | null;
}
interface DisableInstanceRequest {
    /** @format GUID */
    instanceId?: string;
}
interface Empty {
}
interface EnableInstanceRequest {
    /** @format GUID */
    instanceId?: string;
}
interface DeleteAllRequest {
    /** @format GUID */
    instanceId?: string;
}
interface GetBackupMetadataRequest {
    /** @format GUID */
    backupId?: string;
}
interface GetBackupMetadataResponse {
    metaData?: Record<string, any> | null;
}
interface RebuildRequest {
    /** @format GUID */
    backupId?: string;
}
interface MoveRequest {
    /** @format GUID */
    backupId?: string;
}
interface RemoveDeletedRequest {
    limit?: number;
}
interface FailRestorationRequest {
    /** @format GUID */
    restorationId?: string;
}
interface TakeBackupRequest {
    /** @format GUID */
    instanceId?: string;
    type?: TypeWithLiterals;
}
interface UpdateBackupMetadataRequest {
    /** @format GUID */
    backupId?: string;
    s3Region?: string;
    s3Bucket?: string;
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
interface CreateBackupRequest {
}
interface CreateBackupResponse {
    /** Details of the requested backup. */
    backup?: Backup;
}
interface ListBackupsRequest {
    /**
     * Statuses to filter by.
     * If provided, only backups with the specified statuses are listed.
     * For example, to list only completed backups, use `?status=READY`.
     * To list completed and pending backups, use `?status=READY&status=PENDING`.
     *
     * Default: No filtering
     * @maxSize 10
     */
    status?: StatusWithLiterals[];
    /**
     * Type to filter by. If provided, only backups of the specified type are listed.
     *
     * Default: No filtering
     * @maxSize 10
     */
    type?: TypeWithLiterals[];
    /** Paging preferences. */
    paging?: Paging;
}
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface ListBackupsResponse {
    /**
     * Retrieved backups.
     * @maxSize 1000
     */
    backups?: Backup[];
    /** Paging information. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
}
interface RestoreBackupRequest {
    /**
     * ID of backup to be restored.
     * @format GUID
     */
    backupId: string;
}
interface RestoreBackupResponse {
    /** Details of data restoration from backup. */
    restoration?: Restoration;
}
interface RestorePartialBackupRequest {
    /**
     * ID of backup to be restored.
     * @format GUID
     */
    backupId: string;
    /**
     * **Required.** Collections to be restored.
     *
     * Note: If collections have a multi-reference relationship,
     * the preexisting references will be restored if at least one of those collections are restored.
     * @maxSize 1000
     */
    restorationCollections?: RestorationCollection[];
}
interface RestorePartialBackupResponse {
    /** Details of data restoration from backup. */
    restoration?: Restoration;
}
interface ListRestorationsRequest {
    /**
     * Statuses to filter by. If provided, only restorations with the specified statuses are listed.
     * For example, to list only completed restorations, use `?status=COMPLETED`.
     * To list completed and pending restorations, use `?status=COMPLETED&status=PENDING`.
     *
     * Default: No filtering
     * @maxSize 10
     */
    status?: RestorationStatusWithLiterals[];
    /** Offset and limit of items to retrieve. */
    paging?: Paging;
}
interface ListRestorationsResponse {
    /**
     * Retrieved restorations.
     * @maxSize 1000
     */
    restorations?: Restoration[];
    /** Paging information. */
    pagingMetadata?: PagingMetadataV2;
}
interface DeleteBackupRequest {
    /**
     * ID of the backup to be deleted.
     * @format GUID
     */
    backupId: string;
}
interface DeleteBackupResponse {
}
interface CancelBackupRequest {
    /**
     * ID of the backup to be cancelled.
     * @format GUID
     */
    backupId?: string;
}
interface CancelBackupResponse {
}
interface MigrateNamespaceRequest {
    /** @maxLength 255 */
    newNamespace?: string;
    /** @maxLength 255 */
    existingNamespace?: string;
}
interface MigrateNamespaceResponse {
}
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface BackupStateChangedEnvelope {
    data: BackupStateChanged;
    metadata: EventMetadata;
}
/**
 * Triggered when a backup's status is changed. This includes when a backup is created and its status is set to `PENDING`.
 * @permissionScope Manage Data Backups
 * @permissionScopeId SCOPE.DC-DATA.MANAGE-BACKUPS
 * @permissionId WIX_DATA.LIST_BACKUPS
 * @webhook
 * @eventType wix.data.v2.backup_backup_state_changed
 * @slug backup_state_changed
 */
declare function onBackupStateChanged(handler: (event: BackupStateChangedEnvelope) => void | Promise<void>): void;
interface BackupRestorationStateChangedEnvelope {
    data: RestorationStateChanged;
    metadata: EventMetadata;
}
/**
 * Triggered when a restoration's status is changed. This includes when a new restoration is initiated and its status is `PENDING`.
 * @permissionScope Manage Data Backups
 * @permissionScopeId SCOPE.DC-DATA.MANAGE-BACKUPS
 * @permissionId WIX_DATA.LIST_BACKUPS
 * @webhook
 * @eventType wix.data.v2.backup_restoration_state_changed
 * @slug restoration_state_changed
 */
declare function onBackupRestorationStateChanged(handler: (event: BackupRestorationStateChangedEnvelope) => void | Promise<void>): void;
/**
 * Creates an on-demand backup of live content in a site's collections.
 *
 *
 * By default, all of the site's collections are included in the backup. For a partial backup, specify which collections to include in the `backup.collections` parameter.
 *
 * The process of creating a backup takes time.
 * You can check whether a backup has completed successfully with List Backups.
 *
 * You can store up to 3 on-demand backups for each site.
 * If 3 on-demand backups already exist, the oldest existing on-demand backup for the site is deleted when a new one is created. Automated backups are not affected.
 * @public
 * @permissionId WIX_DATA.CREATE_BACKUP
 * @applicableIdentity APP
 * @fqn com.wixpress.dstore.api.backup.v2.BackupService.CreateBackup
 */
declare function createBackup(): Promise<NonNullablePaths<CreateBackupResponse, `backup._id` | `backup.status` | `backup.type` | `backup.collections` | `backup.collections.${number}._id`, 5>>;
/**
 * Retrieves a list of all backups for a site.
 *
 * Results are sorted by requested date, with the newest first.
 *
 * You can use this method to check whether a backup initiated with Create Backup has been completed successfully.
 * @public
 * @permissionId WIX_DATA.LIST_BACKUPS
 * @applicableIdentity APP
 * @fqn com.wixpress.dstore.api.backup.v2.BackupService.ListBackups
 */
declare function listBackups(options?: ListBackupsOptions): Promise<NonNullablePaths<ListBackupsResponse, `backups` | `backups.${number}._id` | `backups.${number}.status` | `backups.${number}.type`, 4>>;
interface ListBackupsOptions {
    /**
     * Statuses to filter by.
     * If provided, only backups with the specified statuses are listed.
     * For example, to list only completed backups, use `?status=READY`.
     * To list completed and pending backups, use `?status=READY&status=PENDING`.
     *
     * Default: No filtering
     * @maxSize 10
     */
    status?: StatusWithLiterals[];
    /**
     * Type to filter by. If provided, only backups of the specified type are listed.
     *
     * Default: No filtering
     * @maxSize 10
     */
    type?: TypeWithLiterals[];
    /** Paging preferences. */
    paging?: Paging;
}
/**
 * Restores all data from a backup.
 *
 * The process of restoring data from a backup takes time.
 * You can check whether your restoration has completed successfully with List Restorations.
 * @param backupId - ID of backup to be restored.
 * @public
 * @requiredField backupId
 * @permissionId WIX_DATA.RESTORE_BACKUP
 * @applicableIdentity APP
 * @fqn com.wixpress.dstore.api.backup.v2.BackupService.RestoreBackup
 */
declare function restoreBackup(backupId: string): Promise<NonNullablePaths<RestoreBackupResponse, `restoration._id` | `restoration.backup._id` | `restoration.backup.status` | `restoration.backup.type` | `restoration.backup.collections` | `restoration.backup.collections.${number}._id` | `restoration.status` | `restoration.restorationCollections` | `restoration.restorationCollections.${number}.dataCollectionId` | `restoration.restorationCollections.${number}.restoreDestination.dataCollectionId`, 6>>;
/**
 * Restores specific collections from a backup.
 *
 * The process of restoring data from a backup takes time.
 * You can check whether your restoration has completed successfully with List Restorations.
 * @param backupId - ID of backup to be restored.
 * @param restorationCollections - **Required.** Collections to be restored.
 *
 * Note: If collections have a multi-reference relationship,
 * the preexisting references will be restored if at least one of those collections are restored.
 * @public
 * @requiredField backupId
 * @requiredField restorationCollections
 * @permissionId WIX_DATA.RESTORE_BACKUP
 * @applicableIdentity APP
 * @fqn com.wixpress.dstore.api.backup.v2.BackupService.RestorePartialBackup
 */
declare function restorePartialBackup(backupId: string, restorationCollections: RestorationCollection[]): Promise<NonNullablePaths<RestorePartialBackupResponse, `restoration._id` | `restoration.backup._id` | `restoration.backup.status` | `restoration.backup.type` | `restoration.backup.collections` | `restoration.backup.collections.${number}._id` | `restoration.status` | `restoration.restorationCollections` | `restoration.restorationCollections.${number}.dataCollectionId` | `restoration.restorationCollections.${number}.restoreDestination.dataCollectionId`, 6>>;
/**
 * Retrieves a list of all data restorations from backups.
 *
 * Results are sorted by requested date, with the newest first.
 *
 * You can use this method to check whether a restoration initiated with Restore Backup has been completed successfully.
 * @public
 * @permissionId WIX_DATA.LIST_RESTORATIONS
 * @applicableIdentity APP
 * @fqn com.wixpress.dstore.api.backup.v2.BackupService.ListRestorations
 */
declare function listRestorations(options?: ListRestorationsOptions): Promise<NonNullablePaths<ListRestorationsResponse, `restorations` | `restorations.${number}._id` | `restorations.${number}.backup._id` | `restorations.${number}.backup.status` | `restorations.${number}.backup.type` | `restorations.${number}.status`, 5>>;
interface ListRestorationsOptions {
    /**
     * Statuses to filter by. If provided, only restorations with the specified statuses are listed.
     * For example, to list only completed restorations, use `?status=COMPLETED`.
     * To list completed and pending restorations, use `?status=COMPLETED&status=PENDING`.
     *
     * Default: No filtering
     * @maxSize 10
     */
    status?: RestorationStatusWithLiterals[];
    /** Offset and limit of items to retrieve. */
    paging?: Paging;
}
/**
 * Deletes a backup.
 *
 * The process of deleting a backup takes time.
 * You can check whether a backup has been deleted successfully with List Backups.
 * @param backupId - ID of the backup to be deleted.
 * @public
 * @requiredField backupId
 * @permissionId WIX_DATA.DELETE_BACKUP
 * @applicableIdentity APP
 * @fqn com.wixpress.dstore.api.backup.v2.BackupService.DeleteBackup
 */
declare function deleteBackup(backupId: string): Promise<void>;

export { type ActionEvent, type Backup, type BackupRestorationStateChangedEnvelope, type BackupStateChanged, type BackupStateChangedEnvelope, type BaseEventMetadata, type CancelBackupRequest, type CancelBackupResponse, type Collection, type CreateBackupRequest, type CreateBackupResponse, type DeleteAllRequest, type DeleteBackupRequest, type DeleteBackupResponse, type DisableInstanceRequest, type DomainEvent, type DomainEventBodyOneOf, type Empty, type EnableInstanceRequest, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type EventMetadata, type FailRestorationRequest, type GetBackupMetadataRequest, type GetBackupMetadataResponse, type IdentificationData, type IdentificationDataIdOneOf, type ListBackupsOptions, type ListBackupsRequest, type ListBackupsResponse, type ListRestorationsOptions, type ListRestorationsRequest, type ListRestorationsResponse, type MessageEnvelope, type MigrateNamespaceRequest, type MigrateNamespaceResponse, type MoveRequest, type Paging, type PagingMetadataV2, type RebuildRequest, type RemoveDeletedRequest, type Restoration, type RestorationCollection, type RestorationStateChanged, RestorationStatus, type RestorationStatusWithLiterals, type RestoreBackupRequest, type RestoreBackupResponse, type RestoreDestination, type RestoreInfo, type RestorePartialBackupRequest, type RestorePartialBackupResponse, Status, type StatusWithLiterals, type TakeBackupRequest, Type, type TypeWithLiterals, type UpdateBackupMetadataRequest, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, createBackup, deleteBackup, listBackups, listRestorations, onBackupRestorationStateChanged, onBackupStateChanged, restoreBackup, restorePartialBackup };
