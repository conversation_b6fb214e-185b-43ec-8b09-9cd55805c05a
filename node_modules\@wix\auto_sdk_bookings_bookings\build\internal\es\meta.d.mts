import { ConfirmOrDeclineBookingRequest as ConfirmOrDeclineBookingRequest$1, ConfirmOrDeclineBookingResponse as ConfirmOrDeclineBookingResponse$1, BulkConfirmOrDeclineBookingRequest as BulkConfirmOrDeclineBookingRequest$1, BulkConfirmOrDeclineBookingResponse as BulkConfirmOrDeclineBookingResponse$1, CreateBookingRequest as CreateBookingRequest$1, CreateBookingResponse as CreateBookingResponse$1, BulkCreateBookingRequest as BulkCreateBookingRequest$1, BulkCreateBookingResponse as BulkCreateBookingResponse$1, RescheduleBookingRequest as RescheduleBookingRequest$1, RescheduleBookingResponse as RescheduleBookingResponse$1, ConfirmBookingRequest as ConfirmBookingRequest$1, ConfirmBookingResponse as ConfirmBookingResponse$1, SetBookingSubmissionIdRequest as SetBookingSubmissionIdRequest$1, SetBookingSubmissionIdResponse as SetBookingSubmissionIdResponse$1, UpdateExtendedFieldsRequest as UpdateExtendedFieldsRequest$1, UpdateExtendedFieldsResponse as UpdateExtendedFieldsResponse$1, DeclineBookingRequest as DeclineBookingRequest$1, DeclineBookingResponse as DeclineBookingResponse$1, CancelBookingRequest as CancelBookingRequest$1, CancelBookingResponse as CancelBookingResponse$1, UpdateNumberOfParticipantsRequest as UpdateNumberOfParticipantsRequest$1, UpdateNumberOfParticipantsResponse as UpdateNumberOfParticipantsResponse$1, MarkBookingAsPendingRequest as MarkBookingAsPendingRequest$1, MarkBookingAsPendingResponse as MarkBookingAsPendingResponse$1, CreateMultiServiceBookingRequest as CreateMultiServiceBookingRequest$1, CreateMultiServiceBookingResponse as CreateMultiServiceBookingResponse$1, RescheduleMultiServiceBookingRequest as RescheduleMultiServiceBookingRequest$1, RescheduleMultiServiceBookingResponse as RescheduleMultiServiceBookingResponse$1, GetMultiServiceBookingAvailabilityRequest as GetMultiServiceBookingAvailabilityRequest$1, GetMultiServiceBookingAvailabilityResponse as GetMultiServiceBookingAvailabilityResponse$1, CancelMultiServiceBookingRequest as CancelMultiServiceBookingRequest$1, CancelMultiServiceBookingResponse as CancelMultiServiceBookingResponse$1, MarkMultiServiceBookingAsPendingRequest as MarkMultiServiceBookingAsPendingRequest$1, MarkMultiServiceBookingAsPendingResponse as MarkMultiServiceBookingAsPendingResponse$1, ConfirmMultiServiceBookingRequest as ConfirmMultiServiceBookingRequest$1, ConfirmMultiServiceBookingResponse as ConfirmMultiServiceBookingResponse$1, DeclineMultiServiceBookingRequest as DeclineMultiServiceBookingRequest$1, DeclineMultiServiceBookingResponse as DeclineMultiServiceBookingResponse$1, BulkGetMultiServiceBookingAllowedActionsRequest as BulkGetMultiServiceBookingAllowedActionsRequest$1, BulkGetMultiServiceBookingAllowedActionsResponse as BulkGetMultiServiceBookingAllowedActionsResponse$1, GetMultiServiceBookingRequest as GetMultiServiceBookingRequest$1, GetMultiServiceBookingResponse as GetMultiServiceBookingResponse$1, AddBookingsToMultiServiceBookingRequest as AddBookingsToMultiServiceBookingRequest$1, AddBookingsToMultiServiceBookingResponse as AddBookingsToMultiServiceBookingResponse$1, RemoveBookingsFromMultiServiceBookingRequest as RemoveBookingsFromMultiServiceBookingRequest$1, RemoveBookingsFromMultiServiceBookingResponse as RemoveBookingsFromMultiServiceBookingResponse$1 } from './index.typings.mjs';
import '@wix/sdk-types';

/** An entity representing a scheduled appointment, class session, or course. */
interface Booking extends BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
    /**
     * Booking ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * An object describing the bookable entity - either a specific time slot or a recurring schedule.
     *
     * The structure depends on the type of service being booked:
     *
     * *For appointment services:** Use `slot` to book a specific time slot with a
     * service provider. Appointments are typically one-time sessions at a specific date and time.
     *
     * *For class services:** Use `slot` to book a specific class session. Classes
     * are individual sessions that can have multiple participants.
     *
     * *For course services:** Use `schedule` to book an entire course consisting of
     * multiple sessions over time. Courses are recurring, multi-session offerings.
     *
     * Choose the appropriate field based on your service type and booking requirements.
     */
    bookedEntity?: BookedEntity;
    /**
     * Contact details of the site visitor or member
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/members/members/introduction) | [REST](https://dev.wix.com/docs/rest/crm/members-contacts/members/members/introduction))
     * making the booking.
     */
    contactDetails?: ContactDetails;
    /** Additional custom fields submitted with the booking form. */
    additionalFields?: CustomFormField[];
    /**
     * Booking status. A booking is automatically confirmed if the service allows it
     * and an eCommerce order is created. It is automatically declined if there is a
     * double booking and the customer hasn't paid or is eligible for an automatic
     * refund. Currently, only payments with pricing plans are automatically refundable.
     */
    status?: BookingStatusWithLiterals;
    /**
     * The payment status of the booking. This field automatically syncs with the
     * `paymentStatus` of the corresponding eCommerce order
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/orders/setup)| [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/orders/introduction))
     * when customers use Wix eCommerce checkout.
     *
     * ## Integration patterns
     *
     * *When using Wix eCommerce checkout:** Wix Bookings automatically syncs the payment status based on the eCommerce order's payment status.
     * Do not manually update this field.
     *
     * *When using custom payment flows:** You can manually update the payment status with Confirm Booking or Decline Booking ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/bookings/confirm-or-decline-booking) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/bookings-writer-v2/confirm-or-decline-booking)) to reflect the customer's payment state.
     *
     * *For membership/pricing plan payments:** Wix Bookings automatically manages the payment status when customers pay with an active pricing plan ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/introduction)) subscription.
     *
     * All payment statuses are supported for every booking `status`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
    /**
     * Payment option selected by the customer. If the customer hasn't completed their checkout, they may still change the payment method. Must be one of the payment options offered by the service ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)), unless `skipSelectedPaymentOptionValidation` is `true`.
     *
     * When undefined during an eCommerce checkout ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)), Wix Bookings uses the service's default payment option
     */
    selectedPaymentOption?: SelectedPaymentOptionWithLiterals;
    /**
     * Date and time the booking was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
    /** External user ID that you can provide. */
    externalUserId?: string | null;
    /**
     * Revision number to be used when updating, rescheduling, or cancelling the booking.
     * Increments by 1 each time the booking is updated, rescheduled, or canceled. To prevent conflicting changes, the current revision must be specified when updating the booking.
     */
    revision?: string | null;
    /**
     * ID of the creator of the booking.
     * If `appId` and another ID are present, the other ID takes precedence.
     * @readonly
     */
    createdBy?: CommonIdentificationData;
    /**
     * The start date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the start date of the slot. For a schedule, this is the start date of the first session.
     * @readonly
     */
    startDate?: Date | null;
    /**
     * The end date of the booking in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * For a slot, this is the end date of the slot. For a schedule, this is the end date of the last session.
     * @readonly
     */
    endDate?: Date | null;
    /**
     * Date and time the booking was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Custom field data for this object.
     * Extended fields must be configured in the app dashboard before they can be accessed with API calls.
     */
    extendedFields?: ExtendedFields;
    /**
     * Whether this booking overlaps with another confirmed booking. Returned
     * only if set to `true`.
     * @readonly
     */
    doubleBooked?: boolean | null;
}
/** @oneof */
interface BookingParticipantsInfoOneOf {
    /**
     * Total number of participants.
     * When creating a booking, use this field only if the relevant service has fixed pricing and doesn't offer variants and options ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    totalParticipants?: number;
    /**
     * Information about the booked service choices and participant count for each choice.
     * When creating a booking, use this field only if the booking includes multiple service variants
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     *
     * For example, use this for a spa package booking that includes different service levels:
     * - 2 participants chose "Standard Package".
     * - 1 participant chose "VIP Package".
     */
    participantsChoices?: ParticipantChoices;
}
/**
 * A multi-service booking is considered available if all single-service bookings are available as returned from List Multi Service Availability Time Slots.
 * Currently, `SEPARATE_BOOKINGS` and `PARALLEL_BOOKINGS` are not supported.
 * Multi-service booking is available if each of its bookings is available separately.
 * For `SEQUENTIAL_BOOKINGS`, see `List Multi Service Availability Time Slots` documentation.
 */
declare enum MultiServiceBookingType {
    /** You must schedule single-service bookings back-to-back with each booking starting when the previous booking ends */
    SEQUENTIAL_BOOKINGS = "SEQUENTIAL_BOOKINGS",
    /** Not currently supported. */
    SEPARATE_BOOKINGS = "SEPARATE_BOOKINGS",
    /** Not currently supported. */
    PARALLEL_BOOKINGS = "PARALLEL_BOOKINGS"
}
/** @enumType */
type MultiServiceBookingTypeWithLiterals = MultiServiceBookingType | 'SEQUENTIAL_BOOKINGS' | 'SEPARATE_BOOKINGS' | 'PARALLEL_BOOKINGS';
interface BookedEntity extends BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
    /**
     * Session title at the time of booking. If there is no pre-existing session,
     * for example for appointment-based services, Wix Bookings sets `title` to the service name.
     * @readonly
     * @maxLength 6000
     */
    title?: string | null;
    /**
     * List of tags for the booking.
     *
     * - "INDIVIDUAL": For bookings of appointment-based services. Including when the appointment is for a group of participants.
     * - "GROUP": For bookings of individual class sessions.
     * - "COURSE": For course bookings.
     */
    tags?: string[] | null;
}
/** @oneof */
interface BookedEntityItemOneOf {
    /**
     * Booked slot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/availability-calendar/query-availability) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings-and-time-slots/time-slots/availability-calendar/query-availability)).
     *
     * Specify `slot` when creating bookings for:
     * - **Appointment-based services:** Individual sessions with service providers (consultations, treatments).
     * Wix Bookings creates a new session when the booking is confirmed.
     * - **Class services:** Group sessions at specific times (fitness classes, workshops).
     * Wix Bookings links the booking to an existing scheduled session.
     *
     * For course services, specify `schedule` instead of `slot`.
     */
    slot?: BookedSlot;
    /**
     * Booked schedule
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)).
     *
     * Specify `schedule` when creating bookings for:
     * - **Course services:** Multi-session offerings spanning weeks or months (educational courses, training programs).
     * Wix Bookings enrolls participants in all sessions defined by the course schedule.
     */
    schedule?: BookedSchedule;
}
interface BookedSlot {
    /** Session ID. */
    sessionId?: string | null;
    /** Service ID. */
    serviceId?: string;
    /** Schedule ID. */
    scheduleId?: string;
    /**
     * ID of the corresponding event
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).
     * Available for both appointment and class bookings, not available for course bookings.
     * For appointment-based services, Wix Bookings automatically populates `eventId` when the booking `status` changes to `CONFIRMED`.
     * For class bookings, Wix Bookings automatically populates `eventId` upon booking creation.
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
    /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    startDate?: string | null;
    /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    endDate?: string | null;
    /** The time zone according to which the slot was shown to the user when booking, and should be shown in the future. */
    timezone?: string | null;
    /**
     * Primary resource
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resources/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resources-v2/introduction)) for the booking.
     * For example, the staff member ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction)) providing the service.
     */
    resource?: BookedResource;
    /** Location where the session takes place. */
    location?: Location;
}
interface BookedResource {
    /**
     * ID of the booking's primary resource.
     * @format GUID
     */
    id?: string;
    /**
     * Resource's name at the time of booking.
     * @maxLength 40
     */
    name?: string | null;
    /**
     * Resource's email at the time of booking.
     * @maxLength 500
     */
    email?: string | null;
    /**
     * ID of the schedule belonging to the booking's primary resource.
     * @format GUID
     */
    scheduleId?: string | null;
}
interface Location {
    /**
     * Business location ID. Available only for locations that are business locations,
     * meaning the `location_type` is `"OWNER_BUSINESS"`.
     * @format GUID
     */
    id?: string | null;
    /** Location name. */
    name?: string | null;
    /** The full address of this location. */
    formattedAddress?: string | null;
    /**
     * The full translated address of this location.
     * @maxLength 512
     */
    formattedAddressTranslated?: string | null;
    /**
     * Location type.
     *
     * - `"OWNER_BUSINESS"`: The business address, as set in the site’s general settings.
     * - `"OWNER_CUSTOM"`: The address as set when creating the service.
     * - `"CUSTOM"`: The address as set for the individual session.
     */
    locationType?: LocationTypeWithLiterals;
}
declare enum LocationType {
    UNDEFINED = "UNDEFINED",
    OWNER_BUSINESS = "OWNER_BUSINESS",
    OWNER_CUSTOM = "OWNER_CUSTOM",
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationTypeWithLiterals = LocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface BookedSchedule {
    /** Schedule ID ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)). */
    scheduleId?: string;
    /**
     * Booked service ID.
     * @format GUID
     */
    serviceId?: string | null;
    /**
     * Location ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)) where the schedule's sessions take place.
     * @readonly
     */
    location?: Location;
    /**
     * Time zone in which the slot or session was shown to the customer when they booked.
     * Also used whenever the customer reviews the booking's timing in the future.
     */
    timezone?: string | null;
    /**
     * Start time of the first session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    firstSessionStart?: string | null;
    /**
     * End time of the last session related to the booking in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`.
     * @readonly
     */
    lastSessionEnd?: string | null;
}
interface ContactDetails {
    /**
     * Contact ID.
     * @format GUID
     */
    contactId?: string | null;
    /**
     * Contact's first name. When populated from a standard booking form, this
     * property corresponds to the `name` field.
     */
    firstName?: string | null;
    /** Contact's last name. */
    lastName?: string | null;
    /**
     * Contact's email. If no [contact](https://dev.wix.com/docs/rest/crm/members-contacts/contacts/contacts/contact-v4/contact-object)
     * with this email exist, a new contact is created.
     * Used to validate coupon usage limitations per contact. If not specified,
     * the coupon usage limitation will not be enforced. (Coupon usage limitation
     * validation is not supported yet).
     * @format EMAIL
     */
    email?: string | null;
    /** Contact's phone number. */
    phone?: string | null;
    /** Contact's full address. */
    fullAddress?: Address;
    /**
     * Contact's time zone.
     * @deprecated
     */
    timeZone?: string | null;
    /**
     * Contact's country in [ISO 3166-1 alpha-2 code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
     * format.
     * @format COUNTRY
     */
    countryCode?: string | null;
}
/** Physical address */
interface Address extends AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
    /**
     * Country code.
     * @format COUNTRY
     */
    country?: string | null;
    /** Subdivision. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    subdivision?: string | null;
    /** City name. */
    city?: string | null;
    /** Zip/postal code. */
    postalCode?: string | null;
    /** Free text providing more detailed address info. Usually contains Apt, Suite, and Floor. */
    addressLine2?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Free text to help find the address. */
    hint?: string | null;
    /** Coordinates of the physical address. */
    geocode?: AddressLocation;
    /** Country full name. */
    countryFullname?: string | null;
    /** Multi-level subdivisions from top to bottom. */
    subdivisions?: Subdivision[];
}
/** @oneof */
interface AddressStreetOneOf {
    /** Street name, number and apartment number. */
    streetAddress?: StreetAddress;
    /** Main address line, usually street and number, as free text. */
    addressLine?: string | null;
}
interface StreetAddress {
    /** Street number. */
    number?: string;
    /** Street name. */
    name?: string;
    /** Apartment number. */
    apt?: string;
}
interface AddressLocation {
    /** Address latitude. */
    latitude?: number | null;
    /** Address longitude. */
    longitude?: number | null;
}
interface Subdivision {
    /** Subdivision code. Usually state, region, prefecture or province code, according to [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2). */
    code?: string;
    /** Subdivision full name. */
    name?: string;
}
interface CustomFormField {
    /**
     * ID of the form field as defined in the form.
     * @format GUID
     */
    id?: string;
    /** Value that was submitted for this field. */
    value?: string | null;
    /**
     * Form field's label at the time of submission.
     * @readonly
     */
    label?: string | null;
    valueType?: ValueTypeWithLiterals;
}
declare enum ValueType {
    /** Short text. This is the default value type. */
    SHORT_TEXT = "SHORT_TEXT",
    /** Long text. */
    LONG_TEXT = "LONG_TEXT",
    /** A text that represents the check box value. If selected the value is `true`, otherwise `false`. */
    CHECK_BOX = "CHECK_BOX"
}
/** @enumType */
type ValueTypeWithLiterals = ValueType | 'SHORT_TEXT' | 'LONG_TEXT' | 'CHECK_BOX';
/** Booking status. */
declare enum BookingStatus {
    /** The booking was created, but the customer hasn't completed the related eCommerce order yet. */
    CREATED = "CREATED",
    /**
     * The merchant has confirmed the booking and it appears in the business calendar. Merchants can set up their services
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) to automatically confirm all `PENDING` bookings.
     */
    CONFIRMED = "CONFIRMED",
    /**
     * The customer has canceled the booking. Depending on the relevant service's policy snapshot
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policy-snapshots/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policy-snapshots/introduction))
     * they may have to pay a cancellation fee ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-fees/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/pricing/booking-fees/introduction)).
     */
    CANCELED = "CANCELED",
    /** The merchant must manually confirm the booking before it appears in the business calendar. */
    PENDING = "PENDING",
    /** The merchant has declined the booking before the customer was charged. */
    DECLINED = "DECLINED",
    /**
     * The booking is on a waitlist.
     * Currently, you can't call Register to Waitlist ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/bookings/waitlist/register-to-waitlist)) for course or appointment bookings, even though this is supported in live sites.
     * You can call Register to Waitlist only for class session bookings.
     */
    WAITING_LIST = "WAITING_LIST"
}
/** @enumType */
type BookingStatusWithLiterals = BookingStatus | 'CREATED' | 'CONFIRMED' | 'CANCELED' | 'PENDING' | 'DECLINED' | 'WAITING_LIST';
/**
 * Payment status.
 * Automatically updated when using eCommerce checkout APIs.
 */
declare enum PaymentStatus {
    /** Undefined payment status. */
    UNDEFINED = "UNDEFINED",
    /** The booking isn't paid. */
    NOT_PAID = "NOT_PAID",
    /** The booking is fully paid. */
    PAID = "PAID",
    /** The booking is partially paid. */
    PARTIALLY_PAID = "PARTIALLY_PAID",
    /** The booking is refunded. */
    REFUNDED = "REFUNDED",
    /** The booking is free of charge. */
    EXEMPT = "EXEMPT"
}
/** @enumType */
type PaymentStatusWithLiterals = PaymentStatus | 'UNDEFINED' | 'NOT_PAID' | 'PAID' | 'PARTIALLY_PAID' | 'REFUNDED' | 'EXEMPT';
/**
 * Selected payment option.
 *
 * One of the payment options offered by the service.
 * This field is set when the user selects an option during booking.
 * If left undefined, the payment option is resolved by the service configuration on checkout.
 */
declare enum SelectedPaymentOption {
    /** Undefined payment option. */
    UNDEFINED = "UNDEFINED",
    /** Offline payment. */
    OFFLINE = "OFFLINE",
    /** Online payment. */
    ONLINE = "ONLINE",
    /** Payment using a Wix Pricing Plan. */
    MEMBERSHIP = "MEMBERSHIP",
    /**
     * Customers can pay only in person with a Wix Pricing Plan, while the Wix user
     * must manually redeem the pricing plan in the dashboard.
     */
    MEMBERSHIP_OFFLINE = "MEMBERSHIP_OFFLINE"
}
/** @enumType */
type SelectedPaymentOptionWithLiterals = SelectedPaymentOption | 'UNDEFINED' | 'OFFLINE' | 'ONLINE' | 'MEMBERSHIP' | 'MEMBERSHIP_OFFLINE';
interface ParticipantNotification {
    /**
     * Whether to send a message about the changes to the customer.
     *
     * Default: `false`
     */
    notifyParticipants?: boolean;
    /** Custom message to send to the participants about the changes to the booking. */
    message?: string | null;
}
interface CommonIdentificationData extends CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /**
     * ID of of a contact in the site's [CRM by Ascend](https://www.wix.com/ascend/crm) system.
     * @format GUID
     */
    contactId?: string | null;
}
/** @oneof */
interface CommonIdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface ParticipantChoices {
    /**
     * Information about the booked service choices. Includes the number of participants.
     * @minSize 1
     * @maxSize 20
     */
    serviceChoices?: ServiceChoices[];
}
interface ServiceChoices {
    /**
     * Number of participants for this variant ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * @min 1
     */
    numberOfParticipants?: number | null;
    /**
     * Service choices for these participants.
     * @maxSize 5
     */
    choices?: ServiceChoice[];
}
interface ServiceChoice extends ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
    /**
     * ID of the corresponding option for the choice. For example, the choice `child`
     * could correspond to the option `ageGroup`. In this case, `optionId` is the ID
     * for the `ageGroup` option.
     * @format GUID
     */
    optionId?: string;
}
/** @oneof */
interface ServiceChoiceChoiceOneOf {
    /**
     * Value for one of the choices in the [`CustomServiceOption.choices`](https://example.com) list.
     * Choices are specific values for an option the customer can choose to book. For example,
     * the option `ageGroup` may have these choices: `child`, `student`, `adult`, and `senior`.
     * Each choice may have a different price.
     */
    custom?: string;
    duration?: Duration;
}
interface Duration {
    /**
     * Duration of the service in minutes.
     * Min: 1 minute, Max: 30 days, 23 hours, and 59 minutes
     * @min 1
     * @max 44639
     */
    minutes?: number;
    /**
     * Name of the duration option.
     * Defaults to the formatted duration e.g. "1 hour, 30 minutes".
     * @maxLength 255
     */
    name?: string | null;
}
interface ConfirmOrDeclineBookingRequest {
    /**
     * ID of the booking to confirm or decline.
     * @format GUID
     */
    bookingId: string;
    /**
     * Current payment status of the booking when using a custom checkout page and
     * not the *eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     *
     * The booking is declined if there is a double booking conflict and you provide
     * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
}
interface ConfirmOrDeclineBookingResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface BulkConfirmOrDeclineBookingRequest {
    /**
     * Bookings to confirm or decline.
     * @minSize 1
     * @maxSize 300
     */
    details: BulkConfirmOrDeclineBookingRequestBookingDetails[];
    /** Whether to return the confirmed or declined booking objects. */
    returnEntity?: boolean;
}
interface BulkConfirmOrDeclineBookingRequestBookingDetails {
    /**
     * ID of the booking to confirm or decline.
     * @format GUID
     */
    bookingId?: string;
    /**
     * Current payment status of the booking when using a custom checkout page and
     * not the *eCommerce checkout*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/ecom/checkout/setup) | [REST](https://dev.wix.com/docs/rest/business-solutions/e-commerce/checkout/introduction)).
     *
     * The booking is declined if there is a double booking conflict and you provide
     * one of these payment statuses: `UNDEFINED`, `NOT_PAID`, `REFUNDED`, or `EXEMPT`.
     */
    paymentStatus?: PaymentStatusWithLiterals;
}
interface BulkConfirmOrDeclineBookingResponse {
    /** List of confirmed or declined bookings, including metadata. */
    results?: BulkBookingResult[];
    /** Total successes and failures of the Bulk Confirm Or Decline call. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface BulkBookingResult {
    /**
     * Information about the booking that was created or updated.
     * Including its ID, index in the bulk request and whether it was
     * successfully created or updated.
     */
    itemMetadata?: ItemMetadata;
    /**
     * Created or updated booking. Available only if you requested
     * to return the booking entity.
     */
    item?: Booking;
}
interface ItemMetadata {
    /** Item ID. Should always be available, unless it's impossible (for example, when failing to create an item). */
    id?: string | null;
    /** Index of the item within the request array. Allows for correlation between request and response items. */
    originalIndex?: number;
    /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */
    success?: boolean;
    /** Details about the error in case of failure. */
    error?: ApplicationError;
}
interface ApplicationError {
    /** Error code. */
    code?: string;
    /** Description of the error. */
    description?: string;
    /** Data related to the error. */
    data?: Record<string, any> | null;
}
interface BulkActionMetadata {
    /** Number of items that were successfully processed. */
    totalSuccesses?: number;
    /** Number of items that couldn't be processed. */
    totalFailures?: number;
    /** Number of failures without details because detailed failure threshold was exceeded. */
    undetailedFailures?: number;
}
interface CreateBookingRequest {
    /** The booking to create. */
    booking: Booking;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     * Default: `true`.
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when creating a booking.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
}
interface CreateBookingFlowControlSettings {
    /**
     * Whether the availability is checked before creating the booking.
     *
     * - `false`: A booking is only created when the slot or schedule is available.
     * - `true`: The booking is created regardless of availability conflicts. Make sure the call's [identity](https://dev.wix.com/docs/build-apps/develop-your-app/access/about-identities) has the required permissions.
     *
     * *Use cases for `true`:**
     * - Emergency or priority bookings that must be accommodated.
     * - Administrative bookings that override normal availability rules.
     * - Testing or demonstration purposes.
     *
     * Default: `false`.
     */
    skipAvailabilityValidation?: boolean;
    /**
     * Whether `PENDING` bookings are automatically set to `CONFIRMED` for
     * services that normally require the owner's manual confirmation.
     *
     * Your app must have the `BOOKINGS.OVERRIDE_AVAILABILITY` permission
     * when passing `true`.
     * Default: `false`.
     */
    skipBusinessConfirmation?: boolean;
    /**
     * Whether customers can pay using a payment method that isn't supported
     * for the service, but that's supported for other services.
     *
     * Your app must have the `BOOKINGS.MANAGE_PAYMENTS` permission when passing
     * `true`.
     * Default: `false`.
     */
    skipSelectedPaymentOptionValidation?: boolean;
}
interface CreateBookingResponse {
    /** Created booking. */
    booking?: Booking;
}
interface BulkCreateBookingRequest {
    /**
     * Bookings to create.
     *
     * Max: 8 bookings
     * @minSize 1
     * @maxSize 8
     */
    createBookingsInfo: CreateBookingInfo[];
    /** Whether to return the created bookings. */
    returnFullEntity?: boolean;
}
interface CreateBookingInfo {
    /** Booking to create. */
    booking?: Booking;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     * Default: `true`.
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when creating a booking.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
}
interface BulkCreateBookingResponse {
    /** List of individual Bulk Create Bookings results. */
    results?: BulkBookingResult[];
    /** Total number of successes and failures for Bulk Create Bookings. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface RescheduleBookingRequest extends RescheduleBookingRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Specify when not all
     * participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
    /**
     * ID of the booking to reschedule.
     * @format GUID
     */
    bookingId: string;
    /** New slot of the booking. */
    slot: V2Slot;
    /**
     * Revision number, which increments by 1 each time the booking is rescheduled.
     * To prevent conflicting changes, the current revision must be passed when
     * rescheduling the booking.
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer about the rescheduling and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when rescheduling a booking.
     */
    flowControlSettings?: RescheduleBookingFlowControlSettings;
}
/** @oneof */
interface RescheduleBookingRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Specify when not all
     * participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
interface V2Slot {
    /** Identifier for the underlying session when the session is a single session or generated from a recurring session. */
    sessionId?: string | null;
    /** Service identifier. Required. */
    serviceId?: string;
    /** Schedule identifier. Required. */
    scheduleId?: string;
    /** The start time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    startDate?: string | null;
    /** The end time of this slot in `YYYY-MM-DDThh:mm:ss`, `YYYY-MM-DDThh:mm:ss:SSS`, or `YYYY-MM-DDThh:mm:ss:SSSZZ` [ISO-8601 format](https://en.wikipedia.org/wiki/ISO_8601). For example, `2026-01-30T13:30:00`, `2026-01-30T13:30:00:000`, or `2026-01-30T13:30:00:000-05:00`. */
    endDate?: string | null;
    /** The timezone according to which the slot is calculated and presented. */
    timezone?: string | null;
    /**
     * The resource required for this slot.
     * When populated, the specified resource will be assigned to the slot upon confirmation according to its availability.
     * When empty, if `skip_availability_validation` is `false`, a random available resource will be assigned to the slot upon confirmation.
     * Otherwise, one of the service resources will be assigned to the slot randomly upon confirmation.
     */
    resource?: SlotSlotResource;
    /** Geographic location of the slot. */
    location?: SlotLocation;
    /**
     * Calendar event ID - not supported.
     * If not empty, on all write flows (create/update), it takes priority over `sessionId`.
     * So if both `sessionId` and `eventId` are provided, the `sessionId` will be based on the `eventId`.
     * Otherwise, if `eventId` is empty on write flow,
     * @minLength 36
     * @maxLength 250
     */
    eventId?: string | null;
}
declare enum LocationLocationType {
    /** Undefined location type. */
    UNDEFINED = "UNDEFINED",
    /** The business address as set in the site’s general settings. */
    OWNER_BUSINESS = "OWNER_BUSINESS",
    /** The address set when creating the service. */
    OWNER_CUSTOM = "OWNER_CUSTOM",
    /** The address set for the individual session. */
    CUSTOM = "CUSTOM"
}
/** @enumType */
type LocationLocationTypeWithLiterals = LocationLocationType | 'UNDEFINED' | 'OWNER_BUSINESS' | 'OWNER_CUSTOM' | 'CUSTOM';
interface SlotSlotResource {
    /**
     * Resource ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Resource name.
     * @maxLength 1200
     */
    name?: string | null;
}
interface SlotLocation {
    /**
     * Business Location ID. Present if the location is a business location.
     * @format GUID
     */
    id?: string | null;
    /** Location name. */
    name?: string | null;
    /** A string containing the full address of this location. */
    formattedAddress?: string | null;
    /** Location type. */
    locationType?: LocationLocationTypeWithLiterals;
}
interface RescheduleBookingFlowControlSettings {
    /**
     * Whether the rescheduling policy applies when rescheduling the booking.
     *
     * When passing `false`, you can only cancel a booking if the rescheduling
     * policy allows it.
     * Default: `false`.
     */
    ignoreReschedulePolicy?: boolean;
    /**
     * Whether the availability is checked before rescheduling the booking.
     *
     * When passing `false`, a booking is only created when the slot or
     * schedule is available.
     * Default: `false`.
     */
    skipAvailabilityValidation?: boolean;
    /**
     * Whether the rescheduled booking's status is automatically set to
     * `CONFIRMED` for services that normally require the owner's manual
     * confirmation.
     * Default: `false`.
     */
    skipBusinessConfirmation?: boolean;
}
interface RescheduleBookingResponse {
    /** Rescheduled booking. */
    booking?: Booking;
}
interface ConfirmBookingRequest {
    /**
     * ID of the booking to confirm.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be passed when
     * updating the booking.
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer about the confirmation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability when confirming a booking.
     */
    flowControlSettings?: ConfirmBookingFlowControlSettings;
}
interface ConfirmBookingFlowControlSettings {
    /**
     * Whether the availability is checked before confirming the booking.
     *
     * When specifying `false`, a booking is only updated with status `CONFIRMED`.
     * Default: `false`.
     */
    checkAvailabilityValidation?: boolean;
}
interface ConfirmBookingResponse {
    /** Confirmed booking. */
    booking?: Booking;
}
interface SetBookingSubmissionIdRequest {
    /**
     * ID of the booking to set `submissionId` for.
     * @format GUID
     */
    bookingId: string;
    /**
     * ID of the form submission to set on the booking.
     * @format GUID
     */
    submissionId: string;
}
interface SetBookingSubmissionIdResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface UpdateExtendedFieldsRequest {
    /** ID of the entity to update. */
    id: string;
    /** Identifier for the app whose extended fields are being updated. */
    namespace: string;
    /** Data to update. Structured according to the [schema](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields#json-schema-for-extended-fields) defined when the extended fields were configured. */
    namespaceData: Record<string, any> | null;
}
interface UpdateExtendedFieldsResponse {
    /**
     * Updated namespace.
     * @maxLength 164
     */
    namespace?: string;
    /** Updated data. */
    namespaceData?: Record<string, any> | null;
}
interface DeclineBookingRequest {
    /**
     * ID of the booking to decline.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * declining the booking.
     * @min 1
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to refund a declined booking.
     */
    flowControlSettings?: DeclineBookingFlowControlSettings;
}
interface DeclineBookingFlowControlSettings {
    /**
     * Whether to issue a refund when declining the booking.
     *
     * The refund will be issued only if the booking is refundable.
     * Currently, a booking is considered refundable when it was paid by membership.
     * If specifying `true`, the booking flow control settings will be set with a refund.
     * If you specify `false` or an empty field,
     * the booking flow control settings are set without refund.
     *
     * Default: `false`.
     */
    withRefund?: boolean | null;
}
interface DeclineBookingResponse {
    /** Declined booking. */
    booking?: Booking;
}
interface CancelBookingRequest {
    /**
     * ID of the booking to cancel.
     * @format GUID
     */
    bookingId: string;
    /**
     * Information about whether to notify the customer about the cancellation and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to allow a cancellation even though the service's
     * policy doesn't allow it.
     */
    flowControlSettings?: CancelBookingFlowControlSettings;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * managing the booking.
     */
    revision: string | null;
}
interface CancelBookingFlowControlSettings {
    /**
     * Whether the cancellation policy applies when canceling the booking.
     *
     * When passing `false`, you can only cancel a booking if the cancellation policy allows it.
     * Default: `false`.
     */
    ignoreCancellationPolicy?: boolean | null;
    /**
     * Whether to issue a refund when canceling the booking.
     *
     * The refund will be issued only if the booking is refundable.
     * Currently, a booking is considered refundable when it was paid by membership.
     * If you specify `true`, the booking flow control settings is set to include a refund.
     * If `false` is specified or the field remains empty,
     * the booking flow control settings are set without refund.
     *
     * Default: `false`.
     */
    withRefund?: boolean | null;
}
interface CancelBookingResponse {
    /** Canceled booking. */
    booking?: Booking;
}
interface UpdateNumberOfParticipantsRequest extends UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices the participants have booked. Specify
     * when not all participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
    /**
     * ID of the booking to update the number of participants for.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified
     * when updating the booking.
     */
    revision: string | null;
}
/** @oneof */
interface UpdateNumberOfParticipantsRequestParticipantsInfoOneOf {
    /**
     * Total number of participants. Specify when all participants have booked the
     * same service variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices the participants have booked. Specify
     * when not all participants have booked the same service variant.
     */
    participantsChoices?: ParticipantChoices;
}
interface UpdateNumberOfParticipantsResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface BulkCalculateAllowedActionsResult {
    /** Metadata for the booking. Including ID, index in the provided sequence, success status, and error. */
    itemMetadata?: ItemMetadata;
    /** Booking entity. */
    item?: AllowedActions;
}
/** Possible actions allowed for the booking. */
interface AllowedActions {
    /** Whether canceling the booking is allowed. */
    cancel?: boolean;
    /** Whether rescheduling the booking is allowed. */
    reschedule?: boolean;
}
interface BookingPolicyViolations {
    /** Booking policy violation: Too early to book this slot. */
    tooEarlyToBook?: boolean | null;
    /** Booking policy violation: Too late to book this slot. */
    tooLateToBook?: boolean | null;
    /** Booking policy violation: Online booking is disabled for this slot. */
    bookOnlineDisabled?: boolean | null;
}
interface BookingPolicySettings {
    /** Booking policy settings for a given slot or schedule. */
    maxParticipantsPerBooking?: number | null;
}
interface MarkBookingAsPendingRequest {
    /**
     * ID of the booking to mark as `PENDING`.
     * @format GUID
     */
    bookingId: string;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     *
     * To prevent conflicting changes, the current revision must be specified when
     * managing the booking.
     */
    revision: string | null;
    /**
     * Information about whether to notify the customer and
     * the message to send.
     */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the
     * session starts. The phone number is taken from `contactDetails.phone`.
     */
    sendSmsReminder?: boolean | null;
    /** Payment status to set for the booking. */
    paymentStatus?: PaymentStatusWithLiterals;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to check availability before updating the status.
     */
    flowControlSettings?: MarkBookingAsPendingFlowControlSettings;
}
interface MarkBookingAsPendingFlowControlSettings {
    /**
     * Whether to check for double bookings before updating the booking as pending.
     *
     * When passing `false`, a booking is only updated with status `PENDING`.
     * Default: `false`.
     */
    checkAvailabilityValidation?: boolean;
    /**
     * Whether to validate that the booking to be marked as pending has a `booking.slot.serviceId`
     * of a pending approval service.
     *
     * Default: `false`.
     */
    skipPendingApprovalServiceValidation?: boolean;
}
interface MarkBookingAsPendingResponse {
    /** Updated booking. */
    booking?: Booking;
}
interface CreateMultiServiceBookingRequest {
    /**
     * Between 2 to 8 new single-service booking specifications to create and combine in a multi-service booking.
     *
     * Each single-service booking specification must include `slot` details (`scheduleId`, `startDate`, and `endDate`).
     * Specify contact details, number of participants, and any additional fields as needed.
     *
     * For sequential bookings, ensure the timing allows services to be scheduled back-to-back.
     * @minSize 2
     * @maxSize 8
     */
    bookings: Booking[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /**
     * Whether to ignore specific standard procedures of the Wix Bookings flow.
     * For example, whether to skip checking availability before updating the status.
     */
    flowControlSettings?: CreateBookingFlowControlSettings;
    /** Whether to return the created single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Multi-service booking type.
     *
     * Currently only `SEQUENTIAL_BOOKINGS` is supported.
     */
    multiServiceBookingType?: MultiServiceBookingTypeWithLiterals;
}
interface CreateMultiServiceBookingResponse {
    /**
     * Created multi-service booking.
     * Contains the single-service bookings in the same order as specified in the request.
     */
    multiServiceBooking?: MultiServiceBooking;
}
/**
 * A multi-service booking combines multiple single-service bookings into a package.
 *
 * Currently, multi-service bookings support only sequential scheduling where services are scheduled back-to-back at the same location, with each single-service booking starting when the previous 1 ends.
 */
interface MultiServiceBooking {
    /**
     * Multi-service booking ID.
     * @format GUID
     */
    id?: string | null;
    /** The single-service bookings that make up the multi-service booking package. */
    bookings?: BookingResult[];
}
interface BookingResult {
    /**
     * Booking ID.
     * @format GUID
     */
    bookingId?: string | null;
    /** Booking entity. */
    booking?: Booking;
}
interface RescheduleMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to reschedule.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to reschedule. */
    rescheduleBookingsInfo: RescheduleBookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings rescheduling flow are changed.
     * For example, whether the availability of the new slot is checked before rescheduling the booking or if the customer can reschedule the booking even though the service's rescheduling policy doesn't allow it.
     */
    flowControlSettings?: RescheduleBookingFlowControlSettings;
    /** Whether to return the rescheduled bookings entities. */
    returnFullEntity?: boolean;
}
interface RescheduleBookingInfo extends RescheduleBookingInfoParticipantsInfoOneOf {
    /**
     * Total number of participants. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when all participants book the same variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when not all participants book the same variant.
     */
    participantsChoices?: ParticipantChoices;
    /**
     * ID of the booking to reschedule.
     * @format GUID
     */
    bookingId?: string | null;
    /** Information about the new slot. */
    slot?: V2Slot;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be specified when managing the booking.
     */
    revision?: string | null;
}
/** @oneof */
interface RescheduleBookingInfoParticipantsInfoOneOf {
    /**
     * Total number of participants. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when all participants book the same variant.
     */
    totalParticipants?: number;
    /**
     * Information about the service choices to book. Available only for services with variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).
     * Specify when not all participants book the same variant.
     */
    participantsChoices?: ParticipantChoices;
}
interface RescheduleMultiServiceBookingResponse {
    /** Rescheduled multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface GetMultiServiceBookingAvailabilityRequest {
    /**
     * ID of the multi-service booking to retrieve.
     * @format GUID
     */
    multiServiceBookingId: string | null;
}
interface GetMultiServiceBookingAvailabilityResponse {
    /** Whether all contained single-service bookings are bookable. */
    bookable?: boolean;
    /** Total number of spots. */
    totalCapacity?: number | null;
    /** Number of open spots. */
    remainingCapacity?: number | null;
    /** Indicators for policy violations of the multi-service booking. */
    policyViolations?: BookingPolicyViolations;
    /** Multi-service booking policy settings. */
    policySettings?: BookingPolicySettings;
    /** Details of the multi-service booking. */
    multiServiceBookingInfo?: GetMultiServiceBookingAvailabilityResponseBookingInfo[];
}
interface GetMultiServiceBookingAvailabilityResponseBookingInfo {
    /**
     * Booking ID.
     * @format GUID
     */
    bookingId?: string | null;
}
interface CancelMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to cancel.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Information about whether specific procedures of the standard Wix Bookings cancellation flow are changed.
     * For example, whether the customer can cancel the booking even though the service's cancellation policy doesn't allow it or whether to issue a refund upon cancellation.
     */
    flowControlSettings?: CancelBookingFlowControlSettings;
    /** Whether to return the canceled single-service bookings. */
    returnFullEntity?: boolean;
}
interface CancelMultiServiceBookingResponse {
    /** Canceled multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface MarkMultiServiceBookingAsPendingRequest {
    /**
     * ID of the multi-service booking to mark as `PENDING`.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to mark as `PENDING`. */
    markAsPendingBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /**
     * Whether to return the single-service bookings that were marked as `PENDING`.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings creation flow are changed.
     * For example, whether Wix Bookings checks availability before updating the booking.
     */
    flowControlSettings?: MarkBookingAsPendingFlowControlSettings;
}
interface BookingInfo {
    /**
     * ID of the single-service booking.
     * @format GUID
     */
    bookingId?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be specified when managing the booking.
     */
    revision?: string | null;
    /** Payment status to set for the single-service booking. */
    paymentStatus?: PaymentStatusWithLiterals;
}
interface MarkMultiServiceBookingAsPendingResponse {
    /** Updated multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface ConfirmMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to confirm its related bookings.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to confirm. */
    confirmBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /**
     * Whether to send an SMS reminder to the customer 24 hours before the session starts.
     * Wix Bookings takes the phone number from `contactDetails.phone`.
     *
     * Default: `true`
     */
    sendSmsReminder?: boolean | null;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /** Whether to return the confirmed single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings confirmation flow are changed.
     * For example, whether Wix Bookings checks availability before confirming the booking.
     */
    flowControlSettings?: ConfirmBookingFlowControlSettings;
}
interface ConfirmMultiServiceBookingResponse {
    /** Confirmed multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface DeclineMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking to decline.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /** Information about the single-service bookings to decline. */
    declineBookingsInfo?: BookingInfo[];
    /** Information about whether to notify the customer and the message to send. */
    participantNotification?: ParticipantNotification;
    /** Whether this booking has a conflict with at least 1 other confirmed booking. */
    doubleBooked?: boolean | null;
    /** Whether to return the declined single-service bookings. */
    returnFullEntity?: boolean;
    /**
     * Information about whether specific procedures of the standard Wix Bookings declining flow are changed.
     * For example, whether to issue a refund upon cancellation.
     */
    flowControlSettings?: DeclineBookingFlowControlSettings;
}
interface DeclineMultiServiceBookingResponse {
    /** Declined multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
}
interface BulkGetMultiServiceBookingAllowedActionsRequest {
    /**
     * IDs of the multi-service bookings to retrieve allowed actions for.
     * @format GUID
     * @minSize 1
     * @maxSize 50
     */
    multiServiceBookingIds: string[] | null;
}
interface BulkGetMultiServiceBookingAllowedActionsResponse {
    /**
     * Information about the multi-service bookings that were retrieved.
     * Includes their ID, index in the bulk request and whether they were successfully processed.
     */
    results?: BulkCalculateAllowedActionsResult[];
    /** Total number of successes and failures for Bulk Get Multi Service Booking Allowed Actions. */
    bulkActionMetadata?: BulkActionMetadata;
}
interface GetMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking.
     * @format GUID
     */
    multiServiceBookingId: string | null;
}
interface GetMultiServiceBookingResponse {
    /** Retrieved multi-service booking. */
    multiServiceBooking?: MultiServiceBooking;
    /** Details about how many single-service bookings belong to the multi-service booking. */
    metadata?: MultiServiceBookingMetadata;
}
interface MultiServiceBookingMetadata {
    /**
     * Total number of `CONFIRMED` and `PENDING` single-service bookings belonging to the multi-service booking.
     * The total includes the number of single-service bookings which couldn't be retrieved due to lack of permissions.
     */
    totalNumberOfScheduledBookings?: number | null;
}
interface AddBookingsToMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /**
     * List of single-service booking IDs and their revision.
     * @maxSize 8
     */
    bookings: BookingIdAndRevision[];
    /**
     * Whether to return the single-service bookings that were added to the multi-service booking.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
}
interface BookingIdAndRevision {
    /**
     * ID of the single-service booking.
     * @format GUID
     */
    bookingId?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking is updated.
     * To prevent conflicting changes, the current revision must be specified when managing the booking.
     */
    revision?: string | null;
}
interface AddBookingsToMultiServiceBookingResponse {
    /** Single-service bookings that were added to the multi-service booking. */
    bookings?: BookingResult[];
}
interface RemoveBookingsFromMultiServiceBookingRequest {
    /**
     * ID of the multi-service booking.
     * @format GUID
     */
    multiServiceBookingId: string | null;
    /**
     * List of single-service booking IDs and their revision.
     * @maxSize 8
     */
    bookings?: BookingIdAndRevision[];
    /**
     * Whether to return the single-service bookings.
     *
     * Default: `false`
     */
    returnFullEntity?: boolean;
}
interface RemoveBookingsFromMultiServiceBookingResponse {
    /** Single-service bookings that were removed from the multi-service booking. */
    bookings?: BookingResult[];
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function confirmOrDeclineBooking(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, ConfirmOrDeclineBookingRequest$1, ConfirmOrDeclineBookingRequest, ConfirmOrDeclineBookingResponse$1, ConfirmOrDeclineBookingResponse>;
declare function bulkConfirmOrDeclineBooking(): __PublicMethodMetaInfo<'POST', {}, BulkConfirmOrDeclineBookingRequest$1, BulkConfirmOrDeclineBookingRequest, BulkConfirmOrDeclineBookingResponse$1, BulkConfirmOrDeclineBookingResponse>;
declare function createBooking(): __PublicMethodMetaInfo<'POST', {}, CreateBookingRequest$1, CreateBookingRequest, CreateBookingResponse$1, CreateBookingResponse>;
declare function bulkCreateBooking(): __PublicMethodMetaInfo<'POST', {}, BulkCreateBookingRequest$1, BulkCreateBookingRequest, BulkCreateBookingResponse$1, BulkCreateBookingResponse>;
declare function rescheduleBooking(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, RescheduleBookingRequest$1, RescheduleBookingRequest, RescheduleBookingResponse$1, RescheduleBookingResponse>;
declare function confirmBooking(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, ConfirmBookingRequest$1, ConfirmBookingRequest, ConfirmBookingResponse$1, ConfirmBookingResponse>;
declare function setBookingSubmissionId(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, SetBookingSubmissionIdRequest$1, SetBookingSubmissionIdRequest, SetBookingSubmissionIdResponse$1, SetBookingSubmissionIdResponse>;
declare function updateExtendedFields(): __PublicMethodMetaInfo<'POST', {
    id: string;
}, UpdateExtendedFieldsRequest$1, UpdateExtendedFieldsRequest, UpdateExtendedFieldsResponse$1, UpdateExtendedFieldsResponse>;
declare function declineBooking(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, DeclineBookingRequest$1, DeclineBookingRequest, DeclineBookingResponse$1, DeclineBookingResponse>;
declare function cancelBooking(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, CancelBookingRequest$1, CancelBookingRequest, CancelBookingResponse$1, CancelBookingResponse>;
declare function updateNumberOfParticipants(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, UpdateNumberOfParticipantsRequest$1, UpdateNumberOfParticipantsRequest, UpdateNumberOfParticipantsResponse$1, UpdateNumberOfParticipantsResponse>;
declare function markBookingAsPending(): __PublicMethodMetaInfo<'POST', {
    bookingId: string;
}, MarkBookingAsPendingRequest$1, MarkBookingAsPendingRequest, MarkBookingAsPendingResponse$1, MarkBookingAsPendingResponse>;
declare function createMultiServiceBooking(): __PublicMethodMetaInfo<'POST', {}, CreateMultiServiceBookingRequest$1, CreateMultiServiceBookingRequest, CreateMultiServiceBookingResponse$1, CreateMultiServiceBookingResponse>;
declare function rescheduleMultiServiceBooking(): __PublicMethodMetaInfo<'POST', {
    multiServiceBookingId: string;
}, RescheduleMultiServiceBookingRequest$1, RescheduleMultiServiceBookingRequest, RescheduleMultiServiceBookingResponse$1, RescheduleMultiServiceBookingResponse>;
declare function getMultiServiceBookingAvailability(): __PublicMethodMetaInfo<'POST', {
    multiServiceBookingId: string;
}, GetMultiServiceBookingAvailabilityRequest$1, GetMultiServiceBookingAvailabilityRequest, GetMultiServiceBookingAvailabilityResponse$1, GetMultiServiceBookingAvailabilityResponse>;
declare function cancelMultiServiceBooking(): __PublicMethodMetaInfo<'POST', {
    multiServiceBookingId: string;
}, CancelMultiServiceBookingRequest$1, CancelMultiServiceBookingRequest, CancelMultiServiceBookingResponse$1, CancelMultiServiceBookingResponse>;
declare function markMultiServiceBookingAsPending(): __PublicMethodMetaInfo<'POST', {
    multiServiceBookingId: string;
}, MarkMultiServiceBookingAsPendingRequest$1, MarkMultiServiceBookingAsPendingRequest, MarkMultiServiceBookingAsPendingResponse$1, MarkMultiServiceBookingAsPendingResponse>;
declare function confirmMultiServiceBooking(): __PublicMethodMetaInfo<'POST', {
    multiServiceBookingId: string;
}, ConfirmMultiServiceBookingRequest$1, ConfirmMultiServiceBookingRequest, ConfirmMultiServiceBookingResponse$1, ConfirmMultiServiceBookingResponse>;
declare function declineMultiServiceBooking(): __PublicMethodMetaInfo<'POST', {
    multiServiceBookingId: string;
}, DeclineMultiServiceBookingRequest$1, DeclineMultiServiceBookingRequest, DeclineMultiServiceBookingResponse$1, DeclineMultiServiceBookingResponse>;
declare function bulkGetMultiServiceBookingAllowedActions(): __PublicMethodMetaInfo<'POST', {}, BulkGetMultiServiceBookingAllowedActionsRequest$1, BulkGetMultiServiceBookingAllowedActionsRequest, BulkGetMultiServiceBookingAllowedActionsResponse$1, BulkGetMultiServiceBookingAllowedActionsResponse>;
declare function getMultiServiceBooking(): __PublicMethodMetaInfo<'GET', {
    multiServiceBookingId: string;
}, GetMultiServiceBookingRequest$1, GetMultiServiceBookingRequest, GetMultiServiceBookingResponse$1, GetMultiServiceBookingResponse>;
declare function addBookingsToMultiServiceBooking(): __PublicMethodMetaInfo<'POST', {}, AddBookingsToMultiServiceBookingRequest$1, AddBookingsToMultiServiceBookingRequest, AddBookingsToMultiServiceBookingResponse$1, AddBookingsToMultiServiceBookingResponse>;
declare function removeBookingsFromMultiServiceBooking(): __PublicMethodMetaInfo<'POST', {}, RemoveBookingsFromMultiServiceBookingRequest$1, RemoveBookingsFromMultiServiceBookingRequest, RemoveBookingsFromMultiServiceBookingResponse$1, RemoveBookingsFromMultiServiceBookingResponse>;

export { type __PublicMethodMetaInfo, addBookingsToMultiServiceBooking, bulkConfirmOrDeclineBooking, bulkCreateBooking, bulkGetMultiServiceBookingAllowedActions, cancelBooking, cancelMultiServiceBooking, confirmBooking, confirmMultiServiceBooking, confirmOrDeclineBooking, createBooking, createMultiServiceBooking, declineBooking, declineMultiServiceBooking, getMultiServiceBooking, getMultiServiceBookingAvailability, markBookingAsPending, markMultiServiceBookingAsPending, removeBookingsFromMultiServiceBooking, rescheduleBooking, rescheduleMultiServiceBooking, setBookingSubmissionId, updateExtendedFields, updateNumberOfParticipants };
