"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// index.typings.ts
var index_typings_exports = {};
__export(index_typings_exports, {
  listPolicySnapshotsByBookingIds: () => listPolicySnapshotsByBookingIds2
});
module.exports = __toCommonJS(index_typings_exports);

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.ts
var import_transform_error = require("@wix/sdk-runtime/transform-error");
var import_rename_all_nested_keys = require("@wix/sdk-runtime/rename-all-nested-keys");

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      }
    ],
    _: [
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/_api/booking-policy-snapshots",
        destPath: ""
      },
      {
        srcPath: "/booking-policy-snapshots",
        destPath: ""
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policy-snapshots";
function listPolicySnapshotsByBookingIds(payload) {
  function __listPolicySnapshotsByBookingIds({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.policy_snapshots.v1.booking_policy_snapshot",
      method: "GET",
      methodFqn: "com.wixpress.bookings.policy.snapshots.v1.BookingPolicySnapshots.ListPolicySnapshotsByBookingIds",
      packageName: PACKAGE_NAME,
      url: resolveComWixpressBookingsPolicySnapshotsV1BookingPolicySnapshotsUrl(
        { protoPath: "/v1/policy-snapshots", data: payload, host }
      ),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicySnapshots.createdDate" },
            { path: "bookingPolicySnapshots.policy.createdDate" },
            { path: "bookingPolicySnapshots.policy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __listPolicySnapshotsByBookingIds;
}

// src/bookings-policy-snapshots-v1-booking-policy-snapshot-booking-policy-snapshots.universal.ts
async function listPolicySnapshotsByBookingIds2(bookingIds) {
  const { httpClient, sideEffects } = arguments[1];
  const payload = (0, import_rename_all_nested_keys.renameKeysFromSDKRequestToRESTRequest)({
    bookingIds
  });
  const reqOpts = listPolicySnapshotsByBookingIds(
    payload
  );
  sideEffects?.onSiteCall?.();
  try {
    const result = await httpClient.request(reqOpts);
    sideEffects?.onSuccess?.(result);
    return (0, import_rename_all_nested_keys.renameKeysFromRESTResponseToSDKResponse)(result.data);
  } catch (err) {
    const transformedError = (0, import_transform_error.transformError)(
      err,
      {
        spreadPathsToArguments: {},
        explicitPathsToArguments: { bookingIds: "$[0]" },
        singleArgumentUnchanged: false
      },
      ["bookingIds"]
    );
    sideEffects?.onError?.(err);
    throw transformedError;
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  listPolicySnapshotsByBookingIds
});
//# sourceMappingURL=index.typings.js.map