{"version": 3, "sources": ["../../src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.http.ts", "../../src/bookings-catalog-v1-service-options-and-variants-service-options-and-variants.meta.ts"], "sourcesContent": ["import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/v1/serviceOptionsAndVariants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/v1/serviceOptionsAndVariants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '/api',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '/api',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n      {\n        srcPath: '/_api/bookings/v1/service-options-and-variants',\n        destPath: '/api',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/bookings/v1/serviceOptionsAndVariants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/v1/service-options-and-variants',\n        destPath: '/v1/serviceOptionsAndVariants',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_service-options-and-variants';\n\n/**\n * Creates a `serviceOptionsAndVariants` object and for a service.\n *\n *\n * ## Calculate variants\n *\n * Before creating a `serviceOptionsAndVariants` object, you need to\n * anticipate and manually define all its variants, since Wix Bookings doesn't\n * automatically calculate them. For the actual\n * Create Service Options And Variants* call, specify both the `options` and\n * `variants` arrays.\n *\n * ## Limitations\n *\n * Wix Bookings allows you to connect only a single `serviceOptionsAndVariants`\n * object to a service. *Create Service Options And Variants* fails, if the\n * service already has a connected `serviceOptionsAndVariants` object.\n *\n * Currently, you can include only a single option per\n * `serviceOptionsAndVariants` object. Taken together, this means that services\n * are limited to a single option.\n *\n * ## Option ID\n *\n * When creating a`serviceOptionsAndVariants` object, you must specify an ID in\n * [UUID format](https://en.wikipedia.org/wiki/Universally_unique_identifier)\n * for its only option. You must reference this option ID for each variant as\n * `variants.values.choices.optionId`.\n *\n * ## Staff member option\n *\n * To creating an option based on the *staff member*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n * providing the service, you need to specify `STAFF_MEMBER` as `options.values.type`.\n * Also, specify all staff member IDs as `variants.values.choices.staffMemberId`.\n * You could follow this *sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-staff-member-based-service-variants) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-staff-member-based-service-variants)).\n *\n * ## Custom option\n *\n * To create an option based on a custom parameter, specify `CUSTOM` as\n * `options.values.type`. Provide descriptive names for all custom choices as\n * `variants.values.choices.custom`. These names are displayed to customers\n * during the book flow. You could follow this *sample flow*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment)).\n *\n * ## Duration option\n *\n * To create an option based on appointment duration, specify `DURATION` as\n * `options.values.type` and set a descriptive name in `options.values.durationData.name`.\n * Also, indicate the appointment length in `minutes` and provide a descriptive\n * `name` for each duration choice in `variants.values.choices.duration`.\n */\nexport function createServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __createServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CreateServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __createServiceOptionsAndVariants;\n}\n\n/**\n * Clones a `serviceOptionsAndVariants` object and connects it to a *service*\n * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n *\n *\n * The call fails if the service already has a connected\n * `serviceOptionsAndVariants` object.\n *\n * The cloned `serviceOptionsAndVariants` object gets a new, unique option ID.\n * The option ID of the existing `serviceOptionsAndVariants` object isn't reused.\n *\n * For example, you may call this method after *cloning a service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/clone-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/clone-service)).\n */\nexport function cloneServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __cloneServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CloneServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants/{cloneFromId}/clone',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __cloneServiceOptionsAndVariants;\n}\n\n/** Retrieves a `serviceOptionsAndVariants` object by its ID. */\nexport function getServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'GET' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath:\n          '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n            protoPath:\n              '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n            data: payload,\n            host,\n          }),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __getServiceOptionsAndVariants;\n}\n\n/**\n * Retrieves a `serviceOptionsAndVariants` object by *service ID*\n * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).\n */\nexport function getServiceOptionsAndVariantsByServiceId(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __getServiceOptionsAndVariantsByServiceId({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'GET' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariantsByServiceId',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants/service_id/{serviceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n\n      fallback: [\n        {\n          method: 'GET' as any,\n          url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n            protoPath: '/v1/serviceOptionsAndVariants/service_id/{serviceId}',\n            data: payload,\n            host,\n          }),\n          params: toURLSearchParams(payload),\n        },\n      ],\n    };\n\n    return metadata;\n  }\n\n  return __getServiceOptionsAndVariantsByServiceId;\n}\n\n/**\n * Updates a `serviceOptionsAndVariants` object.\n *\n *\n * Currently, only a single option is supported per `serviceOptionsAndVariants` object.\n *\n * If you want to update `variants`, you must pass the entire list of supported\n * variants, not only newly added variants.\n *\n * If you want to update `options`, you must pass the entire list of supported\n * options, not only newly added options.\n */\nexport function updateServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __updateServiceOptionsAndVariants({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'PATCH' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.UpdateServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath:\n          '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __updateServiceOptionsAndVariants;\n}\n\n/**\n * Deletes a `serviceOptionsAndVariants` object.\n *\n *\n * Because each service can be connected to only a single `serviceOptionsAndVariants`\n * object, the service doesn't support *varied pricing*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments#service-rates))\n * after deleting a `serviceOptionsAndVariants` object. Instead, Wix Bookings\n * uses its standard price calculation.\n */\nexport function deleteServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __deleteServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'DELETE' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.DeleteServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath:\n          '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteServiceOptionsAndVariants;\n}\n\n/**\n * Creates a query to retrieve a list of `serviceOptionsAndVariants` objects.\n *\n * The `queryServiceOptionsAndVariants()` function builds a query to retrieve a list of `serviceOptionsAndVariants` objects and returns a `ServiceOptionsAndVariantsQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-builder/find) function.\n *\n * You can refine the query by chaining `ServiceOptionsAndVariantsQueryBuilder` functions onto the query. `ServiceOptionsAndVariantsQueryBuilder` functions enable you to sort, filter, and control the results that `queryServiceOptionsAndVariants()` returns.\n *\n * `queryServiceOptionsAndVariants()` runs with the following `ServiceOptionsAndVariantsQueryBuilder` default that you can override:\n *\n * + `limit` is `50`.\n * + Sorted by `id` in ascending order.\n *\n * The functions that are chained to `queryServiceOptionsAndVariants()` are applied in the order they are called. For example, if you apply `ascending(\"options.values.type\")` and then `ascending(\"variants.values.price\")`, the results are sorted first by the `\"type\"`, and then, if there are multiple results with the same `\"type\"`, the items are sorted by `\"price\"`.\n *\n * The following `ServiceOptionsAndVariantsQueryBuilder` functions are supported for the `queryServiceOptionsAndVariants()` function. For a full description of the `serviceOptionsAndVariants` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-result/items) property in `ServiceOptionsAndVariantsQueryResult`.\n */\nexport function queryServiceOptionsAndVariants(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryServiceOptionsAndVariants({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.catalog.v1.service_options_and_variants',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.QueryServiceOptionsAndVariants',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsCatalogV1ServiceOptionsAndVariantsServiceUrl({\n        protoPath: '/v1/serviceOptionsAndVariants/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryServiceOptionsAndVariants;\n}\n", "import * as ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.http.js';\nimport * as ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.types.js';\nimport * as ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes from './bookings-catalog-v1-service-options-and-variants-service-options-and-variants.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function createServiceOptionsAndVariants(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.CreateServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.CreateServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.CreateServiceOptionsAndVariantsResponse,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.CreateServiceOptionsAndVariantsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.createServiceOptionsAndVariants(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/serviceOptionsAndVariants',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function cloneServiceOptionsAndVariants(): __PublicMethodMetaInfo<\n  'POST',\n  { cloneFromId: string },\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.CloneServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.CloneServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.CloneServiceOptionsAndVariantsResponse,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.CloneServiceOptionsAndVariantsResponse\n> {\n  const payload = { cloneFromId: ':cloneFromId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.cloneServiceOptionsAndVariants(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/serviceOptionsAndVariants/{cloneFromId}/clone',\n    pathParams: { cloneFromId: 'cloneFromId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getServiceOptionsAndVariants(): __PublicMethodMetaInfo<\n  'GET',\n  { serviceOptionsAndVariantsId: string },\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.GetServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.GetServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.GetServiceOptionsAndVariantsResponse,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.GetServiceOptionsAndVariantsResponse\n> {\n  const payload = {\n    serviceOptionsAndVariantsId: ':serviceOptionsAndVariantsId',\n  } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.getServiceOptionsAndVariants(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n    pathParams: { serviceOptionsAndVariantsId: 'serviceOptionsAndVariantsId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function getServiceOptionsAndVariantsByServiceId(): __PublicMethodMetaInfo<\n  'GET',\n  { serviceId: string },\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.GetServiceOptionsAndVariantsByServiceIdRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.GetServiceOptionsAndVariantsByServiceIdRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.GetServiceOptionsAndVariantsByServiceIdResponse,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.GetServiceOptionsAndVariantsByServiceIdResponse\n> {\n  const payload = { serviceId: ':serviceId' } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.getServiceOptionsAndVariantsByServiceId(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'GET',\n    path: '/v1/serviceOptionsAndVariants/service_id/{serviceId}',\n    pathParams: { serviceId: 'serviceId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function updateServiceOptionsAndVariants(): __PublicMethodMetaInfo<\n  'PATCH',\n  { serviceOptionsAndVariantsId: string },\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.UpdateServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.UpdateServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.UpdateServiceOptionsAndVariantsResponse,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.UpdateServiceOptionsAndVariantsResponse\n> {\n  const payload = {\n    serviceOptionsAndVariants: { id: ':serviceOptionsAndVariantsId' },\n  } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.updateServiceOptionsAndVariants(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'PATCH',\n    path: '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariants.id}',\n    pathParams: { serviceOptionsAndVariantsId: 'serviceOptionsAndVariantsId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function deleteServiceOptionsAndVariants(): __PublicMethodMetaInfo<\n  'DELETE',\n  { serviceOptionsAndVariantsId: string },\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.DeleteServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.DeleteServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.DeleteServiceOptionsAndVariantsResponse,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.DeleteServiceOptionsAndVariantsResponse\n> {\n  const payload = {\n    serviceOptionsAndVariantsId: ':serviceOptionsAndVariantsId',\n  } as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.deleteServiceOptionsAndVariants(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'DELETE',\n    path: '/v1/serviceOptionsAndVariants/{serviceOptionsAndVariantsId}',\n    pathParams: { serviceOptionsAndVariantsId: 'serviceOptionsAndVariantsId' },\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryServiceOptionsAndVariants(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.QueryServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.QueryServiceOptionsAndVariantsRequest,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsUniversalTypes.QueryServiceOptionsAndVariantsResponse,\n  ambassadorWixBookingsCatalogV1ServiceOptionsAndVariantsTypes.QueryServiceOptionsAndVariantsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsCatalogV1ServiceOptionsAndVariants.queryServiceOptionsAndVariants(\n      payload\n    );\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v1/serviceOptionsAndVariants/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAClC,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,+DACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAuDd,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgBO,SAAS,+BACd,SAC4B;AAC5B,WAAS,iCAAiC,EAAE,KAAK,GAAQ;AACvD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,6BACd,SAC4B;AAC5B,WAAS,+BAA+B,EAAE,KAAK,GAAQ;AACrD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK,+DAA+D;AAAA,YAClE,WACE;AAAA,YACF,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,UACD,QAAQ,kBAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,wCACd,SAC4B;AAC5B,WAAS,0CAA0C,EAAE,KAAK,GAAQ;AAChE,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MAEjC,UAAU;AAAA,QACR;AAAA,UACE,QAAQ;AAAA,UACR,KAAK,+DAA+D;AAAA,YAClE,WAAW;AAAA,YACX,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,UACD,QAAQ,kBAAkB,OAAO;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAcO,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAYO,SAAS,gCACd,SAC4B;AAC5B,WAAS,kCAAkC,EAAE,KAAK,GAAQ;AACxD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,+BACd,SAC4B;AAC5B,WAAS,iCAAiC,EAAE,KAAK,GAAQ;AACvD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+DAA+D;AAAA,QAClE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACxYO,SAASA,mCAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoD;AAAA,IACtD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kCAOd;AACA,QAAM,UAAU,EAAE,aAAa,eAAe;AAE9C,QAAM,oBACoD;AAAA,IACtD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,aAAa,cAAc;AAAA,IACzC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,gCAOd;AACA,QAAM,UAAU;AAAA,IACd,6BAA6B;AAAA,EAC/B;AAEA,QAAM,oBACoD;AAAA,IACtD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,6BAA6B,8BAA8B;AAAA,IACzE,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,2CAOd;AACA,QAAM,UAAU,EAAE,WAAW,aAAa;AAE1C,QAAM,oBACoD;AAAA,IACtD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,WAAW,YAAY;AAAA,IACrC,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,mCAOd;AACA,QAAM,UAAU;AAAA,IACd,2BAA2B,EAAE,IAAI,+BAA+B;AAAA,EAClE;AAEA,QAAM,oBACoD;AAAA,IACtD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,6BAA6B,8BAA8B;AAAA,IACzE,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,mCAOd;AACA,QAAM,UAAU;AAAA,IACd,6BAA6B;AAAA,EAC/B;AAEA,QAAM,oBACoD;AAAA,IACtD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,EAAE,6BAA6B,8BAA8B;AAAA,IACzE,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,kCAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACoD;AAAA,IACtD;AAAA,EACF;AAEF,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["createServiceOptionsAndVariants", "cloneServiceOptionsAndVariants", "getServiceOptionsAndVariants", "getServiceOptionsAndVariantsByServiceId", "updateServiceOptionsAndVariants", "deleteServiceOptionsAndVariants", "queryServiceOptionsAndVariants"]}