"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// meta.ts
var meta_exports = {};
__export(meta_exports, {
  countBookingPolicies: () => countBookingPolicies2,
  createBookingPolicy: () => createBookingPolicy2,
  deleteBookingPolicy: () => deleteBookingPolicy2,
  getBookingPolicy: () => getBookingPolicy2,
  getStrictestBookingPolicy: () => getStrictestBookingPolicy2,
  queryBookingPolicies: () => queryBookingPolicies2,
  setDefaultBookingPolicy: () => setDefaultBookingPolicy2,
  updateBookingPolicy: () => updateBookingPolicy2
});
module.exports = __toCommonJS(meta_exports);

// src/bookings-v1-booking-policy-booking-policies.http.ts
var import_rest_modules = require("@wix/sdk-runtime/rest-modules");
var import_timestamp = require("@wix/sdk-runtime/transformations/timestamp");
var import_timestamp2 = require("@wix/sdk-runtime/transformations/timestamp");
var import_field_mask = require("@wix/sdk-runtime/transformations/field-mask");
var import_transform_paths = require("@wix/sdk-runtime/transformations/transform-paths");
var import_rest_modules2 = require("@wix/sdk-runtime/rest-modules");
function resolveWixBookingsV1BookingPoliciesServiceUrl(opts) {
  const domainToMappings = {
    "manage._base_domain_": [
      {
        srcPath: "/_api/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    "www.wixapis.com": [
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ],
    _: [
      {
        srcPath: "/_api/bookings/v1/booking-policies/strictest",
        destPath: "/v1/booking-policies/strictest"
      },
      {
        srcPath: "/_api/bookings/v1/booking-policies/query",
        destPath: "/v1/booking-policies/query"
      },
      {
        srcPath: "/bookings/v1/booking-policies",
        destPath: "/v1/booking-policies"
      }
    ]
  };
  return (0, import_rest_modules2.resolveUrl)(Object.assign(opts, { domainToMappings }));
}
var PACKAGE_NAME = "@wix/auto_sdk_bookings_booking-policies";
function createBookingPolicy(payload) {
  function __createBookingPolicy({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CreateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __createBookingPolicy;
}
function getBookingPolicy(payload) {
  function __getBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "GET",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload),
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getBookingPolicy;
}
function getStrictestBookingPolicy(payload) {
  function __getStrictestBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.GetStrictestBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/strictest",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __getStrictestBookingPolicy;
}
function updateBookingPolicy(payload) {
  function __updateBookingPolicy({ host }) {
    const serializedData = (0, import_transform_paths.transformPaths)(payload, [
      {
        transformFn: import_field_mask.transformSDKFieldMaskToRESTFieldMask,
        paths: [{ path: "mask" }]
      },
      {
        transformFn: import_timestamp.transformSDKTimestampToRESTTimestamp,
        paths: [
          { path: "bookingPolicy.createdDate" },
          { path: "bookingPolicy.updatedDate" }
        ]
      }
    ]);
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "PATCH",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.UpdateBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicy.id}",
        data: serializedData,
        host
      }),
      data: serializedData,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicy.createdDate" },
            { path: "bookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __updateBookingPolicy;
}
function setDefaultBookingPolicy(payload) {
  function __setDefaultBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.SetDefaultBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}:setDefault",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "currentDefaultBookingPolicy.createdDate" },
            { path: "currentDefaultBookingPolicy.updatedDate" },
            { path: "previousDefaultBookingPolicy.createdDate" },
            { path: "previousDefaultBookingPolicy.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __setDefaultBookingPolicy;
}
function deleteBookingPolicy(payload) {
  function __deleteBookingPolicy({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "DELETE",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.DeleteBookingPolicy",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/{bookingPolicyId}",
        data: payload,
        host
      }),
      params: (0, import_rest_modules.toURLSearchParams)(payload)
    };
    return metadata;
  }
  return __deleteBookingPolicy;
}
function queryBookingPolicies(payload) {
  function __queryBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.QueryBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/query",
        data: payload,
        host
      }),
      data: payload,
      transformResponse: (payload2) => (0, import_transform_paths.transformPaths)(payload2, [
        {
          transformFn: import_timestamp2.transformRESTTimestampToSDKTimestamp,
          paths: [
            { path: "bookingPolicies.createdDate" },
            { path: "bookingPolicies.updatedDate" }
          ]
        }
      ])
    };
    return metadata;
  }
  return __queryBookingPolicies;
}
function countBookingPolicies(payload) {
  function __countBookingPolicies({ host }) {
    const metadata = {
      entityFqdn: "wix.bookings.v1.booking_policy",
      method: "POST",
      methodFqn: "wix.bookings.v1.BookingPoliciesService.CountBookingPolicies",
      packageName: PACKAGE_NAME,
      url: resolveWixBookingsV1BookingPoliciesServiceUrl({
        protoPath: "/v1/booking-policies/count",
        data: payload,
        host
      }),
      data: payload
    };
    return metadata;
  }
  return __countBookingPolicies;
}

// src/bookings-v1-booking-policy-booking-policies.meta.ts
function createBookingPolicy2() {
  const payload = {};
  const getRequestOptions = createBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getBookingPolicy2() {
  const payload = { bookingPolicyId: ":bookingPolicyId" };
  const getRequestOptions = getBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "GET",
    path: "/v1/booking-policies/{bookingPolicyId}",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function getStrictestBookingPolicy2() {
  const payload = {};
  const getRequestOptions = getStrictestBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/strictest",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function updateBookingPolicy2() {
  const payload = { bookingPolicy: { id: ":bookingPolicyId" } };
  const getRequestOptions = updateBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "PATCH",
    path: "/v1/booking-policies/{bookingPolicy.id}",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function setDefaultBookingPolicy2() {
  const payload = { bookingPolicyId: ":bookingPolicyId" };
  const getRequestOptions = setDefaultBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/{bookingPolicyId}:setDefault",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function deleteBookingPolicy2() {
  const payload = { bookingPolicyId: ":bookingPolicyId" };
  const getRequestOptions = deleteBookingPolicy(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "DELETE",
    path: "/v1/booking-policies/{bookingPolicyId}",
    pathParams: { bookingPolicyId: "bookingPolicyId" },
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function queryBookingPolicies2() {
  const payload = {};
  const getRequestOptions = queryBookingPolicies(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/query",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
function countBookingPolicies2() {
  const payload = {};
  const getRequestOptions = countBookingPolicies(payload);
  const getUrl = (context) => {
    const { url } = getRequestOptions(context);
    return url;
  };
  return {
    getUrl,
    httpMethod: "POST",
    path: "/v1/booking-policies/count",
    pathParams: {},
    __requestType: null,
    __originalRequestType: null,
    __responseType: null,
    __originalResponseType: null
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  countBookingPolicies,
  createBookingPolicy,
  deleteBookingPolicy,
  getBookingPolicy,
  getStrictestBookingPolicy,
  queryBookingPolicies,
  setDefaultBookingPolicy,
  updateBookingPolicy
});
//# sourceMappingURL=meta.js.map