{"version": 3, "sources": ["../../../src/bookings-services-v2-service-services.universal.ts", "../../../src/bookings-services-v2-service-services.http.ts"], "sourcesContent": ["import { transformError as sdkTransformError } from '@wix/sdk-runtime/transform-error';\nimport { queryBuilder } from '@wix/sdk-runtime/query-builder';\nimport {\n  renameKeysFromSDKRequestToRESTRequest,\n  renameK<PERSON>sFromRESTResponseToSDKResponse,\n} from '@wix/sdk-runtime/rename-all-nested-keys';\nimport {\n  HttpClient,\n  HttpResponse,\n  Search as SearchSdkType,\n  NonNullablePaths,\n  SearchSpec,\n} from '@wix/sdk-types';\nimport * as ambassadorWixBookingsServicesV2Service from './bookings-services-v2-service-services.http.js';\n// @ts-ignore\nimport { transformSDKAddressToRESTAddress } from '@wix/sdk-runtime/transformations/address';\nimport { transformRESTAddressToSDKAddress } from '@wix/sdk-runtime/transformations/address';\nimport { transformSDKImageToRESTImage } from '@wix/sdk-runtime/transformations/image';\nimport { transformRESTImageToSDKImage } from '@wix/sdk-runtime/transformations/image';\nimport { transformSDKPageURLV2ToRESTPageURLV2 } from '@wix/sdk-runtime/transformations/page-url-v2';\nimport { transformRESTPageURLV2ToSDKPageURLV2 } from '@wix/sdk-runtime/transformations/page-url-v2';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\n\n/** The `service` object represents an offering that a business provides to its customers. */\nexport interface Service {\n  /**\n   * Service ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Service type.\n   * Learn more about *service types*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)).\n   */\n  type?: ServiceTypeWithLiterals;\n  /**\n   * Order of the service within a *category*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object)).\n   */\n  sortOrder?: number | null;\n  /**\n   * Service name.\n   * @maxLength 400\n   * @minLength 1\n   */\n  name?: string | null;\n  /**\n   * Service description. For example, `High-class hair styling, cuts, straightening and color`.\n   * @maxLength 7000\n   */\n  description?: string | null;\n  /**\n   * Short service description, such as `Hair styling`.\n   * @maxLength 6000\n   */\n  tagLine?: string | null;\n  /**\n   * Default maximum number of customers that can book the service. The service cannot be booked beyond this capacity.\n   * @min 1\n   * @max 1000\n   */\n  defaultCapacity?: number | null;\n  /** Media associated with the service. */\n  media?: Media;\n  /** Whether the service is hidden from Wix Bookings pages and widgets. */\n  hidden?: boolean | null;\n  /**\n   * _Category_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object))\n   * the service is associated with.\n   */\n  category?: V2Category;\n  /** Form the customer filled out when booking the service. */\n  form?: Form;\n  /**\n   * Payment options for booking the service.\n   * Learn more about *service payments*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)).\n   */\n  payment?: Payment;\n  /** Online booking settings. */\n  onlineBooking?: OnlineBooking;\n  /** Conferencing options for the service. */\n  conferencing?: Conferencing;\n  /**\n   * The locations this service is offered at. Read more about *service locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations)).\n   * @immutable\n   * @maxSize 500\n   */\n  locations?: Location[];\n  /**\n   * _Policy_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction))\n   * determining under what conditions this service can be booked. For example, whether the service can only be booked up to 30 minutes before it begins.\n   */\n  bookingPolicy?: BookingPolicy;\n  /**\n   * The service's *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),\n   * which can be used to manage the service's *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).\n   */\n  schedule?: Schedule;\n  /**\n   * IDs of the *staff members*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n   * providing the service. Available only for appointment-based services.\n   * @maxSize 220\n   * @format GUID\n   */\n  staffMemberIds?: string[];\n  /**\n   * Information about which resources must be available so customers can book the service.\n   * For example, a meeting room or equipment.\n   * @maxSize 3\n   */\n  serviceResources?: ServiceResource[];\n  /**\n   * A slug is the last part of the URL address that serves as a unique identifier of the service.\n   * The list of supported slugs includes past service names for backwards compatibility, and a custom slug if one was set by the business owner.\n   * @readonly\n   * @maxSize 100\n   */\n  supportedSlugs?: Slug[];\n  /**\n   * Active slug for the service.\n   * Learn more about *service slugs*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-slugs) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-slugs)).\n   * @readonly\n   */\n  mainSlug?: Slug;\n  /**\n   * URLs to various service-related pages, such as the calendar page and the booking page.\n   * @readonly\n   */\n  urls?: URLs;\n  /** Extensions enabling users to save custom data related to the service. */\n  extendedFields?: ExtendedFields;\n  /** Custom SEO data for the service. */\n  seoData?: SeoSchema;\n  /**\n   * Date and time the service was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the service was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Revision number, which increments by 1 each time the service is updated. To\n   * prevent conflicting changes, the existing revision must be used when updating\n   * a service.\n   * @readonly\n   */\n  revision?: string | null;\n}\n\nexport enum ServiceType {\n  /** Appointment-based service. */\n  APPOINTMENT = 'APPOINTMENT',\n  /** Class service. */\n  CLASS = 'CLASS',\n  /** Course service. */\n  COURSE = 'COURSE',\n}\n\n/** @enumType */\nexport type ServiceTypeWithLiterals =\n  | ServiceType\n  | 'APPOINTMENT'\n  | 'CLASS'\n  | 'COURSE';\n\nexport interface Media {\n  /**\n   * Media items associated with the service.\n   * @maxSize 100\n   */\n  items?: MediaItem[];\n  /** Primary media associated with the service. */\n  mainMedia?: MediaItem;\n  /** Cover media associated with the service. */\n  coverMedia?: MediaItem;\n}\n\nexport interface MediaItem extends MediaItemItemOneOf {\n  /** Details of the image associated with the service, such as URL and size. */\n  image?: string;\n}\n\n/** @oneof */\nexport interface MediaItemItemOneOf {\n  /** Details of the image associated with the service, such as URL and size. */\n  image?: string;\n}\n\nexport interface V2Category {\n  /**\n   * Category ID.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Category name.\n   * @maxLength 500\n   * @readonly\n   */\n  name?: string | null;\n  /**\n   * Order of a category within a category list.\n   * @readonly\n   */\n  sortOrder?: number | null;\n}\n\nexport interface Form {\n  /**\n   * ID of the form associated with the service.\n   * The form information that you submit when booking includes contact details, participants, and other form fields set up for the service.\n   * You can manage the service booking form fields using the Bookings Forms API.\n   * @format GUID\n   */\n  _id?: string;\n}\n\nexport interface FormSettings {\n  /** Whether the service booking form should be hidden from the site. */\n  hidden?: boolean | null;\n}\n\nexport interface Payment extends PaymentRateOneOf {\n  /**\n   * The details for the fixed price of the service.\n   *\n   * Required when: `rateType` is `FIXED`\n   */\n  fixed?: FixedPayment;\n  /**\n   * The details for the custom price of the service.\n   *\n   * Required when: `rateType` is `CUSTOM`\n   */\n  custom?: CustomPayment;\n  /**\n   * The details for the varied pricing of the service.\n   * Read more about [varied price options](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online#offering-varied-price-options).\n   *\n   * Required when: `rateType` is `VARIED`\n   */\n  varied?: VariedPayment;\n  /** The rate the customer is expected to pay for the service. */\n  rateType?: RateTypeWithLiterals;\n  /** The payment options a customer can use to pay for the service. */\n  options?: PaymentOptions;\n  /**\n   * IDs of pricing plans that can be used as payment for the service.\n   * @readonly\n   * @maxSize 75\n   * @format GUID\n   */\n  pricingPlanIds?: string[];\n}\n\n/** @oneof */\nexport interface PaymentRateOneOf {\n  /**\n   * The details for the fixed price of the service.\n   *\n   * Required when: `rateType` is `FIXED`\n   */\n  fixed?: FixedPayment;\n  /**\n   * The details for the custom price of the service.\n   *\n   * Required when: `rateType` is `CUSTOM`\n   */\n  custom?: CustomPayment;\n  /**\n   * The details for the varied pricing of the service.\n   * Read more about [varied price options](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online#offering-varied-price-options).\n   *\n   * Required when: `rateType` is `VARIED`\n   */\n  varied?: VariedPayment;\n}\n\nexport enum RateType {\n  /** Unknown rate type. */\n  UNKNOWN_RATE_TYPE = 'UNKNOWN_RATE_TYPE',\n  /** The service has a fixed price. */\n  FIXED = 'FIXED',\n  /** The service has a custom price, expressed as a price description. */\n  CUSTOM = 'CUSTOM',\n  /** This service is offered with a set of different prices based on different terms. */\n  VARIED = 'VARIED',\n  /** This service is offered free of charge. */\n  NO_FEE = 'NO_FEE',\n}\n\n/** @enumType */\nexport type RateTypeWithLiterals =\n  | RateType\n  | 'UNKNOWN_RATE_TYPE'\n  | 'FIXED'\n  | 'CUSTOM'\n  | 'VARIED'\n  | 'NO_FEE';\n\nexport interface FixedPayment {\n  /**\n   * The fixed price required to book the service.\n   *\n   * Required when: `rateType` is `FIXED`\n   */\n  price?: Money;\n  /**\n   * The deposit price required to book the service.\n   *\n   * Required when: `rateType` is `FIXED` and `paymentOptions.deposit` is `true`\n   */\n  deposit?: Money;\n}\n\n/**\n * Money.\n * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.\n */\nexport interface Money {\n  /**\n   * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.\n   * @format DECIMAL_VALUE\n   * @decimalValue options { gt:0, maxScale:2 }\n   */\n  value?: string;\n  /**\n   * Currency code. Must be valid ISO 4217 currency code (e.g., USD).\n   * @format CURRENCY\n   * @readonly\n   */\n  currency?: string;\n  /**\n   * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.\n   * @maxLength 50\n   */\n  formattedValue?: string | null;\n}\n\nexport interface CustomPayment {\n  /**\n   * A custom description explaining to the customer how to pay for the service.\n   * @maxLength 50\n   */\n  description?: string | null;\n}\n\nexport interface VariedPayment {\n  /** The default price for the service without any variants. It will also be used as the default price for any new variant. */\n  defaultPrice?: Money;\n  /**\n   * The deposit price required to book the service.\n   *\n   * Required when: `rateType` is `VARIED` and `paymentOptions.deposit` is `true`\n   */\n  deposit?: Money;\n  /**\n   * The minimal price a customer may pay for this service, based on its variants.\n   * @readonly\n   */\n  minPrice?: Money;\n  /**\n   * The maximum price a customer may pay for this service, based on its variants.\n   * @readonly\n   */\n  maxPrice?: Money;\n}\n\nexport interface PaymentOptions {\n  /**\n   * Customers can pay for the service online.\n   * When `true`:\n   * + `rateType` must be either `FIXED` or `VARIED`.\n   * + `fixed.price` or `varied.default_price` must be specified respectively. Read more about [getting paid online](https://support.wix.com/en/article/wix-bookings-about-getting-paid-online).\n   */\n  online?: boolean | null;\n  /** Customers can pay for the service in person. */\n  inPerson?: boolean | null;\n  /**\n   * This service requires a deposit to be made online in order to book it.\n   * When `true`:\n   * + `rateType` must be `VARIED` or `FIXED`.\n   * + A `deposit` must be specified.\n   */\n  deposit?: boolean | null;\n  /** Customers can pay for the service using a pricing plan. */\n  pricingPlan?: boolean | null;\n}\n\nexport interface OnlineBooking {\n  /**\n   * Whether the service can be booked online.\n   * When set to `true`, customers can book the service online. Configure the payment options via the `service.payment` property.\n   * When set to `false`, customers cannot book the service online, and the service can only be paid for in person.\n   */\n  enabled?: boolean | null;\n  /** Booking the service requires approval by the Wix user. */\n  requireManualApproval?: boolean | null;\n  /** Multiple customers can request to book the same time slot. This is relevant when `requireManualApproval` is `true`. */\n  allowMultipleRequests?: boolean | null;\n}\n\nexport interface Conferencing {\n  /** Whether a conference link is generated for the service's sessions. */\n  enabled?: boolean | null;\n}\n\nexport interface Location extends LocationOptionsOneOf {\n  /** Information about business locations. */\n  business?: BusinessLocationOptions;\n  /** Information about custom locations. */\n  custom?: CustomLocationOptions;\n  /**\n   * Location ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string;\n  /**\n   * Location type.\n   *\n   * Default: `CUSTOM`\n   */\n  type?: LocationTypeWithLiterals;\n  /**\n   * Location address. Empty for `{\"type\": \"CUSTOMER\"}`.\n   * @readonly\n   */\n  calculatedAddress?: CommonAddress;\n}\n\n/** @oneof */\nexport interface LocationOptionsOneOf {\n  /** Information about business locations. */\n  business?: BusinessLocationOptions;\n  /** Information about custom locations. */\n  custom?: CustomLocationOptions;\n}\n\nexport enum LocationType {\n  UNKNOWN_LOCATION_TYPE = 'UNKNOWN_LOCATION_TYPE',\n  /**\n   * Location set by the business that is not a standard business *location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   */\n  CUSTOM = 'CUSTOM',\n  /**\n   * Business *location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   */\n  BUSINESS = 'BUSINESS',\n  /**\n   * The customer specifies any address when booking. Available only for\n   * appointment-based services.\n   */\n  CUSTOMER = 'CUSTOMER',\n}\n\n/** @enumType */\nexport type LocationTypeWithLiterals =\n  | LocationType\n  | 'UNKNOWN_LOCATION_TYPE'\n  | 'CUSTOM'\n  | 'BUSINESS'\n  | 'CUSTOMER';\n\nexport interface CommonAddress extends CommonAddressStreetOneOf {\n  /** Street name and number. */\n  streetAddress?: StreetAddress;\n  /** @maxLength 255 */\n  addressLine1?: string | null;\n  /**\n   * 2-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format.\n   * @format COUNTRY\n   */\n  country?: string | null;\n  /**\n   * Code for a subdivision (such as state, prefecture, or province) in [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) format.\n   * @maxLength 255\n   */\n  subdivision?: string | null;\n  /**\n   * City name.\n   * @maxLength 255\n   */\n  city?: string | null;\n  /**\n   * Postal or zip code.\n   * @maxLength 255\n   */\n  postalCode?: string | null;\n  /**\n   * Full address of the location.\n   * @maxLength 512\n   */\n  formatted?: string | null;\n}\n\n/** @oneof */\nexport interface CommonAddressStreetOneOf {\n  /** Street name and number. */\n  streetAddress?: StreetAddress;\n  /** @maxLength 255 */\n  addressLine?: string | null;\n}\n\n/** Street address. Includes street name, number, and apartment number in separate fields. */\nexport interface StreetAddress {\n  /**\n   * Street number.\n   * @maxLength 255\n   */\n  number?: string;\n  /**\n   * Street name.\n   * @maxLength 255\n   */\n  name?: string;\n  /**\n   * Apartment number.\n   * @maxLength 255\n   */\n  apt?: string;\n}\n\nexport interface AddressLocation {\n  /** Address latitude. */\n  latitude?: number | null;\n  /** Address longitude. */\n  longitude?: number | null;\n}\n\nexport interface BusinessLocationOptions {\n  /**\n   * ID of the business *location*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)).\n   * When setting a business location, specify only the location ID. Other location details are overwritten.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Business location name.\n   * @readonly\n   * @maxLength 150\n   */\n  name?: string;\n  /**\n   * Whether this is the default location. There can only be a single default location per site.\n   * @readonly\n   */\n  default?: boolean | null;\n  /**\n   * Business location address.\n   * @readonly\n   */\n  address?: CommonAddress;\n  /**\n   * Business location email.\n   * @format EMAIL\n   * @readonly\n   */\n  email?: string | null;\n  /**\n   * Business location phone.\n   * @format PHONE\n   * @readonly\n   */\n  phone?: string | null;\n}\n\nexport interface CustomLocationOptions {\n  /**\n   * ID of the custom location.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string;\n  /** Address of the custom location. */\n  address?: CommonAddress;\n}\n\n/**\n * `BookingPolicy` is the main entity of `BookingPolicyService` and specifies a set of rules for booking a service\n * by visitors and members.\n *\n * Each `BookingPolicy` consists of a number of sub-policies. When the Bookings App is provisioned to a meta site then a\n * default `BookingPolicy` will be created with defaults for each of these sub-policies. This also applies when a request\n * is received to create a new `BookingPolicy` and one or more of these sub-policies are not provided.\n *\n * Sub-policies are defined in separate objects as specified below.\n *\n * - The `LimitEarlyBookingPolicy` object defines the policy for limiting early bookings.\n * - The `LimitLateBookingPolicy` object defines the policy for limiting late bookings.\n * - The `BookAfterStartPolicy` object defines the policy for booking after the start of the schedule.\n * - The `CancellationPolicy` object defines the policy for canceling a booked entity.\n * - The `ReschedulePolicy` object defines the policy for rescheduling booked entity.\n * - The `WaitlistPolicy` object defines the policy for a waitlist.\n * - The `ParticipantsPolicy` object defines the policy regarding the participants per booking.\n * - The `ResourcesPolicy` object defines the policy regarding the resources per booking.\n * - The `CancellationFeePolicy` object defines the policy regarding cancellation fees.\n * - The `SaveCreditCardPolicy` object defines the policy for saving credit card details.\n *\n * By default each sub-policy is disabled. A more detailed specification of the default settings of each sub-policy\n * can be found in the description of the corresponding object.\n *\n * Partial updates are supported on the main entity level, however in order to update a sub-policy the client needs to provide the whole sub-policy object.\n */\nexport interface BookingPolicy {\n  /**\n   * The ID to the policy for the booking.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Date and time the policy was created.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the policy was updated.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Name of the policy.\n   * @maxLength 400\n   * @readonly\n   */\n  name?: string | null;\n  /**\n   * Custom description for the policy. This policy is displayed to the participant.\n   * @readonly\n   */\n  customPolicyDescription?: PolicyDescription;\n  /**\n   * Whether the policy is the default for the meta site.\n   * @readonly\n   */\n  default?: boolean | null;\n  /**\n   * Policy for limiting early bookings.\n   * @readonly\n   */\n  limitEarlyBookingPolicy?: LimitEarlyBookingPolicy;\n  /**\n   * Policy for limiting late bookings.\n   * @readonly\n   */\n  limitLateBookingPolicy?: LimitLateBookingPolicy;\n  /**\n   * Policy on booking an entity after the start of the schedule.\n   * @readonly\n   */\n  bookAfterStartPolicy?: BookAfterStartPolicy;\n  /**\n   * Policy for canceling a booked entity.\n   * @readonly\n   */\n  cancellationPolicy?: CancellationPolicy;\n  /**\n   * Policy for rescheduling a booked entity.\n   * @readonly\n   */\n  reschedulePolicy?: ReschedulePolicy;\n  /**\n   * Waitlist policy for the service.\n   * @readonly\n   */\n  waitlistPolicy?: WaitlistPolicy;\n  /**\n   * Policy regarding the participants per booking.\n   * @readonly\n   */\n  participantsPolicy?: ParticipantsPolicy;\n  /**\n   * Policy for allocating resources.\n   * @readonly\n   */\n  resourcesPolicy?: ResourcesPolicy;\n  /**\n   * Rules for cancellation fees.\n   * @readonly\n   */\n  cancellationFeePolicy?: CancellationFeePolicy;\n  /**\n   * Rule for saving credit card.\n   * @readonly\n   */\n  saveCreditCardPolicy?: SaveCreditCardPolicy;\n}\n\n/** A description of the policy to display to participants. */\nexport interface PolicyDescription {\n  /**\n   * Whether the description should be displayed. If `true`, the description is displayed.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * The description to display.\n   *\n   * Default: Empty\n   * Max length: 2500 characters\n   * @maxLength 2500\n   */\n  description?: string;\n}\n\n/** The policy for limiting early bookings. */\nexport interface LimitEarlyBookingPolicy {\n  /**\n   * Whether there is a limit on how early a customer\n   * can book. When `false`, there is no limit on the earliest\n   * booking time and customers can book in advance, as early as they like.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Maximum number of minutes before the start of the session that a booking can be made. This value must be greater\n   * than `latest_booking_in_minutes` in the `LimitLateBookingPolicy` policy.\n   *\n   * Default: 10080 minutes (7 days)\n   * Min: 1 minute\n   * @min 1\n   */\n  earliestBookingInMinutes?: number;\n}\n\n/**\n * The policy for limiting late bookings.\n *\n * This policy and the `BookAfterStartPolicy` policy cannot be enabled at the same time. So if this policy\n * is enabled, `BookAfterStartPolicy` must be disabled.\n */\nexport interface LimitLateBookingPolicy {\n  /**\n   * Whether there is a limit on how late a customer\n   * can book. When `false`, there is no limit on the latest\n   * booking time and customers can book up to the last minute.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Minimum number of minutes before the start of the session that a booking can be made.\n   * For a schedule, this is relative to the start time of the next booked session, excluding past-booked sessions.\n   * This value must be less than `earliest_booking_in_minutes` in the `LimitEarlyBookingPolicy` policy.\n   *\n   * Default: 1440 minutes (1 day)\n   * Min: 1 minute\n   * @min 1\n   */\n  latestBookingInMinutes?: number;\n}\n\n/**\n * The policy for whether a session can be booked after the start of the schedule.\n * This policy and `LimitLateBookingPolicy` cannot be enabled at the same time. So if this policy\n * is enabled, the `LimitLateBookingPolicy` policy must be disabled.\n */\nexport interface BookAfterStartPolicy {\n  /**\n   * Whether booking is allowed after the start of the schedule. When `true`,\n   * customers can book after the start of the schedule.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n}\n\n/** The policy for canceling a booked session. */\nexport interface CancellationPolicy {\n  /**\n   * Whether canceling a booking is allowed. When `true`, customers\n   * can cancel the booking.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Whether there is a limit on the latest cancellation time. When `true`,\n   * a time limit is enforced.\n   *\n   * Default: `false`\n   */\n  limitLatestCancellation?: boolean;\n  /**\n   * Minimum number of minutes before the start of the booked session that the booking can be canceled.\n   *\n   * Default: 1440 minutes (1 day)\n   * Min: 1 minute\n   * @min 1\n   */\n  latestCancellationInMinutes?: number;\n}\n\n/** The policy for rescheduling a booked session. */\nexport interface ReschedulePolicy {\n  /**\n   * Whether rescheduling a booking is allowed. When `true`, customers\n   * can reschedule the booking.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Whether there is a limit on the latest reschedule time. When `true`,\n   * a time limit is enforced.\n   *\n   * Default: `false`\n   */\n  limitLatestReschedule?: boolean;\n  /**\n   * Minimum number of minutes before the start of the booked session that the booking can be rescheduled.\n   *\n   * Default: 1440 minutes (1 day)\n   * Min: 1 minute\n   * @min 1\n   */\n  latestRescheduleInMinutes?: number;\n}\n\n/** The policy for the waitlist. */\nexport interface WaitlistPolicy {\n  /**\n   * Whether the session has a waitlist. If `true`, there is a waitlist.\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Number of spots available in the waitlist.\n   *\n   * Default: 10 spots\n   * Min: 1 spot\n   * @min 1\n   */\n  capacity?: number;\n  /**\n   * Amount of time a participant is given to book, once notified that a spot is available.\n   *\n   * Default: 10 minutes\n   * Min: 1 spot\n   * @min 1\n   */\n  reservationTimeInMinutes?: number;\n}\n\n/** The policy for the maximum number of participants per booking. */\nexport interface ParticipantsPolicy {\n  /**\n   * Maximum number of participants allowed.\n   *\n   * Default: 1 participant\n   * Min: 1 participant\n   * @min 1\n   */\n  maxParticipantsPerBooking?: number;\n}\n\n/** The policy regarding the allocation of resources (e.g. staff members). */\nexport interface ResourcesPolicy {\n  /**\n   * `true` if this policy is enabled, `false` otherwise.\n   * When `false` then the client must always select a resource when booking an appointment.\n   */\n  enabled?: boolean;\n  /**\n   * `true`, if it is allowed to automatically assign a resource when booking an appointment,\n   * `false`, if the client must always select a resource.\n   *\n   * Default: `false`\n   */\n  autoAssignAllowed?: boolean;\n}\n\nexport interface CancellationFeePolicy {\n  /**\n   * Whether canceling a booking will result in a cancellation fee\n   *\n   * Default: `false`\n   */\n  enabled?: boolean;\n  /**\n   * Cancellation windows describing the time of cancellation and the fee to charge.\n   * @maxSize 2\n   */\n  cancellationWindows?: CancellationWindow[];\n  /**\n   * Whether the cancellation fee should not be automatically collected when customer cancels the booking.\n   *\n   * Default: `true`\n   */\n  autoCollectFeeEnabled?: boolean | null;\n}\n\nexport interface CancellationWindow extends CancellationWindowFeeOneOf {\n  /** Amount to be charged as a cancellation fee. */\n  amount?: Money;\n  /**\n   * Percentage of the original price to be charged as a cancellation fee.\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentage?: string;\n  /**\n   * The fee will be applied if the booked session starts within this start time in minutes.\n   * @min 1\n   */\n  startInMinutes?: number | null;\n}\n\n/** @oneof */\nexport interface CancellationWindowFeeOneOf {\n  /** Amount to be charged as a cancellation fee. */\n  amount?: Money;\n  /**\n   * Percentage of the original price to be charged as a cancellation fee.\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentage?: string;\n}\n\nexport interface SaveCreditCardPolicy {\n  /** Default: `false` */\n  enabled?: boolean;\n}\n\nexport interface Schedule {\n  /**\n   * ID of the *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n   * to which the service's events belong.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Start time of the first session in the schedule. For courses only.\n   * @readonly\n   */\n  firstSessionStart?: Date | null;\n  /**\n   * End time of the last session in the schedule. For courses only.\n   * @readonly\n   */\n  lastSessionEnd?: Date | null;\n  /** Limitations affecting the service availability. */\n  availabilityConstraints?: AvailabilityConstraints;\n}\n\nexport interface AvailabilityConstraints {\n  /**\n   * Calculated list of all supported session durations for the service. For\n   * appointment-based services without varied pricing based on session length, it\n   * matches the single value in the `sessionDurations` array. For appointment-based\n   * services with varied pricing based on session length, it includes session\n   * durations for all *variants*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),\n   * while ignoring `sessionDurations`.\n   * For courses and classes, it includes durations for all future\n   * recurring sessions but excludes durations for one-off or past recurring sessions.\n   * @readonly\n   * @maxSize 50\n   */\n  durations?: Duration[];\n  /**\n   * List of supported session durations in minutes.\n   *\n   * - For appointment-based services, specify `sessionDurations` when creating a service.\n   * - For appointment-based services with varied pricing by session length, you must still specify `sessionDurations`, but the values are ignored. Actual durations are taken from the service variants ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   * - For classes or courses, don't specify `sessionDurations` when creating a service.\n   *\n   * Min: `1` minute\n   * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)\n   * @min 1\n   * @max 44639\n   * @maxSize 50\n   */\n  sessionDurations?: number[];\n  /**\n   * The number of minutes between the end of a session and the start of the next.\n   *\n   *\n   * Min: `0` minutes\n   * Max: `720` minutes\n   * @max 720\n   */\n  timeBetweenSessions?: number;\n}\n\nexport interface Duration {\n  /**\n   * The duration of the service in minutes.\n   *\n   * Min: `1` minute\n   * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)\n   * @min 1\n   * @max 44639\n   */\n  minutes?: number;\n}\n\nexport interface StaffMember {\n  /**\n   * ID of the staff member providing the service, can be used to retrieve resource information using wix-bookings-backend resources API.\n   * @format GUID\n   * @readonly\n   */\n  staffMemberId?: string;\n  /**\n   * Name of the staff member\n   * @maxLength 40\n   * @readonly\n   */\n  name?: string | null;\n  /**\n   * Main media associated with the service.\n   * @readonly\n   */\n  mainMedia?: StaffMediaItem;\n}\n\nexport interface StaffMediaItem extends StaffMediaItemItemOneOf {\n  /** Details of the image associated with the staff, such as URL and size. */\n  image?: string;\n}\n\n/** @oneof */\nexport interface StaffMediaItemItemOneOf {\n  /** Details of the image associated with the staff, such as URL and size. */\n  image?: string;\n}\n\nexport interface StaffMemberDetails {\n  /**\n   * Staff members providing the service. For appointments only.\n   * @maxSize 220\n   */\n  staffMembers?: StaffMember[];\n}\n\nexport interface ResourceGroup {\n  /**\n   * An optional resource group ID. If specified, it references a resource group in the resource groups API.\n   * TODO - referenced_entity annotation\n   * @format GUID\n   */\n  resourceGroupId?: string | null;\n  /**\n   * Resource IDs. Each ID references a resource in the resources API and may be a subset of resources within a resource group.\n   * TODO - referenced_entity annotation\n   */\n  resourceIds?: ResourceIds;\n  /**\n   * Specifies how many resources in the group / resource IDs are required to book the service.\n   * Defaults to 1.\n   * @min 1\n   */\n  requiredResourcesNumber?: number | null;\n  /**\n   * If set to `true`, the customer can select the specific resources while booking the service.\n   * If set to `false`, the resources required to book the service will be auto-selected at the time of the booking.\n   * Defaults to false.\n   * @readonly\n   */\n  selectableResource?: boolean | null;\n}\n\nexport interface ResourceIds {\n  /**\n   * Values of the resource IDs.\n   * @maxSize 100\n   * @format GUID\n   */\n  values?: string[];\n}\n\nexport interface ServiceResource extends ServiceResourceSelectionOneOf {\n  /**\n   * Details about the required *resource type*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/resource-types/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/resources/resource-types-v2/introduction)).\n   */\n  resourceType?: ResourceType;\n}\n\n/** @oneof */\nexport interface ServiceResourceSelectionOneOf {}\n\nexport interface ResourceType {\n  /**\n   * The type of the resource.\n   * @format GUID\n   */\n  _id?: string | null;\n  /**\n   * The name of the resource type.\n   * @readonly\n   * @maxLength 40\n   * @minLength 1\n   */\n  name?: string | null;\n}\n\nexport interface Slug {\n  /**\n   * The unique part of service's URL that identifies the service's information page. For example, `service-1` in `https:/example.com/services/service-1`.\n   * @maxLength 500\n   */\n  name?: string;\n  /**\n   * Whether the slug was generated or customized. If `true`, the slug was customized manually by the business owner. Otherwise, the slug was automatically generated from the service name.\n   * @readonly\n   */\n  custom?: boolean | null;\n  /**\n   * Date and time the slug was created. This is a system field.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n}\n\nexport interface URLs {\n  /**\n   * The URL for the service page.\n   * @readonly\n   */\n  servicePage?: string;\n  /**\n   * The URL for the booking entry point. It can be either to the calendar or to the service page.\n   * @readonly\n   */\n  bookingPage?: string;\n  /**\n   * The URL for the calendar. Can be empty if no calendar exists.\n   * @readonly\n   */\n  calendarPage?: string;\n}\n\nexport interface ExtendedFields {\n  /**\n   * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.\n   * The value of each key is structured according to the schema defined when the extended fields were configured.\n   *\n   * You can only access fields for which you have the appropriate permissions.\n   *\n   * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).\n   */\n  namespaces?: Record<string, Record<string, any>>;\n}\n\n/**\n * The SEO schema object contains data about different types of meta tags. It makes sure that the information about your page is presented properly to search engines.\n * The search engines use this information for ranking purposes, or to display snippets in the search results.\n * This data will override other sources of tags (for example patterns) and will be included in the <head> section of the HTML document, while not being displayed on the page itself.\n */\nexport interface SeoSchema {\n  /** SEO tag information. */\n  tags?: Tag[];\n  /** SEO general settings. */\n  settings?: Settings;\n}\n\nexport interface Keyword {\n  /** Keyword value. */\n  term?: string;\n  /** Whether the keyword is the main focus keyword. */\n  isMain?: boolean;\n  /**\n   * The source that added the keyword terms to the SEO settings.\n   * @maxLength 1000\n   */\n  origin?: string | null;\n}\n\nexport interface Tag {\n  /**\n   * SEO tag type.\n   *\n   *\n   * Supported values: `title`, `meta`, `script`, `link`.\n   */\n  type?: string;\n  /**\n   * A `{\"key\": \"value\"}` pair object where each SEO tag property (`\"name\"`, `\"content\"`, `\"rel\"`, `\"href\"`) contains a value.\n   * For example: `{\"name\": \"description\", \"content\": \"the description itself\"}`.\n   */\n  props?: Record<string, any> | null;\n  /** SEO tag metadata. For example, `{\"height\": 300, \"width\": 240}`. */\n  meta?: Record<string, any> | null;\n  /** SEO tag inner content. For example, `<title> inner content </title>`. */\n  children?: string;\n  /** Whether the tag is a [custom tag](https://support.wix.com/en/article/adding-additional-meta-tags-to-your-sites-pages). */\n  custom?: boolean;\n  /** Whether the tag is disabled. If the tag is disabled, people can't find your page when searching for this phrase in search engines. */\n  disabled?: boolean;\n}\n\nexport interface Settings {\n  /**\n   * Whether the [automatical redirect visits](https://support.wix.com/en/article/customizing-your-pages-seo-settings-in-the-seo-panel) from the old URL to the new one is enabled.\n   *\n   *\n   * Default: `false` (automatical redirect is enabled).\n   */\n  preventAutoRedirect?: boolean;\n  /**\n   * User-selected keyword terms for a specific page.\n   * @maxSize 5\n   */\n  keywords?: Keyword[];\n}\n\nexport interface AddOnGroup {\n  /**\n   * ID of the group.\n   * @readonly\n   * @format GUID\n   */\n  _id?: string | null;\n  /**\n   * The name of the group.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /**\n   * The maximum number of AddOns that can be selected from this group.\n   * If not set, there is no upper limit.\n   */\n  maxNumberOfAddOns?: number | null;\n  /**\n   * List of AddOn IDs that are part of this group.\n   * @format GUID\n   * @maxSize 7\n   */\n  addOnIds?: string[] | null;\n  /**\n   * The group prompt.\n   * @maxLength 200\n   */\n  prompt?: string | null;\n}\n\nexport interface AddOnDetails {\n  /**\n   * ID of the AddOn.\n   * @format GUID\n   */\n  addOnId?: string | null;\n  /**\n   * The duration of the AddOn in minutes.\n   * This field can be empty if the AddOn has no specific duration.\n   */\n  durationInMinutes?: number | null;\n}\n\n/**\n * Message for reindexing search data to a given search schema. Support both upsert and delete flows as well as\n * performs context manipulation with adding tenant, provided in message to callscope.\n */\nexport interface ReindexMessage extends ReindexMessageActionOneOf {\n  upsert?: Upsert;\n  delete?: Delete;\n  entityFqdn?: string;\n  tenantId?: string;\n  eventTime?: Date | null;\n  entityEventSequence?: string | null;\n  schema?: Schema;\n}\n\n/** @oneof */\nexport interface ReindexMessageActionOneOf {\n  upsert?: Upsert;\n  delete?: Delete;\n}\n\nexport interface Upsert {\n  entityId?: string;\n  entityAsJson?: string;\n}\n\nexport interface Delete {\n  entityId?: string;\n}\n\nexport interface Schema {\n  label?: string;\n  clusterName?: string;\n}\n\nexport interface SetCustomSlugEvent {\n  /** The main slug for the service after the update */\n  mainSlug?: Slug;\n}\n\nexport interface ServicesUrlsChanged {}\n\nexport interface DomainEvent extends DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\n/** @oneof */\nexport interface DomainEventBodyOneOf {\n  createdEvent?: EntityCreatedEvent;\n  updatedEvent?: EntityUpdatedEvent;\n  deletedEvent?: EntityDeletedEvent;\n  actionEvent?: ActionEvent;\n}\n\nexport interface EntityCreatedEvent {\n  entity?: string;\n}\n\nexport interface RestoreInfo {\n  deletedDate?: Date | null;\n}\n\nexport interface EntityUpdatedEvent {\n  /**\n   * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.\n   * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.\n   * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.\n   */\n  currentEntity?: string;\n}\n\nexport interface EntityDeletedEvent {\n  /** Entity that was deleted. */\n  deletedEntity?: string | null;\n}\n\nexport interface ActionEvent {\n  body?: string;\n}\n\nexport interface MessageEnvelope {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n  /** Stringify payload. */\n  data?: string;\n}\n\nexport interface IdentificationData extends IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n  /** @readonly */\n  identityType?: WebhookIdentityTypeWithLiterals;\n}\n\n/** @oneof */\nexport interface IdentificationDataIdOneOf {\n  /**\n   * ID of a site visitor that has not logged in to the site.\n   * @format GUID\n   */\n  anonymousVisitorId?: string;\n  /**\n   * ID of a site visitor that has logged in to the site.\n   * @format GUID\n   */\n  memberId?: string;\n  /**\n   * ID of a Wix user (site owner, contributor, etc.).\n   * @format GUID\n   */\n  wixUserId?: string;\n  /**\n   * ID of an app.\n   * @format GUID\n   */\n  appId?: string;\n}\n\nexport enum WebhookIdentityType {\n  UNKNOWN = 'UNKNOWN',\n  ANONYMOUS_VISITOR = 'ANONYMOUS_VISITOR',\n  MEMBER = 'MEMBER',\n  WIX_USER = 'WIX_USER',\n  APP = 'APP',\n}\n\n/** @enumType */\nexport type WebhookIdentityTypeWithLiterals =\n  | WebhookIdentityType\n  | 'UNKNOWN'\n  | 'ANONYMOUS_VISITOR'\n  | 'MEMBER'\n  | 'WIX_USER'\n  | 'APP';\n\nexport interface CreateAddOnGroupRequest {\n  /** AddOnGroup to create. */\n  addOnGroup: AddOnGroup;\n  /**\n   * ID of the service to create the AddOnGroup for.\n   * @format GUID\n   */\n  serviceId?: string | null;\n}\n\nexport interface CreateAddOnGroupResponse {\n  /** Created AddOnGroup. */\n  addOnGroup?: AddOnGroup;\n}\n\nexport interface DeleteAddOnGroupRequest {\n  /**\n   * ID of the AddOnGroup to delete.\n   * @format GUID\n   */\n  addOnGroupId: string | null;\n  /**\n   * ID of the service from which to delete the AddOnGroup.\n   * @format GUID\n   */\n  serviceId: string | null;\n}\n\nexport interface DeleteAddOnGroupResponse {}\n\nexport interface UpdateAddOnGroupRequest {\n  /** AddOnGroup to update. */\n  addOnGroup: AddOnGroup;\n  /**\n   * ID of the service that contains the AddOnGroup.\n   * @format GUID\n   */\n  serviceId: string | null;\n}\n\nexport interface UpdateAddOnGroupResponse {\n  /** Updated AddOnGroup */\n  addOnGroup?: AddOnGroup;\n}\n\nexport interface ListAddOnGroupsByServiceIdRequest {\n  /**\n   * ID of the service to retrieve AddOnGroups for.\n   * @format GUID\n   */\n  serviceId: string | null;\n  /**\n   * List of group ids to return. If not provided, all groups are returned.\n   * @format GUID\n   * @maxSize 3\n   */\n  groupIds?: string[] | null;\n}\n\nexport interface ListAddOnGroupsByServiceIdResponse {\n  /**\n   * List of group IDs and their linked AddOns.\n   * @maxSize 3\n   */\n  addOnGroupsDetails?: AddOnGroupDetail[];\n}\n\nexport interface AddOn extends AddOnAddOnInfoOneOf {\n  /** The AddOn description. */\n  durationInMinutes?: number;\n  /** The AddOn max quantity. */\n  maxQuantity?: number;\n  /**\n   * The AddOn ID.\n   * @format GUID\n   */\n  addOnId?: string | null;\n  /**\n   * The AddOn name.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /** The AddOn price. */\n  price?: Money;\n}\n\n/** @oneof */\nexport interface AddOnAddOnInfoOneOf {\n  /** The AddOn description. */\n  durationInMinutes?: number;\n  /** The AddOn max quantity. */\n  maxQuantity?: number;\n}\n\nexport interface AddOnGroupDetail {\n  /**\n   * The group ID.\n   * @format GUID\n   */\n  groupId?: string | null;\n  /** The group max number of AddOns. */\n  maxNumberOfAddOns?: number | null;\n  /**\n   * The group name.\n   * @maxLength 100\n   */\n  groupName?: string | null;\n  /**\n   * The AddOns information linked to the group.\n   * @maxSize 7\n   */\n  addOns?: AddOn[];\n  /**\n   * The group prompt.\n   * @maxLength 200\n   */\n  prompt?: string | null;\n}\n\nexport interface SetAddOnsForGroupRequest {\n  /**\n   * The service ID to set AddOns for.\n   * @format GUID\n   */\n  serviceId: string | null;\n  /**\n   * The group ID to set AddOns for.\n   * @format GUID\n   */\n  groupId: string | null;\n  /**\n   * The IDs of AddOns to set.\n   * @format GUID\n   * @minSize 1\n   * @maxSize 7\n   */\n  addOnIds: string[] | null;\n}\n\nexport interface SetAddOnsForGroupResponse {\n  /** The updated AddOnGroup. */\n  addOnGroup?: AddOnGroup;\n}\n\nexport interface CreateServiceRequest {\n  /** Service to create. */\n  service: Service;\n}\n\nexport interface CreateServiceResponse {\n  /** Created service. */\n  service?: Service;\n}\n\nexport interface ValidateServiceRequest {\n  /** Service to validate. */\n  service?: Service;\n}\n\nexport interface ValidateServiceResponse {\n  /** Whether the service is valid. */\n  valid?: boolean;\n  /** Field violations. */\n  fieldViolations?: FieldViolation[];\n}\n\nexport interface FieldViolation {\n  /**\n   * Path to the invalid field.\n   * @maxLength 2000\n   */\n  fieldName?: string;\n  /**\n   * Description of the error.\n   * @maxLength 2000\n   */\n  description?: string;\n  /**\n   * Rule name of the violation\n   * @maxLength 2000\n   */\n  ruleName?: string;\n}\n\nexport interface BulkCreateServicesRequest {\n  /**\n   * Services to create.\n   * @maxSize 100\n   */\n  services: Service[];\n  /** Whether to return the created service objects. */\n  returnEntity?: boolean;\n}\n\nexport interface BulkCreateServicesResponse {\n  /** The result of each service creation. */\n  results?: BulkServiceResult[];\n  /** Create statistics. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkServiceResult {\n  /** Update metadata. */\n  itemMetadata?: ItemMetadata;\n  /** Updated service. */\n  item?: Service;\n}\n\nexport interface ItemMetadata {\n  /**\n   * Item ID. Should always be available, unless it's impossible (for example, when failing to create an item).\n   * @format GUID\n   */\n  _id?: string | null;\n  /** Index of the item within the request array. Allows for correlation between request and response items. */\n  originalIndex?: number;\n  /** Whether the requested action was successful for this item. When `false`, the `error` field is populated. */\n  success?: boolean;\n  /** Details about the error in case of failure. */\n  error?: ApplicationError;\n}\n\nexport interface ApplicationError {\n  /** Error code. */\n  code?: string;\n  /** Description of the error. */\n  description?: string;\n  /** Data related to the error. */\n  data?: Record<string, any> | null;\n}\n\nexport interface BulkActionMetadata {\n  /** Number of items that were successfully processed. */\n  totalSuccesses?: number;\n  /** Number of items that couldn't be processed. */\n  totalFailures?: number;\n  /** Number of failures without details because detailed failure threshold was exceeded. */\n  undetailedFailures?: number;\n}\n\nexport interface GetServiceRequest {\n  /**\n   * ID of the service to retrieve.\n   * @format GUID\n   */\n  serviceId: string;\n}\n\nexport enum V2RequestedFields {\n  /** Unknown requested field. */\n  UNKNOWN_REQUESTED_FIELD = 'UNKNOWN_REQUESTED_FIELD',\n  /** When passed, `service.staff_members` is returned. */\n  STAFF_MEMBER_DETAILS = 'STAFF_MEMBER_DETAILS',\n  /** When passed, `service.service_resources.resource_type.name` is returned. */\n  RESOURCE_TYPE_DETAILS = 'RESOURCE_TYPE_DETAILS',\n}\n\n/** @enumType */\nexport type V2RequestedFieldsWithLiterals =\n  | V2RequestedFields\n  | 'UNKNOWN_REQUESTED_FIELD'\n  | 'STAFF_MEMBER_DETAILS'\n  | 'RESOURCE_TYPE_DETAILS';\n\nexport interface GetServiceResponse {\n  /** Retrieved service. */\n  service?: Service;\n}\n\nexport interface GetServiceAvailabilityConstraintsRequest {\n  /**\n   * ID of the service to retrieve.\n   * @format GUID\n   */\n  serviceId?: string;\n}\n\nexport interface GetServiceAvailabilityConstraintsResponse {\n  /** The retrieved availability constraints of the service. */\n  constraints?: ServiceAvailabilityConstraints;\n}\n\nexport interface ServiceAvailabilityConstraints {\n  /**\n   * The booking policy.\n   * @readonly\n   */\n  bookingPolicy?: BookingPolicy;\n  /**\n   * The service schedule, including the schedule ID and availability constraints.\n   * @readonly\n   */\n  schedule?: Schedule;\n  /**\n   * The locations this service is offered at.\n   * Only multiple locations of type `BUSINESS` are supported. Multiple locations of type `CUSTOM` or `CUSTOMER` are not supported.\n   * For courses only: Currently, only one location is supported for all location types.\n   * Use the `Set Service Locations` method to change the locations this service is offered at.\n   * @readonly\n   * @maxSize 100\n   */\n  locations?: Location[];\n  /**\n   * Resource groups required to book the service. For backward compatibility only. Use `Service Resources` instead.\n   * @maxSize 3\n   * @readonly\n   * @deprecated Resource groups required to book the service. For backward compatibility only. Use `Service Resources` instead.\n   * @replacedBy service_resources\n   * @targetRemovalDate 2024-08-19\n   */\n  resourceGroups?: ResourceGroup[];\n  /**\n   * Resource groups required to book the service.\n   * @maxSize 3\n   * @readonly\n   */\n  serviceResources?: ServiceResource[];\n  /**\n   * The time between available slots' start times.\n   * For example, for 5-minute slots: 3:00, 3:05, 3:10 etc. For 1-hour slots: 3:00, 4:00, 5:00 etc.\n   * Applied to all schedules of the site.\n   * For appointments only.\n   * @readonly\n   */\n  slotsSplitInterval?: SplitInterval;\n  /**\n   * Online booking settings.\n   * @readonly\n   */\n  onlineBooking?: OnlineBooking;\n}\n\n/** The time between available slots' start times. For example, For 5 minute slots, 3:00, 3:05, 3:15 etc. For 1 hour slots, 3:00, 4:00, 5:00 etc. */\nexport interface SplitInterval {\n  /**\n   * Whether the slot duration is used as the split interval value.\n   * If `same_as_duration` is `true`, the `value_in_minutes` is the sum of the first duration in\n   * `schedule.availabilityConstraints.SlotDurations` field, and `schedule.availabilityConstraints.TimeBetweenSlots` field.\n   */\n  sameAsDuration?: boolean | null;\n  /** Number of minutes between available slots' start times when `same_as_duration` is `false`. */\n  valueInMinutes?: number | null;\n}\n\nexport interface UpdateServiceRequest {\n  /** Service to update. */\n  service: Service;\n}\n\nexport interface UpdateServiceResponse {\n  /** Updated service. */\n  service?: Service;\n}\n\nexport interface BulkUpdateServicesRequest {\n  /**\n   * Services to update.\n   * @maxSize 100\n   */\n  services?: MaskedService[];\n  /** Whether to include the updated services in the response. Default: `false` */\n  returnEntity?: boolean;\n}\n\nexport interface MaskedService {\n  /** Service to update. */\n  service?: Service;\n  /** Explicit list of fields to update. */\n  mask?: string[];\n}\n\nexport interface BulkUpdateServicesResponse {\n  /** The result of each service update. */\n  results?: BulkServiceResult[];\n  /** Update statistics. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkUpdateServicesByFilterRequest {\n  /** Filter to identify the services to update. */\n  filter: Record<string, any> | null;\n  /** Service to update. */\n  service: Service;\n}\n\nexport interface BulkUpdateServicesByFilterResponse {\n  /**\n   * ID of the service update job.\n   *\n   * Pass this ID to [Get Async Job](https://dev.wix.com/docs/rest/business-management/async-job/introduction) to retrieve job details and metadata.\n   * @format GUID\n   */\n  jobId?: string;\n}\n\nexport interface DeleteServiceRequest {\n  /**\n   * ID of the service to delete.\n   * @format GUID\n   */\n  serviceId: string;\n  /**\n   * Whether to preserve future sessions with participants.\n   *\n   * Default: `false`\n   */\n  preserveFutureSessionsWithParticipants?: boolean;\n  /**\n   * Whether to notify participants about the change and an optional\n   * custom message.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface ParticipantNotification {\n  /**\n   * Whether to send a message about the changes to the customer.\n   *\n   * Default: `false`\n   */\n  notifyParticipants?: boolean | null;\n  /**\n   * Custom message to send to the participants about the changes to the booking.\n   * @maxLength 2000\n   */\n  message?: string | null;\n}\n\nexport interface DeleteServiceResponse {}\n\nexport interface BulkDeleteServicesRequest {\n  /**\n   * IDs of the services to delete.\n   * @format GUID\n   * @maxSize 100\n   */\n  ids: string[];\n  /**\n   * Whether to preserve future sessions with participants.\n   *\n   * Default: `false`.\n   */\n  preserveFutureSessionsWithParticipants?: boolean;\n  /**\n   * Whether to notify participants about the change and an optional\n   * custom message.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface BulkDeleteServicesResponse {\n  /** The result of each service removal. */\n  results?: BulkServiceResult[];\n  /** Delete statistics. */\n  bulkActionMetadata?: BulkActionMetadata;\n}\n\nexport interface BulkDeleteServicesByFilterRequest {\n  /** Filter to identify the services that need to be deleted. */\n  filter: Record<string, any> | null;\n  /**\n   * Whether to preserve future sessions with participants.\n   *\n   * Default: `false`.\n   */\n  preserveFutureSessionsWithParticipants?: boolean;\n  /** Whether to notify participants about the change and an optional custom message. */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface BulkDeleteServicesByFilterResponse {\n  /**\n   * ID of the service deletion job.\n   *\n   * Pass this ID to [Get Async Job](https://dev.wix.com/docs/rest/business-management/async-job/introduction) to retrieve job details and metadata.\n   * @format GUID\n   */\n  jobId?: string;\n}\n\nexport interface QueryServicesRequest {\n  /** WQL expression. */\n  query: QueryV2;\n}\n\nexport interface QueryV2 extends QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n  /**\n   * Filter object in the following format:\n   *\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   *\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`\n   *\n   * Read more about [supported fields and operators](https://dev.wix.com/api/rest/wix-bookings/services-v2/filtering-and-sorting).\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[ {\"fieldName\":\"sortField1\",\"order\":\"ASC\"},\n   * {\"fieldName\":\"sortField2\",\"order\":\"DESC\"} ]`\n   *\n   * Read more about [sorting](https://dev.wix.com/api/rest/wix-bookings/services-v2/filtering-and-sorting#wix-bookings_services-v2_filtering-and-sorting_sorting).\n   * @maxSize 50\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface QueryV2PagingMethodOneOf {\n  /** Paging options to limit and skip the number of items. */\n  paging?: Paging;\n}\n\nexport interface Sorting {\n  /**\n   * Name of the field to sort by.\n   * @maxLength 512\n   */\n  fieldName?: string;\n  /** Sort order. */\n  order?: SortOrderWithLiterals;\n}\n\nexport enum SortOrder {\n  ASC = 'ASC',\n  DESC = 'DESC',\n}\n\n/** @enumType */\nexport type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';\n\nexport interface Paging {\n  /** Number of items to load. */\n  limit?: number | null;\n  /** Number of items to skip in the current sort order. */\n  offset?: number | null;\n}\n\nexport interface CursorPaging {\n  /**\n   * Number of items to load.\n   * @max 100\n   */\n  limit?: number | null;\n  /**\n   * Pointer to the next or previous page in the list of results.\n   *\n   * You can get the relevant cursor token\n   * from the `pagingMetadata` object in the previous call's response.\n   * Not relevant for the first request.\n   * @maxLength 16000\n   */\n  cursor?: string | null;\n}\n\nexport interface QueryServicesResponse {\n  /** The retrieved services. */\n  services?: Service[];\n  /** Paging metadata, including offset and count. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface PagingMetadataV2 {\n  /** Number of items returned in the response. */\n  count?: number | null;\n  /** Offset that was requested. */\n  offset?: number | null;\n  /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */\n  total?: number | null;\n  /** Flag that indicates the server failed to calculate the `total` field. */\n  tooManyToCount?: boolean | null;\n  /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */\n  cursors?: Cursors;\n}\n\nexport interface Cursors {\n  /**\n   * Cursor token for retrieving the next page of results.\n   *\n   * Use this token in subsequent requests to continue pagination forward.\n   * Value is `null` when on the last page of results.\n   * @maxLength 16000\n   */\n  next?: string | null;\n  /**\n   * Cursor token for retrieving the previous page of results.\n   *\n   * Use this token to navigate backwards through result pages.\n   * Value is `null` when on the first page of results.\n   * @maxLength 16000\n   */\n  prev?: string | null;\n}\n\nexport interface SearchServicesRequest {\n  /**\n   * Search criteria including filter, sort, aggregations, and paging options.\n   *\n   * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)) for complete filter and sorting options.\n   */\n  search: CursorSearch;\n}\n\nexport interface CursorSearch extends CursorSearchPagingMethodOneOf {\n  /**\n   * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,\n   * `filter`, `sort`, or `search` can't be specified.\n   */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object for narrowing search results. For example, to return only services with specific payment options: `\"filter\": {\"payment.options.online\": true, \"payment.options.in_person\": false}`.\n   *\n   * Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Array of sort objects specifying result order. For example, to sort by creation date in descending order: `\"sort\": [{\"fieldName\": \"createdDate\", \"order\": \"DESC\"}]`.\n   *\n   * Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).\n   * @maxSize 10\n   */\n  sort?: Sorting[];\n  /**\n   * Aggregations for grouping data into categories (facets) and providing summaries for each category.\n   * For example, use aggregations to categorize search results by service type, payment options, or locations.\n   * @maxSize 10\n   */\n  aggregations?: Aggregation[];\n  /** Free text to match in searchable fields. */\n  search?: SearchDetails;\n  /**\n   * Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.\n   *\n   * Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties).\n   * @maxLength 50\n   */\n  timeZone?: string | null;\n}\n\n/** @oneof */\nexport interface CursorSearchPagingMethodOneOf {\n  /**\n   * Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,\n   * `filter`, `sort`, or `search` can't be specified.\n   */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface Aggregation extends AggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. */\n  dateHistogram?: DateHistogramAggregation;\n  /** Nested aggregation configuration. */\n  nested?: NestedAggregation;\n  /**\n   * User-defined name of aggregation. Must be unique and will appear in aggregation results.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /** Type of aggregation. Client must specify matching aggregation field below. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n  /**\n   * Deprecated, use `nested` instead.\n   * @deprecated Deprecated, use `nested` instead.\n   * @replacedBy kind.nested\n   * @targetRemovalDate 2025-01-01\n   */\n  groupBy?: GroupByAggregation;\n}\n\n/** @oneof */\nexport interface AggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. */\n  dateHistogram?: DateHistogramAggregation;\n  /** Nested aggregation configuration. */\n  nested?: NestedAggregation;\n}\n\nexport interface RangeBucket {\n  /** Inclusive lower bound of the range. Required if `to` isn't specified. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. Required if `from` isn't specified. */\n  to?: number | null;\n}\n\nexport enum SortType {\n  COUNT = 'COUNT',\n  VALUE = 'VALUE',\n}\n\n/** @enumType */\nexport type SortTypeWithLiterals = SortType | 'COUNT' | 'VALUE';\n\nexport enum SortDirection {\n  DESC = 'DESC',\n  ASC = 'ASC',\n}\n\n/** @enumType */\nexport type SortDirectionWithLiterals = SortDirection | 'DESC' | 'ASC';\n\nexport enum MissingValues {\n  EXCLUDE = 'EXCLUDE',\n  INCLUDE = 'INCLUDE',\n}\n\n/** @enumType */\nexport type MissingValuesWithLiterals = MissingValues | 'EXCLUDE' | 'INCLUDE';\n\nexport interface IncludeMissingValuesOptions {\n  /**\n   * Custom bucket name for missing values.\n   *\n   * Default values:\n   * - string: `N/A`\n   * - int: `0`\n   * - bool: `false`\n   * @maxLength 20\n   */\n  addToBucket?: string;\n}\n\nexport enum ScalarType {\n  UNKNOWN_SCALAR_TYPE = 'UNKNOWN_SCALAR_TYPE',\n  /** Total number of distinct values. */\n  COUNT_DISTINCT = 'COUNT_DISTINCT',\n  /** Minimum value. */\n  MIN = 'MIN',\n  /** Maximum value. */\n  MAX = 'MAX',\n}\n\n/** @enumType */\nexport type ScalarTypeWithLiterals =\n  | ScalarType\n  | 'UNKNOWN_SCALAR_TYPE'\n  | 'COUNT_DISTINCT'\n  | 'MIN'\n  | 'MAX';\n\nexport interface ValueAggregation extends ValueAggregationOptionsOneOf {\n  /** Options for including missing values in the aggregation results. */\n  includeOptions?: IncludeMissingValuesOptions;\n  /** Whether to sort by number of matches or value of the field. */\n  sortType?: SortTypeWithLiterals;\n  /** Whether to sort in ascending or descending order. */\n  sortDirection?: SortDirectionWithLiterals;\n  /**\n   * Number of aggregations to return.\n   *\n   * Min: `1`\n   * Max: `250`\n   * Default: `10`\n   */\n  limit?: number | null;\n  /**\n   * Whether missing values should be included or excluded from the aggregation results.\n   *\n   * Default: `EXCLUDE`\n   */\n  missingValues?: MissingValuesWithLiterals;\n}\n\n/** @oneof */\nexport interface ValueAggregationOptionsOneOf {\n  /** Options for including missing values in the aggregation results. */\n  includeOptions?: IncludeMissingValuesOptions;\n}\n\nexport enum NestedAggregationType {\n  UNKNOWN_AGGREGATION_TYPE = 'UNKNOWN_AGGREGATION_TYPE',\n  /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */\n  VALUE = 'VALUE',\n  /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */\n  RANGE = 'RANGE',\n  /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */\n  SCALAR = 'SCALAR',\n  /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */\n  DATE_HISTOGRAM = 'DATE_HISTOGRAM',\n}\n\n/** @enumType */\nexport type NestedAggregationTypeWithLiterals =\n  | NestedAggregationType\n  | 'UNKNOWN_AGGREGATION_TYPE'\n  | 'VALUE'\n  | 'RANGE'\n  | 'SCALAR'\n  | 'DATE_HISTOGRAM';\n\nexport interface RangeAggregation {\n  /**\n   * List of range buckets defining the ranges for aggregation. During aggregation, each entity is placed in the first bucket where its value falls within the specified range bounds.\n   * @maxSize 50\n   */\n  buckets?: RangeBucket[];\n}\n\nexport interface ScalarAggregation {\n  /** Operator for the scalar aggregation, for example `COUNT_DISTINCT`, `MIN`, `MAX`. */\n  type?: ScalarTypeWithLiterals;\n}\n\nexport interface DateHistogramAggregation {\n  /** Time interval for date histogram aggregation, for example `DAY`, `HOUR`, `MONTH`. */\n  interval?: IntervalWithLiterals;\n}\n\nexport enum Interval {\n  /** Unknown interval. */\n  UNKNOWN_INTERVAL = 'UNKNOWN_INTERVAL',\n  /** Yearly interval. */\n  YEAR = 'YEAR',\n  /** Monthly interval. */\n  MONTH = 'MONTH',\n  /** Weekly interval. */\n  WEEK = 'WEEK',\n  /** Daily interval. */\n  DAY = 'DAY',\n  /** Hourly interval. */\n  HOUR = 'HOUR',\n  /** Minute interval. */\n  MINUTE = 'MINUTE',\n  /** Second interval. */\n  SECOND = 'SECOND',\n}\n\n/** @enumType */\nexport type IntervalWithLiterals =\n  | Interval\n  | 'UNKNOWN_INTERVAL'\n  | 'YEAR'\n  | 'MONTH'\n  | 'WEEK'\n  | 'DAY'\n  | 'HOUR'\n  | 'MINUTE'\n  | 'SECOND';\n\nexport interface NestedAggregationItem extends NestedAggregationItemKindOneOf {\n  /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. Calculates counts within user-defined value ranges. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. Calculates counts within time intervals. */\n  dateHistogram?: DateHistogramAggregation;\n  /**\n   * User-defined name of aggregation. Must be unique and will appear in aggregation results.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /** Type of aggregation. Client must specify matching aggregation field below. */\n  type?: NestedAggregationTypeWithLiterals;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface NestedAggregationItemKindOneOf {\n  /** Value aggregation configuration. Calculates the distribution of field values within the dataset. */\n  value?: ValueAggregation;\n  /** Range aggregation configuration. Calculates counts within user-defined value ranges. */\n  range?: RangeAggregation;\n  /** Scalar aggregation configuration. Calculates single numerical metrics like count, min, max, sum, or average. */\n  scalar?: ScalarAggregation;\n  /** Date histogram aggregation configuration. Calculates counts within time intervals. */\n  dateHistogram?: DateHistogramAggregation;\n}\n\nexport enum AggregationType {\n  UNKNOWN_AGGREGATION_TYPE = 'UNKNOWN_AGGREGATION_TYPE',\n  /** Calculates the distribution of a specific field's values within a dataset, providing insights into the overall distribution and key statistics of those values. */\n  VALUE = 'VALUE',\n  /** Calculates the count of the values from the specified field in the dataset that fall within the range of each bucket you define. */\n  RANGE = 'RANGE',\n  /** Calculates a single numerical value from a dataset, summarizing the dataset into 1 key metric: `COUNT_DISTINCT`, `SUM`, `AVG`, `MIN`, or `MAX`. */\n  SCALAR = 'SCALAR',\n  /** Calculates the count of time values from the specified field in the dataset that fall within each time interval you define (hour, day, week, etc.). */\n  DATE_HISTOGRAM = 'DATE_HISTOGRAM',\n  /** Flattened list of aggregations, where each aggregation is nested within previous 1. */\n  NESTED = 'NESTED',\n}\n\n/** @enumType */\nexport type AggregationTypeWithLiterals =\n  | AggregationType\n  | 'UNKNOWN_AGGREGATION_TYPE'\n  | 'VALUE'\n  | 'RANGE'\n  | 'SCALAR'\n  | 'DATE_HISTOGRAM'\n  | 'NESTED';\n\n/** Nested aggregation for multi-level faceted search. Allows exploring large amounts of data through multiple levels of categorization, where each subsequent aggregation is nested within the previous aggregation to create hierarchical data summaries. */\nexport interface NestedAggregation {\n  /**\n   * Flattened list of aggregations where each next aggregation is nested within the previous 1.\n   * @minSize 2\n   * @maxSize 10\n   */\n  nestedAggregations?: NestedAggregationItem[];\n}\n\nexport interface GroupByAggregation extends GroupByAggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n  /**\n   * User-defined name of aggregation. Must be unique and will appear in aggregation results.\n   * @maxLength 100\n   */\n  name?: string | null;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface GroupByAggregationKindOneOf {\n  /** Value aggregation configuration. */\n  value?: ValueAggregation;\n}\n\nexport interface SearchDetails {\n  /** Search mode. Defines the search logic for combining multiple terms in the `expression`. */\n  mode?: ModeWithLiterals;\n  /**\n   * Search term or expression.\n   * @maxLength 200\n   */\n  expression?: string | null;\n  /**\n   * Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `order.address.streetName`.\n   * @maxSize 10\n   * @maxLength 200\n   */\n  fields?: string[];\n  /** Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions. */\n  fuzzy?: boolean;\n}\n\nexport enum Mode {\n  /** At least 1 of the search terms must be present. */\n  OR = 'OR',\n  /** All search terms must be present. */\n  AND = 'AND',\n}\n\n/** @enumType */\nexport type ModeWithLiterals = Mode | 'OR' | 'AND';\n\nexport interface SearchServicesResponse {\n  /**\n   * Retrieved services that match the search criteria specified in the request.\n   *\n   * Each service includes all standard service information including name, description,\n   * pricing details, location options, schedule information, and payment configuration.\n   */\n  services?: Service[];\n  /**\n   * Cursor-based paging metadata for navigating search results.\n   *\n   * Contains the current page's cursor information, whether there are more results available,\n   * and count details. Use the `next` cursor to retrieve subsequent pages of results.\n   */\n  pagingMetadata?: CursorPagingMetadata;\n  /**\n   * Aggregation results based on the aggregations specified in the search request.\n   *\n   * Provides categorized data summaries such as service counts by type, location distribution,\n   * payment method statistics, or custom aggregations. Available only when aggregations\n   * are requested in the search criteria.\n   */\n  aggregationData?: AggregationData;\n}\n\nexport interface CursorPagingMetadata {\n  /**\n   * Number of items returned in the current response page.\n   *\n   * This count reflects the actual number of items in the current result set,\n   * which may be less than the requested limit if fewer items are available.\n   */\n  count?: number | null;\n  /**\n   * Navigation cursors for moving between result pages.\n   *\n   * Contains `next` and `prev` cursor tokens for pagination. Use the `next` cursor\n   * to retrieve subsequent pages and `prev` cursor to go back to previous pages.\n   * Learn more about cursor paging in the [API Query Language guide](https://dev.wix.com/api/rest/getting-started/api-query-language#getting-started_api-query-language_cursor-paging).\n   */\n  cursors?: Cursors;\n  /**\n   * Indicates whether additional results are available beyond the current page.\n   *\n   * - `true`: More results exist and can be retrieved using the `next` cursor.\n   * - `false`: This is the final page of results.\n   */\n  hasNext?: boolean | null;\n}\n\nexport interface AggregationData {\n  /**\n   * Array of aggregation results, each containing the aggregation metadata and its calculated values for the specified search criteria.\n   * @maxSize 10000\n   */\n  results?: AggregationResults[];\n}\n\nexport interface ValueAggregationResult {\n  /**\n   * Value of the field.\n   * @maxLength 100\n   */\n  value?: string;\n  /** Count of entities with this value. */\n  count?: number;\n}\n\nexport interface RangeAggregationResult {\n  /** Inclusive lower bound of the range. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. */\n  to?: number | null;\n  /** Count of entities in this range. */\n  count?: number;\n}\n\nexport interface NestedAggregationResults\n  extends NestedAggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /**\n   * User-defined name of aggregation, matches the one specified in request.\n   * @maxLength 100\n   */\n  name?: string;\n  /** Type of aggregation that matches result. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface NestedAggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n}\n\nexport interface ValueResults {\n  /**\n   * Array of value aggregation results, each containing a field value and the count of entities with that value.\n   * @maxSize 250\n   */\n  results?: ValueAggregationResult[];\n}\n\nexport interface RangeResults {\n  /**\n   * Array of range aggregation results returned in the same order as requested, each containing range bounds and count of entities within that range.\n   * @maxSize 50\n   */\n  results?: RangeAggregationResult[];\n}\n\nexport interface AggregationResultsScalarResult {\n  /** Type of scalar aggregation. */\n  type?: ScalarTypeWithLiterals;\n  /** Value of the scalar aggregation. */\n  value?: number;\n}\n\nexport interface NestedValueAggregationResult {\n  /**\n   * Value of the field.\n   * @maxLength 1000\n   */\n  value?: string;\n  /** Nested aggregations. */\n  nestedResults?: NestedAggregationResults;\n}\n\nexport interface ValueResult {\n  /**\n   * Value of the field.\n   * @maxLength 1000\n   */\n  value?: string;\n  /** Count of entities with this value. */\n  count?: number | null;\n}\n\nexport interface RangeResult {\n  /** Inclusive lower bound of the range. */\n  from?: number | null;\n  /** Exclusive upper bound of the range. */\n  to?: number | null;\n  /** Count of entities in this range. */\n  count?: number | null;\n}\n\nexport interface ScalarResult {\n  /** Value of the scalar aggregation. */\n  value?: number;\n}\n\nexport interface NestedResultValue extends NestedResultValueResultOneOf {\n  /** Value aggregation result. */\n  value?: ValueResult;\n  /** Range aggregation result. */\n  range?: RangeResult;\n  /** Scalar aggregation result. */\n  scalar?: ScalarResult;\n  /** Date histogram aggregation result. */\n  dateHistogram?: ValueResult;\n}\n\n/** @oneof */\nexport interface NestedResultValueResultOneOf {\n  /** Value aggregation result. */\n  value?: ValueResult;\n  /** Range aggregation result. */\n  range?: RangeResult;\n  /** Scalar aggregation result. */\n  scalar?: ScalarResult;\n  /** Date histogram aggregation result. */\n  dateHistogram?: ValueResult;\n}\n\nexport interface Results {\n  /** Map of nested aggregation results, keyed by aggregation name. */\n  results?: Record<string, NestedResultValue>;\n}\n\nexport interface DateHistogramResult {\n  /**\n   * Date in [ISO 8601 format](https://en.wikipedia.org/wiki/ISO_8601).\n   * @maxLength 100\n   */\n  value?: string;\n  /** Count of documents in the bucket. */\n  count?: number;\n}\n\nexport interface GroupByValueResults {\n  /**\n   * Array of nested value aggregation results, each containing a field value and the associated nested aggregation data.\n   * @maxSize 1000\n   */\n  results?: NestedValueAggregationResult[];\n}\n\nexport interface DateHistogramResults {\n  /**\n   * Array of date histogram aggregation results, each containing a date bucket and its count.\n   * @maxSize 200\n   */\n  results?: DateHistogramResult[];\n}\n\n/**\n * Results of `NESTED` aggregation type in a flattened form.\n * Aggregations in resulting array are keyed by requested aggregation `name`.\n */\nexport interface NestedResults {\n  /**\n   * Array of nested aggregation result groups, each containing multiple aggregation results.\n   * @maxSize 1000\n   */\n  results?: Results[];\n}\n\nexport interface AggregationResults extends AggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /** Group by value aggregation results. */\n  groupedByValue?: GroupByValueResults;\n  /** Date histogram aggregation results. */\n  dateHistogram?: DateHistogramResults;\n  /** Nested aggregation results. */\n  nested?: NestedResults;\n  /**\n   * User-defined name of aggregation as derived from search request.\n   * @maxLength 100\n   */\n  name?: string;\n  /** Type of aggregation that must match specified kind as derived from search request. */\n  type?: AggregationTypeWithLiterals;\n  /**\n   * Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`.\n   * @maxLength 200\n   */\n  fieldPath?: string;\n}\n\n/** @oneof */\nexport interface AggregationResultsResultOneOf {\n  /** Value aggregation results. */\n  values?: ValueResults;\n  /** Range aggregation results. */\n  ranges?: RangeResults;\n  /** Scalar aggregation results. */\n  scalar?: AggregationResultsScalarResult;\n  /** Group by value aggregation results. */\n  groupedByValue?: GroupByValueResults;\n  /** Date histogram aggregation results. */\n  dateHistogram?: DateHistogramResults;\n  /** Nested aggregation results. */\n  nested?: NestedResults;\n}\n\nexport interface QueryPoliciesRequest {\n  /**\n   * Information about filters, paging, and sorting. See the article about\n   * booking policy filters\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n   * for all supported filters and sorting options.\n   */\n  query: CursorQuery;\n}\n\nexport interface CursorQuery extends CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n  /**\n   * Filter object in the following format:\n   * `\"filter\" : {\n   * \"fieldName1\": \"value1\",\n   * \"fieldName2\":{\"$operator\":\"value2\"}\n   * }`\n   * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`\n   */\n  filter?: Record<string, any> | null;\n  /**\n   * Sort object in the following format:\n   * `[{\"fieldName\":\"sortField1\",\"order\":\"ASC\"},{\"fieldName\":\"sortField2\",\"order\":\"DESC\"}]`\n   * @maxSize 50\n   */\n  sort?: Sorting[];\n}\n\n/** @oneof */\nexport interface CursorQueryPagingMethodOneOf {\n  /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */\n  cursorPaging?: CursorPaging;\n}\n\nexport interface QueryPoliciesResponse {\n  /** Retrieved booking policies and information about the services using them. */\n  bookingPolicies?: BookingPolicyWithServices[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n}\n\nexport interface BookingPolicyWithServices {\n  /** Retrieved booking policy. */\n  bookingPolicy?: BookingPolicy;\n  /**\n   * Up to 5 services connected to the booking policy. If `totalServiceCount` is\n   * greater than 5, there are additional services connected to the policy.\n   * You can call *Search Services*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))\n   * specifying the relevant policy ID in the filter, to retrieve all services that are\n   * connected to a booking policy.\n   * @maxSize 5\n   */\n  connectedServices?: Service[];\n  /** Total number of services connected to the booking policy. */\n  totalServiceCount?: number;\n}\n\nexport interface QueryBookingFormsRequest {\n  /**\n   * Information about filters, paging, and sorting. See the article about\n   * form filters ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object))\n   * for all supported filters and sorting options.\n   */\n  query: CursorQuery;\n  /**\n   * Conditional fields to return.\n   * @maxSize 1\n   */\n  conditionalFields?: RequestedFieldsWithLiterals[];\n}\n\nexport enum RequestedFields {\n  /** Unknown requested conditional field. */\n  UNKNOWN_REQUESTED_FIELD = 'UNKNOWN_REQUESTED_FIELD',\n  /** Whether to return the site's default booking form. */\n  DEFAULT_BOOKING_FORM = 'DEFAULT_BOOKING_FORM',\n}\n\n/** @enumType */\nexport type RequestedFieldsWithLiterals =\n  | RequestedFields\n  | 'UNKNOWN_REQUESTED_FIELD'\n  | 'DEFAULT_BOOKING_FORM';\n\nexport interface QueryBookingFormsResponse {\n  /** Retrieved booking forms and information about connected services. */\n  bookingForms?: BookingForm[];\n  /** Paging metadata. */\n  pagingMetadata?: CursorPagingMetadata;\n  /** The site's default booking form and information about connected services. */\n  defaultBookingForm?: BookingForm;\n}\n\nexport interface BookingForm {\n  /** Information about the retrieved booking form. */\n  formDetails?: FormDetails;\n  /**\n   * Up to 5 services connected to the booking form. If `totalServiceCount` is\n   * greater than 5, there are additional services connected to the policy.\n   * You can call *Search Services*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))\n   * specifying the relevant policy ID in the filter, to retrieve all services that are\n   * connected to a booking policy.\n   * @maxSize 5\n   */\n  connectedServices?: ConnectedService[];\n  /** Total number of services connected to the booking form. */\n  totalServiceCount?: number;\n}\n\nexport interface FormDetails {\n  /**\n   * Form ID.\n   * @format GUID\n   */\n  formId?: string;\n  /**\n   * Form name.\n   * @maxLength 200\n   */\n  name?: string | null;\n  /**\n   * Revision number, which increments by 1 each time the form is updated.\n   * To prevent conflicting changes,\n   * the current revision must be passed when updating the form.\n   * @readonly\n   */\n  revision?: string | null;\n}\n\nexport interface ConnectedService {\n  /**\n   * ID of the service connected to the booking form.\n   * @format GUID\n   */\n  _id?: string | null;\n  /**\n   * Name of the service connected to the booking form.\n   * @maxLength 400\n   * @minLength 1\n   */\n  name?: string | null;\n}\n\nexport interface CountServicesRequest {\n  /**\n   * Query filter to base the count on. See supported filters\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n   * for more details.\n   */\n  filter?: Record<string, any> | null;\n}\n\nexport interface CountServicesResponse {\n  /** Number of services matching the specified filter. */\n  count?: number;\n}\n\nexport interface QueryLocationsRequest {\n  /** Filter. */\n  filter?: QueryLocationsFilter;\n}\n\nexport interface QueryLocationsFilter {\n  /**\n   * Service filter. See supported filters\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n   * for more details.\n   */\n  services?: Record<string, any> | null;\n  /**\n   * List of business IDs to filter by.\n   * @format GUID\n   * @maxSize 100\n   */\n  businessLocationIds?: string[];\n}\n\nexport interface QueryLocationsResponse {\n  /**\n   * Retrieved business locations and whether each location is connected to at\n   * least one service.\n   */\n  businessLocations?: BusinessLocations;\n  /**\n   * Retrieved custom locations and whether each location is connected to at\n   * least one service.\n   */\n  customLocations?: CustomLocations;\n  /**\n   * Retrieved customer locations and whether each location is connected to at\n   * least one service.\n   */\n  customerLocations?: CustomerLocations;\n}\n\nexport interface BusinessLocations {\n  /**\n   * Whether at least one service matching the filter is connected to any of the\n   * retrieved business locations.\n   */\n  exists?: boolean;\n  /**\n   * Retrieved business locations.\n   * @maxSize 100\n   */\n  locations?: Location[];\n}\n\nexport interface CustomLocations {\n  /**\n   * Whether at least one service matching the filter is connected to any of the\n   * retrieved custom locations.\n   */\n  exists?: boolean;\n}\n\nexport interface CustomerLocations {\n  /**\n   * Whether at least one service matching the filter is connected to any of the\n   * retrieved customer locations.\n   */\n  exists?: boolean;\n}\n\nexport interface QueryCategoriesRequest {\n  /** Filter. */\n  filter?: QueryCategoriesFilter;\n}\n\nexport interface QueryCategoriesFilter {\n  /**\n   * Service filter. See supported filters\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n   * for more details.\n   */\n  services?: Record<string, any> | null;\n  /**\n   * List of category IDs to filter by.\n   * @format GUID\n   * @maxSize 100\n   */\n  categoryIds?: string[];\n}\n\nexport interface QueryCategoriesResponse {\n  /** Retrieved categories. */\n  categories?: V2Category[];\n}\n\nexport interface QueryServicesMultiLanguageRequest {\n  /** WQL expression. */\n  query?: QueryV2;\n}\n\nexport interface QueryServicesMultiLanguageResponse {\n  /** The retrieved services in the main language */\n  services?: Service[];\n  /**\n   * the retrieved services in the requested language according to the\n   * provided linguist aspect\n   */\n  translatedServices?: Service[];\n  /** Paging metadata, including offset and count. */\n  pagingMetadata?: PagingMetadataV2;\n}\n\nexport interface SetServiceLocationsRequest {\n  /**\n   * ID of the service.\n   * @format GUID\n   */\n  serviceId: string;\n  /**\n   * List of locations replacing existing service locations. Omitting an existing location removes it from the service. Specify `removedLocationSessionsAction` to determine the handling of future sessions scheduled at that location.\n   * @maxSize 100\n   */\n  locations: Location[];\n  /**\n   * The action to perform on sessions currently set to a removed location. For\n   * example, move existing sessions to a new specified location.\n   * If not specified, sessions will not be moved to a new location.\n   */\n  removedLocationSessionsAction?: RemovedLocationSessionsAction;\n  /**\n   * Whether to notify participants about the change of location, and an\n   * Optional custom message. The notification is sent only to participants of sessions that are affected by the change.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\nexport interface RemovedLocationSessionsAction\n  extends RemovedLocationSessionsActionActionOptionsOneOf {\n  /**\n   * Details about the new location of future events that were scheduled to take\n   * place at a removed location.\n   */\n  moveToLocationOptions?: MoveToNewLocationsOptions;\n  /** Information about what to do with future events at the removed location. */\n  action?: ActionWithLiterals;\n}\n\n/** @oneof */\nexport interface RemovedLocationSessionsActionActionOptionsOneOf {\n  /**\n   * Details about the new location of future events that were scheduled to take\n   * place at a removed location.\n   */\n  moveToLocationOptions?: MoveToNewLocationsOptions;\n}\n\nexport enum Action {\n  UNKNOWN_ACTION_TYPE = 'UNKNOWN_ACTION_TYPE',\n  /** Retain all future sessions at their current location. This is the default. */\n  KEEP_AT_CURRENT_LOCATION = 'KEEP_AT_CURRENT_LOCATION',\n  /** Move future events to a new location. */\n  MOVE_TO_LOCATION = 'MOVE_TO_LOCATION',\n  /**\n   * Cancel all future events at the removed location.\n   * Currently not supported.\n   */\n  DELETE = 'DELETE',\n}\n\n/** @enumType */\nexport type ActionWithLiterals =\n  | Action\n  | 'UNKNOWN_ACTION_TYPE'\n  | 'KEEP_AT_CURRENT_LOCATION'\n  | 'MOVE_TO_LOCATION'\n  | 'DELETE';\n\nexport interface MoveToNewLocationsOptions {\n  /** The new location to move existing sessions currently set to a removed location, used when `action` is `MOVE_TO_LOCATION`. */\n  newLocation?: Location;\n}\n\nexport interface SetServiceLocationsResponse {\n  /** The updated service with the newly set locations. */\n  service?: Service;\n}\n\nexport interface EnablePricingPlansForServiceRequest {\n  /**\n   * ID of the service to update.\n   * @format GUID\n   */\n  serviceId: string;\n  /**\n   * IDs of the *pricing plans*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n   * to add to the service's `payment.pricingPlanIds` array.\n   * @format GUID\n   * @maxSize 100\n   */\n  pricingPlanIds: string[];\n}\n\nexport interface EnablePricingPlansForServiceResponse {\n  /** Updated service. */\n  service?: Service;\n}\n\nexport interface InvalidPricingPlan {\n  /**\n   * ID of the invalid pricing plan.\n   * @format GUID\n   */\n  _id?: string;\n  /**\n   * Explanation why the pricing plan is considered invalid.\n   * @maxLength 2000\n   */\n  message?: string;\n}\n\nexport interface DisablePricingPlansForServiceRequest {\n  /**\n   * ID of the service to update.\n   * @format GUID\n   */\n  serviceId: string;\n  /**\n   * IDs of the *pricing plans*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n   * to remove from the service's `payment.pricingPlanIds` array.\n   * @format GUID\n   * @maxSize 75\n   */\n  pricingPlanIds?: string[];\n}\n\nexport interface DisablePricingPlansForServiceResponse {\n  /** Updated service. */\n  service?: Service;\n}\n\nexport interface SetCustomSlugRequest {\n  /**\n   * ID of the service for which to update the active slug.\n   * @format GUID\n   */\n  serviceId: string;\n  /**\n   * Slug to set as the active service slug.\n   * @maxLength 500\n   */\n  slug?: string;\n}\n\nexport interface SetCustomSlugResponse {\n  /** Updated active service slug. */\n  slug?: Slug;\n  /** Updated service. */\n  service?: Service;\n}\n\nexport interface ValidateSlugRequest {\n  /**\n   * IO of the service to check custom slug validity for.\n   * @format GUID\n   */\n  serviceId: string;\n  /**\n   * Custom slug to validate.\n   * @maxLength 500\n   */\n  slug?: string;\n}\n\nexport interface ValidateSlugResponse {\n  /** Whether the slug is valid. */\n  valid?: boolean;\n  /**\n   * Valid slug. Available only if `{\"valid\": true}`.\n   * @maxLength 500\n   */\n  slug?: string | null;\n  /**\n   * Reasons why the slug is invalid. Available only if `{\"valid\": false}`.\n   * @maxSize 3\n   */\n  errors?: InvalidSlugErrorWithLiterals[];\n}\n\nexport enum InvalidSlugError {\n  /** Unknown slug error. */\n  UNKNOWN_SLUG_ERROR = 'UNKNOWN_SLUG_ERROR',\n  /** Slug contains illegal characters. */\n  SLUG_CONTAINS_ILLEGAL_CHARACTERS = 'SLUG_CONTAINS_ILLEGAL_CHARACTERS',\n  /** Slug is already associated with another service. */\n  SLUG_ALREADY_EXISTS = 'SLUG_ALREADY_EXISTS',\n}\n\n/** @enumType */\nexport type InvalidSlugErrorWithLiterals =\n  | InvalidSlugError\n  | 'UNKNOWN_SLUG_ERROR'\n  | 'SLUG_CONTAINS_ILLEGAL_CHARACTERS'\n  | 'SLUG_ALREADY_EXISTS';\n\nexport interface CloneServiceRequest {\n  /**\n   * ID of the service to clone.\n   * @format GUID\n   */\n  sourceServiceId: string;\n}\n\nexport interface CloneServiceResponse {\n  /** Cloned service. */\n  service?: Service;\n  /**\n   * Information about connected entities that couldn't be cloned. For example,\n   * future recurring events, the booking form, service variants, and connected\n   * pricing plans.\n   */\n  errors?: CloneErrorsWithLiterals[];\n}\n\nexport enum CloneErrors {\n  /**\n   * Failed to clone the original service's *options and variants*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n   */\n  OPTIONS_AND_VARIANTS = 'OPTIONS_AND_VARIANTS',\n  /** Failed to clone the original service's *booking form*. */\n  FORM = 'FORM',\n}\n\n/** @enumType */\nexport type CloneErrorsWithLiterals =\n  | CloneErrors\n  | 'OPTIONS_AND_VARIANTS'\n  | 'FORM';\n\n/** An event sent every time a category entity is changed. */\nexport interface CategoryNotification {\n  category?: Category;\n  event?: CategoryNotificationEventWithLiterals;\n}\n\n/** Categories are used to group multiple services together. A service must be associated with a category in order to be exposed in the Wix Bookings UI. */\nexport interface Category {\n  /**\n   * Category ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Category name.\n   * @maxLength 500\n   */\n  name?: string | null;\n  /**\n   * Category status.\n   *\n   * Default: `CREATED`\n   * @readonly\n   */\n  status?: StatusWithLiterals;\n  /** Sort order of the category in the live site and dashboard. */\n  sortOrder?: number | null;\n}\n\nexport enum Status {\n  /** The category was created. */\n  CREATED = 'CREATED',\n  /** The category was deleted. */\n  DELETED = 'DELETED',\n}\n\n/** @enumType */\nexport type StatusWithLiterals = Status | 'CREATED' | 'DELETED';\n\nexport enum CategoryNotificationEvent {\n  /** Category was updated. */\n  Updated = 'Updated',\n  /** Category was deleted. */\n  Deleted = 'Deleted',\n  /** Category was created. */\n  Created = 'Created',\n}\n\n/** @enumType */\nexport type CategoryNotificationEventWithLiterals =\n  | CategoryNotificationEvent\n  | 'Updated'\n  | 'Deleted'\n  | 'Created';\n\nexport interface Empty {}\n\nexport interface BenefitNotification {\n  /**\n   * Plan unique ID\n   * @format GUID\n   */\n  planId?: string;\n  /**\n   * App def ID\n   * @format GUID\n   */\n  appDefId?: string;\n  /** Current benefit details */\n  benefit?: Benefit;\n  /** Previous benefit */\n  prevBenefit?: Benefit;\n  /** Notification event */\n  event?: EventWithLiterals;\n}\n\nexport interface Benefit {\n  /**\n   * Benefit unique ID\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /** Benefit Type */\n  benefitType?: BenefitTypeWithLiterals;\n  /**\n   * Resource IDs that serves by this benefit\n   * @format GUID\n   */\n  resourceIds?: string[];\n  /** Amount of credits that provided by this benefit */\n  creditAmount?: number | null;\n  /**\n   * additional details related to benefit; limited to 20 entries, 20 symbols for key and 20 symbols for value\n   * @maxSize 20\n   */\n  customFields?: Record<string, string>;\n  /** return value only in case it required in the ListRequest, true means that benefit's type could be updated */\n  editable?: boolean | null;\n  /** Benefit behavior */\n  behavior?: Behavior;\n  /**\n   * Id of the app associated with this benefit\n   * @format GUID\n   * @readonly\n   */\n  appDefId?: string | null;\n}\n\nexport interface EntryPass {}\n\nexport interface Discount extends DiscountDiscountOneOf {\n  /**\n   * Fixed-rate percent off discount\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentOffRate?: string;\n  /**\n   * Absolute amount discount\n   * @decimalValue options { gt:0, maxScale:2 }\n   */\n  moneyOffAmount?: string;\n}\n\n/** @oneof */\nexport interface DiscountDiscountOneOf {\n  /**\n   * Fixed-rate percent off discount\n   * @decimalValue options { gt:0, lte:100, maxScale:2 }\n   */\n  percentOffRate?: string;\n  /**\n   * Absolute amount discount\n   * @decimalValue options { gt:0, maxScale:2 }\n   */\n  moneyOffAmount?: string;\n}\n\nexport enum BenefitType {\n  /** Should never be used */\n  UNDEFINED = 'UNDEFINED',\n  /** Limited benefit type */\n  LIMITED = 'LIMITED',\n  /** Unlimited benefit type */\n  UNLIMITED = 'UNLIMITED',\n}\n\n/** @enumType */\nexport type BenefitTypeWithLiterals =\n  | BenefitType\n  | 'UNDEFINED'\n  | 'LIMITED'\n  | 'UNLIMITED';\n\nexport interface Behavior extends BehaviorBehaviorOneOf {\n  /** Entry pass for resources, e.g. a ticket for Bookings service or a ticket for Events. */\n  defaultBehavior?: EntryPass;\n  /** Discount applied to paid resources */\n  discount?: Discount;\n}\n\n/** @oneof */\nexport interface BehaviorBehaviorOneOf {\n  /** Entry pass for resources, e.g. a ticket for Bookings service or a ticket for Events. */\n  defaultBehavior?: EntryPass;\n  /** Discount applied to paid resources */\n  discount?: Discount;\n}\n\nexport enum Event {\n  Updated = 'Updated',\n  Deleted = 'Deleted',\n  Created = 'Created',\n}\n\n/** @enumType */\nexport type EventWithLiterals = Event | 'Updated' | 'Deleted' | 'Created';\n\nexport interface UserDomainInfoChangedEvent {\n  domainName?: string;\n  crudType?: CrudTypeWithLiterals;\n  /** @format GUID */\n  metaSiteId?: string | null;\n  changeTime?: Date | null;\n}\n\nexport enum CrudType {\n  INVALID_CRUD_TYPE = 'INVALID_CRUD_TYPE',\n  CREATE = 'CREATE',\n  UPDATE = 'UPDATE',\n  DELETE = 'DELETE',\n  /** Unfortunately this action is used by hibernate save in wix-war */\n  CREATE_OR_UPDATE = 'CREATE_OR_UPDATE',\n}\n\n/** @enumType */\nexport type CrudTypeWithLiterals =\n  | CrudType\n  | 'INVALID_CRUD_TYPE'\n  | 'CREATE'\n  | 'UPDATE'\n  | 'DELETE'\n  | 'CREATE_OR_UPDATE';\n\nexport interface HtmlSitePublished {\n  /**\n   * Application instance ID\n   * @maxLength 50\n   */\n  appInstanceId?: string;\n  /**\n   * Application type\n   * @maxLength 100\n   */\n  appType?: string;\n  /** Revision */\n  revision?: string;\n  /**\n   * MSID\n   * @maxLength 100\n   */\n  metaSiteId?: string | null;\n  /**\n   * optional branch id if publish is done from branch\n   * @format GUID\n   */\n  branchId?: string | null;\n  /** The site's last transactionId */\n  lastTransactionId?: string | null;\n  /** A list of the site's pages */\n  pages?: Page[];\n  /** Site's publish date */\n  publishDate?: string;\n}\n\nexport interface Page {\n  /**\n   * Page's Id\n   * @maxLength 100\n   */\n  _id?: string;\n}\n\n/** Encapsulates all details written to the Greyhound topic when a site's properties are updated. */\nexport interface SitePropertiesNotification {\n  /** The site ID for which this update notification applies. */\n  metasiteId?: string;\n  /** The actual update event. */\n  event?: SitePropertiesEvent;\n  /**\n   * A convenience set of mappings from the MetaSite ID to its constituent services.\n   * @maxSize 500\n   */\n  translations?: Translation[];\n  /** Context of the notification */\n  changeContext?: ChangeContext;\n}\n\n/** The actual update event for a particular notification. */\nexport interface SitePropertiesEvent {\n  /** Version of the site's properties represented by this update. */\n  version?: number;\n  /** Set of properties that were updated - corresponds to the fields in \"properties\". */\n  fields?: string[];\n  /** Updated properties. */\n  properties?: Properties;\n}\n\nexport interface Properties {\n  /** Site categories. */\n  categories?: Categories;\n  /** Site locale. */\n  locale?: Locale;\n  /**\n   * Site language.\n   *\n   * Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format.\n   */\n  language?: string | null;\n  /**\n   * Site currency format used to bill customers.\n   *\n   * Three-letter currency code in [ISO-4217 alphabetic](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) format.\n   */\n  paymentCurrency?: string | null;\n  /** Timezone in `America/New_York` format. */\n  timeZone?: string | null;\n  /** Email address. */\n  email?: string | null;\n  /** Phone number. */\n  phone?: string | null;\n  /** Fax number. */\n  fax?: string | null;\n  /** Address. */\n  address?: Address;\n  /** Site display name. */\n  siteDisplayName?: string | null;\n  /** Business name. */\n  businessName?: string | null;\n  /** Path to the site's logo in Wix Media (without Wix Media base URL). */\n  logo?: string | null;\n  /** Site description. */\n  description?: string | null;\n  /**\n   * Business schedule. Regular and exceptional time periods when the business is open or the service is available.\n   *\n   * __Note:__ Not supported by Wix Bookings.\n   */\n  businessSchedule?: BusinessSchedule;\n  /** Supported languages of a site and the primary language. */\n  multilingual?: Multilingual;\n  /** Cookie policy the Wix user defined for their site (before the site visitor interacts with/limits it). */\n  consentPolicy?: ConsentPolicy;\n  /**\n   * Supported values: `FITNESS SERVICE`, `RESTAURANT`, `BLOG`, `STORE`, `EVENT`, `UNKNOWN`.\n   *\n   * Site business type.\n   */\n  businessConfig?: string | null;\n  /** External site URL that uses Wix as its headless business solution. */\n  externalSiteUrl?: string | null;\n  /** Track clicks analytics. */\n  trackClicksAnalytics?: boolean;\n}\n\nexport interface Categories {\n  /** Primary site category. */\n  primary?: string;\n  /**\n   * Secondary site category.\n   * @maxSize 50\n   */\n  secondary?: string[];\n  /** Business Term Id */\n  businessTermId?: string | null;\n}\n\nexport interface Locale {\n  /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */\n  languageCode?: string;\n  /** Two-letter country code in [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements) format. */\n  country?: string;\n}\n\nexport interface Address {\n  /** Street name. */\n  street?: string;\n  /** City name. */\n  city?: string;\n  /** Two-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format. */\n  country?: string;\n  /** State. */\n  state?: string;\n  /**\n   * Zip or postal code.\n   * @maxLength 20\n   */\n  zip?: string;\n  /** Extra information to be displayed in the address. */\n  hint?: AddressHint;\n  /** Whether this address represents a physical location. */\n  isPhysical?: boolean;\n  /** Google-formatted version of this address. */\n  googleFormattedAddress?: string;\n  /** Street number. */\n  streetNumber?: string;\n  /** Apartment number. */\n  apartmentNumber?: string;\n  /** Geographic coordinates of location. */\n  coordinates?: GeoCoordinates;\n}\n\n/**\n * Extra information on displayed addresses.\n * This is used for display purposes. Used to add additional data about the address, such as \"In the passage\".\n * Free text. In addition, the user can state where to display the additional description - before, after, or instead of the address string.\n */\nexport interface AddressHint {\n  /** Extra text displayed next to, or instead of, the actual address. */\n  text?: string;\n  /** Where the extra text should be displayed. */\n  placement?: PlacementTypeWithLiterals;\n}\n\n/** Where the extra text should be displayed: before, after or instead of the actual address. */\nexport enum PlacementType {\n  BEFORE = 'BEFORE',\n  AFTER = 'AFTER',\n  REPLACE = 'REPLACE',\n}\n\n/** @enumType */\nexport type PlacementTypeWithLiterals =\n  | PlacementType\n  | 'BEFORE'\n  | 'AFTER'\n  | 'REPLACE';\n\n/** Geocoordinates for a particular address. */\nexport interface GeoCoordinates {\n  /** Latitude of the location. Must be between -90 and 90. */\n  latitude?: number;\n  /** Longitude of the location. Must be between -180 and 180. */\n  longitude?: number;\n}\n\n/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */\nexport interface BusinessSchedule {\n  /**\n   * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.\n   * @maxSize 100\n   */\n  periods?: TimePeriod[];\n  /**\n   * Exceptions to the business's regular hours. The business can be open or closed during the exception.\n   * @maxSize 100\n   */\n  specialHourPeriod?: SpecialHourPeriod[];\n}\n\n/** Weekly recurring time periods when the business is regularly open or the service is available. */\nexport interface TimePeriod {\n  /** Day of the week the period starts on. */\n  openDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   */\n  openTime?: string;\n  /** Day of the week the period ends on. */\n  closeDay?: DayOfWeekWithLiterals;\n  /**\n   * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents\n   * midnight at the end of the specified day.\n   *\n   * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.\n   */\n  closeTime?: string;\n}\n\n/** Enumerates the days of the week. */\nexport enum DayOfWeek {\n  MONDAY = 'MONDAY',\n  TUESDAY = 'TUESDAY',\n  WEDNESDAY = 'WEDNESDAY',\n  THURSDAY = 'THURSDAY',\n  FRIDAY = 'FRIDAY',\n  SATURDAY = 'SATURDAY',\n  SUNDAY = 'SUNDAY',\n}\n\n/** @enumType */\nexport type DayOfWeekWithLiterals =\n  | DayOfWeek\n  | 'MONDAY'\n  | 'TUESDAY'\n  | 'WEDNESDAY'\n  | 'THURSDAY'\n  | 'FRIDAY'\n  | 'SATURDAY'\n  | 'SUNDAY';\n\n/** Exception to the business's regular hours. The business can be open or closed during the exception. */\nexport interface SpecialHourPeriod {\n  /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  startDate?: string;\n  /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */\n  endDate?: string;\n  /**\n   * Whether the business is closed (or the service is not available) during the exception.\n   *\n   * Default: `true`.\n   */\n  isClosed?: boolean;\n  /** Additional info about the exception. For example, \"We close earlier on New Year's Eve.\" */\n  comment?: string;\n}\n\nexport interface Multilingual {\n  /**\n   * Supported languages list.\n   * @maxSize 200\n   */\n  supportedLanguages?: SupportedLanguage[];\n  /** Whether to redirect to user language. */\n  autoRedirect?: boolean;\n}\n\nexport interface SupportedLanguage {\n  /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */\n  languageCode?: string;\n  /** Locale. */\n  locale?: Locale;\n  /** Whether the supported language is the primary language for the site. */\n  isPrimary?: boolean;\n  /** Language icon. */\n  countryCode?: string;\n  /** How the language will be resolved. For internal use. */\n  resolutionMethod?: ResolutionMethodWithLiterals;\n  /** Whether the supported language is the primary language for site visitors. */\n  isVisitorPrimary?: boolean | null;\n}\n\nexport enum ResolutionMethod {\n  QUERY_PARAM = 'QUERY_PARAM',\n  SUBDOMAIN = 'SUBDOMAIN',\n  SUBDIRECTORY = 'SUBDIRECTORY',\n}\n\n/** @enumType */\nexport type ResolutionMethodWithLiterals =\n  | ResolutionMethod\n  | 'QUERY_PARAM'\n  | 'SUBDOMAIN'\n  | 'SUBDIRECTORY';\n\nexport interface ConsentPolicy {\n  /** Whether the site uses cookies that are essential to site operation. Always `true`. */\n  essential?: boolean | null;\n  /** Whether the site uses cookies that affect site performance and other functional measurements. */\n  functional?: boolean | null;\n  /** Whether the site uses cookies that collect analytics about how the site is used (in order to improve it). */\n  analytics?: boolean | null;\n  /** Whether the site uses cookies that collect information allowing better customization of the experience for a current visitor. */\n  advertising?: boolean | null;\n  /** CCPA compliance flag. */\n  dataToThirdParty?: boolean | null;\n}\n\n/** A single mapping from the MetaSite ID to a particular service. */\nexport interface Translation {\n  /** The service type. */\n  serviceType?: string;\n  /** The application definition ID; this only applies to services of type ThirdPartyApps. */\n  appDefId?: string;\n  /** The instance ID of the service. */\n  instanceId?: string;\n}\n\nexport interface ChangeContext extends ChangeContextPayloadOneOf {\n  /** Properties were updated. */\n  propertiesChange?: PropertiesChange;\n  /** Default properties were created on site creation. */\n  siteCreated?: SiteCreated;\n  /** Properties were cloned on site cloning. */\n  siteCloned?: SiteCloned;\n}\n\n/** @oneof */\nexport interface ChangeContextPayloadOneOf {\n  /** Properties were updated. */\n  propertiesChange?: PropertiesChange;\n  /** Default properties were created on site creation. */\n  siteCreated?: SiteCreated;\n  /** Properties were cloned on site cloning. */\n  siteCloned?: SiteCloned;\n}\n\nexport interface PropertiesChange {}\n\nexport interface SiteCreated {\n  /** Origin template site id. */\n  originTemplateId?: string | null;\n}\n\nexport interface SiteCloned {\n  /** Origin site id. */\n  originMetaSiteId?: string;\n}\n\n/** @docsIgnore */\nexport type DeleteAddOnGroupApplicationErrors = {\n  code?: 'GROUP_NOT_ON_SERVICE';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type UpdateAddOnGroupApplicationErrors = {\n  code?: 'GROUP_NOT_ON_SERVICE';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type SetAddOnsForGroupApplicationErrors = {\n  code?: 'ADD_ON_GROUP_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type CreateServiceValidationErrors =\n  | {\n      ruleName?: 'INVALID_FORM';\n    }\n  | {\n      ruleName?: 'INVALID_CATEGORY';\n    }\n  | {\n      ruleName?: 'INVALID_BOOKING_POLICY';\n    }\n  | {\n      ruleName?: 'INVALID_SERVICE_TYPE';\n    }\n  | {\n      ruleName?: 'INVALID_SERVICE_NAME';\n    }\n  | {\n      ruleName?: 'INVALID_ONLINE_BOOKING';\n    }\n  | {\n      ruleName?: 'INVALID_STAFF_MEMBER_IDS';\n    }\n  | {\n      ruleName?: 'PAYMENT_REQUIRED';\n    }\n  | {\n      ruleName?: 'INVALID_PAYMENT_TYPE';\n    }\n  | {\n      ruleName?: 'INVALID_RATE';\n    }\n  | {\n      ruleName?: 'INVALID_PAYMENT_OPTIONS';\n    }\n  | {\n      ruleName?: 'INVALID_BUSINESS_LOCATIONS';\n    }\n  | {\n      ruleName?: 'INVALID_LOCATIONS';\n    }\n  | {\n      ruleName?: 'INVALID_BUSINESS_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_CUSTOM_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_CUSTOMER_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_UNKNOWN_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_MANUAL_APPROVAL_WITH_PRICING_PLANS';\n    }\n  | {\n      ruleName?: 'INVALID_DEFAULT_CAPACITY';\n    }\n  | {\n      ruleName?: 'INVALID_APPOINTMENT_CAPACITY';\n    }\n  | {\n      ruleName?: 'INVALID_SESSION_DURATION';\n    };\n/** @docsIgnore */\nexport type UpdateServiceValidationErrors =\n  | {\n      ruleName?: 'INVALID_FORM';\n    }\n  | {\n      ruleName?: 'INVALID_CATEGORY';\n    }\n  | {\n      ruleName?: 'INVALID_BOOKING_POLICY';\n    }\n  | {\n      ruleName?: 'INVALID_SERVICE_TYPE';\n    }\n  | {\n      ruleName?: 'INVALID_SERVICE_NAME';\n    }\n  | {\n      ruleName?: 'INVALID_ONLINE_BOOKING';\n    }\n  | {\n      ruleName?: 'INVALID_STAFF_MEMBER_IDS';\n    }\n  | {\n      ruleName?: 'PAYMENT_REQUIRED';\n    }\n  | {\n      ruleName?: 'INVALID_PAYMENT_TYPE';\n    }\n  | {\n      ruleName?: 'INVALID_RATE';\n    }\n  | {\n      ruleName?: 'INVALID_PAYMENT_OPTIONS';\n    }\n  | {\n      ruleName?: 'INVALID_BUSINESS_LOCATIONS';\n    }\n  | {\n      ruleName?: 'INVALID_LOCATIONS';\n    }\n  | {\n      ruleName?: 'INVALID_BUSINESS_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_CUSTOM_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_CUSTOMER_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_UNKNOWN_LOCATION';\n    }\n  | {\n      ruleName?: 'INVALID_MANUAL_APPROVAL_WITH_PRICING_PLANS';\n    }\n  | {\n      ruleName?: 'INVALID_DEFAULT_CAPACITY';\n    }\n  | {\n      ruleName?: 'INVALID_APPOINTMENT_CAPACITY';\n    }\n  | {\n      ruleName?: 'INVALID_SESSION_DURATION';\n    };\n/** @docsIgnore */\nexport type QueryBookingFormsApplicationErrors = {\n  code?: 'DEFAULT_BOOKING_FORM_NOT_FOUND';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type EnablePricingPlansForServiceApplicationErrors =\n  | {\n      code?: 'INVALID_PRICING_PLAN';\n      description?: string;\n      data?: InvalidPricingPlan;\n    }\n  | {\n      code?: 'SERVICE_DOES_NOT_SUPPORT_PRICING_PLANS';\n      description?: string;\n      data?: Record<string, any>;\n    };\n/** @docsIgnore */\nexport type DisablePricingPlansForServiceApplicationErrors = {\n  code?: 'INVALID_PRICING_PLAN';\n  description?: string;\n  data?: InvalidPricingPlan;\n};\n/** @docsIgnore */\nexport type SetCustomSlugApplicationErrors = {\n  code?: 'SLUG_ALREADY_EXISTS';\n  description?: string;\n  data?: Record<string, any>;\n};\n/** @docsIgnore */\nexport type SetCustomSlugValidationErrors = {\n  ruleName?: 'SLUG_CONTAINS_ILLEGAL_CHARACTERS';\n};\n\nexport interface BaseEventMetadata {\n  /**\n   * App instance ID.\n   * @format GUID\n   */\n  instanceId?: string | null;\n  /**\n   * Event type.\n   * @maxLength 150\n   */\n  eventType?: string;\n  /** The identification type and identity data. */\n  identity?: IdentificationData;\n}\n\nexport interface EventMetadata extends BaseEventMetadata {\n  /** Event ID. With this ID you can easily spot duplicated events and ignore them. */\n  _id?: string;\n  /**\n   * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.\n   * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.\n   */\n  entityFqdn?: string;\n  /**\n   * Event action name, placed at the top level to make it easier for users to dispatch messages.\n   * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.\n   */\n  slug?: string;\n  /** ID of the entity associated with the event. */\n  entityId?: string;\n  /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */\n  eventTime?: Date | null;\n  /**\n   * Whether the event was triggered as a result of a privacy regulation application\n   * (for example, GDPR).\n   */\n  triggeredByAnonymizeRequest?: boolean | null;\n  /** If present, indicates the action that triggered the event. */\n  originatedFrom?: string | null;\n  /**\n   * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.\n   * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.\n   */\n  entityEventSequence?: string | null;\n}\n\nexport interface ServiceCreatedEnvelope {\n  entity: Service;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a service is created.\n * @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.SERVICES_READ\n * @webhook\n * @eventType wix.bookings.services.v2.service_created\n * @slug created\n */\nexport declare function onServiceCreated(\n  handler: (event: ServiceCreatedEnvelope) => void | Promise<void>\n): void;\n\nexport interface ServiceDeletedEnvelope {\n  entity: Service;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a service is deleted.\n * @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.SERVICES_READ\n * @webhook\n * @eventType wix.bookings.services.v2.service_deleted\n * @slug deleted\n */\nexport declare function onServiceDeleted(\n  handler: (event: ServiceDeletedEnvelope) => void | Promise<void>\n): void;\n\nexport interface ServiceUpdatedEnvelope {\n  entity: Service;\n  metadata: EventMetadata;\n}\n\n/**\n * Triggered when a service is updated.\n * @permissionScope Manage Stores\n * @permissionScopeId SCOPE.STORES.MANAGE-STORES\n * @permissionScope Read Bookings - Public Data\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC\n * @permissionScope Manage Bookings Services and Settings\n * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION\n * @permissionScope Manage Bookings\n * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS\n * @permissionScope Read Bookings - Including Participants\n * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE\n * @permissionScope Read Bookings - all read permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS\n * @permissionScope Manage Bookings - all permissions\n * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS\n * @permissionId BOOKINGS.SERVICES_READ\n * @webhook\n * @eventType wix.bookings.services.v2.service_updated\n * @slug updated\n */\nexport declare function onServiceUpdated(\n  handler: (event: ServiceUpdatedEnvelope) => void | Promise<void>\n): void;\n\n/**\n * Create a new AddOns group.\n * An AddOns group defines a collection of AddOns that can be linked to a Service,\n * with constraints such as minimum and maximum selections.\n * @param addOnGroup - AddOnGroup to create.\n * @public\n * @documentationMaturity preview\n * @requiredField addOnGroup\n * @requiredField addOnGroup.name\n * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUP_CREATE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.AddOnGroupsService.CreateAddOnGroup\n */\nexport async function createAddOnGroup(\n  addOnGroup: NonNullablePaths<AddOnGroup, `name`, 2>,\n  options?: CreateAddOnGroupOptions\n): Promise<\n  NonNullablePaths<CreateAddOnGroupResponse, `addOnGroup.addOnIds`, 3>\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    addOnGroup: addOnGroup,\n    serviceId: options?.serviceId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.createAddOnGroup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          addOnGroup: '$[0]',\n          serviceId: '$[1].serviceId',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['addOnGroup', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CreateAddOnGroupOptions {\n  /**\n   * ID of the service to create the AddOnGroup for.\n   * @format GUID\n   */\n  serviceId?: string | null;\n}\n\n/**\n * Delete an existing AddOns group.\n * This will remove the group and unlink all associated AddOns.\n * @param addOnGroupId - ID of the AddOnGroup to delete.\n * @public\n * @documentationMaturity preview\n * @requiredField addOnGroupId\n * @requiredField options\n * @requiredField options.serviceId\n * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUP_CREATE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.AddOnGroupsService.DeleteAddOnGroup\n */\nexport async function deleteAddOnGroup(\n  addOnGroupId: string,\n  options: NonNullablePaths<DeleteAddOnGroupOptions, `serviceId`, 2>\n): Promise<\n  void & {\n    __applicationErrorsType?: DeleteAddOnGroupApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    addOnGroupId: addOnGroupId,\n    serviceId: options?.serviceId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.deleteAddOnGroup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          addOnGroupId: '$[0]',\n          serviceId: '$[1].serviceId',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['addOnGroupId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DeleteAddOnGroupOptions {\n  /**\n   * ID of the service from which to delete the AddOnGroup.\n   * @format GUID\n   */\n  serviceId: string | null;\n}\n\n/**\n * Update an existing AddOns group.\n * This allows modifying group settings such as its name, prompt, constraints, or associated AddOns.\n * @param addOnGroup - AddOnGroup to update.\n * @public\n * @documentationMaturity preview\n * @requiredField addOnGroup\n * @requiredField addOnGroup._id\n * @requiredField options\n * @requiredField options.serviceId\n * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUP_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.AddOnGroupsService.UpdateAddOnGroup\n */\nexport async function updateAddOnGroup(\n  addOnGroup: NonNullablePaths<AddOnGroup, `_id`, 2>,\n  options: NonNullablePaths<UpdateAddOnGroupOptions, `serviceId`, 2>\n): Promise<\n  NonNullablePaths<UpdateAddOnGroupResponse, `addOnGroup.addOnIds`, 3> & {\n    __applicationErrorsType?: UpdateAddOnGroupApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    addOnGroup: addOnGroup,\n    serviceId: options?.serviceId,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.updateAddOnGroup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          addOnGroup: '$[0]',\n          serviceId: '$[1].serviceId',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['addOnGroup', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateAddOnGroupOptions {\n  /**\n   * ID of the service that contains the AddOnGroup.\n   * @format GUID\n   */\n  serviceId: string | null;\n}\n\n/**\n * Retrieves a list of AddOnGroups including enriched AddOn details in the correct order.\n * If the group_id is specified, only the AddOns for the specified group will be returned,\n * otherwise all groups will be returned.\n * @param serviceId - ID of the service to retrieve AddOnGroups for.\n * @public\n * @documentationMaturity preview\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUPS_LIST\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.AddOnGroupsService.ListAddOnGroupsByServiceId\n */\nexport async function listAddOnGroupsByServiceId(\n  serviceId: string,\n  options?: ListAddOnGroupsByServiceIdOptions\n): Promise<\n  NonNullablePaths<ListAddOnGroupsByServiceIdResponse, `addOnGroupsDetails`, 2>\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    groupIds: options?.groupIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.listAddOnGroupsByServiceId(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceId: '$[0]',\n          groupIds: '$[1].groupIds',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ListAddOnGroupsByServiceIdOptions {\n  /**\n   * List of group ids to return. If not provided, all groups are returned.\n   * @format GUID\n   * @maxSize 3\n   */\n  groupIds?: string[] | null;\n}\n\n/**\n * Sets the AddOns for a specific group.\n * The order of the AddOns in the list will be used to determine their display order.\n * @param serviceId - The service ID to set AddOns for.\n * @public\n * @documentationMaturity preview\n * @requiredField options\n * @requiredField options.addOnIds\n * @requiredField options.groupId\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_ADD_ON_GROUPS_SET_ADD_ONS\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.AddOnGroupsService.SetAddOnsForGroup\n */\nexport async function setAddOnsForGroup(\n  serviceId: string,\n  options: NonNullablePaths<SetAddOnsForGroupOptions, `addOnIds` | `groupId`, 2>\n): Promise<\n  NonNullablePaths<SetAddOnsForGroupResponse, `addOnGroup.addOnIds`, 3> & {\n    __applicationErrorsType?: SetAddOnsForGroupApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    groupId: options?.groupId,\n    addOnIds: options?.addOnIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.setAddOnsForGroup(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceId: '$[0]',\n          groupId: '$[1].groupId',\n          addOnIds: '$[1].addOnIds',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface SetAddOnsForGroupOptions {\n  /**\n   * The group ID to set AddOns for.\n   * @format GUID\n   */\n  groupId: string | null;\n  /**\n   * The IDs of AddOns to set.\n   * @format GUID\n   * @minSize 1\n   * @maxSize 7\n   */\n  addOnIds: string[] | null;\n}\n\n/**\n * Creates a service.\n *\n *\n * ## Required fields\n *\n * When creating a service you must specify the following fields:\n * - `type`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)):\n * Whether it's an appointment-based service, class, or course.\n * - `name`: Service name that's displayed to customers.\n * - `onlineBooking`:\n * Settings determining whether customers can book online, whether the business\n * must manually confirm bookings, and whether customers can request to book an\n * appointment time slot that already has a booking request awaiting business\n * confirmation.\n * - `payment`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)):\n * How customers can pay when signing up for the service.\n *\n * ### Session durations\n *\n * Depending on which type of service you're creating, you may also need to specify\n * supported session durations.\n *\n * __Classes and courses__\n *\n * Don't specify `schedule.availabilityConstraints.sessionDurations`.\n *\n * __Appointment-based services without varied pricing based on session length__\n *\n * Specify the single supported session duration in the\n * `schedule.availabilityConstraints.sessionDurations` array.\n *\n * __Appointment-based services with varied pricing based on session length__\n *\n * - Specify all supported session durations in `schedule.availabilityConstraints.sessionDurations`.\n * - Note that Wix Bookings doesn't display these values to customers and\n * ignores them in pricing and availability calculations. Instead session\n * durations are retrieved from the relevant service *variants*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n * - It is mandatory to specify `schedule.availabilityConstraints.sessionDurations`,\n * even though these values are ignored.\n * @param service - Service to create.\n * @public\n * @requiredField service\n * @permissionId BOOKINGS.SERVICES_CREATE\n * @applicableIdentity APP\n * @returns Created service.\n * @fqn wix.bookings.services.v2.ServicesService.CreateService\n */\nexport async function createService(service: Service): Promise<\n  NonNullablePaths<\n    Service,\n    | `type`\n    | `media.items`\n    | `category._id`\n    | `form._id`\n    | `payment.fixed.price.value`\n    | `payment.fixed.price.currency`\n    | `payment.rateType`\n    | `payment.pricingPlanIds`\n    | `locations`\n    | `locations.${number}.business._id`\n    | `locations.${number}.business.name`\n    | `locations.${number}.custom._id`\n    | `locations.${number}._id`\n    | `locations.${number}.type`\n    | `bookingPolicy._id`\n    | `bookingPolicy.customPolicyDescription.enabled`\n    | `bookingPolicy.customPolicyDescription.description`\n    | `bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `bookingPolicy.limitLateBookingPolicy.enabled`\n    | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookingPolicy.bookAfterStartPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `bookingPolicy.reschedulePolicy.enabled`\n    | `bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `bookingPolicy.waitlistPolicy.enabled`\n    | `bookingPolicy.waitlistPolicy.capacity`\n    | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `bookingPolicy.resourcesPolicy.enabled`\n    | `bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `bookingPolicy.cancellationFeePolicy.enabled`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `bookingPolicy.saveCreditCardPolicy.enabled`\n    | `schedule.availabilityConstraints.durations`\n    | `schedule.availabilityConstraints.durations.${number}.minutes`\n    | `schedule.availabilityConstraints.sessionDurations`\n    | `schedule.availabilityConstraints.timeBetweenSessions`\n    | `staffMemberIds`\n    | `serviceResources`\n    | `supportedSlugs`\n    | `supportedSlugs.${number}.name`\n    | `seoData.tags`\n    | `seoData.tags.${number}.type`\n    | `seoData.tags.${number}.children`\n    | `seoData.tags.${number}.custom`\n    | `seoData.tags.${number}.disabled`\n    | `seoData.settings.preventAutoRedirect`\n    | `seoData.settings.keywords`\n    | `seoData.settings.keywords.${number}.term`\n    | `seoData.settings.keywords.${number}.isMain`,\n    6\n  > & {\n    __validationErrorsType?: CreateServiceValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({ service: service }),\n    [\n      {\n        transformFn: transformSDKImageToRESTImage,\n        paths: [\n          { path: 'service.media.items.image' },\n          { path: 'service.media.mainMedia.image' },\n          { path: 'service.media.coverMedia.image' },\n          { path: 'service.staffMembers.mainMedia.image' },\n          { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n        ],\n      },\n      {\n        transformFn: transformSDKAddressToRESTAddress,\n        paths: [\n          { path: 'service.locations.calculatedAddress' },\n          { path: 'service.locations.business.address' },\n          { path: 'service.locations.custom.address' },\n        ],\n      },\n      {\n        transformFn: transformSDKPageURLV2ToRESTPageURLV2,\n        paths: [\n          { path: 'service.urls.servicePage' },\n          { path: 'service.urls.bookingPage' },\n          { path: 'service.urls.calendarPage' },\n        ],\n      },\n    ]\n  );\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.createService(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )?.service!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { service: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['service']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Creates multiple services.\n *\n *\n * See *Create Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service))\n * for more details.\n * @param services - Services to create.\n * @public\n * @requiredField services\n * @permissionId BOOKINGS.SERVICES_CREATE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.BulkCreateServices\n */\nexport async function bulkCreateServices(\n  services: Service[],\n  options?: BulkCreateServicesOptions\n): Promise<\n  NonNullablePaths<\n    BulkCreateServicesResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `results.${number}.item.type`\n    | `results.${number}.item.category._id`\n    | `results.${number}.item.form._id`\n    | `results.${number}.item.payment.rateType`\n    | `results.${number}.item.bookingPolicy._id`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({\n      services: services,\n      returnEntity: options?.returnEntity,\n    }),\n    [\n      {\n        transformFn: transformSDKImageToRESTImage,\n        paths: [\n          { path: 'services.media.items.image' },\n          { path: 'services.media.mainMedia.image' },\n          { path: 'services.media.coverMedia.image' },\n          { path: 'services.staffMembers.mainMedia.image' },\n          { path: 'services.staffMemberDetails.staffMembers.mainMedia.image' },\n        ],\n      },\n      {\n        transformFn: transformSDKAddressToRESTAddress,\n        paths: [\n          { path: 'services.locations.calculatedAddress' },\n          { path: 'services.locations.business.address' },\n          { path: 'services.locations.custom.address' },\n        ],\n      },\n      {\n        transformFn: transformSDKPageURLV2ToRESTPageURLV2,\n        paths: [\n          { path: 'services.urls.servicePage' },\n          { path: 'services.urls.bookingPage' },\n          { path: 'services.urls.calendarPage' },\n        ],\n      },\n    ]\n  );\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.bulkCreateServices(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'results.item.media.items.image' },\n            { path: 'results.item.media.mainMedia.image' },\n            { path: 'results.item.media.coverMedia.image' },\n            { path: 'results.item.staffMembers.mainMedia.image' },\n            {\n              path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image',\n            },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'results.item.locations.calculatedAddress' },\n            { path: 'results.item.locations.business.address' },\n            { path: 'results.item.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'results.item.urls.servicePage' },\n            { path: 'results.item.urls.bookingPage' },\n            { path: 'results.item.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          services: '$[0]',\n          returnEntity: '$[1].returnEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['services', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkCreateServicesOptions {\n  /** Whether to return the created service objects. */\n  returnEntity?: boolean;\n}\n\n/**\n * Retrieves a service.\n * @param serviceId - ID of the service to retrieve.\n * @public\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_READ\n * @applicableIdentity APP\n * @returns Retrieved service.\n * @fqn wix.bookings.services.v2.ServicesService.GetService\n */\nexport async function getService(\n  serviceId: string\n): Promise<\n  NonNullablePaths<\n    Service,\n    | `type`\n    | `media.items`\n    | `category._id`\n    | `form._id`\n    | `payment.fixed.price.value`\n    | `payment.fixed.price.currency`\n    | `payment.rateType`\n    | `payment.pricingPlanIds`\n    | `locations`\n    | `locations.${number}.business._id`\n    | `locations.${number}.business.name`\n    | `locations.${number}.custom._id`\n    | `locations.${number}._id`\n    | `locations.${number}.type`\n    | `bookingPolicy._id`\n    | `bookingPolicy.customPolicyDescription.enabled`\n    | `bookingPolicy.customPolicyDescription.description`\n    | `bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `bookingPolicy.limitLateBookingPolicy.enabled`\n    | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookingPolicy.bookAfterStartPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `bookingPolicy.reschedulePolicy.enabled`\n    | `bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `bookingPolicy.waitlistPolicy.enabled`\n    | `bookingPolicy.waitlistPolicy.capacity`\n    | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `bookingPolicy.resourcesPolicy.enabled`\n    | `bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `bookingPolicy.cancellationFeePolicy.enabled`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `bookingPolicy.saveCreditCardPolicy.enabled`\n    | `schedule.availabilityConstraints.durations`\n    | `schedule.availabilityConstraints.durations.${number}.minutes`\n    | `schedule.availabilityConstraints.sessionDurations`\n    | `schedule.availabilityConstraints.timeBetweenSessions`\n    | `staffMemberIds`\n    | `serviceResources`\n    | `supportedSlugs`\n    | `supportedSlugs.${number}.name`\n    | `seoData.tags`\n    | `seoData.tags.${number}.type`\n    | `seoData.tags.${number}.children`\n    | `seoData.tags.${number}.custom`\n    | `seoData.tags.${number}.disabled`\n    | `seoData.settings.preventAutoRedirect`\n    | `seoData.settings.keywords`\n    | `seoData.settings.keywords.${number}.term`\n    | `seoData.settings.keywords.${number}.isMain`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n  });\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.getService(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )?.service!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { serviceId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Updates a service.\n *\n *\n * Each time the service is updated, `revision` increments by 1. You must\n * include the number of the existing revision when updating the service.\n * This ensures you're working with the latest service information and\n * prevents unintended overwrites.\n *\n * ## Session durations\n *\n * Specify `schedule.availabilityConstraints.sessionDurations`\n * only if you want to update it for appointment-based services without varied\n * pricing based on session length. Don't specify `schedule.availabilityConstraints.sessionDurations`\n * for all other appointment-based services, classes, or courses. See *Create Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service#session-durations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service#session-durations))\n * for more details.\n * @param _id - Service ID.\n * @public\n * @requiredField _id\n * @requiredField service\n * @requiredField service.revision\n * @permissionId BOOKINGS.SERVICES_UPDATE\n * @applicableIdentity APP\n * @returns Updated service.\n * @fqn wix.bookings.services.v2.ServicesService.UpdateService\n */\nexport async function updateService(\n  _id: string,\n  service: NonNullablePaths<UpdateService, `revision`, 2>\n): Promise<\n  NonNullablePaths<\n    Service,\n    | `type`\n    | `media.items`\n    | `category._id`\n    | `form._id`\n    | `payment.fixed.price.value`\n    | `payment.fixed.price.currency`\n    | `payment.rateType`\n    | `payment.pricingPlanIds`\n    | `locations`\n    | `locations.${number}.business._id`\n    | `locations.${number}.business.name`\n    | `locations.${number}.custom._id`\n    | `locations.${number}._id`\n    | `locations.${number}.type`\n    | `bookingPolicy._id`\n    | `bookingPolicy.customPolicyDescription.enabled`\n    | `bookingPolicy.customPolicyDescription.description`\n    | `bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `bookingPolicy.limitLateBookingPolicy.enabled`\n    | `bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookingPolicy.bookAfterStartPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.enabled`\n    | `bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `bookingPolicy.reschedulePolicy.enabled`\n    | `bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `bookingPolicy.waitlistPolicy.enabled`\n    | `bookingPolicy.waitlistPolicy.capacity`\n    | `bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `bookingPolicy.resourcesPolicy.enabled`\n    | `bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `bookingPolicy.cancellationFeePolicy.enabled`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `bookingPolicy.saveCreditCardPolicy.enabled`\n    | `schedule.availabilityConstraints.durations`\n    | `schedule.availabilityConstraints.durations.${number}.minutes`\n    | `schedule.availabilityConstraints.sessionDurations`\n    | `schedule.availabilityConstraints.timeBetweenSessions`\n    | `staffMemberIds`\n    | `serviceResources`\n    | `supportedSlugs`\n    | `supportedSlugs.${number}.name`\n    | `seoData.tags`\n    | `seoData.tags.${number}.type`\n    | `seoData.tags.${number}.children`\n    | `seoData.tags.${number}.custom`\n    | `seoData.tags.${number}.disabled`\n    | `seoData.settings.preventAutoRedirect`\n    | `seoData.settings.keywords`\n    | `seoData.settings.keywords.${number}.term`\n    | `seoData.settings.keywords.${number}.isMain`,\n    6\n  > & {\n    __validationErrorsType?: UpdateServiceValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({ service: { ...service, id: _id } }),\n    [\n      {\n        transformFn: transformSDKImageToRESTImage,\n        paths: [\n          { path: 'service.media.items.image' },\n          { path: 'service.media.mainMedia.image' },\n          { path: 'service.media.coverMedia.image' },\n          { path: 'service.staffMembers.mainMedia.image' },\n          { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n        ],\n      },\n      {\n        transformFn: transformSDKAddressToRESTAddress,\n        paths: [\n          { path: 'service.locations.calculatedAddress' },\n          { path: 'service.locations.business.address' },\n          { path: 'service.locations.custom.address' },\n        ],\n      },\n      {\n        transformFn: transformSDKPageURLV2ToRESTPageURLV2,\n        paths: [\n          { path: 'service.urls.servicePage' },\n          { path: 'service.urls.bookingPage' },\n          { path: 'service.urls.calendarPage' },\n        ],\n      },\n    ]\n  );\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.updateService(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )?.service!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: { service: '$[1]' },\n        explicitPathsToArguments: { 'service.id': '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['_id', 'service']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface UpdateService {\n  /**\n   * Service ID.\n   * @format GUID\n   * @readonly\n   */\n  _id?: string | null;\n  /**\n   * Service type.\n   * Learn more about *service types*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)).\n   */\n  type?: ServiceTypeWithLiterals;\n  /**\n   * Order of the service within a *category*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object)).\n   */\n  sortOrder?: number | null;\n  /**\n   * Service name.\n   * @maxLength 400\n   * @minLength 1\n   */\n  name?: string | null;\n  /**\n   * Service description. For example, `High-class hair styling, cuts, straightening and color`.\n   * @maxLength 7000\n   */\n  description?: string | null;\n  /**\n   * Short service description, such as `Hair styling`.\n   * @maxLength 6000\n   */\n  tagLine?: string | null;\n  /**\n   * Default maximum number of customers that can book the service. The service cannot be booked beyond this capacity.\n   * @min 1\n   * @max 1000\n   */\n  defaultCapacity?: number | null;\n  /** Media associated with the service. */\n  media?: Media;\n  /** Whether the service is hidden from Wix Bookings pages and widgets. */\n  hidden?: boolean | null;\n  /**\n   * _Category_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v1/category-object))\n   * the service is associated with.\n   */\n  category?: V2Category;\n  /** Form the customer filled out when booking the service. */\n  form?: Form;\n  /**\n   * Payment options for booking the service.\n   * Learn more about *service payments*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)).\n   */\n  payment?: Payment;\n  /** Online booking settings. */\n  onlineBooking?: OnlineBooking;\n  /** Conferencing options for the service. */\n  conferencing?: Conferencing;\n  /**\n   * The locations this service is offered at. Read more about *service locations*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-locations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-locations)).\n   * @immutable\n   * @maxSize 500\n   */\n  locations?: Location[];\n  /**\n   * _Policy_\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction))\n   * determining under what conditions this service can be booked. For example, whether the service can only be booked up to 30 minutes before it begins.\n   */\n  bookingPolicy?: BookingPolicy;\n  /**\n   * The service's *schedule*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction)),\n   * which can be used to manage the service's *events*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction)).\n   */\n  schedule?: Schedule;\n  /**\n   * IDs of the *staff members*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))\n   * providing the service. Available only for appointment-based services.\n   * @maxSize 220\n   * @format GUID\n   */\n  staffMemberIds?: string[];\n  /**\n   * Information about which resources must be available so customers can book the service.\n   * For example, a meeting room or equipment.\n   * @maxSize 3\n   */\n  serviceResources?: ServiceResource[];\n  /**\n   * A slug is the last part of the URL address that serves as a unique identifier of the service.\n   * The list of supported slugs includes past service names for backwards compatibility, and a custom slug if one was set by the business owner.\n   * @readonly\n   * @maxSize 100\n   */\n  supportedSlugs?: Slug[];\n  /**\n   * Active slug for the service.\n   * Learn more about *service slugs*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-slugs) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-slugs)).\n   * @readonly\n   */\n  mainSlug?: Slug;\n  /**\n   * URLs to various service-related pages, such as the calendar page and the booking page.\n   * @readonly\n   */\n  urls?: URLs;\n  /** Extensions enabling users to save custom data related to the service. */\n  extendedFields?: ExtendedFields;\n  /** Custom SEO data for the service. */\n  seoData?: SeoSchema;\n  /**\n   * Date and time the service was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _createdDate?: Date | null;\n  /**\n   * Date and time the service was updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.\n   * @readonly\n   */\n  _updatedDate?: Date | null;\n  /**\n   * Revision number, which increments by 1 each time the service is updated. To\n   * prevent conflicting changes, the existing revision must be used when updating\n   * a service.\n   * @readonly\n   */\n  revision?: string | null;\n}\n\n/**\n * Updates up to 100 services.\n *\n *\n * See *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * for more details.\n *\n * The call succeeds even if one or more individual services can't be updated.\n * Information about failures is returned in `bulkActionMetadata`.\n * @public\n * @requiredField options.services.service\n * @requiredField options.services.service._id\n * @requiredField options.services.service.revision\n * @permissionId BOOKINGS.SERVICES_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.BulkUpdateServices\n */\nexport async function bulkUpdateServices(\n  options?: NonNullablePaths<\n    BulkUpdateServicesOptions,\n    | `services.${number}.service`\n    | `services.${number}.service._id`\n    | `services.${number}.service.revision`,\n    5\n  >\n): Promise<\n  NonNullablePaths<\n    BulkUpdateServicesResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `results.${number}.item.type`\n    | `results.${number}.item.category._id`\n    | `results.${number}.item.form._id`\n    | `results.${number}.item.payment.rateType`\n    | `results.${number}.item.bookingPolicy._id`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({\n      services: options?.services,\n      returnEntity: options?.returnEntity,\n    }),\n    [\n      {\n        transformFn: transformSDKImageToRESTImage,\n        paths: [\n          { path: 'services.service.media.items.image' },\n          { path: 'services.service.media.mainMedia.image' },\n          { path: 'services.service.media.coverMedia.image' },\n          { path: 'services.service.staffMembers.mainMedia.image' },\n          {\n            path: 'services.service.staffMemberDetails.staffMembers.mainMedia.image',\n          },\n        ],\n      },\n      {\n        transformFn: transformSDKAddressToRESTAddress,\n        paths: [\n          { path: 'services.service.locations.calculatedAddress' },\n          { path: 'services.service.locations.business.address' },\n          { path: 'services.service.locations.custom.address' },\n        ],\n      },\n      {\n        transformFn: transformSDKPageURLV2ToRESTPageURLV2,\n        paths: [\n          { path: 'services.service.urls.servicePage' },\n          { path: 'services.service.urls.bookingPage' },\n          { path: 'services.service.urls.calendarPage' },\n        ],\n      },\n    ]\n  );\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.bulkUpdateServices(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'results.item.media.items.image' },\n            { path: 'results.item.media.mainMedia.image' },\n            { path: 'results.item.media.coverMedia.image' },\n            { path: 'results.item.staffMembers.mainMedia.image' },\n            {\n              path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image',\n            },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'results.item.locations.calculatedAddress' },\n            { path: 'results.item.locations.business.address' },\n            { path: 'results.item.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'results.item.urls.servicePage' },\n            { path: 'results.item.urls.bookingPage' },\n            { path: 'results.item.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          services: '$[0].services',\n          returnEntity: '$[0].returnEntity',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkUpdateServicesOptions {\n  /**\n   * Services to update.\n   * @maxSize 100\n   */\n  services?: MaskedService[];\n  /** Whether to include the updated services in the response. Default: `false` */\n  returnEntity?: boolean;\n}\n\n/**\n * Updates multiple services by filter.\n *\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * See *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * for more details about updating a service.\n *\n * The call succeeds even if one or more individual services can't be updated.\n * Information about failures is returned in `bulkActionMetadata`.\n * @param filter - Filter to identify the services to update.\n * @public\n * @requiredField filter\n * @requiredField options\n * @requiredField options.service\n * @permissionId BOOKINGS.SERVICES_UPDATE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.BulkUpdateServicesByFilter\n */\nexport async function bulkUpdateServicesByFilter(\n  filter: Record<string, any>,\n  options: NonNullablePaths<BulkUpdateServicesByFilterOptions, `service`, 2>\n): Promise<NonNullablePaths<BulkUpdateServicesByFilterResponse, `jobId`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({\n      filter: filter,\n      service: options?.service,\n    }),\n    [\n      {\n        transformFn: transformSDKImageToRESTImage,\n        paths: [\n          { path: 'service.media.items.image' },\n          { path: 'service.media.mainMedia.image' },\n          { path: 'service.media.coverMedia.image' },\n          { path: 'service.staffMembers.mainMedia.image' },\n          { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n        ],\n      },\n      {\n        transformFn: transformSDKAddressToRESTAddress,\n        paths: [\n          { path: 'service.locations.calculatedAddress' },\n          { path: 'service.locations.business.address' },\n          { path: 'service.locations.custom.address' },\n        ],\n      },\n      {\n        transformFn: transformSDKPageURLV2ToRESTPageURLV2,\n        paths: [\n          { path: 'service.urls.servicePage' },\n          { path: 'service.urls.bookingPage' },\n          { path: 'service.urls.calendarPage' },\n        ],\n      },\n    ]\n  );\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.bulkUpdateServicesByFilter(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0]', service: '$[1].service' },\n        singleArgumentUnchanged: false,\n      },\n      ['filter', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkUpdateServicesByFilterOptions {\n  /** Service to update. */\n  service: Service;\n}\n\n/**\n * Deletes a service.\n *\n *\n * Specify `{\"preserveFutureSessionsWithParticipants\": true}` to retain all\n * future sessions for the service. By default, all future sessions are canceled.\n * @param serviceId - ID of the service to delete.\n * @public\n * @requiredField serviceId\n * @param options - Allows you to configure how to handle the deleted service's future sessions and how to notify the sessions participants.\n * @permissionId BOOKINGS.SERVICES_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.DeleteService\n */\nexport async function deleteService(\n  serviceId: string,\n  options?: DeleteServiceOptions\n): Promise<void> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    preserveFutureSessionsWithParticipants:\n      options?.preserveFutureSessionsWithParticipants,\n    participantNotification: options?.participantNotification,\n  });\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.deleteService(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceId: '$[0]',\n          preserveFutureSessionsWithParticipants:\n            '$[1].preserveFutureSessionsWithParticipants',\n          participantNotification: '$[1].participantNotification',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DeleteServiceOptions {\n  /**\n   * Whether to preserve future sessions with participants.\n   *\n   * Default: `false`\n   */\n  preserveFutureSessionsWithParticipants?: boolean;\n  /**\n   * Whether to notify participants about the change and an optional\n   * custom message.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\n/**\n * Deletes multiple services.\n *\n *\n * See *Delete Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))\n * for more details about deleting a service.\n *\n * The call succeeds even if one or more individual services can't be deleted.\n * Information about failures is returned in `bulkActionMetadata`.\n * @param ids - IDs of the services to delete.\n * @public\n * @requiredField ids\n * @permissionId BOOKINGS.SERVICES_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.BulkDeleteServices\n */\nexport async function bulkDeleteServices(\n  ids: string[],\n  options?: BulkDeleteServicesOptions\n): Promise<\n  NonNullablePaths<\n    BulkDeleteServicesResponse,\n    | `results`\n    | `results.${number}.itemMetadata.originalIndex`\n    | `results.${number}.itemMetadata.success`\n    | `results.${number}.itemMetadata.error.code`\n    | `results.${number}.itemMetadata.error.description`\n    | `results.${number}.item.type`\n    | `results.${number}.item.category._id`\n    | `results.${number}.item.form._id`\n    | `results.${number}.item.payment.rateType`\n    | `results.${number}.item.bookingPolicy._id`\n    | `bulkActionMetadata.totalSuccesses`\n    | `bulkActionMetadata.totalFailures`\n    | `bulkActionMetadata.undetailedFailures`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    ids: ids,\n    preserveFutureSessionsWithParticipants:\n      options?.preserveFutureSessionsWithParticipants,\n    participantNotification: options?.participantNotification,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.bulkDeleteServices(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'results.item.media.items.image' },\n            { path: 'results.item.media.mainMedia.image' },\n            { path: 'results.item.media.coverMedia.image' },\n            { path: 'results.item.staffMembers.mainMedia.image' },\n            {\n              path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image',\n            },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'results.item.locations.calculatedAddress' },\n            { path: 'results.item.locations.business.address' },\n            { path: 'results.item.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'results.item.urls.servicePage' },\n            { path: 'results.item.urls.bookingPage' },\n            { path: 'results.item.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          ids: '$[0]',\n          preserveFutureSessionsWithParticipants:\n            '$[1].preserveFutureSessionsWithParticipants',\n          participantNotification: '$[1].participantNotification',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['ids', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkDeleteServicesOptions {\n  /**\n   * Whether to preserve future sessions with participants.\n   *\n   * Default: `false`.\n   */\n  preserveFutureSessionsWithParticipants?: boolean;\n  /**\n   * Whether to notify participants about the change and an optional\n   * custom message.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\n/**\n * Deletes multiple services by filter.\n *\n *\n * See *Delete Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))\n * for more details about deleting a service.\n *\n * The call succeeds even if one or more individual services can't be deleted.\n * Information about failures is returned in `bulkActionMetadata`.\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).\n * @param filter - Filter to identify the services that need to be deleted.\n * @public\n * @requiredField filter\n * @permissionId BOOKINGS.SERVICES_DELETE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.BulkDeleteServicesByFilter\n */\nexport async function bulkDeleteServicesByFilter(\n  filter: Record<string, any>,\n  options?: BulkDeleteServicesByFilterOptions\n): Promise<NonNullablePaths<BulkDeleteServicesByFilterResponse, `jobId`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: filter,\n    preserveFutureSessionsWithParticipants:\n      options?.preserveFutureSessionsWithParticipants,\n    participantNotification: options?.participantNotification,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.bulkDeleteServicesByFilter(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          filter: '$[0]',\n          preserveFutureSessionsWithParticipants:\n            '$[1].preserveFutureSessionsWithParticipants',\n          participantNotification: '$[1].participantNotification',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['filter', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface BulkDeleteServicesByFilterOptions {\n  /**\n   * Whether to preserve future sessions with participants.\n   *\n   * Default: `false`.\n   */\n  preserveFutureSessionsWithParticipants?: boolean;\n  /** Whether to notify participants about the change and an optional custom message. */\n  participantNotification?: ParticipantNotification;\n}\n\n/**\n * Creates a query to retrieve a list of `service` objects.\n *\n * The `queryServices()` function builds a query to retrieve a list of `service` objects and returns a `ServicesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-builder/find) function.\n *\n * You can refine the query by chaining `ServicesQueryBuilder` functions onto the query. `ServicesQueryBuilder` functions enable you to sort, filter, and control the results that `queryServices()` returns.\n *\n * `queryServices()` runs with the following `ServicesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `100`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryServices()` are applied in the order they are called. For example, if you apply `ascending(\"category.name\")` and then `ascending(\"name\")`, the results are sorted first by `category.name`, and then, if there are multiple results with the same `category.name`, the items are sorted by `name`.\n *\n * The following `ServicesQueryBuilder` functions are supported for the `queryServices()` function. For a full description of the `service` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-result/items) property in `ServicesQueryResult`.\n * @public\n * @permissionId BOOKINGS.SERVICES_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.QueryServices\n */\nexport function queryServices(): ServicesQueryBuilder {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[0] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  return queryBuilder<\n    Service,\n    'OFFSET',\n    QueryServicesRequest,\n    QueryServicesResponse\n  >({\n    func: async (payload: QueryServicesRequest) => {\n      const reqOpts =\n        ambassadorWixBookingsServicesV2Service.queryServices(payload);\n\n      sideEffects?.onSiteCall?.();\n      try {\n        const result = await httpClient.request(reqOpts);\n        sideEffects?.onSuccess?.(result);\n        return result;\n      } catch (err) {\n        sideEffects?.onError?.(err);\n        throw err;\n      }\n    },\n    requestTransformer: (query: QueryServicesRequest['query']) => {\n      const args = [query, {}] as [QueryServicesRequest['query'], {}];\n      return renameKeysFromSDKRequestToRESTRequest({\n        ...args?.[1],\n        query: args?.[0],\n      });\n    },\n    responseTransformer: ({ data }: HttpResponse<QueryServicesResponse>) => {\n      const transformedData = renameKeysFromRESTResponseToSDKResponse(\n        transformPaths(data, [\n          {\n            transformFn: transformRESTImageToSDKImage,\n            paths: [\n              { path: 'services.media.items.image' },\n              { path: 'services.media.mainMedia.image' },\n              { path: 'services.media.coverMedia.image' },\n              { path: 'services.staffMembers.mainMedia.image' },\n              {\n                path: 'services.staffMemberDetails.staffMembers.mainMedia.image',\n              },\n            ],\n          },\n          {\n            transformFn: transformRESTAddressToSDKAddress,\n            paths: [\n              { path: 'services.locations.calculatedAddress' },\n              { path: 'services.locations.business.address' },\n              { path: 'services.locations.custom.address' },\n            ],\n          },\n          {\n            transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n            paths: [\n              { path: 'services.urls.servicePage' },\n              { path: 'services.urls.bookingPage' },\n              { path: 'services.urls.calendarPage' },\n            ],\n          },\n        ])\n      );\n\n      return {\n        items: transformedData?.services,\n        pagingMetadata: transformedData?.pagingMetadata,\n      };\n    },\n    errorTransformer: (err: unknown) => {\n      const transformedError = sdkTransformError(err, {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      });\n\n      throw transformedError;\n    },\n    pagingMethod: 'OFFSET',\n    transformationPaths: {},\n  });\n}\n\ninterface QueryOffsetResult {\n  currentPage: number | undefined;\n  totalPages: number | undefined;\n  totalCount: number | undefined;\n  hasNext: () => boolean;\n  hasPrev: () => boolean;\n  length: number;\n  pageSize: number;\n}\n\nexport interface ServicesQueryResult extends QueryOffsetResult {\n  items: Service[];\n  query: ServicesQueryBuilder;\n  next: () => Promise<ServicesQueryResult>;\n  prev: () => Promise<ServicesQueryResult>;\n}\n\nexport interface ServicesQueryBuilder {\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  eq: (\n    propertyName:\n      | '_id'\n      | 'type'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'hidden'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'payment.options.online'\n      | 'payment.options.inPerson'\n      | 'payment.options.pricingPlan'\n      | 'onlineBooking.enabled'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'staffMemberIds'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ne: (\n    propertyName:\n      | '_id'\n      | 'type'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'hidden'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'payment.options.online'\n      | 'payment.options.inPerson'\n      | 'payment.options.pricingPlan'\n      | 'onlineBooking.enabled'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'staffMemberIds'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  ge: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  gt: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  le: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `value`.\n   * @param value - Value to compare against.\n   */\n  lt: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `string`.\n   * @param string - String to compare against. Case-insensitive.\n   */\n  startsWith: (\n    propertyName:\n      | '_id'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'locations.business.id'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: string\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   */\n  hasSome: (\n    propertyName:\n      | '_id'\n      | 'type'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'hidden'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'payment.options.online'\n      | 'payment.options.inPerson'\n      | 'payment.options.pricingPlan'\n      | 'onlineBooking.enabled'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'staffMemberIds'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any[]\n  ) => ServicesQueryBuilder;\n  /** @param propertyName - Property whose value is compared with `values`.\n   * @param values - List of values to compare against.\n   */\n  hasAll: (\n    propertyName: 'staffMemberIds',\n    value: any[]\n  ) => ServicesQueryBuilder;\n  in: (\n    propertyName:\n      | '_id'\n      | 'type'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'hidden'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'payment.options.online'\n      | 'payment.options.inPerson'\n      | 'payment.options.pricingPlan'\n      | 'onlineBooking.enabled'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'staffMemberIds'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: any\n  ) => ServicesQueryBuilder;\n  exists: (\n    propertyName:\n      | '_id'\n      | 'type'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'hidden'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'payment.options.online'\n      | 'payment.options.inPerson'\n      | 'payment.options.pricingPlan'\n      | 'onlineBooking.enabled'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'staffMemberIds'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name',\n    value: boolean\n  ) => ServicesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  ascending: (\n    ...propertyNames: Array<\n      | '_id'\n      | 'appId'\n      | 'type'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'hidden'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'payment.options.online'\n      | 'payment.options.inPerson'\n      | 'payment.options.pricingPlan'\n      | 'onlineBooking.enabled'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'staffMemberIds'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name'\n    >\n  ) => ServicesQueryBuilder;\n  /** @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */\n  descending: (\n    ...propertyNames: Array<\n      | '_id'\n      | 'appId'\n      | 'type'\n      | 'name'\n      | 'description'\n      | 'tagLine'\n      | 'hidden'\n      | 'category.id'\n      | 'category.name'\n      | 'form.id'\n      | 'payment.options.online'\n      | 'payment.options.inPerson'\n      | 'payment.options.pricingPlan'\n      | 'onlineBooking.enabled'\n      | 'locations.business.id'\n      | 'schedule.firstSessionStart'\n      | 'schedule.lastSessionEnd'\n      | 'staffMemberIds'\n      | 'supportedSlugs.name'\n      | 'mainSlug.name'\n    >\n  ) => ServicesQueryBuilder;\n  /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */\n  limit: (limit: number) => ServicesQueryBuilder;\n  /** @param skip - Number of items to skip in the query results before returning the results. */\n  skip: (skip: number) => ServicesQueryBuilder;\n  find: () => Promise<ServicesQueryResult>;\n}\n\n/**\n * Retrieves a list of up to 100 services, given the provided filtering, paging,\n * and sorting.\n *\n *\n * ## Defaults\n *\n * Search Services has the following default settings, which you can override:\n * + Sorted by `createdDate` in ascending order.\n * + `paging.limit` set to `100`.\n * + `paging.offset` set to `0`.\n *\n * ## Filters\n *\n * When using filters for dates, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with *Search* methods, see\n * _API Query Language_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))\n * and *Sorting and Paging*\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).\n * @param search - Search criteria including filter, sort, aggregations, and paging options.\n *\n * Refer to the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)) for complete filter and sorting options.\n * @public\n * @documentationMaturity preview\n * @requiredField search\n * @permissionId BOOKINGS.SERVICES_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.SearchServices\n */\nexport async function searchServices(\n  search: ServiceSearch\n): Promise<\n  NonNullablePaths<\n    SearchServicesResponse,\n    | `services`\n    | `services.${number}.type`\n    | `services.${number}.category._id`\n    | `services.${number}.form._id`\n    | `services.${number}.payment.rateType`\n    | `services.${number}.bookingPolicy._id`\n    | `services.${number}.bookingPolicy.customPolicyDescription.enabled`\n    | `services.${number}.bookingPolicy.customPolicyDescription.description`\n    | `services.${number}.bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `services.${number}.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `services.${number}.bookingPolicy.limitLateBookingPolicy.enabled`\n    | `services.${number}.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `services.${number}.bookingPolicy.bookAfterStartPolicy.enabled`\n    | `services.${number}.bookingPolicy.cancellationPolicy.enabled`\n    | `services.${number}.bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `services.${number}.bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `services.${number}.bookingPolicy.reschedulePolicy.enabled`\n    | `services.${number}.bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `services.${number}.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `services.${number}.bookingPolicy.waitlistPolicy.enabled`\n    | `services.${number}.bookingPolicy.waitlistPolicy.capacity`\n    | `services.${number}.bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `services.${number}.bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `services.${number}.bookingPolicy.resourcesPolicy.enabled`\n    | `services.${number}.bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `services.${number}.bookingPolicy.cancellationFeePolicy.enabled`\n    | `services.${number}.bookingPolicy.saveCreditCardPolicy.enabled`\n    | `services.${number}.schedule.availabilityConstraints.timeBetweenSessions`\n    | `services.${number}.seoData.settings.preventAutoRedirect`\n    | `aggregationData.results`\n    | `aggregationData.results.${number}.scalar.type`\n    | `aggregationData.results.${number}.scalar.value`\n    | `aggregationData.results.${number}.name`\n    | `aggregationData.results.${number}.type`\n    | `aggregationData.results.${number}.fieldPath`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ search: search });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.searchServices(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'services.media.items.image' },\n            { path: 'services.media.mainMedia.image' },\n            { path: 'services.media.coverMedia.image' },\n            { path: 'services.staffMembers.mainMedia.image' },\n            {\n              path: 'services.staffMemberDetails.staffMembers.mainMedia.image',\n            },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'services.locations.calculatedAddress' },\n            { path: 'services.locations.business.address' },\n            { path: 'services.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'services.urls.servicePage' },\n            { path: 'services.urls.bookingPage' },\n            { path: 'services.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { search: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['search']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ServiceSearchSpec extends SearchSpec {\n  searchable: ['name'];\n  aggregatable: [\n    '_id',\n    'addOnDetails.addOnId',\n    'addOnDetails.durationInMinutes',\n    'appId',\n    'category._id',\n    'category.name',\n    'category.sortOrder',\n    'description',\n    'form._id',\n    'hidden',\n    'locations.business._id',\n    'locations.business.name',\n    'locations.type',\n    'mainSlug.name',\n    'name',\n    'onlineBooking.enabled',\n    'payment.options.inPerson',\n    'payment.options.online',\n    'payment.options.pricingPlan',\n    'staffMemberIds',\n    'supportedSlugs.name',\n    'tagLine',\n    'type'\n  ];\n  paging: 'cursor';\n  wql: [\n    {\n      operators: ['$hasAll', '$hasSome'];\n      fields: [\n        'locations.business._id',\n        'locations.business.name',\n        'locations.calculatedAddress.formattedAddress',\n        'locations.type',\n        'staffMemberIds',\n        'supportedSlugs.name'\n      ];\n      sort: 'BOTH';\n    },\n    {\n      operators: '*';\n      fields: [\n        '_id',\n        'addOnDetails.addOnId',\n        'addOnDetails.durationInMinutes',\n        'appId',\n        'category._id',\n        'category.name',\n        'category.sortOrder',\n        'description',\n        'form._id',\n        'hidden',\n        'mainSlug.name',\n        'name',\n        'onlineBooking.enabled',\n        'payment.options.inPerson',\n        'payment.options.online',\n        'payment.options.pricingPlan',\n        'tagLine',\n        'type'\n      ];\n      sort: 'BOTH';\n    }\n  ];\n}\n\nexport type CommonSearchWithEntityContext = SearchSdkType<\n  Service,\n  ServiceSearchSpec\n>;\nexport type ServiceSearch = {\n  /** \n  Cursor-based paging for result navigation. When requesting `cursor_paging.cursor`,\n  `filter`, `sort`, or `search` can't be specified.  \n  */\n  cursorPaging?: {\n    /** \n  Number of items to load. \n  @max: 100 \n  */\n    limit?:\n      | NonNullable<CommonSearchWithEntityContext['cursorPaging']>['limit']\n      | null;\n    /** \n  Pointer to the next or previous page in the list of results.\n\n  You can get the relevant cursor token\n  from the `pagingMetadata` object in the previous call's response.\n  Not relevant for the first request. \n  @maxLength: 16000 \n  */\n    cursor?:\n      | NonNullable<CommonSearchWithEntityContext['cursorPaging']>['cursor']\n      | null;\n  };\n  /** \n  Filter object for narrowing search results. For example, to return only services with specific payment options: `\"filter\": {\"payment.options.online\": true, \"payment.options.in_person\": false}`.\n\n  Learn more about the filter format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)).  \n  */\n  filter?: CommonSearchWithEntityContext['filter'] | null;\n  /** \n  Array of sort objects specifying result order. For example, to sort by creation date in descending order: `\"sort\": [{\"fieldName\": \"createdDate\", \"order\": \"DESC\"}]`.\n\n  Learn more about the sort format in the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting)). \n  @maxSize: 10 \n  */\n  sort?: {\n    /** \n  Name of the field to sort by. \n  @maxLength: 512 \n  */\n    fieldName?: NonNullable<\n      CommonSearchWithEntityContext['sort']\n    >[number]['fieldName'];\n    /** \n  Sort order.  \n  */\n    order?: NonNullable<CommonSearchWithEntityContext['sort']>[number]['order'];\n  }[];\n  /** \n  Aggregations for grouping data into categories (facets) and providing summaries for each category.\n  For example, use aggregations to categorize search results by service type, payment options, or locations. \n  @maxSize: 10 \n  */\n  aggregations?: {\n    /** \n  Value aggregation configuration.  \n  */\n    value?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['value'];\n    /** \n  Range aggregation configuration.  \n  */\n    range?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['range'];\n    /** \n  Scalar aggregation configuration.  \n  */\n    scalar?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['scalar'];\n    /** \n  Date histogram aggregation configuration.  \n  */\n    dateHistogram?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['dateHistogram'];\n    /** \n  Nested aggregation configuration.  \n  */\n    nested?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['nested'];\n    /** \n  User-defined name of aggregation. Must be unique and will appear in aggregation results. \n  @maxLength: 100 \n  */\n    name?:\n      | NonNullable<\n          CommonSearchWithEntityContext['aggregations']\n        >[number]['name']\n      | null;\n    /** \n  Type of aggregation. Client must specify matching aggregation field below.  \n  */\n    type?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['type'];\n    /** \n  Path to the field to aggregate by in dot notation. For example `name` or `paymentOptions.online`. \n  @maxLength: 200 \n  */\n    fieldPath?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['fieldPath'];\n    /** \n  Deprecated, use `nested` instead. \n  @deprecated: Deprecated, use `nested` instead.,\n  @replacedBy: kind.nested,\n  @targetRemovalDate: 2025-01-01 \n  */\n    groupBy?: NonNullable<\n      CommonSearchWithEntityContext['aggregations']\n    >[number]['groupBy'];\n  }[];\n  /** \n  Free text to match in searchable fields.  \n  */\n  search?: {\n    /** \n  Search mode. Defines the search logic for combining multiple terms in the `expression`.  \n  */\n    mode?: NonNullable<CommonSearchWithEntityContext['search']>['mode'];\n    /** \n  Search term or expression. \n  @maxLength: 200 \n  */\n    expression?:\n      | NonNullable<CommonSearchWithEntityContext['search']>['expression']\n      | null;\n    /** \n  Fields to search in. If the array is empty, all searchable fields are searched. Use dot notation to specify a JSON path. For example, `order.address.streetName`. \n  @maxSize: 10,\n  @maxLength: 200 \n  */\n    fields?: NonNullable<CommonSearchWithEntityContext['search']>['fields'];\n    /** \n  Whether to enable the search function to use an algorithm to automatically find results that are close to the search expression, such as typos and declensions.  \n  */\n    fuzzy?: NonNullable<CommonSearchWithEntityContext['search']>['fuzzy'];\n  };\n  /** \n  Time zone in [IANA tz database format](https://en.wikipedia.org/wiki/Tz_database) or [ISO 8601 UTC offset format](https://en.wikipedia.org/wiki/ISO_8601#Time_offsets_from_UTC) for adjusting time fields in the specified filters and returned aggregation data. For example, `America/New_York`, `UTC`, or `+02:00`.\n\n  Default: Time zone specified in the business [site properties](https://dev.wix.com/docs/rest/business-management/site-properties/properties/get-site-properties). \n  @maxLength: 50 \n  */\n  timeZone?: CommonSearchWithEntityContext['timeZone'] | null;\n};\n\n/**\n * Retrieves a list of up to 100 *booking policies*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)),\n * and information about the services that are connected to them,\n * given the provided filtering, paging, and sorting.\n *\n * ## Defaults\n *\n * Query Policies has the following default settings, which you can override:\n *\n * + Sorted by `id` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to **Booking Policies API: Supported Filters**\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## Returned services\n *\n * If a booking policy is connected to more than 5 services, only a subset of\n * those services is returned. The `bookingPolicies.totalServiceCount` values\n * indicate the total number of services linked to each policy. You can call *Search Services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))\n * and specify the relevant policy ID in the filter to retrieve all services\n * connected to the booking policy.\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n * @param query - Information about filters, paging, and sorting. See the article about\n * booking policy filters\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters))\n * for all supported filters and sorting options.\n * @public\n * @documentationMaturity preview\n * @requiredField query\n * @permissionId BOOKINGS.BOOKING_POLICY_READ\n * @permissionId BOOKINGS.SERVICES_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.QueryPolicies\n */\nexport async function queryPolicies(\n  query: CursorQuery\n): Promise<\n  NonNullablePaths<\n    QueryPoliciesResponse,\n    | `bookingPolicies`\n    | `bookingPolicies.${number}.bookingPolicy._id`\n    | `bookingPolicies.${number}.bookingPolicy.customPolicyDescription.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.customPolicyDescription.description`\n    | `bookingPolicies.${number}.bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `bookingPolicies.${number}.bookingPolicy.limitLateBookingPolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `bookingPolicies.${number}.bookingPolicy.bookAfterStartPolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `bookingPolicies.${number}.bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `bookingPolicies.${number}.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.capacity`\n    | `bookingPolicies.${number}.bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `bookingPolicies.${number}.bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `bookingPolicies.${number}.bookingPolicy.resourcesPolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `bookingPolicies.${number}.bookingPolicy.cancellationFeePolicy.enabled`\n    | `bookingPolicies.${number}.bookingPolicy.saveCreditCardPolicy.enabled`\n    | `bookingPolicies.${number}.totalServiceCount`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({ query: query });\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.queryPolicies(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'bookingPolicies.services.media.items.image' },\n            { path: 'bookingPolicies.services.media.mainMedia.image' },\n            { path: 'bookingPolicies.services.media.coverMedia.image' },\n            { path: 'bookingPolicies.services.staffMembers.mainMedia.image' },\n            {\n              path: 'bookingPolicies.services.staffMemberDetails.staffMembers.mainMedia.image',\n            },\n            { path: 'bookingPolicies.connectedServices.media.items.image' },\n            { path: 'bookingPolicies.connectedServices.media.mainMedia.image' },\n            {\n              path: 'bookingPolicies.connectedServices.media.coverMedia.image',\n            },\n            {\n              path: 'bookingPolicies.connectedServices.staffMembers.mainMedia.image',\n            },\n            {\n              path: 'bookingPolicies.connectedServices.staffMemberDetails.staffMembers.mainMedia.image',\n            },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'bookingPolicies.services.locations.calculatedAddress' },\n            { path: 'bookingPolicies.services.locations.business.address' },\n            { path: 'bookingPolicies.services.locations.custom.address' },\n            {\n              path: 'bookingPolicies.connectedServices.locations.calculatedAddress',\n            },\n            {\n              path: 'bookingPolicies.connectedServices.locations.business.address',\n            },\n            {\n              path: 'bookingPolicies.connectedServices.locations.custom.address',\n            },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'bookingPolicies.services.urls.servicePage' },\n            { path: 'bookingPolicies.services.urls.bookingPage' },\n            { path: 'bookingPolicies.services.urls.calendarPage' },\n            { path: 'bookingPolicies.connectedServices.urls.servicePage' },\n            { path: 'bookingPolicies.connectedServices.urls.bookingPage' },\n            { path: 'bookingPolicies.connectedServices.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { query: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['query']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Retrieves a list of up to 100 *booking forms*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/forms/introduction) | [REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)),\n * and information about the services that are connected to them,\n * given the provided filtering, paging, and sorting.\n *\n *\n * ## Defaults\n *\n * Query Booking Forms has the following default settings, which you can override:\n *\n * + Sorted by `id` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to **Forms API: Supported Filters**\n * ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## Returned services\n *\n * If a booking policy is connected to more than 5 services, only a subset of\n * these service IDs and names is returned. The `bookingForms.totalServiceCount`\n * values indicate the total number of services linked to each form. You can call *Query Services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/query-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/query-services))\n * and specify the relevant form ID in the filter to retrieve all services\n * connected to the booking form.\n *\n * ## Default booking forms\n *\n * By default, all Wix Bookings services use a standard booking form. To retrieve\n * a site's default booking form with Query Booking Forms, specify\n * `{\"conditionalFields\": [\"DEFAULT_BOOKING_FORM\"]}`.\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n * @param query - Information about filters, paging, and sorting. See the article about\n * form filters ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object))\n * for all supported filters and sorting options.\n * @public\n * @requiredField query\n * @permissionId BOOKINGS.SERVICE_BOOKING_FORMS_READ\n * @permissionId BOOKINGS.SERVICES_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.QueryBookingForms\n */\nexport async function queryBookingForms(\n  query: CursorQuery,\n  options?: QueryBookingFormsOptions\n): Promise<\n  NonNullablePaths<\n    QueryBookingFormsResponse,\n    | `bookingForms`\n    | `bookingForms.${number}.formDetails.formId`\n    | `bookingForms.${number}.totalServiceCount`\n    | `defaultBookingForm.formDetails.formId`\n    | `defaultBookingForm.connectedServices`\n    | `defaultBookingForm.totalServiceCount`,\n    5\n  > & {\n    __applicationErrorsType?: QueryBookingFormsApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    query: query,\n    conditionalFields: options?.conditionalFields,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.queryBookingForms(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          query: '$[0]',\n          conditionalFields: '$[1].conditionalFields',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['query', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface QueryBookingFormsOptions {\n  /**\n   * Conditional fields to return.\n   * @maxSize 1\n   */\n  conditionalFields?: RequestedFieldsWithLiterals[];\n}\n\n/**\n * Counts how many services match the given filter.\n *\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters)\n * @public\n * @permissionId BOOKINGS.SERVICES_COUNT\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.CountServices\n */\nexport async function countServices(\n  options?: CountServicesOptions\n): Promise<NonNullablePaths<CountServicesResponse, `count`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.countServices(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface CountServicesOptions {\n  /**\n   * Query filter to base the count on. See supported filters\n   * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n   * for more details.\n   */\n  filter?: Record<string, any> | null;\n}\n\n/**\n * Retrieves 3 separate lists of business, custom, and customer *locations*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)),\n * given the provided filtering, and whether each location is connected to at\n * least one of the site's services.\n *\n *\n * ## Defaults\n *\n * Query Locations has the following default setting, which you can't override:\n * Sorted by `id` in ascending order.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to the `location` object\n * ([REST](https://dev.wix.com/docs/rest/business-management/locations/location-object)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n * @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.SERVICES_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.QueryLocations\n */\nexport async function queryLocations(\n  options?: QueryLocationsOptions\n): Promise<\n  NonNullablePaths<\n    QueryLocationsResponse,\n    | `businessLocations.exists`\n    | `businessLocations.locations`\n    | `businessLocations.locations.${number}.business._id`\n    | `businessLocations.locations.${number}.business.name`\n    | `businessLocations.locations.${number}.custom._id`\n    | `businessLocations.locations.${number}._id`\n    | `businessLocations.locations.${number}.type`\n    | `customLocations.exists`\n    | `customerLocations.exists`,\n    6\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.queryLocations(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'businessLocations.locations.calculatedAddress' },\n            { path: 'businessLocations.locations.business.address' },\n            { path: 'businessLocations.locations.custom.address' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface QueryLocationsOptions {\n  /** Filter. */\n  filter?: QueryLocationsFilter;\n}\n\n/**\n * Retrieves a list of service *categories*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/introduction)),\n * given the provided filtering.\n *\n *\n * ## Defaults\n *\n * Query Categories has the following default setting, which you can't override:\n * Sorted by `id` in ascending order.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n * @public\n * @documentationMaturity preview\n * @permissionId BOOKINGS.SERVICES_READ\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.QueryCategories\n */\nexport async function queryCategories(\n  options?: QueryCategoriesOptions\n): Promise<\n  NonNullablePaths<\n    QueryCategoriesResponse,\n    `categories` | `categories.${number}._id`,\n    4\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    filter: options?.filter,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.queryCategories(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { filter: '$[0].filter' },\n        singleArgumentUnchanged: false,\n      },\n      ['options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface QueryCategoriesOptions {\n  /** Filter. */\n  filter?: QueryCategoriesFilter;\n}\n\n/**\n * Replaces the list of the *locations*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))\n * where the business offers the service.\n *\n *\n * ## Consequences for customers\n *\n * Removing a service location may impact existing sessions and their\n * participants. If you remove at least one service location, you must\n * specify `removedLocationSessionsAction` to indicate what happens to all\n * future sessions scheduled at this location.\n *\n * - **Keep existing location**: If you want to retain future sessions at their\n * originally scheduled location, specify\n * `{\"removedLocationSessionsAction.action\": \"KEEP_AT_CURRENT_LOCATION\"}`.\n * This ensures nothing changes for the customer, but the business must be\n * able to provide access to the removed location in the future.\n * - **Update location**: If you want to update the location for future sessions\n * scheduled at the removed location, specify\n * `{\"removedLocationSessionsAction.action\": \"MOVE_TO_LOCATION\"}` and\n * `moveToLocationOptions.newLocation`.\n *\n * You can't mix and match to keep some sessions at the previous location while\n * moving other sessions to an updated location.\n *\n * Past session details aren't changed, no matter which option you choose for\n * future sessions.\n *\n * Future sessions scheduled for a location defined by the customer are also not\n * updated.\n *\n * ## Specify location details\n *\n * Depending on whether the new or updated location is a business or custom location,\n * you need to specify different fields.\n *\n * - **Business location**: Specify the relevant *location ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))\n * in `locations.business.id`.\n * - **Custom location**: Specify the complete address object as\n * `locations.custom.address`.\n *\n * ## Participant notifications\n *\n * You can specify a `participantNotification.message` that's immediately send\n * to all customers who had booked at a changed location. Ensure\n * `participantNotification.notifyParticipants` is set to `true` to send the\n * message.\n * @param serviceId - ID of the service.\n * @param locations - List of locations replacing existing service locations. Omitting an existing location removes it from the service. Specify `removedLocationSessionsAction` to determine the handling of future sessions scheduled at that location.\n * @public\n * @documentationMaturity preview\n * @requiredField locations\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_LOCATIONS_SET\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.SetServiceLocations\n */\nexport async function setServiceLocations(\n  serviceId: string,\n  locations: Location[],\n  options?: SetServiceLocationsOptions\n): Promise<\n  NonNullablePaths<\n    SetServiceLocationsResponse,\n    | `service.type`\n    | `service.media.items`\n    | `service.category._id`\n    | `service.form._id`\n    | `service.payment.fixed.price.value`\n    | `service.payment.fixed.price.currency`\n    | `service.payment.rateType`\n    | `service.payment.pricingPlanIds`\n    | `service.locations`\n    | `service.locations.${number}.business._id`\n    | `service.locations.${number}.business.name`\n    | `service.locations.${number}.custom._id`\n    | `service.locations.${number}._id`\n    | `service.locations.${number}.type`\n    | `service.bookingPolicy._id`\n    | `service.bookingPolicy.customPolicyDescription.enabled`\n    | `service.bookingPolicy.customPolicyDescription.description`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `service.bookingPolicy.limitLateBookingPolicy.enabled`\n    | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `service.bookingPolicy.bookAfterStartPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `service.bookingPolicy.reschedulePolicy.enabled`\n    | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `service.bookingPolicy.waitlistPolicy.enabled`\n    | `service.bookingPolicy.waitlistPolicy.capacity`\n    | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `service.bookingPolicy.resourcesPolicy.enabled`\n    | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `service.bookingPolicy.cancellationFeePolicy.enabled`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `service.bookingPolicy.saveCreditCardPolicy.enabled`\n    | `service.schedule.availabilityConstraints.durations`\n    | `service.schedule.availabilityConstraints.durations.${number}.minutes`\n    | `service.schedule.availabilityConstraints.sessionDurations`\n    | `service.schedule.availabilityConstraints.timeBetweenSessions`\n    | `service.staffMemberIds`\n    | `service.serviceResources`\n    | `service.supportedSlugs`\n    | `service.supportedSlugs.${number}.name`\n    | `service.seoData.tags`\n    | `service.seoData.tags.${number}.type`\n    | `service.seoData.tags.${number}.children`\n    | `service.seoData.tags.${number}.custom`\n    | `service.seoData.tags.${number}.disabled`\n    | `service.seoData.settings.preventAutoRedirect`\n    | `service.seoData.settings.keywords`\n    | `service.seoData.settings.keywords.${number}.term`\n    | `service.seoData.settings.keywords.${number}.isMain`,\n    7\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[3] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = transformPaths(\n    renameKeysFromSDKRequestToRESTRequest({\n      serviceId: serviceId,\n      locations: locations,\n      removedLocationSessionsAction: options?.removedLocationSessionsAction,\n      participantNotification: options?.participantNotification,\n    }),\n    [\n      {\n        transformFn: transformSDKAddressToRESTAddress,\n        paths: [\n          { path: 'locations.calculatedAddress' },\n          { path: 'locations.business.address' },\n          { path: 'locations.custom.address' },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address',\n          },\n        ],\n      },\n    ]\n  );\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.setServiceLocations(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceId: '$[0]',\n          locations: '$[1]',\n          removedLocationSessionsAction: '$[2].removedLocationSessionsAction',\n          participantNotification: '$[2].participantNotification',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'locations', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface SetServiceLocationsOptions {\n  /**\n   * The action to perform on sessions currently set to a removed location. For\n   * example, move existing sessions to a new specified location.\n   * If not specified, sessions will not be moved to a new location.\n   */\n  removedLocationSessionsAction?: RemovedLocationSessionsAction;\n  /**\n   * Whether to notify participants about the change of location, and an\n   * Optional custom message. The notification is sent only to participants of sessions that are affected by the change.\n   */\n  participantNotification?: ParticipantNotification;\n}\n\n/**\n * Adds a list of *pricing plan IDs*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n * to a service's `payment.pricingPlanIds` array.\n *\n *\n * The call doesn't validate whether the service's `payment.options.pricingPlan`\n * is set to `true`. If it's set to `false`, customers aren't able to pay\n * for their bookings using pricing plans. You can call *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * to change a service's supported payment methods.\n * @param serviceId - ID of the service to update.\n * @param pricingPlanIds - IDs of the *pricing plans*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n * to add to the service's `payment.pricingPlanIds` array.\n * @public\n * @requiredField pricingPlanIds\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_PRICING_PLANS_ADD\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.EnablePricingPlansForService\n */\nexport async function enablePricingPlansForService(\n  serviceId: string,\n  pricingPlanIds: string[]\n): Promise<\n  NonNullablePaths<\n    EnablePricingPlansForServiceResponse,\n    | `service.type`\n    | `service.media.items`\n    | `service.category._id`\n    | `service.form._id`\n    | `service.payment.fixed.price.value`\n    | `service.payment.fixed.price.currency`\n    | `service.payment.rateType`\n    | `service.payment.pricingPlanIds`\n    | `service.locations`\n    | `service.locations.${number}.business._id`\n    | `service.locations.${number}.business.name`\n    | `service.locations.${number}.custom._id`\n    | `service.locations.${number}._id`\n    | `service.locations.${number}.type`\n    | `service.bookingPolicy._id`\n    | `service.bookingPolicy.customPolicyDescription.enabled`\n    | `service.bookingPolicy.customPolicyDescription.description`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `service.bookingPolicy.limitLateBookingPolicy.enabled`\n    | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `service.bookingPolicy.bookAfterStartPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `service.bookingPolicy.reschedulePolicy.enabled`\n    | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `service.bookingPolicy.waitlistPolicy.enabled`\n    | `service.bookingPolicy.waitlistPolicy.capacity`\n    | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `service.bookingPolicy.resourcesPolicy.enabled`\n    | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `service.bookingPolicy.cancellationFeePolicy.enabled`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `service.bookingPolicy.saveCreditCardPolicy.enabled`\n    | `service.schedule.availabilityConstraints.durations`\n    | `service.schedule.availabilityConstraints.durations.${number}.minutes`\n    | `service.schedule.availabilityConstraints.sessionDurations`\n    | `service.schedule.availabilityConstraints.timeBetweenSessions`\n    | `service.staffMemberIds`\n    | `service.serviceResources`\n    | `service.supportedSlugs`\n    | `service.supportedSlugs.${number}.name`\n    | `service.seoData.tags`\n    | `service.seoData.tags.${number}.type`\n    | `service.seoData.tags.${number}.children`\n    | `service.seoData.tags.${number}.custom`\n    | `service.seoData.tags.${number}.disabled`\n    | `service.seoData.settings.preventAutoRedirect`\n    | `service.seoData.settings.keywords`\n    | `service.seoData.settings.keywords.${number}.term`\n    | `service.seoData.settings.keywords.${number}.isMain`,\n    7\n  > & {\n    __applicationErrorsType?: EnablePricingPlansForServiceApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    pricingPlanIds: pricingPlanIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.enablePricingPlansForService(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { serviceId: '$[0]', pricingPlanIds: '$[1]' },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'pricingPlanIds']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\n/**\n * Removes a list of *pricing plan IDs*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n * from a service's `payment.pricingPlanIds` array.\n *\n *\n * If you remove all pricing plan IDs from `payment.pricingPlanIds` and the\n * service supports only payments by pricing plan, customers will no longer be\n * able to book the service, as they will have no payment options available.\n * @param serviceId - ID of the service to update.\n * @public\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_PRICING_PLANS_REMOVE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.DisablePricingPlansForService\n */\nexport async function disablePricingPlansForService(\n  serviceId: string,\n  options?: DisablePricingPlansForServiceOptions\n): Promise<\n  NonNullablePaths<\n    DisablePricingPlansForServiceResponse,\n    | `service.type`\n    | `service.media.items`\n    | `service.category._id`\n    | `service.form._id`\n    | `service.payment.fixed.price.value`\n    | `service.payment.fixed.price.currency`\n    | `service.payment.rateType`\n    | `service.payment.pricingPlanIds`\n    | `service.locations`\n    | `service.locations.${number}.business._id`\n    | `service.locations.${number}.business.name`\n    | `service.locations.${number}.custom._id`\n    | `service.locations.${number}._id`\n    | `service.locations.${number}.type`\n    | `service.bookingPolicy._id`\n    | `service.bookingPolicy.customPolicyDescription.enabled`\n    | `service.bookingPolicy.customPolicyDescription.description`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `service.bookingPolicy.limitLateBookingPolicy.enabled`\n    | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `service.bookingPolicy.bookAfterStartPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `service.bookingPolicy.reschedulePolicy.enabled`\n    | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `service.bookingPolicy.waitlistPolicy.enabled`\n    | `service.bookingPolicy.waitlistPolicy.capacity`\n    | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `service.bookingPolicy.resourcesPolicy.enabled`\n    | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `service.bookingPolicy.cancellationFeePolicy.enabled`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `service.bookingPolicy.saveCreditCardPolicy.enabled`\n    | `service.schedule.availabilityConstraints.durations`\n    | `service.schedule.availabilityConstraints.durations.${number}.minutes`\n    | `service.schedule.availabilityConstraints.sessionDurations`\n    | `service.schedule.availabilityConstraints.timeBetweenSessions`\n    | `service.staffMemberIds`\n    | `service.serviceResources`\n    | `service.supportedSlugs`\n    | `service.supportedSlugs.${number}.name`\n    | `service.seoData.tags`\n    | `service.seoData.tags.${number}.type`\n    | `service.seoData.tags.${number}.children`\n    | `service.seoData.tags.${number}.custom`\n    | `service.seoData.tags.${number}.disabled`\n    | `service.seoData.settings.preventAutoRedirect`\n    | `service.seoData.settings.keywords`\n    | `service.seoData.settings.keywords.${number}.term`\n    | `service.seoData.settings.keywords.${number}.isMain`,\n    7\n  > & {\n    __applicationErrorsType?: DisablePricingPlansForServiceApplicationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    pricingPlanIds: options?.pricingPlanIds,\n  });\n\n  const reqOpts =\n    ambassadorWixBookingsServicesV2Service.disablePricingPlansForService(\n      payload\n    );\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: {\n          serviceId: '$[0]',\n          pricingPlanIds: '$[1].pricingPlanIds',\n        },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface DisablePricingPlansForServiceOptions {\n  /**\n   * IDs of the *pricing plans*\n   * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n   * to remove from the service's `payment.pricingPlanIds` array.\n   * @format GUID\n   * @maxSize 75\n   */\n  pricingPlanIds?: string[];\n}\n\n/**\n * Sets a new active slug for the service.\n *\n *\n * The call fails if at least one of these conditions is met:\n * - The slug doesn't adheres to the supported format.\n * - Another service is currently using the slug.\n * - Another service has used the slug in the past.\n * @param serviceId - ID of the service for which to update the active slug.\n * @public\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_CUSTOM_SLUGS_SET\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.SetCustomSlug\n */\nexport async function setCustomSlug(\n  serviceId: string,\n  options?: SetCustomSlugOptions\n): Promise<\n  NonNullablePaths<\n    SetCustomSlugResponse,\n    | `slug.name`\n    | `service.type`\n    | `service.media.items`\n    | `service.category._id`\n    | `service.form._id`\n    | `service.payment.fixed.price.value`\n    | `service.payment.fixed.price.currency`\n    | `service.payment.rateType`\n    | `service.payment.pricingPlanIds`\n    | `service.locations`\n    | `service.locations.${number}.business._id`\n    | `service.locations.${number}.business.name`\n    | `service.locations.${number}.custom._id`\n    | `service.locations.${number}._id`\n    | `service.locations.${number}.type`\n    | `service.bookingPolicy._id`\n    | `service.bookingPolicy.customPolicyDescription.enabled`\n    | `service.bookingPolicy.customPolicyDescription.description`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `service.bookingPolicy.limitLateBookingPolicy.enabled`\n    | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `service.bookingPolicy.bookAfterStartPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `service.bookingPolicy.reschedulePolicy.enabled`\n    | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `service.bookingPolicy.waitlistPolicy.enabled`\n    | `service.bookingPolicy.waitlistPolicy.capacity`\n    | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `service.bookingPolicy.resourcesPolicy.enabled`\n    | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `service.bookingPolicy.cancellationFeePolicy.enabled`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `service.bookingPolicy.saveCreditCardPolicy.enabled`\n    | `service.schedule.availabilityConstraints.durations`\n    | `service.schedule.availabilityConstraints.durations.${number}.minutes`\n    | `service.schedule.availabilityConstraints.sessionDurations`\n    | `service.schedule.availabilityConstraints.timeBetweenSessions`\n    | `service.staffMemberIds`\n    | `service.serviceResources`\n    | `service.supportedSlugs`\n    | `service.supportedSlugs.${number}.name`\n    | `service.seoData.tags`\n    | `service.seoData.tags.${number}.type`\n    | `service.seoData.tags.${number}.children`\n    | `service.seoData.tags.${number}.custom`\n    | `service.seoData.tags.${number}.disabled`\n    | `service.seoData.settings.preventAutoRedirect`\n    | `service.seoData.settings.keywords`\n    | `service.seoData.settings.keywords.${number}.term`\n    | `service.seoData.settings.keywords.${number}.isMain`,\n    7\n  > & {\n    __applicationErrorsType?: SetCustomSlugApplicationErrors;\n    __validationErrorsType?: SetCustomSlugValidationErrors;\n  }\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    slug: options?.slug,\n  });\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.setCustomSlug(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { serviceId: '$[0]', slug: '$[1].slug' },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface SetCustomSlugOptions {\n  /**\n   * Slug to set as the active service slug.\n   * @maxLength 500\n   */\n  slug?: string;\n}\n\n/**\n * Checks whether a custom slug is validate for the service.\n *\n *\n * The checks include:\n * - The slug adheres to the supported format.\n * - No other service is currently using the slug.\n * - No other service has used the slug in the past.\n *\n * The call fails if at least one of the checks fails.\n * @param serviceId - IO of the service to check custom slug validity for.\n * @public\n * @requiredField serviceId\n * @permissionId BOOKINGS.SERVICES_CUSTOM_SLUGS_SET\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.ValidateSlug\n */\nexport async function validateSlug(\n  serviceId: string,\n  options?: ValidateSlugOptions\n): Promise<NonNullablePaths<ValidateSlugResponse, `valid` | `errors`, 2>> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[2] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    serviceId: serviceId,\n    slug: options?.slug,\n  });\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.validateSlug(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(result.data)!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { serviceId: '$[0]', slug: '$[1].slug' },\n        singleArgumentUnchanged: false,\n      },\n      ['serviceId', 'options']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n\nexport interface ValidateSlugOptions {\n  /**\n   * Custom slug to validate.\n   * @maxLength 500\n   */\n  slug?: string;\n}\n\n/**\n * Clones a service.\n *\n *\n * ## Connected entities\n *\n * By default, not all entities connected to the service are cloned.\n *\n * ### Schedule\n *\n * Wix Bookings automatically creates a new active *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * for the cloned service. If Wix Bookings can't create this schedule, the\n * Clone Service* call fails.\n *\n * - __For appointment-based services__: Future appointments aren't added to the\n * cloned service's schedule. Use the *Events API*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * to add them as needed.\n * - __For classes and courses__: Future one-time events aren't added to the\n * cloned service's schedule, while future recurring events are added\n * asynchronously. The *Clone Service* call always succeeds, whether or not\n * recurring events are added.\n * If the response includes `RECURRING_EVENTS` in the `errors` array, it means the\n * cloned service doesn't have future recurring events, even though the original\n * service does. You can either delete the cloned service and try again or use\n * the *Events API*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * to add missing events to the schedule.\n *\n * Wix Bookings never adds past events to the cloned service's schedule.\n *\n * ### Service options and variants\n *\n * If the original service has *variants*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),\n * they're cloned asynchronously. The *Clone Service* call always succeeds,\n * regardless of whether variants were cloned.\n *\n * If the response includes `OPTIONS_AND_VARIANTS` in the `errors` array, the cloned\n * service doesn't have variants, even though the original service does. You can\n * delete the cloned service and call *Clone Service* again, or use the\n * _Service Options And Variants API_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction))\n * to add variants.\n *\n * ### Booking form\n *\n * The original service's booking form isn't cloned, whether it's\n * the default or a custom booking form.\n *\n * ### Pricing plans\n *\n * If the original service's accepts payments via *pricing plans*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/plans/introduction)),\n * the cloned service's `payment.options.pricingPlan` is also set to `true`. To\n * accept specific pricing plans, call *Enable Pricing Plans For Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/enable-pricing-plans-for-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/enable-pricing-plans-for-service)),\n * after cloning the service. If the original service accepts only\n * pricing plan payments and you don't call *Enable Pricing Plans For Service*\n * after cloning the service, customers will be unable to book the service.\n * @param sourceServiceId - ID of the service to clone.\n * @public\n * @requiredField sourceServiceId\n * @permissionId BOOKINGS.SERVICES_CREATE\n * @applicableIdentity APP\n * @fqn wix.bookings.services.v2.ServicesService.CloneService\n */\nexport async function cloneService(\n  sourceServiceId: string\n): Promise<\n  NonNullablePaths<\n    CloneServiceResponse,\n    | `service.type`\n    | `service.media.items`\n    | `service.category._id`\n    | `service.form._id`\n    | `service.payment.fixed.price.value`\n    | `service.payment.fixed.price.currency`\n    | `service.payment.rateType`\n    | `service.payment.pricingPlanIds`\n    | `service.locations`\n    | `service.locations.${number}.business._id`\n    | `service.locations.${number}.business.name`\n    | `service.locations.${number}.custom._id`\n    | `service.locations.${number}._id`\n    | `service.locations.${number}.type`\n    | `service.bookingPolicy._id`\n    | `service.bookingPolicy.customPolicyDescription.enabled`\n    | `service.bookingPolicy.customPolicyDescription.description`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.enabled`\n    | `service.bookingPolicy.limitEarlyBookingPolicy.earliestBookingInMinutes`\n    | `service.bookingPolicy.limitLateBookingPolicy.enabled`\n    | `service.bookingPolicy.limitLateBookingPolicy.latestBookingInMinutes`\n    | `service.bookingPolicy.bookAfterStartPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.enabled`\n    | `service.bookingPolicy.cancellationPolicy.limitLatestCancellation`\n    | `service.bookingPolicy.cancellationPolicy.latestCancellationInMinutes`\n    | `service.bookingPolicy.reschedulePolicy.enabled`\n    | `service.bookingPolicy.reschedulePolicy.limitLatestReschedule`\n    | `service.bookingPolicy.reschedulePolicy.latestRescheduleInMinutes`\n    | `service.bookingPolicy.waitlistPolicy.enabled`\n    | `service.bookingPolicy.waitlistPolicy.capacity`\n    | `service.bookingPolicy.waitlistPolicy.reservationTimeInMinutes`\n    | `service.bookingPolicy.participantsPolicy.maxParticipantsPerBooking`\n    | `service.bookingPolicy.resourcesPolicy.enabled`\n    | `service.bookingPolicy.resourcesPolicy.autoAssignAllowed`\n    | `service.bookingPolicy.cancellationFeePolicy.enabled`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows`\n    | `service.bookingPolicy.cancellationFeePolicy.cancellationWindows.${number}.percentage`\n    | `service.bookingPolicy.saveCreditCardPolicy.enabled`\n    | `service.schedule.availabilityConstraints.durations`\n    | `service.schedule.availabilityConstraints.durations.${number}.minutes`\n    | `service.schedule.availabilityConstraints.sessionDurations`\n    | `service.schedule.availabilityConstraints.timeBetweenSessions`\n    | `service.staffMemberIds`\n    | `service.serviceResources`\n    | `service.supportedSlugs`\n    | `service.supportedSlugs.${number}.name`\n    | `service.seoData.tags`\n    | `service.seoData.tags.${number}.type`\n    | `service.seoData.tags.${number}.children`\n    | `service.seoData.tags.${number}.custom`\n    | `service.seoData.tags.${number}.disabled`\n    | `service.seoData.settings.preventAutoRedirect`\n    | `service.seoData.settings.keywords`\n    | `service.seoData.settings.keywords.${number}.term`\n    | `service.seoData.settings.keywords.${number}.isMain`\n    | `errors`,\n    7\n  >\n> {\n  // @ts-ignore\n  const { httpClient, sideEffects } = arguments[1] as {\n    httpClient: HttpClient;\n    sideEffects?: any;\n  };\n\n  const payload = renameKeysFromSDKRequestToRESTRequest({\n    sourceServiceId: sourceServiceId,\n  });\n\n  const reqOpts = ambassadorWixBookingsServicesV2Service.cloneService(payload);\n\n  sideEffects?.onSiteCall?.();\n  try {\n    const result = await httpClient.request(reqOpts);\n    sideEffects?.onSuccess?.(result);\n\n    return renameKeysFromRESTResponseToSDKResponse(\n      transformPaths(result.data, [\n        {\n          transformFn: transformRESTImageToSDKImage,\n          paths: [\n            { path: 'service.media.items.image' },\n            { path: 'service.media.mainMedia.image' },\n            { path: 'service.media.coverMedia.image' },\n            { path: 'service.staffMembers.mainMedia.image' },\n            { path: 'service.staffMemberDetails.staffMembers.mainMedia.image' },\n          ],\n        },\n        {\n          transformFn: transformRESTAddressToSDKAddress,\n          paths: [\n            { path: 'service.locations.calculatedAddress' },\n            { path: 'service.locations.business.address' },\n            { path: 'service.locations.custom.address' },\n          ],\n        },\n        {\n          transformFn: transformRESTPageURLV2ToSDKPageURLV2,\n          paths: [\n            { path: 'service.urls.servicePage' },\n            { path: 'service.urls.bookingPage' },\n            { path: 'service.urls.calendarPage' },\n          ],\n        },\n      ])\n    )!;\n  } catch (err: any) {\n    const transformedError = sdkTransformError(\n      err,\n      {\n        spreadPathsToArguments: {},\n        explicitPathsToArguments: { sourceServiceId: '$[0]' },\n        singleArgumentUnchanged: false,\n      },\n      ['sourceServiceId']\n    );\n    sideEffects?.onError?.(err);\n\n    throw transformedError;\n  }\n}\n", "import { toURLSearchParams } from '@wix/sdk-runtime/rest-modules';\nimport { transformSDKFloatToRESTFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformSDKTimestampToRESTTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformSDKFieldMaskToRESTFieldMask } from '@wix/sdk-runtime/transformations/field-mask';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveWixBookingsServicesV2ServicesServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/services-2',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n      {\n        srcPath: '/bookings/services/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/services-2',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nfunction resolveWixBookingsServicesV2AddOnGroupsServiceUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    '*.dev.wix-code.com': [\n      {\n        srcPath: '/_api/services-2',\n        destPath: '',\n      },\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n      {\n        srcPath: '/bookings/services/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n      {\n        srcPath: '/_api/bookings/v2/bulk/services',\n        destPath: '/v2/bulk/services',\n      },\n    ],\n    _: [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'www._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'bo._base_domain_': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wixbo.ai': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'wix-bo.com': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'editor.wixapps.net': [\n      {\n        srcPath: '/_api/bookings/v2/services',\n        destPath: '/v2/services',\n      },\n    ],\n    'api._api_base_domain_': [\n      {\n        srcPath: '/services-2',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_services';\n\n/**\n * Create a new AddOns group.\n * An AddOns group defines a collection of AddOns that can be linked to a Service,\n * with constraints such as minimum and maximum selections.\n */\nexport function createAddOnGroup(payload: object): RequestOptionsFactory<any> {\n  function __createAddOnGroup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.AddOnGroupsService.CreateAddOnGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/create',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __createAddOnGroup;\n}\n\n/**\n * Delete an existing AddOns group.\n * This will remove the group and unlink all associated AddOns.\n */\nexport function deleteAddOnGroup(payload: object): RequestOptionsFactory<any> {\n  function __deleteAddOnGroup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.AddOnGroupsService.DeleteAddOnGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/delete',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __deleteAddOnGroup;\n}\n\n/**\n * Update an existing AddOns group.\n * This allows modifying group settings such as its name, prompt, constraints, or associated AddOns.\n */\nexport function updateAddOnGroup(payload: object): RequestOptionsFactory<any> {\n  function __updateAddOnGroup({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.AddOnGroupsService.UpdateAddOnGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/update',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __updateAddOnGroup;\n}\n\n/**\n * Retrieves a list of AddOnGroups including enriched AddOn details in the correct order.\n * If the group_id is specified, only the AddOns for the specified group will be returned,\n * otherwise all groups will be returned.\n */\nexport function listAddOnGroupsByServiceId(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __listAddOnGroupsByServiceId({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.AddOnGroupsService.ListAddOnGroupsByServiceId',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath:\n          '/v2/services/add-on-groups/list-add-on-groups-by-service-id',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __listAddOnGroupsByServiceId;\n}\n\n/**\n * Sets the AddOns for a specific group.\n * The order of the AddOns in the list will be used to determine their display order.\n */\nexport function setAddOnsForGroup(payload: object): RequestOptionsFactory<any> {\n  function __setAddOnsForGroup({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.AddOnGroupsService.SetAddOnsForGroup',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2AddOnGroupsServiceUrl({\n        protoPath: '/v2/services/add-on-groups/set-add-ons-for-group',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __setAddOnsForGroup;\n}\n\n/**\n * Creates a service.\n *\n *\n * ## Required fields\n *\n * When creating a service you must specify the following fields:\n * - `type`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-types) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-types)):\n * Whether it's an appointment-based service, class, or course.\n * - `name`: Service name that's displayed to customers.\n * - `onlineBooking`:\n * Settings determining whether customers can book online, whether the business\n * must manually confirm bookings, and whether customers can request to book an\n * appointment time slot that already has a booking request awaiting business\n * confirmation.\n * - `payment`\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments)):\n * How customers can pay when signing up for the service.\n *\n * ### Session durations\n *\n * Depending on which type of service you're creating, you may also need to specify\n * supported session durations.\n *\n * __Classes and courses__\n *\n * Don't specify `schedule.availabilityConstraints.sessionDurations`.\n *\n * __Appointment-based services without varied pricing based on session length__\n *\n * Specify the single supported session duration in the\n * `schedule.availabilityConstraints.sessionDurations` array.\n *\n * __Appointment-based services with varied pricing based on session length__\n *\n * - Specify all supported session durations in `schedule.availabilityConstraints.sessionDurations`.\n * - Note that Wix Bookings doesn't display these values to customers and\n * ignores them in pricing and availability calculations. Instead session\n * durations are retrieved from the relevant service *variants*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)).\n * - It is mandatory to specify `schedule.availabilityConstraints.sessionDurations`,\n * even though these values are ignored.\n */\nexport function createService(payload: object): RequestOptionsFactory<any> {\n  function __createService({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'service.createdDate' },\n          { path: 'service.updatedDate' },\n          { path: 'service.media.items.image.urlExpirationDate' },\n          { path: 'service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'service.bookingPolicy.createdDate' },\n          { path: 'service.bookingPolicy.updatedDate' },\n          { path: 'service.schedule.firstSessionStart' },\n          { path: 'service.schedule.lastSessionEnd' },\n          { path: 'service.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'service.supportedSlugs.createdDate' },\n          { path: 'service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'service.locations.business.address.geocode.latitude' },\n          { path: 'service.locations.business.address.geocode.longitude' },\n          { path: 'service.locations.custom.address.geocode.latitude' },\n          { path: 'service.locations.custom.address.geocode.longitude' },\n          { path: 'service.locations.calculatedAddress.geocode.latitude' },\n          { path: 'service.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.CreateService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __createService;\n}\n\n/**\n * Creates multiple services.\n *\n *\n * See *Create Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service))\n * for more details.\n */\nexport function bulkCreateServices(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkCreateServices({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'services.createdDate' },\n          { path: 'services.updatedDate' },\n          { path: 'services.media.items.image.urlExpirationDate' },\n          { path: 'services.media.mainMedia.image.urlExpirationDate' },\n          { path: 'services.media.coverMedia.image.urlExpirationDate' },\n          { path: 'services.bookingPolicy.createdDate' },\n          { path: 'services.bookingPolicy.updatedDate' },\n          { path: 'services.schedule.firstSessionStart' },\n          { path: 'services.schedule.lastSessionEnd' },\n          { path: 'services.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'services.supportedSlugs.createdDate' },\n          { path: 'services.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'services.locations.business.address.geocode.latitude' },\n          { path: 'services.locations.business.address.geocode.longitude' },\n          { path: 'services.locations.custom.address.geocode.latitude' },\n          { path: 'services.locations.custom.address.geocode.longitude' },\n          { path: 'services.locations.calculatedAddress.geocode.latitude' },\n          { path: 'services.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.BulkCreateServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/create',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.media.items.image.urlExpirationDate' },\n              { path: 'results.item.media.mainMedia.image.urlExpirationDate' },\n              { path: 'results.item.media.coverMedia.image.urlExpirationDate' },\n              { path: 'results.item.bookingPolicy.createdDate' },\n              { path: 'results.item.bookingPolicy.updatedDate' },\n              { path: 'results.item.schedule.firstSessionStart' },\n              { path: 'results.item.schedule.lastSessionEnd' },\n              {\n                path: 'results.item.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'results.item.supportedSlugs.createdDate' },\n              { path: 'results.item.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkCreateServices;\n}\n\n/** Retrieves a service. */\nexport function getService(payload: object): RequestOptionsFactory<any> {\n  function __getService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'GET' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.GetService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __getService;\n}\n\n/**\n * Updates a service.\n *\n *\n * Each time the service is updated, `revision` increments by 1. You must\n * include the number of the existing revision when updating the service.\n * This ensures you're working with the latest service information and\n * prevents unintended overwrites.\n *\n * ## Session durations\n *\n * Specify `schedule.availabilityConstraints.sessionDurations`\n * only if you want to update it for appointment-based services without varied\n * pricing based on session length. Don't specify `schedule.availabilityConstraints.sessionDurations`\n * for all other appointment-based services, classes, or courses. See *Create Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/create-service#session-durations) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/create-service#session-durations))\n * for more details.\n */\nexport function updateService(payload: object): RequestOptionsFactory<any> {\n  function __updateService({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'service.createdDate' },\n          { path: 'service.updatedDate' },\n          { path: 'service.media.items.image.urlExpirationDate' },\n          { path: 'service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'service.bookingPolicy.createdDate' },\n          { path: 'service.bookingPolicy.updatedDate' },\n          { path: 'service.schedule.firstSessionStart' },\n          { path: 'service.schedule.lastSessionEnd' },\n          { path: 'service.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'service.supportedSlugs.createdDate' },\n          { path: 'service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'service.locations.business.address.geocode.latitude' },\n          { path: 'service.locations.business.address.geocode.longitude' },\n          { path: 'service.locations.custom.address.geocode.latitude' },\n          { path: 'service.locations.custom.address.geocode.longitude' },\n          { path: 'service.locations.calculatedAddress.geocode.latitude' },\n          { path: 'service.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'PATCH' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.UpdateService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{service.id}',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __updateService;\n}\n\n/**\n * Updates up to 100 services.\n *\n *\n * See *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * for more details.\n *\n * The call succeeds even if one or more individual services can't be updated.\n * Information about failures is returned in `bulkActionMetadata`.\n */\nexport function bulkUpdateServices(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateServices({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'services.mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'services.service.createdDate' },\n          { path: 'services.service.updatedDate' },\n          { path: 'services.service.media.items.image.urlExpirationDate' },\n          { path: 'services.service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'services.service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'services.service.bookingPolicy.createdDate' },\n          { path: 'services.service.bookingPolicy.updatedDate' },\n          { path: 'services.service.schedule.firstSessionStart' },\n          { path: 'services.service.schedule.lastSessionEnd' },\n          {\n            path: 'services.service.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          {\n            path: 'services.service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'services.service.supportedSlugs.createdDate' },\n          { path: 'services.service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          {\n            path: 'services.service.locations.business.address.geocode.latitude',\n          },\n          {\n            path: 'services.service.locations.business.address.geocode.longitude',\n          },\n          {\n            path: 'services.service.locations.custom.address.geocode.latitude',\n          },\n          {\n            path: 'services.service.locations.custom.address.geocode.longitude',\n          },\n          {\n            path: 'services.service.locations.calculatedAddress.geocode.latitude',\n          },\n          {\n            path: 'services.service.locations.calculatedAddress.geocode.longitude',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.BulkUpdateServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/update',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.media.items.image.urlExpirationDate' },\n              { path: 'results.item.media.mainMedia.image.urlExpirationDate' },\n              { path: 'results.item.media.coverMedia.image.urlExpirationDate' },\n              { path: 'results.item.bookingPolicy.createdDate' },\n              { path: 'results.item.bookingPolicy.updatedDate' },\n              { path: 'results.item.schedule.firstSessionStart' },\n              { path: 'results.item.schedule.lastSessionEnd' },\n              {\n                path: 'results.item.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'results.item.supportedSlugs.createdDate' },\n              { path: 'results.item.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateServices;\n}\n\n/**\n * Updates multiple services by filter.\n *\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * See *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * for more details about updating a service.\n *\n * The call succeeds even if one or more individual services can't be updated.\n * Information about failures is returned in `bulkActionMetadata`.\n */\nexport function bulkUpdateServicesByFilter(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkUpdateServicesByFilter({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFieldMaskToRESTFieldMask,\n        paths: [{ path: 'mask' }],\n      },\n      {\n        transformFn: transformSDKTimestampToRESTTimestamp,\n        paths: [\n          { path: 'service.createdDate' },\n          { path: 'service.updatedDate' },\n          { path: 'service.media.items.image.urlExpirationDate' },\n          { path: 'service.media.mainMedia.image.urlExpirationDate' },\n          { path: 'service.media.coverMedia.image.urlExpirationDate' },\n          { path: 'service.bookingPolicy.createdDate' },\n          { path: 'service.bookingPolicy.updatedDate' },\n          { path: 'service.schedule.firstSessionStart' },\n          { path: 'service.schedule.lastSessionEnd' },\n          { path: 'service.staffMembers.mainMedia.image.urlExpirationDate' },\n          {\n            path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n          },\n          { path: 'service.supportedSlugs.createdDate' },\n          { path: 'service.mainSlug.createdDate' },\n        ],\n      },\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'service.locations.business.address.geocode.latitude' },\n          { path: 'service.locations.business.address.geocode.longitude' },\n          { path: 'service.locations.custom.address.geocode.latitude' },\n          { path: 'service.locations.custom.address.geocode.longitude' },\n          { path: 'service.locations.calculatedAddress.geocode.latitude' },\n          { path: 'service.locations.calculatedAddress.geocode.longitude' },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.BulkUpdateServicesByFilter',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/update-by-filter',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n    };\n\n    return metadata;\n  }\n\n  return __bulkUpdateServicesByFilter;\n}\n\n/**\n * Deletes a service.\n *\n *\n * Specify `{\"preserveFutureSessionsWithParticipants\": true}` to retain all\n * future sessions for the service. By default, all future sessions are canceled.\n */\nexport function deleteService(payload: object): RequestOptionsFactory<any> {\n  function __deleteService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'DELETE' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.DeleteService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}',\n        data: payload,\n        host,\n      }),\n      params: toURLSearchParams(payload),\n    };\n\n    return metadata;\n  }\n\n  return __deleteService;\n}\n\n/**\n * Deletes multiple services.\n *\n *\n * See *Delete Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))\n * for more details about deleting a service.\n *\n * The call succeeds even if one or more individual services can't be deleted.\n * Information about failures is returned in `bulkActionMetadata`.\n */\nexport function bulkDeleteServices(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkDeleteServices({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.BulkDeleteServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/delete',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'results.item.createdDate' },\n              { path: 'results.item.updatedDate' },\n              { path: 'results.item.media.items.image.urlExpirationDate' },\n              { path: 'results.item.media.mainMedia.image.urlExpirationDate' },\n              { path: 'results.item.media.coverMedia.image.urlExpirationDate' },\n              { path: 'results.item.bookingPolicy.createdDate' },\n              { path: 'results.item.bookingPolicy.updatedDate' },\n              { path: 'results.item.schedule.firstSessionStart' },\n              { path: 'results.item.schedule.lastSessionEnd' },\n              {\n                path: 'results.item.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'results.item.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'results.item.supportedSlugs.createdDate' },\n              { path: 'results.item.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'results.item.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'results.item.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __bulkDeleteServices;\n}\n\n/**\n * Deletes multiple services by filter.\n *\n *\n * See *Delete Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/delete-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/delete-service))\n * for more details about deleting a service.\n *\n * The call succeeds even if one or more individual services can't be deleted.\n * Information about failures is returned in `bulkActionMetadata`.\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters).\n */\nexport function bulkDeleteServicesByFilter(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __bulkDeleteServicesByFilter({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.BulkDeleteServicesByFilter',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/bulk/services/delete-by-filter',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __bulkDeleteServicesByFilter;\n}\n\n/**\n * Creates a query to retrieve a list of `service` objects.\n *\n * The `queryServices()` function builds a query to retrieve a list of `service` objects and returns a `ServicesQueryBuilder` object.\n *\n * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-builder/find) function.\n *\n * You can refine the query by chaining `ServicesQueryBuilder` functions onto the query. `ServicesQueryBuilder` functions enable you to sort, filter, and control the results that `queryServices()` returns.\n *\n * `queryServices()` runs with the following `ServicesQueryBuilder` defaults that you can override:\n *\n * + `limit` is `100`.\n * + Sorted by `createdDate` in ascending order.\n *\n * The functions that are chained to `queryServices()` are applied in the order they are called. For example, if you apply `ascending(\"category.name\")` and then `ascending(\"name\")`, the results are sorted first by `category.name`, and then, if there are multiple results with the same `category.name`, the items are sorted by `name`.\n *\n * The following `ServicesQueryBuilder` functions are supported for the `queryServices()` function. For a full description of the `service` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/services-query-result/items) property in `ServicesQueryResult`.\n */\nexport function queryServices(payload: object): RequestOptionsFactory<any> {\n  function __queryServices({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'services.createdDate' },\n              { path: 'services.updatedDate' },\n              { path: 'services.media.items.image.urlExpirationDate' },\n              { path: 'services.media.mainMedia.image.urlExpirationDate' },\n              { path: 'services.media.coverMedia.image.urlExpirationDate' },\n              { path: 'services.bookingPolicy.createdDate' },\n              { path: 'services.bookingPolicy.updatedDate' },\n              { path: 'services.schedule.firstSessionStart' },\n              { path: 'services.schedule.lastSessionEnd' },\n              {\n                path: 'services.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'services.supportedSlugs.createdDate' },\n              { path: 'services.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'services.locations.business.address.geocode.latitude' },\n              { path: 'services.locations.business.address.geocode.longitude' },\n              { path: 'services.locations.custom.address.geocode.latitude' },\n              { path: 'services.locations.custom.address.geocode.longitude' },\n              { path: 'services.locations.calculatedAddress.geocode.latitude' },\n              {\n                path: 'services.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryServices;\n}\n\n/**\n * Retrieves a list of up to 100 services, given the provided filtering, paging,\n * and sorting.\n *\n *\n * ## Defaults\n *\n * Search Services has the following default settings, which you can override:\n * + Sorted by `createdDate` in ascending order.\n * + `paging.limit` set to `100`.\n * + `paging.offset` set to `0`.\n *\n * ## Filters\n *\n * When using filters for dates, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting))\n * for a complete list of supported filters and sorting options.\n *\n * ## See also\n *\n * To learn about working with *Search* methods, see\n * _API Query Language_\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))\n * and *Sorting and Paging*\n * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).\n */\nexport function searchServices(payload: object): RequestOptionsFactory<any> {\n  function __searchServices({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'search.aggregations.range.buckets.from' },\n          { path: 'search.aggregations.range.buckets.to' },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.from',\n          },\n          {\n            path: 'search.aggregations.nested.nestedAggregations.range.buckets.to',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.SearchServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/search',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'services.createdDate' },\n              { path: 'services.updatedDate' },\n              { path: 'services.media.items.image.urlExpirationDate' },\n              { path: 'services.media.mainMedia.image.urlExpirationDate' },\n              { path: 'services.media.coverMedia.image.urlExpirationDate' },\n              { path: 'services.bookingPolicy.createdDate' },\n              { path: 'services.bookingPolicy.updatedDate' },\n              { path: 'services.schedule.firstSessionStart' },\n              { path: 'services.schedule.lastSessionEnd' },\n              {\n                path: 'services.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'services.supportedSlugs.createdDate' },\n              { path: 'services.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'services.locations.business.address.geocode.latitude' },\n              { path: 'services.locations.business.address.geocode.longitude' },\n              { path: 'services.locations.custom.address.geocode.latitude' },\n              { path: 'services.locations.custom.address.geocode.longitude' },\n              { path: 'services.locations.calculatedAddress.geocode.latitude' },\n              {\n                path: 'services.locations.calculatedAddress.geocode.longitude',\n              },\n              { path: 'aggregationData.results.ranges.results.from' },\n              { path: 'aggregationData.results.ranges.results.to' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.from',\n              },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.ranges.results.to',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.from',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.range.to',\n              },\n              { path: 'aggregationData.results.scalar.value' },\n              {\n                path: 'aggregationData.results.groupedByValue.results.nestedResults.scalar.value',\n              },\n              {\n                path: 'aggregationData.results.nested.results.results.*.scalar.value',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __searchServices;\n}\n\n/**\n * Retrieves a list of up to 100 *booking policies*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)),\n * and information about the services that are connected to them,\n * given the provided filtering, paging, and sorting.\n *\n * ## Defaults\n *\n * Query Policies has the following default settings, which you can override:\n *\n * + Sorted by `id` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to **Booking Policies API: Supported Filters**\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/supported-filters)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## Returned services\n *\n * If a booking policy is connected to more than 5 services, only a subset of\n * those services is returned. The `bookingPolicies.totalServiceCount` values\n * indicate the total number of services linked to each policy. You can call *Search Services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/search-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/search-services))\n * and specify the relevant policy ID in the filter to retrieve all services\n * connected to the booking policy.\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryPolicies(payload: object): RequestOptionsFactory<any> {\n  function __queryPolicies({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryPolicies',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/policies/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'bookingPolicies.bookingPolicy.createdDate' },\n              { path: 'bookingPolicies.bookingPolicy.updatedDate' },\n              { path: 'bookingPolicies.services.createdDate' },\n              { path: 'bookingPolicies.services.updatedDate' },\n              {\n                path: 'bookingPolicies.services.media.items.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.services.media.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.services.media.coverMedia.image.urlExpirationDate',\n              },\n              { path: 'bookingPolicies.services.bookingPolicy.createdDate' },\n              { path: 'bookingPolicies.services.bookingPolicy.updatedDate' },\n              { path: 'bookingPolicies.services.schedule.firstSessionStart' },\n              { path: 'bookingPolicies.services.schedule.lastSessionEnd' },\n              {\n                path: 'bookingPolicies.services.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.services.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'bookingPolicies.services.supportedSlugs.createdDate' },\n              { path: 'bookingPolicies.services.mainSlug.createdDate' },\n              { path: 'bookingPolicies.connectedServices.createdDate' },\n              { path: 'bookingPolicies.connectedServices.updatedDate' },\n              {\n                path: 'bookingPolicies.connectedServices.media.items.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.media.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.media.coverMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.bookingPolicy.createdDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.bookingPolicy.updatedDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.schedule.firstSessionStart',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.schedule.lastSessionEnd',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.supportedSlugs.createdDate',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.mainSlug.createdDate',\n              },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'bookingPolicies.services.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.services.locations.calculatedAddress.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'bookingPolicies.connectedServices.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryPolicies;\n}\n\n/**\n * Retrieves a list of up to 100 *booking forms*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/forms/introduction) | [REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)),\n * and information about the services that are connected to them,\n * given the provided filtering, paging, and sorting.\n *\n *\n * ## Defaults\n *\n * Query Booking Forms has the following default settings, which you can override:\n *\n * + Sorted by `id` in ascending order.\n * + `cursorPaging.limit` set to `100`.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to **Forms API: Supported Filters**\n * ([REST](https://dev.wix.com/docs/rest/crm/forms/form-schemas/form-object)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## Returned services\n *\n * If a booking policy is connected to more than 5 services, only a subset of\n * these service IDs and names is returned. The `bookingForms.totalServiceCount`\n * values indicate the total number of services linked to each form. You can call *Query Services*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/query-services) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/query-services))\n * and specify the relevant form ID in the filter to retrieve all services\n * connected to the booking form.\n *\n * ## Default booking forms\n *\n * By default, all Wix Bookings services use a standard booking form. To retrieve\n * a site's default booking form with Query Booking Forms, specify\n * `{\"conditionalFields\": [\"DEFAULT_BOOKING_FORM\"]}`.\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryBookingForms(payload: object): RequestOptionsFactory<any> {\n  function __queryBookingForms({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryBookingForms',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/booking-forms/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryBookingForms;\n}\n\n/**\n * Counts how many services match the given filter.\n *\n *\n * Refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering))\n * for more details.\n *\n * To learn about working with filters in general, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#filters)\n */\nexport function countServices(payload: object): RequestOptionsFactory<any> {\n  function __countServices({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.CountServices',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countServices;\n}\n\n/**\n * Retrieves 3 separate lists of business, custom, and customer *locations*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction)),\n * given the provided filtering, and whether each location is connected to at\n * least one of the site's services.\n *\n *\n * ## Defaults\n *\n * Query Locations has the following default setting, which you can't override:\n * Sorted by `id` in ascending order.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to the `location` object\n * ([REST](https://dev.wix.com/docs/rest/business-management/locations/location-object)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryLocations(payload: object): RequestOptionsFactory<any> {\n  function __queryLocations({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryLocations',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/locations/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'businessLocations.locations.business.address.geocode.latitude',\n              },\n              {\n                path: 'businessLocations.locations.business.address.geocode.longitude',\n              },\n              {\n                path: 'businessLocations.locations.custom.address.geocode.latitude',\n              },\n              {\n                path: 'businessLocations.locations.custom.address.geocode.longitude',\n              },\n              {\n                path: 'businessLocations.locations.calculatedAddress.geocode.latitude',\n              },\n              {\n                path: 'businessLocations.locations.calculatedAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryLocations;\n}\n\n/**\n * Retrieves a list of service *categories*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/categories-v2/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/categories-v2/introduction)),\n * given the provided filtering.\n *\n *\n * ## Defaults\n *\n * Query Categories has the following default setting, which you can't override:\n * Sorted by `id` in ascending order.\n *\n * ## Filters\n *\n * For a complete list of supported filters, refer to the supported filters article\n * ([REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/filtering-and-sorting#filtering)).\n *\n * When using date filters, you must use\n * [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * ## See also\n *\n * To learn about working with *Query* endpoints, see\n * [API Query Language](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language)\n * and [Sorting and Paging](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging).\n */\nexport function queryCategories(payload: object): RequestOptionsFactory<any> {\n  function __queryCategories({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.QueryCategories',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/categories/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __queryCategories;\n}\n\n/**\n * Replaces the list of the *locations*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))\n * where the business offers the service.\n *\n *\n * ## Consequences for customers\n *\n * Removing a service location may impact existing sessions and their\n * participants. If you remove at least one service location, you must\n * specify `removedLocationSessionsAction` to indicate what happens to all\n * future sessions scheduled at this location.\n *\n * - **Keep existing location**: If you want to retain future sessions at their\n * originally scheduled location, specify\n * `{\"removedLocationSessionsAction.action\": \"KEEP_AT_CURRENT_LOCATION\"}`.\n * This ensures nothing changes for the customer, but the business must be\n * able to provide access to the removed location in the future.\n * - **Update location**: If you want to update the location for future sessions\n * scheduled at the removed location, specify\n * `{\"removedLocationSessionsAction.action\": \"MOVE_TO_LOCATION\"}` and\n * `moveToLocationOptions.newLocation`.\n *\n * You can't mix and match to keep some sessions at the previous location while\n * moving other sessions to an updated location.\n *\n * Past session details aren't changed, no matter which option you choose for\n * future sessions.\n *\n * Future sessions scheduled for a location defined by the customer are also not\n * updated.\n *\n * ## Specify location details\n *\n * Depending on whether the new or updated location is a business or custom location,\n * you need to specify different fields.\n *\n * - **Business location**: Specify the relevant *location ID*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/business-tools/locations/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/locations/introduction))\n * in `locations.business.id`.\n * - **Custom location**: Specify the complete address object as\n * `locations.custom.address`.\n *\n * ## Participant notifications\n *\n * You can specify a `participantNotification.message` that's immediately send\n * to all customers who had booked at a changed location. Ensure\n * `participantNotification.notifyParticipants` is set to `true` to send the\n * message.\n */\nexport function setServiceLocations(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __setServiceLocations({ host }: any) {\n    const serializedData = transformPaths(payload, [\n      {\n        transformFn: transformSDKFloatToRESTFloat,\n        paths: [\n          { path: 'locations.business.address.geocode.latitude' },\n          { path: 'locations.business.address.geocode.longitude' },\n          { path: 'locations.custom.address.geocode.latitude' },\n          { path: 'locations.custom.address.geocode.longitude' },\n          { path: 'locations.calculatedAddress.geocode.latitude' },\n          { path: 'locations.calculatedAddress.geocode.longitude' },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.latitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.business.address.geocode.longitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.latitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.custom.address.geocode.longitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.latitude',\n          },\n          {\n            path: 'removedLocationSessionsAction.moveToLocationOptions.newLocation.calculatedAddress.geocode.longitude',\n          },\n        ],\n      },\n    ]);\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.SetServiceLocations',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/locations',\n        data: serializedData,\n        host,\n      }),\n      data: serializedData,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setServiceLocations;\n}\n\n/**\n * Adds a list of *pricing plan IDs*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n * to a service's `payment.pricingPlanIds` array.\n *\n *\n * The call doesn't validate whether the service's `payment.options.pricingPlan`\n * is set to `true`. If it's set to `false`, customers aren't able to pay\n * for their bookings using pricing plans. You can call *Update Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/update-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/update-service))\n * to change a service's supported payment methods.\n */\nexport function enablePricingPlansForService(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __enablePricingPlansForService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.EnablePricingPlansForService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/pricing-plans/add',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __enablePricingPlansForService;\n}\n\n/**\n * Removes a list of *pricing plan IDs*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/api/rest/wix-pricing-plans/pricing-plans/plans/plan-object))\n * from a service's `payment.pricingPlanIds` array.\n *\n *\n * If you remove all pricing plan IDs from `payment.pricingPlanIds` and the\n * service supports only payments by pricing plan, customers will no longer be\n * able to book the service, as they will have no payment options available.\n */\nexport function disablePricingPlansForService(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __disablePricingPlansForService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn:\n        'wix.bookings.services.v2.ServicesService.DisablePricingPlansForService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/pricing-plans/remove',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __disablePricingPlansForService;\n}\n\n/**\n * Sets a new active slug for the service.\n *\n *\n * The call fails if at least one of these conditions is met:\n * - The slug doesn't adheres to the supported format.\n * - Another service is currently using the slug.\n * - Another service has used the slug in the past.\n */\nexport function setCustomSlug(payload: object): RequestOptionsFactory<any> {\n  function __setCustomSlug({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.SetCustomSlug',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/{serviceId}/slugs/custom',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'slug.createdDate' },\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __setCustomSlug;\n}\n\n/**\n * Checks whether a custom slug is validate for the service.\n *\n *\n * The checks include:\n * - The slug adheres to the supported format.\n * - No other service is currently using the slug.\n * - No other service has used the slug in the past.\n *\n * The call fails if at least one of the checks fails.\n */\nexport function validateSlug(payload: object): RequestOptionsFactory<any> {\n  function __validateSlug({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.ValidateSlug',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/slugs/validate',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __validateSlug;\n}\n\n/**\n * Clones a service.\n *\n *\n * ## Connected entities\n *\n * By default, not all entities connected to the service are cloned.\n *\n * ### Schedule\n *\n * Wix Bookings automatically creates a new active *schedule*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/schedules/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/schedules-v3/introduction))\n * for the cloned service. If Wix Bookings can't create this schedule, the\n * Clone Service* call fails.\n *\n * - __For appointment-based services__: Future appointments aren't added to the\n * cloned service's schedule. Use the *Events API*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * to add them as needed.\n * - __For classes and courses__: Future one-time events aren't added to the\n * cloned service's schedule, while future recurring events are added\n * asynchronously. The *Clone Service* call always succeeds, whether or not\n * recurring events are added.\n * If the response includes `RECURRING_EVENTS` in the `errors` array, it means the\n * cloned service doesn't have future recurring events, even though the original\n * service does. You can either delete the cloned service and try again or use\n * the *Events API*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/calendar/events/introduction) | [REST](https://dev.wix.com/docs/rest/business-management/calendar/events-v3/introduction))\n * to add missing events to the schedule.\n *\n * Wix Bookings never adds past events to the cloned service's schedule.\n *\n * ### Service options and variants\n *\n * If the original service has *variants*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction)),\n * they're cloned asynchronously. The *Clone Service* call always succeeds,\n * regardless of whether variants were cloned.\n *\n * If the response includes `OPTIONS_AND_VARIANTS` in the `errors` array, the cloned\n * service doesn't have variants, even though the original service does. You can\n * delete the cloned service and call *Clone Service* again, or use the\n * _Service Options And Variants API_\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/introduction))\n * to add variants.\n *\n * ### Booking form\n *\n * The original service's booking form isn't cloned, whether it's\n * the default or a custom booking form.\n *\n * ### Pricing plans\n *\n * If the original service's accepts payments via *pricing plans*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/pricing-plans/plans/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/pricing-plans/pricing-plans/plans/introduction)),\n * the cloned service's `payment.options.pricingPlan` is also set to `true`. To\n * accept specific pricing plans, call *Enable Pricing Plans For Service*\n * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/enable-pricing-plans-for-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/enable-pricing-plans-for-service)),\n * after cloning the service. If the original service accepts only\n * pricing plan payments and you don't call *Enable Pricing Plans For Service*\n * after cloning the service, customers will be unable to book the service.\n */\nexport function cloneService(payload: object): RequestOptionsFactory<any> {\n  function __cloneService({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.services.v2.service',\n      method: 'POST' as any,\n      methodFqn: 'wix.bookings.services.v2.ServicesService.CloneService',\n      packageName: PACKAGE_NAME,\n      url: resolveWixBookingsServicesV2ServicesServiceUrl({\n        protoPath: '/v2/services/clone',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'service.createdDate' },\n              { path: 'service.updatedDate' },\n              { path: 'service.media.items.image.urlExpirationDate' },\n              { path: 'service.media.mainMedia.image.urlExpirationDate' },\n              { path: 'service.media.coverMedia.image.urlExpirationDate' },\n              { path: 'service.bookingPolicy.createdDate' },\n              { path: 'service.bookingPolicy.updatedDate' },\n              { path: 'service.schedule.firstSessionStart' },\n              { path: 'service.schedule.lastSessionEnd' },\n              {\n                path: 'service.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              {\n                path: 'service.staffMemberDetails.staffMembers.mainMedia.image.urlExpirationDate',\n              },\n              { path: 'service.supportedSlugs.createdDate' },\n              { path: 'service.mainSlug.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              { path: 'service.locations.business.address.geocode.latitude' },\n              { path: 'service.locations.business.address.geocode.longitude' },\n              { path: 'service.locations.custom.address.geocode.latitude' },\n              { path: 'service.locations.custom.address.geocode.longitude' },\n              { path: 'service.locations.calculatedAddress.geocode.latitude' },\n              { path: 'service.locations.calculatedAddress.geocode.longitude' },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __cloneService;\n}\n"], "mappings": ";AAAA,SAAS,kBAAkB,yBAAyB;AACpD,SAAS,oBAAoB;AAC7B;AAAA,EACE;AAAA,EACA;AAAA,OACK;;;ACLP,SAAS,yBAAyB;AAClC,SAAS,oCAAoC;AAC7C,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,+CACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,SAAS,kDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AAOd,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,iBAAiB,SAA6C;AAC5E,WAAS,mBAAmB,EAAE,KAAK,GAAQ;AACzC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAOO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WACE;AAAA,QACF,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,kDAAkD;AAAA,QACrD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA8CO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,kDAAkD;AAAA,UAC1D,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,kCAAkC;AAAA,UAC1C,EAAE,MAAM,yDAAyD;AAAA,UACjE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,uBAAuB;AAAA,UAC/B,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,mCAAmC;AAAA,UAC3C,EAAE,MAAM,0DAA0D;AAAA,UAClE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,gCAAgC;AAAA,QAC1C;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,UAChE,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,wDAAwD;AAAA,UAChE,EAAE,MAAM,yDAAyD;AAAA,QACnE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,oCAAoC;AAAA,UAC9C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,WAAW,SAA6C;AACtE,WAAS,aAAa,EAAE,KAAK,GAAQ;AACnC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,MACjC,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,kDAAkD;AAAA,UAC1D,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,kCAAkC;AAAA,UAC1C,EAAE,MAAM,yDAAyD;AAAA,UACjE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,gBAAgB,CAAC;AAAA,MACnC;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,+BAA+B;AAAA,UACvC,EAAE,MAAM,+BAA+B;AAAA,UACvC,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,2DAA2D;AAAA,UACnE,EAAE,MAAM,4DAA4D;AAAA,UACpE,EAAE,MAAM,6CAA6C;AAAA,UACrD,EAAE,MAAM,6CAA6C;AAAA,UACrD,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,2CAA2C;AAAA,UACnD;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,wCAAwC;AAAA,QAClD;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,oCAAoC;AAAA,UAC9C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAiBO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,sBAAsB;AAAA,UAC9B,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,kDAAkD;AAAA,UAC1D,EAAE,MAAM,mDAAmD;AAAA,UAC3D,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,kCAAkC;AAAA,UAC1C,EAAE,MAAM,yDAAyD;AAAA,UACjE;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,+BAA+B;AAAA,QACzC;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sDAAsD;AAAA,UAC9D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,oDAAoD;AAAA,UAC5D,EAAE,MAAM,qDAAqD;AAAA,UAC7D,EAAE,MAAM,uDAAuD;AAAA,UAC/D,EAAE,MAAM,wDAAwD;AAAA,QAClE;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,QAAQ,kBAAkB,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,mBACd,SAC4B;AAC5B,WAAS,qBAAqB,EAAE,KAAK,GAAQ;AAC3C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,yCAAyC;AAAA,YACjD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,oCAAoC;AAAA,UAC9C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,2BACd,SAC4B;AAC5B,WAAS,6BAA6B,EAAE,KAAK,GAAQ;AACnD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoBO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,mCAAmC;AAAA,YAC3C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,wDAAwD;AAAA,YAChE;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA+BO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,yCAAyC;AAAA,UACjD,EAAE,MAAM,uCAAuC;AAAA,UAC/C;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,uBAAuB;AAAA,YAC/B,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,mCAAmC;AAAA,YAC3C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,gCAAgC;AAAA,UAC1C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,YAChE,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,wDAAwD;AAAA,YAChE;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAsCO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,uCAAuC;AAAA,YAC/C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,mDAAmD;AAAA,YAC3D;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,gDAAgD;AAAA,YACxD,EAAE,MAAM,gDAAgD;AAAA,YACxD,EAAE,MAAM,gDAAgD;AAAA,YACxD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA6CO,SAAS,kBAAkB,SAA6C;AAC7E,WAAS,oBAAoB,EAAE,KAAK,GAAQ;AAC1C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA4BO,SAAS,eAAe,SAA6C;AAC1E,WAAS,iBAAiB,EAAE,KAAK,GAAQ;AACvC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA2BO,SAAS,gBAAgB,SAA6C;AAC3E,WAAS,kBAAkB,EAAE,KAAK,GAAQ;AACxC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAoDO,SAAS,oBACd,SAC4B;AAC5B,WAAS,sBAAsB,EAAE,KAAK,GAAQ;AAC5C,UAAM,iBAAiB,eAAe,SAAS;AAAA,MAC7C;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,4CAA4C;AAAA,UACpD,EAAE,MAAM,6CAA6C;AAAA,UACrD,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,gDAAgD;AAAA,UACxD;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAcO,SAAS,6BACd,SAC4B;AAC5B,WAAS,+BAA+B,EAAE,KAAK,GAAQ;AACrD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAYO,SAAS,8BACd,SAC4B;AAC5B,WAAS,gCAAgC,EAAE,KAAK,GAAQ;AACtD,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAWO,SAAS,cAAc,SAA6C;AACzE,WAAS,gBAAgB,EAAE,KAAK,GAAQ;AACtC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,mBAAmB;AAAA,YAC3B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAaO,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAgEO,SAAS,aAAa,SAA6C;AACxE,WAAS,eAAe,EAAE,KAAK,GAAQ;AACrC,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,+CAA+C;AAAA,QAClD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,sBAAsB;AAAA,YAC9B,EAAE,MAAM,8CAA8C;AAAA,YACtD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,mDAAmD;AAAA,YAC3D,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,oCAAoC;AAAA,YAC5C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,kCAAkC;AAAA,YAC1C;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,+BAA+B;AAAA,UACzC;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,oDAAoD;AAAA,YAC5D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,wDAAwD;AAAA,UAClE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ADvrEA,SAAS,wCAAwC;AACjD,SAAS,wCAAwC;AACjD,SAAS,oCAAoC;AAC7C,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,4CAA4C;AACrD,SAAS,kBAAAC,uBAAsB;AA6IxB,IAAK,cAAL,kBAAKC,iBAAL;AAEL,EAAAA,aAAA,iBAAc;AAEd,EAAAA,aAAA,WAAQ;AAER,EAAAA,aAAA,YAAS;AANC,SAAAA;AAAA,GAAA;AAiIL,IAAK,WAAL,kBAAKC,cAAL;AAEL,EAAAA,UAAA,uBAAoB;AAEpB,EAAAA,UAAA,WAAQ;AAER,EAAAA,UAAA,YAAS;AAET,EAAAA,UAAA,YAAS;AAET,EAAAA,UAAA,YAAS;AAVC,SAAAA;AAAA,GAAA;AAiKL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,2BAAwB;AAKxB,EAAAA,cAAA,YAAS;AAKT,EAAAA,cAAA,cAAW;AAKX,EAAAA,cAAA,cAAW;AAhBD,SAAAA;AAAA,GAAA;AAm+BL,IAAK,sBAAL,kBAAKC,yBAAL;AACL,EAAAA,qBAAA,aAAU;AACV,EAAAA,qBAAA,uBAAoB;AACpB,EAAAA,qBAAA,YAAS;AACT,EAAAA,qBAAA,cAAW;AACX,EAAAA,qBAAA,SAAM;AALI,SAAAA;AAAA,GAAA;AAyQL,IAAK,oBAAL,kBAAKC,uBAAL;AAEL,EAAAA,mBAAA,6BAA0B;AAE1B,EAAAA,mBAAA,0BAAuB;AAEvB,EAAAA,mBAAA,2BAAwB;AANd,SAAAA;AAAA,GAAA;AAwRL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AAkLL,IAAK,WAAL,kBAAKC,cAAL;AACL,EAAAA,UAAA,WAAQ;AACR,EAAAA,UAAA,WAAQ;AAFE,SAAAA;AAAA,GAAA;AAQL,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,UAAO;AACP,EAAAA,eAAA,SAAM;AAFI,SAAAA;AAAA,GAAA;AAQL,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,aAAU;AACV,EAAAA,eAAA,aAAU;AAFA,SAAAA;AAAA,GAAA;AAqBL,IAAK,aAAL,kBAAKC,gBAAL;AACL,EAAAA,YAAA,yBAAsB;AAEtB,EAAAA,YAAA,oBAAiB;AAEjB,EAAAA,YAAA,SAAM;AAEN,EAAAA,YAAA,SAAM;AAPI,SAAAA;AAAA,GAAA;AA+CL,IAAK,wBAAL,kBAAKC,2BAAL;AACL,EAAAA,uBAAA,8BAA2B;AAE3B,EAAAA,uBAAA,WAAQ;AAER,EAAAA,uBAAA,WAAQ;AAER,EAAAA,uBAAA,YAAS;AAET,EAAAA,uBAAA,oBAAiB;AATP,SAAAA;AAAA,GAAA;AAuCL,IAAK,WAAL,kBAAKC,cAAL;AAEL,EAAAA,UAAA,sBAAmB;AAEnB,EAAAA,UAAA,UAAO;AAEP,EAAAA,UAAA,WAAQ;AAER,EAAAA,UAAA,UAAO;AAEP,EAAAA,UAAA,SAAM;AAEN,EAAAA,UAAA,UAAO;AAEP,EAAAA,UAAA,YAAS;AAET,EAAAA,UAAA,YAAS;AAhBC,SAAAA;AAAA,GAAA;AAkEL,IAAK,kBAAL,kBAAKC,qBAAL;AACL,EAAAA,iBAAA,8BAA2B;AAE3B,EAAAA,iBAAA,WAAQ;AAER,EAAAA,iBAAA,WAAQ;AAER,EAAAA,iBAAA,YAAS;AAET,EAAAA,iBAAA,oBAAiB;AAEjB,EAAAA,iBAAA,YAAS;AAXC,SAAAA;AAAA,GAAA;AAyEL,IAAK,OAAL,kBAAKC,UAAL;AAEL,EAAAA,MAAA,QAAK;AAEL,EAAAA,MAAA,SAAM;AAJI,SAAAA;AAAA,GAAA;AAuWL,IAAK,kBAAL,kBAAKC,qBAAL;AAEL,EAAAA,iBAAA,6BAA0B;AAE1B,EAAAA,iBAAA,0BAAuB;AAJb,SAAAA;AAAA,GAAA;AAgPL,IAAK,SAAL,kBAAKC,YAAL;AACL,EAAAA,QAAA,yBAAsB;AAEtB,EAAAA,QAAA,8BAA2B;AAE3B,EAAAA,QAAA,sBAAmB;AAKnB,EAAAA,QAAA,YAAS;AAVC,SAAAA;AAAA,GAAA;AAsIL,IAAK,mBAAL,kBAAKC,sBAAL;AAEL,EAAAA,kBAAA,wBAAqB;AAErB,EAAAA,kBAAA,sCAAmC;AAEnC,EAAAA,kBAAA,yBAAsB;AANZ,SAAAA;AAAA,GAAA;AAmCL,IAAK,cAAL,kBAAKC,iBAAL;AAKL,EAAAA,aAAA,0BAAuB;AAEvB,EAAAA,aAAA,UAAO;AAPG,SAAAA;AAAA,GAAA;AA8CL,IAAK,SAAL,kBAAKC,YAAL;AAEL,EAAAA,QAAA,aAAU;AAEV,EAAAA,QAAA,aAAU;AAJA,SAAAA;AAAA,GAAA;AAUL,IAAK,4BAAL,kBAAKC,+BAAL;AAEL,EAAAA,2BAAA,aAAU;AAEV,EAAAA,2BAAA,aAAU;AAEV,EAAAA,2BAAA,aAAU;AANA,SAAAA;AAAA,GAAA;AAmGL,IAAK,cAAL,kBAAKC,iBAAL;AAEL,EAAAA,aAAA,eAAY;AAEZ,EAAAA,aAAA,aAAU;AAEV,EAAAA,aAAA,eAAY;AANF,SAAAA;AAAA,GAAA;AA+BL,IAAK,QAAL,kBAAKC,WAAL;AACL,EAAAA,OAAA,aAAU;AACV,EAAAA,OAAA,aAAU;AACV,EAAAA,OAAA,aAAU;AAHA,SAAAA;AAAA,GAAA;AAiBL,IAAK,WAAL,kBAAKC,cAAL;AACL,EAAAA,UAAA,uBAAoB;AACpB,EAAAA,UAAA,YAAS;AACT,EAAAA,UAAA,YAAS;AACT,EAAAA,UAAA,YAAS;AAET,EAAAA,UAAA,sBAAmB;AANT,SAAAA;AAAA,GAAA;AAuML,IAAK,gBAAL,kBAAKC,mBAAL;AACL,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,aAAU;AAHA,SAAAA;AAAA,GAAA;AAwDL,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,aAAU;AACV,EAAAA,WAAA,eAAY;AACZ,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,cAAW;AACX,EAAAA,WAAA,YAAS;AAPC,SAAAA;AAAA,GAAA;AA8DL,IAAK,mBAAL,kBAAKC,sBAAL;AACL,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,eAAY;AACZ,EAAAA,kBAAA,kBAAe;AAHL,SAAAA;AAAA,GAAA;AA+YZ,eAAsBC,kBACpB,YACA,SAGA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,WAAW,SAAS;AAAA,EACtB,CAAC;AAED,QAAM,UACmC,iBAAiB,OAAO;AAEjE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,WAAW;AAAA,QACb;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc,SAAS;AAAA,IAC1B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAuBA,eAAsBC,kBACpB,cACA,SAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,WAAW,SAAS;AAAA,EACtB,CAAC;AAED,QAAM,UACmC,iBAAiB,OAAO;AAEjE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,cAAc;AAAA,UACd,WAAW;AAAA,QACb;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,gBAAgB,SAAS;AAAA,IAC5B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAwBA,eAAsBC,kBACpB,YACA,SAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,WAAW,SAAS;AAAA,EACtB,CAAC;AAED,QAAM,UACmC,iBAAiB,OAAO;AAEjE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,YAAY;AAAA,UACZ,WAAW;AAAA,QACb;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,cAAc,SAAS;AAAA,IAC1B;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsBA,eAAsBC,4BACpB,WACA,SAGA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,UAAU,SAAS;AAAA,EACrB,CAAC;AAED,QAAM,UACmC,2BAA2B,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,UAAU;AAAA,QACZ;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyBA,eAAsBC,mBACpB,WACA,SAKA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,SAAS,SAAS;AAAA,IAClB,UAAU,SAAS;AAAA,EACrB,CAAC;AAED,QAAM,UACmC,kBAAkB,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAoEA,eAAsBC,eAAc,SA8DlC;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAUhC;AAAA,IACd,sCAAsC,EAAE,QAAiB,CAAC;AAAA,IAC1D;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,gCAAgC;AAAA,UACxC,EAAE,MAAM,iCAAiC;AAAA,UACzC,EAAE,MAAM,uCAAuC;AAAA,UAC/C,EAAE,MAAM,0DAA0D;AAAA,QACpE;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,mCAAmC;AAAA,QAC7C;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UAAiD,cAAc,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLA,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AAAA,EACL,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,SAAS,OAAO;AAAA,QAC5C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAgBA,eAAsBiC,oBACpB,UACA,SAmBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAUjC;AAAA,IACd,sCAAsC;AAAA,MACpC;AAAA,MACA,cAAc,SAAS;AAAA,IACzB,CAAC;AAAA,IACD;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,6BAA6B;AAAA,UACrC,EAAE,MAAM,iCAAiC;AAAA,UACzC,EAAE,MAAM,kCAAkC;AAAA,UAC1C,EAAE,MAAM,wCAAwC;AAAA,UAChD,EAAE,MAAM,2DAA2D;AAAA,QACrE;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,uCAAuC;AAAA,UAC/C,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,oCAAoC;AAAA,QAC9C;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,6BAA6B;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UACmC,mBAAmB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLA,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2CAA2C;AAAA,YACnD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,wCAAwC;AAAA,UAClD;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,UAC3C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,YAAY,SAAS;AAAA,IACxB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAiBA,eAAsBkC,YACpB,WA6DA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAAiD,WAAW,OAAO;AAEzE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLlC,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AAAA,EACL,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,WAAW,OAAO;AAAA,QAC9C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,WAAW;AAAA,IACd;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA6BA,eAAsBmC,eACpB,KACA,SA+DA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAUnC;AAAA,IACd,sCAAsC,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC;AAAA,IAC1E;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,gCAAgC;AAAA,UACxC,EAAE,MAAM,iCAAiC;AAAA,UACzC,EAAE,MAAM,uCAAuC;AAAA,UAC/C,EAAE,MAAM,0DAA0D;AAAA,QACpE;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,mCAAmC;AAAA,QAC7C;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UAAiD,cAAc,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLA,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,GAAG;AAAA,EACL,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,EAAE,SAAS,OAAO;AAAA,QAC1C,0BAA0B,EAAE,cAAc,OAAO;AAAA,QACjD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,SAAS;AAAA,IACnB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA8JA,eAAsBoC,oBACpB,SAyBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAUpC;AAAA,IACd,sCAAsC;AAAA,MACpC,UAAU,SAAS;AAAA,MACnB,cAAc,SAAS;AAAA,IACzB,CAAC;AAAA,IACD;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,yCAAyC;AAAA,UACjD,EAAE,MAAM,0CAA0C;AAAA,UAClD,EAAE,MAAM,gDAAgD;AAAA,UACxD;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,+CAA+C;AAAA,UACvD,EAAE,MAAM,8CAA8C;AAAA,UACtD,EAAE,MAAM,4CAA4C;AAAA,QACtD;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,oCAAoC;AAAA,UAC5C,EAAE,MAAM,qCAAqC;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UACmC,mBAAmB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLA,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2CAA2C;AAAA,YACnD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,wCAAwC;AAAA,UAClD;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,UAC3C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,UAAU;AAAA,UACV,cAAc;AAAA,QAChB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmCA,eAAsBqC,4BACpB,QACA,SAC2E;AAE3E,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAUrC;AAAA,IACd,sCAAsC;AAAA,MACpC;AAAA,MACA,SAAS,SAAS;AAAA,IACpB,CAAC;AAAA,IACD;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,4BAA4B;AAAA,UACpC,EAAE,MAAM,gCAAgC;AAAA,UACxC,EAAE,MAAM,iCAAiC;AAAA,UACzC,EAAE,MAAM,uCAAuC;AAAA,UAC/C,EAAE,MAAM,0DAA0D;AAAA,QACpE;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,sCAAsC;AAAA,UAC9C,EAAE,MAAM,qCAAqC;AAAA,UAC7C,EAAE,MAAM,mCAAmC;AAAA,QAC7C;AAAA,MACF;AAAA,MACA;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,2BAA2B;AAAA,UACnC,EAAE,MAAM,4BAA4B;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UACmC,2BAA2B,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,QAAQ,SAAS,eAAe;AAAA,QACpE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU,SAAS;AAAA,IACtB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAqBA,eAAsBsC,eACpB,WACA,SACe;AAEf,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,wCACE,SAAS;AAAA,IACX,yBAAyB,SAAS;AAAA,EACpC,CAAC;AAED,QAAM,UAAiD,cAAc,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAAA,EACjC,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,wCACE;AAAA,UACF,yBAAyB;AAAA,QAC3B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAiCA,eAAsBC,oBACpB,KACA,SAmBA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,wCACE,SAAS;AAAA,IACX,yBAAyB,SAAS;AAAA,EACpC,CAAC;AAED,QAAM,UACmC,mBAAmB,OAAO;AAEnE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLvC,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,4CAA4C;AAAA,YACpD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2CAA2C;AAAA,YACnD,EAAE,MAAM,0CAA0C;AAAA,YAClD,EAAE,MAAM,wCAAwC;AAAA,UAClD;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,UAC3C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,KAAK;AAAA,UACL,wCACE;AAAA,UACF,yBAAyB;AAAA,QAC3B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO,SAAS;AAAA,IACnB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAwCA,eAAsBwC,4BACpB,QACA,SAC2E;AAE3E,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,wCACE,SAAS;AAAA,IACX,yBAAyB,SAAS;AAAA,EACpC,CAAC;AAED,QAAM,UACmC,2BAA2B,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,QAAQ;AAAA,UACR,wCACE;AAAA,UACF,yBAAyB;AAAA,QAC3B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,UAAU,SAAS;AAAA,IACtB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAmCO,SAASC,iBAAsC;AAEpD,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,SAAO,aAKL;AAAA,IACA,MAAM,OAAO,YAAkC;AAC7C,YAAM,UACmC,cAAc,OAAO;AAE9D,mBAAa,aAAa;AAC1B,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,qBAAa,YAAY,MAAM;AAC/B,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,qBAAa,UAAU,GAAG;AAC1B,cAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,UAAyC;AAC5D,YAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AACvB,aAAO,sCAAsC;AAAA,QAC3C,GAAG,OAAO,CAAC;AAAA,QACX,OAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,CAAC,EAAE,KAAK,MAA2C;AACtE,YAAM,kBAAkB;AAAA,QACtBzC,gBAAe,MAAM;AAAA,UACnB;AAAA,YACE,aAAa;AAAA,YACb,OAAO;AAAA,cACL,EAAE,MAAM,6BAA6B;AAAA,cACrC,EAAE,MAAM,iCAAiC;AAAA,cACzC,EAAE,MAAM,kCAAkC;AAAA,cAC1C,EAAE,MAAM,wCAAwC;AAAA,cAChD;AAAA,gBACE,MAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,aAAa;AAAA,YACb,OAAO;AAAA,cACL,EAAE,MAAM,uCAAuC;AAAA,cAC/C,EAAE,MAAM,sCAAsC;AAAA,cAC9C,EAAE,MAAM,oCAAoC;AAAA,YAC9C;AAAA,UACF;AAAA,UACA;AAAA,YACE,aAAa;AAAA,YACb,OAAO;AAAA,cACL,EAAE,MAAM,4BAA4B;AAAA,cACpC,EAAE,MAAM,4BAA4B;AAAA,cACpC,EAAE,MAAM,6BAA6B;AAAA,YACvC;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,QACL,OAAO,iBAAiB;AAAA,QACxB,gBAAgB,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC,QAAiB;AAClC,YAAM,mBAAmB,kBAAkB,KAAK;AAAA,QAC9C,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B,CAAC;AAED,YAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,qBAAqB,CAAC;AAAA,EACxB,CAAC;AACH;AAmVA,eAAsB0C,gBACpB,QAyCA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC,EAAE,OAAe,CAAC;AAExE,QAAM,UACmC,eAAe,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACL1C,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,6BAA6B;AAAA,YACrC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,kCAAkC;AAAA,YAC1C,EAAE,MAAM,wCAAwC;AAAA,YAChD;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,oCAAoC;AAAA,UAC9C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,6BAA6B;AAAA,UACvC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,OAAO;AAAA,QAC3C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,QAAQ;AAAA,IACX;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkRA,eAAsB2C,eACpB,OA8BA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC,EAAE,MAAa,CAAC;AAEtE,QAAM,UAAiD,cAAc,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACL3C,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,6CAA6C;AAAA,YACrD,EAAE,MAAM,iDAAiD;AAAA,YACzD,EAAE,MAAM,kDAAkD;AAAA,YAC1D,EAAE,MAAM,wDAAwD;AAAA,YAChE;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,0DAA0D;AAAA,YAClE;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uDAAuD;AAAA,YAC/D,EAAE,MAAM,sDAAsD;AAAA,YAC9D,EAAE,MAAM,oDAAoD;AAAA,YAC5D;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,4CAA4C;AAAA,YACpD,EAAE,MAAM,6CAA6C;AAAA,YACrD,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,sDAAsD;AAAA,UAChE;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,OAAO,OAAO;AAAA,QAC1C,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,OAAO;AAAA,IACV;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsDA,eAAsB4C,mBACpB,OACA,SAcA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,mBAAmB,SAAS;AAAA,EAC9B,CAAC;AAED,QAAM,UACmC,kBAAkB,OAAO;AAElE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,OAAO;AAAA,UACP,mBAAmB;AAAA,QACrB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS,SAAS;AAAA,IACrB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAyBA,eAAsBC,eACpB,SAC8D;AAE9D,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UAAiD,cAAc,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA0CA,eAAsBC,gBACpB,SAeA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACmC,eAAe,OAAO;AAE/D,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACL9C,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,gDAAgD;AAAA,YACxD,EAAE,MAAM,+CAA+C;AAAA,YACvD,EAAE,MAAM,6CAA6C;AAAA,UACvD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAqCA,eAAsB+C,iBACpB,SAOA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD,QAAQ,SAAS;AAAA,EACnB,CAAC;AAED,QAAM,UACmC,gBAAgB,OAAO;AAEhE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,QAAQ,cAAc;AAAA,QAClD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS;AAAA,IACZ;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkEA,eAAsBC,qBACpB,WACA,WACA,SA6DA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAUhD;AAAA,IACd,sCAAsC;AAAA,MACpC;AAAA,MACA;AAAA,MACA,+BAA+B,SAAS;AAAA,MACxC,yBAAyB,SAAS;AAAA,IACpC,CAAC;AAAA,IACD;AAAA,MACE;AAAA,QACE,aAAa;AAAA,QACb,OAAO;AAAA,UACL,EAAE,MAAM,8BAA8B;AAAA,UACtC,EAAE,MAAM,6BAA6B;AAAA,UACrC,EAAE,MAAM,2BAA2B;AAAA,UACnC;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UACmC,oBAAoB,OAAO;AAEpE,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLA,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,WAAW;AAAA,UACX,+BAA+B;AAAA,UAC/B,yBAAyB;AAAA,QAC3B;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,aAAa,SAAS;AAAA,IACtC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAsCA,eAAsBiD,8BACpB,WACA,gBA+DA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,UACmC;AAAA,IACrC;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLjD,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,WAAW,QAAQ,gBAAgB,OAAO;AAAA,QACtE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,gBAAgB;AAAA,IAChC;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AAkBA,eAAsBkD,+BACpB,WACA,SA+DA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,gBAAgB,SAAS;AAAA,EAC3B,CAAC;AAED,QAAM,UACmC;AAAA,IACrC;AAAA,EACF;AAEF,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLlD,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B;AAAA,UACxB,WAAW;AAAA,UACX,gBAAgB;AAAA,QAClB;AAAA,QACA,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA4BA,eAAsBmD,eACpB,WACA,SAiEA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,MAAM,SAAS;AAAA,EACjB,CAAC;AAED,QAAM,UAAiD,cAAc,OAAO;AAE5E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLnD,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,WAAW,QAAQ,MAAM,YAAY;AAAA,QACjE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA2BA,eAAsBoD,cACpB,WACA,SACwE;AAExE,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,IACA,MAAM,SAAS;AAAA,EACjB,CAAC;AAED,QAAM,UAAiD,aAAa,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO,wCAAwC,OAAO,IAAI;AAAA,EAC5D,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,WAAW,QAAQ,MAAM,YAAY;AAAA,QACjE,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,aAAa,SAAS;AAAA,IACzB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;AA8EA,eAAsBC,cACpB,iBA8DA;AAEA,QAAM,EAAE,YAAY,YAAY,IAAI,UAAU,CAAC;AAK/C,QAAM,UAAU,sCAAsC;AAAA,IACpD;AAAA,EACF,CAAC;AAED,QAAM,UAAiD,aAAa,OAAO;AAE3E,eAAa,aAAa;AAC1B,MAAI;AACF,UAAM,SAAS,MAAM,WAAW,QAAQ,OAAO;AAC/C,iBAAa,YAAY,MAAM;AAE/B,WAAO;AAAA,MACLrD,gBAAe,OAAO,MAAM;AAAA,QAC1B;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,4BAA4B;AAAA,YACpC,EAAE,MAAM,gCAAgC;AAAA,YACxC,EAAE,MAAM,iCAAiC;AAAA,YACzC,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,0DAA0D;AAAA,UACpE;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,sCAAsC;AAAA,YAC9C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,UAC7C;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,2BAA2B;AAAA,YACnC,EAAE,MAAM,4BAA4B;AAAA,UACtC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,KAAU;AACjB,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,QACE,wBAAwB,CAAC;AAAA,QACzB,0BAA0B,EAAE,iBAAiB,OAAO;AAAA,QACpD,yBAAyB;AAAA,MAC3B;AAAA,MACA,CAAC,iBAAiB;AAAA,IACpB;AACA,iBAAa,UAAU,GAAG;AAE1B,UAAM;AAAA,EACR;AACF;", "names": ["payload", "transformPaths", "ServiceType", "RateType", "LocationType", "WebhookIdentityType", "V2RequestedFields", "SortOrder", "SortType", "SortDirection", "<PERSON><PERSON><PERSON><PERSON>", "ScalarType", "NestedAggregationType", "Interval", "AggregationType", "Mode", "RequestedFields", "Action", "InvalidSlugError", "<PERSON><PERSON>E<PERSON><PERSON>", "Status", "CategoryNotificationEvent", "BenefitType", "Event", "CrudType", "PlacementType", "DayOfWeek", "ResolutionMethod", "createAddOnGroup", "deleteAddOnGroup", "updateAddOnGroup", "listAddOnGroupsByServiceId", "setAddOnsForGroup", "createService", "bulkCreateServices", "getService", "updateService", "bulkUpdateServices", "bulkUpdateServicesByFilter", "deleteService", "bulkDeleteServices", "bulkDeleteServicesByFilter", "queryServices", "searchServices", "queryPolicies", "queryBookingForms", "countServices", "queryLocations", "queryCategories", "setServiceLocations", "enablePricingPlansForService", "disablePricingPlansForService", "setCustomSlug", "validateSlug", "cloneService"]}