{"version": 3, "sources": ["../../src/bookings-reader-v2-extended-booking-extended-bookings.http.ts", "../../src/bookings-reader-v2-extended-booking-extended-bookings.meta.ts"], "sourcesContent": ["import { transformRESTFloatToSDKFloat } from '@wix/sdk-runtime/transformations/float';\nimport { transformRESTTimestampToSDKTimestamp } from '@wix/sdk-runtime/transformations/timestamp';\nimport { transformPaths } from '@wix/sdk-runtime/transformations/transform-paths';\nimport { resolveUrl } from '@wix/sdk-runtime/rest-modules';\nimport { ResolveUrlOpts } from '@wix/sdk-runtime/rest-modules';\nimport { RequestOptionsFactory } from '@wix/sdk-types';\n\nfunction resolveComWixpressBookingsReaderV2BookingsReaderUrl(\n  opts: Omit<ResolveUrlOpts, 'domainToMappings'>\n) {\n  const domainToMappings = {\n    _: [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'editor._base_domain': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'manage._base_domain_': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'www.wixapis.com': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n      {\n        srcPath: '/bookings/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'editor._base_domain_': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'blocks._base_domain_': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n    'create.editorx': [\n      {\n        srcPath: '/_api/bookings-reader',\n        destPath: '',\n      },\n    ],\n  };\n\n  return resolveUrl(Object.assign(opts, { domainToMappings }));\n}\n\nconst PACKAGE_NAME = '@wix/auto_sdk_bookings_extended-bookings';\n\n/**\n * > **Deprecation Notice**\n * >\n * > **This endpoint has been replaced with [Query Extended Bookings](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/query-extended-bookings) and will be removed on May 31, 2025.**\n * > **If your app uses this endpoint, we recommend updating your code as soon as possible.**\n *\n *\n * Retrieves a list of bookings, given the provided paging, filtering, and sorting.\n *\n *\n * You can also retrieve information about which actions the customer can perform\n * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.\n *\n * Query Bookings runs with these defaults:\n *\n * - `createdDate` sorted in `DESC` order\n * - `cursorPaging.limit` is `50`\n *\n * You can check the overview about all\n * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)\n * for more information.\n *\n * `query.fields` and `query.fieldsets` aren't supported for this endpoint.\n *\n * When using filters for dates, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n *\n * Bookings belonging to a schedule don't have a `sessionId`. Therefore you\n * must use the `sessionId` filter that isn't part of the `query` object to\n * filter bookings for courses.\n *\n * To learn about working with _Query_ endpoints, see\n * [API Query Language](https://dev.wix.com/api/rest/getting-started/api-query-language),\n * [Sorting and Paging](https://dev.wix.com/api/rest/getting-started/sorting-and-paging),\n * and [Field Projection](https://dev.wix.com/api/rest/getting-started/field-projection).\n *\n * When calling Query Bookings, the retrieved data may not contain your most recent changes. See\n * [Wix-data and Eventual Consistency](https://dev.wix.com/docs/rest/business-solutions/cms/eventual-consistency)\n * for more information.\n * @deprecated\n */\nexport function query(payload: object): RequestOptionsFactory<any> {\n  function __query({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.reader.v2.extended_booking',\n      method: 'POST' as any,\n      methodFqn: 'com.wixpress.bookings.reader.v2.BookingsReader.Query',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({\n        protoPath: '/v2/extended-booking/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'extendedBookings.booking.createdDate' },\n              { path: 'extendedBookings.booking.startDate' },\n              { path: 'extendedBookings.booking.endDate' },\n              { path: 'extendedBookings.booking.updatedDate' },\n              { path: 'extendedBookings.booking.canceledDate' },\n              { path: 'extendedBookings.transactions.payments.createdDate' },\n              { path: 'extendedBookings.transactions.payments.updatedDate' },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate',\n              },\n              { path: 'extendedBookings.transactions.refunds.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __query;\n}\n\n/**\n * Retrieves a list of bookings, including additional extended information, given the provided paging, filtering, and sorting.\n *\n * `queryExtendedBookings()` doesn't use a query builder, instead it uses [API query language](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#with-the-api-query-language).\n *\n * Up to 100 extended bookings can be returned per request.\n *\n * `queryExtendedBookings()` runs with these defaults, which you can override:\n *\n * - `createdDate` sorted in `DESC` order\n * - `cursorPaging.limit` is `50`\n *\n *\n * You can retrieve information about which actions the customer can perform\n * for the bookings. To do so, pass `withBookingAllowedActions` as `true`.\n *\n *\n * For field support, see\n * [supported filters](https://www.wix.com/velo/reference/wix-bookings-v2/extendedbookings/supported-filters)\n * for more information.\n *\n * You can specify a filter only once per query. If you specify a filter\n * more than once, only the first filter determines the extended bookings that are returned.\n *\n * When filtering by date, you must use [UTC time](https://en.wikipedia.org/wiki/Coordinated_Universal_Time).\n */\nexport function queryExtendedBookings(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __queryExtendedBookings({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.reader.v2.extended_booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.reader.v2.BookingsReader.QueryExtendedBookings',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({\n        protoPath: '/v2/extended-bookings/query',\n        data: payload,\n        host,\n      }),\n      data: payload,\n      transformResponse: (payload: any) =>\n        transformPaths(payload, [\n          {\n            transformFn: transformRESTTimestampToSDKTimestamp,\n            paths: [\n              { path: 'extendedBookings.booking.createdDate' },\n              { path: 'extendedBookings.booking.startDate' },\n              { path: 'extendedBookings.booking.endDate' },\n              { path: 'extendedBookings.booking.updatedDate' },\n              { path: 'extendedBookings.booking.canceledDate' },\n              { path: 'extendedBookings.transactions.payments.createdDate' },\n              { path: 'extendedBookings.transactions.payments.updatedDate' },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.authorizedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.captures.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.void.voidedDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.authorizationDetails.scheduledAction.executionDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.createdDate',\n              },\n              {\n                path: 'extendedBookings.transactions.payments.regularPaymentDetails.chargebacks.updatedDate',\n              },\n              { path: 'extendedBookings.transactions.refunds.createdDate' },\n            ],\n          },\n          {\n            transformFn: transformRESTFloatToSDKFloat,\n            paths: [\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.latitude',\n              },\n              {\n                path: 'extendedBookings.booking.contactDetails.fullAddress.geocode.longitude',\n              },\n            ],\n          },\n        ]),\n    };\n\n    return metadata;\n  }\n\n  return __queryExtendedBookings;\n}\n\n/**\n * Counts the number of bookings matching the specified filters.\n *\n *\n * For field support see\n * [supported filters](https://dev.wix.com/api/rest/wix-bookings/bookings-reader-v2/supported-filters)\n * for more information.\n */\nexport function countExtendedBookings(\n  payload: object\n): RequestOptionsFactory<any> {\n  function __countExtendedBookings({ host }: any) {\n    const metadata = {\n      entityFqdn: 'wix.bookings.reader.v2.extended_booking',\n      method: 'POST' as any,\n      methodFqn:\n        'com.wixpress.bookings.reader.v2.BookingsReader.CountExtendedBookings',\n      packageName: PACKAGE_NAME,\n      url: resolveComWixpressBookingsReaderV2BookingsReaderUrl({\n        protoPath: '/v2/extended-bookings/count',\n        data: payload,\n        host,\n      }),\n      data: payload,\n    };\n\n    return metadata;\n  }\n\n  return __countExtendedBookings;\n}\n", "import * as ambassadorWixBookingsReaderV2ExtendedBooking from './bookings-reader-v2-extended-booking-extended-bookings.http.js';\nimport * as ambassadorWixBookingsReaderV2ExtendedBookingTypes from './bookings-reader-v2-extended-booking-extended-bookings.types.js';\nimport * as ambassadorWixBookingsReaderV2ExtendedBookingUniversalTypes from './bookings-reader-v2-extended-booking-extended-bookings.universal.js';\n\nexport type __PublicMethodMetaInfo<\n  K = string,\n  M = unknown,\n  T = unknown,\n  S = unknown,\n  Q = unknown,\n  R = unknown\n> = {\n  getUrl: (context: any) => string;\n  httpMethod: K;\n  path: string;\n  pathParams: M;\n  __requestType: T;\n  __originalRequestType: S;\n  __responseType: Q;\n  __originalResponseType: R;\n};\n\nexport function query(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsReaderV2ExtendedBookingUniversalTypes.QueryExtendedBookingRequest,\n  ambassadorWixBookingsReaderV2ExtendedBookingTypes.QueryExtendedBookingRequest,\n  ambassadorWixBookingsReaderV2ExtendedBookingUniversalTypes.QueryExtendedBookingResponse,\n  ambassadorWixBookingsReaderV2ExtendedBookingTypes.QueryExtendedBookingResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsReaderV2ExtendedBooking.query(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/extended-booking/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function queryExtendedBookings(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsReaderV2ExtendedBookingUniversalTypes.QueryExtendedBookingsRequest,\n  ambassadorWixBookingsReaderV2ExtendedBookingTypes.QueryExtendedBookingsRequest,\n  ambassadorWixBookingsReaderV2ExtendedBookingUniversalTypes.QueryExtendedBookingsResponse,\n  ambassadorWixBookingsReaderV2ExtendedBookingTypes.QueryExtendedBookingsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsReaderV2ExtendedBooking.queryExtendedBookings(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/extended-bookings/query',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n\nexport function countExtendedBookings(): __PublicMethodMetaInfo<\n  'POST',\n  {},\n  ambassadorWixBookingsReaderV2ExtendedBookingUniversalTypes.CountExtendedBookingsRequest,\n  ambassadorWixBookingsReaderV2ExtendedBookingTypes.CountExtendedBookingsRequest,\n  ambassadorWixBookingsReaderV2ExtendedBookingUniversalTypes.CountExtendedBookingsResponse,\n  ambassadorWixBookingsReaderV2ExtendedBookingTypes.CountExtendedBookingsResponse\n> {\n  const payload = {} as any;\n\n  const getRequestOptions =\n    ambassadorWixBookingsReaderV2ExtendedBooking.countExtendedBookings(payload);\n\n  const getUrl = (context: any): string => {\n    const { url } = getRequestOptions(context);\n    return url!;\n  };\n\n  return {\n    getUrl,\n    httpMethod: 'POST',\n    path: '/v2/extended-bookings/count',\n    pathParams: {},\n    __requestType: null as any,\n    __originalRequestType: null as any,\n    __responseType: null as any,\n    __originalResponseType: null as any,\n  };\n}\n"], "mappings": ";AAAA,SAAS,oCAAoC;AAC7C,SAAS,4CAA4C;AACrD,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAI3B,SAAS,oDACP,MACA;AACA,QAAM,mBAAmB;AAAA,IACvB,GAAG;AAAA,MACD;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,MACjB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,wBAAwB;AAAA,MACtB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAW,OAAO,OAAO,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAC7D;AAEA,IAAM,eAAe;AA0Cd,SAAS,MAAM,SAA6C;AACjE,WAAS,QAAQ,EAAE,KAAK,GAAQ;AAC9B,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,YAC3C,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,wCAAwC;AAAA,YAChD,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,qDAAqD;AAAA,YAC7D;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AA4BO,SAAS,sBACd,SAC4B;AAC5B,WAAS,wBAAwB,EAAE,KAAK,GAAQ;AAC9C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,MACN,mBAAmB,CAACA,aAClB,eAAeA,UAAS;AAAA,QACtB;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,qCAAqC;AAAA,YAC7C,EAAE,MAAM,mCAAmC;AAAA,YAC3C,EAAE,MAAM,uCAAuC;AAAA,YAC/C,EAAE,MAAM,wCAAwC;AAAA,YAChD,EAAE,MAAM,qDAAqD;AAAA,YAC7D,EAAE,MAAM,qDAAqD;AAAA,YAC7D;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA,EAAE,MAAM,oDAAoD;AAAA,UAC9D;AAAA,QACF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,OAAO;AAAA,YACL;AAAA,cACE,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACL;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAUO,SAAS,sBACd,SAC4B;AAC5B,WAAS,wBAAwB,EAAE,KAAK,GAAQ;AAC9C,UAAM,WAAW;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WACE;AAAA,MACF,aAAa;AAAA,MACb,KAAK,oDAAoD;AAAA,QACvD,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,MACD,MAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACjRO,SAASC,SAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACyC,MAAM,OAAO;AAE5D,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,yBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACyC,sBAAsB,OAAO;AAE5E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;AAEO,SAASC,yBAOd;AACA,QAAM,UAAU,CAAC;AAEjB,QAAM,oBACyC,sBAAsB,OAAO;AAE5E,QAAM,SAAS,CAAC,YAAyB;AACvC,UAAM,EAAE,IAAI,IAAI,kBAAkB,OAAO;AACzC,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,IACb,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,EAC1B;AACF;", "names": ["payload", "query", "queryExtendedBookings", "countExtendedBookings"]}