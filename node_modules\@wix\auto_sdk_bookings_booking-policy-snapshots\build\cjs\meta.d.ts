import { ListPolicySnapshotsByBookingIdsRequest as ListPolicySnapshotsByBookingIdsRequest$1, ListPolicySnapshotsByBookingIdsResponse as ListPolicySnapshotsByBookingIdsResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

/**
 * The `bookingPolicySnapshot` object is the version of a service's booking policy
 * at the time a booking is created. This allows you for example to charge
 * customers the correct cancellation fee even after a service's cancellation
 * policy has been updated.
 */
interface BookingPolicySnapshot {
    /**
     * Booking policy snapshot ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * ID of the booking that's associated with this policy snapshot.
     * @format GUID
     */
    bookingId?: string | null;
    /**
     * Snapshot of the *booking policy*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/booking-policies/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/policies/booking-policies/introduction)).
     * at the time the corresponding booking was created.
     */
    policy?: BookingPolicy;
    /**
     * Date and time the booking policy snapshot was created in `YYYY-MM-DDThh:mm:ssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
}
/**
 * `BookingPolicy` is the main entity of `BookingPolicyService` and specifies a set of rules for booking a service
 * by visitors and members.
 *
 * Each `BookingPolicy` consists of a number of sub-policies. When the Bookings App is provisioned to a meta site then a
 * default `BookingPolicy` will be created with defaults for each of these sub-policies. This also applies when a request
 * is received to create a new `BookingPolicy` and one or more of these sub-policies are not provided.
 *
 * Sub-policies are defined in separate objects as specified below.
 * - The `CancellationPolicy` object defines the policy for canceling a booked session.
 * - The `ReschedulePolicy` object defines the policy for rescheduling booked session.
 *
 * By default each sub-policy is disabled. A more detailed specification of the default settings of each sub-policy
 * can be found in the description of the corresponding object.
 *
 * Partial updates are supported on the main entity level, however in order to update a sub-policy the client needs to provide the whole sub-policy object.
 */
interface BookingPolicy {
    /**
     * Booking policy ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Revision number, which increments by 1 each time the booking policy is updated. To prevent conflicting changes, the current `revision` must be passed when updating the booking policy.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the booking policy was created.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Date and time the booking policy was updated.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Name of the booking policy.
     * @maxLength 400
     */
    name?: string | null;
    /** Custom description for the booking policy and whether the booking policy is displayed to the participant. */
    customPolicyDescription?: PolicyDescription;
    /**
     * Whether the booking policy is the default.
     * @readonly
     */
    default?: boolean | null;
    /** Rule for canceling a booking. */
    cancellationPolicy?: CancellationPolicy;
    /** Rule for rescheduling a booking. */
    reschedulePolicy?: ReschedulePolicy;
    /** Rules for cancellation fees. */
    cancellationFeePolicy?: CancellationFeePolicy;
    /** Rule for saving credit card details. */
    saveCreditCardPolicy?: SaveCreditCardPolicy;
}
/** A description of the booking policy to display to participants. */
interface PolicyDescription {
    /**
     * Whether the description is displayed to the participant. `true` means the
     * description is displayed.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Description of the booking policy.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    description?: string;
    /**
     * Description of the translated booking policy.
     *
     * Default: Empty
     * Max length: 2500 characters
     * @maxLength 2500
     */
    descriptionTranslated?: string | null;
}
/** The rule for canceling a booked session. */
interface CancellationPolicy {
    /**
     * Whether customers can cancel the booking. `true` means customers can cancel
     * the booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest cancellation time. `false` means
     * customers can cancel the booking until the last minute before the course or
     * session starts.
     *
     * Default: `false`
     */
    limitLatestCancellation?: boolean;
    /**
     * Minimum number of minutes before the start of the session customers can cancel.
     * For courses, this refers to the start of the first course session.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestCancellationInMinutes?: number;
}
/** The rule for rescheduling a booked session. */
interface ReschedulePolicy {
    /**
     * Whether customers can reschedule a booking for an appointment-based service.
     * `true` means customers can reschedule.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Whether there's a limit on the latest supported rescheduling time. `false`
     * means customers can reschedule until the last minute before the session start.
     *
     * Default: `false`
     */
    limitLatestReschedule?: boolean;
    /**
     * Minimum number of minutes before the session start session customers can
     * reschedule their booking.
     *
     * Default: `1440` minutes (1 day)
     * Min: `1` minute
     * @min 1
     */
    latestRescheduleInMinutes?: number;
}
interface CancellationFeePolicy {
    /**
     * Whether customers must pay a cancellation fee when canceling a booking.
     *
     * Default: `false`
     */
    enabled?: boolean;
    /**
     * Time windows relative to the session start during which customers can cancel
     * their booking. Each window includes details about the fee for canceling
     * within it.
     * @maxSize 2
     */
    cancellationWindows?: CancellationWindow[];
    /**
     * Whether Wix automatically charges the cancellation fee from the customer once
     * they cancel their booking.
     *
     * Default: `true`
     */
    autoCollectFeeEnabled?: boolean | null;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gt:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface CancellationWindow extends CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
    /**
     * Start of the cancellation window in minutes before the session start. For
     * courses, this refers to the start of the first course session.
     * @min 1
     */
    startInMinutes?: number | null;
}
/** @oneof */
interface CancellationWindowFeeOneOf {
    /** Fixed amount customers must pay when canceling the booking within this window. */
    amount?: Money;
    /**
     * Percentage of the booking price customers must pay when canceling within this window.
     *
     * Min: `0.01` percent
     * Max: `100` percent
     * @decimalValue options { gt:0, lte:100, maxScale:2 }
     */
    percentage?: string;
}
interface SaveCreditCardPolicy {
    /**
     * Whether Wix stores credit card details of the customer. Storing the details
     * allows Wix to prefill the checkout and thus increases the likelihood that the
     * customer completes the booking process.
     *
     * Default: `false`
     */
    enabled?: boolean;
}
interface ListPolicySnapshotsByBookingIdsRequest {
    /**
     * List of booking IDs to retrieve policy snapshots for.
     * @minSize 1
     * @maxSize 100
     * @format GUID
     */
    bookingIds: string[] | null;
}
interface ListPolicySnapshotsByBookingIdsResponse {
    /** Retrieved booking policy snapshots. */
    bookingPolicySnapshots?: BookingPolicySnapshot[];
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function listPolicySnapshotsByBookingIds(): __PublicMethodMetaInfo<'GET', {}, ListPolicySnapshotsByBookingIdsRequest$1, ListPolicySnapshotsByBookingIdsRequest, ListPolicySnapshotsByBookingIdsResponse$1, ListPolicySnapshotsByBookingIdsResponse>;

export { type __PublicMethodMetaInfo, listPolicySnapshotsByBookingIds };
