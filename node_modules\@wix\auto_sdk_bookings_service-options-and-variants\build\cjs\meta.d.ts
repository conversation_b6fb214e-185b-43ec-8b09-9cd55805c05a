import { CreateServiceOptionsAndVariantsRequest as CreateServiceOptionsAndVariantsRequest$1, CreateServiceOptionsAndVariantsResponse as CreateServiceOptionsAndVariantsResponse$1, CloneServiceOptionsAndVariantsRequest as CloneServiceOptionsAndVariantsRequest$1, CloneServiceOptionsAndVariantsResponse as CloneServiceOptionsAndVariantsResponse$1, GetServiceOptionsAndVariantsRequest as GetServiceOptionsAndVariantsRequest$1, GetServiceOptionsAndVariantsResponse as GetServiceOptionsAndVariantsResponse$1, GetServiceOptionsAndVariantsByServiceIdRequest as GetServiceOptionsAndVariantsByServiceIdRequest$1, GetServiceOptionsAndVariantsByServiceIdResponse as GetServiceOptionsAndVariantsByServiceIdResponse$1, UpdateServiceOptionsAndVariantsRequest as UpdateServiceOptionsAndVariantsRequest$1, UpdateServiceOptionsAndVariantsResponse as UpdateServiceOptionsAndVariantsResponse$1, DeleteServiceOptionsAndVariantsRequest as DeleteServiceOptionsAndVariantsRequest$1, DeleteServiceOptionsAndVariantsResponse as DeleteServiceOptionsAndVariantsResponse$1, QueryServiceOptionsAndVariantsRequest as QueryServiceOptionsAndVariantsRequest$1, QueryServiceOptionsAndVariantsResponse as QueryServiceOptionsAndVariantsResponse$1 } from './index.typings.js';
import '@wix/sdk-types';

/**
 * The `serviceOptionsAndVariants` object links a *service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
 * to its variants.
 * You can use it to offer customers different prices for a service,
 * depending on which choices they book.
 */
interface ServiceOptionsAndVariants {
    /**
     * ID of the `serviceOptionsAndVariants` object.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * ID of the service related to these options and variants.
     * @format GUID
     * @immutable
     */
    serviceId?: string | null;
    /** Service options. Note that currently only a single option is supported per service. */
    options?: ServiceOptions;
    /** Information about the service's variants. */
    variants?: ServiceVariants;
    /**
     * Price of the cheapest service variant.
     * @readonly
     */
    minPrice?: Money;
    /**
     * Price of the most expensive service variant.
     * @readonly
     */
    maxPrice?: Money;
    /**
     * Revision number, which increments by 1 each time the `serviceOptionsAndVariants` object is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating and deleting the `serviceOptionsAndVariants` object.
     *
     * Ignored when creating a `serviceOptionsAndVariants` object.
     * @immutable
     */
    revision?: string | null;
    /** Extensions enabling users to save custom data related to service options and variants. */
    extendedFields?: ExtendedFields;
}
interface ServiceOption extends ServiceOptionOptionSpecificDataOneOf {
    /** Details about the custom option. Available only for `CUSTOM` options. */
    customData?: CustomServiceOption;
    durationData?: DurationServiceOption;
    /**
     * ID of the service option.
     * @format GUID
     */
    id?: string;
    /** Type of the service option. */
    type?: ServiceOptionTypeWithLiterals;
}
/** @oneof */
interface ServiceOptionOptionSpecificDataOneOf {
    /** Details about the custom option. Available only for `CUSTOM` options. */
    customData?: CustomServiceOption;
    durationData?: DurationServiceOption;
}
declare enum ServiceOptionType {
    /** There is no information about the option type. */
    UNKNOWN = "UNKNOWN",
    /**
     * The service option is based on a custom parameter. For example, age group,
     * booked equipment, or appointment timing.
     */
    CUSTOM = "CUSTOM",
    /**
     * It's a *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * based option.
     */
    STAFF_MEMBER = "STAFF_MEMBER",
    /** It's a duration-based option. */
    DURATION = "DURATION"
}
/** @enumType */
type ServiceOptionTypeWithLiterals = ServiceOptionType | 'UNKNOWN' | 'CUSTOM' | 'STAFF_MEMBER' | 'DURATION';
interface CustomServiceOption {
    /**
     * Name of the service option. For example, `Age group`, `Location`, `Equipment`,
     * or `Time`.
     * @maxLength 255
     */
    name?: string;
    /**
     * Available choices for the service option. For example, `child`, `student`,
     * `adult`, and `senior` for a service option named `Age group`. Each value must
     * be unique. The value's case is ignored, meaning `Child` and `child` are
     * considered to be identical. Currently, only a single choice is supported
     * because a service can have only a single option.
     *
     * Max: 1 choice
     * @maxSize 100
     * @maxLength 255
     */
    choices?: string[];
}
interface DurationServiceOption {
    /**
     * Optional name of the duration option. For example, `Short Class`, or
     * `Extended Class`.
     * @maxLength 255
     */
    name?: string | null;
}
interface ServiceVariant {
    /**
     * Choices for the service option. Currently, only a single choice is supported
     * because a service can have only a single option.
     *
     * Max: 1 choice
     * @minSize 1
     * @maxSize 1
     */
    choices?: ServiceChoice[];
    /** Information about the service variant's price. */
    price?: Money;
}
interface ServiceChoice extends ServiceChoiceChoiceOneOf {
    /**
     * Name of the custom choice.
     * @maxLength 255
     */
    custom?: string;
    /**
     * ID of the *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service.
     * @format GUID
     */
    staffMemberId?: string;
    /** Information about the option's duration. */
    duration?: Duration;
    /**
     * ID of the service option.
     * @format GUID
     */
    optionId?: string;
}
/** @oneof */
interface ServiceChoiceChoiceOneOf {
    /**
     * Name of the custom choice.
     * @maxLength 255
     */
    custom?: string;
    /**
     * ID of the *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service.
     * @format GUID
     */
    staffMemberId?: string;
    /** Information about the option's duration. */
    duration?: Duration;
}
interface Duration {
    /**
     * Duration of the service in minutes.
     *
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     */
    minutes?: number;
    /**
     * Name of the duration option.
     *
     * Default: Human-readable text of `minutes`. For example, `1 hr 30 min`.
     * @maxLength 255
     */
    name?: string | null;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gte:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface ServiceOptions {
    /**
     * Values of the service options.
     *
     * Max: 1 service option
     * @minSize 1
     * @maxSize 1
     */
    values?: ServiceOption[];
}
interface ServiceVariants {
    /**
     * Values of the service variants.
     * @minSize 1
     * @maxSize 100
     */
    values?: ServiceVariant[];
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateServiceOptionsAndVariantsRequest {
    /** Service options and variants to create. */
    serviceOptionsAndVariants: ServiceOptionsAndVariants;
}
interface CreateServiceOptionsAndVariantsResponse {
    /** Information about the created service options and variants. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface CloneServiceOptionsAndVariantsRequest {
    /**
     * ID of the `serviceOptionsAndVariants` object to clone.
     * @format GUID
     */
    cloneFromId: string;
    /**
     * ID of the service to which the cloned `serviceOptionsAndVariants` are
     * connected.
     * @format GUID
     */
    targetServiceId: string;
}
interface CloneServiceOptionsAndVariantsResponse {
    /** Cloned `serviceOptionsAndVariants` object. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface GetServiceOptionsAndVariantsRequest {
    /**
     * ID of the `serviceOptionsAndVariants` object to retrieve.
     * @format GUID
     */
    serviceOptionsAndVariantsId: string;
}
interface GetServiceOptionsAndVariantsResponse {
    /** Retrieved `serviceOptionsAndVariants` object. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface GetServiceOptionsAndVariantsByServiceIdRequest {
    /**
     * ID of the service to retrieve options and variants for.
     * @format GUID
     */
    serviceId: string;
}
interface GetServiceOptionsAndVariantsByServiceIdResponse {
    /** Retrieved `serviceOptionsAndVariants` object. */
    serviceVariants?: ServiceOptionsAndVariants;
}
interface UpdateServiceOptionsAndVariantsRequest {
    /** `ServiceOptionsAndVariants` object to update. */
    serviceOptionsAndVariants: ServiceOptionsAndVariants;
}
interface UpdateServiceOptionsAndVariantsResponse {
    /** Updated `serviceOptionsAndVariants` object. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface DeleteServiceOptionsAndVariantsRequest {
    /**
     * ID of the `serviceOptionsAndVariants` object to delete.
     * @format GUID
     */
    serviceOptionsAndVariantsId: string;
    /** Revision of the `serviceOptionsAndVariants` object to delete. */
    revision?: string;
}
interface DeleteServiceOptionsAndVariantsResponse {
}
interface QueryServiceOptionsAndVariantsRequest {
    /** Information about filters, paging, and returned fields. */
    query: QueryV2;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object.
     *
     * Learn more about the [filter section](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#the-filter-section).
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object.
     *
     * Learn more about the [sort section](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#the-sort-section).
     * @maxSize 10
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryServiceOptionsAndVariantsResponse {
    /** Retrieved `serviceOptionsAndVariants` objects. */
    serviceOptionsAndVariantsList?: ServiceOptionsAndVariants[];
    /** Paging metadata. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
    /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */
    cursors?: Cursors;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createServiceOptionsAndVariants(): __PublicMethodMetaInfo<'POST', {}, CreateServiceOptionsAndVariantsRequest$1, CreateServiceOptionsAndVariantsRequest, CreateServiceOptionsAndVariantsResponse$1, CreateServiceOptionsAndVariantsResponse>;
declare function cloneServiceOptionsAndVariants(): __PublicMethodMetaInfo<'POST', {
    cloneFromId: string;
}, CloneServiceOptionsAndVariantsRequest$1, CloneServiceOptionsAndVariantsRequest, CloneServiceOptionsAndVariantsResponse$1, CloneServiceOptionsAndVariantsResponse>;
declare function getServiceOptionsAndVariants(): __PublicMethodMetaInfo<'GET', {
    serviceOptionsAndVariantsId: string;
}, GetServiceOptionsAndVariantsRequest$1, GetServiceOptionsAndVariantsRequest, GetServiceOptionsAndVariantsResponse$1, GetServiceOptionsAndVariantsResponse>;
declare function getServiceOptionsAndVariantsByServiceId(): __PublicMethodMetaInfo<'GET', {
    serviceId: string;
}, GetServiceOptionsAndVariantsByServiceIdRequest$1, GetServiceOptionsAndVariantsByServiceIdRequest, GetServiceOptionsAndVariantsByServiceIdResponse$1, GetServiceOptionsAndVariantsByServiceIdResponse>;
declare function updateServiceOptionsAndVariants(): __PublicMethodMetaInfo<'PATCH', {
    serviceOptionsAndVariantsId: string;
}, UpdateServiceOptionsAndVariantsRequest$1, UpdateServiceOptionsAndVariantsRequest, UpdateServiceOptionsAndVariantsResponse$1, UpdateServiceOptionsAndVariantsResponse>;
declare function deleteServiceOptionsAndVariants(): __PublicMethodMetaInfo<'DELETE', {
    serviceOptionsAndVariantsId: string;
}, DeleteServiceOptionsAndVariantsRequest$1, DeleteServiceOptionsAndVariantsRequest, DeleteServiceOptionsAndVariantsResponse$1, DeleteServiceOptionsAndVariantsResponse>;
declare function queryServiceOptionsAndVariants(): __PublicMethodMetaInfo<'POST', {}, QueryServiceOptionsAndVariantsRequest$1, QueryServiceOptionsAndVariantsRequest, QueryServiceOptionsAndVariantsResponse$1, QueryServiceOptionsAndVariantsResponse>;

export { type __PublicMethodMetaInfo, cloneServiceOptionsAndVariants, createServiceOptionsAndVariants, deleteServiceOptionsAndVariants, getServiceOptionsAndVariants, getServiceOptionsAndVariantsByServiceId, queryServiceOptionsAndVariants, updateServiceOptionsAndVariants };
