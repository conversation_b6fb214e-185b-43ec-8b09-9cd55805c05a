import { aP as CursorSearch, h as SearchStaffMembersOptions, i as SearchStaffMembersResponse } from './bookings-staff-v1-staff-member-staff-members.universal-Tbhk-dK0.mjs';
export { bv as ActionEvent, bF as Address, bI as AddressLocation, bG as AddressStreetOneOf, aS as AggregationData, b9 as AggregationResults, ba as AggregationResultsResultOneOf, aZ as AggregationResultsScalarResult, E as AggregationType, cA as AggregationTypeWithLiterals, bg as ApplicationError, O as ApprovalStatus, cI as ApprovalStatusWithLiterals, l as AssignCustomScheduleOptions, bd as AssignCustomScheduleRequest, m as AssignCustomScheduleResponse, A as AssignWorkingHoursScheduleOptions, bc as AssignWorkingHoursScheduleRequest, k as AssignWorkingHoursScheduleResponse, ad as AssociatedConferencingAccount, ae as AssociatedConferencingAccountAccountOneOf, ac as AssociatedConferencingAccounts, ab as AssociatedConferencingProvider, aa as AssociatedConferencingProviders, a6 as AssociatedWixIdentity, w as AssociatedWixIdentityConnectionStatusEnumConnectionStatus, cu as AssociatedWixIdentityConnectionStatusEnumConnectionStatusWithLiterals, bT as Availability, bU as AvailabilityConstraints, cc as AvailabilityPolicy, cb as AvailabilityPolicyUpdated, cp as BaseEventMetadata, bi as BulkActionMetadata, o as BulkUpdateStaffMemberTagsByFilterOptions, bj as BulkUpdateStaffMemberTagsByFilterRequest, p as BulkUpdateStaffMemberTagsByFilterResponse, B as BulkUpdateStaffMemberTagsOptions, be as BulkUpdateStaffMemberTagsRequest, n as BulkUpdateStaffMemberTagsResponse, bh as BulkUpdateStaffMemberTagsResult, a5 as BusinessLocation, bO as BusinessSchedule, b_ as CalendarConference, c2 as CalendarDateTime, W as CalendarType, cL as CalendarTypeWithLiterals, a7 as CommonIdentificationData, a8 as CommonIdentificationDataIdOneOf, cP as CommonSearchWithEntityContext, bZ as ConferenceProvider, V as ConferenceType, cK as ConferenceTypeWithLiterals, e as ConnectStaffMemberToUserOptions, aN as ConnectStaffMemberToUserRequest, f as ConnectStaffMemberToUserResponse, a9 as Connection, x as ConnectionStatus, cv as ConnectionStatusWithLiterals, c as CountStaffMembersOptions, aM as CountStaffMembersRequest, d as CountStaffMembersResponse, C as CreateStaffMemberOptions, am as CreateStaffMemberRequest, an as CreateStaffMemberResponse, at as CursorPaging, av as CursorPagingMetadata, aG as CursorQuery, aH as CursorQueryPagingMethodOneOf, aQ as CursorSearchPagingMethodOneOf, aw as Cursors, af as CustomConferenceAccount, b5 as DateHistogramResult, b7 as DateHistogramResults, F as Day, N as DayOfWeek, cH as DayOfWeekWithLiterals, cB as DayWithLiterals, aD as DeleteStaffMemberRequest, aE as DeleteStaffMemberResponse, D as DisconnectStaffMemberFromUserOptions, bb as DisconnectStaffMemberFromUserRequest, j as DisconnectStaffMemberFromUserResponse, bp as DomainEvent, bq as DomainEventBodyOneOf, bm as Empty, br as EntityCreatedEvent, bu as EntityDeletedEvent, bt as EntityUpdatedEvent, cq as EventMetadata, a2 as EventSchedule, ag as ExtendedFields, c4 as ExternalCalendarInfo, bX as ExternalCalendarOverrides, bC as Frequency, aq as GetDeletedStaffMemberRequest, ar as GetDeletedStaffMemberResponse, G as GetStaffMemberOptions, ao as GetStaffMemberRequest, ap as GetStaffMemberResponse, b6 as GroupByValueResults, cn as IdentificationData, co as IdentificationDataIdOneOf, I as IdentityType, ct as IdentityTypeWithLiterals, bB as Interval, cd as IntervalSplit, bf as ItemMetadata, bD as LinkedSchedule, as as ListDeletedStaffMembersRequest, au as ListDeletedStaffMembersResponse, c3 as LocalDateTime, bE as Location, a3 as LocationOptions, J as LocationStatus, cF as LocationStatusWithLiterals, L as LocationType, cE as LocationTypeWithLiterals, bL as LocationsAddress, bN as LocationsAddressLocation, bK as LocationsLocation, K as LocationsLocationType, cG as LocationsLocationTypeWithLiterals, bM as LocationsStreetAddress, _ as MediaItem, $ as MediaItemMediaOneOf, cm as MessageEnvelope, ck as MigrationData, cj as MigrationEvent, M as Mode, cy as ModeWithLiterals, cg as MultipleSessionsCreated, aV as NestedAggregationResults, aW as NestedAggregationResultsResultOneOf, b2 as NestedResultValue, b3 as NestedResultValueResultOneOf, b8 as NestedResults, a_ as NestedValueAggregationResult, bW as Participant, c6 as ParticipantNotification, bn as PolicyRemovedFromContributor, bo as PolicyUpdatedForContributor, bS as Price, aK as QueryStaffMembersMultiLanguageRequest, aL as QueryStaffMembersMultiLanguageResponse, Q as QueryStaffMembersOptions, aF as QueryStaffMembersRequest, aJ as QueryStaffMembersResponse, aU as RangeAggregationResult, b0 as RangeResult, aY as RangeResults, bR as Rate, bA as RecurringInterval, H as RecurringIntervalType, cD as RecurringIntervalTypeWithLiterals, ce as RecurringSessionSplit, c0 as RecurringSessionsUpdated, ax as RemoveStaffMemberFromTrashBinRequest, ay as RemoveStaffMemberFromTrashBinResponse, R as RequestedFields, cw as RequestedFieldsWithLiterals, a0 as Resource, bs as RestoreInfo, az as RestoreStaffMemberFromTrashBinRequest, aA as RestoreStaffMemberFromTrashBinResponse, bk as RestoreStaffRequest, bl as RestoreStaffResponse, b4 as Results, b1 as ScalarResult, z as ScalarType, cz as ScalarTypeWithLiterals, bz as Schedule, c7 as ScheduleCancelled, by as ScheduleCreated, bw as ScheduleNotification, bx as ScheduleNotificationEventOneOf, P as ScheduleStatus, cJ as ScheduleStatusWithLiterals, cf as ScheduleUnassignedFromUser, b$ as ScheduleUpdated, ch as ScheduleWithSessions, aR as SearchDetails, aO as SearchStaffMembersRequest, c1 as Session, ca as SessionCancelled, c8 as SessionCreated, Y as SessionType, cN as SessionTypeWithLiterals, c9 as SessionUpdated, c5 as SessionVersion, ci as SitePropertiesOnScheduleCreation, y as SortOrder, cx as SortOrderWithLiterals, aI as Sorting, bQ as SpecialHourPeriod, a4 as SpecificLocation, bV as SplitInterval, cl as StaffData, S as StaffMember, ak as StaffMemberConnectedToUser, q as StaffMemberConnectedToUserEnvelope, r as StaffMemberCreatedEnvelope, s as StaffMemberDeletedEnvelope, aj as StaffMemberDisconnectedFromUser, t as StaffMemberDisconnectedFromUserEnvelope, al as StaffMemberFullyCreated, u as StaffMemberFullyCreatedEnvelope, cs as StaffMemberSearchSpec, v as StaffMemberUpdatedEnvelope, b as StaffMembersQueryBuilder, cr as StaffMembersQueryResult, X as Status, cM as StatusWithLiterals, bH as StreetAddress, bJ as Subdivision, ai as TagList, ah as Tags, bP as TimePeriod, T as Transparency, cC as TransparencyWithLiterals, U as UpdateStaffMember, a as UpdateStaffMemberOptions, aB as UpdateStaffMemberRequest, aC as UpdateStaffMemberResponse, aT as ValueAggregationResult, a$ as ValueResult, aX as ValueResults, bY as Version, Z as WebhookIdentityType, cO as WebhookIdentityTypeWithLiterals, a1 as WorkingHoursSchedule, d3 as assignCustomSchedule, d2 as assignWorkingHoursSchedule, d4 as bulkUpdateStaffMemberTags, d5 as bulkUpdateStaffMemberTagsByFilter, d0 as connectStaffMemberToUser, c$ as countStaffMembers, cW as createStaffMember, cZ as deleteStaffMember, d1 as disconnectStaffMemberFromUser, cX as getStaffMember, cQ as onStaffMemberConnectedToUser, cR as onStaffMemberCreated, cS as onStaffMemberDeleted, cT as onStaffMemberDisconnectedFromUser, cU as onStaffMemberFullyCreated, cV as onStaffMemberUpdated, c_ as queryStaffMembers, cY as updateStaffMember } from './bookings-staff-v1-staff-member-staff-members.universal-Tbhk-dK0.mjs';
import '@wix/sdk-types';

/** @hidden */
type StaffMemberSearch = {};
/**
 * Retrieves a list of up to 100 staff members, given the provided filtering, paging,
 * and sorting.
 *
 *
 * ## Defaults
 *
 * Search Staff Members has the following default settings, which you can override:
 * + Sorted by `createdDate` in ascending order.
 * + `cursorPaging.limit` set to `100`.
 *
 * ## Filter
 *
 * Refer to the _supported filters article_
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting))
 * for a complete list of supported filters and sorting options.
 *
 * ## See also
 *
 * To learn about working with *Search* methods, see
 * _API Query Language_
 * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language))
 * and _Sorting and Paging_
 * ([SDK](https://dev.wix.com/docs/sdk/articles/work-with-the-sdk/api-query-language#the-sort-array) | [REST](https://dev.wix.com/docs/rest/articles/getting-started/sorting-and-paging)).
 * @param search - Search criteria including filter, sort, and paging options.
 *
 * See the supported filters article ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/supported-filters) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/filtering-and-sorting)) for complete filter and sorting options.
 * @public
 * @documentationMaturity preview
 * @requiredField search
 * @permissionId BOOKINGS.STAFF_MEMBER_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.staff.v1.StaffMembersService.SearchStaffMembers
 */
declare function searchStaffMembers(search: CursorSearch, options: SearchStaffMembersOptions): Promise<SearchStaffMembersResponse>;

export { CursorSearch, SearchStaffMembersOptions, SearchStaffMembersResponse, type StaffMemberSearch, searchStaffMembers };
