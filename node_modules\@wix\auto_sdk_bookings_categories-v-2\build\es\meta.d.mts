import { CreateCategoryRequest as CreateCategoryRequest$1, CreateCategoryResponse as CreateCategoryResponse$1, GetCategoryRequest as GetCategoryRequest$1, GetCategoryResponse as GetCategoryResponse$1, UpdateCategoryRequest as UpdateCategoryRequest$1, UpdateCategoryResponse as UpdateCategoryResponse$1, DeleteCategoryRequest as DeleteCategoryRequest$1, DeleteCategoryResponse as DeleteCategoryResponse$1, QueryCategoriesRequest as QueryCategoriesRequest$1, QueryCategoriesResponse as QueryCategoriesResponse$1, CountCategoriesRequest as CountCategoriesRequest$1, CountCategoriesResponse as CountCategoriesResponse$1, MoveCategoryRequest as MoveCategoryRequest$1, MoveCategoryResponse as MoveCategoryResponse$1 } from './index.typings.mjs';
import '@wix/sdk-types';

/** Categories organize services ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction)) by controlling the order in which customers see services on the live site and business owners see them in the dashboard. */
interface Category {
    /**
     * Category ID.
     * @format GUID
     * @readonly
     */
    id?: string | null;
    /**
     * Revision number, which increments by 1 each time the category is updated.
     * To prevent conflicting changes, you must specify the current revision when updating the category.
     *
     * Ignored when creating a category.
     * @readonly
     */
    revision?: string | null;
    /**
     * Date and time the category was created in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    createdDate?: Date | null;
    /**
     * Date and time the Category was last updated in `YYYY-MM-DDThh:mm:ss.sssZ` format.
     * @readonly
     */
    updatedDate?: Date | null;
    /**
     * Category name.
     * @minLength 1
     * @maxLength 100
     */
    name?: string | null;
    /**
     * Defines the category's position in the categories list relative to other categories.
     * Wix Bookings assigns `sortOrder` values with large gaps between adjacent categories, allowing efficient reordering without updating the entire list. When gaps become too small, the system automatically reassigns new `sortOrder` values to restore larger gaps.
     * @readonly
     */
    sortOrder?: number | null;
    /**
     * Custom field data for the category object.
     *
     * [Extended fields](https://dev.wix.com/docs/build-apps/develop-your-app/extensions/backend-extensions/schema-plugins/about-schema-plugin-extensions) must be configured in the app dashboard before they can be accessed with API calls.
     */
    extendedFields?: ExtendedFields;
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateCategoryRequest {
    /** Category to create. */
    category: Category;
}
interface CreateCategoryResponse {
    /** Created category. */
    category?: Category;
}
interface GetCategoryRequest {
    /**
     * ID of the category to retrieve.
     * @format GUID
     */
    categoryId: string;
}
interface GetCategoryResponse {
    /** Retrieved category. */
    category?: Category;
}
interface UpdateCategoryRequest {
    /** Category to update. */
    category: Category;
}
interface UpdateCategoryResponse {
    /** Updated category. */
    category?: Category;
}
interface DeleteCategoryRequest {
    /**
     * ID of the category to delete.
     * @format GUID
     */
    categoryId: string;
}
interface DeleteCategoryResponse {
}
interface QueryCategoriesRequest {
    /** WQL expression. */
    query?: CursorQuery;
}
interface CursorQuery extends CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object in the following format:
     * `"filter" : {
     * "fieldName1": "value1",
     * "fieldName2":{"$operator":"value2"}
     * }`
     * Example of operators: `$eq`, `$ne`, `$lt`, `$lte`, `$gt`, `$gte`, `$in`, `$hasSome`, `$hasAll`, `$startsWith`, `$contains`
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object in the following format:
     * `[{"fieldName":"sortField1","order":"ASC"},{"fieldName":"sortField2","order":"DESC"}]`
     * @maxSize 5
     */
    sort?: Sorting[];
}
/** @oneof */
interface CursorQueryPagingMethodOneOf {
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryCategoriesResponse {
    /** Retrieved categories. */
    categories?: Category[];
    /** Paging metadata. */
    pagingMetadata?: CursorPagingMetadata;
}
interface CursorPagingMetadata {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Cursor strings that point to the next page, previous page, or both. */
    cursors?: Cursors;
    /**
     * Whether there are more pages to retrieve following the current page.
     *
     * + `true`: Another page of results can be retrieved.
     * + `false`: This is the last page.
     */
    hasNext?: boolean | null;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface CountCategoriesRequest {
    /** Filter to base the count on. */
    filter?: Record<string, any> | null;
}
interface CountCategoriesResponse {
    /** Number of categories matching the filter. */
    count?: number;
}
interface MoveCategoryRequest {
    /**
     * ID of the category to move.
     * @format GUID
     */
    categoryId: string;
    /** New position of the category. */
    position?: PositionWithLiterals;
    /**
     * ID of the reference category.
     * Specify only for `{"position": "AFTER_CATEGORY"}`.
     * @format GUID
     */
    afterCategoryId?: string | null;
}
declare enum Position {
    UNKNOWN_POSITION = "UNKNOWN_POSITION",
    /** Place the category at the end of the list. */
    LAST = "LAST",
    /** Place the category at the beginning of the list. */
    FIRST = "FIRST",
    /** Place the category immediately after a specific category. */
    AFTER_CATEGORY = "AFTER_CATEGORY"
}
/** @enumType */
type PositionWithLiterals = Position | 'UNKNOWN_POSITION' | 'LAST' | 'FIRST' | 'AFTER_CATEGORY';
interface MoveCategoryResponse {
    /** Updated category. */
    category?: Category;
}

type __PublicMethodMetaInfo<K = string, M = unknown, T = unknown, S = unknown, Q = unknown, R = unknown> = {
    getUrl: (context: any) => string;
    httpMethod: K;
    path: string;
    pathParams: M;
    __requestType: T;
    __originalRequestType: S;
    __responseType: Q;
    __originalResponseType: R;
};
declare function createCategory(): __PublicMethodMetaInfo<'POST', {}, CreateCategoryRequest$1, CreateCategoryRequest, CreateCategoryResponse$1, CreateCategoryResponse>;
declare function getCategory(): __PublicMethodMetaInfo<'GET', {
    categoryId: string;
}, GetCategoryRequest$1, GetCategoryRequest, GetCategoryResponse$1, GetCategoryResponse>;
declare function updateCategory(): __PublicMethodMetaInfo<'PATCH', {
    categoryId: string;
}, UpdateCategoryRequest$1, UpdateCategoryRequest, UpdateCategoryResponse$1, UpdateCategoryResponse>;
declare function deleteCategory(): __PublicMethodMetaInfo<'DELETE', {
    categoryId: string;
}, DeleteCategoryRequest$1, DeleteCategoryRequest, DeleteCategoryResponse$1, DeleteCategoryResponse>;
declare function queryCategories(): __PublicMethodMetaInfo<'GET', {}, QueryCategoriesRequest$1, QueryCategoriesRequest, QueryCategoriesResponse$1, QueryCategoriesResponse>;
declare function countCategories(): __PublicMethodMetaInfo<'POST', {}, CountCategoriesRequest$1, CountCategoriesRequest, CountCategoriesResponse$1, CountCategoriesResponse>;
declare function moveCategory(): __PublicMethodMetaInfo<'POST', {
    categoryId: string;
}, MoveCategoryRequest$1, MoveCategoryRequest, MoveCategoryResponse$1, MoveCategoryResponse>;

export { type __PublicMethodMetaInfo, countCategories, createCategory, deleteCategory, getCategory, moveCategory, queryCategories, updateCategory };
