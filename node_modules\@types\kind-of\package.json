{"name": "@types/kind-of", "version": "6.0.3", "description": "TypeScript definitions for kind-of", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/kind-of", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/claasahl"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/kind-of"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "95dafd1463779322ca428c9a2bcafaae03ca2af1a03f5b2ae0d36a8b6ec5287b", "typeScriptVersion": "4.5"}