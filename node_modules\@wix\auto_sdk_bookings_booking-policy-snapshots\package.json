{"name": "@wix/auto_sdk_bookings_booking-policy-snapshots", "version": "1.0.30", "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "keywords": [], "sideEffects": false, "main": "./build/cjs/index.js", "types": "./build/cjs/index.d.ts", "exports": {".": {"import": "./build/es/index.mjs", "require": "./build/cjs/index.js", "types": "./build/es/index.d.mts"}, "./package.json": "./package.json", "./meta": {"import": "./build/es/meta.mjs", "require": "./build/cjs/meta.js", "types": "./build/es/meta.d.mts"}}, "files": ["build", "meta", "service-plugins"], "dependencies": {"@wix/sdk-runtime": "^0.3.55", "@wix/sdk-types": "^1.13.35"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.3.2"}, "scripts": {"build": "tsup", "test": ":"}, "wix": {"artifact": {"artifactId": "auto-sdk-bookings-booking-policy-snapshots", "groupId": "com.wixpress.public-sdk-dependencies"}, "sdkDependency": {"fqdnNamespace": "bookingPolicySnapshots", "fqdn": "wix.bookings.policy_snapshots.v1.booking_policy_snapshot"}}, "falconPackageHash": "7e7739995b4b75eca6200e67a8d79de479fa5475a766afe1e51275d3"}