import { NonNullablePaths } from '@wix/sdk-types';

/**
 * The `serviceOptionsAndVariants` object links a *service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction))
 * to its variants.
 * You can use it to offer customers different prices for a service,
 * depending on which choices they book.
 */
interface ServiceOptionsAndVariants {
    /**
     * ID of the `serviceOptionsAndVariants` object.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * ID of the service related to these options and variants.
     * @format GUID
     * @immutable
     */
    serviceId?: string | null;
    /** Service options. Note that currently only a single option is supported per service. */
    options?: ServiceOptions;
    /** Information about the service's variants. */
    variants?: ServiceVariants;
    /**
     * Price of the cheapest service variant.
     * @readonly
     */
    minPrice?: Money;
    /**
     * Price of the most expensive service variant.
     * @readonly
     */
    maxPrice?: Money;
    /**
     * Revision number, which increments by 1 each time the `serviceOptionsAndVariants` object is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating and deleting the `serviceOptionsAndVariants` object.
     *
     * Ignored when creating a `serviceOptionsAndVariants` object.
     * @immutable
     */
    revision?: string | null;
    /** Extensions enabling users to save custom data related to service options and variants. */
    extendedFields?: ExtendedFields;
}
interface ServiceOption extends ServiceOptionOptionSpecificDataOneOf {
    /** Details about the custom option. Available only for `CUSTOM` options. */
    customData?: CustomServiceOption;
    durationData?: DurationServiceOption;
    /**
     * ID of the service option.
     * @format GUID
     */
    _id?: string;
    /** Type of the service option. */
    type?: ServiceOptionTypeWithLiterals;
}
/** @oneof */
interface ServiceOptionOptionSpecificDataOneOf {
    /** Details about the custom option. Available only for `CUSTOM` options. */
    customData?: CustomServiceOption;
    durationData?: DurationServiceOption;
}
declare enum ServiceOptionType {
    /** There is no information about the option type. */
    UNKNOWN = "UNKNOWN",
    /**
     * The service option is based on a custom parameter. For example, age group,
     * booked equipment, or appointment timing.
     */
    CUSTOM = "CUSTOM",
    /**
     * It's a *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * based option.
     */
    STAFF_MEMBER = "STAFF_MEMBER",
    /** It's a duration-based option. */
    DURATION = "DURATION"
}
/** @enumType */
type ServiceOptionTypeWithLiterals = ServiceOptionType | 'UNKNOWN' | 'CUSTOM' | 'STAFF_MEMBER' | 'DURATION';
interface CustomServiceOption {
    /**
     * Name of the service option. For example, `Age group`, `Location`, `Equipment`,
     * or `Time`.
     * @maxLength 255
     */
    name?: string;
    /**
     * Available choices for the service option. For example, `child`, `student`,
     * `adult`, and `senior` for a service option named `Age group`. Each value must
     * be unique. The value's case is ignored, meaning `Child` and `child` are
     * considered to be identical. Currently, only a single choice is supported
     * because a service can have only a single option.
     *
     * Max: 1 choice
     * @maxSize 100
     * @maxLength 255
     */
    choices?: string[];
}
interface DurationServiceOption {
    /**
     * Optional name of the duration option. For example, `Short Class`, or
     * `Extended Class`.
     * @maxLength 255
     */
    name?: string | null;
}
interface ServiceVariant {
    /**
     * Choices for the service option. Currently, only a single choice is supported
     * because a service can have only a single option.
     *
     * Max: 1 choice
     * @minSize 1
     * @maxSize 1
     */
    choices?: ServiceChoice[];
    /** Information about the service variant's price. */
    price?: Money;
}
interface ServiceChoice extends ServiceChoiceChoiceOneOf {
    /**
     * Name of the custom choice.
     * @maxLength 255
     */
    custom?: string;
    /**
     * ID of the *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service.
     * @format GUID
     */
    staffMemberId?: string;
    /** Information about the option's duration. */
    duration?: Duration;
    /**
     * ID of the service option.
     * @format GUID
     */
    optionId?: string;
}
/** @oneof */
interface ServiceChoiceChoiceOneOf {
    /**
     * Name of the custom choice.
     * @maxLength 255
     */
    custom?: string;
    /**
     * ID of the *staff member*
     * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
     * providing the service.
     * @format GUID
     */
    staffMemberId?: string;
    /** Information about the option's duration. */
    duration?: Duration;
}
interface Duration {
    /**
     * Duration of the service in minutes.
     *
     * Min: `1` minute
     * Max: `44639` minutes (30 days, 23 hours, and 59 minutes)
     * @min 1
     * @max 44639
     */
    minutes?: number;
    /**
     * Name of the duration option.
     *
     * Default: Human-readable text of `minutes`. For example, `1 hr 30 min`.
     * @maxLength 255
     */
    name?: string | null;
}
/**
 * Money.
 * Default format to use. Sufficiently compliant with majority of standards: w3c, ISO 4217, ISO 20022, ISO 8583:2003.
 */
interface Money {
    /**
     * Monetary amount. Decimal string with a period as a decimal separator (e.g., 3.99). Optionally, a single (-), to indicate that the amount is negative.
     * @format DECIMAL_VALUE
     * @decimalValue options { gte:0, maxScale:2 }
     */
    value?: string;
    /**
     * Currency code. Must be valid ISO 4217 currency code (e.g., USD).
     * @format CURRENCY
     */
    currency?: string;
    /**
     * Monetary amount. Decimal string in local format (e.g., 1 000,30). Optionally, a single (-), to indicate that the amount is negative.
     * @maxLength 50
     */
    formattedValue?: string | null;
}
interface ServiceOptions {
    /**
     * Values of the service options.
     *
     * Max: 1 service option
     * @minSize 1
     * @maxSize 1
     */
    values?: ServiceOption[];
}
interface ServiceVariants {
    /**
     * Values of the service variants.
     * @minSize 1
     * @maxSize 100
     */
    values?: ServiceVariant[];
}
interface ExtendedFields {
    /**
     * Extended field data. Each key corresponds to the namespace of the app that created the extended fields.
     * The value of each key is structured according to the schema defined when the extended fields were configured.
     *
     * You can only access fields for which you have the appropriate permissions.
     *
     * Learn more about [extended fields](https://dev.wix.com/docs/rest/articles/getting-started/extended-fields).
     */
    namespaces?: Record<string, Record<string, any>>;
}
interface CreateServiceOptionsAndVariantsRequest {
    /** Service options and variants to create. */
    serviceOptionsAndVariants: ServiceOptionsAndVariants;
}
interface CreateServiceOptionsAndVariantsResponse {
    /** Information about the created service options and variants. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface CloneServiceOptionsAndVariantsRequest {
    /**
     * ID of the `serviceOptionsAndVariants` object to clone.
     * @format GUID
     */
    cloneFromId: string;
    /**
     * ID of the service to which the cloned `serviceOptionsAndVariants` are
     * connected.
     * @format GUID
     */
    targetServiceId: string;
}
interface CloneServiceOptionsAndVariantsResponse {
    /** Cloned `serviceOptionsAndVariants` object. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface GetServiceOptionsAndVariantsRequest {
    /**
     * ID of the `serviceOptionsAndVariants` object to retrieve.
     * @format GUID
     */
    serviceOptionsAndVariantsId: string;
}
interface GetServiceOptionsAndVariantsResponse {
    /** Retrieved `serviceOptionsAndVariants` object. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface GetServiceOptionsAndVariantsByServiceIdRequest {
    /**
     * ID of the service to retrieve options and variants for.
     * @format GUID
     */
    serviceId: string;
}
interface GetServiceOptionsAndVariantsByServiceIdResponse {
    /** Retrieved `serviceOptionsAndVariants` object. */
    serviceVariants?: ServiceOptionsAndVariants;
}
interface UpdateServiceOptionsAndVariantsRequest {
    /** `ServiceOptionsAndVariants` object to update. */
    serviceOptionsAndVariants: ServiceOptionsAndVariants;
}
interface UpdateServiceOptionsAndVariantsResponse {
    /** Updated `serviceOptionsAndVariants` object. */
    serviceOptionsAndVariants?: ServiceOptionsAndVariants;
}
interface DeleteServiceOptionsAndVariantsRequest {
    /**
     * ID of the `serviceOptionsAndVariants` object to delete.
     * @format GUID
     */
    serviceOptionsAndVariantsId: string;
    /** Revision of the `serviceOptionsAndVariants` object to delete. */
    revision?: string;
}
interface DeleteServiceOptionsAndVariantsResponse {
}
interface QueryServiceOptionsAndVariantsRequest {
    /** Information about filters, paging, and returned fields. */
    query: QueryV2;
}
interface QueryV2 extends QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
    /**
     * Filter object.
     *
     * Learn more about the [filter section](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#the-filter-section).
     */
    filter?: Record<string, any> | null;
    /**
     * Sort object.
     *
     * Learn more about the [sort section](https://dev.wix.com/docs/rest/articles/getting-started/api-query-language#the-sort-section).
     * @maxSize 10
     */
    sort?: Sorting[];
}
/** @oneof */
interface QueryV2PagingMethodOneOf {
    /** Paging options to limit and skip the number of items. */
    paging?: Paging;
    /** Cursor token pointing to a page of results. Not used in the first request. Following requests use the cursor token and not `filter` or `sort`. */
    cursorPaging?: CursorPaging;
}
interface Sorting {
    /**
     * Name of the field to sort by.
     * @maxLength 512
     */
    fieldName?: string;
    /** Sort order. */
    order?: SortOrderWithLiterals;
}
declare enum SortOrder {
    ASC = "ASC",
    DESC = "DESC"
}
/** @enumType */
type SortOrderWithLiterals = SortOrder | 'ASC' | 'DESC';
interface Paging {
    /** Number of items to load. */
    limit?: number | null;
    /** Number of items to skip in the current sort order. */
    offset?: number | null;
}
interface CursorPaging {
    /**
     * Maximum number of items to return in the results.
     * @max 100
     */
    limit?: number | null;
    /**
     * Pointer to the next or previous page in the list of results.
     *
     * Pass the relevant cursor token from the `pagingMetadata` object in the previous call's response.
     * Not relevant for the first request.
     * @maxLength 16000
     */
    cursor?: string | null;
}
interface QueryServiceOptionsAndVariantsResponse {
    /** Retrieved `serviceOptionsAndVariants` objects. */
    serviceOptionsAndVariantsList?: ServiceOptionsAndVariants[];
    /** Paging metadata. */
    pagingMetadata?: PagingMetadataV2;
}
interface PagingMetadataV2 {
    /** Number of items returned in the response. */
    count?: number | null;
    /** Offset that was requested. */
    offset?: number | null;
    /** Total number of items that match the query. Returned if offset paging is used and the `tooManyToCount` flag is not set. */
    total?: number | null;
    /** Flag that indicates the server failed to calculate the `total` field. */
    tooManyToCount?: boolean | null;
    /** Cursors to navigate through the result pages using `next` and `prev`. Returned if cursor paging is used. */
    cursors?: Cursors;
}
interface Cursors {
    /**
     * Cursor string pointing to the next page in the list of results.
     * @maxLength 16000
     */
    next?: string | null;
    /**
     * Cursor pointing to the previous page in the list of results.
     * @maxLength 16000
     */
    prev?: string | null;
}
interface DomainEvent extends DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
/** @oneof */
interface DomainEventBodyOneOf {
    createdEvent?: EntityCreatedEvent;
    updatedEvent?: EntityUpdatedEvent;
    deletedEvent?: EntityDeletedEvent;
    actionEvent?: ActionEvent;
}
interface EntityCreatedEvent {
    entity?: string;
}
interface RestoreInfo {
    deletedDate?: Date | null;
}
interface EntityUpdatedEvent {
    /**
     * Since platformized APIs only expose PATCH and not PUT we can't assume that the fields sent from the client are the actual diff.
     * This means that to generate a list of changed fields (as opposed to sent fields) one needs to traverse both objects.
     * We don't want to impose this on all developers and so we leave this traversal to the notification recipients which need it.
     */
    currentEntity?: string;
}
interface EntityDeletedEvent {
    /** Entity that was deleted. */
    deletedEntity?: string | null;
}
interface ActionEvent {
    body?: string;
}
interface Empty {
}
/** Encapsulates all details written to the Greyhound topic when a site's properties are updated. */
interface SitePropertiesNotification {
    /** The site ID for which this update notification applies. */
    metasiteId?: string;
    /** The actual update event. */
    event?: SitePropertiesEvent;
    /**
     * A convenience set of mappings from the MetaSite ID to its constituent services.
     * @maxSize 500
     */
    translations?: Translation[];
    /** Context of the notification */
    changeContext?: ChangeContext;
}
/** The actual update event for a particular notification. */
interface SitePropertiesEvent {
    /** Version of the site's properties represented by this update. */
    version?: number;
    /** Set of properties that were updated - corresponds to the fields in "properties". */
    fields?: string[];
    /** Updated properties. */
    properties?: Properties;
}
interface Properties {
    /** Site categories. */
    categories?: Categories;
    /** Site locale. */
    locale?: Locale;
    /**
     * Site language.
     *
     * Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format.
     */
    language?: string | null;
    /**
     * Site currency format used to bill customers.
     *
     * Three-letter currency code in [ISO-4217 alphabetic](https://en.wikipedia.org/wiki/ISO_4217#Active_codes) format.
     */
    paymentCurrency?: string | null;
    /** Timezone in `America/New_York` format. */
    timeZone?: string | null;
    /** Email address. */
    email?: string | null;
    /** Phone number. */
    phone?: string | null;
    /** Fax number. */
    fax?: string | null;
    /** Address. */
    address?: Address;
    /** Site display name. */
    siteDisplayName?: string | null;
    /** Business name. */
    businessName?: string | null;
    /** Path to the site's logo in Wix Media (without Wix Media base URL). */
    logo?: string | null;
    /** Site description. */
    description?: string | null;
    /**
     * Business schedule. Regular and exceptional time periods when the business is open or the service is available.
     *
     * __Note:__ Not supported by Wix Bookings.
     */
    businessSchedule?: BusinessSchedule;
    /** Supported languages of a site and the primary language. */
    multilingual?: Multilingual;
    /** Cookie policy the Wix user defined for their site (before the site visitor interacts with/limits it). */
    consentPolicy?: ConsentPolicy;
    /**
     * Supported values: `FITNESS SERVICE`, `RESTAURANT`, `BLOG`, `STORE`, `EVENT`, `UNKNOWN`.
     *
     * Site business type.
     */
    businessConfig?: string | null;
    /** External site URL that uses Wix as its headless business solution. */
    externalSiteUrl?: string | null;
    /** Track clicks analytics. */
    trackClicksAnalytics?: boolean;
}
interface Categories {
    /** Primary site category. */
    primary?: string;
    /**
     * Secondary site category.
     * @maxSize 50
     */
    secondary?: string[];
    /** Business Term Id */
    businessTermId?: string | null;
}
interface Locale {
    /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */
    languageCode?: string;
    /** Two-letter country code in [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements) format. */
    country?: string;
}
interface Address {
    /** Street name. */
    street?: string;
    /** City name. */
    city?: string;
    /** Two-letter country code in an [ISO-3166 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) format. */
    country?: string;
    /** State. */
    state?: string;
    /**
     * Zip or postal code.
     * @maxLength 20
     */
    zip?: string;
    /** Extra information to be displayed in the address. */
    hint?: AddressHint;
    /** Whether this address represents a physical location. */
    isPhysical?: boolean;
    /** Google-formatted version of this address. */
    googleFormattedAddress?: string;
    /** Street number. */
    streetNumber?: string;
    /** Apartment number. */
    apartmentNumber?: string;
    /** Geographic coordinates of location. */
    coordinates?: GeoCoordinates;
}
/**
 * Extra information on displayed addresses.
 * This is used for display purposes. Used to add additional data about the address, such as "In the passage".
 * Free text. In addition, the user can state where to display the additional description - before, after, or instead of the address string.
 */
interface AddressHint {
    /** Extra text displayed next to, or instead of, the actual address. */
    text?: string;
    /** Where the extra text should be displayed. */
    placement?: PlacementTypeWithLiterals;
}
/** Where the extra text should be displayed: before, after or instead of the actual address. */
declare enum PlacementType {
    BEFORE = "BEFORE",
    AFTER = "AFTER",
    REPLACE = "REPLACE"
}
/** @enumType */
type PlacementTypeWithLiterals = PlacementType | 'BEFORE' | 'AFTER' | 'REPLACE';
/** Geocoordinates for a particular address. */
interface GeoCoordinates {
    /** Latitude of the location. Must be between -90 and 90. */
    latitude?: number;
    /** Longitude of the location. Must be between -180 and 180. */
    longitude?: number;
}
/** Business schedule. Regular and exceptional time periods when the business is open or the service is available. */
interface BusinessSchedule {
    /**
     * Weekly recurring time periods when the business is regularly open or the service is available. Limited to 100 time periods.
     * @maxSize 100
     */
    periods?: TimePeriod[];
    /**
     * Exceptions to the business's regular hours. The business can be open or closed during the exception.
     * @maxSize 100
     */
    specialHourPeriod?: SpecialHourPeriod[];
}
/** Weekly recurring time periods when the business is regularly open or the service is available. */
interface TimePeriod {
    /** Day of the week the period starts on. */
    openDay?: DayOfWeekWithLiterals;
    /**
     * Time the period starts in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     */
    openTime?: string;
    /** Day of the week the period ends on. */
    closeDay?: DayOfWeekWithLiterals;
    /**
     * Time the period ends in 24-hour [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) extended format. Valid values are `00:00` to `24:00`, where `24:00` represents
     * midnight at the end of the specified day.
     *
     * __Note:__ If `openDay` and `closeDay` specify the same day of the week `closeTime` must be later than `openTime`.
     */
    closeTime?: string;
}
/** Enumerates the days of the week. */
declare enum DayOfWeek {
    MONDAY = "MONDAY",
    TUESDAY = "TUESDAY",
    WEDNESDAY = "WEDNESDAY",
    THURSDAY = "THURSDAY",
    FRIDAY = "FRIDAY",
    SATURDAY = "SATURDAY",
    SUNDAY = "SUNDAY"
}
/** @enumType */
type DayOfWeekWithLiterals = DayOfWeek | 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY';
/** Exception to the business's regular hours. The business can be open or closed during the exception. */
interface SpecialHourPeriod {
    /** Start date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    startDate?: string;
    /** End date and time of the exception in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format and [Coordinated Universal Time (UTC)](https://en.wikipedia.org/wiki/Coordinated_Universal_Time). */
    endDate?: string;
    /**
     * Whether the business is closed (or the service is not available) during the exception.
     *
     * Default: `true`.
     */
    isClosed?: boolean;
    /** Additional info about the exception. For example, "We close earlier on New Year's Eve." */
    comment?: string;
}
interface Multilingual {
    /**
     * Supported languages list.
     * @maxSize 200
     */
    supportedLanguages?: SupportedLanguage[];
    /** Whether to redirect to user language. */
    autoRedirect?: boolean;
}
interface SupportedLanguage {
    /** Two-letter language code in [ISO 639-1 alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) format. */
    languageCode?: string;
    /** Locale. */
    locale?: Locale;
    /** Whether the supported language is the primary language for the site. */
    isPrimary?: boolean;
    /** Language icon. */
    countryCode?: string;
    /** How the language will be resolved. For internal use. */
    resolutionMethod?: ResolutionMethodWithLiterals;
    /** Whether the supported language is the primary language for site visitors. */
    isVisitorPrimary?: boolean | null;
}
declare enum ResolutionMethod {
    QUERY_PARAM = "QUERY_PARAM",
    SUBDOMAIN = "SUBDOMAIN",
    SUBDIRECTORY = "SUBDIRECTORY"
}
/** @enumType */
type ResolutionMethodWithLiterals = ResolutionMethod | 'QUERY_PARAM' | 'SUBDOMAIN' | 'SUBDIRECTORY';
interface ConsentPolicy {
    /** Whether the site uses cookies that are essential to site operation. Always `true`. */
    essential?: boolean | null;
    /** Whether the site uses cookies that affect site performance and other functional measurements. */
    functional?: boolean | null;
    /** Whether the site uses cookies that collect analytics about how the site is used (in order to improve it). */
    analytics?: boolean | null;
    /** Whether the site uses cookies that collect information allowing better customization of the experience for a current visitor. */
    advertising?: boolean | null;
    /** CCPA compliance flag. */
    dataToThirdParty?: boolean | null;
}
/** A single mapping from the MetaSite ID to a particular service. */
interface Translation {
    /** The service type. */
    serviceType?: string;
    /** The application definition ID; this only applies to services of type ThirdPartyApps. */
    appDefId?: string;
    /** The instance ID of the service. */
    instanceId?: string;
}
interface ChangeContext extends ChangeContextPayloadOneOf {
    /** Properties were updated. */
    propertiesChange?: PropertiesChange;
    /** Default properties were created on site creation. */
    siteCreated?: SiteCreated;
    /** Properties were cloned on site cloning. */
    siteCloned?: SiteCloned;
}
/** @oneof */
interface ChangeContextPayloadOneOf {
    /** Properties were updated. */
    propertiesChange?: PropertiesChange;
    /** Default properties were created on site creation. */
    siteCreated?: SiteCreated;
    /** Properties were cloned on site cloning. */
    siteCloned?: SiteCloned;
}
interface PropertiesChange {
}
interface SiteCreated {
    /** Origin template site id. */
    originTemplateId?: string | null;
}
interface SiteCloned {
    /** Origin site id. */
    originMetaSiteId?: string;
}
interface MessageEnvelope {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
    /** Stringify payload. */
    data?: string;
}
interface IdentificationData extends IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
    /** @readonly */
    identityType?: WebhookIdentityTypeWithLiterals;
}
/** @oneof */
interface IdentificationDataIdOneOf {
    /**
     * ID of a site visitor that has not logged in to the site.
     * @format GUID
     */
    anonymousVisitorId?: string;
    /**
     * ID of a site visitor that has logged in to the site.
     * @format GUID
     */
    memberId?: string;
    /**
     * ID of a Wix user (site owner, contributor, etc.).
     * @format GUID
     */
    wixUserId?: string;
    /**
     * ID of an app.
     * @format GUID
     */
    appId?: string;
}
declare enum WebhookIdentityType {
    UNKNOWN = "UNKNOWN",
    ANONYMOUS_VISITOR = "ANONYMOUS_VISITOR",
    MEMBER = "MEMBER",
    WIX_USER = "WIX_USER",
    APP = "APP"
}
/** @enumType */
type WebhookIdentityTypeWithLiterals = WebhookIdentityType | 'UNKNOWN' | 'ANONYMOUS_VISITOR' | 'MEMBER' | 'WIX_USER' | 'APP';
/** @docsIgnore */
type CreateServiceOptionsAndVariantsApplicationErrors = {
    code?: 'UNKNOWN_OPTION_IDS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTIPLE_CURRENCIES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNSUPPORTED_OPTION_TYPE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNKNOWN_STAFF_IDS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MISSING_STAFF_VARIANTS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'IDENTICAL_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MISSING_CUSTOM_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNSUPPORTED_OPTION_DATA';
    description?: string;
    data?: Record<string, any>;
};
/** @docsIgnore */
type UpdateServiceOptionsAndVariantsApplicationErrors = {
    code?: 'UNKNOWN_OPTION_IDS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MULTIPLE_CURRENCIES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNSUPPORTED_OPTION_TYPE';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNKNOWN_STAFF_IDS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MISSING_STAFF_VARIANTS';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'IDENTICAL_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'MISSING_CUSTOM_CHOICES';
    description?: string;
    data?: Record<string, any>;
} | {
    code?: 'UNSUPPORTED_OPTION_DATA';
    description?: string;
    data?: Record<string, any>;
};
interface BaseEventMetadata {
    /**
     * App instance ID.
     * @format GUID
     */
    instanceId?: string | null;
    /**
     * Event type.
     * @maxLength 150
     */
    eventType?: string;
    /** The identification type and identity data. */
    identity?: IdentificationData;
}
interface EventMetadata extends BaseEventMetadata {
    /** Event ID. With this ID you can easily spot duplicated events and ignore them. */
    _id?: string;
    /**
     * Fully Qualified Domain Name of an entity. This is a unique identifier assigned to the API main business entities.
     * For example, `wix.stores.catalog.product`, `wix.bookings.session`, `wix.payments.transaction`.
     */
    entityFqdn?: string;
    /**
     * Event action name, placed at the top level to make it easier for users to dispatch messages.
     * For example: `created`/`updated`/`deleted`/`started`/`completed`/`email_opened`.
     */
    slug?: string;
    /** ID of the entity associated with the event. */
    entityId?: string;
    /** Event timestamp in [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) format and UTC time. For example, `2020-04-26T13:57:50.699Z`. */
    eventTime?: Date | null;
    /**
     * Whether the event was triggered as a result of a privacy regulation application
     * (for example, GDPR).
     */
    triggeredByAnonymizeRequest?: boolean | null;
    /** If present, indicates the action that triggered the event. */
    originatedFrom?: string | null;
    /**
     * A sequence number that indicates the order of updates to an entity. For example, if an entity was updated at 16:00 and then again at 16:01, the second update will always have a higher sequence number.
     * You can use this number to make sure you're handling updates in the right order. Just save the latest sequence number on your end and compare it to the one in each new message. If the new message has an older (lower) number, you can safely ignore it.
     */
    entityEventSequence?: string | null;
}
interface ServiceOptionsAndVariantsCreatedEnvelope {
    entity: ServiceOptionsAndVariants;
    metadata: EventMetadata;
}
/** @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ
 * @webhook
 * @eventType wix.bookings.catalog.v1.service_options_and_variants_created
 * @slug created
 */
declare function onServiceOptionsAndVariantsCreated(handler: (event: ServiceOptionsAndVariantsCreatedEnvelope) => void | Promise<void>): void;
interface ServiceOptionsAndVariantsDeletedEnvelope {
    entity: ServiceOptionsAndVariants;
    metadata: EventMetadata;
}
/** @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ
 * @webhook
 * @eventType wix.bookings.catalog.v1.service_options_and_variants_deleted
 * @slug deleted
 */
declare function onServiceOptionsAndVariantsDeleted(handler: (event: ServiceOptionsAndVariantsDeletedEnvelope) => void | Promise<void>): void;
interface ServiceOptionsAndVariantsUpdatedEnvelope {
    entity: ServiceOptionsAndVariants;
    metadata: EventMetadata;
}
/** @permissionScope Read Bookings - Public Data
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-PUBLIC
 * @permissionScope Manage Bookings Services and Settings
 * @permissionScopeId SCOPE.BOOKINGS.CONFIGURATION
 * @permissionScope Manage Bookings
 * @permissionScopeId SCOPE.DC-BOOKINGS.MANAGE-BOOKINGS
 * @permissionScope Read Bookings - Including Participants
 * @permissionScopeId SCOPE.DC-BOOKINGS.READ-BOOKINGS-SENSITIVE
 * @permissionScope Read Bookings - all read permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.READ-BOOKINGS
 * @permissionScope Manage Bookings - all permissions
 * @permissionScopeId SCOPE.DC-BOOKINGS-MEGA.MANAGE-BOOKINGS
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ
 * @webhook
 * @eventType wix.bookings.catalog.v1.service_options_and_variants_updated
 * @slug updated
 */
declare function onServiceOptionsAndVariantsUpdated(handler: (event: ServiceOptionsAndVariantsUpdatedEnvelope) => void | Promise<void>): void;
/**
 * Creates a `serviceOptionsAndVariants` object and for a service.
 *
 *
 * ## Calculate variants
 *
 * Before creating a `serviceOptionsAndVariants` object, you need to
 * anticipate and manually define all its variants, since Wix Bookings doesn't
 * automatically calculate them. For the actual
 * Create Service Options And Variants* call, specify both the `options` and
 * `variants` arrays.
 *
 * ## Limitations
 *
 * Wix Bookings allows you to connect only a single `serviceOptionsAndVariants`
 * object to a service. *Create Service Options And Variants* fails, if the
 * service already has a connected `serviceOptionsAndVariants` object.
 *
 * Currently, you can include only a single option per
 * `serviceOptionsAndVariants` object. Taken together, this means that services
 * are limited to a single option.
 *
 * ## Option ID
 *
 * When creating a`serviceOptionsAndVariants` object, you must specify an ID in
 * [UUID format](https://en.wikipedia.org/wiki/Universally_unique_identifier)
 * for its only option. You must reference this option ID for each variant as
 * `variants.values.choices.optionId`.
 *
 * ## Staff member option
 *
 * To creating an option based on the *staff member*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/staff-members/introduction) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/staff-members/introduction))
 * providing the service, you need to specify `STAFF_MEMBER` as `options.values.type`.
 * Also, specify all staff member IDs as `variants.values.choices.staffMemberId`.
 * You could follow this *sample flow*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-staff-member-based-service-variants) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-staff-member-based-service-variants)).
 *
 * ## Custom option
 *
 * To create an option based on a custom parameter, specify `CUSTOM` as
 * `options.values.type`. Provide descriptive names for all custom choices as
 * `variants.values.choices.custom`. These names are displayed to customers
 * during the book flow. You could follow this *sample flow*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/service-options-and-variants/sample-flows#create-service-variants-based-on-the-booked-equipment)).
 *
 * ## Duration option
 *
 * To create an option based on appointment duration, specify `DURATION` as
 * `options.values.type` and set a descriptive name in `options.values.durationData.name`.
 * Also, indicate the appointment length in `minutes` and provide a descriptive
 * `name` for each duration choice in `variants.values.choices.duration`.
 * @param serviceOptionsAndVariants - Service options and variants to create.
 * @public
 * @requiredField serviceOptionsAndVariants
 * @requiredField serviceOptionsAndVariants.options
 * @requiredField serviceOptionsAndVariants.serviceId
 * @requiredField serviceOptionsAndVariants.variants
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_CREATE
 * @applicableIdentity APP
 * @returns Information about the created service options and variants.
 * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CreateServiceOptionsAndVariants
 */
declare function createServiceOptionsAndVariants(serviceOptionsAndVariants: NonNullablePaths<ServiceOptionsAndVariants, `options` | `serviceId` | `variants`, 2>): Promise<NonNullablePaths<ServiceOptionsAndVariants, `options.values` | `options.values.${number}.customData.name` | `options.values.${number}._id` | `options.values.${number}.type` | `variants.values` | `variants.values.${number}.price.value` | `variants.values.${number}.price.currency`, 6> & {
    __applicationErrorsType?: CreateServiceOptionsAndVariantsApplicationErrors;
}>;
/**
 * Clones a `serviceOptionsAndVariants` object and connects it to a *service*
 * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).
 *
 *
 * The call fails if the service already has a connected
 * `serviceOptionsAndVariants` object.
 *
 * The cloned `serviceOptionsAndVariants` object gets a new, unique option ID.
 * The option ID of the existing `serviceOptionsAndVariants` object isn't reused.
 *
 * For example, you may call this method after *cloning a service*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/clone-service) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/clone-service)).
 * @param cloneFromId - ID of the `serviceOptionsAndVariants` object to clone.
 * @param targetServiceId - ID of the service to which the cloned `serviceOptionsAndVariants` are
 * connected.
 * @public
 * @requiredField cloneFromId
 * @requiredField targetServiceId
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_CREATE
 * @applicableIdentity APP
 * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.CloneServiceOptionsAndVariants
 */
declare function cloneServiceOptionsAndVariants(cloneFromId: string, targetServiceId: string): Promise<NonNullablePaths<CloneServiceOptionsAndVariantsResponse, `serviceOptionsAndVariants.options.values` | `serviceOptionsAndVariants.options.values.${number}.customData.name` | `serviceOptionsAndVariants.options.values.${number}._id` | `serviceOptionsAndVariants.options.values.${number}.type` | `serviceOptionsAndVariants.variants.values` | `serviceOptionsAndVariants.variants.values.${number}.price.value` | `serviceOptionsAndVariants.variants.values.${number}.price.currency`, 7>>;
/**
 * Retrieves a `serviceOptionsAndVariants` object by its ID.
 * @param serviceOptionsAndVariantsId - ID of the `serviceOptionsAndVariants` object to retrieve.
 * @public
 * @requiredField serviceOptionsAndVariantsId
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ
 * @applicableIdentity APP
 * @returns Retrieved `serviceOptionsAndVariants` object.
 * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariants
 */
declare function getServiceOptionsAndVariants(serviceOptionsAndVariantsId: string): Promise<NonNullablePaths<ServiceOptionsAndVariants, `options.values` | `options.values.${number}.customData.name` | `options.values.${number}._id` | `options.values.${number}.type` | `variants.values` | `variants.values.${number}.price.value` | `variants.values.${number}.price.currency`, 6>>;
/**
 * Retrieves a `serviceOptionsAndVariants` object by *service ID*
 * ([SDK](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/introduction) | [REST](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/introduction)).
 * @param serviceId - ID of the service to retrieve options and variants for.
 * @public
 * @requiredField serviceId
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.GetServiceOptionsAndVariantsByServiceId
 */
declare function getServiceOptionsAndVariantsByServiceId(serviceId: string): Promise<NonNullablePaths<GetServiceOptionsAndVariantsByServiceIdResponse, `serviceVariants.options.values` | `serviceVariants.options.values.${number}.customData.name` | `serviceVariants.options.values.${number}._id` | `serviceVariants.options.values.${number}.type` | `serviceVariants.variants.values` | `serviceVariants.variants.values.${number}.price.value` | `serviceVariants.variants.values.${number}.price.currency`, 7>>;
/**
 * Updates a `serviceOptionsAndVariants` object.
 *
 *
 * Currently, only a single option is supported per `serviceOptionsAndVariants` object.
 *
 * If you want to update `variants`, you must pass the entire list of supported
 * variants, not only newly added variants.
 *
 * If you want to update `options`, you must pass the entire list of supported
 * options, not only newly added options.
 * @param _id - ID of the `serviceOptionsAndVariants` object.
 * @public
 * @requiredField _id
 * @requiredField serviceOptionsAndVariants
 * @requiredField serviceOptionsAndVariants.revision
 * @param serviceOptionsAndVariants - Service options and variants to update.
 * @param options - Options for updating the service options and variants.
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_UPDATE
 * @applicableIdentity APP
 * @returns Updated `serviceOptionsAndVariants` object.
 * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.UpdateServiceOptionsAndVariants
 */
declare function updateServiceOptionsAndVariants(_id: string, serviceOptionsAndVariants: NonNullablePaths<UpdateServiceOptionsAndVariants, `revision`, 2>): Promise<NonNullablePaths<ServiceOptionsAndVariants, `options.values` | `options.values.${number}.customData.name` | `options.values.${number}._id` | `options.values.${number}.type` | `variants.values` | `variants.values.${number}.price.value` | `variants.values.${number}.price.currency`, 6> & {
    __applicationErrorsType?: UpdateServiceOptionsAndVariantsApplicationErrors;
}>;
interface UpdateServiceOptionsAndVariants {
    /**
     * ID of the `serviceOptionsAndVariants` object.
     * @format GUID
     * @readonly
     */
    _id?: string | null;
    /**
     * ID of the service related to these options and variants.
     * @format GUID
     * @immutable
     */
    serviceId?: string | null;
    /** Service options. Note that currently only a single option is supported per service. */
    options?: ServiceOptions;
    /** Information about the service's variants. */
    variants?: ServiceVariants;
    /**
     * Price of the cheapest service variant.
     * @readonly
     */
    minPrice?: Money;
    /**
     * Price of the most expensive service variant.
     * @readonly
     */
    maxPrice?: Money;
    /**
     * Revision number, which increments by 1 each time the `serviceOptionsAndVariants` object is updated.
     * To prevent conflicting changes,
     * the current revision must be passed when updating and deleting the `serviceOptionsAndVariants` object.
     *
     * Ignored when creating a `serviceOptionsAndVariants` object.
     * @immutable
     */
    revision?: string | null;
    /** Extensions enabling users to save custom data related to service options and variants. */
    extendedFields?: ExtendedFields;
}
/**
 * Deletes a `serviceOptionsAndVariants` object.
 *
 *
 * Because each service can be connected to only a single `serviceOptionsAndVariants`
 * object, the service doesn't support *varied pricing*
 * ([SDK](https://dev.wix.com/docs/sdk/backend-modules/bookings/services/about-service-payments) | [REST](https://dev.wix.com/docs/rest/business-solutions/bookings/services/services-v2/about-service-payments#service-rates))
 * after deleting a `serviceOptionsAndVariants` object. Instead, Wix Bookings
 * uses its standard price calculation.
 * @param serviceOptionsAndVariantsId - ID of the `serviceOptionsAndVariants` object to delete.
 * @public
 * @requiredField serviceOptionsAndVariantsId
 * @param options - Options for deleting the service options and variants.
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_DELETE
 * @applicableIdentity APP
 * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.DeleteServiceOptionsAndVariants
 */
declare function deleteServiceOptionsAndVariants(serviceOptionsAndVariantsId: string, options?: DeleteServiceOptionsAndVariantsOptions): Promise<void>;
interface DeleteServiceOptionsAndVariantsOptions {
    /** Revision of the `serviceOptionsAndVariants` object to delete. */
    revision?: string;
}
/**
 * Creates a query to retrieve a list of `serviceOptionsAndVariants` objects.
 *
 * The `queryServiceOptionsAndVariants()` function builds a query to retrieve a list of `serviceOptionsAndVariants` objects and returns a `ServiceOptionsAndVariantsQueryBuilder` object.
 *
 * The returned object contains the query definition, which is typically used to run the query using the [find()](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-builder/find) function.
 *
 * You can refine the query by chaining `ServiceOptionsAndVariantsQueryBuilder` functions onto the query. `ServiceOptionsAndVariantsQueryBuilder` functions enable you to sort, filter, and control the results that `queryServiceOptionsAndVariants()` returns.
 *
 * `queryServiceOptionsAndVariants()` runs with the following `ServiceOptionsAndVariantsQueryBuilder` default that you can override:
 *
 * + `limit` is `50`.
 * + Sorted by `id` in ascending order.
 *
 * The functions that are chained to `queryServiceOptionsAndVariants()` are applied in the order they are called. For example, if you apply `ascending("options.values.type")` and then `ascending("variants.values.price")`, the results are sorted first by the `"type"`, and then, if there are multiple results with the same `"type"`, the items are sorted by `"price"`.
 *
 * The following `ServiceOptionsAndVariantsQueryBuilder` functions are supported for the `queryServiceOptionsAndVariants()` function. For a full description of the `serviceOptionsAndVariants` object, see the object returned for the [items](https://dev.wix.com/docs/sdk/backend-modules/bookings/service-options-and-variants/service-options-and-variants-list-query-result/items) property in `ServiceOptionsAndVariantsQueryResult`.
 * @public
 * @permissionId BOOKINGS.SERVICE_OPTIONS_AND_VARIANTS_READ
 * @applicableIdentity APP
 * @fqn wix.bookings.catalog.v1.ServiceOptionsAndVariantsService.QueryServiceOptionsAndVariants
 */
declare function queryServiceOptionsAndVariants(): ServiceOptionsAndVariantsListQueryBuilder;
interface QueryCursorResult {
    cursors: Cursors;
    hasNext: () => boolean;
    hasPrev: () => boolean;
    length: number;
    pageSize: number;
}
interface ServiceOptionsAndVariantsListQueryResult extends QueryCursorResult {
    items: ServiceOptionsAndVariants[];
    query: ServiceOptionsAndVariantsListQueryBuilder;
    next: () => Promise<ServiceOptionsAndVariantsListQueryResult>;
    prev: () => Promise<ServiceOptionsAndVariantsListQueryResult>;
}
interface ServiceOptionsAndVariantsListQueryBuilder {
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    eq: (propertyName: '_id' | 'serviceId', value: any) => ServiceOptionsAndVariantsListQueryBuilder;
    /** @param propertyName - Property whose value is compared with `value`.
     * @param value - Value to compare against.
     */
    ne: (propertyName: '_id' | 'serviceId', value: any) => ServiceOptionsAndVariantsListQueryBuilder;
    /** @param propertyName - Property whose value is compared with `values`.
     * @param values - List of values to compare against.
     */
    hasSome: (propertyName: 'options.values.id' | 'options.values.type' | 'variants.values.choices.custom' | 'variants.values.choices.optionId' | 'variants.values.price.value' | 'variants.values.price.currency', value: any[]) => ServiceOptionsAndVariantsListQueryBuilder;
    /** Refines a query to match items where the specified property is in a short list of specified values. */
    in: (propertyName: '_id' | 'serviceId', value: any) => ServiceOptionsAndVariantsListQueryBuilder;
    /** Refines a query to match items where the specified property is in a list of specified values, such as from another table. */
    exists: (propertyName: 'options.values' | 'variants.values', value: boolean) => ServiceOptionsAndVariantsListQueryBuilder;
    /**
     * Adds a sort to a query, sorting by the specified properties in ascending order.
     *
     * The `ascending()` function refines a `CUSTOM_QUERY_BUILDER_NAME` to sort by the value of `propertyName` in ascending order. You can specify multiple properties for sorting in ascending order by passing each property name as an additional argument. `ascending()` sorts the results in the order the properties are passed. You can sort the following types:
     *
     * - Number: Sorts numerically.
     * - Date: Sorts by date and time.
     * - String: Sorts lexicographically, so `'abc'` comes after `'XYZ'`. If a property contains a number stored as a string (for example, `'0'`), that value is sorted alphabetically and not numerically. If a property doesn't have a value, that value is ranked lowest.
     * @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    ascending: (...propertyNames: Array<'_id' | 'serviceId'>) => ServiceOptionsAndVariantsListQueryBuilder;
    /**
     * Adds a sort to a query, sorting by the specified properties in descending order.
     *
     * The `descending()` function refines a `CUSTOM_QUERY_BUILDER_NAME` to sort by the value of `propertyName` in descending order.
     *
     * You can specify multiple properties for sorting in descending order by passing each property name as an additional argument. `descending()` sorts the results in the order the properties are passed. You can sort the following types:
     *
     * - Number: Sorts numerically.
     * - Date: Sorts by date and time.
     * - String: Sorts lexicographically, so `'abc'` comes after `'XYZ'`. If a property contains a number stored as a string (for example, `'0'`), that value is sorted alphabetically and not numerically. If a property doesn't have a value, that value is ranked lowest.
     * @param propertyNames - Properties used in the sort. To sort by multiple properties, pass properties as additional arguments. */
    descending: (...propertyNames: Array<'_id' | 'serviceId'>) => ServiceOptionsAndVariantsListQueryBuilder;
    /** @param limit - Number of items to return, which is also the `pageSize` of the results object. */
    limit: (limit: number) => ServiceOptionsAndVariantsListQueryBuilder;
    /** @param cursor - A pointer to specific record */
    skipTo: (cursor: string) => ServiceOptionsAndVariantsListQueryBuilder;
    find: () => Promise<ServiceOptionsAndVariantsListQueryResult>;
}

export { type ActionEvent, type Address, type AddressHint, type BaseEventMetadata, type BusinessSchedule, type Categories, type ChangeContext, type ChangeContextPayloadOneOf, type CloneServiceOptionsAndVariantsRequest, type CloneServiceOptionsAndVariantsResponse, type ConsentPolicy, type CreateServiceOptionsAndVariantsApplicationErrors, type CreateServiceOptionsAndVariantsRequest, type CreateServiceOptionsAndVariantsResponse, type CursorPaging, type Cursors, type CustomServiceOption, DayOfWeek, type DayOfWeekWithLiterals, type DeleteServiceOptionsAndVariantsOptions, type DeleteServiceOptionsAndVariantsRequest, type DeleteServiceOptionsAndVariantsResponse, type DomainEvent, type DomainEventBodyOneOf, type Duration, type DurationServiceOption, type Empty, type EntityCreatedEvent, type EntityDeletedEvent, type EntityUpdatedEvent, type EventMetadata, type ExtendedFields, type GeoCoordinates, type GetServiceOptionsAndVariantsByServiceIdRequest, type GetServiceOptionsAndVariantsByServiceIdResponse, type GetServiceOptionsAndVariantsRequest, type GetServiceOptionsAndVariantsResponse, type IdentificationData, type IdentificationDataIdOneOf, type Locale, type MessageEnvelope, type Money, type Multilingual, type Paging, type PagingMetadataV2, PlacementType, type PlacementTypeWithLiterals, type Properties, type PropertiesChange, type QueryServiceOptionsAndVariantsRequest, type QueryServiceOptionsAndVariantsResponse, type QueryV2, type QueryV2PagingMethodOneOf, ResolutionMethod, type ResolutionMethodWithLiterals, type RestoreInfo, type ServiceChoice, type ServiceChoiceChoiceOneOf, type ServiceOption, type ServiceOptionOptionSpecificDataOneOf, ServiceOptionType, type ServiceOptionTypeWithLiterals, type ServiceOptions, type ServiceOptionsAndVariants, type ServiceOptionsAndVariantsCreatedEnvelope, type ServiceOptionsAndVariantsDeletedEnvelope, type ServiceOptionsAndVariantsListQueryBuilder, type ServiceOptionsAndVariantsListQueryResult, type ServiceOptionsAndVariantsUpdatedEnvelope, type ServiceVariant, type ServiceVariants, type SiteCloned, type SiteCreated, type SitePropertiesEvent, type SitePropertiesNotification, SortOrder, type SortOrderWithLiterals, type Sorting, type SpecialHourPeriod, type SupportedLanguage, type TimePeriod, type Translation, type UpdateServiceOptionsAndVariants, type UpdateServiceOptionsAndVariantsApplicationErrors, type UpdateServiceOptionsAndVariantsRequest, type UpdateServiceOptionsAndVariantsResponse, WebhookIdentityType, type WebhookIdentityTypeWithLiterals, cloneServiceOptionsAndVariants, createServiceOptionsAndVariants, deleteServiceOptionsAndVariants, getServiceOptionsAndVariants, getServiceOptionsAndVariantsByServiceId, onServiceOptionsAndVariantsCreated, onServiceOptionsAndVariantsDeleted, onServiceOptionsAndVariantsUpdated, queryServiceOptionsAndVariants, updateServiceOptionsAndVariants };
